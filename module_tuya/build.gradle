plugins {
    id 'com.android.library'
}

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    buildToolsVersion rootProject.ext.android.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode rootProject.ext.android.versionCode
        versionName rootProject.ext.android.versionName

        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            debuggable rootProject.ext.debuggable
            minifyEnabled rootProject.ext.minifyEnabled
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility rootProject.ext.Java_Version
        targetCompatibility rootProject.ext.Java_Version
    }
}

dependencies {

    if (isAar) {
        implementation 'com.dinsafer.dinsdk:dincore:' + rootProject.ext.android.versionName
    } else {
        implementation project(':dincore')
    }
    // Tuya Home 最新稳定版：
    implementation 'com.tuya.smart:tuyasmart:3.36.2'
    implementation 'com.alibaba:fastjson:1.1.67.android'
    implementation 'androidx.annotation:annotation:1.0.0'

    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.okhttp3:okhttp:4.9.1'
}


apply plugin: 'maven'

uploadArchives {
    repositories {
        mavenDeployer {
            pom.groupId = 'com.dinsafer.dinsdk'
            pom.artifactId = "tuya"
            pom.version = rootProject.ext.android.versionName
            pom.packaging = 'aar'
            repository(url: uri("${rootProject.projectDir}/dinsdk-repository"))
        }
    }
}
