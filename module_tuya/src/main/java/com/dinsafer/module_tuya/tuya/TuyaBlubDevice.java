package com.dinsafer.module_tuya.tuya;

import android.graphics.Color;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.utils.RandomStringUtils;
import com.dinsafer.dssupport.plugin.PluginTypeHelper;
import com.dinsafer.dssupport.utils.DDLog;
import com.tuya.smart.sdk.api.IResultCallback;
import com.tuya.smart.sdk.bean.DeviceBean;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

class TuyaBlubDevice extends TuyaDevice {
    private static final String TAG = TuyaBlubDevice.class.getSimpleName();

    //    设备的更新是否我自己引起的
    private int myLastColor = 0;
    private boolean isMyAction = false;

    private int brightMax = 255;
    private int brightMin = 25;

    private boolean isPidFor20Dp;

    private Map<String, String> supportCmd = new HashMap<>();

    public TuyaBlubDevice(DeviceBean deviceBean) {
        super(deviceBean);

        convertToInfo();
    }

    public TuyaBlubDevice(@NonNull TuyaCacheInfo.CacheInfo cacheInfo) {
        super(cacheInfo);
    }

    protected void convertToInfo() {
        if (!isCanOperate()) {
            return;
        }
        super.convertToInfo();
//       有一下几个字段要暴露出去
//        灯泡：
//        1. name String
//        2. isOn boolean
//        3. mode String
//        3.1 white
//        3.2 colour
//        4. color int
//        5. brightness int
//        6. state int
        Map<String, Object> par = this.getInfo();
        par.put(TuyaConstants.ATTR_NAME, sourceDevice.getName());
        boolean isOn;
        isPidFor20Dp = PluginTypeHelper.getInstance()
                .isTuyaPid20Dp(sourceDevice.getProductId());
        if (isPidFor20Dp) {
            supportCmd.put("20", TuyaCmd.TUYA_SET_ON);
            supportCmd.put("21", TuyaCmd.TUYA_SET_BLUB_MODE);
            supportCmd.put("22", TuyaCmd.TUYA_SET_BLUB_COLOR);
            supportCmd.put("24", TuyaCmd.TUYA_SET_BLUB_COLOR);
        } else {
            supportCmd.put("1", TuyaCmd.TUYA_SET_ON);
            supportCmd.put("2", TuyaCmd.TUYA_SET_BLUB_MODE);
            supportCmd.put("3", TuyaCmd.TUYA_SET_BLUB_COLOR);
            supportCmd.put("5", TuyaCmd.TUYA_SET_BLUB_COLOR);
        }

        /**
         * 新旧灯泡最低值就是10%！！！
         */
        brightMax = isPidFor20Dp ? 1000 : 255;
        brightMin = isPidFor20Dp ? 100 : 26;
        if (isPidFor20Dp) {
            isOn = (boolean) getSourceDeviceDps().get("20");
        } else {
            isOn = (boolean) getSourceDeviceDps().get("1");
        }
        par.put(TuyaConstants.ATTR_IS_ON, isOn);

        String mode = getMode();
        par.put(TuyaConstants.ATTR_MODE, mode);
        if (mode.equals("colour")) {
            par.put(TuyaConstants.ATTR_COLOR, getColor());
        }
        par.put(TuyaConstants.ATTR_BRIGHTNESS, getBrightNess());

    }

    private double getBrightNess() {
        if (TextUtils.isEmpty(sourceDevice.getDevId())) {
            return 0;
        }
        Map<String, Object> map = sourceDevice
                .getDps();
        if (map == null) {
            return 0;
        }
        if (getMode().equals("colour")) {
            if (isPidFor20Dp) {
                String hsv = (String) map.get("24");
                return ((Integer.valueOf(hsv.substring(8, 12), 16) - 0) * 1.0) / brightMax;
            } else {
                //                设置颜色
                String hsv = (String) map.get("5");
//                    根据涂鸦文档，前6位为rgb，7-10位色调值（0-360），11-13位饱和度，14-16位亮度
                double brightProgress = (Integer.valueOf(hsv.substring(12, 14), 16) * 1.0) / brightMax;
                return brightProgress;
            }
        }

//        白灯模式
        if (isPidFor20Dp) {
            return (DeviceHelper.getInt(getSourceDeviceDps(), "22", 0) * 1.0) / brightMax;
        } else {
            return (DeviceHelper.getInt(getSourceDeviceDps(), "3", 0) * 1.0) / brightMax;
        }
    }

    private String getMode() {
        String mode = "";
        if (isPidFor20Dp) {
            mode = (String) getSourceDeviceDps().get("21");
        } else {
            mode = (String) getSourceDeviceDps().get("2");
        }
        return mode;
    }

    private int getColor() {
        Map<String, Object> map = sourceDevice
                .getDps();
        if (map == null) {
            return Color.WHITE;
        }
        if (isPidFor20Dp) {
            //                设置颜色
            String hsv = (String) map.get("24");
            float h = Integer.valueOf(hsv.substring(0, 4), 16);
            float s = Integer.valueOf(hsv.substring(4, 8), 16) / 1000.0f;
            float v = Integer.valueOf(hsv.substring(8, 12), 16) / 1000.0f;
            int color = Color.HSVToColor(new float[]{
                    h, s, v});

            //将颜色值改为 调色盘 所需的颜色（亮度值为 1.0 f）
            int finalColor = changeColorToLocal(Color.parseColor("#" + Integer.toHexString(color)));
            return finalColor;
        } else {
            //                设置颜色
            String hsv = (String) map.get("5");
            i("整个值为：" + hsv);
            String color = hsv.substring(0, 6);
//                    根据涂鸦文档，前6位为rgb，7-10位色调值（0-360），11-13位饱和度，14-16位亮度
            int brightProgress = Integer.valueOf(hsv.substring(12, 14), 16);

            //将颜色值改为 调色盘 所需的颜色（亮度值为 1.0 f）
            int finalColor = changeColorToLocal(Color.parseColor("#" + color));
            return finalColor;
        }
    }

    private int convertRemoteBrightProgress() {
        if (sourceDevice == null) {
            return 0;
        }
        Map<String, Object> map = sourceDevice
                .getDps();
        if (map == null) {
            return 0;
        }
        if (isPidFor20Dp) {
            String hsv = (String) map.get("24");
            return Integer.valueOf(hsv.substring(8, 12), 16) - 0;
        } else {
            //                设置颜色
            String hsv = (String) map.get("5");
//                    根据涂鸦文档，前6位为rgb，7-10位色调值（0-360），11-13位饱和度，14-16位亮度
            int brightProgress = Integer.valueOf(hsv.substring(12, 14), 16);

            return brightProgress;
        }
    }

    private int convertRemoteColor() {
        Map<String, Object> map = sourceDevice
                .getDps();
        if (map == null) {
            return Color.WHITE;
        }
        if (isPidFor20Dp) {
            //                设置颜色
            String hsv = (String) map.get("24");
            float h = Integer.valueOf(hsv.substring(0, 4), 16);
            float s = Integer.valueOf(hsv.substring(4, 8), 16) / 1000.0f;
            float v = Integer.valueOf(hsv.substring(8, 12), 16) / 1000.0f;
            int color = Color.HSVToColor(new float[]{
                    h, s, v});

            //将颜色值改为 调色盘 所需的颜色（亮度值为 1.0 f）
            int finalColor = changeColorToLocal(Color.parseColor("#" + Integer.toHexString(color)));
            return finalColor;
        } else {
            //                设置颜色
            String hsv = (String) map.get("5");
            i("整个值为：" + hsv);
            String color = hsv.substring(0, 6);
//                    根据涂鸦文档，前6位为rgb，7-10位色调值（0-360），11-13位饱和度，14-16位亮度
            int brightProgress = Integer.valueOf(hsv.substring(12, 14), 16);

            //将颜色值改为 调色盘 所需的颜色（亮度值为 1.0 f）
            int finalColor = changeColorToLocal(Color.parseColor("#" + color));
            return finalColor;
        }
    }


    @Override
    public void submit(Map arg) {
        String cmd = (String) arg.get("cmd");
        if (!isCanOperate()) {
            DDLog.e(TAG, "当前涂鸦设备为缓存设备，不能进行操作");
            if (TuyaCmd.DELETE_DEVICE.equals(cmd)) {
                super.submit(arg);
            }
            return;
        }

        switch (cmd) {
            case TuyaCmd.TUYA_SET_ON:
                setOn((Boolean) arg.get("isOn"));
                break;
            case TuyaCmd.TUYA_SET_BLUB_MODE:
                setMode((String) arg.get("mode"));
                break;
            case TuyaCmd.TUYA_SET_BLUB_COLOR:

                if ("colour".equals(getMode())) {
                    setColorAndBrightness((int) arg.get("color"), arg.get("brightness"));
                } else {
                    setBrightness(arg.get("brightness"));
                }
                break;

            default:
                super.submit(arg);
                break;

        }
    }

    @Override
    public void onTuyaDpUpdate(String devId, String dpStr) {

        try {
            JSONObject jsonObject = new JSONObject(dpStr);
            //            里边包含什么字段，就表示什么东西更新了

            Iterator<String> sIterator = jsonObject.keys();
            while (sIterator.hasNext()) {
                String key = sIterator.next();
                if (supportCmd.containsKey(key)) {
                    dispatchResult(supportCmd.get(key), getTuyaResult(supportCmd.get(key),
                            jsonObject.get(key)));
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

    }

    private Map getTuyaResult(String cmd, Object value) {

        Map data = TuyaCmd.getDefaultResultMap(true, cmd);
        Map result = new HashMap();
        switch (cmd) {
            case TuyaCmd.TUYA_SET_ON:
                result.put("isOn", value);
                break;
            case TuyaCmd.TUYA_SET_BLUB_MODE:
                result.put("mode", value);
                break;
            case TuyaCmd.TUYA_SET_BLUB_COLOR:
                result.put("color", convertRemoteColor());
                break;
        }
        data.put("result", result);
        return data;
    }

    private void setMode(String mode) {
        String dps;

        if (isPidFor20Dp) {
            dps = "{\"21\": \"" + mode + "\"}";
        } else {
            dps = "{\"2\": \"" + mode + "\"}";
        }

        publishDps(dps, new IResultCallback() {
            @Override
            public void onError(String code, String error) {
                Map result = TuyaCmd.getDefaultResultMap(false, TuyaCmd.TUYA_SET_BLUB_MODE);
                result.put("errorMessage", "code:" + code + " error:" + error);
                dispatchResult(TuyaCmd.TUYA_SET_BLUB_MODE, result);
            }

            @Override
            public void onSuccess() {

            }
        });
    }

    //    白灯模式下使用
    private void setBrightness(Object brightness) {
        String dps = "";
        int b = 0;
        if (brightness instanceof Double) {
            b = (int) ((Double) brightness * brightMax);
        } else {
            b = (int) ((Float) brightness * brightMax);
        }
        if (isPidFor20Dp) {

            i("toSetColor--" + "is white");
//            String dps = "{\"4\": " + tuyaLightSaturationSeekbar.getProgress() + ",\"3\":"
//                    + b + "}";
            dps = "{\"22\":" + b + "}";

        } else {

            i("toSetColor--" + "is white");
//            String dps = "{\"4\": " + tuyaLightSaturationSeekbar.getProgress() + ",\"3\":"
//                    + b + "}";
            dps = "{\"3\":" + b + "}";

        }

        publishDps(dps, new IResultCallback() {
            @Override
            public void onError(String code, String error) {
                Map result = TuyaCmd.getDefaultResultMap(false, TuyaCmd.TUYA_SET_BLUB_COLOR);
                result.put("errorMessage", "code:" + code + " error:" + error);
                dispatchResult(TuyaCmd.TUYA_SET_BLUB_COLOR, result);
            }

            @Override
            public void onSuccess() {
            }
        });
    }


    private void setColorAndBrightness(int color, Object brightness) {
        String dps;
        int b = 0;
        if (brightness instanceof Double) {
            b = (int) ((Double) brightness * brightMax);
        } else {
            b = (int) ((Float) brightness * brightMax);
        }
        if (b > brightMax) {
            b = brightMax;
        }
        if (isPidFor20Dp) {
            float hsb[] = new float[3];
            Color.colorToHSV(color, hsb);

            i("hsbH:" + hsb[0] + " hsbS:" + hsb[1] + " hsbB:" + hsb[2]);

//                float brightNess = (float) (seekBar.getProgress() * 1.0 / brightMax);
//                int color = Color.HSVToColor(new float[]{
//                        hsb[0], hsb[1], brightNess});
            i("changeColorToRemote-转换后的颜色：" + color);

            i("hsbH:" + hsb[0] + " hsbS:" + hsb[1] + " hsbB:" + hsb[2]);

            String hexColor = RandomStringUtils.addZeroForNum(
                    Integer.toHexString((int) hsb[0]),
                    4);
            hexColor = hexColor + RandomStringUtils.addZeroForNum(
                    Integer.toHexString((int) (hsb[1] * 1000)),
                    4);
            hexColor = hexColor + RandomStringUtils.addZeroForNum(
                    Integer.toHexString(b),
                    4);
            i(hexColor);
            dps = "{\"24\": \"" + hexColor + "\"}";
        } else {
            int _color = changeColorToRemote(color);
            i("toSetColor, 转换后color is" + _color);
            float hsb[] = color2hsb(_color);

            i("hsbH:" + hsb[0] + " hsbS:" + hsb[1] + " hsbB:" + hsb[2]);

            String hexColor = String.format("%06X", (0xFFFFFF & _color));
            hexColor = hexColor +
                    RandomStringUtils.addZeroForNum(
                            Integer.toHexString((int) hsb[0]),
                            4);
            hexColor = hexColor + RandomStringUtils.addZeroForNum(
                    Integer.toHexString(100),
                    2);
            hexColor = hexColor + RandomStringUtils.addZeroForNum(
                    Integer.toHexString(b + 0),
                    2);
            dps = "{\"5\": \"" + hexColor + "\"}";
        }

        publishDps(dps, new IResultCallback() {
            @Override
            public void onError(String code, String error) {
                Map result = TuyaCmd.getDefaultResultMap(false, TuyaCmd.TUYA_SET_BLUB_COLOR);
                result.put("errorMessage", "code:" + code + " error:" + error);
                dispatchResult(TuyaCmd.TUYA_SET_BLUB_COLOR, result);
            }

            @Override
            public void onSuccess() {

            }
        });
    }

    /**
     * 转颜色，然后把颜色给到远方。服务器、灯泡
     */
    private int changeColorToRemote(int _color) {
        i("changeColorToRemote-转换前的颜色：" + _color);
        myLastColor = _color;
        float hsb[] = color2hsb(_color);
        i("hsbH:" + hsb[0] + " hsbS:" + hsb[1] + " hsbB:" + hsb[2]);

//        float brightNess = (float) (seekBar.getProgress() * 1.0 / brightMax);
        int color = Color.HSVToColor(new float[]{
                hsb[0], hsb[1], (float) getBrightNess()});
        i("changeColorToRemote-转换后的颜色：" + color);
        return color;
    }

    private void setOn(boolean isOn) {
        String dps;

        if (isPidFor20Dp) {
            if (!isOn) {
                dps = "{\"20\": false}";
            } else {
                dps = "{\"20\": true }";
            }
        } else {
            if (!isOn) {
                dps = "{\"1\": false}";
            } else {
                dps = "{\"1\": true }";
            }
        }

        publishDps(dps, new IResultCallback() {
            @Override
            public void onError(String code, String error) {
                Map result = TuyaCmd.getDefaultResultMap(false, TuyaCmd.TUYA_SET_ON);
                result.put("errorMessage", "code:" + code + " error:" + error);
                dispatchResult(TuyaCmd.TUYA_SET_ON, result);
            }

            @Override
            public void onSuccess() {
                Map result = TuyaCmd.getDefaultResultMap(true, TuyaCmd.TUYA_SET_ON);
                result.put(TuyaConstants.ATTR_IS_ON, isOn);
                dispatchResult(TuyaCmd.TUYA_SET_ON, result);
            }
        });


    }


    /**
     * 转颜色:把远方颜色转到亮度为1的颜色，以为要去设置色盘
     */
    private int changeColorToLocal(int _color) {
        i("changeColorToLocal-转换前的颜色：" + _color);
        float hsb[] = color2hsb(_color);
        i("hsbH:" + hsb[0] + " hsbS:" + hsb[1] + " hsbB:" + hsb[2]);
        //将第三个参数改为 亮度为1.0f。因为设置 调试盘 的颜色。亮度是固定的。
        int color = Color.HSVToColor(new float[]{
                hsb[0], hsb[1], 1.0f});
        i("changeColorToLocal-转换后的颜色：" + color);

        if (color == myLastColor) {
            isMyAction = true;
        } else {
            isMyAction = false;
        }
        return color;
    }

    private void i(String msg) {
        DDLog.i(TAG, msg);

    }

    public static float[] color2hsb(int color) {
        final int a = (color >>> 24);
        final int rgbR = (color >> 16) & 0xFF;
        final int rgbG = (color >> 8) & 0xFF;
        final int rgbB = (color) & 0xFF;

        return rgb2hsb(rgbR, rgbG, rgbB);
    }

    public static float[] rgb2hsb(int rgbR, int rgbG, int rgbB) {

        assert 0 <= rgbR && rgbR <= 255;
        assert 0 <= rgbG && rgbG <= 255;
        assert 0 <= rgbB && rgbB <= 255;
        int[] rgb = new int[]{rgbR, rgbG, rgbB};
        Arrays.sort(rgb);
        int max = rgb[2];
        int min = rgb[0];

        float hsbB = max / 255.0f;
        float hsbS = max == 0 ? 0 : (max - min) / (float) max;

        float hsbH = 0;
        if (max == rgbR && rgbG >= rgbB) {
            hsbH = (rgbG - rgbB) * 60f / (max - min) + 0;
        } else if (max == rgbR && rgbG < rgbB) {
            hsbH = (rgbG - rgbB) * 60f / (max - min) + 360;
        } else if (max == rgbG) {
            hsbH = (rgbB - rgbR) * 60f / (max - min) + 120;
        } else if (max == rgbB) {
            hsbH = (rgbR - rgbG) * 60f / (max - min) + 240;
        }

        return new float[]{hsbH, hsbS, hsbB};
    }
}
