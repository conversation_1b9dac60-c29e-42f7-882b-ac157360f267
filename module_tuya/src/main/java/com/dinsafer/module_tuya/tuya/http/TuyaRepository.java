package com.dinsafer.module_tuya.tuya.http;

import com.dinsafer.dincore.http.StringResponseEntry;

import retrofit2.Callback;


public class TuyaRepository {

    public void getAccount(String home_id, Callback<TuyaAccountResponse> callback) {
        TuyaApi.getApi().getAccount(home_id).enqueue(callback);
    }


    public void setAccount(String home_id, String password, String username, String countrycode, String uid, String mobileApiUrl, Callback<StringResponseEntry> callback) {
        TuyaApi.getApi().setAccount(home_id, password, username, countrycode, uid, mobileApiUrl).enqueue(callback);
    }


}
