package com.dinsafer.module_tuya.tuya.http;


import com.dinsafer.dincore.http.StringResponseEntry;

import java.util.Map;

import retrofit2.Call;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;
import retrofit2.http.Url;

interface ITuyaApi {

    @POST
    @FormUrlEncoded
    Call<TuyaAccountResponse> getAccountCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> setAccountCall(@Url String url, @FieldMap Map<String, Object> map);
}
