package com.dinsafer.module_tuya.tuya;

import android.content.Context;
import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.activtor.bean.Plugin;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dssupport.utils.DDLog;
import com.tuya.smart.home.sdk.TuyaHomeSdk;
import com.tuya.smart.sdk.api.IResultCallback;
import com.tuya.smart.sdk.api.ITuyaActivator;
import com.tuya.smart.sdk.api.ITuyaActivatorGetToken;
import com.tuya.smart.sdk.api.ITuyaDevice;
import com.tuya.smart.sdk.api.ITuyaSmartActivatorListener;
import com.tuya.smart.sdk.bean.DeviceBean;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * 涂鸦触发配对器
 */
@Keep
public class TuyaTriggerBinder extends BasePluginBinder {

    private ITuyaActivator mTuyaActivator;
    private final String mSsid, mSsidPassword;
    private ActivatorCallback activatorCallback;

    private List<DeviceBean> mOldDeviceList;
    private boolean isTimeOutToFindDevice = false;

    public TuyaTriggerBinder(@NonNull Context mContext, @NonNull String ssid, @NonNull String ssidPassword) {
        super(mContext);
        this.mSsid = ssid;
        this.mSsidPassword = ssidPassword;
    }

    @Override
    public void bindDevice(Plugin plugin) {

    }

    public void start() {
        mOldDeviceList = new ArrayList<>();
        mOldDeviceList.addAll(TuyaDelegate.getInstance().getLocalDeviceList());

        TuyaDelegate.getInstance().getActivatorToken(new ITuyaActivatorGetToken() {
            @Override
            public void onSuccess(String token) {
                mTuyaActivator = TuyaDelegate.getInstance().getActivator(mContext,
                        new ITuyaSmartActivatorListener() {
                            @Override
                            public void onError(String errorCode, String errorMsg) {
                                findDeviceAfterActivatorFail();
                            }

                            @Override
                            public void onActiveSuccess(DeviceBean devResp) {
                                if (activatorCallback != null) {
                                    if (activatorCallback.onActiveInteract(devResp)) {
                                        acitvateSuccess(devResp);
                                    } else {
                                        ITuyaDevice mDevice = TuyaHomeSdk.newDeviceInstance(devResp.getDevId());
                                        if (mDevice != null) {
                                            mDevice.removeDevice(new IResultCallback() {

                                                @Override
                                                public void onError(String s, String s1) {
                                                    mDevice.unRegisterDevListener();
                                                    mDevice.onDestroy();

                                                    callBackBindResult(ErrorCode.ACTIVTOR_BIND_DEVICE_FAIL, s + "/" + s1);
                                                }

                                                @Override
                                                public void onSuccess() {
                                                    mDevice.unRegisterDevListener();
                                                    mDevice.onDestroy();

                                                    callBackBindResult(ErrorCode.ACTIVTOR_BIND_DEVICE_FAIL, "not offical tuya plugin");
                                                }
                                            });
                                        } else {
                                            callBackBindResult(ErrorCode.ACTIVTOR_BIND_DEVICE_FAIL, "del ITuyaDevice null");
                                        }
                                    }
                                } else {
                                    acitvateSuccess(devResp);
                                }
                            }

                            @Override
                            public void onStep(String step, Object data) {
                                DDLog.i(TAG, "bind tuya plugin please wait");
                            }
                        }, mSsid, mSsidPassword, token);

                mTuyaActivator.start();
            }

            @Override
            public void onFailure(String s, String s1) {
                callBackBindResult(ErrorCode.ACTIVTOR_BIND_DEVICE_FAIL, s + "/" + s1);
            }
        });
    }


    private void findDeviceAfterActivatorFail() {

        TuyaDelegate.getInstance().queryDevList(new IGetTuyaDeviceCallBack() {
            @Override
            public void onSuccess(List<DeviceBean> deviceBeanList) {
                DDLog.i("onSuccess", deviceBeanList.toString());
//                添加超时的时候，遍历新旧涂鸦列表，找到添加的设备
//                i("mOldDeviceList.size:" + mOldDeviceList.size());
                for (int i = 0; i < deviceBeanList.size(); i++) {
                    boolean isFind = false;
                    for (int j = 0; j < mOldDeviceList.size(); j++) {
                        if (deviceBeanList.get(i).getDevId()
                                .equals(mOldDeviceList.get(j).getDevId())) {
                            isFind = true;
                            break;
                        }
                    }
                    if (!isFind) {
//                        i("找到了：" + deviceBeanList.get(i).getProductId());
                        isTimeOutToFindDevice = true;
//                        onActiveSuccess(deviceBeanList.get(i));
//                        EventBus.getDefault().post(new TuyaUpdata());
                        acitvateSuccess(deviceBeanList.get(i));
                        return;
                    }
                }
//                i("找不到");
//                closeLoadingFragment();
//                showAddError();
//                mTuyaActivator.stop();
                callBackBindResult(ErrorCode.ACTIVTOR_BIND_DEVICE_FAIL, "找不到");
                stop();
            }

            @Override
            public void onError(String code, String msg) {
                DDLog.i("onFailure", "code:" + code + "s1:" + msg);
//                closeLoadingFragment();
//                showAddError();
//                mTuyaActivator.stop();
                stop();
            }
        });
    }

    private void acitvateSuccess(DeviceBean deviceBean) {
        ITuyaDevice mDevice = TuyaDelegate.getInstance().getDev(deviceBean.getDevId());
        mDevice.renameDevice(deviceBean.getName(), new IResultCallback() {
            @Override
            public void onError(String s, String s1) {
            }

            @Override
            public void onSuccess() {
//                                        EventBus.getDefault().post(new TuyaUpdata());
            }
        });

//                                通知Devicemanager和Tuyaservice去更新缓存列表
//                                2021年05月25日14:19:58 不需要在这里增加回调，因为在Tuyadelegate已经有通知了
//                                DeivceChangeEvent event = new DeivceChangeEvent(TuyaDevice.create(devResp));
//                                event.setAdd(true);
//                                EventBus.getDefault().post(event);

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceBean.getDevId());
            jsonObject.put("productid", deviceBean.getProductId());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        callBackBindResult(1, jsonObject.toString());
    }

    @Override
    public void stop() {
        super.stop();
        if (mTuyaActivator != null) {
            mTuyaActivator.stop();
        }
    }

    @Override
    public void destroyBinder() {
        super.destroyBinder();
        if (mTuyaActivator != null) {
            mTuyaActivator.onDestroy();
        }
        activatorCallback = null;
    }

    public void setActivatorCallback(ActivatorCallback activatorCallback) {
        this.activatorCallback = activatorCallback;
    }

    @Keep
    public interface ActivatorCallback {
        boolean onActiveInteract(DeviceBean devResp);

        void onStart();
    }
}
