package com.dinsafer.module_tuya.tuya;

import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.annotation.Nullable;

import com.dinsafer.dincore.db.cache.ICacheInfo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/11/28 23:34
 */
@Keep
public class TuyaCacheInfo implements ICacheInfo {

    private static final long serialVersionUID = 9071743990704672842L;
    private long cacheTime;
    private List<CacheInfo> cacheInfoList;

    public TuyaCacheInfo() {
        cacheInfoList = new ArrayList<>();
    }

    public long getCacheTime() {
        return cacheTime;
    }

    public void setCacheTime(long cacheTime) {
        this.cacheTime = cacheTime;
    }

    public void addDevice(final String id, final String subCategory) {
        addDevice(new CacheInfo(id, subCategory));
    }

    public void addDevice(final CacheInfo cacheInfo) {
        if (null != cacheInfo
                && !TextUtils.isEmpty(cacheInfo.getId())
                && !TextUtils.isEmpty(cacheInfo.getSubCategory())
                && !cacheInfoList.contains(cacheInfo)) {
            cacheInfoList.add(cacheInfo);
        }
    }

    public boolean isCacheDevice(final String id, final String subCategory) {
        return isCacheDevice(new CacheInfo(id, subCategory));
    }

    public boolean isCacheDevice(final CacheInfo cacheInfo) {
        return null != cacheInfo
                && !TextUtils.isEmpty(cacheInfo.getId())
                && !TextUtils.isEmpty(cacheInfo.getSubCategory())
                && cacheInfoList.contains(cacheInfo);
    }

    public List<CacheInfo> getCacheInfoList() {
        return cacheInfoList;
    }

    public void updateFrom(@Nullable final TuyaCacheInfo src) {
        cacheInfoList.clear();
        if (null == src) {
            cacheTime = 0L;
            return;
        }
        this.cacheTime = src.cacheTime;
        if (null != src.cacheInfoList) {
            this.cacheInfoList.addAll(src.cacheInfoList);
        }
        if (null == this.cacheInfoList || cacheInfoList.size() == 0) {
            cacheTime = 1;
        }
    }

    @Override
    public String toString() {
        return "TuyaCacheInfo{" +
                "cacheInfoList=" + cacheInfoList +
                '}';
    }

    @Override
    public boolean isNeedSaveCache() {
        return true;
    }

    public boolean removeDevice(String id, String subCategory) {
        return removeDevice(new CacheInfo(id, subCategory));
    }

    public boolean removeDevice(@Nullable CacheInfo cacheInfo) {
        if (null != cacheInfo && !TextUtils.isEmpty(cacheInfo.getId())) {
            boolean isRemove = false;
            Iterator<TuyaCacheInfo.CacheInfo> it = cacheInfoList.iterator();
            while (it.hasNext()) {
                if (it.next().getId().equals(cacheInfo.getId())) {
                    it.remove();
                    isRemove = true;
                    break;
                }
            }
            return isRemove;
        }
        return false;
    }

    @Keep
    public static class CacheInfo implements Serializable {
        private static final long serialVersionUID = 2143504326146860515L;
        private String id;
        private String subCategory;

        public CacheInfo() {
        }

        public CacheInfo(String id, String subCategory) {
            this.id = id;
            this.subCategory = subCategory;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getSubCategory() {
            return subCategory;
        }

        public void setSubCategory(String subCategory) {
            this.subCategory = subCategory;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            CacheInfo cacheInfo = (CacheInfo) o;
            return Objects.equals(id, cacheInfo.id) && Objects.equals(subCategory, cacheInfo.subCategory);
        }

        @Override
        public int hashCode() {
            return Objects.hash(id, subCategory);
        }

        @Override
        public String toString() {
            return "CacheInfo{" +
                    "id='" + id + '\'' +
                    ", subCategory='" + subCategory + '\'' +
                    '}';
        }
    }
}
