package com.dinsafer.module_tuya.tuya;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dincore.common.Cmd;
import com.dinsafer.dincore.common.CommonCmdEvent;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dssupport.plugin.PluginConstants;
import com.dinsafer.dssupport.plugin.PluginTypeHelper;
import com.dinsafer.dssupport.utils.DDLog;
import com.tuya.smart.sdk.api.IDevListener;
import com.tuya.smart.sdk.api.IResultCallback;
import com.tuya.smart.sdk.api.ITuyaDevice;
import com.tuya.smart.sdk.bean.DeviceBean;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public abstract class TuyaDevice extends Device implements IDevListener {
    protected static final String TAG = TuyaDevice.class.getSimpleName();

    protected DeviceBean sourceDevice;
    protected ITuyaDevice sourceTuyaDevice;

    public static TuyaDevice create(DeviceBean deviceBean) {
        for (String s : PluginTypeHelper.TUYA_COLOR_LIGHT_PRODUCTID) {
            if (s.equals(deviceBean.productId)) {
                return new TuyaBlubDevice(deviceBean);
            }
        }

        return new TuyaSwitchDevice(deviceBean);
    }

    @Nullable
    public static TuyaDevice createFromCache(TuyaCacheInfo.CacheInfo cacheInfo) {
        if (null != cacheInfo && !TextUtils.isEmpty(cacheInfo.getId()) && !TextUtils.isEmpty(cacheInfo.getSubCategory())) {
            if (TuyaConstants.ATTR_SUBCATEGORY_WIFI_PLUG.equals(cacheInfo.getSubCategory())) {
                return new TuyaSwitchDevice(cacheInfo);
            } else if (TuyaConstants.ATTR_SUBCATEGORY_WIFI_BULB.equals(cacheInfo.getSubCategory())) {
                return new TuyaBlubDevice(cacheInfo);
            }
        }
        return null;
    }

    public TuyaDevice(@NonNull TuyaCacheInfo.CacheInfo cacheInfo) {
        super();
        initFlagOnReadCache();
        setFlagDeleted(true);
        setFlagLoaded(true);
        this.setId(cacheInfo.getId());
        this.setCategory(PluginConstants.D_TYPE_12);
        this.setSubCategory(cacheInfo.getSubCategory());
    }

    public TuyaDevice(DeviceBean deviceBean) {
        super();
        initFlagOnNetwork();
        setFlagLoaded(true);
        this.sourceDevice = deviceBean;
        this.setId(deviceBean.devId);
        this.setCategory(PluginConstants.D_TYPE_12);
//        默认是灯泡
        this.setSubCategory("01");
        for (String s : PluginTypeHelper.TUYA_COLOR_LIGHT_PRODUCTID) {
            if (s.equals(deviceBean.getProductId())) {
                this.setSubCategory("02");
                break;
            }
        }

        sourceTuyaDevice = TuyaDelegate.getInstance().getDev(sourceDevice.getDevId());
        sourceTuyaDevice.registerDevListener(this);
    }

    public void setNeedLoadInfoAgain() {
        setFlagLoaded(false);
        setFlagLoaded(false);
    }

    public void markFlagLoaded() {
        setFlagLoaded(true);
    }

    protected void convertToInfo() {
        if (!isCanOperate()) {
            return;
        }

        getInfo().put(STATE, sourceDevice.getIsOnline() ? 1 : -1);
        getInfo().put(TuyaConstants.ATTR_PRODUCT_ID, sourceDevice.getProductId());
    }

    public boolean updata(DeviceBean deviceBean) {
        if (null == this.sourceDevice) {
            this.sourceDevice = deviceBean;
            sourceTuyaDevice = TuyaDelegate.getInstance().getDev(sourceDevice.getDevId());
            sourceTuyaDevice.registerDevListener(this);
        } else {
            this.sourceDevice = deviceBean;
        }
        this.setId(deviceBean.devId);
        this.setCategory(PluginConstants.D_TYPE_12);
//        默认是灯泡
        this.setSubCategory(TuyaConstants.ATTR_SUBCATEGORY_WIFI_PLUG);
        for (String s : PluginTypeHelper.TUYA_COLOR_LIGHT_PRODUCTID) {
            if (s.equals(deviceBean.productId)) {
                this.setSubCategory(TuyaConstants.ATTR_SUBCATEGORY_WIFI_BULB);
                break;
            }
        }
        convertToInfo();
        return true;

//        先不做以下判断，涂鸦返回什么，我们都认为有更新
//        String tempInfo = mapToString(getInfo());
//
//        String newInfo = mapToString(getInfo());
////        不相等，表示有数据更新
//        return !tempInfo.equals(newInfo);
    }

    public String mapToString(Map<String, ?> map) {
        StringBuilder mapAsString = new StringBuilder("{");
        for (String key : map.keySet()) {
            mapAsString.append(key + "=" + map.get(key) + ", ");
        }
        mapAsString.delete(mapAsString.length() - 2, mapAsString.length()).append("}");
        return mapAsString.toString();
    }

    public TuyaDevice(String id, int category, String subCategory, Map<String, Object> info) {
        super(id, category, subCategory, info);

    }

    public TuyaDevice(String id, int category, String subCategory, Map<String, Object> info, String fatherId) {
        super(id, category, subCategory, info, fatherId);
    }

    @Override
    public void submit(Map arg) {
        String cmd = (String) arg.get("cmd");
        if (!isCanOperate()) {
            if (TuyaCmd.DELETE_DEVICE.equals(cmd)) {
                remove();
            }
            return;
        }
        switch (cmd) {
            case TuyaCmd.SET_NAME:
                setName((String) arg.get("name"));
                break;
            case TuyaCmd.DELETE_DEVICE:
                delete();
                break;
            default: {
                Map result = Cmd.getDefaultResultMap(false, null);
                result.put("errorMessage", "cmd:" + cmd + " not support");
                dispatchResult(cmd, result);
            }

        }
    }

    void delete() {
        sourceTuyaDevice.removeDevice(new IResultCallback() {
            @Override
            public void onError(String code, String error) {
                Map result = TuyaCmd.getDefaultResultMap(false, TuyaCmd.DELETE_DEVICE);
                result.put("errorMessage", "code:" + code + " error:" + error);
                dispatchResult(TuyaCmd.DELETE_DEVICE, result);
            }

            @Override
            public void onSuccess() {
                destory();
                remove();
            }
        });

    }

    void setName(String name) {
        sourceTuyaDevice.renameDevice(name, new IResultCallback() {
            @Override
            public void onError(String code, String error) {
                Map result = TuyaCmd.getDefaultResultMap(false, TuyaCmd.SET_NAME);
                result.put("errorMessage", "code:" + code + " error:" + error);
                dispatchResult(TuyaCmd.SET_NAME, result);
            }

            @Override
            public void onSuccess() {
                DDLog.i(TAG, "set name success");
//                 通知服务器名字更新了，主机，Smart button用到,发送事件到panel模块
                JSONObject object = new JSONObject();
                try {
                    object.put("pluginid", sourceDevice.getDevId());
                    object.put("name", name);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                CommonCmdEvent commonCmdEvent = new CommonCmdEvent(Cmd.UPDATE_TUYA_DEVICE_NAME);
                commonCmdEvent.setExtra(object.toString());
                EventBus.getDefault().post(commonCmdEvent);


                Map map = TuyaCmd.getDefaultResultMap(true,
                        TuyaCmd.SET_NAME);
                Map result = new HashMap();
                result.put("name", name);
                map.put("result", result);
                dispatchResult(TuyaCmd.SET_NAME, map);
            }
        });
    }

    void publishDps(String dps, IResultCallback callback) {
        if (sourceTuyaDevice == null) {
            return;
        }

        sourceTuyaDevice.publishDps(dps, callback);

    }

    protected boolean isBlub() {
        return "02".equals(this.getSubCategory());
    }

    @Override
    public boolean equals(@Nullable Object obj) {
        return getId().equals(((TuyaDevice) obj).getId());
    }

    /**
     * DP 数据更新
     *
     * @param devId 设备 ID
     * @param dpStr 设备发生变动的功能点，为 JSON 字符串，数据格式：{"101": true}
     */
    @Override
    public void onDpUpdate(String devId, String dpStr) {
        DDLog.i(TAG, "onDpUpdate:" + devId + " dpStr:" + dpStr);
        DeviceBean newSource = TuyaDelegate.getInstance().getDevBean(devId);
        boolean hasUpdate = false;
        if (newSource != null) {
            hasUpdate = updata(newSource);
        }
//        因为涂鸦有时候自己返回所有字段，所以通过dpstrkey的数量，来区分是不是我们操作引起的变化，不然总会回调上去告诉他们设备信息变换了。
//        onDpUpdate:2605732484f3eb14bb28 dpStr:{"1":true,"10":"ffff0505ff000000ff00ffff00ff00ff0000ff000000","2":"white","3":116,"5":"677E020047647e","6":"00ff0000000000","7":"ffff500100ff00","8":"ffff8003ff000000ff000000ff000000000000000000","9":"ffff5001ff0000"}
//        2021年05月19日14:17:46，不用要以下代码，插座开关更改，会发送两个字段以上，
//        dpStr:{"1":true,"2":0}
//        try {
//            if (new JSONObject(dpStr).length() > 1) {
//                return;
//            }
//        } catch (JSONException e) {
//            e.printStackTrace();
//            return;
//        }
        if (hasUpdate) {
            onTuyaDpUpdate(devId, dpStr);
        }
    }

    public abstract void onTuyaDpUpdate(String devId, String dpStr);

    /**
     * 设备移除回调
     *
     * @param devId 设备id
     */
    @Override
    public void onRemoved(String devId) {
        DDLog.i(TAG, "onRemoved:" + devId);
        Map result = TuyaCmd.getDefaultResultMap(true, TuyaCmd.DELETE_DEVICE);
        dispatchResult(TuyaCmd.DELETE_DEVICE, result);
        destory();
//        不需要在这里回调devicechange，在tuyaservice统一回调
//        this.remove();
    }

    /**
     * 设备上下线回调。如果设备断电或断网，服务端将会在3分钟后回调到此方法。
     *
     * @param devId  设备 ID
     * @param online 是否在线，在线为 true
     */
    @Override
    public void onStatusChanged(String devId, boolean online) {
        DDLog.i(TAG, "onStatusChanged:" + devId + " online:" + online);
        if (online) {
            getInfo().put(STATE, 1);
            dispatchOnline();
        } else {
            getInfo().put(STATE, -1);
            dispatchOffline("");
        }
    }

    @Override
    public void onNetworkStatusChanged(String devId, boolean status) {

    }

    /**
     * 设备信息更新回调
     *
     * @param devId 设备 ID
     */
    @Override
    public void onDevInfoUpdate(String devId) {
        DDLog.i(TAG, "onDevInfoUpdate:" + devId);
//        由于涂鸦信息更新都在里，无法区分是否名字变化，所以通过缓存名字对比来判断名字是否变化
        Object oldName = getInfo().get("name");
        if (oldName == null) {
            return;
        }
        DeviceBean newSource = TuyaDelegate.getInstance().getDevBean(devId);
        if (newSource == null) {
            return;
        }
//        if (((String) oldName).equals(newSource.getName())) {
////            名字跟旧的一样，表示名字没有更新
//        } else {
//            名字跟旧的不一样，表示要更新
        updata(newSource);
        Map map = TuyaCmd.getDefaultResultMap(true,
                TuyaCmd.SET_NAME);
        Map result = new HashMap();
        result.put("name", newSource.getName());
        map.put("result", result);
        dispatchResult(TuyaCmd.SET_NAME, map);

//        }
    }


    @Override
    public void destory() {
        super.destory();
        if (sourceTuyaDevice != null) {
            sourceTuyaDevice.unRegisterDevListener();
            sourceTuyaDevice.onDestroy();
            sourceTuyaDevice = null;
        }
    }

    /**
     * 缓存的数据不能
     *
     * @return true 可以操作
     */
    protected boolean isCanOperate() {
        return !getFlagDeleted();
    }

    @NonNull
    protected Map<String, Object> getSourceDeviceDps() {
        Map<String, Object> result = null;
        if (null != sourceDevice) {
            result = sourceDevice.getDps();
        }
        if (null == result) {
            result = new HashMap<>();
        }
        return result;
    }
}
