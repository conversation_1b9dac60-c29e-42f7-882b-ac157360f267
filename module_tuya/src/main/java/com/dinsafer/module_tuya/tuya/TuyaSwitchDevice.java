package com.dinsafer.module_tuya.tuya;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dssupport.plugin.PluginTypeHelper;
import com.dinsafer.dssupport.utils.DDLog;
import com.google.gson.Gson;
import com.tuya.smart.android.device.api.IGetDataPointStatCallback;
import com.tuya.smart.android.device.bean.DataPointStatBean;
import com.tuya.smart.android.device.enums.DataPointTypeEnum;
import com.tuya.smart.sdk.api.IResultCallback;
import com.tuya.smart.sdk.bean.DeviceBean;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

class TuyaSwitchDevice extends TuyaDevice {
    private static final String TAG = TuyaSwitchDevice.class.getSimpleName();
    private boolean isPidFor20Dp;
    private Map<String, String> supportCmd = new HashMap<>();

    public TuyaSwitchDevice(DeviceBean deviceBean) {
        super(deviceBean);
        convertToInfo();
    }

    public TuyaSwitchDevice(@NonNull TuyaCacheInfo.CacheInfo cacheInfo) {
        super(cacheInfo);
    }

    protected void convertToInfo() {
        if (!isCanOperate()) {
            return;
        }

        super.convertToInfo();
//       有一下几个字段要暴露出去
//        插座：
//        1. name
//        2. isOn
        Map jsonObject = this.getInfo();
        jsonObject.put(TuyaConstants.ATTR_NAME, sourceDevice.getName());
        isPidFor20Dp = PluginTypeHelper.getInstance()
                .isTuyaPid20Dp(sourceDevice.getProductId());
        boolean isOn;
        boolean isPidFor20Dp = PluginTypeHelper.getInstance()
                .isTuyaPid20Dp(sourceDevice.getProductId());
        if (isPidFor20Dp) {
            isOn = (boolean) getSourceDeviceDps().get("20");
        } else {
            isOn = (boolean) getSourceDeviceDps().get("1");
        }
        jsonObject.put(TuyaConstants.ATTR_IS_ON, isOn);
        if (getSourceDeviceDps().containsKey("4")) {
            jsonObject.put(TuyaConstants.ATTR_CURRENT, (Integer) getSourceDeviceDps().get("4"));
        }
        if (getSourceDeviceDps().containsKey("5")) {
            jsonObject.put(TuyaConstants.ATTR_POWER_CONSUME, (Integer) getSourceDeviceDps().get("5"));
        }
        if (getSourceDeviceDps().containsKey("6")) {
            jsonObject.put(TuyaConstants.ATTR_VOLTAGE, (Integer) getSourceDeviceDps().get("6") / 10.0f);
        }

        if (isPidFor20Dp) {
            supportCmd.put("20", TuyaCmd.TUYA_SET_ON);
        } else {
            supportCmd.put("1", TuyaCmd.TUYA_SET_ON);
        }

    }


    private void setOn(boolean isOn) {
        String dps;

        if (isPidFor20Dp) {
            if (!isOn) {
                dps = "{\"20\": false}";
            } else {
                dps = "{\"20\": true }";
            }
        } else {
            if (!isOn) {
                dps = "{\"1\": false}";
            } else {
                dps = "{\"1\": true }";
            }
        }

        publishDps(dps, new IResultCallback() {
            @Override
            public void onError(String code, String error) {
                Map result = TuyaCmd.getDefaultResultMap(false, TuyaCmd.TUYA_SET_ON);
                result.put("errorMessage", "code:" + code + " error:" + error);
                dispatchResult(TuyaCmd.TUYA_SET_ON, result);
            }

            @Override
            public void onSuccess() {
                Map result = TuyaCmd.getDefaultResultMap(true, TuyaCmd.TUYA_SET_ON);
                result.put(TuyaConstants.ATTR_IS_ON, isOn);
                dispatchResult(TuyaCmd.TUYA_SET_ON, result);
            }
        });

    }

    @Override
    public void submit(Map arg) {
        String cmd = (String) arg.get("cmd");
        if (!isCanOperate()) {
            DDLog.e(TAG, "当前涂鸦设备为缓存设备，不能进行操作");
            if (TuyaCmd.DELETE_DEVICE.equals(cmd)) {
                super.submit(arg);
            }
            return;
        }

        switch (cmd) {
            case TuyaCmd.TUYA_SET_ON:
                setOn((Boolean) arg.get("isOn"));
                break;
            case TuyaCmd.TUYA_GET_ENERGY:
                getEnergy((String) arg.get("type"), (Long) arg.get("start_time"), (Integer) arg.get("count"));
                break;
            default:
                super.submit(arg);
                break;
        }
    }

    private void getEnergy(String type, Long startTime, int count) {
        DDLog.d(TAG, "getEnergy: " + type);
        DataPointTypeEnum _type = DataPointTypeEnum.DAY;
        switch (type) {
            case "day":
                _type = DataPointTypeEnum.DAY;
                break;
            case "week":
                _type = DataPointTypeEnum.WEEK;
                break;
            case "month":
                _type = DataPointTypeEnum.MONTH;
                break;
        }

        String key = "";
        if (getSourceDeviceDps().containsKey("3")) {
            key = "3";
        } else if (getSourceDeviceDps().containsKey("17")) {
            key = "17";
        }
        if (TextUtils.isEmpty(key)) {
            Map result = TuyaCmd.getDefaultResultMap(false, TuyaCmd.TUYA_GET_ENERGY);
            result.put("errorMessage", "code:" + ErrorCode.PARAM_ERROR + " error:" + "unknown type");
            dispatchResult(TuyaCmd.TUYA_GET_ENERGY, result);
            return;
        }

        DataPointTypeEnum final_type = _type;
        sourceTuyaDevice.getDataPointStat(_type, startTime, count, key, new IGetDataPointStatCallback() {
            @Override
            public void onError(String errorCode, String errorMsg) {
                DDLog.i(TAG, "getEnergy fail:" + errorCode + " errorMsg:" + errorMsg);
                Map result = TuyaCmd.getDefaultResultMap(false, TuyaCmd.TUYA_GET_ENERGY);
                result.put("errorMessage", "code:" + errorCode + " error:" + errorMsg);
                Map data = new HashMap();
                data.put("type", final_type.getType());
                result.put("result", data);
                dispatchResult(TuyaCmd.TUYA_GET_ENERGY, result);
            }

            @Override
            public void onSuccess(DataPointStatBean bean) {
                Map result = TuyaCmd.getDefaultResultMap(true, TuyaCmd.TUYA_GET_ENERGY);
                Map data = new HashMap();
                data.put("type", final_type.getType());
                data.put("total", bean.getTotal());
                if (bean.getData() != null) {
                    data.put("data", new Gson().toJson(bean.getData()));
                } else {
                    data.put("data", "[]");
                }
                result.put("result", data);
                dispatchResult(TuyaCmd.TUYA_GET_ENERGY, result);
            }
        });


    }


    @Override
    public void onTuyaDpUpdate(String devId, String dpStr) {
        try {
            JSONObject jsonObject = new JSONObject(dpStr);
            //            里边包含什么字段，就表示什么东西更新了

            Iterator<String> sIterator = jsonObject.keys();
            while (sIterator.hasNext()) {
                String key = sIterator.next();
                if (supportCmd.containsKey(key)) {
                    dispatchResult(supportCmd.get(key), getTuyaResult(supportCmd.get(key),
                            jsonObject.get(key)));
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private Map getTuyaResult(String cmd, Object value) {

        Map data = TuyaCmd.getDefaultResultMap(true, cmd);
        Map result = new HashMap();
        switch (cmd) {
            case TuyaCmd.TUYA_SET_ON:
                result.put("isOn", value);
                break;
        }
        data.put("result", result);
        return data;
    }


}
