package com.dinsafer.module_tuya.tuya.http;

import android.text.TextUtils;

import com.dinsafer.dincore.http.Api;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.user.UserManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;

class TuyaApi {

    private final ITuyaApi services;
    public static final String GM = "gm";
    public static final String GMTIME = "gmtime";

    private TuyaApi() {
        services = Api.getApi().getRetrofit().create(ITuyaApi.class);
    }

    private static class Holder {
        private static final TuyaApi INSTANT = new TuyaApi();
    }

    public static TuyaApi getApi() {
        return Holder.INSTANT;
    }

    private Map<String, Object> getGM(Map<String, Object> map) {
        map.put(GM, 1);
        return map;
    }

    private JSONObject getGMTime(JSONObject jsonObject) {
        try {
            jsonObject.put(GMTIME, System.currentTimeMillis() * 1000);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObject;
    }

    public Call<TuyaAccountResponse> getAccount(String home_id) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        String userToken = UserManager.getInstance().getToken();
        if (!TextUtils.isEmpty(userToken)) {
            map.put("token", userToken);
        } else {
            map.put("token", "");
        }

        return services.getAccountCall(Api.getApi().getUrl(TuyaUrls.URL_GET_ACCOUNT), map);
    }

    public Call<StringResponseEntry> setAccount(String home_id, String password, String username, String countrycode, String uid, String mobileApiUrl) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
            jsonObject.put("password", password);
            jsonObject.put("username", username);
            jsonObject.put("countrycode", countrycode);
            jsonObject.put("uid", uid);
            jsonObject.put("mobileApiUrl", mobileApiUrl);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        String userToken = UserManager.getInstance().getToken();
        if (!TextUtils.isEmpty(userToken)) {
            map.put("token", userToken);
        } else {
            map.put("token", "");
        }

        return services.setAccountCall(Api.getApi().getUrl(TuyaUrls.URL_SET_ACCOUNT), map);
    }


}
