package com.dinsafer.module_tuya.tuya;


import android.app.Application;
import android.content.Context;
import android.util.Log;

import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_tuya.tuya.http.TuyaRepository;
import com.google.gson.Gson;
import com.tuya.smart.android.user.api.ILogoutCallback;
import com.tuya.smart.android.user.api.IUidLoginCallback;
import com.tuya.smart.android.user.bean.User;
import com.tuya.smart.home.sdk.TuyaHomeSdk;
import com.tuya.smart.home.sdk.api.ITuyaHome;
import com.tuya.smart.home.sdk.api.ITuyaHomeDeviceStatusListener;
import com.tuya.smart.home.sdk.api.ITuyaHomeStatusListener;
import com.tuya.smart.home.sdk.bean.HomeBean;
import com.tuya.smart.home.sdk.builder.ActivatorBuilder;
import com.tuya.smart.home.sdk.callback.ITuyaGetHomeListCallback;
import com.tuya.smart.home.sdk.callback.ITuyaHomeResultCallback;
import com.tuya.smart.sdk.api.INeedLoginListener;
import com.tuya.smart.sdk.api.ITuyaActivator;
import com.tuya.smart.sdk.api.ITuyaActivatorGetToken;
import com.tuya.smart.sdk.api.ITuyaDevice;
import com.tuya.smart.sdk.api.ITuyaSmartActivatorListener;
import com.tuya.smart.sdk.bean.DeviceBean;
import com.tuya.smart.sdk.enums.ActivatorModelEnum;

import java.util.ArrayList;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class TuyaDelegate {

    private static volatile TuyaDelegate instance;

    private static String DefaultHomeName = "default_home";
    private static String DefaultRoomName = "default_room";

    private static final String TAG = TuyaDelegate.class.getSimpleName();

    private HomeBean homeBean;

    private ITuyaHome iTuyaHome;

    private List<ITuyaDeviceChange> tuyaDeviceChangesList = new ArrayList<>();

    private TuyaRepository tuyaRepository;

    private ITuyaHomeStatusListener listener = new ITuyaHomeStatusListener() {

        @Override
        public void onDeviceAdded(String devId) {
            DDLog.i(TAG, "onDeviceAdded:" + devId);


            for (ITuyaDeviceChange deviceChange :
                    tuyaDeviceChangesList) {
                deviceChange.onDeviceChange(true, devId);
            }

        }

        @Override
        public void onDeviceRemoved(String devId) {
//            TODO 测试一下这个回调是否需要
            DDLog.i(TAG, "onDeviceRemoved:" + devId);
//            for (DeviceBean deviceBean : TuyaDelegate.getInstance().getLocalDeviceList()) {
//                if (deviceBean.getDevId().equals(devId)) {
//                    DeivceChangeEvent event = new DeivceChangeEvent(TuyaDevice.create(deviceBean));
//                    event.setRemove(true);
//                    EventBus.getDefault().post(event);
//                }
//            }
            for (ITuyaDeviceChange deviceChange :
                    tuyaDeviceChangesList) {
                deviceChange.onDeviceChange(false, devId);
            }

        }

        @Override
        public void onGroupAdded(long groupId) {

        }

        @Override
        public void onGroupRemoved(long groupId) {

        }

        @Override
        public void onMeshAdded(String meshId) {

        }
    };


    public TuyaDelegate(Application application, String appKey, String appSecret) {
        TuyaHomeSdk.init(application, appKey, appSecret);
        tuyaRepository = new TuyaRepository();
    }

    public static void init(Application application, String appKey, String appSecret) {
        if (instance == null) {
            synchronized (TuyaDelegate.class) {
                if (instance == null) {
                    instance = new TuyaDelegate(application, appKey, appSecret);
                }
            }
        }
    }

    public static TuyaDelegate getInstance() {
        return instance;
    }

    public User getUser() {
        return TuyaHomeSdk.getUserInstance().getUser();
    }

    public void setDebugMode(boolean debug) {
        TuyaHomeSdk.setDebugMode(debug);
    }

    public void destory() {
        TuyaHomeSdk.onDestroy();
    }

    public boolean isLogin() {
        return TuyaHomeSdk.getUserInstance().isLogin();
    }

    public List<DeviceBean> getLocalDeviceList() {
        if (homeBean == null) {
            DDLog.e(TAG + "-涂鸦",
                    "当前未登录涂鸦或登录的不是该主机的涂鸦账号，不能获取涂鸦配件列表!!!!!!");
            return new ArrayList<>();
        }
        return TuyaHomeSdk.getDataInstance().getHomeDeviceList(homeBean.getHomeId());
    }

    public void getActivatorToken(ITuyaActivatorGetToken callback) {
//        if (homeBean == null) {
//            if (callback != null) {
//                callback.onFailure("-1", "homebean null");
//            }
//            return;
//        }
//        TuyaHomeSdk.getActivatorInstance().getActivatorToken(homeBean.getHomeId(),
//                callback);
        checkIsHasDefaultHomeAndRoom(new ICheckHomeAndRoomCallBack() {
            @Override
            public void onSuccess() {
                TuyaHomeSdk.getActivatorInstance().getActivatorToken(homeBean.getHomeId(),
                        callback);
            }

            @Override
            public void onError(String code, String msg) {
                if (callback != null) {
                    callback.onFailure("-1", "homebean null");
                }
                return;
            }
        });
    }

    public ITuyaActivator getActivator(Context context, ITuyaSmartActivatorListener listener,
                                       String ssid, String password, String token) {
        return TuyaHomeSdk.getActivatorInstance().newActivator(new ActivatorBuilder()
                .setSsid(ssid)
                .setContext(context)
                .setPassword(password)
                //EZ模式
                .setActivatorModel(ActivatorModelEnum.TY_EZ)
                //超时时间
                .setTimeOut(100)
                //unit is seconds
                .setToken(token)
                .setListener(listener));
    }

    public void login(String home_id, String countryCode, String username, String pwd, boolean isRegister, final IDefaultCallBack callback) {
        if (TuyaHomeSdk.getUserInstance().isLogin() &&
                getUser().getUsername().equals(username)) {
            DDLog.i(TAG, username + " 登陆了,不重复登录");
            if (callback == null) {
                return;
            }

            callback.onSuccess();
            return;
        }

        logout(new ILogoutCallback() {
            @Override
            public void onSuccess() {
                DDLog.i(TAG, "logout-->onSuccess: ");
                loginInternal(home_id, countryCode, username, pwd, isRegister, callback);
            }

            @Override
            public void onError(String code, String error) {
                DDLog.e(TAG, "logout-->onError: " + error);
                loginInternal(home_id, countryCode, username, pwd, isRegister, callback);
            }
        });

    }

    private void loginInternal(String home_id, String countryCode, String username, String pwd, boolean isRegister, final IDefaultCallBack callback) {
        TuyaHomeSdk.getUserInstance().loginOrRegisterWithUid(countryCode,
                username, pwd, false, new IUidLoginCallback() {
                    @Override
                    public void onSuccess(final User user, final long homeId) {
                        if (isRegister) {
                            tuyaRepository.setAccount(home_id, pwd, user.getUsername(), user.getPhoneCode(), user.getUid(), user.getDomain().getMobileApiUrl(), new Callback<StringResponseEntry>() {
                                @Override
                                public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                                    DDLog.i(TAG, "login-->setAccount:保存涂鸦账号成功");
                                }

                                @Override
                                public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                                    DDLog.e(TAG, "login-->setAccount:保存涂鸦账号失败 " + t.getMessage());
                                }
                            });
                        }
                        DDLog.i(TAG, "login-->登录成功: " + new Gson().toJson(user));
                        checkIsHasDefaultHomeAndRoom(new ICheckHomeAndRoomCallBack() {
                            @Override
                            public void onSuccess() {
                                if (callback == null) {
                                    return;
                                }

                                callback.onSuccess();

                            }

                            @Override
                            public void onError(String code, String msg) {
                                if (callback == null) {
                                    return;
                                }

                                callback.onError(-1, code + ":" + msg);
                            }
                        });
                    }

                    @Override
                    public void onError(String code, String error) {
                        if (callback == null) {
                            return;
                        }

                        callback.onError(-1, code + ":" + error);
                    }
                });
    }

    public void logout(ILogoutCallback callback) {
        TuyaHomeSdk.getUserInstance().logout(callback);
    }

    public DeviceBean getDevBean(String id) {
        return TuyaHomeSdk.getDataInstance().getDeviceBean(id);
    }

    public void registeDevicesChange(ITuyaDeviceChange callback) {
        if (tuyaDeviceChangesList.contains(callback)) {
            return;
        }
        tuyaDeviceChangesList.add(callback);
    }

    public void unregisteDevicesChange(ITuyaDeviceChange callback) {
        if (!tuyaDeviceChangesList.contains(callback)) {
            return;
        }
        tuyaDeviceChangesList.remove(callback);
    }

    public void setOnNeedLoginListener(INeedLoginListener listener) {
        TuyaHomeSdk.setOnNeedLoginListener(listener);
    }

    public ITuyaDevice getDev(String id) {
        return TuyaHomeSdk.newDeviceInstance(id);
    }

    public void addTuyaDevice(DeviceBean bean) {
        homeBean.getDeviceList().add(bean);
    }

    public void setDevName(String id, String name) {
        DeviceBean deviceBean = getDevBean(id);
        if (deviceBean == null) {
            return;
        }

        deviceBean.setName(name);
    }


    public void registerDeviceStatusListener(ITuyaHomeDeviceStatusListener deviceStatusListener) {
        if (homeBean == null || iTuyaHome == null) {
            return;
        }
        iTuyaHome.registerHomeDeviceStatusListener(deviceStatusListener);
    }

    public void unRegisterDeviceStatusListener(ITuyaHomeDeviceStatusListener
                                                       deviceStatusListener) {
        if (homeBean == null || iTuyaHome == null) {
            return;
        }
        iTuyaHome.unRegisterHomeDeviceStatusListener(deviceStatusListener);
    }


    public void queryDevList(final IGetTuyaDeviceCallBack callBack) {
        checkIsHasDefaultHomeAndRoom(new ICheckHomeAndRoomCallBack() {
            @Override
            public void onSuccess() {
//                为什么还要请求homedetail，因为要刷新homeBean信息，否则拿到的设备还是旧的
                DDLog.i(TAG, "getHomeDetail：" + homeBean.toString() + " "
                        + Thread.currentThread().getName());
                iTuyaHome.getHomeDetail(new ITuyaHomeResultCallback() {
                    @Override
                    public void onSuccess(HomeBean bean) {
                        // do something
                        homeBean = bean;
                        DDLog.i(TAG, "涂鸦有device：" + homeBean.getDeviceList().size());
                        if (callBack == null) {
                            return;
                        }
                        callBack.onSuccess(homeBean.getDeviceList());
                    }

                    @Override
                    public void onError(String errorCode, String errorMsg) {
                        DDLog.i(TAG, "getHomeDetail error ：" + homeBean.toString()
                                + "-->>errmsg:" + errorCode + ":" + errorMsg);
                        if (callBack == null) {
                            return;
                        }
                        callBack.onError(errorCode, errorMsg);
                    }
                });
            }

            @Override
            public void onError(String code, String msg) {
                DDLog.i(TAG, "checkIsHasDefaultHomeAndRoom onError：" + msg);
                if (callBack == null) {
                    return;
                }
                callBack.onError(code, msg);
            }
        });

    }

    public void checkIsHasDefaultHomeAndRoom(final ICheckHomeAndRoomCallBack callBack) {
//        if (homeBean != null && iTuyaHome != null) {
//            DDLog.i(TAG, "checkIsHasDefaultHomeAndRoom：" + homeBean.getDeviceList().size());
//            callBack.onSuccess();
//            return;
//        }
        DDLog.i(TAG, "checkIsHasDefaultHomeAndRoom start");
        TuyaHomeSdk.getHomeManagerInstance().queryHomeList(new ITuyaGetHomeListCallback() {
            @Override
            public void onSuccess(List<HomeBean> homeBeans) {
//                // do something
//                if (homeBeans == null || homeBeans.size() <= 0) {
//                    callBack.onError("-1", "homebeans error");
//                    return;
//                }
                DDLog.i(TAG, "checkIsHasDefaultHomeAndRoom onSuccess：" + homeBeans.size());

                for (HomeBean homeBean : homeBeans) {
                    if (homeBean.getName().equals(DefaultHomeName)) {
                        DDLog.i(TAG, "checkIsHasDefaultHomeAndRoom-->onSuccess: 存在" + DefaultHomeName);
                        TuyaDelegate.this.homeBean = homeBean;
                        if (iTuyaHome != null) {
                            iTuyaHome.unRegisterHomeStatusListener(listener);
                        }
                        iTuyaHome = TuyaHomeSdk.newHomeInstance(homeBean.getHomeId());
                        iTuyaHome.registerHomeStatusListener(listener);
                        callBack.onSuccess();
                        return;
                    }
                }
                DDLog.i(TAG, "checkIsHasDefaultHomeAndRoom-->onSuccess: 没有家庭，创建一个默认的家庭");
                ArrayList<String> arrayList = new ArrayList<String>();
                arrayList.add(DefaultRoomName);
                TuyaHomeSdk.getHomeManagerInstance().createHome(DefaultHomeName,
                        0, 0, "", arrayList,
                        new ITuyaHomeResultCallback() {
                            @Override
                            public void onSuccess(HomeBean bean) {
                                // do something
                                DDLog.i(TAG, "createHome-->onSuccess: 创建家庭成功");
                                TuyaDelegate.this.homeBean = bean;
                                if (iTuyaHome != null) {
                                    iTuyaHome.unRegisterHomeStatusListener(listener);
                                }
                                iTuyaHome = TuyaHomeSdk.newHomeInstance(homeBean.getHomeId());
                                iTuyaHome.registerHomeStatusListener(listener);
                                callBack.onSuccess();
                            }

                            @Override
                            public void onError(String errorCode, String errorMsg) {
                                // do something
                                callBack.onError(errorCode, errorMsg);
                            }
                        });


            }

            @Override
            public void onError(String errorCode, String error) {
                // do something
                callBack.onError(errorCode, error);
            }
        });
    }
}
