package com.dinsafer.module_tuya;

import android.app.Application;
import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.common.CommonCmdEvent;
import com.dinsafer.dincore.common.DeivceChangeEvent;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IService;
import com.dinsafer.dincore.db.cache.DeviceCacheHelper;
import com.dinsafer.dincore.user.bean.DinUser;
import com.dinsafer.dincore.utils.AppStateEvent;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dincore.utils.MapUtils;
import com.dinsafer.dincore.utils.RandomStringUtils;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_tuya.tuya.IGetTuyaDeviceCallBack;
import com.dinsafer.module_tuya.tuya.ITuyaDeviceChange;
import com.dinsafer.module_tuya.tuya.TuyaBinder;
import com.dinsafer.module_tuya.tuya.TuyaCacheInfo;
import com.dinsafer.module_tuya.tuya.TuyaConstants;
import com.dinsafer.module_tuya.tuya.TuyaDelegate;
import com.dinsafer.module_tuya.tuya.TuyaDevice;
import com.tuya.smart.android.user.bean.User;
import com.tuya.smart.home.sdk.TuyaHomeSdk;
import com.tuya.smart.sdk.api.INeedLoginListener;
import com.tuya.smart.sdk.bean.DeviceBean;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Keep
public class TuyaService implements IService, ITuyaDeviceChange {
    private static final String BINDER_KEY_TUYA = "tuya_binder";
    private static final String CACHE_IDENTIFY = "TuyaService"; // 缓存key后缀

    private static final String TAG = TuyaService.class.getSimpleName();

    private String tuyaKey, tuyaSecret;

    private Application app;

    private boolean isDebug;

    //    list的操作要加锁，否则在遍历的时候，去remove，就会奔溃
    private List<Device> mTuyaDevice = new ArrayList<>();
    //    涂鸦原始数据
    private List<DeviceBean> sourceTuyaDevice = new ArrayList<>();

    private static final byte[] fetchDeviceLock = new byte[0];
    private static final byte[] loginLock = new byte[0];
    private static final byte[] listLock = new byte[0];

    //    支持的配件类型
    private Map<String, String> supportDeivceType = new HashMap<>();

    private Map<String, Object> configMap;

    private String currentHomeID;

    private String domain = "";
    private String uid = "";
    private String username = "";
    private String password = "";
    private String countrycode = "";

    private final TuyaCacheInfo cacheInfo = new TuyaCacheInfo();

    private boolean isForeground;

    public TuyaService(String tuyaKey, String tuyaSecret, Application app, boolean isDebug) {
        this.tuyaKey = tuyaKey;
        this.tuyaSecret = tuyaSecret;
        this.app = app;
        this.isDebug = isDebug;
        EventBus.getDefault().register(this);
    }

    @Override
    public void load() {
        DDLog.i(TAG, "load: ");
        supportDeivceType.put("WIFIPlug", TuyaConstants.ATTR_SUBCATEGORY_WIFI_PLUG);
        supportDeivceType.put("WIFIBulb", TuyaConstants.ATTR_SUBCATEGORY_WIFI_BULB);
        TuyaDelegate.init(app, tuyaKey, tuyaSecret);
//        TuyaDelegate.getInstance().setDebugMode(isDebug);
        TuyaDelegate.getInstance().registeDevicesChange(this);
    }

    @Override
    public void unLoad() {
        DDLog.i(TAG, "unLoad: ");
        EventBus.getDefault().unregister(this);
        TuyaDelegate.getInstance().destory();
    }

    @Override
    public void config(Map<String, Object> arg) {
        configMap = arg;
        final String lastHomeId = currentHomeID;
        currentHomeID = (String) MapUtils.get(arg, "homeID", "");
        DDLog.d(TAG, "config-->currentHomeID:" + currentHomeID);
        domain = "";
        uid = "";
        username = "";
        password = "";
        countrycode = "";
        sourceTuyaDevice.clear();

        if (!TextUtils.isEmpty(lastHomeId) && !lastHomeId.equals(currentHomeID)) {
            // 切换了家庭
            cacheInfo.updateFrom(null);
        } else {
            mTuyaDevice.clear();
        }
    }

    @Subscribe
    public void onEvent(CommonCmdEvent commonCmdEvent) {
        DDLog.i(TAG, "on Event: commonCmdEvent " + commonCmdEvent.getCmd() + " /" + commonCmdEvent.getExtra());
        if (CommonCmdEvent.CMD.GET_HOME_INFO.equals(commonCmdEvent.getCmd())
                && !TextUtils.isEmpty(commonCmdEvent.getExtra())) {
            try {
                JSONObject homeInfo = new JSONObject(commonCmdEvent.getExtra());
                JSONObject tuyaInfo = DDJSONUtil.getJSONObject(homeInfo, "tuyaInfo");

                //获取涂鸦账户信息，注册或登录涂鸦
                if (tuyaInfo != null) {
                    domain = DDJSONUtil.getString(tuyaInfo, "domain");
                    uid = DDJSONUtil.getString(tuyaInfo, "uid");
                    username = DDJSONUtil.getString(tuyaInfo, "username");
                    password = DDJSONUtil.getString(tuyaInfo, "password");
                    countrycode = DDJSONUtil.getString(tuyaInfo, "countrycode");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (CommonCmdEvent.CMD.ON_BEFORE_HOME_DISCONNECT.equals(commonCmdEvent.getCmd())) {
            final List<Device> localList = this.mTuyaDevice;
            if (null != localList && localList.size() > 0) {
                for (Device device : localList) {
                    if (device instanceof TuyaDevice) {
                        ((TuyaDevice) device).setNeedLoadInfoAgain();
                    }
                }
            }
        }
    }

    private void login() {
        if (!TextUtils.isEmpty(uid)
                && !TextUtils.isEmpty(username)
                && !TextUtils.isEmpty(password)
                && !TextUtils.isEmpty(countrycode)) {

            User user = TuyaHomeSdk.getUserInstance().getUser();
            if (TuyaHomeSdk.getUserInstance().isLogin()
                    && user != null
                    && !TextUtils.isEmpty(user.getUsername())
                    && user.getUsername().equals(username)) {
                DDLog.d(TAG, "login--> " + user.getUsername() + " 已登录，不重新登录。");
                //注册token过期监听
                TuyaHomeSdk.setOnNeedLoginListener(new INeedLoginListener() {
                    @Override
                    public void onNeedLogin(Context context) {
                        // 需要重新登录
                        DDLog.d(TAG, "onNeedLogin: Token过期，需要重新登录");

                    }
                });

                synchronized (loginLock) {
                    loginLock.notify();
                }
                return;
            }

            DDLog.d(TAG, "login-->开始登录：" + currentHomeID + " /countrycode:" + countrycode + " /username:" + username);

            TuyaDelegate.getInstance().login(currentHomeID, countrycode, username, password, false, new IDefaultCallBack() {
                @Override
                public void onSuccess() {
                    DDLog.d(TAG, "login-->涂鸦登录成功。currentHomeID:" + currentHomeID + " /tuya username:" + username);
                    synchronized (loginLock) {
                        loginLock.notify();
                    }
                }

                @Override
                public void onError(int code, String error) {
                    DDLog.e(TAG, "login-->涂鸦登录失败。" + error);
                    synchronized (loginLock) {
                        loginLock.notify();
                    }
                }
            });
        } else {
            DDLog.d(TAG, "login-->开始注册：" + currentHomeID + " /domain:" + domain);
            String name = RandomStringUtils.getMessageId();
            String pwd = RandomStringUtils.getMessageId();
            TuyaDelegate.getInstance().login(currentHomeID, domain, name, pwd, true, new IDefaultCallBack() {
                @Override
                public void onSuccess() {
                    DDLog.d(TAG, "login-->涂鸦注册成功。currentHomeID:" + currentHomeID);
                    try {
                        countrycode = TuyaHomeSdk.getUserInstance().getUser().getPhoneCode();
                        uid = TuyaHomeSdk.getUserInstance().getUser().getUid();
                        username = TuyaHomeSdk.getUserInstance().getUser().getUsername();
                        password = pwd;
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    synchronized (loginLock) {
                        loginLock.notify();
                    }
                }

                @Override
                public void onError(int code, String error) {
                    DDLog.e(TAG, "login-->涂鸦注册失败:" + currentHomeID);
                    synchronized (loginLock) {
                        loginLock.notify();
                    }
                }
            });
        }

        try {
            DDLog.i(TAG, "fetchDevices-->等待涂鸦登录结果...");
            loginLock.wait();
            DDLog.i(TAG, "fetchDevices-->涂鸦登录结束，开始获取设备列表");
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Override
    public List<Device> fetchDevices() {
        synchronized (loginLock) {
            login();
        }

        readCacheInfo();
        sourceTuyaDevice = TuyaDelegate.getInstance().getLocalDeviceList();
//        if (sourceTuyaDevice.size() <= 0) {
//           通过网络获取涂鸦设备
        synchronized (fetchDeviceLock) {
            DDLog.i(TAG, "fetchDevices-->get device by NetWork");
            TuyaDelegate.getInstance().queryDevList(new IGetTuyaDeviceCallBack() {
                @Override
                public void onSuccess(List<DeviceBean> deviceBeanList) {
                    sourceTuyaDevice = deviceBeanList;
                    synchronized (fetchDeviceLock) {
                        fetchDeviceLock.notify();
                    }
                }

                @Override
                public void onError(String code, String msg) {
                    synchronized (fetchDeviceLock) {
                        fetchDeviceLock.notify();
                    }
                }
            });

            try {
                DDLog.i(TAG, "fetchDevices-->waiting tuya device");
                fetchDeviceLock.wait();
                DDLog.i(TAG, "fetchDevices-->get tuya device finish");
                DDLog.i(TAG, "fetchDevices-->source device size:" + (sourceTuyaDevice == null ? 0 : sourceTuyaDevice.size()));

            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
//        }

        List<DeviceBean> sourceDeviceTemp = new ArrayList<>();
        for (DeviceBean deviceBean : sourceTuyaDevice) {
            boolean isContain = false;
            for (DeviceBean bean : sourceDeviceTemp) {
                if (bean.getDevId().equals(deviceBean.getDevId())) {
                    isContain = true;
                    break;
                }
            }
            if (!isContain) {
                sourceDeviceTemp.add(deviceBean);
            }
        }
        sourceTuyaDevice = sourceDeviceTemp;

        try {
            //删除mTuyaDevice里被删除的设备
            if (mTuyaDevice.size() != sourceTuyaDevice.size()) {
                DDLog.i(TAG, "fetchDevices-->数量不一致，修正数据，以tuya数据为准");
                List<Device> tempList = new ArrayList<>();
                for (Device device : mTuyaDevice) {
                    boolean isContain = false;
                    for (DeviceBean deviceBean : sourceTuyaDevice) {
                        if (device.getId().equals(deviceBean.getDevId())) {
                            isContain = true;
                            break;
                        }
                    }
                    if (!isContain) {
                        // 错误的数据并且不在缓存中
                        if (!cacheInfo.isCacheDevice(device.getId(), device.getSubCategory())) {
                            tempList.add(device);
                        } else {
                            // 如果还在缓存列表中，不需要删除Device，仅标记为被删除状态
                            device.setFlagDeleted(true);
                        }
                    }
                }

                mTuyaDevice.removeAll(tempList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            DDLog.i(TAG, "fetchDevices-->for1:" + e.getLocalizedMessage());
        }


        try {
            //遍历source device,更新mTuyaDevice里已存在的设备,或者把新设备加到mTuyaDevice里.
            for (DeviceBean deviceBean : sourceTuyaDevice) {
                String id = null;
                String subcategory = null;
                boolean isContain = false;
                for (Device device : mTuyaDevice) {
                    if (device.getId().equals(deviceBean.getDevId())) {
                        ((TuyaDevice) device).updata(deviceBean);
                        device.setFlagDeleted(false);
                        isContain = true;

                        id = device.getId();
                        subcategory = device.getSubCategory();
                        device.setFlagCache(cacheInfo.isCacheDevice(id, subcategory));
                        ((TuyaDevice) device).markFlagLoaded();
                        break;
                    }
                }
                if (!isContain) {
                    TuyaDevice tuyaDevice = TuyaDevice.create(deviceBean);
                    mTuyaDevice.add(tuyaDevice);

                    id = tuyaDevice.getId();
                    subcategory = tuyaDevice.getSubCategory();
                    tuyaDevice.setFlagCache(cacheInfo.isCacheDevice(id, subcategory));
                    tuyaDevice.markFlagLoaded();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            DDLog.i(TAG, "fetchDevices-->for2:" + e.getLocalizedMessage());
        }

        // 遍历缓存数据，创建已经被删除的Device
        for (TuyaCacheInfo.CacheInfo info : cacheInfo.getCacheInfoList()) {
            String id = info.getId();
            String subCategory = info.getSubCategory();
            if (TextUtils.isEmpty(id) || TextUtils.isEmpty(subCategory)) {
                continue;
            }

            boolean found = false;
            for (int i = 0; i < mTuyaDevice.size(); i++) {
                Device device = mTuyaDevice.get(i);
                if (id.equals(device.getId())) {
                    device.setFlagCache(true);
                    ((TuyaDevice)device).markFlagLoaded();
                    found = true;
                    break;
                }
            }

            if (!found) {
                mTuyaDevice.add(TuyaDevice.createFromCache(new TuyaCacheInfo.CacheInfo(id, subCategory)));
            }
        }

        return mTuyaDevice;
    }

    @Override
    public Device getDevice(String id) {
        for (Device device : mTuyaDevice) {
            if (device.getId().equals(id)) {
                return device;
            }
        }
        return null;
    }

    @Override
    public List<Device> getDeviceByType(String sub) {
//         判断类型是否属于自己
        if (!supportDeivceType.containsKey(sub)) {
            return null;
        }
        fetchDevices();
        if (mTuyaDevice.size() <= 0) {
            return mTuyaDevice;
        }

        List<Device> temp = new ArrayList<>();
        boolean needSaveCache = false;
        for (Device device : mTuyaDevice) {
            if (device.getSubCategory().equals(supportDeivceType.get(sub))) {
                temp.add(device);
                cacheInfo.addDevice(device.getId(), device.getSubCategory());
                needSaveCache = true;
            }
        }

        if (needSaveCache)
            saveDeviceCache();

        return temp;
    }

    @Override
    public List<Device> getDeviceByType(String sub, boolean cacheFirst) {
        return getDeviceByType(sub);
    }

    @Override
    public boolean removeDeviceCacheById(String s) {
        if (TextUtils.isEmpty(s)) {
            return false;
        }

        synchronized (listLock) {
            boolean remove = false;
            for (Device device : mTuyaDevice) {
                if (s.equals(device.getId())) {
                    remove = cacheInfo.removeDevice(s, null);
                    if (remove) {
                        mTuyaDevice.remove(device);
                    }
                    saveDeviceCache();
                    break;
                }
            }
            DDLog.i(TAG, "removeDeviceCacheById. " + s + " remove succeed ? " + remove);
            return remove;
        }

    }

    @Override
    public boolean removeDeviceCacheByType(String sub) {
        if (supportDeivceType.containsKey(sub)) {
            clearDeviceCache();
            return true;
        }
        return false;
    }

    @Override
    public boolean releaseDeviceByType(String s) {
        return false;
    }

    @Override
    public BasePluginBinder createPluginBinder(Context context, @NonNull String type) {
        if (BINDER_KEY_TUYA.equals(type)) {
            return new TuyaBinder(context);
        }
        return null;
    }

    @Override
    public void onDeviceChange(boolean isAdd, String deviceID) {
        if (isAdd) {
            DDLog.d(TAG, "onDeviceAdd: " + deviceID);
            for (Device device : mTuyaDevice) {
                if (device.getId().equals(deviceID)) {
                    DDLog.d(TAG, "onDeviceAdd: 设备已存在" + deviceID);
                    TuyaDevice d = (TuyaDevice) device;
                    d.setFlagDeleted(false);

                    cacheInfo.addDevice(d.getId(), d.getSubCategory());
                    saveDeviceCache();

                    boolean found = false;
                    for (int i = 0; i < sourceTuyaDevice.size(); i++) {
                        DeviceBean deviceBean = sourceTuyaDevice.get(i);
                        if (device.getId().equals(deviceBean.getDevId())) {
                            d.updata(deviceBean);
                            found = true;
                            break;
                        }
                    }
                    if (!found) {
                        DeviceBean devBean = TuyaDelegate.getInstance().getDevBean(deviceID);
                        if (null != devBean) {
                            sourceTuyaDevice.add(devBean);
                            d.updata(devBean);
                        }
                    }
                    return;
                }
            }
            TuyaDevice tuyaDevice = TuyaDevice.create(TuyaDelegate.getInstance().getDevBean(deviceID));
            mTuyaDevice.add(tuyaDevice);

            cacheInfo.addDevice(tuyaDevice.getId(), tuyaDevice.getSubCategory());
            saveDeviceCache();

            DeivceChangeEvent event = new DeivceChangeEvent(tuyaDevice);
            event.setAdd(true);
            EventBus.getDefault().post(event);
        } else {
            for (Device device : mTuyaDevice) {
                if (device.getId().equals(deviceID)) {
                    DeivceChangeEvent event = new DeivceChangeEvent(device);
                    event.setRemove(true);
                    EventBus.getDefault().post(event);
                    mTuyaDevice.remove(device);
                    break;
                }
            }
        }
    }

    private void cleanDeviceData() {
        if (mTuyaDevice != null) {
            mTuyaDevice.clear();
        }
        if (sourceTuyaDevice != null) {
            sourceTuyaDevice.clear();
        }

    }

    @Subscribe
    public void onEvent(DeivceChangeEvent event) {
        if (event.isAdd()) {

        } else if (event.isRemove()) {
            for (Device device : mTuyaDevice) {
                if (device.getId().equals(event.getDevice().getId())) {
                    device.setFlagDeleted(true);
                    if (isForeground) {
                        cacheInfo.removeDevice(device.getId(), device.getSubCategory());
                        mTuyaDevice.remove(device);
                    }
                    saveDeviceCache();
                    DDLog.i(TAG, " DeivceChangeEvent: 删除涂鸦：" + mTuyaDevice.size());
                    break;
                }
            }
        }
    }

    @Subscribe
    public void onEvent(AppStateEvent event) {
        final boolean font = event.isFore();
        MsctLog.d(TAG, "AppStateEvent isFont" + font);
        isForeground = font;
    }

    private void readCacheInfo() {
        if (cacheInfo.getCacheTime() > 0) {
            return;
        }

        final String homeId = currentHomeID;
        DinUser user = DinCore.getUserInstance().getUser();
        final String userId = null != user ? user.getUser_id() : null;
        TuyaCacheInfo cache = DeviceCacheHelper.getCache(homeId, userId, CACHE_IDENTIFY, TuyaCacheInfo.class);
        this.cacheInfo.updateFrom(cache);
        DDLog.d(TAG, CACHE_IDENTIFY + ":cache--------readcache: " + cacheInfo);
    }

    private void saveDeviceCache() {
        final String homeId = currentHomeID;
        DinUser user = DinCore.getUserInstance().getUser();
        final String userId = null != user ? user.getUser_id() : null;
        cacheInfo.setCacheTime(System.currentTimeMillis());
        DeviceCacheHelper.saveCacheAsync(homeId, userId, CACHE_IDENTIFY, cacheInfo);
        DDLog.d(TAG, CACHE_IDENTIFY + ":cache--------savecache: " + cacheInfo);
    }

    private void clearDeviceCache() {
        final String homeId = currentHomeID;
        DinUser user = DinCore.getUserInstance().getUser();
        final String userId = null != user ? user.getUser_id() : null;
        DeviceCacheHelper.removeCacheASync(homeId, userId, CACHE_IDENTIFY);
    }
}
