package com.dinsafer.module_dscam.channel;

import android.text.TextUtils;
import android.util.Log;

import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.msctlib.kcp.IKcpCallBack;
import com.dinsafer.dssupport.msctlib.kcp.IKcpCreateCallBack;
import com.dinsafer.dssupport.msctlib.kcp.KcpClientImpl;
import com.dinsafer.dssupport.msctlib.msct.MsctDataFactory;
import com.dinsafer.module_dscam.bean.BaseCamDevice;
import com.dinsafer.module_dscam.bean.DsCamCmd;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.concurrent.TimeUnit;

import rx.Observable;
import rx.Subscriber;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;

import static com.dinsafer.dssupport.msctlib.kcp.KcpClientImpl.TYPE_JSON;

public class LanChannel extends Channel {

    private static final String TAG = LanChannel.class.getSimpleName();
    private String ip;
    private int port;
    //    因为局域网和p2p是不需要groupid和end_id的，所以把设备id+type作为对应的信息，唯一作用就只有在收到
//    udp包的时候，能正常分发到对应的msctsender而已。
    private String TYPE = "@lan";
    protected int missHeartbitPackage = 0;
    protected IKcpCallBack kcpCallBack;
    private Subscription heartBitInterval;
    private ChannelManager channelManager;

    public LanChannel(ChannelManager channelManager, BaseCamDevice device, IKcpCallBack kcpCallBack) {
        this.channelManager = channelManager;
        this.dsCamDevice = device;
        this.type = Channel.LAN;
        this.kcpCallBack = kcpCallBack;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    @Override
    public void connect() {
        if (isConnect()) {
            if (channelCallBack != null) {
                channelCallBack.onConnect();
            }
            return;
        }

        Channel proxyChannel = channelManager.getChannel(Channel.PROXY);

        if (TextUtils.isEmpty(ip) || port <= 0) {
            if (channelCallBack != null) {
                channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT, "lan ip is empty");
            }
            return;
        }

        if (TextUtils.isEmpty(proxyChannel.getChat_secret())) {
            if (channelCallBack != null) {
                channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT, "lan chart_secret is empty");
            }
            return;
        }

        if (channelCallBack != null) {
            channelCallBack.onConnecting();
        }

        msctSender = dsCamDevice.getMultiSender().createMsct(dsCamDevice.getId() + TYPE,
                proxyChannel.getChat_secret(),
                dsCamDevice.getId() + TYPE,
                proxyChannel.getEnd_secret());
//        这里不能用proxy的信息，因为lan和proxy都是公用一个udp对象，如果连下面的信息都一样的话，那么信息来了之后，无法区分这信息是去到proxy channel还是 lan channel
//        msctSender = dsCamDevice.getMultiSender().createMsct(receiveID, group_id, chat_secret, end_id, end_secret);
        msctSender.setIP(this.ip);
        msctSender.setPort(this.port);
        msctSender.setNickName("Lan Sender");
        msctSender.connect();

        dsCamDevice.getMultiSender().createDirectKcp(msctSender,
                TYPE_JSON, new IKcpCreateCallBack() {
                    @Override
                    public void onCreate(KcpClientImpl kcp) {
                        if (cmdkcpClient != null) {
                            cmdkcpClient.close();
                            dsCamDevice.getMultiSender().removeKcp(msctSender, cmdkcpClient.getConv());
                        }
                        cmdkcpClient = kcp;
                        cmdkcpClient.setCallBack(new IKcpCallBack() {
                            @Override
                            public void onMessage(int i, byte[] bytes) {
                                byte[] msgb = bytes;
                                try {
                                    String j = new String(msgb);
                                    MsctLog.v(TAG, dsCamDevice.getId() + " lan receive decode:" + j);
                                    JSONObject jsonObject = new JSONObject(j);
                                    String cmd = DDJSONUtil.getString(jsonObject, "cmd");
                                    if (DsCamCmd.KEEPALIVE.equals(cmd)) {
                                        missHeartbitPackage = 0;
                                        if (connectStatus != 1) {
                                            connectStatus = 1;
                                            if (channelCallBack != null) {
                                                channelCallBack.onConnect();
                                            }
                                        }
                                    }
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                }


                                if (kcpCallBack != null) {
                                    kcpCallBack.onMessage(i, bytes);
                                }
                            }

                            @Override
                            public void onException(String s) {
                                Log.e(TAG, "onException: lan TYPE_JSON " + s);
                            }

                            @Override
                            public void onClose() {
                                Log.e(TAG, "onClose: lan TYPE_JSON ");
                                MsctLog.i(TAG, dsCamDevice.getId() + " onClose");
                                disconnect();
                            }
                        });
                        cmdkcpClient.connect();

                        startHeartBit();
                    }

                    @Override
                    public void onError(int i, String s) {

                    }
                });
    }

    private void startHeartBit() {
        if (null != heartBitInterval
                && !heartBitInterval.isUnsubscribed()) {
            return;
        }
        missHeartbitPackage = 0;
        heartBitInterval = Observable.interval(5, TimeUnit.SECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {
                    }

                    @Override
                    public void onError(Throwable e) {
                    }

                    @Override
                    public void onNext(Object o) {
                        if (cmdkcpClient == null) {
                            disconnect();
                            return;
                        }
                        if (missHeartbitPackage >= 3) {
                            MsctLog.i(TAG, "heartbit timeout");
                            disconnect();
                            return;
                        }
                        MsctLog.v(TAG, "travelHeartBit");

                        JSONObject jsonObject = new JSONObject();
                        try {
                            jsonObject.put("cmd", DsCamCmd.KEEPALIVE);
                            jsonObject.put("client", "Android");
                            cmdkcpClient.sendString(jsonObject.toString());
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        missHeartbitPackage++;
                    }
                });
    }


    private void stopHeartBit() {
        MsctLog.i(TAG, "stopHeartBit");
        if (null != heartBitInterval
                && !heartBitInterval.isUnsubscribed()) {
            heartBitInterval.unsubscribe();
            heartBitInterval = null;
        }
    }

    @Override
    public void createKcp(int type, int sessionID, IKcpCreateCallBack kcpCreateCallBack) {
        dsCamDevice.getMultiSender().createDirectKcp(msctSender, type, new IKcpCreateCallBack() {
            @Override
            public void onCreate(KcpClientImpl kcpClient) {
                kcpClientList.add(kcpClient);
                if (kcpCreateCallBack == null) {
                    return;
                }

                kcpCreateCallBack.onCreate(kcpClient);
            }

            @Override
            public void onError(int i, String s) {
                if (kcpCreateCallBack == null) {
                    return;
                }

                kcpCreateCallBack.onError(i, s);
            }
        });
    }


    @Override
    public void disconnect() {
        MsctLog.i(TAG, "channel disconnect");
        connectStatus = -1;
        stopHeartBit();
        if (msctSender != null) {
            if (cmdkcpClient != null) {
                cmdkcpClient.close();
                dsCamDevice.getMultiSender().removeKcp(msctSender, cmdkcpClient.getConv());
            }
            for (KcpClientImpl kcpClient : kcpClientList) {
                kcpClient.close();
                dsCamDevice.getMultiSender().removeDirectKcp(msctSender, kcpClient.getConv());
            }
            msctSender.disconnect();
            dsCamDevice.getMultiSender().removeMsct(msctSender.getEnd_id());
            msctSender.release();
        }
        if (channelCallBack != null) {
            channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT_STATUS, "lan channel disconnect");
        }
    }

    @Override
    public void kcpCmdSend(byte[] bytes) {
        if (cmdkcpClient == null) {
            return;
        }
        MsctLog.d(TAG, "kcp cmd send:" + bytes.length);
        cmdkcpClient.sendByte(bytes);
    }

    @Override
    public void removeKcp(int conv) {
        for (KcpClientImpl kcpClient : kcpClientList) {
            if (kcpClient.getConv() == conv) {
                kcpClientList.remove(kcpClient);
                break;
            }
        }
        if (msctSender != null) {
            dsCamDevice.getMultiSender().removeDirectKcp(msctSender, conv);
        }
    }


    @Override
    public void msctCmdSend(MsctDataFactory data) {
        if (msctSender != null) {
            msctSender.send(data);
        }
    }

    @Override
    public void destory() {
        disconnect();
    }
}
