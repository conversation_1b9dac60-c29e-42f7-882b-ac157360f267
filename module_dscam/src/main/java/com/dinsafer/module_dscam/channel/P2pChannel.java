package com.dinsafer.module_dscam.channel;

import static com.dinsafer.dssupport.msctlib.kcp.KcpClientImpl.TYPE_JSON;
import static com.dinsafer.dssupport.msctlib.kcp.KcpClientImpl.TYPE_P2P_HANDSHAKE;

import android.text.TextUtils;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.msctlib.RandomStringUtils;
import com.dinsafer.dssupport.msctlib.convert.ConvertCreator;
import com.dinsafer.dssupport.msctlib.kcp.IKcpCallBack;
import com.dinsafer.dssupport.msctlib.kcp.IKcpCreateCallBack;
import com.dinsafer.dssupport.msctlib.kcp.KcpClientImpl;
import com.dinsafer.dssupport.msctlib.msct.Exoption;
import com.dinsafer.dssupport.msctlib.msct.IMsg;
import com.dinsafer.dssupport.msctlib.msct.MessageType;
import com.dinsafer.dssupport.msctlib.msct.MsctContext;
import com.dinsafer.dssupport.msctlib.msct.MsctDataFactory;
import com.dinsafer.dssupport.msctlib.msct.MsctResponse;
import com.dinsafer.dssupport.msctlib.netty.IMultipleSender;
import com.dinsafer.dssupport.msctlib.netty.IMultipleSenderCallBack;
import com.dinsafer.dssupport.msctlib.netty.P2PSender;
import com.dinsafer.dssupport.msctlib.queue.IRequestCallBack;
import com.dinsafer.module_dscam.bean.BaseCamDevice;
import com.dinsafer.module_dscam.bean.DsCamCmd;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import rx.Observable;
import rx.Subscriber;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;

public class P2pChannel extends Channel {

    private static final String TAG = "P2P-P2pChannel";
    private String ip;
    private int port = -1;
    protected int missHeartbitPackage = 0;
    protected IKcpCallBack kcpCallBack;

    private volatile boolean isNioSenderConnect = false;
    private final byte[] lock = new byte[0];
    private IMultipleSender multipleSender;
    private final IMultipleSenderCallBack mP2PSenderCallback = new IMultipleSenderCallBack() {
        @Override
        public void onDisconnect(String s) {
            isNioSenderConnect = false;
            MsctLog.e(TAG, "multisender disconnect");
        }

        @Override
        public void onConnenct() {
            isNioSenderConnect = true;
            connect();
            MsctLog.e(TAG, "multisender onConnenct");
        }

        @Override
        public void onReconnect() {
            MsctLog.e(TAG, "multisender onReconnect");
        }
    };

    private static final int p2pTimeout = 6;
    private Subscription p2pInterval;
    private String TYPE = "@p2p";

    private boolean hasGetWakeAck = false;

    private ChannelManager channelManager;
    private Subscription heartBitInterval;
    private String dataIV = RandomStringUtils.getRandomALLChar(16);
    private int connectId = -1;
    private KcpClientImpl p2pHandshakeKcp;

    public P2pChannel(ChannelManager channelManager, BaseCamDevice device, IKcpCallBack kcpCallBack) {
        this.channelManager = channelManager;
        this.dsCamDevice = device;
        this.type = Channel.P2P;
        this.kcpCallBack = kcpCallBack;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    //    进行打洞
    int p2pTime = 0;

    private void startP2p() {
        MsctLog.d(TAG, "startP2p");
        if (null != p2pInterval
                && !p2pInterval.isUnsubscribed()) {
            return;
        }
        hasGetWakeAck = false;
        p2pTime = 0;
        p2pInterval = Observable.interval(2, TimeUnit.SECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {
                    }

                    @Override
                    public void onError(Throwable e) {
                    }

                    @Override
                    public void onNext(Object o) {
                        p2pTime++;
                        sendConnecting();
                        if (p2pTime > p2pTimeout) {
                            stopP2p();
                            disconnect();
                            if (channelCallBack != null) {
                                channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT, "connect timeout");
                            }
                        }
                    }
                });
    }

    private void connectP2pSender() {
        if (TextUtils.isEmpty(DinCore.getInstance().getE2eHelpDomain())) {
            MsctLog.w(TAG, "UnConfig help domain!!!");
            if (null != channelCallBack) {
                channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT, "UnConfig help domain!!!");
            }
            return;
        }
        synchronized (lock) {
            if (!isNioSenderConnect && null == multipleSender) {
                multipleSender = new P2PSender("", 8888);
                multipleSender.addStatusListener(mP2PSenderCallback);
                multipleSender.connect();
            }
        }
    }

    private void disconnectP2pSender() {
        synchronized (lock) {
            if (null != multipleSender) {
                multipleSender.disConnect();
                multipleSender = null;
                isNioSenderConnect = false;
            }
        }
    }

    private boolean isRootSenderNotReady() {
        if (null == multipleSender) {
            if (null != channelCallBack) {
                channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT, "Empty multipleSender");
            }
            return true;
        }
        return false;
    }

    private void sendConnecting() {
        MsctLog.d(TAG, dsCamDevice.getId() + ": sendConnecting");
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsCamCmd.CONNECTING);
            jsonObject.put("display_name", "Android");
            jsonObject.put("connect_id", connectId);
            jsonObject.put("timestamp", System.currentTimeMillis() / 1000);
            MsctLog.i(TAG, "send: " + jsonObject);
            byte[] msg = jsonObject.toString().getBytes();
            kcpHandshakeSend(msg);
        } catch (JSONException e) {
        }
    }

    private void sendConnected() {
        MsctLog.d(TAG, dsCamDevice.getId() + ": sendConnected");
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsCamCmd.CONNECTED);
            jsonObject.put("display_name", "Android");
            jsonObject.put("connect_id", connectId);
            jsonObject.put("timestamp", System.currentTimeMillis() / 1000);
            byte[] msg = jsonObject.toString().getBytes();
            kcpHandshakeSend(msg);
        } catch (JSONException e) {
        }
    }

    private void stopP2p() {
        MsctLog.i(TAG, dsCamDevice.getId() + ": stop p2p");
        if (null != p2pInterval
                && !p2pInterval.isUnsubscribed()) {
            p2pInterval.unsubscribe();
            p2pInterval = null;
        }
    }

    @Override
    public void connect() {
        MsctLog.i(TAG, dsCamDevice.getId() + ": connect");
        if (isConnect()) {
            if (channelCallBack != null) {
                channelCallBack.onConnect();
            }
            return;
        }
        Channel proxyChannel = channelManager.getChannel(Channel.PROXY);

        if (TextUtils.isEmpty(proxyChannel.getChat_secret())) {
            if (channelCallBack != null) {
                channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT, "p2p chart_secret is empty");
            }
            return;
        }

        if (channelCallBack != null) {
            channelCallBack.onConnecting();
        }

        if (!isNioSenderConnect) {
            connectP2pSender();
            return;
        }

        if (isRootSenderNotReady()) {
            return;
        }

        if (msctSender != null) {
            for (KcpClientImpl kcpClient : kcpClientList) {
                kcpClient.close();
                multipleSender.removeDirectKcp(msctSender, kcpClient.getConv());
            }
            msctSender.disconnect();
            multipleSender.removeMsct(msctSender.getEnd_id());
            msctSender.release();
        }

        msctSender = multipleSender.createMsct(dsCamDevice.getId() + TYPE,
                proxyChannel.getChat_secret(),
                dsCamDevice.getId() + TYPE,
                proxyChannel.getEnd_secret());
        msctSender.setIP(this.ip);
        msctSender.setPort(this.port);
        msctSender.setNickName("P2P Sender");
        msctSender.connect();
//         通知服务器告诉ipc进行打洞，成功了，再去startp2p

        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("__time", System.currentTimeMillis() / 1000);
//        公网IP
        objectMap.put("cmd", "request");
        objectMap.put("display_name", "Android");
        objectMap.put("protocol", "kcp");
        objectMap.put("kcp_ipv4", multipleSender.getLocalIp() + ":" + multipleSender.getLocalPort());
        MsctLog.i(TAG, "myIp: " + multipleSender.getLocalIp() + ", myPort: " + multipleSender.getLocalPort());
        MsctDataFactory p2p = new MsctDataFactory.Builder(MessageType.CON,
                MsctContext.ChannelType.NORCHAN1, ConvertCreator.createMsctConvert(dataIV, proxyChannel.getChat_secret()))
                .addOptionHeader(Exoption.OPTION_END_ID, proxyChannel.getEnd_id())
                .addOptionHeader(Exoption.OPTION_GROUP_ID, proxyChannel.getGroup_id())
                .addOptionHeader(Exoption.OPTION_RECEIVER_ID, proxyChannel.getReceiveID())
                .addOptionHeader(Exoption.OPTION_AES, dataIV)
                .addOptionHeader(Exoption.OPTION_PROXY, 1)
                .addOptionHeader(Exoption.OPTION_METHOD, "p2p")
                .addOptionHeader(Exoption.OPTION_APP_ID, DinCore.getInstance().getAppID())
                .addOptionHeader(Exoption.OPTION_MSGID, RandomStringUtils.getMessageId())
                .setIsNeedResult(false)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg msg) {
                        MsctLog.i("P2P----------", TAG + "onAckEvent: " + msg);
                        if (msg instanceof MsctResponse) {
                            MsctResponse msctResponse = (MsctResponse) msg;
                            JSONObject result = null;
                            try {
                                result = new JSONObject((String) msctResponse.getMsctContext().getDecodedPayload());
                                connectId = DDJSONUtil.getInt(result, "connect_id");
                                String[] addr = DDJSONUtil.getString(result, "kcp_ipv4").split(":");
                                if (addr.length >= 2) {
                                    ip = addr[0];
                                    port = Integer.parseInt(addr[1]);
                                    msctSender.setIP(ip);
                                    msctSender.setPort(port);
                                    MsctLog.i("P2P----------", TAG + ", connect_id: " + connectId + ", IP: " + addr[0] + ", port: " + addr[1]);
                                }
                                createP2PHandshakeKcp();
                            } catch (JSONException e) {
                                e.printStackTrace();
                                if (channelCallBack != null) {
                                    channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT, "connect p2p fail");
                                }
                            }
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg msg) {
                        MsctLog.i("P2P----------", TAG + " onResultEvent: " + msg);
                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {

                    }

                    @Override
                    public void onTimeOut() {
                        MsctLog.i("P2P----------", TAG + " TimeOut ");
                        if (channelCallBack != null) {
                            channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT, "connect p2p timeout");
                        }
                    }
                })
                .addPayload(objectMap)
                .build();
        proxyChannel.msctCmdSend(p2p);
    }

    private void createP2PHandshakeKcp() {
        if (isRootSenderNotReady()) {
            return;
        }
        multipleSender.createDirectKcp(msctSender,
                TYPE_P2P_HANDSHAKE, new IKcpCreateCallBack() {
                    @Override
                    public void onCreate(KcpClientImpl kcp) {
                        MsctLog.i(TAG, "onKcpP2pHandshakeCreated");
                        if (isRootSenderNotReady()) {
                            return;
                        }
                        if (p2pHandshakeKcp != null) {
                            p2pHandshakeKcp.close();
                            multipleSender.removeKcp(msctSender, p2pHandshakeKcp.getConv());
                        }
                        p2pHandshakeKcp = kcp;
                        p2pHandshakeKcp.setCallBack(new IKcpCallBack() {
                            @Override
                            public void onMessage(int i, byte[] bytes) {
                                if (hasGetWakeAck) {
                                    return;
                                }
                                final byte[] msgb = bytes;
                                try {
                                    String j = new String(msgb);
                                    MsctLog.v(TAG, dsCamDevice.getId() + " p2p receive decode:" + j);
                                    JSONObject jsonObject = new JSONObject(j);
                                    String cmd = DDJSONUtil.getString(jsonObject, "cmd");
                                    if (DsCamCmd.CONNECTED.equals(cmd)) {
                                        MsctLog.i(TAG, dsCamDevice.getId() + "  P2P receive connected!");
                                        hasGetWakeAck = true;
                                        stopP2p();
                                        msctSender.clearTask();
                                        sendConnected();
                                        createCmdKcp();
                                    }
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                }
                            }

                            @Override
                            public void onException(String s) {

                            }

                            @Override
                            public void onClose() {
                                MsctLog.w(TAG, dsCamDevice.getId() + "TYPE_P2P_HANDSHAKE onClose");
                            }
                        });
                        p2pHandshakeKcp.connect();
                        startP2p();
                    }

                    @Override
                    public void onError(int i, String s) {

                    }
                });
    }

    private void createCmdKcp() {
        MsctLog.i(TAG, "createCmdKcp");
        if (isRootSenderNotReady()) {
            return;
        }
        multipleSender.createDirectKcp(msctSender,
                TYPE_JSON, new IKcpCreateCallBack() {
                    @Override
                    public void onCreate(KcpClientImpl kcp) {
                        if (isRootSenderNotReady()) {
                            return;
                        }
                        if (cmdkcpClient != null) {
                            cmdkcpClient.close();
                            multipleSender.removeKcp(msctSender, cmdkcpClient.getConv());
                        }
                        cmdkcpClient = kcp;
                        cmdkcpClient.setCallBack(new IKcpCallBack() {
                            @Override
                            public void onMessage(int i, byte[] bytes) {
                                byte[] msgb = bytes;
                                try {
                                    String j = new String(msgb);
                                    MsctLog.v(TAG, dsCamDevice.getId() + " p2p receive decode:" + j);
                                    JSONObject jsonObject = new JSONObject(j);
                                    String cmd = DDJSONUtil.getString(jsonObject, "cmd");
                                    if (DsCamCmd.KEEPALIVE.equals(cmd)) {
                                        if (connectStatus != 1) {
                                            connectStatus = 1;
                                            if (channelCallBack != null) {
                                                channelCallBack.onConnect();
                                            }
                                        }
                                        missHeartbitPackage = 0;
                                    }
                                } catch (JSONException e) {
                                    e.printStackTrace();
                                }

                                if (kcpCallBack != null) {
                                    kcpCallBack.onMessage(i, bytes);
                                }
                            }

                            @Override
                            public void onException(String s) {

                            }

                            @Override
                            public void onClose() {
                                MsctLog.i(TAG, dsCamDevice.getId() + " onClose");
                                disconnect();
                            }
                        });
                        cmdkcpClient.connect();
                        startHeartBit();
                    }

                    @Override
                    public void onError(int i, String s) {

                    }
                });
    }

    private void startHeartBit() {
        if (null != heartBitInterval
                && !heartBitInterval.isUnsubscribed()) {
            return;
        }
        missHeartbitPackage = 0;
        heartBitInterval = Observable.interval(5, TimeUnit.SECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {
                    }

                    @Override
                    public void onError(Throwable e) {
                    }

                    @Override
                    public void onNext(Object o) {
                        if (cmdkcpClient == null) {
                            disconnect();
                            return;
                        }
                        if (missHeartbitPackage >= 3) {
                            MsctLog.i(TAG, "heartbit timeout");
                            disconnect();
                            return;
                        }
                        MsctLog.v(TAG, "travelHeartBit");

                        JSONObject jsonObject = new JSONObject();
                        try {
                            jsonObject.put("cmd", DsCamCmd.KEEPALIVE);
                            jsonObject.put("client", "Android");
                            cmdkcpClient.sendString(jsonObject.toString());
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        missHeartbitPackage++;
                    }
                });
    }


    private void stopHeartBit() {
        MsctLog.i(TAG, "stopHeartBit");
        if (null != heartBitInterval
                && !heartBitInterval.isUnsubscribed()) {
            heartBitInterval.unsubscribe();
            heartBitInterval = null;
        }
    }

    @Override
    public void createKcp(int type, int sessionID, IKcpCreateCallBack kcpCreateCallBack) {
        MsctLog.i(TAG, "createKcp, type: " + type + ", sessionID: " + sessionID);
        if (isRootSenderNotReady()) {
            return;
        }
        multipleSender.createDirectKcp(msctSender, type, new IKcpCreateCallBack() {
            @Override
            public void onCreate(KcpClientImpl kcpClient) {
                if (isRootSenderNotReady()) {
                    return;
                }
                kcpClientList.add(kcpClient);
                if (kcpCreateCallBack == null) {
                    return;
                }

                kcpCreateCallBack.onCreate(kcpClient);
            }

            @Override
            public void onError(int i, String s) {
                if (kcpCreateCallBack == null) {
                    return;
                }

                kcpCreateCallBack.onError(i, s);
            }
        });
    }

    @Override
    public void msctCmdSend(MsctDataFactory data) {
        if (msctSender != null) {
            msctSender.send(data);
        }
    }


    @Override
    public void disconnect() {
        MsctLog.i(TAG, "channel disconnect");
        connectStatus = -1;
        hasGetWakeAck = false;
        stopP2p();
        stopHeartBit();
        if (isRootSenderNotReady()) {
            return;
        }
        if (msctSender != null) {
            if (p2pHandshakeKcp != null) {
                p2pHandshakeKcp.close();
                multipleSender.removeDirectKcp(msctSender, p2pHandshakeKcp.getConv());
            }
            if (cmdkcpClient != null) {
                cmdkcpClient.close();
                multipleSender.removeKcp(msctSender, cmdkcpClient.getConv());
            }
            for (KcpClientImpl kcpClient : kcpClientList) {
                kcpClient.close();
                multipleSender.removeDirectKcp(msctSender, kcpClient.getConv());
            }
            msctSender.disconnect();
            multipleSender.removeMsct(msctSender.getEnd_id());
            msctSender.release();
        }
        if (channelCallBack != null) {
            channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT_STATUS, "p2p channel disconnect");
        }
        disconnectP2pSender();
    }

    private void kcpHandshakeSend(byte[] bytes) {
        if (p2pHandshakeKcp == null && null != multipleSender) {
            return;
        }
        MsctLog.d(TAG, "kcp p2pHandshake send:" + bytes.length);
        p2pHandshakeKcp.sendByte(bytes);
    }

    @Override
    public void kcpCmdSend(byte[] bytes) {
        if (cmdkcpClient == null && null != multipleSender) {
            return;
        }
        MsctLog.d(TAG, "kcp cmd send:" + bytes.length);
        cmdkcpClient.sendByte(bytes);
    }

    @Override
    public void removeKcp(int conv) {
        for (KcpClientImpl kcpClient : kcpClientList) {
            if (kcpClient.getConv() == conv) {
                kcpClientList.remove(kcpClient);
                break;
            }
        }
        if (msctSender != null && null != multipleSender) {
            multipleSender.removeDirectKcp(msctSender, conv);
        }
    }

    @Override
    public void destory() {
        disconnect();
    }
}
