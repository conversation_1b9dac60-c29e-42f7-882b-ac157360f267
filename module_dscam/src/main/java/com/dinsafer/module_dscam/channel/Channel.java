package com.dinsafer.module_dscam.channel;

import com.dinsafer.dssupport.msctlib.kcp.IKcpCreateCallBack;
import com.dinsafer.dssupport.msctlib.kcp.KcpClientImpl;
import com.dinsafer.dssupport.msctlib.msct.MsctDataFactory;
import com.dinsafer.dssupport.msctlib.netty.IMsctSender;
import com.dinsafer.module_dscam.bean.BaseCamDevice;

import java.util.ArrayList;
import java.util.List;

public abstract class Channel {
    private static final String TAG = Channel.class.getSimpleName();
    public static final int PROXY = 0;
    public static final int LAN = 1;
    public static final int P2P = 2;
    public BaseCamDevice dsCamDevice;
    public int type;
    protected IMsctSender msctSender;
    protected KcpClientImpl cmdkcpClient;
    protected int connectStatus = -1;
    protected List<KcpClientImpl> kcpClientList = new ArrayList<>();
    protected IChannelCallBack channelCallBack;
    protected String receiveID, group_id, chat_secret, end_id, end_secret;

    public IChannelCallBack getChannelCallBack() {
        return channelCallBack;
    }

    public void setChannelCallBack(IChannelCallBack channelCallBack) {
        this.channelCallBack = channelCallBack;
    }

    public abstract void connect();

    public abstract void disconnect();

    public boolean isConnect() {
        return connectStatus == 1;
    }

    public boolean isConnectting() {
        return connectStatus == 0;
    }

    public boolean isDisconnect() {
        return connectStatus == -1;
    }

    public abstract void kcpCmdSend(byte[] bytes);

    public abstract void msctCmdSend(MsctDataFactory data);

    public abstract void createKcp(int type, int sessionID, IKcpCreateCallBack kcpCreateCallBack);

    public abstract void removeKcp(int conv);

    public abstract void destory();

    public String getReceiveID() {
        return receiveID;
    }

    public void setReceiveID(String receiveID) {
        this.receiveID = receiveID;
    }

    public String getGroup_id() {
        return group_id;
    }

    public void setGroup_id(String group_id) {
        this.group_id = group_id;
    }

    public String getChat_secret() {
        return chat_secret;
    }

    public void setChat_secret(String chat_secret) {
        this.chat_secret = chat_secret;
    }

    public String getEnd_id() {
        return end_id;
    }

    public void setEnd_id(String end_id) {
        this.end_id = end_id;
    }

    public String getEnd_secret() {
        return end_secret;
    }

    public void setEnd_secret(String end_secret) {
        this.end_secret = end_secret;
    }
}

