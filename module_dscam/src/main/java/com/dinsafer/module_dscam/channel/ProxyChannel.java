package com.dinsafer.module_dscam.channel;

import static com.dinsafer.dssupport.msctlib.kcp.KcpClientImpl.TYPE_JSON;

import android.text.TextUtils;
import android.util.Log;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.msctlib.RandomStringUtils;
import com.dinsafer.dssupport.msctlib.convert.ConvertCreator;
import com.dinsafer.dssupport.msctlib.kcp.IKcpCallBack;
import com.dinsafer.dssupport.msctlib.kcp.IKcpCreateCallBack;
import com.dinsafer.dssupport.msctlib.kcp.KcpClientImpl;
import com.dinsafer.dssupport.msctlib.msct.Exoption;
import com.dinsafer.dssupport.msctlib.msct.IMsg;
import com.dinsafer.dssupport.msctlib.msct.MessageType;
import com.dinsafer.dssupport.msctlib.msct.MsctContext;
import com.dinsafer.dssupport.msctlib.msct.MsctDataFactory;
import com.dinsafer.dssupport.msctlib.msct.MsctResponse;
import com.dinsafer.dssupport.msctlib.msct.Utils;
import com.dinsafer.dssupport.msctlib.netty.IMultipleSender;
import com.dinsafer.dssupport.msctlib.netty.ISenderCallBack;
import com.dinsafer.dssupport.msctlib.queue.IRequestCallBack;
import com.dinsafer.module_dscam.bean.BaseCamDevice;
import com.dinsafer.module_dscam.bean.DsCamCmd;
import com.dinsafer.module_dscam.bean.DsCamE2ELoginResponse;
import com.dinsafer.module_dscam.http.DsCamApi;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import rx.Observable;
import rx.Subscriber;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;

public class ProxyChannel extends Channel implements ISenderCallBack {

    private static final String TAG = ProxyChannel.class.getSimpleName();
    private String ip;
    private int port;
    protected int missHeartbitPackage = 0;
    protected IKcpCallBack kcpCallBack;


    private IMultipleSender sender;
    private String homeID;
    private ChannelManager channelManager;
    private Subscription heartBitInterval;
    private String dataIV = RandomStringUtils.getRandomALLChar(16);

    public ProxyChannel(ChannelManager channelManager, BaseCamDevice device, String homeID, IKcpCallBack kcpCallBack) {
        this.channelManager = channelManager;
        this.dsCamDevice = device;
        this.type = Channel.PROXY;
        this.kcpCallBack = kcpCallBack;
        this.homeID = homeID;
        sender = this.dsCamDevice.getMultiSender();
    }

    public String getReceiveID() {
        return receiveID;
    }

    public void setReceiveID(String receiveID) {
        this.receiveID = receiveID;
    }

    public String getGroup_id() {
        return group_id;
    }

    public void setGroup_id(String group_id) {
        this.group_id = group_id;
    }

    public String getChat_secret() {
        return chat_secret;
    }

    public void setChat_secret(String chat_secret) {
        this.chat_secret = chat_secret;
    }

    public String getEnd_id() {
        return end_id;
    }

    public void setEnd_id(String end_id) {
        this.end_id = end_id;
    }

    public String getEnd_secret() {
        return end_secret;
    }

    public void setEnd_secret(String end_secret) {
        this.end_secret = end_secret;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    //    为什么连接某个ipc的时候，需要去调用获取ipc列表？
//    ipc 获取到连接有以下几个过程
//    1. 获取ipc列表,确认ipc的end_id即receiveID
//    2. receiveID为空，表示ipc离线
//    3. ipc重新上线后，客户端无法被动知道ipc的end_id,所以,只能通过获取ipc列表重新获取ipc的end_id
//    明明只需要把end_id 获取放到通讯登录去做就可以减少一个接口请求，并且可以统一行为，

    //    1. 获取ipc列表，得到pid
//    2. 请求通讯登录,得知ipc的end_id，建立msct等等，
//    无论从那个状态进来，都是可以统一行为，为什么不这么做呢
//    因为服务器不肯改！！！！！
    @Override
    public void connect() {
        MsctLog.i(TAG, "connect: ");
        if (TextUtils.isEmpty(group_id) || TextUtils.isEmpty(receiveID)) {
            connectStatus = -1;
            if (null != channelCallBack) {
                channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT, "end_id or group_id is null");
            }
            return;
        }

        if (connectStatus == 1) {
            connectStatus = 1;
            if (channelCallBack == null) {
                return;
            }
            channelCallBack.onConnect();
            return;
        }
        if (sender == null) {
            connectStatus = -1;
            if (channelCallBack == null) {
                return;
            }
            channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT, "sender is null");
            return;
        }


        connectStatus = 0;
        if (channelCallBack != null) {
            channelCallBack.onConnecting();
        }

        // 改为由Device请求信息
        e2eLogin();

        // DsCamApi.getInstance().fetchDsCamList(homeID, dsCamDevice.getId(), dsCamDevice.getSubCategory())
        //         .enqueue(new Callback<DsCamListResponse>() {
        //             @Override
        //             public void onResponse(Call<DsCamListResponse> call, Response<DsCamListResponse> response) {
        //                 DsCamListResponse response1 = response.body();
        //                 for (DsCamListResponse.ResultBean resultBean : response1.getResult()) {
        //                     if (resultBean.getPid().equals(dsCamDevice.getId())) {
        //                         receiveID = resultBean.getEnd_id();
        //                         group_id = resultBean.getGroup_id();
        //                         break;
        //                     }
        //                 }
        //                 e2eLogin();
        //             }
        //
        //             @Override
        //             public void onFailure(Call<DsCamListResponse> call, Throwable t) {
        //                 e2eLogin();
        //             }
        //         });
    }

    private void e2eLogin() {
        MsctLog.d(TAG, "e2eLogin: ");
        DsCamApi.getInstance().login(homeID, dsCamDevice.getId(), dsCamDevice.getSubCategory()).enqueue(new Callback<DsCamE2ELoginResponse>() {
            @Override
            public void onResponse(Call<DsCamE2ELoginResponse> call, Response<DsCamE2ELoginResponse> response) {
                DsCamE2ELoginResponse response1 = response.body();
                for (DsCamE2ELoginResponse.ResultBean resultBean : response1.getResult()) {
                    if (resultBean.getPid().equals(dsCamDevice.getId())) {
                        end_id = resultBean.getEnd_id();
                        end_secret = resultBean.getEnd_secret();
                        chat_secret = resultBean.getChat_secret();
                        break;
                    }
                }
                createMsct();
            }

            @Override
            public void onFailure(Call<DsCamE2ELoginResponse> call, Throwable t) {
                connectStatus = -1;
                if (channelCallBack == null) {
                    return;
                }
                channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT, "e2e login fail:" + t.getMessage());
            }
        });
    }

    private void createMsct() {
        MsctLog.i(TAG, "createMsct: ");
//        if (TextUtils.isEmpty(receiveID)) {
//            connectStatus = -1;
//            if (channelCallBack == null) {
//                return;
//            }
//            channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT, "ipc offline");
//            return;
//        }
        if (TextUtils.isEmpty(group_id)) {
            connectStatus = -1;
            if (channelCallBack == null) {
                return;
            }
            channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT, "create msct fail group_id empty");
            return;
        }
        if (TextUtils.isEmpty(chat_secret)) {
            connectStatus = -1;
            if (channelCallBack == null) {
                return;
            }
            channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT, "create msct fail chat_s empty");
            return;
        }
        if (TextUtils.isEmpty(end_id)) {
            connectStatus = -1;
            if (channelCallBack == null) {
                return;
            }
            channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT, "create msct fail end_id empty");
            return;
        }
        if (TextUtils.isEmpty(end_secret)) {
            connectStatus = -1;
            if (channelCallBack == null) {
                return;
            }
            channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT, "create msct fail end_s empty");
            return;
        }
        if (msctSender != null) {
//            释放之前的连接的msct，重新创建一个新的
            msctSender.release();
            sender.removeMsct(end_id);
        }
        msctSender = sender.createMsct(receiveID, group_id, chat_secret, end_id, end_secret);
        if (msctSender == null) {
            connectStatus = -1;
            if (channelCallBack == null) {
                return;
            }
            channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT, "create msct fail msctSender empty");
            return;
        }
        msctSender.setNickName("Proxy Sender");
        msctSender.addSenderCallBack(this);
        msctSender.connect();
        MsctLog.v(TAG, dsCamDevice.getId() + " proxy connect msct");
//        不能在这里发wake，一定要等msctsender connect上来（alive指令回复之后才能进行其他操作）
//        wake();
    }

    private void wake() {
        MsctLog.i(TAG, "wake: ");
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("__time", System.currentTimeMillis() / 1000);
        MsctDataFactory wakeup = new MsctDataFactory.Builder(MessageType.CON,
                MsctContext.ChannelType.NORCHAN1, ConvertCreator.createMsctConvert(dataIV, msctSender.getChat_secret()))
                .addOptionHeader(Exoption.OPTION_END_ID, msctSender.getEnd_id())
                .addOptionHeader(Exoption.OPTION_GROUP_ID, msctSender.getGroup_id())
                .addOptionHeader(Exoption.OPTION_RECEIVER_ID, msctSender.getTarget_id())
                .addOptionHeader(Exoption.OPTION_AES, dataIV)
                .addOptionHeader(Exoption.OPTION_PROXY, 1)
                .addOptionHeader(Exoption.OPTION_METHOD, "wake")
                .addOptionHeader(Exoption.OPTION_APP_ID, DinCore.getInstance().getAppID())
                .addOptionHeader(Exoption.OPTION_MSGID, RandomStringUtils.getMessageId())
                .setIsNeedResult(false)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg msg) {
                        if (msg instanceof MsctResponse) {
//                            connect success
                            createCmdKcp();
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg msg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        MsctLog.v(TAG, dsCamDevice.getId() + " proxy onFail");
                    }

                    @Override
                    public void onTimeOut() {
                        connectStatus = -1;
                        if (channelCallBack != null) {
                            channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT, "ipc connect timeout");
                        }
                    }
                })
                .addPayload(objectMap)
                .build();
        msctSender.send(wakeup);
        MsctLog.v(TAG, dsCamDevice.getId() + " proxy send wake!");
    }

    private void createCmdKcp() {
        MsctLog.d(TAG, "createCmdKcp: ");
        sender.createKcp(msctSender, RandomStringUtils.getSessionID(), TYPE_JSON, new IKcpCreateCallBack() {
            @Override
            public void onCreate(KcpClientImpl kcp) {
                if (cmdkcpClient != null) {
                    cmdkcpClient.close();
                    sender.removeKcp(msctSender, cmdkcpClient.getConv());
                }

                cmdkcpClient = kcp;
                cmdkcpClient.setCallBack(new IKcpCallBack() {
                    @Override
                    public void onMessage(int i, byte[] bytes) {
                        byte[] msgb = bytes;
                        try {
                            String j = new String(msgb);
                            MsctLog.v(TAG, dsCamDevice.getId() + " proxy receive decode:" + j);
                            JSONObject jsonObject = new JSONObject(j);
                            String cmd = DDJSONUtil.getString(jsonObject, "cmd");
                            if (DsCamCmd.KEEPALIVE.equals(cmd)) {
                                missHeartbitPackage = 0;
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }


                        if (kcpCallBack != null) {
                            kcpCallBack.onMessage(i, bytes);
                        }
                    }

                    @Override
                    public void onException(String s) {
                        MsctLog.e(TAG, "onException: proxy TYPE_JSON " + s);
                    }

                    @Override
                    public void onClose() {
                        MsctLog.e(TAG, "onClose: proxy TYPE_JSON ");
                        MsctLog.i(TAG, "onClose");
                        disconnect();
                    }
                });
                cmdkcpClient.connect();
                connectStatus = 1;
                if (channelCallBack != null) {
                    channelCallBack.onConnect();
                }
                startHeartBit();

                P2pChannel p2pChannel = (P2pChannel) channelManager.getChannel(Channel.P2P);
                p2pChannel.connect();
            }

            @Override
            public void onError(int i, String s) {
                connectStatus = -1;
                if (channelCallBack != null) {
                    channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT, "connect fail");
                }
            }
        });
    }

    @Override
    public void disconnect() {
        MsctLog.i(TAG, dsCamDevice.getId() + "proxy channel disconnect");
        connectStatus = -1;
        stopHeartBit();
        if (msctSender != null) {
            if (cmdkcpClient != null) {
                cmdkcpClient.close();
                sender.removeKcp(msctSender, cmdkcpClient.getConv());
            }
            for (KcpClientImpl kcpClient : kcpClientList) {
                kcpClient.close();
                sender.removeKcp(msctSender, kcpClient.getConv());
            }
//            这里不能断开，否则就无法收到IPC重新上线的通知了
//            msctSender.disconnect();
//            sender.removeMsct(msctSender.getEnd_id());
//            msctSender.release();
        }
        if (channelCallBack != null) {
            channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT_STATUS, "proxy channel disconnect");
        }
    }

    @Override
    public void kcpCmdSend(byte[] bytes) {
        if (cmdkcpClient == null) {
            return;
        }
        MsctLog.d(TAG, "kcp cmd send:" + bytes.length);
        cmdkcpClient.sendByte(bytes);
    }

    @Override
    public void msctCmdSend(MsctDataFactory data) {
        MsctLog.i("NIO----------", TAG + "msctCmdSend:");
        if (msctSender != null) {
            msctSender.send(data);
        }
    }

    @Override
    public void createKcp(int type, int sessionID, IKcpCreateCallBack kcpCreateCallBack) {
        sender.createKcp(msctSender, sessionID, type, new IKcpCreateCallBack() {
            @Override
            public void onCreate(KcpClientImpl kcpClient) {
                kcpClientList.add(kcpClient);
                if (kcpCreateCallBack == null) {
                    return;
                }

                kcpCreateCallBack.onCreate(kcpClient);
            }

            @Override
            public void onError(int i, String s) {
                if (kcpCreateCallBack == null) {
                    return;
                }

                kcpCreateCallBack.onError(i, s);
            }
        });
    }

    @Override
    public void onDisconnect(String s) {
        connectStatus = -1;
        if (channelCallBack != null) {
            channelCallBack.onDisconnect(ErrorCode.ERROR_CONNECT_STATUS, s);
        }
        stopHeartBit();
    }

    @Override
    public void onConnenct() {
//      ipc在线不是已msct是否通为在线，而是wake指令回复了ack才算在线
        wake();
    }

    @Override
    public void onReconnect() {
        connectStatus = 1;
        if (channelCallBack != null) {
            channelCallBack.onConnect();
        }
        startHeartBit();
    }

    @Override
    public void onReceive(MsctResponse msctResponse) {
        if (!msctResponse.getMsctContext().hasOptionHeader()) {
            return;
        }
        String method = Utils.unSignByteToString(
                msctResponse.getMsctContext().ShouldGetOptionHeader(Exoption.OPTION_METHOD));
        MsctLog.d(TAG, "rect msct data, method: " + method + ", messageId: " + msctResponse.getMessageId());
        if ("notice".equals(method)) {
            String payload = (String) msctResponse.getMsctContext().getDecodedPayload();
            try {
                JSONObject jsonObject = new JSONObject(payload);
                if (!"member-online".equalsIgnoreCase(DDJSONUtil.getString(jsonObject, "cmd"))) {
                    return;
                }

                JSONArray members = DDJSONUtil.getJSONarray(jsonObject, "members");
                for (int i = 0; i < members.length(); i++) {
                    JSONObject member = members.getJSONObject(i);
                    String member_id = DDJSONUtil.getString(member, "member");
                    // 在自研IPC1.0.0以前的版本，出现了一个比较严重的问题，IPC的唯一id会出现重复，为了修复这个问题，服务器、客户端和自研IPC统一改成 "原ID_XXXX"。
                    // 所以0.9.15版本强制升级1.0.0版本的时候会出现，客户端持有 "原ID" 的自研IPC对象请求并且监控IPC的升级，但是当IPC升级完成后，会变成 "原ID_XXXX" 来连接服务器。
                    // 这时候客户端就不能和升级好的IPC通讯了（ID和EndID都变了），所以在收到 "member-online" 的指令后，通过新版"原ID_XXXX" 里面截取出来的 "原ID" 对比，改变当前对象下面的ID，重新连接就可以重新通讯了。
                    if (!dsCamDevice.getId().contains("_") && member_id.contains("_")) {
                        String[] ids = member_id.split("_");
                        if (dsCamDevice.getId().equals(ids[0])) {
                            MsctLog.w(TAG, "onReceive: member-online,refector device id " + dsCamDevice.getId() + " to " + member_id);
                            dsCamDevice.setId(member_id);
                            MsctLog.w(TAG, "onReceive: member-online,refector device id,new id is " + dsCamDevice.getId());
                        }
                    }
                    if (dsCamDevice.getId().equals(DDJSONUtil.getString(member, "member"))) {
                        Log.w(TAG, dsCamDevice.getId() + "proxy channel auto connect");
                        HashMap<String, Object> par = new HashMap<>();
                        par.put("cmd", DsCamCmd.CONNECT);
                        par.put("discardCache", true);
                        dsCamDevice.submit(par);
                        // this.receiveID = DDJSONUtil.getString(member, "end_id");
                        // msctSender.setTarget_id(this.receiveID);
                        // MsctLog.i(TAG, "ipc online:" + this.dsCamDevice.getId() + " end_id:" + receiveID);
                        // createCmdKcp();
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
            return;
        }
    }


    private void startHeartBit() {
        if (null != heartBitInterval
                && !heartBitInterval.isUnsubscribed()) {
            return;
        }
        missHeartbitPackage = 0;
        heartBitInterval = Observable.interval(5, TimeUnit.SECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {
                    }

                    @Override
                    public void onError(Throwable e) {
                    }

                    @Override
                    public void onNext(Object o) {
                        if (cmdkcpClient == null) {
                            disconnect();
                            return;
                        }
                        if (missHeartbitPackage >= 3) {
                            MsctLog.i(TAG, "heartbit timeout");
                            disconnect();
                            return;
                        }
                        MsctLog.v(TAG, "travelHeartBit");

                        JSONObject jsonObject = new JSONObject();
                        try {
                            jsonObject.put("cmd", DsCamCmd.KEEPALIVE);
                            jsonObject.put("client", "Android");
                            cmdkcpClient.sendString(jsonObject.toString());
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        missHeartbitPackage++;
                    }
                });
    }


    private void stopHeartBit() {
        MsctLog.i(TAG, "stopHeartBit");
        if (null != heartBitInterval
                && !heartBitInterval.isUnsubscribed()) {
            heartBitInterval.unsubscribe();
            heartBitInterval = null;
        }
    }


    @Override
    public void removeKcp(int conv) {
        for (KcpClientImpl kcpClient : kcpClientList) {
            if (kcpClient.getConv() == conv) {
                kcpClientList.remove(kcpClient);
                break;
            }
        }
        if (msctSender != null) {
            sender.removeKcp(msctSender, conv);
        }
    }

    @Override
    public void destory() {
        disconnect();
        // 一定要先断开kcp，再断开msct，因为断开kcp需要msct发送term指令
        if (null != msctSender) {
            msctSender.disconnect();
            sender.removeMsct(msctSender.getEnd_id());
            msctSender.release();
            msctSender = null;
        }
    }

}
