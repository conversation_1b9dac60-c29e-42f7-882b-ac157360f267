package com.dinsafer.module_dscam;

import android.content.Context;
import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.dinsafer.dincore.common.CommonCmdEvent;
import com.dinsafer.dssupport.msctlib.MsctLog;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * DsCam添加器,蓝牙
 */
@Keep
public class DsCamBinder extends BaseCamBinder {

    public DsCamBinder(@NonNull Context mContext) {
        super(mContext);
        UUID = "23593c00-69b8-419b-84f3-a3fe7a354cdb";
        WRITE_UUID = "23593c01-69b8-419b-84f3-a3fe7a354cdb";
        READ_UUID = "23593c02-69b8-419b-84f3-a3fe7a354cdb";
    }

    @Override
    protected void handleRegisterSuccess(JSONObject data) throws JSONException {
        data.put("homeID", mHomeID);
        data.put("provider", "dscam");
        MsctLog.i(TAG, data.toString());
        CommonCmdEvent commonCmdEvent = new CommonCmdEvent(CommonCmdEvent.CMD.DSCAM_ADD);
        commonCmdEvent.setExtra(data.toString());
        EventBus.getDefault().post(commonCmdEvent);
    }
}
