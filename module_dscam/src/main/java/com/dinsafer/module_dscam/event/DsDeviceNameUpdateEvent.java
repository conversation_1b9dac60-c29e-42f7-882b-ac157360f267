package com.dinsafer.module_dscam.event;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2023/1/10 5:19 下午
 */
@Keep
public class DsDeviceNameUpdateEvent {
    @NonNull
    private final String pid;
    @Nullable
    private final String name;

    public DsDeviceNameUpdateEvent(@NonNull String pid, @Nullable String name) {
        this.pid = pid;
        this.name = name;
    }

    @NonNull
    public String getPid() {
        return pid;
    }

    @Nullable
    public String getName() {
        return name;
    }

    @Override
    public String toString() {
        return "DsDeviceNameUpdateEvent{" +
                "pid='" + pid + '\'' +
                ", name='" + name + '\'' +
                '}';
    }
}
