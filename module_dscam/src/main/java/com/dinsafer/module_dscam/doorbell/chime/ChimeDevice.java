package com.dinsafer.module_dscam.doorbell.chime;

import androidx.annotation.Keep;

import com.dinsafer.dincore.common.Device;

import java.util.Map;

/**
 * 叮咚
 * Author: MiraclesHed
 * Date: 2021/11/30
 */
@Keep
public class ChimeDevice extends Device {

    public ChimeDevice(String id, Map<String, Object> info) {
        super(id, 666, "CHIME", info);
    }

    public ChimeDevice(String id, Map<String, Object> info, String fatherId) {
        super(id, 666, "CHIME", info, fatherId);
    }

    @Override
    public void submit(Map map) {

    }


}
