package com.dinsafer.module_dscam.doorbell;

import androidx.annotation.Keep;

import com.dinsafer.dincore.common.Cmd;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2021/11/30
 */

@Keep
public class DsDoorbellCmd  extends Cmd {
    //添加叮咚
    public static final String ADD_CHIME = "add_chime";

    //查询叮咚列表
    public static final String GET_CHIME_LIST = "get_chime_list";

    //删除叮咚
    public static final String DEL_CHIME = "del_chime";

    //重命名叮咚
    public static final String RENAME_CHIME = "rename_chime";

    //测试叮咚
    public static final String TEST_CHIME = "test_chime";
}
