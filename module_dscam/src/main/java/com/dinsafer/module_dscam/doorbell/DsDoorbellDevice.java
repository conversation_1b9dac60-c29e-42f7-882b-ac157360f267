package com.dinsafer.module_dscam.doorbell;

import android.util.Log;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.dinsafer.dincore.common.Cmd;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dincore.utils.MapUtils;
import com.dinsafer.dssupport.msctlib.db.KV;
import com.dinsafer.dssupport.msctlib.netty.IMultipleSender;
import com.dinsafer.module_dscam.bean.BaseCamDevice;
import com.dinsafer.module_dscam.bean.DsCamCacheInfo;
import com.dinsafer.module_dscam.bean.DsCamCmd;
import com.dinsafer.module_dscam.bean.DsCamListResponse;
import com.dinsafer.module_dscam.doorbell.chime.ChimeDevice;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 门铃设备
 */
@Keep
public class DsDoorbellDevice extends BaseCamDevice {

    public DsDoorbellDevice(IMultipleSender sender, String pid, String homeID, String receiveID, String group_id, Long addtime) {
        super(sender, pid, homeID, receiveID, group_id, addtime, "DSDOORBELL");
        Log.d(TAG, "DsDoorbellDevice: ");
        updateChimeCount();
    }

    public DsDoorbellDevice(IMultipleSender sender, String homeID, DsCamListResponse.ResultBean deviceBean) {
        super(sender, homeID, deviceBean, "DSDOORBELL");
        updateChimeCount();
    }

    public DsDoorbellDevice(IMultipleSender sender, String homeID, @NonNull final DsCamCacheInfo.CacheInfo cacheInfo) {
        super(sender, homeID, cacheInfo, "DSDOORBELL");
    }

    @Override
    public void submit(Map arg) {
        String cmd = (String) arg.get("cmd");
        switch (cmd) {
            case DsDoorbellCmd.ADD_CHIME:
                addChime(arg);
                break;
            case DsDoorbellCmd.GET_CHIME_LIST:
                getChimeList(arg);
                break;
            case DsDoorbellCmd.DEL_CHIME:
                delChime(arg);
                break;
            case DsDoorbellCmd.RENAME_CHIME:
                renameChime(arg);
                break;
            case DsDoorbellCmd.TEST_CHIME:
                testChime(arg);
                break;
            case DsCamCmd.RESET:
                cacheChimes(getId(), new ArrayList<>());
            default:
                super.submit(arg);
                break;
        }

    }

    @Override
    protected void handlerKcpResponse(JSONObject result) {
        String cmd = DDJSONUtil.getString(result, "cmd");
        if (DsDoorbellCmd.GET_CHIME_LIST.equals(cmd)) {
            int status = DDJSONUtil.getInt(result, "status");
            Map resultMap = Cmd.getDefaultResultMap(status == 1 ? true : false, cmd);
            if (status != 1) {
                resultMap.put("errorMessage", "cmd:" + cmd + " not work");
                dispatchResult(cmd, resultMap);
                return;
            }
            resultMap.putAll(MapUtils.fromJson(result));

            ArrayList<ChimeDevice> chimesList = new ArrayList<>();
            JSONArray list = DDJSONUtil.getJSONarray(result, "list");
            if (list != null && list.length() > 0) {
                for (int i = 0; i < list.length(); i++) {
                    try {
                        JSONObject jsonObject = list.getJSONObject(i);
                        ChimeDevice device = new ChimeDevice(DDJSONUtil.getString(jsonObject, "id"), MapUtils.fromJson(jsonObject));
                        chimesList.add(device);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }

            resultMap.put("list", chimesList);
            dispatchResult(cmd, resultMap);
        } else {
            super.handlerKcpResponse(result);
        }
    }

    private void testChime(Map arg) {
        String chime_id = (String) MapUtils.get(arg, "chime_id", "");

        if (!channelManager.isConnect()) {
            Map result = Cmd.getDefaultResultMap(false, DsDoorbellCmd.TEST_CHIME);
            result.put("errorMessage", "doorbell is no connect");
            dispatchResult(DsDoorbellCmd.TEST_CHIME, result);
            return;
        }
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsDoorbellCmd.TEST_CHIME);
            jsonObject.put("chime_id", chime_id);
            byte[] msg = jsonObject.toString().getBytes();
            sendByte(msg);
        } catch (JSONException e) {
            Map result = Cmd.getDefaultResultMap(false, DsDoorbellCmd.TEST_CHIME);
            result.put("errorMessage", "params error");
            dispatchResult(DsDoorbellCmd.TEST_CHIME, result);
        }

    }

    private void renameChime(Map arg) {
        String chime_id = (String) MapUtils.get(arg, "chime_id", "");
        String chime_name = (String) MapUtils.get(arg, "chime_name", "");

        if (!channelManager.isConnect()) {
            Map result = Cmd.getDefaultResultMap(false, DsDoorbellCmd.RENAME_CHIME);
            result.put("errorMessage", "doorbell is no connect");
            dispatchResult(DsDoorbellCmd.RENAME_CHIME, result);
            return;
        }

//        if (true) {
//            changeChimeName(getId(), chime_id, chime_name);
//            Map result = Cmd.getDefaultResultMap(true, DsDoorbellCmd.RENAME_CHIME);
//            dispatchResult(DsDoorbellCmd.RENAME_CHIME, result);
//            return;
//        }

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsDoorbellCmd.RENAME_CHIME);
            jsonObject.put("chime_id", chime_id);
            jsonObject.put("chime_name", chime_name);
            byte[] msg = jsonObject.toString().getBytes();
            sendByte(msg);
        } catch (JSONException e) {
            Map result = Cmd.getDefaultResultMap(false, DsDoorbellCmd.RENAME_CHIME);
            result.put("errorMessage", "params error");
            dispatchResult(DsDoorbellCmd.RENAME_CHIME, result);
        }

    }

    private void delChime(Map arg) {
        String chime_id = (String) MapUtils.get(arg, "chime_id", "");

//        if (true) {
//            removeCacheChime(getId(), chime_id);
//            Map result = Cmd.getDefaultResultMap(true, DsDoorbellCmd.DEL_CHIME);
//            dispatchResult(DsDoorbellCmd.DEL_CHIME, result);
//            return;
//        }

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsDoorbellCmd.DEL_CHIME);
            jsonObject.put("chime_id", chime_id);
            byte[] msg = jsonObject.toString().getBytes();
            sendByte(msg);
        } catch (JSONException e) {
            Map result = Cmd.getDefaultResultMap(false, DsDoorbellCmd.DEL_CHIME);
            result.put("errorMessage", "params error");
            dispatchResult(DsDoorbellCmd.DEL_CHIME, result);
        }
    }

    private void getChimeList(Map arg) {
        if (!channelManager.isConnect()) {
            Map result = Cmd.getDefaultResultMap(false, DsDoorbellCmd.GET_CHIME_LIST);
            result.put("errorMessage", "doorbell is no connect");
            dispatchResult(DsDoorbellCmd.GET_CHIME_LIST, result);
            return;
        }
//        if (true) {
//            Map result = Cmd.getDefaultResultMap(true, DsDoorbellCmd.GET_CHIME_LIST);
//            result.put("chimes", queryCacheChime(getId()));
//            dispatchResult(DsDoorbellCmd.GET_CHIME_LIST, result);
//            return;
//        }
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsDoorbellCmd.GET_CHIME_LIST);
            byte[] msg = jsonObject.toString().getBytes();
            sendByte(msg);
        } catch (JSONException e) {
            Map result = Cmd.getDefaultResultMap(false, DsDoorbellCmd.GET_CHIME_LIST);
            result.put("errorMessage", "params error");
            dispatchResult(DsDoorbellCmd.GET_CHIME_LIST, result);
        }
    }

    private void addChime(Map arg) {
        String chime_id = (String) MapUtils.get(arg, "chime_id", "");
        String chime_rfid = (String) MapUtils.get(arg, "chime_rfid", "");
        String chime_name = (String) MapUtils.get(arg, "chime_name", "");

        if (!channelManager.isConnect()) {
            Map result = Cmd.getDefaultResultMap(false, DsDoorbellCmd.ADD_CHIME);
            result.put("errorMessage", "doorbell is no connect");
            dispatchResult(DsDoorbellCmd.ADD_CHIME, result);
            return;
        }

//        if (true) {
//            boolean b = addCacheChime(getId(), chime_id, chime_name);
//            Map result = new HashMap();
//            result.put("status", b ? 1 : -1);
//            result.put("errorMessage", "");
//            result.put("result", new HashMap());
//            result.put("cmd", DsDoorbellCmd.ADD_CHIME);
//            dispatchResult(DsDoorbellCmd.ADD_CHIME, result);
//            return;
//        }

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsDoorbellCmd.ADD_CHIME);
            jsonObject.put("chime_id", chime_id);
            jsonObject.put("chime_rfid", chime_rfid);
            jsonObject.put("chime_name", chime_name);
            byte[] msg = jsonObject.toString().getBytes();
            sendByte(msg);
        } catch (JSONException e) {
            Map result = Cmd.getDefaultResultMap(false, DsDoorbellCmd.ADD_CHIME);
            result.put("errorMessage", "params error");
            dispatchResult(DsDoorbellCmd.ADD_CHIME, result);
        }
    }

    private boolean addCacheChime(String doorbellId, String chimeId, String chimeName) {
        ChimeDevice chimeDevice = new ChimeDevice(chimeId, new HashMap<>());
        chimeDevice.getInfo().put("name", chimeName);

        ArrayList<ChimeDevice> cacheChimes = getCacheChimes(doorbellId);
        for (ChimeDevice cacheChime : cacheChimes) {
            if (cacheChime.getId().equals(chimeId)) {
                return false;
            }
        }

        cacheChimes.add(chimeDevice);
        cacheChimes(doorbellId, cacheChimes);
        return true;
    }

    private void removeCacheChime(String doorbellId, String chimeId) {
        Log.d(TAG, "removeCacheChime: " + doorbellId + " /" + chimeId);
        ArrayList<ChimeDevice> cacheChimes = getCacheChimes(doorbellId);
        Log.d(TAG, "removeCacheChime: before size " + cacheChimes.size());
        Iterator<ChimeDevice> iterator = cacheChimes.iterator();
        while (iterator.hasNext()) {
            ChimeDevice next = iterator.next();
            if (next.getId().equals(chimeId)) {
                Log.d(TAG, "removeCacheChime: find! " + chimeId);
                iterator.remove();
            }
        }
        Log.d(TAG, "removeCacheChime: after size " + cacheChimes.size());
        cacheChimes(doorbellId, cacheChimes);
    }

    private void changeChimeName(String doorbellId, String chimeId, String chimeName) {
        ArrayList<ChimeDevice> cacheChimes = getCacheChimes(doorbellId);
        Iterator<ChimeDevice> iterator = cacheChimes.iterator();
        while (iterator.hasNext()) {
            ChimeDevice next = iterator.next();
            if (next.getId().equals(chimeId)) {
                next.getInfo().put("name", chimeName);
                break;
            }
        }

        cacheChimes(doorbellId, cacheChimes);
    }

    private ArrayList<ChimeDevice> queryCacheChime(String doorbellId) {
        ArrayList<ChimeDevice> cacheChimes = getCacheChimes(doorbellId);
        return cacheChimes;
    }

    private void cacheChimes(String doorbellId, ArrayList<ChimeDevice> chimes) {
        KV.putString("chimes_" + doorbellId, new Gson().toJson(chimes));
        updateChimeCount();
    }

    private ArrayList<ChimeDevice> getCacheChimes(String doorbellId) {
        String string = KV.getString("chimes_" + doorbellId, "[]");
        Type type = new TypeToken<ArrayList<ChimeDevice>>() {
        }.getType();
        ArrayList<ChimeDevice> result = new Gson().fromJson(string, type);
        return result;
    }

    private void updateChimeCount() {
        ArrayList<ChimeDevice> cacheChimes = getCacheChimes(getId());
        getInfo().put("chime_count", cacheChimes.size());
    }


}
