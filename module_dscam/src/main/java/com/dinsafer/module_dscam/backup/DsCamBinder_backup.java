//package com.dinsafer.module_dscam.backup;
//
//import android.content.Context;
//import androidx.annotation.Keep;
//import androidx.annotation.NonNull;
//import android.text.TextUtils;
//
//import com.clj.fastble.callback.BleScanCallback;
//import com.clj.fastble.data.BleDevice;
//import com.dinsafer.dincore.DinCore;
//import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
//import com.dinsafer.dincore.activtor.bean.Plugin;
//import com.dinsafer.dincore.common.CommonCmdEvent;
//import com.dinsafer.dincore.common.ErrorCode;
//import com.dinsafer.dincore.utils.BleHelper;
//import com.dinsafer.dssupport.msctlib.MsctLog;
//import com.dinsafer.dssupport.msctlib.utils.MsctJSONUtil;
//import com.dinsafer.module_dscam.DsCamBleCmd;
//import com.dinsafer.module_dscam.DsCamConvert;
//
//import org.greenrobot.eventbus.EventBus;
//import org.json.JSONException;
//import org.json.JSONObject;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * DsCam添加器,蓝牙
// */
//public class DsCamBinder_backup extends BasePluginBinder {
//
//    private String mSsid, mSsidPassword, mHomeID;
//
//    private boolean isTimeOutToFindDevice = false;
//
//    public static final String UUID = "23593c00-69b8-419b-84f3-a3fe7a354cdb";
//    public static final String WRITE_UUID = "23593c01-69b8-419b-84f3-a3fe7a354cdb";
//    public static final String READ_UUID = "23593c02-69b8-419b-84f3-a3fe7a354cdb";
//    private BleHelper.IMessageCallback callback = new BleHelper.IMessageCallback() {
//        @Override
//        public void onMessage(byte[] notifyData) {
//            handlerMessage(notifyData);
//        }
//    };
//
//    private IWifiListCallBack wifiListCallBack;
//
//    private DsCamConvert camConvert = new DsCamConvert();
//
//
//    @Keep
//    public DsCamBinder_backup(@NonNull Context mContext) {
//        super(mContext);
//    }
//
//
//    @Override
//    @Keep
//    public void bindDevice(Plugin plugin) {
//        //
//        if (TextUtils.isEmpty(mSsid)) {
//            callBackBindResult(ErrorCode.DEFAULT, "ssid is null");
//            return;
//        }
//
//        if (TextUtils.isEmpty(mSsidPassword)) {
//            callBackBindResult(ErrorCode.DEFAULT, "ssid is null");
//            return;
//        }
//        if (TextUtils.isEmpty(mHomeID)) {
//            callBackBindResult(ErrorCode.DEFAULT, "ssid is null");
//            return;
//        }
//        setSsid();
//
//    }
//
//    @Keep
//    public void setSsid(String mSsid) {
//        this.mSsid = mSsid;
//    }
//
//    @Keep
//    public void setSsidPassword(String mSsidPassword) {
//        this.mSsidPassword = mSsidPassword;
//    }
//
//
//    @Keep
//    public void setBindHomeID(String mHomeID) {
//        this.mHomeID = mHomeID;
//    }
//
//    private void startConfigWifi() {
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("cmd", DsCamBleCmd.SET_NET);
//            BleHelper.getInstance().write(jsonObject, null);
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//    }
//
//    private void bindDsCam() {
//        setAppID();
//    }
//
//    @Keep
//    public void discoveryDevice(long timeout, BleScanCallback bleScanCallback) {
//        BleHelper.getInstance().setScanRuleWithUUID(timeout, UUID, WRITE_UUID, READ_UUID);
//        BleHelper.getInstance().scanDevice(bleScanCallback);
//    }
//
//    @Keep
//    public void stopDiscoveryDevice() {
//        BleHelper.getInstance().cancelScan();
//    }
//
//    @Keep
//    public void connectDevice(BleDevice dsCam,
//                              BleHelper.ConnectCallback connectCallback) {
//        BleHelper.getInstance().release();
//        BleHelper.getInstance().addConnectCallBack(connectCallback);
//        BleHelper.getInstance().addMessageCallBack(callback);
//        BleHelper.getInstance().connected(dsCam, camConvert);
//    }
//
//    @Keep
//    public void getWifiList() {
//        wifiList.clear();
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("cmd", "get_wifi_list");
//            BleHelper.getInstance().write(jsonObject, null);
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Keep
//    public IWifiListCallBack getWifiListCallBack() {
//        return wifiListCallBack;
//    }
//
//    @Keep
//    public void setWifiListCallBack(IWifiListCallBack wifiListCallBack) {
//        this.wifiListCallBack = wifiListCallBack;
//    }
//
//    private List<String> wifiList = new ArrayList<>();
//
//    private void handlerMessage(byte[] notifyData) {
//        String js = new String(notifyData);
//        MsctLog.i(TAG, "handler rece:" + js);
//        try {
//            JSONObject jsonObject = new JSONObject(js);
//            String cmd = MsctJSONUtil.getString(jsonObject, "cmd");
//            switch (cmd) {
//                case DsCamBleCmd.GET_WIFI_LIST:
//                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
//                        if (wifiListCallBack != null) {
//                            wifiListCallBack.onWifiListCallBack(wifiList);
//                        }
//                    } else {
//                        wifiList.add(MsctJSONUtil.getString(jsonObject, "result"));
//                    }
//                    break;
//                case DsCamBleCmd.SET_NET:
//                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
//                        bindDsCam();
//                    } else {
//                        callBackBindResult(ErrorCode.DEFAULT, "wifi connect error");
//                    }
//                    break;
//                case DsCamBleCmd.SET_WIFI_NAME:
//                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
//                        setSsidPW();
//                    } else {
//                        callBackBindResult(ErrorCode.DEFAULT, "wifi ssid error");
//                    }
//                    break;
//                case DsCamBleCmd.SET_WIFI_PASSWORD:
//                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
//                        startConfigWifi();
//                    } else {
//                        callBackBindResult(ErrorCode.DEFAULT, "wifi ssid error");
//                    }
//                    break;
//                case DsCamBleCmd.SET_APP_ID:
//                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
//                        setAppSecret();
//                    } else {
//                        callBackBindResult(ErrorCode.DEFAULT, "wifi appid error");
//                    }
//                    break;
//                case DsCamBleCmd.SET_APP_SECRET:
//                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
//                        setHomeID();
//                    } else {
//                        callBackBindResult(ErrorCode.DEFAULT, "wifi sec error");
//                    }
//                    break;
//                case DsCamBleCmd.SET_HOME:
//                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
//                        setHTTPHost();
//                    } else {
//                        callBackBindResult(ErrorCode.DEFAULT, "wifi homeid error");
//                    }
//                    break;
//                case DsCamBleCmd.SET_HTTP_HOST:
//                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
//                        setUDP();
//                    } else {
//                        callBackBindResult(ErrorCode.DEFAULT, "wifi http host error");
//                    }
//                    break;
//                case DsCamBleCmd.SET_UDP_HOST:
//                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
//                        register();
//                    } else {
//                        callBackBindResult(ErrorCode.DEFAULT, "wifi udp error");
//                    }
//                    break;
//                case DsCamBleCmd.REGISTER:
//                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
//                        if (cmd.equals(DsCamBleCmd.REGISTER)) {
//                            jsonObject.put("homeID", mHomeID);
//                            jsonObject.put("provider", "dscam");
//                            MsctLog.i(TAG, jsonObject.toString());
//                            CommonCmdEvent commonCmdEvent = new CommonCmdEvent(CommonCmdEvent.CMD.DSCAM_ADD);
//                            commonCmdEvent.setExtra(jsonObject.toString());
//                            EventBus.getDefault().post(commonCmdEvent);
//                            callBackBindResult(1, null);
//                        }
//                    } else {
//                        callBackBindResult(MsctJSONUtil.getInt(jsonObject, "status"), "bind ipc error");
//                    }
//                    break;
//            }
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//    }
//
//    private void setSsid() {
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("cmd", DsCamBleCmd.SET_WIFI_NAME);
//            jsonObject.put("data", mSsid);
//            BleHelper.getInstance().write(jsonObject, null);
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//    }
//
//    private void setSsidPW() {
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("cmd", DsCamBleCmd.SET_WIFI_PASSWORD);
//            jsonObject.put("data", mSsidPassword);
//            BleHelper.getInstance().write(jsonObject, null);
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//    }
//
//    private void setAppID() {
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("cmd", DsCamBleCmd.SET_APP_ID);
//            jsonObject.put("data", DinCore.getInstance().getAppID());
//            BleHelper.getInstance().write(jsonObject, null);
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//    }
//
//    private void setAppSecret() {
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("cmd", DsCamBleCmd.SET_APP_SECRET);
//            jsonObject.put("data", DinCore.getInstance().getAppSecret());
//            BleHelper.getInstance().write(jsonObject, null);
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//    }
//
//    private void setHTTPHost() {
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("cmd", DsCamBleCmd.SET_HTTP_HOST);
//            jsonObject.put("data", DinCore.getInstance().getDomain());
//            BleHelper.getInstance().write(jsonObject, null);
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//    }
//
//    private void setUDP() {
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("cmd", DsCamBleCmd.SET_UDP_HOST);
//            jsonObject.put("data", DinCore.getInstance().getUdpAddress());
//            BleHelper.getInstance().write(jsonObject, null);
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//    }
//
//    private void setHomeID() {
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("cmd", DsCamBleCmd.SET_HOME);
//            jsonObject.put("data", mHomeID);
//            BleHelper.getInstance().write(jsonObject, null);
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//    }
//
//    private void register() {
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("cmd", DsCamBleCmd.REGISTER);
//            BleHelper.getInstance().write(jsonObject, null);
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//    }
//
//
//    @Override
//    public void stop() {
//        super.stop();
//        stopDiscoveryDevice();
//        BleHelper.getInstance().disconnectAllDevice();
//    }
//
//    @Override
//    @Keep
//    public void destroyBinder() {
//        super.destroyBinder();
//        wifiListCallBack = null;
//        BleHelper.getInstance().release();
//        BleHelper.getInstance().disconnectAllDevice();
//    }
//
//    @Keep
//    public interface IWifiListCallBack {
//        void onWifiListCallBack(List<String> wifis);
//    }
//
//}
