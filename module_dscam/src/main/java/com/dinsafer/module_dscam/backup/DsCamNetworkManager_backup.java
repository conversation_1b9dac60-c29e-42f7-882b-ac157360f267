//package com.dinsafer.module_dscam.backup;
//
//import android.content.Context;
//import androidx.annotation.Keep;
//import androidx.annotation.NonNull;
//import android.text.TextUtils;
//
//import com.clj.fastble.callback.BleScanCallback;
//import com.clj.fastble.data.BleDevice;
//import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
//import com.dinsafer.dincore.activtor.bean.Plugin;
//import com.dinsafer.dincore.common.ErrorCode;
//import com.dinsafer.dincore.utils.BleHelper;
//import com.dinsafer.dssupport.msctlib.MsctLog;
//import com.dinsafer.dssupport.msctlib.utils.MsctJSONUtil;
//import com.dinsafer.module_dscam.DsCamBleCmd;
//import com.dinsafer.module_dscam.DsCamConvert;
//
//import org.json.JSONException;
//import org.json.JSONObject;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * DsCam 网络修改器
// */
//public class DsCamNetworkManager_backup extends BasePluginBinder {
//
//    private String mSsid, mSsidPassword;
//
//    private boolean isTimeOutToFindDevice = false;
//
//    public static final String UUID = "23593c00-69b8-419b-84f3-a3fe7a354cdb";
//    public static final String WRITE_UUID = "23593c01-69b8-419b-84f3-a3fe7a354cdb";
//    public static final String READ_UUID = "23593c02-69b8-419b-84f3-a3fe7a354cdb";
//    private BleHelper.IMessageCallback callback = new BleHelper.IMessageCallback() {
//        @Override
//        public void onMessage(byte[] notifyData) {
//            handlerMessage(notifyData);
//        }
//    };
//
//    private IWifiListCallBack wifiListCallBack;
//
//    private DsCamConvert camConvert = new DsCamConvert();
//
//
//    @Keep
//    public DsCamNetworkManager_backup(@NonNull Context mContext) {
//        super(mContext);
//    }
//
//
//    @Override
//    @Keep
//    public void bindDevice(Plugin plugin) {
//        //
//        if (TextUtils.isEmpty(mSsid)) {
//            callBackBindResult(ErrorCode.DEFAULT, "ssid is null");
//            return;
//        }
//
//        if (TextUtils.isEmpty(mSsidPassword)) {
//            callBackBindResult(ErrorCode.DEFAULT, "password is null");
//            return;
//        }
//        setSsid();
//
//    }
//
//    @Keep
//    public void setSsid(String mSsid) {
//        this.mSsid = mSsid;
//    }
//
//    @Keep
//    public void setSsidPassword(String mSsidPassword) {
//        this.mSsidPassword = mSsidPassword;
//    }
//
//
//    private void startConfigWifi() {
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("cmd", DsCamBleCmd.SET_NET);
//            jsonObject.put("modify",1);
//            BleHelper.getInstance().write(jsonObject, null);
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Keep
//    public void discoveryDevice(long timeout, BleScanCallback bleScanCallback) {
//        BleHelper.getInstance().setScanRuleWithUUID(timeout, UUID, WRITE_UUID, READ_UUID);
//        BleHelper.getInstance().scanDevice(bleScanCallback);
//    }
//
//    @Keep
//    public void stopDiscoveryDevice() {
//        BleHelper.getInstance().cancelScan();
//    }
//
//    @Keep
//    public void connectDevice(BleDevice dsCam,
//                              BleHelper.ConnectCallback connectCallback) {
//        BleHelper.getInstance().release();
//        BleHelper.getInstance().addConnectCallBack(connectCallback);
//        BleHelper.getInstance().addMessageCallBack(callback);
//        BleHelper.getInstance().connected(dsCam, camConvert);
//    }
//
//    @Keep
//    public void getWifiList() {
//        wifiList.clear();
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("cmd", "get_wifi_list");
//            BleHelper.getInstance().write(jsonObject, null);
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Keep
//    public IWifiListCallBack getWifiListCallBack() {
//        return wifiListCallBack;
//    }
//
//    @Keep
//    public void setWifiListCallBack(IWifiListCallBack wifiListCallBack) {
//        this.wifiListCallBack = wifiListCallBack;
//    }
//
//    private List<String> wifiList = new ArrayList<>();
//
//    private void handlerMessage(byte[] notifyData) {
//        String js = new String(notifyData);
//        MsctLog.i(TAG, "handler rece:" + js);
//        try {
//            JSONObject jsonObject = new JSONObject(js);
//            String cmd = MsctJSONUtil.getString(jsonObject, "cmd");
//            switch (cmd) {
//                case DsCamBleCmd.GET_WIFI_LIST:
//                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
//                        if (wifiListCallBack != null) {
//                            wifiListCallBack.onWifiListCallBack(wifiList);
//                        }
//                    } else {
//                        wifiList.add(MsctJSONUtil.getString(jsonObject, "result"));
//                    }
//                    break;
//                case DsCamBleCmd.SET_NET:
//                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
//                        callBackBindResult(1, null);
//                    } else {
//                        callBackBindResult(ErrorCode.DEFAULT, "wifi connect error");
//                    }
//                    break;
//                case DsCamBleCmd.SET_WIFI_NAME:
//                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
//                        setSsidPW();
//                    } else {
//                        callBackBindResult(ErrorCode.DEFAULT, "wifi ssid error");
//                    }
//                    break;
//                case DsCamBleCmd.SET_WIFI_PASSWORD:
//                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
//                        startConfigWifi();
//                    } else {
//                        callBackBindResult(ErrorCode.DEFAULT, "wifi ssid error");
//                    }
//                    break;
//            }
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//    }
//
//    private void setSsid() {
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("cmd", DsCamBleCmd.SET_WIFI_NAME);
//            jsonObject.put("data", mSsid);
//            BleHelper.getInstance().write(jsonObject, null);
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//    }
//
//    private void setSsidPW() {
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("cmd", DsCamBleCmd.SET_WIFI_PASSWORD);
//            jsonObject.put("data", mSsidPassword);
//            BleHelper.getInstance().write(jsonObject, null);
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Override
//    public void stop() {
//        super.stop();
//        stopDiscoveryDevice();
//        BleHelper.getInstance().disconnectAllDevice();
//    }
//
//    @Override
//    @Keep
//    public void destroyBinder() {
//        super.destroyBinder();
//        wifiListCallBack = null;
//        BleHelper.getInstance().release();
//        BleHelper.getInstance().disconnectAllDevice();
//    }
//
//    @Keep
//    public interface IWifiListCallBack {
//        void onWifiListCallBack(List<String> wifis);
//    }
//
//}
