//package com.dinsafer.module_dscam.bean;
//
//import android.text.TextUtils;
//
//import com.dinsafer.dincore.common.Cmd;
//import com.dinsafer.dincore.common.Device;
//import com.dinsafer.dincore.common.ErrorCode;
//import com.dinsafer.dincore.http.StringResponseEntry;
//import com.dinsafer.dincore.utils.DDJSONUtil;
//import com.dinsafer.dincore.utils.MapUtils;
//import com.dinsafer.dssupport.msctlib.MsctLog;
//import com.dinsafer.dssupport.msctlib.kcp.IKcpCallBack;
//import com.dinsafer.dssupport.msctlib.kcp.IKcpCreateCallBack;
//import com.dinsafer.dssupport.msctlib.netty.IMultipleSender;
//import com.dinsafer.dssupport.utils.DDLog;
//import com.dinsafer.module_dscam.channel.Channel;
//import com.dinsafer.module_dscam.channel.ChannelManager;
//import com.dinsafer.module_dscam.channel.IChannelCallBack;
//import com.dinsafer.module_dscam.channel.LanChannel;
//import com.dinsafer.module_dscam.channel.ProxyChannel;
//import com.dinsafer.module_dscam.http.DsCamApi;
//
//import org.json.JSONException;
//import org.json.JSONObject;
//
//import java.text.DateFormat;
//import java.text.SimpleDateFormat;
//import java.util.Calendar;
//import java.util.Date;
//import java.util.Locale;
//import java.util.Map;
//import java.util.TimeZone;
//import java.util.concurrent.ExecutorService;
//import java.util.concurrent.Executors;
//
//import retrofit2.Call;
//import retrofit2.Callback;
//import retrofit2.Response;
//
//public class DsCamDevice_backup extends Device {
//    private static final String TAG = DsCamDevice_backup.class.getSimpleName();
//    private String name;
//    //    ipc 的end_id
//    private String receiveID;
//    private String group_id;
//    //    跟ipc通讯的end_id
//    private Long addtime;
//    private String homeID;
//    private IMultipleSender sender;
//    private IKcpCallBack kcpCallBack;
//    private int connectStatus = -1;
//
//    //    ipc 属性，需要调用getipcinfo才能获取
//    private boolean hd = false;
//    private boolean vflip = false;
//    private boolean hflip = false;
//    //    时区
//    private String tz = "";
//    //    当前网络
//    private String ssid = "";
//    private String ip = "";
//    private int battery = -1;
//    private boolean charging = false;
//    //    tf卡容量，KB
//    private long tf_capacity = -1;
//    //    tf卡已使用
//    private long tf_used = -1;
//    //    移动侦测
//    private boolean md = false;
//    private boolean gray = false;
//    //    移动侦测等级
//    private int md_level = -1;
//    private String version = "";
//    private String mdBeginTime = "";
//    private String mdEndTime = "";
//    private String alertMode = "";
//    //    private int dscamSessionID = RandomStringUtils.getSessionID();
//    private String lanIp = "";
//    private String mac = "";
//    private int rssi = 0;
//    private int lanPort;
//    //    表示对象是否已经被删除，删除的话，不要回调连接状态变化，重复了
//    private boolean isDelete = false;
////    ipc 属性结束
//
//    private ChannelManager channelManager;
//
//    private ExecutorService executorService = Executors.newCachedThreadPool();
//
//    private IChannelCallBack channelCallBack = new IChannelCallBack() {
//        @Override
//        public void onConnect() {
//            if (isDelete) {
//                return;
//            }
//            if (connectStatus == 1) {
//                return;
//            }
//            connectStatus = 1;
//            convertToInfo();
//
//            Map result = Cmd.getDefaultResultMap(true, DsCamCmd.CONNECT);
//            result.put("connect_status", 1);
//            dispatchResult(DsCamCmd.CONNECT, result);
//
//            Map statusResult = Cmd.getDefaultResultMap(true, DsCamCmd.CONNECT_STATUS_CHANGED);
//            statusResult.put("connect_status", 1);
//            dispatchResult(DsCamCmd.CONNECT_STATUS_CHANGED, statusResult);
//
//            dispatchOnline();
//        }
//
//        @Override
//        public void onConnecting() {
//            if (isDelete) {
//                return;
//            }
//            if (connectStatus == 0) {
//                return;
//            }
//            Map result = Cmd.getDefaultResultMap(false, DsCamCmd.CONNECT);
//            result.put("connect_status", 0);
//            connectStatus = 0;
//            convertToInfo();
//            dispatchResult(DsCamCmd.CONNECT, result);
//        }
//
//        @Override
//        public void onDisconnect(int code, String msg) {
//            if (isDelete) {
//                return;
//            }
//            connectStatus = -1;
//            convertToInfo();
//            if (code == ErrorCode.ERROR_CONNECT) {
//                Map result = Cmd.getDefaultResultMap(false, DsCamCmd.CONNECT);
//                result.put("errorMessage", msg);
//                result.put("connect_status", -1);
//                dispatchResult(DsCamCmd.CONNECT, result);
//            } else {
//                if (connectStatus == -1) {
//                    return;
//                }
//                Map result = Cmd.getDefaultResultMap(true, DsCamCmd.CONNECT_STATUS_CHANGED);
//                result.put("connect_status", -1);
//                dispatchResult(DsCamCmd.CONNECT_STATUS_CHANGED, result);
//                dispatchOffline(msg);
//            }
//        }
//    };
//
//    public DsCamDevice_backup(IMultipleSender sender, String pid, String homeID, String receiveID, String group_id, Long addtime) {
//        super();
//        this.receiveID = receiveID;
//        this.group_id = group_id;
//        this.addtime = addtime;
//        this.homeID = homeID;
//        this.sender = sender;
//        this.setId(pid);
//        this.setCategory(2);
//        this.setSubCategory("dscam");
//        if (TextUtils.isEmpty(name)) {
//            name = "ipc";
//        }
//        if (receiveID == null) {
//            receiveID = "";
//        }
//        if (group_id == null) {
//            group_id = "";
//        }
//        if (this.homeID == null) {
//            this.homeID = "";
//        }
//        convertToInfo();
//        kcpCallBack = new IKcpCallBack() {
//            @Override
//            public void onMessage(int conv, byte[] bytes) {
//                byte[] msgb = bytes;
//                try {
//                    String j = new String(msgb);
//                    MsctLog.i(TAG, "receive decode:" + j);
//                    JSONObject jsonObject = new JSONObject(j);
//                    handlerKcpResponse(jsonObject);
//                } catch (JSONException e) {
//                    e.printStackTrace();
//                }
//
//            }
//
//            @Override
//            public void onException(String s) {
//
//            }
//
//            @Override
//            public void onClose() {
//
//            }
//        };
//        channelManager = new ChannelManager(this, homeID, kcpCallBack);
//        channelManager.setChannelCallBack(channelCallBack);
//
//    }
//
//    private void handlerKcpResponse(JSONObject result) {
//        String cmd = DDJSONUtil.getString(result, "cmd");
//        int status = DDJSONUtil.getInt(result, "status");
//        Map resultMap = Cmd.getDefaultResultMap(status == 1 ? true : false, cmd);
//        if (status != 1) {
//            resultMap.put("errorMessage", "cmd:" + cmd + " not work");
//            dispatchResult(cmd, resultMap);
//            return;
//        }
//        Map tempResult = MapUtils.fromJson(result);
//        switch (cmd) {
//            case DsCamCmd.GET_PARAMS:
//                this.hd = (boolean) MapUtils.get(tempResult, "hd", false);
//                this.vflip = (boolean) MapUtils.get(tempResult, "vflip", false);
//                this.hflip = (boolean) MapUtils.get(tempResult, "hflip", false);
//                this.tz = (String) MapUtils.get(tempResult, "tz", "");
//                this.ssid = (String) MapUtils.get(tempResult, "ssid", "");
//                this.ip = (String) MapUtils.get(tempResult, "ip", "");
//                this.battery = (int) MapUtils.get(tempResult, "battery", -1);
//                this.charging = (boolean) MapUtils.get(tempResult, "charging", false);
//                if (MapUtils.get(tempResult, "tf_capacity", -1) instanceof Integer) {
//                    this.tf_capacity = (int) MapUtils.get(tempResult, "tf_capacity", -1);
//                } else {
//                    this.tf_capacity = (long) MapUtils.get(tempResult, "tf_capacity", -1);
//                }
//
//                if (MapUtils.get(tempResult, "tf_capacity", -1) instanceof Integer) {
//                    this.tf_capacity = (int) MapUtils.get(tempResult, "tf_capacity", -1);
//                } else {
//                    this.tf_capacity = (long) MapUtils.get(tempResult, "tf_capacity", -1);
//                }
//
//                if (MapUtils.get(tempResult, "tf_capacity", -1) instanceof Integer) {
//                    this.tf_used = (int) MapUtils.get(tempResult, "tf_used", -1);
//                } else {
//                    this.tf_used = (long) MapUtils.get(tempResult, "tf_used", -1);
//                }
//                this.md = (boolean) MapUtils.get(tempResult, "md", false);
//                this.md_level = (int) MapUtils.get(tempResult, "md_level", -1);
//                this.version = (String) MapUtils.get(tempResult, "version", "");
//                this.mdBeginTime = (String) MapUtils.get(tempResult, "md_begin_time", "08:00");
//                this.mdEndTime = (String) MapUtils.get(tempResult, "md_end_time", "23:00");
//                this.mac = (String) MapUtils.get(tempResult, "mac", "");
//                this.rssi = (int) MapUtils.get(tempResult, "rssi", 0);
//                this.gray = (boolean) MapUtils.get(tempResult, "gray", false);
//                this.lanIp = this.ip;
//                if (MapUtils.get(tempResult, "tf_capacity", -1) instanceof Integer) {
//                    this.lanPort = (int) MapUtils.get(tempResult, "port", -1);
//                } else {
//                    this.lanPort = (int) ((long) MapUtils.get(tempResult, "port", -1));
//                }
//                convertToInfo();
//                startLanConnect(this.lanIp, this.lanPort);
//                break;
//            case DsCamCmd.SET_MD:
//                this.md = DDJSONUtil.getBoolean(result, "md");
//                convertToInfo();
//                break;
//            case DsCamCmd.SET_GRAY:
//                this.gray = DDJSONUtil.getBoolean(result, "gray");
//                convertToInfo();
//                break;
//            case DsCamCmd.SET_MD_LEVEL:
//                this.md_level = DDJSONUtil.getInt(result, "md_level");
//                convertToInfo();
//                break;
//            case DsCamCmd.SET_VFLIP:
//                this.vflip = DDJSONUtil.getBoolean(result, "vflip");
//                convertToInfo();
//                break;
//            case DsCamCmd.SET_HFLIP:
//                this.hflip = DDJSONUtil.getBoolean(result, "hflip");
//                convertToInfo();
//                break;
//            case DsCamCmd.SET_TZ:
//                this.tz = DDJSONUtil.getString(result, "tz");
//                convertToInfo();
//                break;
//            case DsCamCmd.FORMAT_TF:
//                break;
//            case DsCamCmd.RESTORE_DEFAULT:
//                this.hd = (boolean) MapUtils.get(tempResult, "hd", false);
//                this.vflip = (boolean) MapUtils.get(tempResult, "vflip", false);
//                this.hflip = (boolean) MapUtils.get(tempResult, "hflip", false);
//                this.tz = (String) MapUtils.get(tempResult, "tz", "");
//                this.md = (boolean) MapUtils.get(tempResult, "md", false);
//                this.md_level = (int) MapUtils.get(tempResult, "md_level", -1);
//                this.mdBeginTime = (String) MapUtils.get(tempResult, "md_begin_time", "08:00");
//                this.mdEndTime = (String) MapUtils.get(tempResult, "md_end_time", "23:00");
//                convertToInfo();
//                break;
//            case DsCamCmd.RESET:
//                reboot();
//                break;
//            case DsCamCmd.SET_MD_TIME:
//                this.mdBeginTime = (String) MapUtils.get(tempResult, "md_begin_time", "08:00");
//                this.mdEndTime = (String) MapUtils.get(tempResult, "md_end_time", "23:00");
//                convertToInfo();
//                break;
//            case DsCamCmd.KEEPALIVE:
//                break;
//            case DsCamCmd.LED_TEST:
//                break;
//            case DsCamCmd.SET_FACTORY:
//            case DsCamCmd.GET_REGTOKEN:
//                break;
//        }
//        resultMap.putAll(tempResult);
//        dispatchResult(cmd, resultMap);
//    }
//
//    public DsCamDevice_backup(IMultipleSender sender, String homeID, DsCamListResponse.ResultBean deviceBean) {
//        super();
//        this.sender = sender;
//        this.setId(deviceBean.getPid());
//        this.setCategory(2);
//        this.setSubCategory("dscam");
//        this.name = deviceBean.getName();
//        this.receiveID = deviceBean.getEnd_id();
//        this.group_id = deviceBean.getGroup_id();
//        this.addtime = deviceBean.getAddtime();
//        this.homeID = homeID;
//        if (TextUtils.isEmpty(name)) {
//            name = "ipc";
//        }
//        if (receiveID == null) {
//            receiveID = "";
//        }
//        if (group_id == null) {
//            group_id = "";
//        }
//        if (this.homeID == null) {
//            this.homeID = "";
//        }
//        convertToInfo();
//        kcpCallBack = new IKcpCallBack() {
//            @Override
//            public void onMessage(int conv, byte[] bytes) {
//                byte[] msgb = bytes;
//                try {
//                    String j = new String(msgb);
//                    MsctLog.i(TAG, "receive decode:" + j);
//                    JSONObject jsonObject = new JSONObject(j);
//                    handlerKcpResponse(jsonObject);
//                } catch (JSONException e) {
//                    e.printStackTrace();
//                }
//
//            }
//
//            @Override
//            public void onException(String s) {
//
//            }
//
//            @Override
//            public void onClose() {
//                MsctLog.i(TAG, "onClose");
//                disconnect();
//            }
//        };
//        channelManager = new ChannelManager(this, homeID, kcpCallBack);
//        channelManager.setChannelCallBack(channelCallBack);
//    }
//
//    private void reboot() {
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("cmd", DsCamCmd.REBOOT);
//            byte[] msg = jsonObject.toString().getBytes();
//            sendByte(msg);
//        } catch (JSONException e) {
//
//        }
//    }
//
//
//    protected void convertToInfo() {
////       有几个字段要暴露出去
////        插座：
////        1. name
////        2. networkState
//        Map jsonObject = this.getInfo();
//        jsonObject.put("name", name);
//        jsonObject.put("networkState", connectStatus);
//        jsonObject.put("addtime", this.addtime);
//        jsonObject.put("hd", this.hd);
//        jsonObject.put("vflip", this.vflip);
//        jsonObject.put("hflip", this.hflip);
//        jsonObject.put("tz", this.tz);
//        jsonObject.put("ssid", this.ssid);
//        jsonObject.put("ip", this.ip);
//        jsonObject.put("battery", this.battery);
//        jsonObject.put("charging", this.charging);
//        jsonObject.put("tfCapacity", this.tf_capacity);
//        jsonObject.put("tfUsed", this.tf_used);
//        jsonObject.put("md", this.md);
//        jsonObject.put("mdLevel", this.md_level);
//        jsonObject.put("mdBeginTime", this.mdBeginTime);
//        jsonObject.put("mdEndTime", this.mdEndTime);
//        jsonObject.put("alertMode", this.alertMode);
//        jsonObject.put("version", this.version);
//        jsonObject.put("mac", this.mac);
//        jsonObject.put("rssi", this.rssi);
//        jsonObject.put("gray", this.gray);
//    }
//
//    @Override
//    public void submit(Map arg) {
//        String cmd = (String) arg.get("cmd");
//        switch (cmd) {
//            case DsCamCmd.SET_NAME:
//                setName((String) arg.get("name"));
//                break;
//            case DsCamCmd.DELETE_DEVICE:
//                delete();
//                break;
//            case DsCamCmd.CONNECT:
//                connect();
//                break;
//            case DsCamCmd.DISCONNECT:
//                disconnect();
//                break;
//            case DsCamCmd.GET_PARAMS:
//                getIPCInfo();
//                break;
//            case DsCamCmd.SET_ALERT_MODE:
//                setAlertMode(arg);
//                break;
//            case DsCamCmd.GET_ALERT_MODE:
//                getAlertMode();
//                break;
//            case DsCamCmd.SET_TZ:
//                snycTimeZone();
//                break;
//            case DsCamCmd.SET_MD:
//            case DsCamCmd.SET_MD_LEVEL:
//            case DsCamCmd.SET_VFLIP:
//            case DsCamCmd.SET_HFLIP:
//            case DsCamCmd.FORMAT_TF:
//            case DsCamCmd.RESTORE_DEFAULT:
//            case DsCamCmd.RESET:
//            case DsCamCmd.SET_GRAY:
//            case DsCamCmd.SET_MD_TIME:
//            case DsCamCmd.LED_TEST:
//            case DsCamCmd.SET_FACTORY:
//            case DsCamCmd.GET_REGTOKEN:
//                setCam(arg);
//                break;
//            default: {
//                Map result = Cmd.getDefaultResultMap(false, null);
//                result.put("errorMessage", "cmd:" + cmd + " not support");
//                dispatchResult(cmd, result);
//            }
//
//        }
//    }
//
//    private void snycTimeZone() {
//        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("GMT"),
//                Locale.getDefault());
//        Date currentLocalTime = calendar.getTime();
//
//        DateFormat date = new SimpleDateFormat("ZZZZZ", Locale.getDefault());
//        String localTime = date.format(currentLocalTime);
//        localTime = "tzn" + localTime + ":00";
//        DDLog.i("timezone", localTime);
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("cmd", DsCamCmd.SET_TZ);
//            jsonObject.put("tz", localTime);
//            byte[] msg = jsonObject.toString().getBytes();
//            sendByte(msg);
//        } catch (JSONException e) {
//            Map result = Cmd.getDefaultResultMap(false, DsCamCmd.SET_TZ);
//            result.put("errorMessage", "params error");
//            dispatchResult(DsCamCmd.SET_TZ, result);
//        }
//
//    }
//
//    private void getAlertMode() {
//        DsCamApi.getInstance().getAlertMode(homeID, getId())
//                .enqueue(new Callback<DsCamAlertModeResponse>() {
//                    @Override
//                    public void onResponse(Call<DsCamAlertModeResponse> call, Response<DsCamAlertModeResponse> response) {
//                        DsCamAlertModeResponse dsCamAlertModeResponse = response.body();
//                        alertMode = dsCamAlertModeResponse.getResult().getAlert_mode();
////                        if (alertMode.equals("critical")) {
////                            alertMode = "normal";
////                        }
//                        convertToInfo();
//                        Map result = Cmd.getDefaultResultMap(true, DsCamCmd.GET_ALERT_MODE);
//                        result.put("alert_mode", alertMode);
//                        dispatchResult(DsCamCmd.GET_ALERT_MODE, result);
//                    }
//
//                    @Override
//                    public void onFailure(Call<DsCamAlertModeResponse> call, Throwable t) {
//                        Map result = Cmd.getDefaultResultMap(false, DsCamCmd.GET_ALERT_MODE);
//                        result.put("errorMessage", "sender is null");
//                        dispatchResult(DsCamCmd.GET_ALERT_MODE, result);
//                    }
//                });
//    }
//
//    private void setAlertMode(Map arg) {
//        DsCamApi.getInstance().setAlertMode(homeID, getId(),
//                (String) arg.get("alert_mode")).enqueue(new Callback<StringResponseEntry>() {
//            @Override
//            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
//                alertMode = (String) arg.get("alert_mode");
//                if (alertMode.equals("critical")) {
//                    alertMode = "normal";
//                }
//                convertToInfo();
//                Map result = Cmd.getDefaultResultMap(true, DsCamCmd.SET_ALERT_MODE);
//                dispatchResult(DsCamCmd.SET_ALERT_MODE, result);
//            }
//
//            @Override
//            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
//                Map result = Cmd.getDefaultResultMap(false, DsCamCmd.SET_ALERT_MODE);
//                result.put("errorMessage", "sender is null");
//                dispatchResult(DsCamCmd.SET_ALERT_MODE, result);
//            }
//        });
//    }
//
//    //    直接把使用者传过来的数据，发给ipc即可，不需要中途处理
//    private void setCam(Map arg) {
//        String par = MapUtils.toJson(arg);
//        byte[] msg = par.getBytes();
//        sendByte(msg);
//    }
//
//    private void getIPCInfo() {
//
//        if (!channelManager.isConnect()) {
//            Map result = Cmd.getDefaultResultMap(false, DsCamCmd.GET_PARAMS);
//            result.put("errorMessage", "cam is no connect");
//            dispatchResult(DsCamCmd.GET_PARAMS, result);
//            return;
//        }
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("cmd", DsCamCmd.GET_PARAMS);
//            byte[] msg = jsonObject.toString().getBytes();
//            sendByte(msg);
//        } catch (JSONException e) {
//            Map result = Cmd.getDefaultResultMap(false, DsCamCmd.GET_PARAMS);
//            result.put("errorMessage", "params error");
//            dispatchResult(DsCamCmd.GET_PARAMS, result);
//        }
//    }
//
//    public void disconnect() {
//        channelManager.disconnect();
//    }
//
//    private void delete() {
//        DsCamApi.getInstance().deleteIpc(homeID, getId()).enqueue(new Callback<StringResponseEntry>() {
//            @Override
//            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
//                MsctLog.i(TAG, "删除ipc成功");
//                Map result = Cmd.getDefaultResultMap(true, DsCamCmd.DELETE_DEVICE);
//                dispatchResult(DsCamCmd.DELETE_DEVICE, result);
//                isDelete = true;
//                disconnect();
//                remove();
//            }
//
//            @Override
//            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
//                MsctLog.i(TAG, "删除ipc失败：" + t.getCause());
//                Map result = Cmd.getDefaultResultMap(false, DsCamCmd.DELETE_DEVICE);
//                result.put("errorMessage", "mes:" + t.getMessage());
//                dispatchResult(DsCamCmd.DELETE_DEVICE, result);
//            }
//        });
//    }
//
//    public void deleteDirect() {
//        isDelete = true;
//        disconnect();
//        remove();
//    }
//
//
//    private void setName(String newname) {
//        DsCamApi.getInstance().setName(homeID, getId(), newname).enqueue(new Callback<StringResponseEntry>() {
//            @Override
//            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
//                name = newname;
//                convertToInfo();
//                Map result = Cmd.getDefaultResultMap(true, DsCamCmd.SET_NAME);
//                dispatchResult(DsCamCmd.SET_NAME, result);
//            }
//
//            @Override
//            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
//                Map result = Cmd.getDefaultResultMap(false, DsCamCmd.SET_NAME);
//                result.put("errorMessage", "sender is null");
//                dispatchResult(DsCamCmd.SET_NAME, result);
//            }
//        });
//    }
//
//    public void setNameDirect(String newname) {
//        name = newname;
//        convertToInfo();
//        Map result = Cmd.getDefaultResultMap(true, DsCamCmd.SET_NAME);
//        dispatchResult(DsCamCmd.SET_NAME, result);
//    }
//
//    private void sendByte(byte[] msg) {
//        Channel channel = channelManager.getChannel();
//        if (channel != null) {
//            channel.kcpCmdSend(msg);
//        } else {
//            MsctLog.e(TAG, "want to send something but no channel connect");
//        }
//
//    }
//
//    private void connect() {
//        MsctLog.e(TAG, "ipc connect groupID:" + group_id);
//        Channel channel = channelManager.getChannel(Channel.PROXY);
//        ProxyChannel proxyChannel = (ProxyChannel) channel;
//        proxyChannel.setGroup_id(group_id);
//        proxyChannel.setReceiveID(receiveID);
//        channel.connect();
////        p2p通道得等proxy通道通了才可以
////        Channel p2pChannel = channelManager.getChannel(Channel.P2P);
////        p2pChannel.connect();
//
//    }
//
//    public void createKcp(int type, int sessionID, IKcpCreateCallBack kcpCreateCallBack) {
//        channelManager.createKcp(type, sessionID, kcpCreateCallBack);
//    }
//
//    public void removeKcp(int conv) {
//        channelManager.removeKcp(conv);
//    }
//
//
//    public IMultipleSender getMultiSender() {
//        return this.sender;
//    }
//
//    public void startLanConnect(String ip, int port) {
////         开始连接lan
//        LanChannel lanChannel = (LanChannel) channelManager.getChannel(Channel.LAN);
//        if (lanChannel.isConnect()) {
//            return;
//        }
//        if (TextUtils.isEmpty(ip) || port <= 0) {
//            return;
//        }
//        this.lanIp = ip;
//        this.lanPort = port;
//        executorService.submit(new Runnable() {
//            @Override
//            public void run() {
//                synchronized (channelCallBack) {
//                    LanChannel lanChannel = (LanChannel) channelManager.getChannel(Channel.LAN);
//                    lanChannel.setIp(lanIp);
//                    lanChannel.setPort(lanPort);
////                    找到ip之后，尝试连，有可能失败，可能还没有chart_secret
//                    if (channelManager.isConnect()) {
//                        lanChannel.connect();
//                    }
//                }
//            }
//        });
//    }
//
//    @Override
//    public void destory() {
//        super.destory();
//        channelManager.destory();
//    }
//}
