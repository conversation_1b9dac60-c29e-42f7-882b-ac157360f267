package com.dinsafer.module_dscam;

import android.bluetooth.BluetoothGatt;
import androidx.annotation.Keep;

import com.clj.fastble.data.BleDevice;
import com.clj.fastble.exception.BleException;

@Keep
public interface IBleConnectCallBack {
    void onStartConnect();

    void onConnectFail(BleDevice bleDevice, BleException exception);

    void onConnectSuccess(BleDevice bleDevice, BluetoothGatt gatt, int status);

    void onDisConnected(BleDevice bleDevice, boolean isActiveDisConnected, BluetoothGatt gatt, int status);

}
