package com.dinsafer.module_dscam.v006;

import android.content.Context;
import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.dinsafer.dincore.common.CommonCmdEvent;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.module_dscam.BaseCamBinder;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * DSCAM_V006添加器,蓝牙
 */
@Keep
public class DsCamV006Binder extends BaseCamBinder {

    public DsCamV006Binder(@NonNull Context mContext) {
        super(mContext);
        UUID = "23593c00-69b8-431b-a241-b9afa31c160b";
        WRITE_UUID = "23593c01-69b8-431b-a241-b9afa31c160b";
        READ_UUID = "23593c02-69b8-431b-a241-b9afa31c160b";
    }

    @Override
    protected void handleRegisterSuccess(JSONObject data) throws JSONException {
        data.put("homeID", mHomeID);
        data.put("provider", "DSCAM_V006");
        MsctLog.i(TAG, data.toString());
        CommonCmdEvent commonCmdEvent = new CommonCmdEvent(CommonCmdEvent.CMD.DSCAM_ADD);
        commonCmdEvent.setExtra(data.toString());
        EventBus.getDefault().post(commonCmdEvent);
    }


}
