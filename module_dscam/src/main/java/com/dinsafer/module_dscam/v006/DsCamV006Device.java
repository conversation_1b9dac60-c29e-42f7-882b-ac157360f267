package com.dinsafer.module_dscam.v006;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.dinsafer.dssupport.msctlib.netty.IMultipleSender;
import com.dinsafer.module_dscam.bean.BaseCamDevice;
import com.dinsafer.module_dscam.bean.DsCamCacheInfo;
import com.dinsafer.module_dscam.bean.DsCamListResponse;

@Keep
public class DsCamV006Device extends BaseCamDevice {

    public DsCamV006Device(IMultipleSender sender, String pid, String homeID, String receiveID, String group_id, Long addtime) {
        super(sender, pid, homeID, receiveID, group_id, addtime, "DSCAM_V006");
    }

    public DsCamV006Device(IMultipleSender sender, String homeID, DsCamListResponse.ResultBean deviceBean) {
        super(sender, homeID, deviceBean, "DSCAM_V006");
    }

    public DsCamV006Device(IMultipleSender sender, String homeID, @NonNull final DsCamCacheInfo.CacheInfo cacheInfo) {
        super(sender, homeID, cacheInfo, "DSCAM_V006");
    }
}