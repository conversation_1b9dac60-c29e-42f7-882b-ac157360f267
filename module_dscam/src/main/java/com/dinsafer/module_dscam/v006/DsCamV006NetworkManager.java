package com.dinsafer.module_dscam.v006;

import android.content.Context;
import androidx.annotation.Keep;

import com.dinsafer.module_dscam.BaseCamNetworkManager;

/**
 * DsCamV006网络修改器
 */
@Keep
public class DsCamV006NetworkManager extends BaseCamNetworkManager {

    public DsCamV006NetworkManager(Context mContext) {
        super(mContext);

        UUID = "23593c00-69b8-431b-a241-b9afa31c160b";
        WRITE_UUID = "23593c01-69b8-431b-a241-b9afa31c160b";
        READ_UUID = "23593c02-69b8-431b-a241-b9afa31c160b";
    }

}
