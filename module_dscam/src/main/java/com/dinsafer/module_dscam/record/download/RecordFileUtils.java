package com.dinsafer.module_dscam.record.download;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dssupport.msctlib.MsctLog;

import java.io.File;

/**
 * SD卡录像下载文件管理辅助类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/12/1 11:25 上午
 */
public class RecordFileUtils {
    private static final String TAG = "RecordFileUtils";

    public static final String POSTFIX_PHOTO = ".jpg";
    public static final String POSTFIX_VIDEO = ".mp4";
    public static final String POSTFIX_DOWNLOADING = ".temp";
    private static final String ROOT_DIR_NAME = "records";

    private final File cacheRoot;

    private static class Holder {
        private static final RecordFileUtils INSTANCE = new RecordFileUtils();
    }

    private RecordFileUtils() {
        File cacheDir = DinCore.getInstance().getApplication().getExternalCacheDir();
        cacheRoot = new File(cacheDir, ROOT_DIR_NAME);
        if (!cacheRoot.exists()) {
            cacheRoot.mkdirs();
        }
        MsctLog.i(TAG, "Record缓存根目录: " + cacheRoot.getAbsolutePath());
    }

    public static RecordFileUtils get() {
        return Holder.INSTANCE;
    }

    public File getRecordInstanceRootDir(RecordDownloadTask task) {
        File taskRootDir = new File(cacheRoot, task.getOwner());
        if (!taskRootDir.exists()) {
            taskRootDir.mkdirs();
        }
        return taskRootDir.exists() ? taskRootDir : null;
    }

    /**
     * 获取之前下载的文件的路径
     *
     * @param task
     * @return 如果不存在，返回null
     */
    public File getRecordFile(RecordDownloadTask task) {
        File taskRootDir = getRecordInstanceRootDir(task);
        if (null == taskRootDir) {
            return null;
        }

        File taskFile = new File(taskRootDir, task.getLocalCompleteFileName());
        return taskFile.exists() ? taskFile : null;
    }
}
