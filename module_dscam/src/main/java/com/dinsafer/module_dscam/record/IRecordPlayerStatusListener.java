package com.dinsafer.module_dscam.record;

import androidx.annotation.Keep;

@Keep
public interface IRecordPlayerStatusListener {

    void onStart();

    void onPrepared();

    void onStop();

    void onRelease();

    /**
     * @param currentPosition 当前进度, 毫秒
     * @param totalDuration   总时长, 毫秒
     * @param bufferPer       缓存百分比
     */
    void onUpdate(int currentPosition, int totalDuration, int bufferPer);

    void onError(int code, String error);

    void onCompletion();
}
