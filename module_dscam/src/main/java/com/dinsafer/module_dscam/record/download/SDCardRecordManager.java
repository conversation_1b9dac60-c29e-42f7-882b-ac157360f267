package com.dinsafer.module_dscam.record.download;

import android.text.TextUtils;

import com.dinsafer.dincore.common.Cmd;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.msctlib.RandomStringUtils;
import com.dinsafer.dssupport.msctlib.convert.DefaultConvert;
import com.dinsafer.dssupport.msctlib.kcp.IKcpCallBack;
import com.dinsafer.dssupport.msctlib.kcp.IKcpCreateCallBack;
import com.dinsafer.dssupport.msctlib.kcp.KcpClientImpl;
import com.dinsafer.module_dscam.bean.BaseCamDevice;
import com.dinsafer.module_dscam.bean.DsCamCmd;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.Map;
import java.util.PriorityQueue;
import java.util.concurrent.TimeUnit;

import rx.Observable;
import rx.Subscriber;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;

/**
 * 摄像头录像管理辅助类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/11/30 6:42 下午
 */
public class SDCardRecordManager implements RecordDownloadCallback {
    private static final String TAG = "SDCardRecordManager";

    private static final int MAX_ERROR_COUNT = 3; // 创建KCP错误的最大重试次数

    private final BaseCamDevice mDevice;

    private volatile KcpClientImpl mKcpRecord;
    private volatile boolean mCreatingKcpRecord = false;
    private boolean mReDownloadOnKcpClose = false;
    private int mCreateErrorCount = 0; // 创建KCP错误的次数

    private Subscription mHeartBitInterval;

    private final PriorityQueue<RecordDownloadTask> mQueue;
    private volatile RecordDownloadTask mDownloadTask;

    public SDCardRecordManager(BaseCamDevice device) {
        this.mDevice = device;
        RecordDownloadTaskComparator comparator = new RecordDownloadTaskComparator();
        this.mQueue = new PriorityQueue<>(10, comparator);
    }

    /**
     * 获取具体的录像或接头文件
     * "cmd":"get_record_file",   // string 指令
     * "fileName": ""             // string，要下载的文件名
     * // "fileName": "2021/11/17/08_41_34"
     * "type": "photo"            // string，(可供选择参数值:"photo","video")
     */
    public void requestRecordFile(Map arg) {
        final String fileName = DeviceHelper.getString(arg, "fileName", "");
        final String type = DeviceHelper.getString(arg, "type", "photo");

        if (DeviceHelper.getInt(mDevice, "networkState", 0) != 1) {
            dispatchFailed(fileName, type, "cam is no connect");
            return;
        }

        try {
            RecordDownloadTask task = buildDownloadTask(fileName, type);
            if (null == task) {
                MsctLog.e(TAG, "Can't build download task");
                return;
            }

            // 处理重复任务
            if (task.equals(mDownloadTask)) {
                return;
            }
            mQueue.remove(task);

            // 先尝试读取缓存
            File response = RecordFileUtils.get().getRecordFile(task);
            if (null != response) {
                MsctLog.i(TAG, "成功读取缓存: " + task.getFileName());
                dispatchSuccess(task.getFileName(), RecordDownloadTask.getTypeInString(task.getType()),
                        response.getAbsolutePath());
                return;
            }

            // 如果当前处于连续多次创建KCP失败的情况，直接通知失败
            if (mCreateErrorCount > MAX_ERROR_COUNT) {
                dispatchFailed(task.getFileName(),
                        RecordDownloadTask.getTypeInString(task.getType()),
                        "Error on create kcp for download record!");
                return;
            }

            // 添加到任务队列
            boolean success = mQueue.offer(task);
            if (!success) {
                dispatchFailed(task.getFileName(),
                        RecordDownloadTask.getTypeInString(task.getType())
                        , "Can't add task to queue");
                return;
            }

            MsctLog.i("TAG", "Current task queue's size: " + mQueue.size());

            // 开始下载
            mCreateErrorCount = 0;
            mReDownloadOnKcpClose = true;
            startDownload();
        } catch (Exception e) {
            e.printStackTrace();
            dispatchFailed(fileName, type, "params error");
        }
    }

    /**
     * 暂停所有的下载，但不会清除队列任务
     */
    public void stopDownload() {
        mReDownloadOnKcpClose = false;
        clearKpcConnection();

        if (null != mDownloadTask) {
            mDownloadTask.release();

            // 将正在下载的任务重新添加到下载队列中
            RecordDownloadTask downloadTask = RecordDownloadTask.copy(mDownloadTask);
            mQueue.offer(downloadTask);

            mDownloadTask = null;
        }
    }

    /**
     * 暂停下载并清空所有下载任务
     */
    public void clearAllDownloadTask() {
        stopDownload();
        mQueue.clear();
    }

    /**
     * 停止心跳并断开KCP连接
     */
    private void clearKpcConnection() {
        MsctLog.i(TAG, "clearKpcConnection");
        stopHeartBit();
        closeKcp();
        mCreatingKcpRecord = false;
    }

    /**
     * 确保KCP就绪
     * 如果当前kcp不可用，创建kcp
     *
     * @return true: kcp已经就绪，可以进行数据传输
     */
    private boolean ensureRecordKcpReady() {
        if (null == mKcpRecord) {
            if (!mCreatingKcpRecord) {
                createKcp();
            }
            return false;
        }

        return true;
    }

    /**
     * 如果没有创建KCP就创建KCP，
     * 如果准备就绪下载任务
     */
    private synchronized void startDownload() {
        if (null != mDownloadTask || mCreatingKcpRecord) {
            MsctLog.w(TAG, "Schedule tasks success, auto download when another task finished");
            return;
        }

        if (!ensureRecordKcpReady()) {
            MsctLog.e(TAG, "kcp not ready!!!");
            return;
        }

        mDownloadTask = findNextDownloadTask();
        if (mDownloadTask == null) {
            return;
        }

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("filename", mDownloadTask.getCompleteFileName());
            byte[] msg = jsonObject.toString().getBytes();
            mKcpRecord.sendByte(msg);
            MsctLog.i(TAG, "Real start download");
            mDownloadTask.setDownloadCallback(this);
            mDownloadTask.onRealStart();
        } catch (JSONException e) {
            e.printStackTrace();
            dispatchCurrentFailedAndRelease("Send cmd failed!");
        }
    }

    /**
     * 获取下一个能进行下载的任务
     *
     * @return 能进行下载的任务
     */
    private RecordDownloadTask findNextDownloadTask() {
        RecordDownloadTask task = mQueue.poll();
        if (task != null) {
            boolean success = task.prepare();
            if (!success) {
                dispatchFailed(task.getFileName(),
                        RecordDownloadTask.getTypeInString(task.getType()), "Error on prepare");
                return findNextDownloadTask();
            } else {
                return task;
            }
        } else {
            return null;
        }
    }

    private RecordDownloadTask buildDownloadTask(final String fileName, final String type) {
        final String deviceId = mDevice.getId();
        if (TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(fileName)) {
            MsctLog.e(TAG, "Empty device id or fileName");
            dispatchFailed(fileName, type, "params error");
            return null;
        }

        int targetType = -1;
        if (RecordDownloadTask.RECORD_TYPE_PHOTO_STR.equals(type)) {
            targetType = RecordDownloadTask.RECORD_TYPE_PHOTO;
        } else if (RecordDownloadTask.RECORD_TYPE_VIDEO_STR.equals(type)) {
            targetType = RecordDownloadTask.RECORD_TYPE_VIDEO;
        }
        if (-1 == targetType) {
            MsctLog.e(TAG, "UnSupport type: " + type);
            dispatchFailed(fileName, type, "UnSupport type: " + type);
            return null;
        }

        return new RecordDownloadTask(deviceId, fileName, targetType);
    }

    private void createKcp() {
        if (mCreatingKcpRecord) {
            return;
        }

        closeKcp();

        mCreatingKcpRecord = true;
        final int stdVideoSessionID = RandomStringUtils.getSessionID();

        mDevice.createKcp(KcpClientImpl.TYPE_DOWNLOAD_RECORD, stdVideoSessionID, new IKcpCreateCallBack() {
            @Override
            public void onCreate(KcpClientImpl kcpClient) {
                mCreateErrorCount = 0;
                mKcpRecord = kcpClient;
                mKcpRecord.setConvert(new DefaultConvert());
                mCreatingKcpRecord = false;

                mKcpRecord.setCallBack(new IKcpCallBack() {
                    @Override
                    public void onMessage(int i, byte[] bytes) {
                        if (null != mDownloadTask && !mDownloadTask.isFinished()) {
                            mDownloadTask.onReceiveData(i, bytes);
                        }
                    }

                    @Override
                    public void onException(String s) {
                        MsctLog.e(TAG, "onException: " + s);
                    }

                    @Override
                    public void onClose() {
                        MsctLog.i(TAG, "onClose");
                        clearKpcConnection();
                        dispatchCurrentFailedAndRelease("On kcp close");

                        if (mReDownloadOnKcpClose) {
                            startDownload();
                        }
                    }
                });

                mKcpRecord.connect();
                startHeartBit();
                startDownload();
            }

            @Override
            public void onError(int i, String s) {
                MsctLog.e(TAG, "create kcp record fail:" + i + " msg:" + s);
                mCreatingKcpRecord = false;
                mCreateErrorCount++;

                if (mCreateErrorCount > MAX_ERROR_COUNT) {
                    MsctLog.e(TAG, "Can't create kcp record:" + i + " msg:" + s);
                    RecordDownloadTask task = findNextDownloadTask();
                    for (; null != task; task = findNextDownloadTask()) {
                        dispatchFailed(task.getFileName(),
                                RecordDownloadTask.getTypeInString(task.getType()),
                                "Can't create kcp for download record!");
                    }
                    mCreateErrorCount = 0;
                }
            }
        });
    }

    private void closeKcp() {
        if (null != mKcpRecord) {
            mKcpRecord.close();
            mDevice.removeKcp(mKcpRecord.getConv());
            mKcpRecord = null;
        }
    }

    private void startHeartBit() {
        if (null != mHeartBitInterval
                && !mHeartBitInterval.isUnsubscribed()) {
            return;
        }
        mHeartBitInterval = Observable.interval(5, TimeUnit.SECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {
                    }

                    @Override
                    public void onError(Throwable e) {
                    }

                    @Override
                    public void onNext(Object o) {
                        if (mKcpRecord == null) {
                            clearKpcConnection();
                            dispatchCurrentFailedAndRelease("Kcp client is null!");
                            return;
                        }

                        MsctLog.v(TAG, "travelHeartBit");
                        JSONObject jsonObject = new JSONObject();
                        try {
                            mKcpRecord.sendString(jsonObject.toString());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
    }

    private void stopHeartBit() {
        MsctLog.i(TAG, "stopHeartBit");
        if (null != mHeartBitInterval
                && !mHeartBitInterval.isUnsubscribed()) {
            mHeartBitInterval.unsubscribe();
            mHeartBitInterval = null;
        }
    }

    private void dispatchCurrentFailedAndRelease(String reason) {
        if (null != mDownloadTask) {
            dispatchFailed(mDownloadTask.getFileName(),
                    RecordDownloadTask.getTypeInString(mDownloadTask.getType()),
                    "Current Task Failed for reason: " + reason);
            mDownloadTask.release();
            mDownloadTask = null;
        }
    }

    private void dispatchFailed(final String fileName, String type, final String errorMsg) {
        Map result = Cmd.getDefaultResultMap(false, DsCamCmd.GET_RECORD_FILE);
        result.put("cmd", DsCamCmd.GET_RECORD_FILE);
        result.put("type", type);
        result.put("fileName", fileName);
        result.put("errorMessage", errorMsg);
        mDevice.dispatchResult(DsCamCmd.GET_RECORD_FILE, result);
    }

    private void dispatchSuccess(String fileName, String type, String filePath) {
        Map result = Cmd.getDefaultResultMap(true, DsCamCmd.GET_RECORD_FILE);
        result.put("cmd", DsCamCmd.GET_RECORD_FILE);
        result.put("type", type);
        result.put("fileName", fileName);
        result.put("filePath", filePath);
        mDevice.dispatchResult(DsCamCmd.GET_RECORD_FILE, result);
    }

    @Override
    public void onSuccess(String fileName, String type, String filePath) {
        // 通知下载结果
        dispatchSuccess(fileName, type, filePath);

        mDownloadTask = null;

        startDownload();
    }

    @Override
    public void onFailed(String fileName, String type, int code, String message) {
        // 通知下载结果
        dispatchFailed(fileName, type, "code:" + code + ", message:" + message);

        mDownloadTask = null;

        if (code == RecordDownloadTask.ERROR_CODE_DEFAULT) {
            // 失败后需要重新连接KCP
            clearKpcConnection();
        }

        startDownload();
    }
}
