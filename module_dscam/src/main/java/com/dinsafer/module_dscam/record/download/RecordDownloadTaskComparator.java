package com.dinsafer.module_dscam.record.download;

import java.util.Comparator;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/12/3 11:25 上午
 */
public class RecordDownloadTaskComparator implements Comparator<RecordDownloadTask> {
    @Override
    public int compare(RecordDownloadTask o1, RecordDownloadTask o2) {
        int result = 0;
        int type = o2.getType() - o1.getType();
        int priority = o2.getPriority() - o1.getPriority();
        if (type != 0) {
            result = type > 0 ? 2 : -1;
        } else {
            if (priority != 0) {
                result = priority > 0 ? 1 : -2;
            }
        }
        return result;
    }
}
