package com.dinsafer.module_dscam.record.download;

import androidx.annotation.IntDef;
import android.text.TextUtils;

import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dssupport.msctlib.MsctLog;

import org.json.JSONObject;

import java.io.File;
import java.io.FileOutputStream;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * SD卡录像下载任务
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/12/1 11:14 上午
 */
public class RecordDownloadTask {
    private static final String TAG = "RecordDownloadTask";

    private static final long TIME_OUT_SECONDS = 120; // 任务超时时间：s

    public static final int ERROR_CODE_DEFAULT = -1; // 默认错误类型-会触发重连kcp
    public static final int ERROR_CODE_NOT_EXIT = -404; // 文件不存在-不会触发重连kcp

    public static final String SEPARATOR_DEFAULT = "/";
    public static final String SEPARATOR_REPLACED = "_";

    public static final int RECORD_TYPE_UNDEFINE = -1; // 未知
    public static final int RECORD_TYPE_PHOTO = 0; // 下载图片
    public static final int RECORD_TYPE_VIDEO = 1; // 下载录像

    public static final String RECORD_TYPE_PHOTO_STR = "photo"; // 下载图片
    public static final String RECORD_TYPE_VIDEO_STR = "video"; // 下载录像

    @Retention(RetentionPolicy.SOURCE)
    @IntDef({RECORD_TYPE_UNDEFINE, RECORD_TYPE_PHOTO, RECORD_TYPE_VIDEO})
    public @interface RecordType {
    }

    private static final ScheduledExecutorService mTimeoutScheduler = Executors.newSingleThreadScheduledExecutor();

    private final String owner; // 录像或截图所属的deviceId，用于创建目录
    private final String fileName;
    private final @RecordType
    int type;
    private int priority;// 越大越先下载

    private long totalLength;
    private long currentLength;
    private File downloadTempFile;
    private FileOutputStream mOs;
    private RecordDownloadCallback mCallback;
    private boolean finished = false;
    private ScheduledFuture<?> mScheduleFuture;

    public RecordDownloadTask(String owner, String fileName, @RecordType int type) {
        this.owner = owner;
        this.fileName = fileName;
        this.type = type;
    }

    public RecordDownloadTask(String owner, String fileName, @RecordType int type, int priority) {
        this.owner = owner;
        this.fileName = fileName;
        this.type = type;
        this.priority = priority;
    }

    public static String getTypeInString(@RecordType int type) {
        if (RECORD_TYPE_VIDEO == type) {
            return RECORD_TYPE_VIDEO_STR;
        } else if (RECORD_TYPE_PHOTO == type) {
            return RECORD_TYPE_PHOTO_STR;
        }
        return "";
    }

    @RecordType
    public static int getTypeByString(String type) {
        if (RECORD_TYPE_VIDEO_STR.equals(type)) {
            return RECORD_TYPE_VIDEO;
        } else if (RECORD_TYPE_PHOTO_STR.equals(type)) {
            return RECORD_TYPE_PHOTO;
        }
        return RECORD_TYPE_UNDEFINE;
    }

    public void onRealStart(){
        scheduleTimeOut();
    }

    /**
     * 准备下载文件路径
     *
     * @return false 表示无法创建文件
     */
    public boolean prepare() {
        try {
            File deviceRootDir = RecordFileUtils.get().getRecordInstanceRootDir(this);
            String tempFileName = getLocalTempFileName();
            if (null == deviceRootDir || TextUtils.isEmpty(tempFileName)) {
                return false;
            }
            downloadTempFile = new File(deviceRootDir, tempFileName);
            if (downloadTempFile.exists()) {
                downloadTempFile.delete();
            }
            MsctLog.d(TAG, "缓存目录: " + downloadTempFile.getAbsolutePath());
            downloadTempFile.createNewFile();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    public void setDownloadCallback(RecordDownloadCallback callback) {
        if (null != mCallback) {
            throw new IllegalArgumentException("You have set callback before");
        }

        this.mCallback = callback;
    }

    public void release() {
        finished = true;
        try {
            if (mOs != null) {
                mOs.close();
            }
            if (null != mScheduleFuture) {
                mScheduleFuture.cancel(true);
                mScheduleFuture = null;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static RecordDownloadTask copy(RecordDownloadTask source) {
        return new RecordDownloadTask(source.getOwner(), source.getFileName(), source.getType(), source.getPriority());
    }

    /**
     * 接收视频数据流
     *
     * @param bytes 数据 {"filename":"2021/11/19/07_12_15.mp4"， "filesize":1024000}\r\n\r\n
     *              [file1 binary] ....
     */
    public void onReceiveData(int i, byte[] bytes) {
        if (finished) {
            return;
        }

        if (totalLength <= 0) {
            parseFileTotalLength(bytes);
        } else {
            onSaveMediaData(bytes);
        }
    }

    private void onSaveMediaData(byte[] bytes) {
        try {
            mOs.write(bytes);
            mOs.flush();
            currentLength += bytes.length;

            MsctLog.d(TAG, "当前文件大小： " + currentLength);
            if (currentLength == totalLength) {
                File saveFile = renameFile();
                release();
                if (null != saveFile) {
                    MsctLog.i(TAG, "下载成功: " + fileName);
                    if (mCallback != null) {
                        mCallback.onSuccess(fileName, getTypeInString(type), saveFile.getAbsolutePath());
                    }
                } else {
                    notifyFailed(ERROR_CODE_DEFAULT, "Error on rename file");
                }
            } else if (currentLength > totalLength) {
                release();
                notifyFailed(ERROR_CODE_DEFAULT, "current: " + currentLength + ", total: " + totalLength);
            }
        } catch (Exception e) {
            release();
            e.printStackTrace();
            notifyFailed(ERROR_CODE_DEFAULT, "Error on saving media data");
        }
    }

    private void parseFileTotalLength(byte[] bytes) {
        try {
            String j = new String(bytes);
            MsctLog.i(TAG, owner + "Receive decode:" + j);
            JSONObject jsonObject = new JSONObject(j);
            totalLength = DDJSONUtil.getLong(jsonObject, "filesize");
            MsctLog.d(TAG, getCompleteFileName() + "文件总大小: " + totalLength);
            if (totalLength <= 0) {
                if (null != downloadTempFile && downloadTempFile.exists()) {
                    downloadTempFile.delete();
                }
                release();
                notifyFailed(ERROR_CODE_NOT_EXIT, "File's total length is zero or file not exit!!!");
                return;
            }

            mOs = new FileOutputStream(downloadTempFile);
        } catch (Exception e) {
            if (null != downloadTempFile && downloadTempFile.exists()) {
                downloadTempFile.delete();
            }
            release();
            e.printStackTrace();
            notifyFailed(ERROR_CODE_DEFAULT, "Error on parseFileTotalLength");
        }
    }

    private void scheduleTimeOut() {
        mScheduleFuture = mTimeoutScheduler.schedule(() -> {
            if (null != downloadTempFile && downloadTempFile.exists()) {
                downloadTempFile.delete();
            }
            release();
            notifyFailed(ERROR_CODE_DEFAULT, "Task timeout after " + TIME_OUT_SECONDS + "s");
        }, TIME_OUT_SECONDS, TimeUnit.SECONDS);
    }

    private File renameFile() {
        try {
            File rootDir = RecordFileUtils.get().getRecordInstanceRootDir(this);
            String name = getLocalCompleteFileName();
            if (!rootDir.exists() || TextUtils.isEmpty(name)) {
                return null;
            }
            File saveFile = new File(rootDir, name);
            if (saveFile.exists()) {
                saveFile.delete();
            }
            if (downloadTempFile.renameTo(saveFile)) {
                return saveFile;
            }
        } catch (Exception e) {
            MsctLog.e(TAG, "Error on renameFile");
            e.printStackTrace();
        }
        return null;
    }

    private void notifyFailed(int code, String message) {
        MsctLog.e(TAG, "Error, code: " + code + ", message: " + message);
        if (null != mCallback) {
            mCallback.onFailed(fileName, getTypeInString(type), code, message);
        }
    }

    public boolean isPhotoTask() {
        return RECORD_TYPE_PHOTO == type;
    }

    public boolean isVideoTask() {
        return RECORD_TYPE_PHOTO == type;
    }

    public String getOwner() {
        return owner;
    }

    public String getFileName() {
        return fileName;
    }

    public int getType() {
        return type;
    }

    public long getTotalLength() {
        return totalLength;
    }

    public void setTotalLength(long totalLength) {
        this.totalLength = totalLength;
    }

    public long getCurrentLength() {
        return currentLength;
    }

    public void setCurrentLength(long currentLength) {
        this.currentLength = currentLength;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public boolean isFinished() {
        return finished;
    }

    public String getLocalCompleteFileName() {
        final String name = getCompleteFileName();
        return TextUtils.isEmpty(name) ? null : name.replace(SEPARATOR_DEFAULT, SEPARATOR_REPLACED);
    }

    public String getLocalTempFileName() {
        final String name = getTempFileName();
        return TextUtils.isEmpty(name) ? null : name.replace(SEPARATOR_DEFAULT, SEPARATOR_REPLACED);
    }

    /**
     * 获取完整的文件名
     *
     * @return 文件名+后缀
     */
    public String getCompleteFileName() {
        if (RECORD_TYPE_PHOTO == type) {
            return fileName + RecordFileUtils.POSTFIX_PHOTO;
        } else if (RECORD_TYPE_VIDEO == type) {
            return fileName + RecordFileUtils.POSTFIX_VIDEO;
        } else {
            return fileName;
        }
    }

    /**
     * 获取缓存的文件名
     *
     * @return 文件名+后缀+.temp
     */
    public String getTempFileName() {
        final String name = getCompleteFileName();
        return TextUtils.isEmpty(name) ? null : name + RecordFileUtils.POSTFIX_DOWNLOADING;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RecordDownloadTask task = (RecordDownloadTask) o;
        return type == task.type && Objects.equals(owner, task.owner) && Objects.equals(fileName, task.fileName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(owner, fileName, type);
    }

    @Override
    public String toString() {
        return "RecordDownloadTask{" +
                "owner='" + owner + '\'' +
                ", fileName='" + fileName + '\'' +
                ", type=" + type +
                ", priority=" + priority +
                '}';
    }
}
