package com.dinsafer.module_dscam.record.google;

/*
 * Copyright (C) 2009 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import android.content.Context;
import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.media.audiofx.AcousticEchoCanceler;
import android.media.audiofx.AutomaticGainControl;
import android.media.audiofx.NoiseSuppressor;
import android.util.Log;

import com.dinsafer.module_dscam.record.RecordListener;

import vip.inode.demo.webrtc.AutomaticGainControlUtils;
import vip.inode.demo.webrtc.NoiseSuppressorUtils;


public class AudioStream {
    private static final String TAG = AudioStream.class.getSimpleName();
    private boolean mRunning = false;
    private RecordTask mRecordTask;
    private RecordListener recordListener;
    private Context context;

    public AudioStream(Context context, int mSampleRate, RecordListener recordListener) {
//        int localFrameSize = mSampleRate / 50; // 50 frames / sec
        int localFrameSize = 480; // 50 frames / sec
        mRecordTask = new RecordTask(mSampleRate, localFrameSize);
        this.recordListener = recordListener;
        this.context = context;
    }


    public void start() {
//        if (mRunning) return;
        mRunning = true;
        mRecordTask.start();
    }

    public void stop() {
        mRunning = false;
        AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        audioManager.setMode(AudioManager.MODE_NORMAL);
        audioManager.setSpeakerphoneOn(false);
    }

    public boolean isRunning() {
        return mRunning;
    }


    private class RecordTask implements Runnable {
        private int mSampleRate;
        private int mFrameSize;

        RecordTask(int sampleRate, int frameSize) {
            mSampleRate = sampleRate;
            mFrameSize = frameSize;
        }

        void start() {
            Log.d(TAG, "start RecordTask");
            new Thread(this).start();
        }

        public void run() {
            Encoder encoder = new G711ACodec();
            int recordBufferSize = encoder.getSampleCount(mFrameSize);
            short[] recordBuffer = new short[recordBufferSize];
            byte[] buffer = new byte[mFrameSize];

//            如果不设置这些，三星手机讲话会小声
            AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
            audioManager.setMode(AudioManager.MODE_IN_COMMUNICATION);
            audioManager.setSpeakerphoneOn(true);

            int bufferSize = AudioRecord.getMinBufferSize(mSampleRate,
                    AudioFormat.CHANNEL_IN_MONO,
                    AudioFormat.ENCODING_PCM_16BIT);
            AudioRecord recorder = new AudioRecord(
                    MediaRecorder.AudioSource.MIC, mSampleRate,
                    AudioFormat.CHANNEL_IN_MONO,
                    AudioFormat.ENCODING_PCM_16BIT, bufferSize);

            AutomaticGainControlUtils agcUtils = new AutomaticGainControlUtils();
            long agcId = agcUtils.agcCreate();
            int agcInitResult = agcUtils.agcInit(agcId, 0, 255, 3, 8000);
            int agcSetConfigResult = agcUtils.agcSetConfig(agcId, (short) 3, (short) 20, true);
            Log.i(TAG, "run: agcId: " + agcId + " agcInitResult: " + agcInitResult + ", agcSetConfigResult: " + agcSetConfigResult);

            recorder.startRecording();
            Log.d(TAG, "start sound recording..." + recorder.getState());
            // skip the first read, kick off read pipeline
            recorder.read(recordBuffer, 0, recordBufferSize);
            long sendCount = 0;
            long startTime = System.currentTimeMillis();
            while (mRunning) {
                int count = recorder.read(recordBuffer, 0, recordBufferSize);
                agcUtils.agcProcess(agcId, recordBuffer, 1, recordBuffer.length, recordBuffer,
                        0, 0, 1, false);
                encoder.encode(recordBuffer, buffer.length, buffer, 0);
                Log.d(TAG, "     buffer = " + buffer.length);
                recordListener.audioData(buffer);
                sendCount++;
            }
            long now = System.currentTimeMillis();
            Log.d(TAG, "     sendCount = " + sendCount);
            Log.d(TAG, "     avg send cycle ="
                    + ((double) (now - startTime) / sendCount));
            Log.d(TAG, "stop sound recording...");
            recorder.stop();
            agcUtils.agcFree(agcId);
        }
    }
}