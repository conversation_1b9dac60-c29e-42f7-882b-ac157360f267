package com.dinsafer.module_dscam.http;

import androidx.annotation.Nullable;

import com.dinsafer.module_dscam.bean.DsCamListResponse;

import retrofit2.Callback;
import retrofit2.Response;

public class DscamRepo {

    public DscamRepo() {
    }

    public void fetchDsCam(String homeID,int pageSize, long addTime,  boolean orderDesc, Callback<DsCamListResponse> callback) {
        DsCamApi.getInstance().fetchDsCamList(homeID, pageSize, addTime, orderDesc).enqueue(callback);
    }

    @Nullable
    public Response<DsCamListResponse> fetchDsCamSync(String homeID, int pageSize, long addTime, boolean orderDesc) {
        Response<DsCamListResponse> response = null;
        try {
            response = DsCamApi.getInstance().fetchDsCamList(homeID, pageSize, addTime, orderDesc).execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return response;
    }
}
