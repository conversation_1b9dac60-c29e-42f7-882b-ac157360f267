package com.dinsafer.module_dscam.http;

import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.module_dscam.bean.DsCamAlertModeResponse;
import com.dinsafer.module_dscam.bean.DsCamE2ELoginResponse;
import com.dinsafer.module_dscam.bean.DsCamListResponse;
import com.dinsafer.module_dscam.bean.SearchIpcResponse;

import java.util.Map;

import retrofit2.Call;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;
import retrofit2.http.Url;

public interface IDsCamApi {
    @POST
    @FormUrlEncoded
    Call<DsCamListResponse> fetchDsCamList(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<DsCamE2ELoginResponse> e2elogin(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> stringCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<DsCamAlertModeResponse> getAlertModeCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<SearchIpcResponse> searchIpc(@Url String url, @FieldMap Map<String, Object> map);
}
