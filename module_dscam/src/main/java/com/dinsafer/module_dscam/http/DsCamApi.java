package com.dinsafer.module_dscam.http;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dincore.http.Api;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.module_dscam.bean.DsCamAlertModeResponse;
import com.dinsafer.module_dscam.bean.DsCamE2ELoginResponse;
import com.dinsafer.module_dscam.bean.DsCamListResponse;
import com.dinsafer.module_dscam.bean.SearchIpcParams;
import com.dinsafer.module_dscam.bean.SearchIpcResponse;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

public class DsCamApi {
    private final IDsCamApi services;
    public static final String GM = "gm";
    public static final String GMTIME = "gmtime";

    private DsCamApi() {
        services = Api.getApi().getRetrofit().create(IDsCamApi.class);
    }


    private static class Holder {
        private static final DsCamApi INSTANT = new DsCamApi();
    }

    public static DsCamApi getInstance() {
        return Holder.INSTANT;
    }

    public Call<DsCamListResponse> fetchDsCamList(String homeID, int pageSize, long addTime, boolean orderDesc) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("providers", new JSONArray().put("DSCAM").put("DSDOORBELL").put("DSCAM_V006").put("DSCAM_V015"));
            jsonObject.put("page_size", pageSize);
            jsonObject.put("addtime", addTime);
            jsonObject.put("order", orderDesc ? "desc" : "asc");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.fetchDsCamList(Api.getApi()
                .getUrl(DsCamUrls.URL_FETCH_DSCAM_LIST), map);
    }

    public Call<DsCamListResponse> fetchDsCamList(String homeID, String pid, String provider) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("pids", new JSONArray().put(pid));
            jsonObject.put("providers", new JSONArray().put(provider.toUpperCase()));
            jsonObject.put("page_size", 100);
            jsonObject.put("addtime", 0);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.fetchDsCamList(Api.getApi()
                .getUrl(DsCamUrls.URL_FETCH_DSCAM_LIST), map);
    }

    public Call<DsCamE2ELoginResponse> login(String homeID, String pid, String provider) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("providers", new JSONArray().put(provider.toUpperCase()));
            jsonObject.put("page_size", 100);
            jsonObject.put("pids", new JSONArray().put(pid));
            jsonObject.put("addtime", 0);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.e2elogin(Api.getApi()
                .getUrl(DsCamUrls.URL_DSCAM_E2E_LOGIN), map);
    }

    public Call<StringResponseEntry> setName(String homeID, String pid, String provider, String name) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("pid", pid);
            jsonObject.put("provider", provider);
            jsonObject.put("name", name);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.stringCall(Api.getApi()
                .getUrl(DsCamUrls.URL_DSCAM_SETNAME), map);
    }

    public Call<StringResponseEntry> setAlertMode(String homeID, String pid, String provider, String mode, boolean cloud_storage,
                                                  @Nullable final Boolean arm, @Nullable final Boolean disarm,
                                                  @Nullable final Boolean homeArm, @Nullable final Boolean sos) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("provider", provider);
            jsonObject.put("pid", pid);
            jsonObject.put("alert_mode", mode);
            jsonObject.put("cloud_storage", cloud_storage);

            if (null != arm && null != disarm && null != homeArm && null != sos) {
                jsonObject.put("arm", arm.booleanValue());
                jsonObject.put("disarm", disarm.booleanValue());
                jsonObject.put("home_arm", homeArm.booleanValue());
                jsonObject.put("sos", sos.booleanValue());
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.stringCall(Api.getApi()
                .getUrl(DsCamUrls.URL_DSCAM_SET_ALERT_MODE), map);
    }

    public Call<DsCamAlertModeResponse> getAlertMode(String homeID, String pid, String provider) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("provider", provider);
            jsonObject.put("pid", pid);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getAlertModeCall(Api.getApi()
                .getUrl(DsCamUrls.URL_DSCAM_GET_ALERT_MODE), map);
    }


    public Call<StringResponseEntry> deleteIpc(String homeID, String pid, String provider) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("provider", provider);
            jsonObject.put("pid", pid);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.stringCall(Api.getApi()
                .getUrl(DsCamUrls.URL_DSCAM_DELETE), map);
    }

    /**
     * 查询指定 pid 的自研 ipc
     */
    public Call<SearchIpcResponse> searchIpc(String homeID, @NonNull List<SearchIpcParams> ipcs) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            final JSONArray pidList = new JSONArray();
            if (null != ipcs && ipcs.size() > 0) {
                for (SearchIpcParams ipc : ipcs) {
                    final String pid = ipc.getPid();
                    final String provider = ipc.getProvider();
                    if (!TextUtils.isEmpty(pid) && !TextUtils.isEmpty(provider)) {
                        final JSONObject obj = new JSONObject();
                        obj.put("pid", pid);
                        obj.put("provider", provider.toUpperCase());
                        pidList.put(obj);
                    }
                }
            }
            jsonObject.put("home_id", homeID);
            jsonObject.put("pids", pidList);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.searchIpc(Api.getApi()
                .getUrl(DsCamUrls.URL_SEARCH_IPC), map);
    }


}
