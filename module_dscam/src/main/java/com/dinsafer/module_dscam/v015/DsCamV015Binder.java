package com.dinsafer.module_dscam.v015;

import android.content.Context;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.dinsafer.dincore.common.CommonCmdEvent;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.module_dscam.BaseCamBinder;
import com.dinsafer.module_dscam.bean.DsCamConst;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * DSCAM_V015添加器,蓝牙
 */
@Keep
public class DsCamV015Binder extends BaseCamBinder {

    public DsCamV015Binder(@NonNull Context mContext) {
        super(mContext);
        UUID = "23593c00-69b8-4e6d-aaca-7151114385e2";
        WRITE_UUID = "23593c01-69b8-4e6d-aaca-7151114385e2";
        READ_UUID = "23593c02-69b8-4e6d-aaca-7151114385e2";
    }

    @Override
    protected void handleRegisterSuccess(JSONObject data) throws JSONException {
        data.put("homeID", mHomeID);
        data.put("provider", DsCamConst.PROVIDER_DSCAM_V015);
        MsctLog.i(TAG, data.toString());
        CommonCmdEvent commonCmdEvent = new CommonCmdEvent(CommonCmdEvent.CMD.DSCAM_ADD);
        commonCmdEvent.setExtra(data.toString());
        EventBus.getDefault().post(commonCmdEvent);
    }


}
