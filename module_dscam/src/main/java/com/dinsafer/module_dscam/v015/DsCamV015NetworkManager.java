package com.dinsafer.module_dscam.v015;

import android.content.Context;

import androidx.annotation.Keep;

import com.dinsafer.module_dscam.BaseCamNetworkManager;

/**
 * DsCamV006网络修改器
 */
@Keep
public class DsCamV015NetworkManager extends BaseCamNetworkManager {

    public DsCamV015NetworkManager(Context mContext) {
        super(mContext);

        UUID = "23593c00-69b8-4e6d-aaca-7151114385e2";
        WRITE_UUID = "23593c01-69b8-4e6d-aaca-7151114385e2";
        READ_UUID = "23593c02-69b8-4e6d-aaca-7151114385e2";
    }

}
