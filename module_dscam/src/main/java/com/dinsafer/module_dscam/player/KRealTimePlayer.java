package com.dinsafer.module_dscam.player;

import android.media.MediaCodec;
import android.media.MediaFormat;
import android.util.Log;
import android.view.Surface;

import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_dscam.player.record.AudioEncodeThread;
import com.dinsafer.module_dscam.player.record.DsCamLiveRecorder;
import com.dinsafer.module_dscam.player.record.IRecordCallBack;

import java.nio.ByteBuffer;

public class KRealTimePlayer implements IAudioDataCallBack {

    private String TAG = getClass().getSimpleName();

    private VideoDecodeThread videoDecodeThread;
    private AudioDecodeThread audioDecodeThread;
    private DsCamLiveRecorder dsCamLiveRecorder;
    private AudioEncodeThread audioEncodeThread;

    public KRealTimePlayer() {
        init();
    }

    private void init() {
        videoDecodeThread = new VideoDecodeThread();
        audioDecodeThread = new AudioDecodeThread();
        audioDecodeThread.setAudioDataCallBack(this);
    }

    /**
     * 请设置create好的surface，在onsurfacecreate调用
     *
     * @param surface
     */
    public void setSurface(Surface surface) {
        videoDecodeThread.setSurface(surface);
        videoDecodeThread.setViewPre(true);
    }

    public Surface getSurface() {
        return videoDecodeThread.getSurface();
    }


    public void clearAudio() {
        audioDecodeThread.clearData();
    }


    public boolean prepareAsync() {
//        if (!videoDecodeThread.isViewPre()) {
//            return false;
//        }
        videoDecodeThread.start();
        audioDecodeThread.start();
        return true;
    }

    public void resetVideo() {
        if (videoDecodeThread != null) {
            Surface surface = videoDecodeThread.getSurface();
            if (surface != null) {
                videoDecodeThread.stopVideo();
                videoDecodeThread = new VideoDecodeThread();
                videoDecodeThread.setSurface(surface);
                videoDecodeThread.setViewPre(true);
                videoDecodeThread.start();
            } else {
                DDLog.e("video", "videodecodethread surface not ready");
                videoDecodeThread = new VideoDecodeThread();
                videoDecodeThread.start();
            }
        } else {
            DDLog.e("video", "videodecodethread not ready");
        }

    }


    public void release() {
        videoDecodeThread.stopVideo();
        audioDecodeThread.stopAudio();
        if (audioEncodeThread != null) {
            audioEncodeThread.stopAudio();
        }
    }


    public void feed(byte[] data, int type) {
        if (type == 0) {
            audioDecodeThread.feed(data);
        } else {
            videoDecodeThread.feed(data);
            if (dsCamLiveRecorder != null) {
                MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();
                dsCamLiveRecorder.writeVideoData(ByteBuffer.wrap(data), bufferInfo);
            }
        }

    }

    public void startRecord(String path, IRecordCallBack callBack) {
        dsCamLiveRecorder = new DsCamLiveRecorder(path,callBack);
        dsCamLiveRecorder.start();
        audioEncodeThread = new AudioEncodeThread();
        audioEncodeThread.setAudioDataCallBack(aacAudioDataCallback);
        audioEncodeThread.start();
    }

    public void stopRecord() {
        if (audioEncodeThread != null) {
            audioEncodeThread.setAudioDataCallBack(null);
            audioEncodeThread.stopAudio();
            audioEncodeThread = null;
        }

        if (null != dsCamLiveRecorder) {
            dsCamLiveRecorder.stop();
            dsCamLiveRecorder.release();
            dsCamLiveRecorder = null;
        }
    }


    /**
     * G711解码成pcm的回调
     *
     * @param pcm
     * @param bufferInfo
     */
    @Override
    public void onAudioData(byte[] pcm, MediaCodec.BufferInfo bufferInfo) {
        Log.v(TAG, "onAudioData pcm: " + pcm.length);
        if (dsCamLiveRecorder != null && dsCamLiveRecorder.isRecording()
                && audioEncodeThread != null && !audioEncodeThread.isInterrupted()) {
            audioEncodeThread.feed(pcm);
        }
    }

    /**
     * pcm编码成aac的回调
     */
    private IAudioDataCallBack aacAudioDataCallback = new IAudioDataCallBack() {

        @Override
        public void onMediaFormatChangeed(MediaFormat mediaFormat) {
//            if (dsCamLiveRecorder != null) {
//                dsCamLiveRecorder.setAudioFormat(mediaFormat);
//            }
        }

        @Override
        public void onAudioData(byte[] aac, MediaCodec.BufferInfo bufferInfo) {
            Log.d(TAG, "onAudioData aac: " + aac.length + ", " + bufferInfo.presentationTimeUs);
            if (dsCamLiveRecorder != null && dsCamLiveRecorder.isRecording()) {
                dsCamLiveRecorder.writeAudioData(ByteBuffer.wrap(aac), bufferInfo);
            }
        }
    };
}
