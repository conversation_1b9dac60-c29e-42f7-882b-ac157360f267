package com.dinsafer.module_dscam.player.record;

import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.AudioTrack;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaFormat;
import android.util.Log;

import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.module_dscam.player.IAudioDataCallBack;
import com.dinsafer.module_dscam.record.google.Decoder;
import com.dinsafer.module_dscam.record.google.G711ACodec;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.concurrent.LinkedBlockingQueue;

public class AudioEncodeThread_backup extends Thread {

    private static final String TAG = AudioEncodeThread_backup.class.getSimpleName();

    /**
     * 用来读取音視频文件 提取器
     */
    private MediaCodec mediaCodec;

    private boolean isPlaying;

    private boolean isStop;

    private LinkedBlockingQueue packages = new LinkedBlockingQueue<>();
//    private AudioTrack audioTrack;

    int sampleRate = 8000;

    private IAudioDataCallBack iAudioDataCallBack;

    public AudioEncodeThread_backup() {
        initAudioTrack();

    }

    /**
     * 初始化AudioTrack，等待播放数据
     */
    private void initAudioTrack() {
        int streamType = AudioManager.STREAM_SYSTEM;
        int channelConfig = AudioFormat.CHANNEL_OUT_MONO;
        int audioFormat = AudioFormat.ENCODING_PCM_16BIT;
        int mode = AudioTrack.MODE_STREAM;

        int minBufferSize = AudioTrack.getMinBufferSize(sampleRate, channelConfig, audioFormat);

//        audioTrack = new AudioTrack(streamType, sampleRate, channelConfig, audioFormat,
//                Math.max(minBufferSize, 2048), mode);
//        audioTrack.play();
    }

    public void setAudioDataCallBack(IAudioDataCallBack iAudioDataCallBack) {
        this.iAudioDataCallBack = iAudioDataCallBack;
    }

    public boolean isPlaying() {
        return isPlaying;
    }

    public boolean isStop() {
        return isStop;
    }

    public void stopAudio() {
        isStop = true;
        iAudioDataCallBack = null;
        this.interrupt();
    }

    public void feed(byte[] pcm) {
        try {
            packages.put(pcm);
            Log.d(TAG, "feed: " + (pcm == null ? 0 : pcm.length));
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public void clearData() {
        try {
            packages.clear();
            iAudioDataCallBack = null;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public void run() {


//        if (audioTrack == null) {
//            MsctLog.e(TAG, "创建播放器失败");
//            return;
//        }
        MediaFormat mediaFormat = createFormat();
        String mimeType = mediaFormat.getString(MediaFormat.KEY_MIME);

        try {
            if (mediaCodecDecoder(mediaFormat, mimeType)) return;
        } catch (Exception e) {
            e.printStackTrace();
            MsctLog.e(TAG, "硬编码失败,mimeType:" + mimeType);
//            softWareDecoder();
            return;
        }
    }

    private void softWareDecoder() {
        MsctLog.e(TAG, "创建解码器失败,使用软解 softWareDecoder");
        Decoder decoder = new G711ACodec();
        isPlaying = true;
        isStop = false;

        // ==========开始解码=============
        while (!Thread.interrupted()) {
            try {
                byte[] frame = (byte[]) packages.take();
//                byte[] pcm = new byte[frame.length];
//                decoder.decode(pcm, frame, frame.length, 0);
                byte[] pcm = new byte[frame.length * 2];
                convertG711ToPcm(frame, frame.length, pcm);
                cutNoise(pcm, pcm.length);
//                audioTrack.write(pcm, 0, pcm.length);
                if (iAudioDataCallBack != null) {
                    iAudioDataCallBack.onAudioData(pcm, null);
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

        }
        return;
    }

    public static byte[] convertG711ToPcm(byte[] g711Buffer, int length, byte[] pcmBuffer) {
        if (pcmBuffer == null) {
            pcmBuffer = new byte[length * 2];
        }
        for (int i = 0; i < length; i++) {
            byte alaw = g711Buffer[i];
            alaw ^= 0xD5;

            int sign = alaw & 0x80;
            int exponent = (alaw & 0x70) >> 4;
            int value = (alaw & 0x0F) >> 4 + 8;
            if (exponent != 0) {
                value += 0x0100;
            }
            if (exponent > 1) {
                value <<= (exponent - 1);
            }
            value = (char) ((sign == 0 ? value : -value) & 0xFFFF);
            pcmBuffer[i * 2 + 0] = (byte) (value & 0xFF);
            pcmBuffer[i * 2 + 1] = (byte) (value >> 8 & 0xFF);
        }
        return pcmBuffer;
    }

    //    降噪
    public int cutNoise(byte[] buf, int nb_sample) {
        int i = 0;
        for (i = 0; i < nb_sample; i++) {
            buf[i] >>= 2;
        }
        return 0;
    }

    private boolean mediaCodecDecoder(MediaFormat mediaFormat, String mimeType) throws IOException {
        mediaCodec = MediaCodec.createEncoderByType(mimeType); // 创建解码器,提供数据输出
        mediaCodec.configure(mediaFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
        if (mediaCodec == null) {
            Log.e(TAG, "Can't find video info!");
            return true;
        }

        mediaCodec.start(); // 启动MediaCodec ，等待传入数据
        // 输入
        ByteBuffer[] inputBuffers = mediaCodec.getInputBuffers(); // 用来存放目标文件的数据
        // 输出
        ByteBuffer[] outputBuffers = mediaCodec.getOutputBuffers(); // 编码后的数据
        MediaCodec.BufferInfo info = new MediaCodec.BufferInfo(); // 用于描述编码得到的byte[]数据的相关信息
        boolean bIsEos = false;
        long startMs = System.currentTimeMillis();

        isPlaying = true;
        isStop = false;

        // ==========开始解码=============
        while (!Thread.interrupted()) {
            try {
                byte[] frame = (byte[]) packages.take();
                Log.d(TAG, "encode:" + frame.length);
                if (!bIsEos) {
                    int inIndex = mediaCodec.dequeueInputBuffer(2000);
                    if (inIndex >= 0) {
                        ByteBuffer buffer = inputBuffers[inIndex];
                        buffer.clear();
                        buffer.put(frame);
                        mediaCodec.queueInputBuffer(inIndex, 0, frame.length, 0, 0);
                    }
                }

                info = new MediaCodec.BufferInfo();
                int outIndex = mediaCodec.dequeueOutputBuffer(info, 10_000);
                switch (outIndex) {
                    case MediaCodec.INFO_OUTPUT_BUFFERS_CHANGED:
                        Log.d(TAG, "INFO_OUTPUT_BUFFERS_CHANGED");
                        outputBuffers = mediaCodec.getOutputBuffers();
                        break;
                    case MediaCodec.INFO_OUTPUT_FORMAT_CHANGED:
                        Log.d(TAG, "New format " + mediaCodec.getOutputFormat());
                        break;
                    case MediaCodec.INFO_TRY_AGAIN_LATER:
                        Log.d(TAG, "dequeueOutputBuffer timed out!");
                        break;
                    default:
                        ByteBuffer buffer = outputBuffers[outIndex];
//                        Log.v(TAG, "We can't use this buffer but render it due to the API limit, " + buffer);
                        byte[] aac = new byte[info.size];
                        buffer.get(aac);
                        buffer.clear();
//                        audioTrack.write(pcm, 0, pcm.length);
                        if (iAudioDataCallBack != null) {
                            iAudioDataCallBack.onAudioData(aac, info);
                        }
                        //防止播放过快
                        while (info.presentationTimeUs / 1000 > System.currentTimeMillis() - startMs) {
                            try {
                                sleep(10);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                                break;
                            }
                        }
                        mediaCodec.releaseOutputBuffer(outIndex, true);
                        break;
                }

                // All decoded frames have been rendered, we can stop playing
                // now
                if ((info.flags & MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0) {
                    break;
                }

            } catch (InterruptedException e) {
                e.printStackTrace();
            }

        }

        mediaCodec.stop();
        mediaCodec.release();
        return false;
    }

    private MediaFormat createFormat() {
//        MediaFormat mediaFormat = MediaFormat
//                .createAudioFormat(MediaFormat.MIMETYPE_AUDIO_AAC,
//                        sampleRate,
//                        AudioFormat.CHANNEL_OUT_MONO);

        MediaFormat mediaFormat = MediaFormat.createAudioFormat(
                MediaFormat.MIMETYPE_AUDIO_AAC,
                sampleRate,
                AudioFormat.CHANNEL_OUT_MONO);
        mediaFormat.setInteger(MediaFormat.KEY_AAC_PROFILE, MediaCodecInfo.CodecProfileLevel.AACObjectLC);
        mediaFormat.setInteger(MediaFormat.KEY_BIT_RATE, AudioFormat.ENCODING_PCM_16BIT);
        mediaFormat.setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, 1024 * 8 * 8);
        return mediaFormat;
    }
}