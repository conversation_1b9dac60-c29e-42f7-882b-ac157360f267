package com.dinsafer.module_dscam.player.webrtc.auth;

import com.amazonaws.auth.AWSAbstractCognitoDeveloperIdentityProvider;
import com.amazonaws.regions.Regions;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2022/3/16
 */
@Deprecated
public class DeveloperAuthenticationProvider extends AWSAbstractCognitoDeveloperIdentityProvider {

    private static final String developerProvider = "login.test.app";

    public DeveloperAuthenticationProvider(String accountId, String identityPoolId, Regions region) {
        super(accountId, identityPoolId, region);
        // Initialize any other objects needed here.
    }

    // Return the developer provider name which you choose while setting up the
    // identity pool in the &COG; Console

    @Override
    public String getProviderName() {
        return developerProvider;
    }

    // Use the refresh method to communicate with your backend to get an
    // identityId and token.

    @Override
    public String refresh() {

        // Override the existing token
        setToken("eyJraWQiOiJhcC1zb3V0aGVhc3QtMTEiLCJ0eXAiOiJKV1MiLCJhbGciOiJSUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.BAr053GYg-3jV9YILrb6sZV9Y_qPsYe11hyVOlhcjOGoV1wi6nSwHKDjfM9prlzs8wwkc28nJJwNoiKxGD7M_Io1hbOH7StFMiOd_H8byLaDV_TlP4cpWGH1t8r0dKLZSBqr71GX-Tvo5KixT-H87mCXAnb4wpcSz4Z3HZsw1OcvBJDlXke5V4ANXM33yr5AnJayjs9Px2An9hCW63wnvd_MzxMIyPM5K1Qv6g0qDevx_ZFGUt9CPF6r01gE3nVBI4lSNDMH--uZSVUc3GE2ntNaxInA5RLFnhuFjN7eEPV1GqkSNjRvwJViwmvdNIF4CfdKX65EKKvKdwXsGKfMYg");

        // Get the identityId and token by making a call to your backend
        // (Call to your backend)

        // Call the update method with updated identityId and token to make sure
        // these are ready to be used from Credentials Provider.
        setIdentityId("ap-southeast-1:77917499-3a44-4966-9bf3-3798a134ac94");
        update(identityId, token);
        return token;
    }

    // If the app has a valid identityId return it, otherwise get a valid
    // identityId from your backend.

    @Override
    public String getIdentityId() {
        // Load the identityId from the cache
//        identityId = cachedIdentityId;

        if (identityId == null) {
            // Call to your backend
        } else {
            return identityId;
        }

        return null;
    }

}
