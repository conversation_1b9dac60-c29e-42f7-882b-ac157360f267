package com.dinsafer.module_dscam.player;

import android.content.Context;
import android.graphics.SurfaceTexture;
import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import android.util.AttributeSet;
import android.view.TextureView;

import com.dinsafer.dragview.PhotoTextueView;


public class KRealTimePlayView extends PhotoTextueView implements TextureView.SurfaceTextureListener {
    private boolean isCreate = false;

    public IPlayer mPlayer;
    private SurfaceTexture savedSurfaceTexture;
    private TextureView.SurfaceTextureListener mOutListener;

    @Keep
    public KRealTimePlayView(Context context) {
        super(context);
        this.setSurfaceTextureListener(this);
    }

    @Keep
    public KRealTimePlayView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.setSurfaceTextureListener(this);
    }

    @Keep
    public KRealTimePlayView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.setSurfaceTextureListener(this);
    }

    public SurfaceTextureListener getOutListener() {
        return mOutListener;
    }

    public void setOutListener(SurfaceTextureListener mOutListener) {
        this.mOutListener = mOutListener;
    }

    //    @Override
//    public void surfaceCreated(SurfaceHolder holder) {
//        Log.e("VideoPlayView", "surfaceCreated");
//        isCreate = true;
//        if (mPlayer == null) {
//            return;
//        }
//        mPlayer.bindView(this);
//    }
//
//    @Override
//    public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
//        Log.e("VideoPlayView", "surfaceChanged");
//
//    }
//
//    @Override
//    public void surfaceDestroyed(SurfaceHolder holder) {
//        Log.e("VideoPlayView", "surfaceDestroyed");
//        isCreate = false;
//    }

    public void bindPlayer(IPlayer player) {
        mPlayer = player;
        if (isCreate) {
//            如果view holder已经准备好，直接绑定,如果还没准备好，那么在surfaceCreated绑定
            if (mPlayer.getSurface() != null) {
                this.savedSurfaceTexture = mPlayer.getSurface();
                if (getSurfaceTexture() == savedSurfaceTexture) {
//                    已经是同一个surface了，不能再设置了
                    return;
                }
                setSurfaceTexture(savedSurfaceTexture);
                return;
            }
            mPlayer.saveSurface();
            mPlayer.bindView(this);
        }
    }

    @Override
    public void onSurfaceTextureAvailable(@NonNull SurfaceTexture surface, int width, int height) {
        this.savedSurfaceTexture = surface;
        isCreate = true;
        if (mPlayer == null) {
            if (mOutListener != null) {
                mOutListener.onSurfaceTextureAvailable(surface, width, height);
            }
            return;
        }
        if (mPlayer.getSurface() != null) {
            this.savedSurfaceTexture = mPlayer.getSurface();
            setSurfaceTexture(savedSurfaceTexture);
            if (mOutListener != null) {
                mOutListener.onSurfaceTextureAvailable(savedSurfaceTexture, width, height);
            }
            return;
        }
        mPlayer.saveSurface();
        mPlayer.bindView(this);
        if (mOutListener != null) {
            mOutListener.onSurfaceTextureAvailable(savedSurfaceTexture, width, height);
        }
//        setSurfaceTexture(savedSurfaceTexture);
    }

    @Override
    public void onSurfaceTextureSizeChanged(@NonNull SurfaceTexture surface, int width, int height) {
        if (mOutListener != null) {
            mOutListener.onSurfaceTextureSizeChanged(surface, width, height);
        }
    }

    @Override
    public boolean onSurfaceTextureDestroyed(@NonNull SurfaceTexture surface) {
        if (mOutListener != null) {
            mOutListener.onSurfaceTextureDestroyed(surface);
        }
        return false;
    }

    @Override
    public void onSurfaceTextureUpdated(@NonNull SurfaceTexture surface) {
        if (mOutListener != null) {
            mOutListener.onSurfaceTextureUpdated(surface);
        }
    }

    public SurfaceTexture getSavedSurfaceTexture() {
        return savedSurfaceTexture;
    }

    public void setSavedSurfaceTexture(SurfaceTexture savedSurfaceTexture) {
        this.savedSurfaceTexture = savedSurfaceTexture;
    }

}
