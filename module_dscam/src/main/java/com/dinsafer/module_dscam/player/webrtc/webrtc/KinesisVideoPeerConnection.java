package com.dinsafer.module_dscam.player.webrtc.webrtc;

import android.util.Log;

import org.webrtc.DataChannel;
import org.webrtc.IceCandidate;
import org.webrtc.MediaStream;
import org.webrtc.PeerConnection;
import org.webrtc.RtpReceiver;

public class KinesisVideoPeerConnection implements PeerConnection.Observer {

    private final static String TAG = "webrtc";
    private final static String clazz = "KinesisVideoPeerConnection-->";

    public KinesisVideoPeerConnection() {

    }

    @Override
    public void onSignalingChange(PeerConnection.SignalingState signalingState) {

        Log.v(TAG, clazz + "onSignalingChange(): signalingState = [" + signalingState + "]");

    }

    @Override
    public void onIceConnectionChange(PeerConnection.IceConnectionState iceConnectionState) {

        Log.v(TAG, clazz + "onIceConnectionChange(): iceConnectionState = [" + iceConnectionState + "]");

    }

    @Override
    public void onIceConnectionReceivingChange(boolean connectionChange) {

        Log.v(TAG, clazz + "onIceConnectionReceivingChange(): connectionChange = [" + connectionChange + "]");

    }

    @Override
    public void onIceGatheringChange(PeerConnection.IceGatheringState iceGatheringState) {

        Log.v(TAG, clazz + "onIceGatheringChange(): iceGatheringState = [" + iceGatheringState + "]");

    }

    @Override
    public void onIceCandidate(IceCandidate iceCandidate) {

        Log.v(TAG, clazz + "onIceCandidate(): iceCandidate = [" + iceCandidate + "]");

    }

    @Override
    public void onIceCandidatesRemoved(IceCandidate[] iceCandidates) {

        Log.v(TAG, clazz + "onIceCandidatesRemoved(): iceCandidates Length = [" + iceCandidates.length + "]");

    }

    @Override
    public void onAddStream(MediaStream mediaStream) {

        Log.v(TAG, clazz + "onAddStream(): mediaStream = [" + mediaStream + "]");

    }

    @Override
    public void onRemoveStream(MediaStream mediaStream) {

        Log.v(TAG, clazz + "onRemoveStream(): mediaStream = [" + mediaStream + "]");

    }

    @Override
    public void onDataChannel(DataChannel dataChannel) {

        Log.v(TAG, clazz + "onDataChannel(): dataChannel = [" + dataChannel + "]");

    }

    @Override
    public void onRenegotiationNeeded() {

        Log.v(TAG, clazz + "onRenegotiationNeeded():");

    }

    @Override
    public void onAddTrack(RtpReceiver rtpReceiver, MediaStream[] mediaStreams) {

        Log.v(TAG, clazz + "onAddTrack(): rtpReceiver = [" + rtpReceiver + "], " +
                "mediaStreams Length = [" + mediaStreams.length + "]");

    }
}
