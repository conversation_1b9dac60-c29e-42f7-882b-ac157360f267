package com.dinsafer.module_dscam.player.webrtc;

import android.graphics.Bitmap;
import android.graphics.SurfaceTexture;
import android.view.View;

import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.module_dscam.player.IPlayer;
import com.dinsafer.module_dscam.player.IPlayerStatusListener;
import com.dinsafer.module_dscam.player.record.IRecordCallBack;

import java.util.ArrayList;
import java.util.List;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2022/3/21
 */
class BasePlayer implements IPlayer {
    public static final int STATUS_START = 0;
    public static final int STATUS_PREPARED = 1;
    /**
     * playing,but no data to play
     */
    public static final int STATUS_WAITING_DATA = 2;
    public static final int STATUS_PLAYING = 3;
    public static final int STATUS_PAUSE = 4;
    public static final int STATUS_STOP = 5;
    public static final int STATUS_ERROR = 6;

    protected int currentStatus = STATUS_STOP;
    protected IPlayerStatusListener statuslistener;
    protected List<IDefaultCallBack2<Bitmap>> snapshotCallBack = new ArrayList<>();

    @Override
    public void loadData() {

    }

    @Override
    public void play(IDefaultCallBack callBack) {

    }

    @Override
    public void play(int videoType, IDefaultCallBack callBack) {

    }

    @Override
    public void pausePlay() {

    }

    @Override
    public void resumePlay() {

    }

    @Override
    public void destory() {

    }

    @Override
    public void startListen(IDefaultCallBack callBack) {

    }

    @Override
    public void startTalk(IDefaultCallBack callBack) {

    }

    @Override
    public void stopListen() {

    }

    @Override
    public void stopTalk() {

    }

    @Override
    public boolean isConnect() {
        return false;
    }

    @Override
    public boolean isListening() {
        return false;
    }

    @Override
    public boolean isTalking() {
        return false;
    }

    @Override
    public boolean isPlaying() {
        return false;
    }

    @Override
    public void switchQuality(int type, IDefaultCallBack callBack) {

    }

    @Override
    public void getSnapshot() {

    }

    @Override
    public void registerSnapShotCallBack(IDefaultCallBack2<Bitmap> snapshotCallBack) {
        if (this.snapshotCallBack.contains(snapshotCallBack)) {
            return;
        }

        this.snapshotCallBack.add(snapshotCallBack);

    }

    @Override
    public void unregisterSnapShotCallBack(IDefaultCallBack2<Bitmap> snapshotCallBack) {
        this.snapshotCallBack.remove(snapshotCallBack);

    }


    @Override
    public void setStatusListener(IPlayerStatusListener listener) {
        this.statuslistener = listener;
    }

    @Override
    public IPlayerStatusListener getStatusListener() {
        return this.statuslistener;
    }

    @Override
    public boolean startRecord(String path, IRecordCallBack callBack) {
        return false;
    }

    @Override
    public void stopRecord() {

    }

    @Override
    public void saveSurface() {

    }

    @Override
    public SurfaceTexture getSurface() {
        return null;
    }

    @Override
    public void bindView(View playView) {

    }

    @Override
    public void changeBindView(View playView) {

    }

    protected void switchStatusTo(int dst) {

        if (currentStatus == dst) {
            return;
        }
        synchronized (this) {
            currentStatus = dst;
//        notify status change
            if (statuslistener == null) {
                return;
            }

            if (currentStatus == STATUS_PREPARED) {
                statuslistener.onPrepared();
                return;
            }


            if (currentStatus == STATUS_START) {
                statuslistener.onStarted();
                return;
            }


            if (currentStatus == STATUS_PLAYING) {
                statuslistener.onPlaying();
                return;
            }

            if (currentStatus == STATUS_PAUSE) {
                statuslistener.onPaused();
                return;
            }

            if (currentStatus == STATUS_STOP) {
                statuslistener.onRelease();
                return;
            }

            if (currentStatus == STATUS_WAITING_DATA) {
                statuslistener.onWaiting();
                return;
            }

            if (currentStatus == STATUS_ERROR) {
//                statuslistener.onError();
                return;
            }
        }
    }
}
