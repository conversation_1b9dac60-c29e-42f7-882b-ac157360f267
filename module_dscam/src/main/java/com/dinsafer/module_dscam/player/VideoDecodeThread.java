package com.dinsafer.module_dscam.player;

import android.media.MediaCodec;
import android.media.MediaFormat;
import android.util.Log;
import android.view.Surface;

import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.utils.DDLog;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.concurrent.LinkedBlockingQueue;

public class VideoDecodeThread extends Thread {

    private static final String TAG = VideoDecodeThread.class.getSimpleName();

    /**
     * 用来读取音視频文件 提取器
     */
    private MediaCodec mediaCodec;
    /**
     * 用来解码 解碼器
     */
    private Surface surface;

    private String path;

    private boolean isViewPre;

    private LinkedBlockingQueue packages = new LinkedBlockingQueue<>();
    private boolean isPlaying;
    private boolean isStop;

    public VideoDecodeThread(Surface surface) {
        this.surface = surface;
    }

    public VideoDecodeThread() {
    }

    public VideoDecodeThread(Surface surface, String path) {
        this.surface = surface;
        this.path = path;
    }

    private MediaFormat createHevcFormat() {
        MediaFormat format = MediaFormat.createVideoFormat(MediaFormat.MIMETYPE_VIDEO_HEVC
                , 640, 360);

        format.setString(MediaFormat.KEY_MIME, MediaFormat.MIMETYPE_VIDEO_HEVC);
        format.setInteger(MediaFormat.KEY_WIDTH, 640);
        format.setInteger(MediaFormat.KEY_HEIGHT, 360);
        format.setInteger(MediaFormat.KEY_FRAME_RATE, 25);
        return format;
    }

    private int timeoutCount = 0;

    @Override
    public void run() {
        MediaFormat mediaFormat = createHevcFormat();
        String mimeType = mediaFormat.getString(MediaFormat.KEY_MIME);

        try {
            mediaCodec = MediaCodec.createDecoderByType(mimeType); // 创建解码器,提供数据输出
        } catch (IOException e) {
            e.printStackTrace();
            MsctLog.e(TAG, "创建解码器失败,mimeType:" + mimeType);
            return;
        }
//
//                //用于临时处理 surfaceView还没有create，却调用configure导致崩溃的问题
        while (!isViewPre) {
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        mediaCodec.configure(mediaFormat, surface, null, 0);
        if (mediaCodec == null) {
            Log.e(TAG, "Can't find video info!");
            return;
        }

        mediaCodec.start(); // 启动MediaCodec ，等待传入数据
        // 输入
        ByteBuffer[] inputBuffers = mediaCodec.getInputBuffers(); // 用来存放目标文件的数据
        // 输出
        ByteBuffer[] outputBuffers = mediaCodec.getOutputBuffers(); // 解码后的数据
        MediaCodec.BufferInfo info = new MediaCodec.BufferInfo(); // 用于描述解码得到的byte[]数据的相关信息
        boolean bIsEos = false;
        long startMs = System.currentTimeMillis();

        isPlaying = true;
        isStop = false;

        // ==========开始解码=============
        while (!Thread.interrupted()) {
            try {
                byte[] frame = (byte[]) packages.take();
//                DDLog.i("video", "rec:" + frame.length + " is I key:" + isIKey(frame));

                if (!bIsEos) {
                    int inIndex = mediaCodec.dequeueInputBuffer(20_000);
                    while (inIndex < 0) {
                        timeoutCount++;
                        Log.d(TAG, "dequeueOutputBuffer input! + loop: " + timeoutCount);
                        inIndex = mediaCodec.dequeueInputBuffer(20_000);
                        if (timeoutCount > 200) {
                            Log.w(TAG, "dequeueOutputBuffer input! + flush");
                            mediaCodec.flush();
                        }
                    }
                    if (inIndex >= 0) {
                        timeoutCount = 0;
                        ByteBuffer buffer = inputBuffers[inIndex];
                        buffer.clear();
                        buffer.put(frame);
                        // Log.d(TAG, "dequeueOutputBuffer input! + size: " + packages.size());
                        mediaCodec.queueInputBuffer(inIndex, 0, frame.length, 0, 0);
                    }
                    // Log.d(TAG, "dequeueOutputBuffer input! + inIndex: " + inIndex);
                }

                int outIndex = mediaCodec.dequeueOutputBuffer(info, 0);
                switch (outIndex) {
                    case MediaCodec.INFO_OUTPUT_BUFFERS_CHANGED:
                        Log.d(TAG, "INFO_OUTPUT_BUFFERS_CHANGED");
                        outputBuffers = mediaCodec.getOutputBuffers();
                        break;
                    case MediaCodec.INFO_OUTPUT_FORMAT_CHANGED:
                        Log.d(TAG, "New format " + mediaCodec.getOutputFormat());
                        break;
                    case MediaCodec.INFO_TRY_AGAIN_LATER:
                        Log.d(TAG, "dequeueOutputBuffer timed out!+ size:" + packages.size());
                        break;
                    default:
                        // ByteBuffer buffer = outputBuffers[outIndex];
//                        Log.v(TAG, "We can't use this buffer but render it due to the API limit, " + buffer);

                        //防止视频播放过快
//                        while (info.presentationTimeUs / 1000 > System.currentTimeMillis() - startMs) {
//                        try {
//                            sleep(10);
//                        } catch (InterruptedException e) {
//                            e.printStackTrace();
//                            break;
//                        }
//                        }
                        while (outIndex >= 0) {
                            mediaCodec.releaseOutputBuffer(outIndex, true);
                            outIndex = mediaCodec.dequeueOutputBuffer(info, 10);
                            // Log.d(TAG, "dequeueOutputBuffer success! + size: " + packages.size());
                        }
                        break;
                }

                // All decoded frames have been rendered, we can stop playing
                // now
                if ((info.flags & MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0) {
                    break;
                }

            } catch (Exception e) {
                e.printStackTrace();
            }

        }

        try {
            mediaCodec.stop();
            mediaCodec.release();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Surface getSurface() {
        return surface;
    }

    public void setSurface(Surface surface) {
        this.surface = surface;
    }

    public boolean isPlaying() {
        return isPlaying;
    }

    public boolean isStop() {
        return isStop;
    }


    public void stopVideo() {
        isStop = true;
        if (mediaCodec != null) {
            mediaCodec.stop();
            mediaCodec.release();
        }
        this.interrupt();
    }

    public void feed(byte[] frame) {
        try {
            if (packages.size() > 200) {
                Log.w(TAG, "dequeueOutputBuffer clear");
                packages.clear();
            }
            packages.put(frame);
            // DDLog.i(TAG, "dequeueOutputBuffer put: size: " + packages.size());
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }


    public boolean isViewPre() {
        return isViewPre;
    }

    public void setViewPre(boolean viewPre) {
        isViewPre = viewPre;
    }
}