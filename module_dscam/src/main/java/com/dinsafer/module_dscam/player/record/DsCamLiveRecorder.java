package com.dinsafer.module_dscam.player.record;

import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaFormat;
import android.media.MediaMuxer;
import android.util.Log;

import com.dinsafer.module_dscam.player.CodecUtils;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.Arrays;

import androidx.annotation.NonNull;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2022/1/11
 */
public class DsCamLiveRecorder implements ILiveRecorder {
    private String TAG = "DsCamLiveRecorder";

    private static final int RECORD_STATE_STARTING = 1;
    private static final int RECORD_STATE_STARTED = 2;
    private static final int RECORD_STATE_STOPING = 3;
    private static final int RECORD_STATE_STOPED = 0;

    private int sampleRate = 8000;

    private MediaMuxer mediaMuxer;
    private String outputPath;

    private volatile int audioTrackIndex = -1;
    private volatile int videoTrackIndex = -1;

    private volatile boolean isRecording = false;
    private volatile boolean isWriteKeyFrame = false;
    private volatile boolean isWriteFirstAudioFrame = false;
    private volatile long firstVideoFrameTimeUs = 0;

    private ByteBuffer videoCsd0;
    private IRecordCallBack callBack;

    public DsCamLiveRecorder(String outputPath, IRecordCallBack callBack) {
        this.outputPath = outputPath;
        this.callBack = callBack;
        try {
            mediaMuxer = new MediaMuxer(this.outputPath, MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    private void addVideoTrack() {
        MediaFormat videoFormat = MediaFormat.createVideoFormat(MediaFormat.MIMETYPE_VIDEO_HEVC, 640, 360);
        videoFormat.setString(MediaFormat.KEY_MIME, MediaFormat.MIMETYPE_VIDEO_HEVC);
        videoFormat.setInteger(MediaFormat.KEY_WIDTH, 640);
        videoFormat.setInteger(MediaFormat.KEY_HEIGHT, 360);
        videoFormat.setInteger(MediaFormat.KEY_FRAME_RATE, 25);
        videoFormat.setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420SemiPlanar);
        videoFormat.setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, 640 * 360);
        videoFormat.setInteger("capture-rate", 25);
        videoFormat.setByteBuffer("csd-0", videoCsd0);
        videoTrackIndex = mediaMuxer.addTrack(videoFormat);
        Log.d(TAG, "addVideoTrack: index " + videoTrackIndex + ", csd-0: " + videoCsd0);

    }

    private void addAudioTrack() {
        MediaFormat audioFormat = MediaFormat.createAudioFormat(
                MediaFormat.MIMETYPE_AUDIO_AAC,
                sampleRate,
                1);
        audioFormat.setInteger(MediaFormat.KEY_AAC_PROFILE, MediaCodecInfo.CodecProfileLevel.AACObjectLC);
        audioFormat.setInteger(MediaFormat.KEY_BIT_RATE, 16000);
        audioFormat.setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, 1024 * 8 * 8);
        audioFormat.setInteger(MediaFormat.KEY_IS_ADTS, 1);

        // 计算参考：https://www.jianshu.com/p/5b9c41df8d18
        byte[] data = new byte[]{(byte) 0x15, (byte) 0x88};
        ByteBuffer csd_0 = ByteBuffer.wrap(data);
        //ADT头的解码信息
        audioFormat.setByteBuffer("csd-0", csd_0);

        audioTrackIndex = mediaMuxer.addTrack(audioFormat);
        Log.d(TAG, "addAudioTrack: index " + audioTrackIndex + ", csd-0: " + csd_0);
    }

    @Override
    public void start() {
        isRecording = true;
    }

    public boolean isRecording() {
        return isRecording;
    }

    /**
     * 视频数据是否写入第一个I幁
     *
     * @return
     */
    public boolean isReceivedData() {
        return isWriteKeyFrame;
    }

    @Override
    public void writeAudioData(@NonNull ByteBuffer byteBuf, @NonNull MediaCodec.BufferInfo bufferInfo) {
        if (!isRecording) {
            Log.w(TAG, "writeAudioData: not start record");
            return;
        }
        if (!isWriteKeyFrame) {
            Log.w(TAG, "writeAudioData: waiting video start");
            return;
        }
        internalWriteAudioData(byteBuf, bufferInfo);
        isWriteFirstAudioFrame = true;
    }

    private synchronized void internalWriteAudioData(@NonNull ByteBuffer byteBuf, @NonNull MediaCodec.BufferInfo bufferInfo) {
        bufferInfo.presentationTimeUs = System.currentTimeMillis() * 1000 - firstVideoFrameTimeUs;
        Log.i(TAG, "internalWriteAudioData: " + bufferInfo.size + " /" + bufferInfo.offset + " /" + bufferInfo.flags + " /" + bufferInfo.presentationTimeUs);
        try {
            mediaMuxer.writeSampleData(audioTrackIndex, byteBuf, bufferInfo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void writeVideoData(@NonNull ByteBuffer byteBuf, @NonNull MediaCodec.BufferInfo bufferInfo) {
        if (!isRecording) {
            Log.w(TAG, "writeVideoData: not start record");
            return;
        }

        if (!isWriteKeyFrame) {
            Log.w(TAG, "writeVideoData: wait first key frame");
            if (CodecUtils.isIKey(byteBuf.array())) {
                Log.d(TAG, "writeVideoData: find first key frame");
                byte[] spsPps = CodecUtils.getvps_sps_pps(byteBuf.array(), 0, 256);
                if (spsPps != null) {
                    videoCsd0 = ByteBuffer.wrap(spsPps);
                }
                addVideoTrack();
                addAudioTrack();
                mediaMuxer.start();

                firstVideoFrameTimeUs = System.currentTimeMillis() * 1000;
                internalWriteVideoData(byteBuf, bufferInfo);
                isWriteKeyFrame = true;
                if (!isWriteFirstAudioFrame) {
                    writeAudioEmptyData();
                }
                if (callBack != null) {
                    callBack.onSuccess();
                }
            }
            return;
        }

        internalWriteVideoData(byteBuf, bufferInfo);
    }

    private synchronized void internalWriteVideoData(@NonNull ByteBuffer byteBuf, @NonNull MediaCodec.BufferInfo bufferInfo) {
        if (CodecUtils.isIKey(byteBuf.array())) {
            bufferInfo.flags = MediaCodec.BUFFER_FLAG_KEY_FRAME;
        }
        bufferInfo.size = byteBuf.array().length;
        bufferInfo.offset = 0;
        bufferInfo.presentationTimeUs = System.currentTimeMillis() * 1000 - firstVideoFrameTimeUs;
        Log.i(TAG, "internalWriteVideoData: flags " + bufferInfo.flags + ", presentationTimeUs " + bufferInfo.presentationTimeUs);
        try {
            mediaMuxer.writeSampleData(videoTrackIndex, byteBuf, bufferInfo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void stop() {
        if (!isRecording) {
            return;
        }
        isRecording = false;
        try {
            mediaMuxer.stop();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void release() {
        isRecording = false;
        callBack = null;
        if (null != mediaMuxer) {
            mediaMuxer.release();
            mediaMuxer = null;
        }
    }

    private void writeAudioEmptyData() {
        byte[] emptyData = new byte[960];
        Arrays.fill(emptyData, (byte) 1);
        MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();
        bufferInfo.size = emptyData.length;
        bufferInfo.offset = 0;
        bufferInfo.flags = 0;
        Log.d(TAG, "writeAudioEmptyData: " + bufferInfo.size + " /" + bufferInfo.offset + " /" + bufferInfo.flags + " /" + bufferInfo.presentationTimeUs);
        internalWriteAudioData(ByteBuffer.wrap(emptyData), bufferInfo);
    }
}
