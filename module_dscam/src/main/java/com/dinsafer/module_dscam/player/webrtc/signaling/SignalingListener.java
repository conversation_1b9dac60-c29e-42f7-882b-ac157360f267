package com.dinsafer.module_dscam.player.webrtc.signaling;


import android.util.Base64;
import android.util.Log;

import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.module_dscam.player.webrtc.signaling.model.Event;
import com.google.gson.Gson;

import javax.websocket.MessageHandler;

public abstract class SignalingListener implements Signaling {

    private final static String TAG = "webrtc";
    private final static String clazz = "SignalingListener$MessageHandler-->";
    private final Gson gson = new Gson();
    private final MessageHandler messageHandler = new MessageHandler.Whole<String>() {

        @Override
        public void onMessage(String message) {
            MsctLog.v(TAG, clazz + "onMessage: Received message" + message);
            if (!message.isEmpty() && message.contains("messagePayload")) {
                Event evt = gson.fromJson(message, Event.class);
                if (evt != null && evt.getMessageType() != null && !evt.getMessagePayload().isEmpty()) {
                    if (evt.getMessageType().equalsIgnoreCase("SDP_OFFER")) {
                        MsctLog.d(TAG, clazz + "onMessage: Offer received: SenderClientId=" + evt.getSenderClientId());
                        byte[] decode = Base64.decode(evt.getMessagePayload(), 0);
                        MsctLog.d(TAG, new String(decode));
                        onSdpOffer(evt);
                    }

                    if (evt.getMessageType().equalsIgnoreCase("SDP_ANSWER")) {
                        MsctLog.d(TAG, clazz + "onMessage: Answer received: SenderClientId=" + evt.getSenderClientId());
                        onSdpAnswer(evt);
                    }

                    if (evt.getMessageType().equalsIgnoreCase("ICE_CANDIDATE")) {
                        byte[] decode = Base64.decode(evt.getMessagePayload(), 0);
                        MsctLog.d(TAG, clazz + "onMessage: Ice Candidate received: SenderClientId=" + evt.getSenderClientId() + " decoded msg:" + new String(decode));
                        onIceCandidate(evt);
                    }
                }
            }

        }
    };

    public MessageHandler getMessageHandler() {
        return messageHandler;
    }
}
