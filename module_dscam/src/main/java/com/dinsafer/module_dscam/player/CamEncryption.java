package com.dinsafer.module_dscam.player;

import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class CamEncryption {

    IvParameterSpec iv;
    SecretKeySpec skeySpec;
    Cipher encodeCipher, decodeCipher;


    public CamEncryption(String initVector, String key) {

        try {
            iv = new IvParameterSpec(initVector.getBytes("UTF-8"));
            skeySpec = new SecretKeySpec(key.getBytes("UTF-8"), "AES");
            encodeCipher = Cipher.getInstance("AES/CBC/NoPadding");
            encodeCipher.init(1, skeySpec, iv);

            decodeCipher = Cipher.getInstance("AES/CBC/NoPadding");
            decodeCipher.init(2, skeySpec, iv);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (NoSuchPaddingException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidAlgorithmParameterException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        }

    }

    public CamEncryption(byte[] initVector, byte[] key) {

        try {
            iv = new IvParameterSpec(initVector);
            skeySpec = new SecretKeySpec(key, "AES");
            encodeCipher = Cipher.getInstance("AES/CBC/NoPadding");
            encodeCipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);

            decodeCipher = Cipher.getInstance("AES/CBC/NoPadding");
            decodeCipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);
        } catch (NoSuchPaddingException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (InvalidAlgorithmParameterException e) {
            e.printStackTrace();
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        }

    }


    public byte[] encryptAes(byte[] value) {
        try {
            return encodeCipher.doFinal(value);
        } catch (Exception var6) {
            var6.printStackTrace();
            return null;
        }
    }

    public byte[] decryptAes(byte[] encrypted) {
        try {
            return decodeCipher.doFinal(encrypted);
        } catch (Exception var6) {
            var6.printStackTrace();
            return null;
        }
    }
}
