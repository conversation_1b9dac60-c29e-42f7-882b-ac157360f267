package com.dinsafer.module_dscam.player.webrtc;

import android.content.Context;
import androidx.annotation.Keep;
import android.util.AttributeSet;

import org.webrtc.SurfaceViewRenderer;
import org.webrtc.VideoFrame;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2022/3/24
 */
@Keep
public class CustomSurfaceViewRender extends SurfaceViewRenderer {
    private String TAG = "CustomSurfaceViewRender";

    public CustomSurfaceViewRender(Context context) {
        super(context);
    }

    public CustomSurfaceViewRender(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public void onFirstFrameRendered() {
        super.onFirstFrameRendered();

    }

    @Override
    public void onFrame(VideoFrame frame) {
        super.onFrame(frame);
//        Log.v(TAG, "onFrame: " + frame);
    }
}
