package com.dinsafer.module_dscam.player.webrtc.webrtc;

import com.dinsafer.dssupport.msctlib.MsctLog;

import org.webrtc.SdpObserver;
import org.webrtc.SessionDescription;

public class KinesisVideoSdpObserver implements SdpObserver {

    protected static final String TAG = "webrtc";
    protected static final String clazz = "KinesisVideoSdpObserver-->";

    @Override
    public void onCreateSuccess(SessionDescription sessionDescription) {
        MsctLog.d(TAG, clazz + "onCreateSuccess(): SDP=" + sessionDescription.description);
    }

    @Override
    public void onSetSuccess() {
        MsctLog.d(TAG, clazz + "onSetSuccess(): SDP");
    }

    @Override
    public void onCreateFailure(String error) {
        MsctLog.e(TAG, clazz + "onCreateFailure(): Error=" + error);
    }

    @Override
    public void onSetFailure(String error) {
        MsctLog.e(TAG, clazz + "onSetFailure(): Error=" + error);

    }
}
