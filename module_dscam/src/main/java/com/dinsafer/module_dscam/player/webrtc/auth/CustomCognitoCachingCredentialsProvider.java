package com.dinsafer.module_dscam.player.webrtc.auth;

import android.content.Context;

import com.amazonaws.auth.AWSSessionCredentials;
import com.amazonaws.auth.BasicSessionCredentials;
import com.amazonaws.auth.CognitoCachingCredentialsProvider;
import com.amazonaws.regions.Regions;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2022/3/18
 */
public class CustomCognitoCachingCredentialsProvider extends CognitoCachingCredentialsProvider {
    private AWSSessionCredentials customSessionCredentials;
    private String awsAccessKey = "********************";
    private String awsSecretKey = "Y8Y0132Qp0OEY0KqGVe9E/PJtUCQIx+k2zasEVxC";
//    private String sessionToken = "";
    private String sessionToken = "IQoJb3JpZ2luX2VjEMv//////////wEaDmFwLXNvdXRoZWFzdC0xIkcwRQIhAJk9s/dcQjgo7tgP07U0TLJXoAM+E8ejvVM8wbxjUsytAiAkgJB6G8PgtS+8wYWOcVCNzp7qFlov+1qyDH2BPVzzaSryAwhUEAMaDDg0MDMyMjg2MzEzOSIM/nSF9ZNeTVeiX1NBKs8DhVthpV0tmzbl6RuJYvHApHVZOQyf86U5fEK7/PnqGpZFp4JX+F6NmJsdwnOu5kD/0DBGhmiDKviWE8jCdi42uvlksvOnGXVSgA8hBKQ0uUs8ejOUc6TsHCGsGFaFo1DmkVeYOZQg3CsYsGpeXwF9504LZAkM8sbcoHjJFbtG0oLH2OtxvUwKLkCj/1UJipaRoaZLIglzPy1/sYFIuZyIN+wQDX0lJ+k/Nhui1d88DGYnjxIY7k8F2S5QZItL3K+gqqiSUUGp7kOYFgWM6TzvMG0b70jBCzUCtbct4cwjzbzX7y2OqH3sbBohz7pnPilL3pnfrRd6jz0O740XtOBZoInO22JWsDxIN51IADBnNOTDgbVzY1UwIxLSSY7JN2rUyj1taEPuskPgKrB5iLxhBMuwRqJHJfmLCs98iCWJS0NxsXD7Te5ZqCpUmNsUiAi6Lf5JO0D/4as+qrfD3QU1mwTcm1L6tjQtBw0/zQBqfwM3/snxT8LeVRaFjwUptftLYCSdPJ3O9QvPFS6tOOPUNh0NftHoe2kAWl0nNGglbEkgvV1Hicn3JHRl3J2xCwfi7tH+xBbZFiO9sjGcOA7l+MSI6fs1PORBkZGHjcDvyTCXxe+RBjqEAvxLVnG7w7lQmZJpGS1fLsKvjazUXnMDZZzmNvv9fwRqzs8ryJZ4PdzwYm7NECNZfcK1ttWLYAwapXLArxKiGpqmYs6HQhsOEm2bFJLNh39UymX/DTP64vwsncA2mjAgRTaEZBbcNFb/EAYqMYDKMOQalFTJGF6PtcL8higB6S46DdIWG6VWnC0XeCTExYqPbtTb8enjkPzOuGqNT2VVJsfA05n1dbGPeHA+hQY9NSqPKM+e4kOy9tCJEmQ6yCbCv4NiWkcyiNvNG4w+MTDqJds8ZCfszO44hy/gOU9FBq1+UE9bhXQKYZlNFrHOTPL3GpPiGZr2LuCBTSH3cwOVFY2UnS83";
    public CustomCognitoCachingCredentialsProvider(Context context,
                                                   DeveloperAuthenticationProvider developerProvider, Regions region) {
        super(context, developerProvider, region);
    }

    @Override
    public AWSSessionCredentials getCredentials() {
//        return super.getCredentials();
        if (customSessionCredentials == null) {
            customSessionCredentials = new BasicSessionCredentials(awsAccessKey, awsSecretKey, sessionToken);
        }
        return customSessionCredentials;
    }
}
