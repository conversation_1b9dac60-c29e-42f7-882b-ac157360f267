package com.dinsafer.module_dscam.player;

import com.dinsafer.dssupport.msctlib.MsctLog;

import java.util.Timer;
import java.util.TimerTask;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2022/4/24
 */
class VideoDataMonitor {
    private String TAG = getClass().getSimpleName();
    //10s超时
    private static final long EXPIRED = 10_000;

    private VideoDataTimeOutListener listener;

    private volatile long lastReceiveTime = 0;

    private Timer timer;

    private boolean isStarted = false;

    public VideoDataMonitor() {
        timer = new Timer();
    }

    public void setListener(VideoDataTimeOutListener listener) {
        this.listener = listener;
    }

    public void start() {
        if (isStarted) {
            MsctLog.w(TAG, "start: isStarted!");
            return;
        }
        MsctLog.d(TAG, "start: ");
        isStarted = true;
        lastReceiveTime = System.currentTimeMillis();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (System.currentTimeMillis() - lastReceiveTime >= EXPIRED) {
                    MsctLog.e(TAG, "run: 超时了!!!");
                    update();
                    if (listener != null) {
                        listener.onVideoDataTimeOut();
                    }
                } else {
                    MsctLog.d(TAG, "run: 未超时");
                }

            }
        }, 0, 1000);
    }

    public void update() {
        lastReceiveTime = System.currentTimeMillis();
    }

    public void stop() {
        MsctLog.d(TAG, "stop: ");
        this.listener = null;
        timer.cancel();
        isStarted = false;
    }


    interface VideoDataTimeOutListener {
        void onVideoDataTimeOut();
    }
}
