package com.dinsafer.module_dscam.player.record;

import android.media.MediaCodec;

import androidx.annotation.NonNull;

import java.nio.ByteBuffer;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2022/1/11
 */
interface ILiveRecorder {

    void start();

    void writeAudioData(@NonNull ByteBuffer byteBuf, @NonNull MediaCodec.BufferInfo bufferInfo);

    void writeVideoData(@NonNull ByteBuffer byteBuf, @NonNull MediaCodec.BufferInfo bufferInfo);

    void stop();

    void release();
}
