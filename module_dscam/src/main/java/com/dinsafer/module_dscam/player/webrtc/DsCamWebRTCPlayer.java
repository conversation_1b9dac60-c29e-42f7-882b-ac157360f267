package com.dinsafer.module_dscam.player.webrtc;

import android.content.Context;
import android.graphics.Bitmap;
import android.media.AudioManager;
import androidx.annotation.Keep;
import android.util.Base64;
import android.util.Log;
import android.view.View;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSSessionCredentials;
import com.amazonaws.services.kinesisvideo.model.ChannelRole;
import com.amazonaws.services.kinesisvideo.model.ResourceEndpointListItem;
import com.amazonaws.services.kinesisvideosignaling.model.IceServer;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.module_dscam.player.webrtc.auth.CustomCriedentialProvider;
import com.dinsafer.module_dscam.player.webrtc.signaling.SignalingListener;
import com.dinsafer.module_dscam.player.webrtc.signaling.model.Event;
import com.dinsafer.module_dscam.player.webrtc.signaling.model.Message;
import com.dinsafer.module_dscam.player.webrtc.signaling.tyrus.SignalingServiceWebSocketClient;
import com.dinsafer.module_dscam.player.webrtc.utils.AwsV4Signer;
import com.dinsafer.module_dscam.player.webrtc.webrtc.KinesisVideoPeerConnection;
import com.dinsafer.module_dscam.player.webrtc.webrtc.KinesisVideoSdpObserver;

import org.webrtc.AudioSource;
import org.webrtc.AudioTrack;
import org.webrtc.DataChannel;
import org.webrtc.DefaultVideoDecoderFactory;
import org.webrtc.DefaultVideoEncoderFactory;
import org.webrtc.EglBase;
import org.webrtc.EglRenderer;
import org.webrtc.IceCandidate;
import org.webrtc.MediaConstraints;
import org.webrtc.MediaStream;
import org.webrtc.PeerConnection;
import org.webrtc.PeerConnectionFactory;
import org.webrtc.RTCStats;
import org.webrtc.RTCStatsCollectorCallback;
import org.webrtc.RTCStatsReport;
import org.webrtc.SessionDescription;
import org.webrtc.SurfaceViewRenderer;
import org.webrtc.VideoCapturer;
import org.webrtc.VideoTrack;

import java.net.URI;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.Set;
import java.util.concurrent.Executors;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2022/3/21
 */
@Keep
public class DsCamWebRTCPlayer extends BasePlayer {
    private String TAG = "webrtc";
    private String clazz = "DsCamWebRTCPlayer-->";

    private Context context;
    private static final boolean ENABLE_INTEL_VP8_ENCODER = true;
    private static final boolean ENABLE_H264_HIGH_PROFILE = true;
    private static final String LOCAL_MEDIA_STREAM_LABEL = "KvsLocalMediaStream";

    private boolean master = false;

    private String channelName = "test-ipc-signaling-channel222";
    //    private String channelName = "test-ipc-signaling-channel";
    private String mClientId;
    private String mRegion = "ap-southeast-1";

    ArrayList<String> mUserNames = null;
    ArrayList<String> mPasswords = null;
    ArrayList<Integer> mTTLs = null;
    ArrayList<List<String>> mUrisList = null;
    private final List<PeerConnection.IceServer> peerIceServers = new ArrayList<>();
    private String mWssEndpoint;

    private EglBase rootEglBase = null;
    private PeerConnectionFactory peerConnectionFactory;
    private VideoCapturer videoCapturer;

    //本地音频流
    private static final String AudioTrackID = "KvsAudioTrack";
    private AudioTrack localAudioTrack;
    private AudioTrack remoteAudioTrack;
    private AudioManager audioManager;
    private int originalAudioMode;
    private boolean originalSpeakerphoneOn;

    private AWSCredentials mCreds = null;
    private PeerConnection localPeer;
    private String recipientClientId;

    private static volatile SignalingServiceWebSocketClient client;
    private HashMap<String, PeerConnection> peerConnectionFoundMap = new HashMap<String, PeerConnection>();
    private HashMap<String, Queue<IceCandidate>> pendingIceCandidatesMap = new HashMap<String, Queue<IceCandidate>>();

    private DataChannel localDataChannel;
    private String mChannelArn;

    private SurfaceViewRenderer playView;

    private Device device;

    private boolean isTalking = false;
    private boolean isListening = false;

    private byte[] snapkcpLock = new byte[0];

    @Override
    public void bindView(View playView) {
        super.bindView(playView);
        if (playView instanceof SurfaceViewRenderer) {
            this.playView = (SurfaceViewRenderer) playView;
        }
    }

    @Override
    public void changeBindView(View playView) {
        super.changeBindView(playView);
        if (playView instanceof SurfaceViewRenderer) {
            this.playView = (SurfaceViewRenderer) playView;
        }
    }

    @Override
    public void loadData() {
        super.loadData();
        KVSClientManager.init(context);
        audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        originalAudioMode = audioManager.getMode();
        originalSpeakerphoneOn = audioManager.isSpeakerphoneOn();
        switchStatusTo(STATUS_START);
        switchStatusTo(STATUS_PREPARED);
    }

    @Override
    public void play(IDefaultCallBack callBack) {
        this.play(0, callBack);
    }

    @Override
    public void play(int videoType, IDefaultCallBack callBack) {
        super.play(videoType, callBack);
        KVSClientManager.getInstance().updateSignalingChannelInfo(mRegion, channelName, ChannelRole.VIEWER, new IDefaultCallBack() {
            @Override
            public void onSuccess() {
                initParms();
                initPeerConnectionFactory();
                initAudioCapturer();
                initSurfaceView();
                initWsConnection();
                switchStatusTo(STATUS_WAITING_DATA);
                if (callBack != null) {
                    callBack.onSuccess();
                }
            }

            @Override
            public void onError(int i, String s) {
                switchStatusTo(STATUS_STOP);
                if (callBack != null) {
                    callBack.onError(i, s);
                }
            }
        });
    }

    @Override
    public void pausePlay() {
//        TODO send stop play command,no need to stop decoder thread and clear decoder buff,
        switchStatusTo(STATUS_PAUSE);
    }

    @Override
    public void resumePlay() {
//        TODO send play command
        switchStatusTo(STATUS_WAITING_DATA);
    }

    @Override
    public void startTalk(IDefaultCallBack callBack) {
        super.startTalk(callBack);
        if (localAudioTrack != null) {
            localAudioTrack.setEnabled(true);
            isTalking = true;
            callBack.onSuccess();
        } else {
            isTalking = false;
            callBack.onError(0, "localAudioTrack is null");
        }
    }

    @Override
    public void stopTalk() {
        super.stopTalk();
        if (localAudioTrack != null) {
            localAudioTrack.setEnabled(false);
        }
        isTalking = false;
    }

    @Override
    public void startListen(IDefaultCallBack callBack) {
        super.startListen(callBack);
        if (remoteAudioTrack != null) {
            remoteAudioTrack.setEnabled(true);
            remoteAudioTrack.setVolume(10.0f);
            audioManager.setMode(AudioManager.MODE_NORMAL);
            audioManager.setSpeakerphoneOn(true);
            isListening = true;
            callBack.onSuccess();
        } else {
            isListening = false;
            callBack.onError(0, "remoteAudioTrack is null");
        }
    }

    @Override
    public void stopListen() {
        super.stopListen();
        if (remoteAudioTrack != null) {
            remoteAudioTrack.setEnabled(false);
            remoteAudioTrack.setVolume(10.0f);
            audioManager.setMode(AudioManager.MODE_NORMAL);
            audioManager.setSpeakerphoneOn(false);
        }
        isListening = false;
    }

    @Override
    public boolean isConnect() {
        return super.isConnect();
    }

    @Override
    public boolean isPlaying() {
        synchronized (this) {
            return currentStatus == STATUS_PLAYING || currentStatus == STATUS_WAITING_DATA;
        }
    }

    @Override
    public boolean isTalking() {
        return isTalking;
    }

    @Override
    public boolean isListening() {
        return isListening;
    }

    @Override
    public void getSnapshot() {
        synchronized (snapkcpLock) {
            EglRenderer.FrameListener listener = null;
            EglRenderer.FrameListener finalListener = listener;
            listener = new EglRenderer.FrameListener() {
                @Override
                public void onFrame(Bitmap bitmap) {
                    playView.getHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            playView.removeFrameListener(finalListener);
                        }
                    });
                    if (bitmap != null) {
                        MsctLog.i(TAG, "get snap by network success");
                        for (IDefaultCallBack2<Bitmap> bitmapIDefaultCallBack2 : snapshotCallBack) {
                            bitmapIDefaultCallBack2.onSuccess(bitmap);
                        }
                    } else {
                        MsctLog.i(TAG, "get snap by network fail");
                        for (IDefaultCallBack2<Bitmap> bitmapIDefaultCallBack2 : snapshotCallBack) {
                            bitmapIDefaultCallBack2.onError(0, "network snap is null");
                        }
                    }
                }
            };

            playView.addFrameListener(listener, 1);
        }
    }


    private void initParms() {
        mUserNames = new ArrayList<>();
        mPasswords = new ArrayList<>();
        mTTLs = new ArrayList<>();
        mUrisList = new ArrayList<>();

        List<IceServer> mIceServerList = KVSClientManager.getInstance().getIceServerList();
        if (mIceServerList != null && mIceServerList.size() > 0) {
            for (IceServer iceServer : mIceServerList) {
                mUserNames.add(iceServer.getUsername());
                mPasswords.add(iceServer.getPassword());
                mTTLs.add(iceServer.getTtl());
                mUrisList.add(iceServer.getUris());
            }
        }

        List<ResourceEndpointListItem> mEndpointList = KVSClientManager.getInstance().getEndpointList();
        if (mEndpointList != null) {
            for (ResourceEndpointListItem endpoint : mEndpointList) {
                if (endpoint.getProtocol().equals("WSS")) {
                    mWssEndpoint = endpoint.getResourceEndpoint();
                }
            }
        }

        mChannelArn = KVSClientManager.getInstance().getChannelArn();

        //sturn服务
        PeerConnection.IceServer stun = PeerConnection
                .IceServer
                .builder(String.format("stun:stun.kinesisvideo.%s.amazonaws.com:443", mRegion))
                .createIceServer();
        peerIceServers.add(stun);

        //turn服务
        if (mUrisList != null) {
            for (int i = 0; i < mUrisList.size(); i++) {
                String turnServer = mUrisList.get(i).toString();
                PeerConnection.IceServer iceServer = PeerConnection.IceServer.builder(turnServer.replace("[", "").replace("]", ""))
                        .setUsername(mUserNames.get(i))
                        .setPassword(mPasswords.get(i))
                        .createIceServer();
                MsctLog.d(TAG, clazz + "initParms: add IceServer details (TURN) = " + iceServer.toString());
                peerIceServers.add(iceServer);
            }
        }

    }

    private void initPeerConnectionFactory() {
        rootEglBase = EglBase.create();

        PeerConnectionFactory.initialize(PeerConnectionFactory
                .InitializationOptions
                .builder(context)
                .createInitializationOptions());

        peerConnectionFactory =
                PeerConnectionFactory.builder()
                        .setVideoDecoderFactory(new DefaultVideoDecoderFactory(rootEglBase.getEglBaseContext()))
                        .setVideoEncoderFactory(new DefaultVideoEncoderFactory(rootEglBase.getEglBaseContext(), ENABLE_INTEL_VP8_ENCODER, ENABLE_H264_HIGH_PROFILE))
                        .createPeerConnectionFactory();
    }

    private void initAudioCapturer() {
        AudioSource audioSource = peerConnectionFactory.createAudioSource(new MediaConstraints());
        localAudioTrack = peerConnectionFactory.createAudioTrack(AudioTrackID, audioSource);
    }

    private void initSurfaceView() {
        if (this.playView != null) {
            this.playView.init(rootEglBase.getEglBaseContext(), null);
        }
    }

    private void initWsConnection() {

        final String masterEndpoint = mWssEndpoint + "?X-Amz-ChannelARN=" + mChannelArn;
        final String viewerEndpoint = mWssEndpoint + "?X-Amz-ChannelARN=" + mChannelArn + "&X-Amz-ClientId=" + mClientId;
        mCreds = CustomCriedentialProvider.getCredentialProvider(context).getCredentials();
        URI signedUri = getSignedUri(masterEndpoint, viewerEndpoint);

//        if (master) {
//            createLocalPeerConnection();
//        }

        final String wsHost = signedUri.toString();
        if (wsHost != null) {
            try {
                client = new SignalingServiceWebSocketClient(wsHost, signalingListener, Executors.newFixedThreadPool(10));
                MsctLog.d(TAG, clazz + "initWsConnection: Client connection " + (client.isOpen() ? "Successful" : "Failed"));
            } catch (Exception e) {
                e.printStackTrace();
//                gotException = true;
            }
            if (isValidClient()) {
                MsctLog.d(TAG, clazz + "initWsConnection: Client connected to Signaling service " + client.isOpen());
                if (!master) {
                    MsctLog.d(TAG, clazz + "initWsConnection: Signaling service is connected: " +
                            "Sending offer as viewer to remote peer"); // Viewer
                    createSdpOffer();
                }
            } else {
                MsctLog.e(TAG, clazz + "initWsConnection: Error in connecting to signaling service");
//                gotException = true;
            }
        }
    }

    private boolean isValidClient() {
        return client != null && client.isOpen();
    }

    final SignalingListener signalingListener = new SignalingListener() {
        private String TAG = getClass().getSimpleName();

        @Override
        public void onSdpOffer(final Event offerEvent) {
            MsctLog.d(TAG, clazz + "SignalingListener onSdpOffer: Received SDP Offer: Setting Remote Description ");
            final String sdp = Event.parseOfferEvent(offerEvent);
            localPeer.setRemoteDescription(new KinesisVideoSdpObserver(),
                    new SessionDescription(SessionDescription.Type.OFFER, sdp));
            recipientClientId = offerEvent.getSenderClientId();
            MsctLog.d(TAG, clazz + "SignalingListener: Received SDP offer for client ID: " + recipientClientId + ".Creating answer");
            //master need
//                createSdpAnswer();
        }

        @Override
        public void onSdpAnswer(final Event answerEvent) {
            MsctLog.d(TAG, clazz + "SignalingListener onSdpAnswer: SDP answer received from signaling");
            final String sdp = Event.parseSdpEvent(answerEvent);
            final SessionDescription sdpAnswer = new SessionDescription(SessionDescription.Type.ANSWER, sdp);
            localPeer.setRemoteDescription(new KinesisVideoSdpObserver(), sdpAnswer);
            MsctLog.d(TAG, clazz + "SignalingListener: Answer Client ID: " + answerEvent.getSenderClientId());
            peerConnectionFoundMap.put(answerEvent.getSenderClientId(), localPeer);
            // Check if ICE candidates are available in the queue and add the candidate
            handlePendingIceCandidates(answerEvent.getSenderClientId());

        }

        @Override
        public void onIceCandidate(Event message) {
            MsctLog.d(TAG, clazz + "SignalingListener onIceCandidate: Received IceCandidate from remote ");
            final IceCandidate iceCandidate = Event.parseIceCandidate(message);
            if (iceCandidate != null) {
                checkAndAddIceCandidate(message, iceCandidate);
            } else {
                MsctLog.e(TAG, clazz + "SignalingListener: Invalid Ice candidate");
            }
        }

        @Override
        public void onError(Event errorMessage) {
            MsctLog.e(TAG, clazz + "SignalingListener onError: Received error message" + errorMessage);

        }

        @Override
        public void onException(Exception e) {
            MsctLog.e(TAG, clazz + "SignalingListener onException: Signaling client returned exception " + e.getMessage());
//                gotException = true;
        }
    };

    private URI getSignedUri(String masterEndpoint, String viewerEndpoint) {
        URI signedUri;

        if (master) {
            signedUri = AwsV4Signer.sign(URI.create(masterEndpoint), mCreds.getAWSAccessKeyId(),
                    mCreds.getAWSSecretKey(), mCreds instanceof AWSSessionCredentials ? ((AWSSessionCredentials) mCreds).getSessionToken() : "", URI.create(mWssEndpoint), mRegion);
        } else {
            signedUri = AwsV4Signer.sign(URI.create(viewerEndpoint), mCreds.getAWSAccessKeyId(),
                    mCreds.getAWSSecretKey(), mCreds instanceof AWSSessionCredentials ? ((AWSSessionCredentials) mCreds).getSessionToken() : "", URI.create(mWssEndpoint), mRegion);
        }
        return signedUri;
    }

    // when mobile sdk is viewer
    private void createSdpOffer() {
        MediaConstraints sdpMediaConstraints = new MediaConstraints();
        sdpMediaConstraints.mandatory.add(new MediaConstraints.KeyValuePair("OfferToReceiveVideo", "true"));
        sdpMediaConstraints.mandatory.add(new MediaConstraints.KeyValuePair("OfferToReceiveAudio", "true"));

        if (localPeer == null) {
            Log.w(TAG, clazz + "createSdpOffer: localPeer is null,start create.");
            createLocalPeerConnection();
        }

        localPeer.createOffer(new KinesisVideoSdpObserver() {

            @Override
            public void onCreateSuccess(SessionDescription sessionDescription) {
                super.onCreateSuccess(sessionDescription);
                localPeer.setLocalDescription(new KinesisVideoSdpObserver(), sessionDescription);
                Message sdpOfferMessage = Message.createOfferMessage(sessionDescription, mClientId);
                if (isValidClient()) {
                    client.sendSdpOffer(sdpOfferMessage);
                } else {
//                    notifySignalingConnectionFailed();
                }
            }
        }, sdpMediaConstraints);
    }

    private Message createIceCandidateMessage(IceCandidate iceCandidate) {
        String sdpMid = iceCandidate.sdpMid;
        int sdpMLineIndex = iceCandidate.sdpMLineIndex;
        String sdp = iceCandidate.sdp;

        String messagePayload =
                "{\"candidate\":\""
                        + sdp
                        + "\",\"sdpMid\":\""
                        + sdpMid
                        + "\",\"sdpMLineIndex\":"
                        + sdpMLineIndex
                        + "}";

        String senderClientId = (master) ? "" : mClientId;

        return new Message("ICE_CANDIDATE", recipientClientId, senderClientId,
                new String(Base64.encode(messagePayload.getBytes(),
                        Base64.URL_SAFE | Base64.NO_PADDING | Base64.NO_WRAP)));
    }


    private void createLocalPeerConnection() {
        Log.d(TAG, clazz + "createLocalPeerConnection: ");
        PeerConnection.RTCConfiguration rtcConfig = new PeerConnection.RTCConfiguration(peerIceServers);
        rtcConfig.bundlePolicy = PeerConnection.BundlePolicy.MAXBUNDLE;
        rtcConfig.sdpSemantics = PeerConnection.SdpSemantics.UNIFIED_PLAN;
        rtcConfig.continualGatheringPolicy = PeerConnection.ContinualGatheringPolicy.GATHER_CONTINUALLY;
        rtcConfig.keyType = PeerConnection.KeyType.ECDSA;
        rtcConfig.rtcpMuxPolicy = PeerConnection.RtcpMuxPolicy.REQUIRE;
        rtcConfig.tcpCandidatePolicy = PeerConnection.TcpCandidatePolicy.ENABLED;

        localPeer = peerConnectionFactory.createPeerConnection(rtcConfig, new KinesisVideoPeerConnection() {

            @Override
            public void onIceCandidate(IceCandidate iceCandidate) {
                super.onIceCandidate(iceCandidate);

                Message message = createIceCandidateMessage(iceCandidate);
                MsctLog.d(TAG, clazz + "createLocalPeerConnection onIceCandidate: Sending IceCandidate to remote peer " + iceCandidate.toString());
                client.sendIceCandidate(message);  /* Send to Peer */

            }

            @Override
            public void onIceConnectionChange(PeerConnection.IceConnectionState iceConnectionState) {
                super.onIceConnectionChange(iceConnectionState);
                if (statuslistener == null) {
                    return;
                }
                if (PeerConnection.IceConnectionState.CONNECTED == iceConnectionState) {
                    switchStatusTo(STATUS_PLAYING);
                } else if (PeerConnection.IceConnectionState.FAILED == iceConnectionState
                        || PeerConnection.IceConnectionState.DISCONNECTED == iceConnectionState
                        || PeerConnection.IceConnectionState.CLOSED == iceConnectionState) {
                    switchStatusTo(STATUS_ERROR);
                    statuslistener.onError(0, "IceConnection error:" + iceConnectionState);
                }
            }

            @Override
            public void onAddStream(MediaStream mediaStream) {
                super.onAddStream(mediaStream);

                MsctLog.d(TAG, clazz + "createLocalPeerConnection onAddStream: Adding remote video stream (and audio) to the view");
                addRemoteStreamToVideoView(mediaStream);
            }

            @Override
            public void onDataChannel(DataChannel dataChannel) {
                super.onDataChannel(dataChannel);

                dataChannel.registerObserver(new DataChannel.Observer() {
                    @Override
                    public void onBufferedAmountChange(long l) {
                        // no op on receiver side
                    }

                    @Override
                    public void onStateChange() {
                        MsctLog.d(TAG, clazz + "createLocalPeerConnection DataChannel.Observer onStateChange: Remote Data Channel onStateChange: state: " + dataChannel.state().toString());
                    }

                    @Override
                    public void onMessage(DataChannel.Buffer buffer) {
                        byte[] bytes;
                        if (buffer.data.hasArray()) {
                            bytes = buffer.data.array();
                        } else {
                            bytes = new byte[buffer.data.remaining()];
                            buffer.data.get(bytes);
                        }

                        MsctLog.d(TAG, clazz + "createLocalPeerConnection DataChannel.Observer onMessage: New message from peer, check notification." + new String(bytes, Charset.defaultCharset()));
                    }
                });
            }
        });

        if (localPeer != null) {
            localPeer.getStats(new RTCStatsCollectorCallback() {
                @Override
                public void onStatsDelivered(RTCStatsReport rtcStatsReport) {
                    Map<String, RTCStats> statsMap = rtcStatsReport.getStatsMap();
                    Set<Map.Entry<String, RTCStats>> entries = statsMap.entrySet();
                    for (Map.Entry<String, RTCStats> entry : entries) {
                        MsctLog.d(TAG, clazz + "createLocalPeerConnection RTCStatsCollectorCallback onStatsDelivered: Stats: " + entry.getKey() + " ," + entry.getValue());
                    }
                }
            });
        }

        addDataChannelToLocalPeer();
        //master need
        addStreamToLocalPeer();
    }

    private void addDataChannelToLocalPeer() {
        MsctLog.d(TAG, clazz + "addDataChannelToLocalPeer");
        localDataChannel = localPeer.createDataChannel("data-channel-of-" + mClientId, new DataChannel.Init());
        localDataChannel.registerObserver(new DataChannel.Observer() {

            @Override
            public void onBufferedAmountChange(long l) {
                MsctLog.d(TAG, clazz + "addDataChannelToLocalPeer DataChannel.Observer onBufferedAmountChange: Local Data Channel onBufferedAmountChange called with amount " + l);
            }

            @Override
            public void onStateChange() {
                MsctLog.d(TAG, clazz + "addDataChannelToLocalPeer DataChannel.Observer onStateChange: Local Data Channel onStateChange: state: " + localDataChannel.state().toString());

//                if (mBinding.sendDataChannelText != null) {
//                    runOnUiThread(() -> {
//                        if (localDataChannel.state() == DataChannel.State.OPEN) {
//                            mBinding.sendDataChannelText.setEnabled(true);
//                        } else {
//                            mBinding.sendDataChannelText.setEnabled(false);
//                        }
//                    });
//                }
            }

            @Override
            public void onMessage(DataChannel.Buffer buffer) {
                // Send out data, no op on sender side
            }
        });

    }

    private void addStreamToLocalPeer() {

        MediaStream stream = peerConnectionFactory.createLocalMediaStream(LOCAL_MEDIA_STREAM_LABEL);
//
//        if (!stream.addTrack(localVideoTrack)) {
//
//            MsctLog.e(TAG, "Add video track failed");
//        }
//
//        localPeer.addTrack(stream.videoTracks.get(0), Collections.singletonList(stream.getId()));

//        if (isAudioSent) {
        if (!stream.addTrack(localAudioTrack)) {
            MsctLog.e(TAG, clazz + "addStreamToLocalPeer: Add audio track failed");
        }

        if (stream.audioTracks.size() > 0) {
            localPeer.addTrack(stream.audioTracks.get(0), Collections.singletonList(stream.getId()));
            MsctLog.d(TAG, clazz + "addStreamToLocalPeer: Sending audio track ");
        }
        localAudioTrack.setEnabled(false);

//        }

    }

    private void handlePendingIceCandidates(String clientId) {
        // Add any pending ICE candidates from the queue for the client ID
        MsctLog.d(TAG, clazz + "handlePendingIceCandidates: Pending ice candidates found? " + pendingIceCandidatesMap.get(clientId));
        Queue<IceCandidate> pendingIceCandidatesQueueByClientId = pendingIceCandidatesMap.get(clientId);
        while (pendingIceCandidatesQueueByClientId != null && !pendingIceCandidatesQueueByClientId.isEmpty()) {
            final IceCandidate iceCandidate = pendingIceCandidatesQueueByClientId.peek();
            final PeerConnection peer = peerConnectionFoundMap.get(clientId);
            final boolean addIce = peer.addIceCandidate(iceCandidate);
            MsctLog.d(TAG, clazz + "handlePendingIceCandidates: Added ice candidate after SDP exchange " + iceCandidate + " " + (addIce ? "Successfully" : "Failed"));
            pendingIceCandidatesQueueByClientId.remove();
        }
        // After sending pending ICE candidates, the client ID's peer connection need not be tracked
        pendingIceCandidatesMap.remove(clientId);
    }

    private void checkAndAddIceCandidate(Event message, IceCandidate iceCandidate) {
        // if answer/offer is not received, it means peer connection is not found. Hold the received ICE candidates in the map.

        if (!peerConnectionFoundMap.containsKey(message.getSenderClientId())) {
            MsctLog.d(TAG, clazz + "checkAndAddIceCandidate: SDP exchange is not complete. Ice candidate " + iceCandidate + " + added to pending queue");

            // If the entry for the client ID already exists (in case of subsequent ICE candidates), update the queue
            if (pendingIceCandidatesMap.containsKey(message.getSenderClientId())) {
                Queue<IceCandidate> pendingIceCandidatesQueueByClientId = pendingIceCandidatesMap.get(message.getSenderClientId());
                pendingIceCandidatesQueueByClientId.add(iceCandidate);
                pendingIceCandidatesMap.put(message.getSenderClientId(), pendingIceCandidatesQueueByClientId);
            }

            // If the first ICE candidate before peer connection is received, add entry to map and ICE candidate to a queue
            else {
                Queue<IceCandidate> pendingIceCandidatesQueueByClientId = new LinkedList<>();
                pendingIceCandidatesQueueByClientId.add(iceCandidate);
                pendingIceCandidatesMap.put(message.getSenderClientId(), pendingIceCandidatesQueueByClientId);
            }
        }

        // This is the case where peer connection is established and ICE candidates are received for the established
        // connection
        else {
            MsctLog.d(TAG, clazz + "checkAndAddIceCandidate: Peer connection found already");
            // Remote sent us ICE candidates, add to local peer connection
            final PeerConnection peer = peerConnectionFoundMap.get(message.getSenderClientId());
            final boolean addIce = peer.addIceCandidate(iceCandidate);

            MsctLog.d(TAG, clazz + "checkAndAddIceCandidate: Added ice candidate " + iceCandidate + " " + (addIce ? "Successfully" : "Failed"));
        }
    }

    private void addRemoteStreamToVideoView(MediaStream stream) {
        MsctLog.d(TAG, clazz + "addRemoteStreamToVideoView: " + stream.toString());
        final VideoTrack remoteVideoTrack = stream.videoTracks != null && stream.videoTracks.size() > 0 ? stream.videoTracks.get(0) : null;
        remoteAudioTrack = stream.audioTracks != null && stream.audioTracks.size() > 0 ? stream.audioTracks.get(0) : null;

        //音频
        if (remoteAudioTrack != null) {
            //TODO 这里设为false的话speak会失效
//            remoteAudioTrack.setEnabled(true);
            MsctLog.d(TAG, clazz + "remoteAudioTrack received: State=" + remoteAudioTrack.state().name());
        }

        //视频
        if (remoteVideoTrack != null) {
//            runOnUiThread(new Runnable() {
//                @Override
//                public void run() {
//                    try {
//                        MsctLog.d(TAG, "remoteVideoTrackId=" + remoteVideoTrack.id() + " videoTrackState=" + remoteVideoTrack.state());
////                        resizeLocalView();
//                        remoteVideoTrack.addSink(mBinding.remoteView);
////                        resizeRemoteView();
//                    } catch (Exception e) {
//                        MsctLog.e(TAG, "Error in setting remote video view" + e);
//                    }
//                }
//            });
            remoteVideoTrack.addSink(playView);
        } else {
            MsctLog.e(TAG, clazz + "Error in setting remote track");
        }

    }

    @Override
    public void destory() {
        switchStatusTo(STATUS_STOP);
        stopTalk();
        stopListen();
        Thread.setDefaultUncaughtExceptionHandler(null);
        audioManager.setMode(originalAudioMode);
        audioManager.setSpeakerphoneOn(originalSpeakerphoneOn);

        if (rootEglBase != null) {
            rootEglBase.release();
            rootEglBase = null;
        }

        if (localPeer != null) {
            localPeer.dispose();
            localPeer = null;
        }

//        if (videoSource != null) {
//            videoSource.dispose();
//            videoSource = null;
//        }

        if (videoCapturer != null) {
            try {
                videoCapturer.stopCapture();
            } catch (InterruptedException e) {
                MsctLog.e(TAG, "Failed to stop webrtc video capture:" + e.getLocalizedMessage());
            }
            videoCapturer = null;
        }

//        if (localView != null) {
//            localView.release();
//            localView = null;
//        }

        if (client != null) {
            client.disconnect();
            client = null;
        }
        peerConnectionFoundMap.clear();
        pendingIceCandidatesMap.clear();

        super.destory();
    }

    @Keep
    public static final class Builder {
        private Context context;
        private String channelName;
        private Device device;

        private Builder() {
        }

        public static Builder newPlayer() {
            return new Builder();
        }

        public Builder withContext(Context context) {
            this.context = context;
            return this;
        }

        public Builder withChannelName(String channelName) {
            this.channelName = channelName;
            return this;
        }

        public Builder withDevice(Device device) {
            this.device = device;
            return this;
        }

        public DsCamWebRTCPlayer build() {
            DsCamWebRTCPlayer dsCamWebRTCPlayer = new DsCamWebRTCPlayer();
            dsCamWebRTCPlayer.channelName = this.channelName;
            dsCamWebRTCPlayer.context = this.context;
            dsCamWebRTCPlayer.device = this.device;
            return dsCamWebRTCPlayer;
        }
    }
}
