package com.dinsafer.module_dscam.player;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.SurfaceTexture;
import androidx.annotation.Keep;
import android.util.Log;
import android.view.Surface;
import android.view.View;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.msctlib.RandomStringUtils;
import com.dinsafer.dssupport.msctlib.kcp.IKcpCallBack;
import com.dinsafer.dssupport.msctlib.kcp.IKcpCreateCallBack;
import com.dinsafer.dssupport.msctlib.kcp.KcpClientImpl;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_dscam.bean.BaseCamDevice;
import com.dinsafer.module_dscam.player.record.IRecordCallBack;
import com.dinsafer.module_dscam.record.RecordListener;
import com.dinsafer.module_dscam.record.google.AudioStream;
import com.dinsafer.module_dscam.utils.AuthUtils;

import java.util.ArrayList;
import java.util.List;

import static com.dinsafer.dssupport.msctlib.kcp.KcpClientImpl.TYPE_SNAPSHOT;


public class DsCamPlayer implements IPlayer, RecordListener {


    private KRealTimePlayView kRealTimePlayView;

    public static final int STATUS_START = 0;
    public static final int STATUS_PREPARED = 1;
    /**
     * playing,but no data to play
     */
    public static final int STATUS_WAITING_DATA = 2;
    public static final int STATUS_PLAYING = 3;
    public static final int STATUS_PAUSE = 4;
    public static final int STATUS_STOP = 5;

    @Keep
    public static final int STD_TYPE = 0;

    @Keep
    public static final int HD_TYPE = 1;

    private int currentStatus = STATUS_STOP;


    private IPlayerStatusListener statuslistener;

    private List<IDefaultCallBack2<Bitmap>> snapshotCallBack = new ArrayList<>();

    KRealTimePlayer kRealTimePlayer;
    private KcpClientImpl stdVideoKcp;
    private KcpClientImpl hdVideoKcp;
    private KcpClientImpl audioKcp;

    private KcpClientImpl talkKcp;

    private KcpClientImpl snapshotClient;

    //    是否已经给播放器喂过关键帧，如果
    private boolean ishasFeedKeyFrame = false;

    //    是否强制第一个位I帧，true:如果不是I帧，则不丢进去播放器
    private boolean isFirstKeyFrame = false;

    private boolean isListener = false;

    private static final String TAG = DsCamPlayer.class.getSimpleName();

    private AudioStream audioStream;

    private CamEncryption camEncryption;

    private BaseCamDevice dsCamDevice;

    private SurfaceTexture mSurfaceTexture;

    private int stdVideoSessionID, hdVideoSessionID, audioSessionID, talkSessionID, snapShotSessionID;
    private byte[] snapkcpLock = new byte[0];
    private Context context;

    //视频流数据监控,长时间收不到视频数据时重新创建kcp
    private VideoDataMonitor videoDataMonitor;

    private int currentVideoType;

    private DsCamPlayer(Builder builder) throws Exception {
        if (builder.device == null) {
            throw new Exception("device is null");
        }

        if (builder.device instanceof BaseCamDevice) {

        } else {
            throw new Exception("device is dscam");
        }
        byte[] key = {0x2B, 0x7E, 0x15, 0x16, 0x28, (byte) 0xAE, (byte) 0xD2,
                (byte) 0xA6, (byte) 0xAB, (byte) 0xF7, 0x15, (byte) 0x88, 0x09, (byte) 0xCF, 0x4F, 0x3C,
                0x2B, 0x7E, 0x15, 0x16, 0x28, (byte) 0xAE, (byte) 0xD2,
                (byte) 0xA6, (byte) 0xAB, (byte) 0xF7, 0x15, (byte) 0x88, 0x09, (byte) 0xCF, 0x4F, 0x3C};

        byte[] iv = {0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F};
        camEncryption = new CamEncryption(iv, key);
        this.context = builder.context;
        kRealTimePlayView = builder.kRealTimePlayView;
        dsCamDevice = (BaseCamDevice) builder.device;
        audioStream = new AudioStream(context, 8000, this);
        isFirstKeyFrame = builder.isFirstKeyFrame;
        videoDataMonitor = new VideoDataMonitor();
        videoDataMonitor.setListener(videoDataTimeOutListener);
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    @Override
    public void loadData() {
        kRealTimePlayer = new KRealTimePlayer();
        kRealTimePlayView.bindPlayer(this);
        kRealTimePlayer.prepareAsync();
        switchStatusTo(STATUS_START);
        switchStatusTo(STATUS_PREPARED);
    }

    @Override
    public void play(IDefaultCallBack callBack) {
        playSTD(callBack);
    }

    @Override
    public void play(int videoType, IDefaultCallBack callBack) {
        if (videoType == STD_TYPE) {
            playSTD(callBack);
        } else {
            playHD(callBack);
        }
    }

    private void switchStatusTo(int dst) {

        if (currentStatus == dst) {
            return;
        }
        synchronized (this) {
            currentStatus = dst;
//        notify status change
            if (statuslistener == null) {
                return;
            }

            if (currentStatus == STATUS_PREPARED) {
                statuslistener.onPrepared();
                return;
            }


            if (currentStatus == STATUS_START) {
                statuslistener.onStarted();
                return;
            }


            if (currentStatus == STATUS_PLAYING) {
                statuslistener.onPlaying();
                return;
            }

            if (currentStatus == STATUS_PAUSE) {
                statuslistener.onPaused();
                return;
            }

            if (currentStatus == STATUS_STOP) {
                statuslistener.onRelease();
                return;
            }

            if (currentStatus == STATUS_WAITING_DATA) {
                statuslistener.onWaiting();
                return;
            }
        }
    }

    @Override
    public void pausePlay() {
//        TODO send stop play command,no need to stop decoder thread and clear decoder buff,
        switchStatusTo(STATUS_PAUSE);
    }

    @Override
    public void resumePlay() {
//        TODO send play command
        if (stdVideoKcp == null) {
            return;
        }
        switchStatusTo(STATUS_WAITING_DATA);

    }

    @Override
    public void destory() {
        switchStatusTo(STATUS_STOP);
        stopTalk();
        stopListen();
        if (stdVideoKcp != null) {
            stdVideoKcp.close();
            dsCamDevice.removeKcp(stdVideoKcp.getConv());
        }
        if (hdVideoKcp != null) {
            hdVideoKcp.close();
            dsCamDevice.removeKcp(hdVideoKcp.getConv());
        }
        if (audioKcp != null) {
            audioKcp.close();
            dsCamDevice.removeKcp(audioKcp.getConv());
        }
        if (talkKcp != null) {
            talkKcp.resetAndClose();
            dsCamDevice.removeKcp(talkKcp.getConv());
        }
        if (snapshotClient != null) {
            snapshotClient.close();
            dsCamDevice.removeKcp(snapshotClient.getConv());
        }
        if (kRealTimePlayer != null) {
            kRealTimePlayer.release();
        }
        synchronized (this) {
            this.statuslistener = null;
            this.snapshotCallBack.clear();
        }
        if (videoDataMonitor != null) {
            videoDataMonitor.stop();
            videoDataMonitor = null;
        }
    }


    @Override
    public void startListen(IDefaultCallBack callBack) {
//       每一次的sessid要不一样，由于停止的term和startlisten是异步的过程，如果相同的sessid，无法保证服务器先收到的是estab还是term
//        有可能term-estab，但服务器收到的是estab-term，就会出现一段音频后没有声音了
        audioSessionID = RandomStringUtils.getSessionID();

        dsCamDevice.createKcp(KcpClientImpl.TYPE_AUDIO, audioSessionID, new IKcpCreateCallBack() {
            @Override
            public void onCreate(KcpClientImpl kcpClient) {
                audioKcp = kcpClient;
                audioKcp.setCallBack(new IKcpCallBack() {

                    @Override
                    public void onException(String s) {

                    }

                    @Override
                    public void onClose() {
                        dsCamDevice.disconnect();
                    }

                    @Override
                    public void onMessage(int conv, byte[] msg) {
                        if (currentStatus > STATUS_PREPARED && currentStatus <= STATUS_PLAYING) {
                            if (isWaitingData() || !isPlaying()) {
                                switchStatusTo(STATUS_PLAYING);
                            }
                            if (isListener) {
//                        byte[] _msg = camEncryption.decryptAes(msg);
                                kRealTimePlayer.feed(msg, 0);
                            } else {
                                MsctLog.d(TAG, "rec audio and but not feed:" + msg.length);
                            }
                        }
                    }
                });
                audioKcp.connect();
                audioKcp.sendString(AuthUtils.getFirstMessage());
                isListener = true;
                if (callBack != null) {
                    callBack.onSuccess();
                }
            }

            @Override
            public void onError(int i, String s) {
                MsctLog.w(TAG, "create audio fail:" + i + " msg:" + s);
                isListener = false;
                if (callBack != null) {
                    callBack.onError(i, s);
                }
            }
        });

    }

    @Override
    public void startTalk(IDefaultCallBack callBack) {
//        TODO 重构录音
//        https://android.googlesource.com/platform/external/nist-sip/+/6f95fdeab4481188b6260041b41d1db12b101266/src/com/android/sip/SipMain.java
//        AudioRecordUtil.getInstance().start();
        talkSessionID = RandomStringUtils.getSessionID();
        dsCamDevice.createKcp(KcpClientImpl.TYPE_TALK, talkSessionID, new IKcpCreateCallBack() {
            @Override
            public void onCreate(KcpClientImpl kcpClient) {
                talkKcp = kcpClient;
                talkKcp.setCallBack(new IKcpCallBack() {
                    @Override
                    public void onMessage(int i, byte[] bytes) {

                    }

                    @Override
                    public void onException(String s) {
//                        input error :
                        MsctLog.e(TAG, s);
                        if ("input error : -4".equals(s)) {
                            if (callBack != null) {
                                callBack.onError(-4, "talk already open");
                            }
                        }
                    }

                    @Override
                    public void onClose() {
                        if (callBack != null) {
                            callBack.onError(ErrorCode.DEFAULT, "talk disconncet");
                        }
                        stopTalk();
                    }
                });
                talkKcp.connect();
                audioStream.start();
                if (callBack != null) {
                    callBack.onSuccess();
                }
            }

            @Override
            public void onError(int i, String s) {
                MsctLog.i(TAG, "create tail channel fail:" + i + " msg:" + s);
                if (callBack != null) {
                    callBack.onError(i, s);
                }
            }
        });


    }

    @Override
    public void stopListen() {
        isListener = false;
        if (audioKcp != null) {
            audioKcp.close();
            kRealTimePlayer.clearAudio();
            dsCamDevice.removeKcp(audioKcp.getConv());
        }
    }

    @Override
    public void stopTalk() {
//        AudioRecordUtil.getInstance().stop();
        audioStream.stop();
        if (talkKcp != null) {
            talkKcp.resetAndClose();
            dsCamDevice.removeKcp(talkKcp.getConv());
        }
    }

    @Override
    public boolean isConnect() {
        return false;
    }

    @Override
    public boolean isListening() {
        return isListener;
    }

    @Override
    public boolean isTalking() {
        return audioStream.isRunning();
    }

    private boolean isStart() {
        synchronized (this) {
            return currentStatus == STATUS_START;
        }
    }

    @Override
    public boolean isPlaying() {
        synchronized (this) {
            return currentStatus == STATUS_PLAYING || currentStatus == STATUS_WAITING_DATA;
        }
    }

    @Override
    public void switchQuality(int type, IDefaultCallBack callBack) {
        kRealTimePlayer.resetVideo();
        if (type == 0) {
            playSTD(callBack);
        } else {
            playHD(callBack);
        }

    }

    private void playSTD(IDefaultCallBack callBack) {
        if (stdVideoKcp != null) {
            stdVideoKcp.close();
            dsCamDevice.removeKcp(stdVideoKcp.getConv());
        }
        if (hdVideoKcp != null) {
            hdVideoKcp.close();
            dsCamDevice.removeKcp(hdVideoKcp.getConv());
        }
        currentVideoType = 0;
        stdVideoSessionID = RandomStringUtils.getSessionID();
        dsCamDevice.createKcp(KcpClientImpl.TYPE_STD_VIDEO, stdVideoSessionID, new IKcpCreateCallBack() {
            @Override
            public void onCreate(KcpClientImpl kcpClient) {
                stdVideoKcp = kcpClient;
                ishasFeedKeyFrame = false;
                stdVideoKcp.setCallBack(new IKcpCallBack() {
                    @Override
                    public void onMessage(int conv, byte[] msg) {
                        if (currentStatus > STATUS_PREPARED && currentStatus <= STATUS_PLAYING) {
//                  FIXME can't switch status,because when first time to play ,current status is prepared,
//                    so it can't not go into below code block

                            if (isWaitingData() || !isPlaying()) {
                                switchStatusTo(STATUS_PLAYING);
                            }
//                            MsctLog.i(TAG, "std rec:" + msg.length);
//                    long time = System.currentTimeMillis();
//                    byte[] _msg = camEncryption.decryptAes(msg);
//                    long time2 = System.currentTimeMillis();
//                    MsctLog.i(TAG, "解密时间:" + (time2 - time));
                            if (isFirstKeyFrame) {
                                if (ishasFeedKeyFrame) {
                                    kRealTimePlayer.feed(msg, 1);
                                } else {
                                    if (isIKey(msg)) {
                                        ishasFeedKeyFrame = true;
                                        kRealTimePlayer.feed(msg, 1);
                                    } else {
//
                                        DDLog.i("video", "not key frame drop");
                                    }
                                }
                            } else {
                                kRealTimePlayer.feed(msg, 1);
                            }

                            if (videoDataMonitor != null) {
                                videoDataMonitor.update();
                            }

                        }
                    }

                    @Override
                    public void onException(String s) {
                        Log.e(TAG, "onException: TYPE_STD_VIDEO " + s);
                    }

                    @Override
                    public void onClose() {
                        Log.e(TAG, "onClose: TYPE_STD_VIDEO ");
//                        dsCamDevice.disconnect();
                        if (videoDataTimeOutListener != null) {
                            videoDataTimeOutListener.onVideoDataTimeOut();
                        }

                    }
                });
                stdVideoKcp.connect();
                stdVideoKcp.sendString(AuthUtils.getFirstMessage());
                switchStatusTo(STATUS_WAITING_DATA);
                if (callBack != null) {
                    callBack.onSuccess();
                }
            }

            @Override
            public void onError(int i, String s) {
                MsctLog.i(TAG, "create hd video fail:" + i + " msg:" + s);
                switchStatusTo(STATUS_STOP);
                if (callBack != null) {
                    callBack.onError(i, s);
                }
            }
        });

        if (videoDataMonitor != null) {
            videoDataMonitor.start();
        }
    }

    private void playHD(IDefaultCallBack callBack) {
        if (stdVideoKcp != null) {
            stdVideoKcp.close();
            dsCamDevice.removeKcp(stdVideoKcp.getConv());
        }
        if (hdVideoKcp != null) {
            hdVideoKcp.close();
            dsCamDevice.removeKcp(hdVideoKcp.getConv());
        }
        currentVideoType = 1;
        hdVideoSessionID = RandomStringUtils.getSessionID();
        dsCamDevice.createKcp(KcpClientImpl.TYPE_HD_VIDEO, hdVideoSessionID, new IKcpCreateCallBack() {
            @Override
            public void onCreate(KcpClientImpl kcpClient) {
                hdVideoKcp = kcpClient;
                ishasFeedKeyFrame = false;
                hdVideoKcp.setCallBack(new IKcpCallBack() {
                    @Override
                    public void onMessage(int conv, byte[] msg) {
                        if (currentStatus > STATUS_PREPARED && currentStatus <= STATUS_PLAYING) {
//                  FIXME can't switch status,because when first time to play ,current status is prepared,
//                    so it can't not go into below code block

                            if (isWaitingData() || !isPlaying()) {
                                switchStatusTo(STATUS_PLAYING);
                            }
                            MsctLog.i(TAG, "hd rec:" + msg.length);
                            if (isFirstKeyFrame) {
                                if (ishasFeedKeyFrame) {
                                    kRealTimePlayer.feed(msg, 1);
                                } else {
                                    if (isIKey(msg)) {
                                        ishasFeedKeyFrame = true;
                                        kRealTimePlayer.feed(msg, 1);
                                    } else {
//
                                        DDLog.i("video", "not key frame drop");
                                    }
                                }
                            } else {
                                kRealTimePlayer.feed(msg, 1);
                            }

                            if (videoDataMonitor != null) {
                                videoDataMonitor.update();
                            }


                        }
                    }

                    @Override
                    public void onException(String s) {
                        Log.e(TAG, "onException: TYPE_HD_VIDEO " + s);
                    }

                    @Override
                    public void onClose() {
                        Log.e(TAG, "onClose: TYPE_HD_VIDEO ");
//                        dsCamDevice.disconnect();
                        if (videoDataTimeOutListener != null) {
                            videoDataTimeOutListener.onVideoDataTimeOut();
                        }
                    }
                });
                hdVideoKcp.connect();
                hdVideoKcp.sendString(AuthUtils.getFirstMessage());
                switchStatusTo(STATUS_WAITING_DATA);
                if (callBack != null) {
                    callBack.onSuccess();
                }
            }

            @Override
            public void onError(int i, String s) {
                MsctLog.i(TAG, "create hd video fail:" + i + " msg:" + s);
                switchStatusTo(STATUS_STOP);
                if (callBack != null) {
                    callBack.onError(i, s);
                }
            }
        });
        if (videoDataMonitor != null) {
            videoDataMonitor.start();
        }
    }


    private boolean isWaitingData() {
        synchronized (this) {
            return currentStatus == STATUS_WAITING_DATA;
        }
    }

    @Override
    public void getSnapshot() {
        synchronized (snapkcpLock) {
            if (this.dsCamDevice == null) {
                for (IDefaultCallBack2<Bitmap> bitmapIDefaultCallBack2 : snapshotCallBack) {
                    bitmapIDefaultCallBack2.onError(ErrorCode.DEFAULT, "cam is not ready");
                }
                return;
            }
            Bitmap snap = kRealTimePlayView.getBitmap();
            if (snap != null) {
//               能通过textureview来截图，那么直接才用kcp截图
                MsctLog.i(TAG, "get snap by view");
                for (IDefaultCallBack2<Bitmap> bitmapIDefaultCallBack2 : snapshotCallBack) {
                    bitmapIDefaultCallBack2.onSuccess(snap);
                }
                return;
            }
            MsctLog.i(TAG, "get snap by network");
            if (snapshotClient == null) {
                createSnapShotKcp();
            } else {
                snapshotClient.sendString(AuthUtils.getFirstMessage());
            }
        }
    }

    private void createSnapShotKcp() {
        snapShotSessionID = RandomStringUtils.getSessionID();
        dsCamDevice.createKcp(TYPE_SNAPSHOT, snapShotSessionID, new IKcpCreateCallBack() {
            @Override
            public void onCreate(KcpClientImpl kcp) {
                if (snapshotClient != null) {
                    snapshotClient.close();
                    dsCamDevice.removeKcp(snapshotClient.getConv());
                }
                snapshotClient = kcp;
                snapshotClient.setCallBack(new IKcpCallBack() {
                    @Override
                    public void onMessage(int i, byte[] bytes) {
                        Bitmap bitmap = BitmapFactory.decodeByteArray(bytes, 0, bytes.length);
                        for (IDefaultCallBack2<Bitmap> bitmapIDefaultCallBack2 : snapshotCallBack) {
                            bitmapIDefaultCallBack2.onSuccess(bitmap);
                        }
                    }

                    @Override
                    public void onException(String s) {

                    }

                    @Override
                    public void onClose() {
                        synchronized (snapkcpLock) {
                            MsctLog.e(TAG, "snap kcp close");
                            if (snapshotClient != null) {
                                snapshotClient.close();
                                dsCamDevice.removeKcp(snapshotClient.getConv());
                                snapshotClient = null;
                            }
                        }
                    }
                });
                snapshotClient.connect();
                snapshotClient.sendString(AuthUtils.getFirstMessage());
            }

            @Override
            public void onError(int i, String s) {
            }
        });

    }

    @Override
    public void registerSnapShotCallBack(IDefaultCallBack2<Bitmap> snapshotCallBack) {
        if (this.snapshotCallBack.contains(snapshotCallBack)) {
            return;
        }

        this.snapshotCallBack.add(snapshotCallBack);

    }

    @Override
    public void unregisterSnapShotCallBack(IDefaultCallBack2<Bitmap> snapshotCallBack) {
        this.snapshotCallBack.remove(snapshotCallBack);

    }

    @Override
    public void setStatusListener(IPlayerStatusListener listener) {
        this.statuslistener = listener;
    }

    @Override
    public IPlayerStatusListener getStatusListener() {
        return statuslistener;
    }

    @Override
    public boolean startRecord(String path, IRecordCallBack callBack) {
        kRealTimePlayer.startRecord(path,callBack);
        return true;
    }

    @Override
    public void stopRecord() {
        kRealTimePlayer.stopRecord();
    }

    @Override
    public void saveSurface() {
        if (this.kRealTimePlayView != null) {
            mSurfaceTexture = this.kRealTimePlayView.getSavedSurfaceTexture();
        }
    }

    @Override
    public SurfaceTexture getSurface() {
        return this.mSurfaceTexture;
    }

    @Override
    public void bindView(View kRealTimePlayView) {
        if (kRealTimePlayView != null && kRealTimePlayer.getSurface() == null &&
                kRealTimePlayView instanceof KRealTimePlayView
                && ((KRealTimePlayView) kRealTimePlayView).getSavedSurfaceTexture() != null) {
            this.kRealTimePlayView = (KRealTimePlayView) kRealTimePlayView;
            kRealTimePlayer.setSurface(new Surface(this.kRealTimePlayView.getSavedSurfaceTexture()));
        }
    }

    @Override
    public void changeBindView(View kRealTimePlayView) {
        if (kRealTimePlayView != null && kRealTimePlayView instanceof KRealTimePlayView) {
            ((KRealTimePlayView) kRealTimePlayView).bindPlayer(this);
        }
    }

//    @Override
//    public void pcm(int count, short[] data) {
//        byte[] gData = new byte[data.length / 2];
////        encoder.encode(data, 0, gData, 0);
//        G711.linear2alaw(data, 0, gData, data.length / 2);
//        talkKcp.sendByte(gData);
//    }

    private boolean isIKey(byte[] data) {
        if ((0x0 == data[0] && 0x0 == data[1] && 0x1 == data[2])) {
            int type = (data[3] & 0x7e) >> 1;
            if (type == 32) {
                return true;
            }
        } else if ((0x0 == data[0] && 0x0 == data[1] && 0x0 == data[2] && 0x1 == data[3])) {
            int type = (data[4] & 0x7e) >> 1;
            if (type == 32) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void audioData(byte[] data) {
        talkKcp.sendByte(data);
    }

    private VideoDataMonitor.VideoDataTimeOutListener videoDataTimeOutListener = new VideoDataMonitor.VideoDataTimeOutListener() {
        @Override
        public void onVideoDataTimeOut() {
            DDLog.e("VideoDataMonitor", "onVideoDataTimeOut!!!");

            //TODO send rst
            if (currentVideoType == 0) {
                if (stdVideoKcp != null) {
                    stdVideoKcp.resetAndClose();
                }
                DDLog.d("VideoDataMonitor", "playSTD: ");
                playSTD(null);
            } else {
                if (hdVideoKcp != null) {
                    hdVideoKcp.resetAndClose();
                }
                DDLog.d("VideoDataMonitor", "playHD: ");
                playHD(null);
            }

            if (isListening()) {
                startListen(null);
            }
        }
    };

    /**
     * {@code KPlayer} builder static inner class.
     */
    @Keep
    public static final class Builder {
        private KRealTimePlayView kRealTimePlayView;
        private Device device;
        private boolean isFirstKeyFrame;
        private Context context;


        public Builder() {
        }

        /**
         * Sets the {@code kRealTimePlayView} and returns a reference to this Builder so that the methods can be chained together.
         *
         * @param kRealTimePlayView the {@code kRealTimePlayView} to set
         * @return a reference to this Builder
         */
        public Builder kRealTimePlayView(KRealTimePlayView kRealTimePlayView) {
            this.kRealTimePlayView = kRealTimePlayView;
            return this;
        }

        public Builder withDevice(Device device) {
            this.device = device;
            return this;
        }

        public Builder withKeyFrame(boolean isFirstKeyFrame) {
            this.isFirstKeyFrame = isFirstKeyFrame;
            return this;
        }

        public Builder withContext(Context context) {
            this.context = context;
            return this;
        }


        /**
         * Returns a {@code KPlayer} built from the parameters previously set.
         *
         * @return a {@code KPlayer} built with parameters of this {@code KPlayer.Builder}
         */
        public DsCamPlayer build() throws Exception {
            return new DsCamPlayer(this);
        }
    }
}
