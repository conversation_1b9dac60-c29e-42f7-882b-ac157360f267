package com.dinsafer.module_dscam.player.record;

import static com.dinsafer.module_dscam.player.record.EasyMuxer.VERBOSE;

import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.AudioTrack;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaFormat;
import android.util.Log;

import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.module_dscam.player.IAudioDataCallBack;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.concurrent.LinkedBlockingQueue;

public class AudioEncodeThread extends Thread {

    private static final String TAG = AudioEncodeThread.class.getSimpleName();

    /**
     * 用来读取音視频文件 提取器
     */
    private MediaCodec mediaCodec;

    private boolean isPlaying;

    private boolean isStop;

    private LinkedBlockingQueue packages = new LinkedBlockingQueue<>();
//    private AudioTrack audioTrack;

    int sampleRate = 8000;

    private IAudioDataCallBack iAudioDataCallBack;

    public AudioEncodeThread() {
        initAudioTrack();

    }

    /**
     * 初始化AudioTrack，等待播放数据
     */
    private void initAudioTrack() {
        int streamType = AudioManager.STREAM_SYSTEM;
        int channelConfig = AudioFormat.CHANNEL_OUT_MONO;
        int audioFormat = AudioFormat.ENCODING_PCM_16BIT;
        int mode = AudioTrack.MODE_STREAM;

        int minBufferSize = AudioTrack.getMinBufferSize(sampleRate, channelConfig, audioFormat);

//        audioTrack = new AudioTrack(streamType, sampleRate, channelConfig, audioFormat,
//                Math.max(minBufferSize, 2048), mode);
//        audioTrack.play();
    }

    public void setAudioDataCallBack(IAudioDataCallBack iAudioDataCallBack) {
        this.iAudioDataCallBack = iAudioDataCallBack;
    }

    public boolean isPlaying() {
        return isPlaying;
    }

    public boolean isStop() {
        return isStop;
    }

    public void stopAudio() {
        isStop = true;
        iAudioDataCallBack = null;
        this.interrupt();
    }

    public void feed(byte[] pcm) {
        try {
            packages.put(pcm);
            Log.v(TAG, "feed: " + (pcm == null ? 0 : pcm.length));
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public void clearData() {
        try {
            packages.clear();
            iAudioDataCallBack = null;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public void run() {


//        if (audioTrack == null) {
//            MsctLog.e(TAG, "创建播放器失败");
//            return;
//        }
        MediaFormat mediaFormat = createFormat();
        String mimeType = mediaFormat.getString(MediaFormat.KEY_MIME);

        try {
            if (mediaCodecDecoder(mediaFormat, mimeType)) return;
        } catch (Exception e) {
            e.printStackTrace();
            MsctLog.e(TAG, "硬编码失败,mimeType:" + mimeType);
            return;
        }
    }

    private boolean mediaCodecDecoder(MediaFormat mediaFormat, String mimeType) throws IOException {
        mediaCodec = MediaCodec.createEncoderByType(mimeType); // 创建解码器,提供数据输出
        mediaCodec.configure(mediaFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
        if (mediaCodec == null) {
            Log.e(TAG, "Can't find video info!");
            return true;
        }

        mediaCodec.start(); // 启动MediaCodec ，等待传入数据
//         输入
//        ByteBuffer[] inputBuffers = mediaCodec.getInputBuffers(); // 用来存放目标文件的数据
        // 输出
        ByteBuffer[] outputBuffers = mediaCodec.getOutputBuffers(); // 编码后的数据
        MediaCodec.BufferInfo info = new MediaCodec.BufferInfo(); // 用于描述编码得到的byte[]数据的相关信息
        boolean bIsEos = false;
        long startMs = System.currentTimeMillis() * 1000;

        isPlaying = true;
        isStop = false;

        // ==========开始解码=============
        int index = 0;
        // 将pcm编码成AAC
        final ByteBuffer[] inputBuffers = mediaCodec.getInputBuffers();
        while (!Thread.currentThread().isInterrupted()) {
            long timeUs = System.currentTimeMillis() * 1000 - startMs;
            byte[] frame = new byte[0];
            try {
                frame = (byte[]) packages.take();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            Log.d(TAG, "encode:" + frame.length);
            index = mediaCodec.dequeueInputBuffer(1000);
            if (index >= 0) {
                inputBuffers[index].clear();
                inputBuffers[index].put(frame, 0, frame.length);
                if (VERBOSE)
                    Log.d(TAG, String.format("queueInputBuffer pcm data length:%d,tmUS:%d", frame.length, timeUs));
                mediaCodec.queueInputBuffer(index, 0, frame.length, timeUs, 0);
            } else {
                Log.w(TAG, "mediaCodecDecoder: dequeueInputBuffer return index:" + index);
            }
            startMs = System.currentTimeMillis() * 1000;

            info = new MediaCodec.BufferInfo();
            index = mediaCodec.dequeueOutputBuffer(info, 1000);
            if (index >= 0) {
                if ((info.flags & MediaCodec.BUFFER_FLAG_CODEC_CONFIG) != 0) {
                    Log.w(TAG, "mediaCodecDecoder: (info.flags & MediaCodec.BUFFER_FLAG_CODEC_CONFIG) != 0");
                    continue;
                }
                if (info.presentationTimeUs == 0) {
                    Log.w(TAG, "mediaCodecDecoder: info.presentationTimeUs == 0");
                    continue;
                }
                if (VERBOSE)
                    Log.d(TAG, String.format("dequeueOutputBuffer data length:%d,tmUS:%d", info.size, info.presentationTimeUs));
                ByteBuffer outputBuffer = outputBuffers[index];
                // ok,编码成功了。将AAC数据写入muxer.
//                pumpStream(outputBuffer, info, false);
                if (iAudioDataCallBack != null) {
                    byte[] aac = new byte[info.size];
                    outputBuffer.get(aac);
                    outputBuffer.clear();
                    iAudioDataCallBack.onAudioData(aac, info);
                }
                mediaCodec.releaseOutputBuffer(index, false);
            } else if (index == MediaCodec.INFO_OUTPUT_BUFFERS_CHANGED) {
                outputBuffers = mediaCodec.getOutputBuffers();
            } else if (index == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) {
                Log.d(TAG, "output format changed...");
                MediaFormat newFormat = mediaCodec.getOutputFormat();
                Log.d(TAG, "output format changed..." + newFormat);
                if (iAudioDataCallBack != null) {
                    iAudioDataCallBack.onMediaFormatChangeed(newFormat);
                }
            } else if (index == MediaCodec.INFO_TRY_AGAIN_LATER) {
                Log.d(TAG, "No buffer available...");
            } else {
                Log.e(TAG, "Message: " + index);
            }
        }

        mediaCodec.stop();
        mediaCodec.release();

        return false;
    }

    private MediaFormat createFormat() {
        MediaFormat mediaFormat = MediaFormat.createAudioFormat(
                MediaFormat.MIMETYPE_AUDIO_AAC,
                sampleRate,
                1);
        mediaFormat.setInteger(MediaFormat.KEY_AAC_PROFILE, MediaCodecInfo.CodecProfileLevel.AACObjectLC);
        mediaFormat.setInteger(MediaFormat.KEY_BIT_RATE, 16000);
        mediaFormat.setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, 1024 * 8 * 8);
//        mediaFormat.setInteger(MediaFormat.KEY_IS_ADTS, 1);
        return mediaFormat;
    }

}