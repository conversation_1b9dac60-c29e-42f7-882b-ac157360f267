package com.dinsafer.module_dscam.player.webrtc;

import android.content.Context;
import android.os.AsyncTask;
import android.util.Log;

import com.amazonaws.regions.Region;
import com.amazonaws.services.kinesisvideo.AWSKinesisVideoClient;
import com.amazonaws.services.kinesisvideo.model.ChannelRole;
import com.amazonaws.services.kinesisvideo.model.CreateSignalingChannelRequest;
import com.amazonaws.services.kinesisvideo.model.CreateSignalingChannelResult;
import com.amazonaws.services.kinesisvideo.model.DescribeSignalingChannelRequest;
import com.amazonaws.services.kinesisvideo.model.DescribeSignalingChannelResult;
import com.amazonaws.services.kinesisvideo.model.GetSignalingChannelEndpointRequest;
import com.amazonaws.services.kinesisvideo.model.GetSignalingChannelEndpointResult;
import com.amazonaws.services.kinesisvideo.model.ResourceEndpointListItem;
import com.amazonaws.services.kinesisvideo.model.ResourceNotFoundException;
import com.amazonaws.services.kinesisvideo.model.SingleMasterChannelEndpointConfiguration;
import com.amazonaws.services.kinesisvideosignaling.AWSKinesisVideoSignalingClient;
import com.amazonaws.services.kinesisvideosignaling.model.GetIceServerConfigRequest;
import com.amazonaws.services.kinesisvideosignaling.model.GetIceServerConfigResult;
import com.amazonaws.services.kinesisvideosignaling.model.IceServer;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.module_dscam.player.webrtc.auth.CustomCriedentialProvider;

import java.util.ArrayList;
import java.util.List;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2022/3/21
 */
public class KVSClientManager {
    private String TAG = "webrtc";
    private String clazz = "KVSClientManager";

    private static Context context;

    private final List<ResourceEndpointListItem> mEndpointList = new ArrayList<>();
    private final List<IceServer> mIceServerList = new ArrayList<>();
    private String mChannelArn = null;

    public static void init(Context c) {
        context = c;
    }

    private KVSClientManager() {

    }

    public synchronized static KVSClientManager getInstance() {
        return Holder.INSTANCE;
    }

    private static class Holder {
        private static KVSClientManager INSTANCE = new KVSClientManager();

        private Holder() {
        }
    }

    private AWSKinesisVideoClient getKVSVideoClient(final String region) {
        final AWSKinesisVideoClient awsKinesisVideoClient = new AWSKinesisVideoClient(
                CustomCriedentialProvider.getCredentialProvider(context).getCredentials());
        awsKinesisVideoClient.setRegion(Region.getRegion(region));
        awsKinesisVideoClient.setSignerRegionOverride(region);
        awsKinesisVideoClient.setServiceNameIntern("kinesisvideo");
        return awsKinesisVideoClient;
    }

    private AWSKinesisVideoSignalingClient getKVSSignalingClient(final String region, final String endpoint) {
        final AWSKinesisVideoSignalingClient client = new AWSKinesisVideoSignalingClient(
                CustomCriedentialProvider.getCredentialProvider(context).getCredentials());
        client.setRegion(Region.getRegion(region));
        client.setSignerRegionOverride(region);
        client.setServiceNameIntern("kinesisvideo");
        client.setEndpoint(endpoint);
        return client;
    }

    public List<ResourceEndpointListItem> getEndpointList() {
        return mEndpointList;
    }

    public List<IceServer> getIceServerList() {
        return mIceServerList;
    }

    public String getChannelArn() {
        return mChannelArn;
    }

    public void updateSignalingChannelInfo(final String region, final String channelName, final ChannelRole role, IDefaultCallBack callBack) {
        mEndpointList.clear();
        mIceServerList.clear();
        mChannelArn = null;
        UpdateSignalingChannelInfoTask task = new UpdateSignalingChannelInfoTask();
        try {
            task.execute(region, channelName, role, callBack);
        } catch (Exception e) {
            Log.e(TAG, "Failed to wait for response of UpdateSignalingChannelInfoTask", e);
        }
    }


    private class UpdateSignalingChannelInfoTask extends AsyncTask<Object, String, String> {
        private String TAG = getClass().getSimpleName();
        private IDefaultCallBack callback;

        UpdateSignalingChannelInfoTask() {
        }

        @Override
        protected String doInBackground(Object... objects) {
            final String region = (String) objects[0];
            final String channelName = (String) objects[1];
            final ChannelRole role = (ChannelRole) objects[2];
            callback = (IDefaultCallBack) objects[3];
            AWSKinesisVideoClient awsKinesisVideoClient = null;
            try {
                awsKinesisVideoClient = KVSClientManager.getInstance().getKVSVideoClient(region);
            } catch (Exception e) {
                return "Create client failed with " + e.getLocalizedMessage();
            }

            try {
                DescribeSignalingChannelResult describeSignalingChannelResult = awsKinesisVideoClient.describeSignalingChannel(
                        new DescribeSignalingChannelRequest()
                                .withChannelName(channelName));
                Log.i(TAG, "Channel ARN is " + describeSignalingChannelResult.getChannelInfo().getChannelARN());
                mChannelArn = describeSignalingChannelResult.getChannelInfo().getChannelARN();
            } catch (final ResourceNotFoundException e) {
                if (role.equals(ChannelRole.MASTER)) {
                    try {
                        CreateSignalingChannelResult createSignalingChannelResult = awsKinesisVideoClient.createSignalingChannel(
                                new CreateSignalingChannelRequest()
                                        .withChannelName(channelName));

                        mChannelArn = createSignalingChannelResult.getChannelARN();
                    } catch (Exception ex) {
                        return "Create Signaling Channel failed with Exception " + ex.getLocalizedMessage();
                    }
                } else {
                    return "Signaling Channel " + channelName + " doesn't exist!";
                }
            } catch (Exception ex) {
                return "Describe Signaling Channel failed with Exception " + ex.getLocalizedMessage();
            }
            try {
                GetSignalingChannelEndpointResult getSignalingChannelEndpointResult = awsKinesisVideoClient.getSignalingChannelEndpoint(
                        new GetSignalingChannelEndpointRequest()
                                .withChannelARN(mChannelArn)
                                .withSingleMasterChannelEndpointConfiguration(
                                        new SingleMasterChannelEndpointConfiguration()
                                                .withProtocols("WSS", "HTTPS")
                                                .withRole(role)));

                Log.i(TAG, "Endpoints " + getSignalingChannelEndpointResult.toString());
                mEndpointList.addAll(getSignalingChannelEndpointResult.getResourceEndpointList());
            } catch (Exception e) {
                return "Get Signaling Endpoint failed with Exception " + e.getLocalizedMessage();
            }

            String dataEndpoint = null;
            for (ResourceEndpointListItem endpoint : mEndpointList) {
                if (endpoint.getProtocol().equals("HTTPS")) {
                    dataEndpoint = endpoint.getResourceEndpoint();
                }
            }

            try {
                final AWSKinesisVideoSignalingClient awsKinesisVideoSignalingClient = KVSClientManager.getInstance().getKVSSignalingClient(region, dataEndpoint);
                GetIceServerConfigResult getIceServerConfigResult = awsKinesisVideoSignalingClient.getIceServerConfig(new GetIceServerConfigRequest().withChannelARN(mChannelArn).withClientId(role.name()));
                mIceServerList.addAll(getIceServerConfigResult.getIceServerList());
                for (IceServer iceServer : mIceServerList) {
                    Log.d(TAG, "iceServer: " + iceServer.toString());
                }
            } catch (Exception e) {
                return "Get Ice Server Config failed with Exception " + e.getLocalizedMessage();
            }

            return null;
        }

        @Override
        protected void onPostExecute(String result) {
            if (result != null) {
                Log.e(TAG, clazz + "UpdateSignalingChannelInfoTask: " + result);
                if (callback != null) {
                    callback.onError(0, result);
                }
            } else {
                if (callback != null) {
                    callback.onSuccess();
                }
            }
        }
    }


}
