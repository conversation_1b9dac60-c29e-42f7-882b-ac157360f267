package com.dinsafer.module_dscam.player.webrtc.auth;

import android.content.Context;

import com.amazonaws.auth.CognitoCachingCredentialsProvider;
import com.amazonaws.regions.Regions;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2022/3/16
 */
public class CustomCriedentialProvider {

    public static CognitoCachingCredentialsProvider getCredentialProvider(Context context) {
        DeveloperAuthenticationProvider developerProvider = new DeveloperAuthenticationProvider(null, "ap-southeast-1:99e6d05f-1992-469f-8dbd-b36c466c24a5", Regions.AP_SOUTHEAST_1);
        CustomCognitoCachingCredentialsProvider credentialsProvider = new CustomCognitoCachingCredentialsProvider(context, developerProvider, Regions.AP_SOUTHEAST_1);
        return credentialsProvider;
    }
}
