package com.dinsafer.module_dscam.player;

import android.graphics.Bitmap;
import android.graphics.SurfaceTexture;
import androidx.annotation.Keep;
import android.view.View;

import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.module_dscam.player.record.IRecordCallBack;

@Keep
public interface IPlayer {

    void loadData();

    void play(IDefaultCallBack callBack);

    void play(int videoType, IDefaultCallBack callBack);

    void pausePlay();

    void resumePlay();

    void destory();

    void startListen(IDefaultCallBack callBack);

    void startTalk(IDefaultCallBack callBack);

    void stopListen();

    void stopTalk();

    boolean isConnect();

    boolean isListening();

    boolean isTalking();

    boolean isPlaying();

    //    0:std 1:HD
    void switchQuality(int type, IDefaultCallBack callBack);

    void getSnapshot();

    void registerSnapShotCallBack(IDefaultCallBack2<Bitmap> snapshotCallBack);

    void unregisterSnapShotCallBack(IDefaultCallBack2<Bitmap> snapshotCallBack);

    void setStatusListener(IPlayerStatusListener listener);

    IPlayerStatusListener getStatusListener();

    boolean startRecord(String path,IRecordCallBack callBack);

    void stopRecord();

    void saveSurface();

    SurfaceTexture getSurface();

    void bindView(View playView);

    void changeBindView(View playView);

}