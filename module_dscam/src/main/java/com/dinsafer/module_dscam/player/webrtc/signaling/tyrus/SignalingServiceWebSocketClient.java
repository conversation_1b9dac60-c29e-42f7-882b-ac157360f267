package com.dinsafer.module_dscam.player.webrtc.signaling.tyrus;

import android.util.Base64;
import android.util.Log;

import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.module_dscam.player.webrtc.signaling.SignalingListener;
import com.dinsafer.module_dscam.player.webrtc.signaling.model.Message;
import com.google.gson.Gson;

import org.glassfish.tyrus.client.ClientManager;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Signaling service client based on websocket.
 */

public class SignalingServiceWebSocketClient {

    private static final String TAG = "webrtc";
    private static final String clazz = "SignalingServiceWebSocketClient-->";

    private final WebSocketClient websocketClient;

    private final ExecutorService executorService;

    private final Gson gson = new Gson();

    public SignalingServiceWebSocketClient(final String uri, final SignalingListener signalingListener,
                                           final ExecutorService executorService) {
        Log.d(TAG, clazz + "Connecting to URI " + uri + " as master");
        websocketClient = new WebSocketClient(uri, new ClientManager(), signalingListener, executorService);
        this.executorService = executorService;
    }

    public boolean isOpen() {
        return websocketClient.isOpen();
    }

    public void sendSdpOffer(final Message offer) {
        executorService.submit(new Runnable() {
            @Override
            public void run() {
                if (offer.getAction().equalsIgnoreCase("SDP_OFFER")) {
                    MsctLog.d(TAG, clazz + "sendSdpOffer: Sending Offer:" + offer.toString());
                    send(offer);
                }
            }
        });
    }

    public void sendSdpAnswer(final Message answer) {
        executorService.submit(new Runnable() {
            @Override
            public void run() {
                if (answer.getAction().equalsIgnoreCase("SDP_ANSWER")) {
                    MsctLog.d(TAG, clazz + "sendSdpAnswer: Answer sent " + new String(Base64.decode(answer.getMessagePayload().getBytes(),
                            Base64.NO_WRAP | Base64.NO_PADDING | Base64.URL_SAFE)));
                    send(answer);
                }
            }
        });
    }

    public void sendIceCandidate(final Message candidate) {
        executorService.submit(new Runnable() {
            @Override
            public void run() {
                if (candidate.getAction().equalsIgnoreCase("ICE_CANDIDATE")) {
                    MsctLog.d(TAG, clazz + "sendIceCandidate: Sent Ice candidate message");
                    send(candidate);
                }
            }
        });
    }

    public void disconnect() {
        executorService.submit(new Runnable() {
            @Override
            public void run() {
                websocketClient.disconnect();
            }
        });
        try {
            executorService.awaitTermination(1, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            MsctLog.e(TAG, clazz + "disconnect: Error in disconnect");
        }
    }

    private void send(final Message message) {
        String jsonMessage = gson.toJson(message);
//        MsctLog.d(TAG, "Sending JSON Message= " + jsonMessage);
        websocketClient.send(jsonMessage);
        MsctLog.d(TAG, clazz + "send: Sent JSON Message= " + jsonMessage);
    }

}
