package com.dinsafer.module_dscam.bean;

import com.dinsafer.dincore.common.Cmd;

public class DsCamCmd extends Cmd {
    public static final String CONNECT = "connect";
    public static final String DISCONNECT = "disconnect";
    public static final String CONNECT_STATUS_CHANGED = "connect_status_changed";
    public static final String GET_PARAMS = "get_params";
    public static final String SET_MD = "set_md";
    public static final String SET_MD_LEVEL = "set_md_level";
    public static final String SET_PIR_LEVEL = "set_pir_level";
    public static final String SET_VFLIP = "set_vflip";
    public static final String SET_HFLIP = "set_hflip";
    public static final String SET_TZ = "set_tz";
    public static final String FORMAT_TF = "format_tf";
    public static final String RESTORE_DEFAULT = "restore_default";
    public static final String RESET = "reset";
    public static final String SET_MD_TIME = "set_md_time";
    public static final String SET_ALERT_MODE = "set_alert_mode";
    public static final String GET_ALERT_MODE = "get_alert_mode";
    public static final String GET_SNAPSHOT = "get_snapshot";
    public static final String KEEPALIVE = "keepalive";
    public static final String REBOOT = "reboot";
    public static final String SET_GRAY = "set_gray";
    public static final String LED_TEST = "led_test";
    public static final String SET_FACTORY = "set_factory";
    public static final String GET_REGTOKEN = "get_reg_token";
    public static final String GET_RECORD_LIST = "get_record_list";
    public static final String GET_RECORD_LIST_V2 = "get_record_list_v2";
    public static final String GET_RECORD_FILE = "get_record_file";
    public static final String STOP_RECORD_FILE_FETCH = "stop_record_file_fetch";
    public static final String DEL_RECORD_FILE = "del_record_file";
    public static final String UPGRADE_FIRMWARE = "upgrade";
    public static final String IPC_VER_NUM_UPDATED = "ipc-ver-num-updated";
    public static final String SET_VIDEO_MODE = "set_video_mode";
    public static final String SET_MD_ALARM = "set_md_alarm";
    public static final String SET_MD_FOLLOW = "set_md_follow";
    public static final String SET_DAILY_MEMORIES = "set_daily_memories";
    public static final String SET_SCHEDULED = "set_scheduled";

    public static final String CONNECTING = "connecting";
    public static final String CONNECTED = "connected";
}
