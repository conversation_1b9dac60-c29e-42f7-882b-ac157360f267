package com.dinsafer.module_dscam.bean;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;

import com.dinsafer.dincore.common.Cmd;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.http.NetWorkException;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dincore.utils.MapUtils;
import com.dinsafer.dincore.utils.MyMessageIdHolder;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.msctlib.kcp.IKcpCallBack;
import com.dinsafer.dssupport.msctlib.kcp.IKcpCreateCallBack;
import com.dinsafer.dssupport.msctlib.netty.IMultipleSender;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_dscam.channel.Channel;
import com.dinsafer.module_dscam.channel.ChannelManager;
import com.dinsafer.module_dscam.channel.IChannelCallBack;
import com.dinsafer.module_dscam.channel.LanChannel;
import com.dinsafer.module_dscam.channel.ProxyChannel;
import com.dinsafer.module_dscam.event.DsDeviceNameUpdateEvent;
import com.dinsafer.module_dscam.http.DsCamApi;
import com.dinsafer.module_dscam.record.download.SDCardRecordManager;
import com.dinsafer.module_dscam.utils.SearchIpcHelper;
import com.dinsafer.module_dscam.v006.DsCamV006Cmd;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.ListIterator;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 摄像头类设备的基类
 * 自研ipc和门铃都继承自这个类
 */
public class BaseCamDevice extends Device {
    protected final String TAG = getClass().getSimpleName();
    protected String name;
    //    ipc 的end_id
    protected String receiveID;
    protected String group_id;
    //    跟ipc通讯的end_id
    protected long addtime;
    protected String homeID;
    protected IMultipleSender sender;
    protected IKcpCallBack kcpCallBack;
    protected int connectStatus = -1;

    //    ipc 属性，需要调用getipcinfo才能获取
    protected boolean hd = false;
    protected boolean vflip = false;
    protected boolean hflip = false;
    //    时区
    protected String tz = "";
    //    当前网络
    protected String ssid = "";
    protected String ip = "";
    protected int battery = -1;
    protected boolean charging = false;
    //    tf卡容量，KB
    protected long tf_capacity = -1;
    //    tf卡已使用
    protected long tf_used = -1;
    //    移动侦测
    protected boolean md = false;
    protected boolean gray = false;
    //    移动侦测等级
    protected int md_level = -1;
    // PIR等级
    protected int pir_level = -1;
    protected String mdBeginTime = "";
    protected String mdEndTime = "";
    protected String alertMode = "";
    protected boolean cloudStorage = false;
    protected boolean alertConArm = false;
    protected boolean alertConDisarm = false;
    protected boolean alertConHomeArm = false;
    protected boolean alertConSos = false;
    protected boolean panelFollow = false;

    //    protected int dscamSessionID = RandomStringUtils.getSessionID();
    protected String lanIp = "";
    protected String mac = "";
    protected int rssi = 0;
    protected int lanPort;
    protected String chip = "";
    //所有模块version信息
    protected Map versions = new LinkedHashMap();
    //下面是各个模块的版本,从versions里解析出来的
    protected String bleVersion = "";
    protected String _3861Version = "";
    protected String _3518AppVersion = "";
    protected String _3518UbootVersion = "";
    protected String _3518RootfsVersion = "";
    //    表示对象是否已经被删除，删除的话，不要回调连接状态变化，重复了
    protected boolean isDelete = false;
    //目前支持kcp和webrtc
    protected String videoMode = "kcp";

    //照明灯模式(v006新增)
    private int floodlight;
    //自动照明灯(v006新增)
    private int auto_floodlight;
    // 移动侦测报警时自动发出报警声-1.2.3新增
    private boolean md_alarm = false;
    // int 每日回忆的开始时间，默认值：0
    private long daily_memories = 0L;
    // 计划录像，默认关
    private boolean scheduled;
    // 计划录像的时间，int32数组 默认时段为24*7
    private List<Long> scheduled_time = new ArrayList<>();
//    ipc 属性结束

    protected ChannelManager channelManager;

    private final SDCardRecordManager sdCardRecordManager;
    private final MyMessageIdHolder messageIdHolder = new MyMessageIdHolder();

    protected ExecutorService executorService = Executors.newCachedThreadPool();

    protected IChannelCallBack channelCallBack = new IChannelCallBack() {
        @Override
        public void onConnect() {
            if (isDelete) {
                return;
            }
            if (connectStatus == 1) {
                return;
            }
            connectStatus = 1;
            convertToInfo();

            Map result = Cmd.getDefaultResultMap(true, DsCamCmd.CONNECT);
            result.put("connect_status", 1);
            dispatchResult(DsCamCmd.CONNECT, result);

            Map statusResult = Cmd.getDefaultResultMap(true, DsCamCmd.CONNECT_STATUS_CHANGED);
            statusResult.put("connect_status", 1);
            dispatchResult(DsCamCmd.CONNECT_STATUS_CHANGED, statusResult);

            dispatchOnline();
        }

        @Override
        public void onConnecting() {
            if (isDelete) {
                return;
            }
            if (connectStatus == 0) {
                return;
            }
            Map result = Cmd.getDefaultResultMap(false, DsCamCmd.CONNECT);
            result.put("connect_status", 0);
            connectStatus = 0;
            convertToInfo();
            dispatchResult(DsCamCmd.CONNECT, result);
        }

        @Override
        public void onDisconnect(int code, String msg) {
            if (isDelete) {
                return;
            }
            final int lastConnectStatus = connectStatus;
            connectStatus = -1;
            convertToInfo();
            if (code == ErrorCode.ERROR_CONNECT) {
                Map result = Cmd.getDefaultResultMap(false, DsCamCmd.CONNECT);
                result.put("errorMessage", msg);
                result.put("connect_status", -1);
                dispatchResult(DsCamCmd.CONNECT, result);
            } else {
                if (lastConnectStatus == -1) {
                    return;
                }
                Map result = Cmd.getDefaultResultMap(true, DsCamCmd.CONNECT_STATUS_CHANGED);
                result.put("connect_status", -1);
                dispatchResult(DsCamCmd.CONNECT_STATUS_CHANGED, result);
                dispatchOffline(msg);
            }
        }
    };

    public BaseCamDevice(IMultipleSender sender, String homeID, @NonNull final DsCamCacheInfo.CacheInfo cacheInfo, String subCategory) {
        super();
        initFlagOnReadCache();
        this.sender = sender;
        this.setId(cacheInfo.getPid());
        this.setCategory(2);
        this.setSubCategory(subCategory);
        this.homeID = homeID;
        this.name = cacheInfo.getName();
        if (TextUtils.isEmpty(name)) {
            name = "ipc";
        }
        if (receiveID == null) {
            receiveID = "";
        }
        if (group_id == null) {
            group_id = "";
        }
        if (this.homeID == null) {
            this.homeID = "";
        }
        convertToInfo();
        kcpCallBack = new IKcpCallBack() {
            @Override
            public void onMessage(int conv, byte[] bytes) {
                byte[] msgb = bytes;
                try {
                    String j = new String(msgb);
                    MsctLog.v(TAG, "receive decode:" + j);
                    JSONObject jsonObject = new JSONObject(j);
                    handlerKcpResponse(jsonObject);
                } catch (JSONException e) {
                    e.printStackTrace();
                }

            }

            @Override
            public void onException(String s) {

            }

            @Override
            public void onClose() {
                MsctLog.i(TAG, "onClose");
                disconnect();
            }
        };
        channelManager = new ChannelManager(this, homeID, kcpCallBack);
        channelManager.setChannelCallBack(channelCallBack);
        sdCardRecordManager = new SDCardRecordManager(this);
    }

    public BaseCamDevice(IMultipleSender sender, String pid, String homeID, String receiveID, String group_id, Long addtime, String subCategory) {
        super();
        initFlagOnNetwork();
        this.receiveID = receiveID;
        this.group_id = group_id;
        this.addtime = addtime;
        this.homeID = homeID;
        this.sender = sender;
        this.setId(pid);
        this.setCategory(2);
        this.setSubCategory(subCategory);
        if (TextUtils.isEmpty(name)) {
            name = "ipc";
        }
        if (receiveID == null) {
            receiveID = "";
        }
        if (group_id == null) {
            group_id = "";
        }
        if (this.homeID == null) {
            this.homeID = "";
        }
        convertToInfo();
        kcpCallBack = new IKcpCallBack() {
            @Override
            public void onMessage(int conv, byte[] bytes) {
                byte[] msgb = bytes;
                try {
                    String j = new String(msgb);
                    MsctLog.v(TAG, "receive decode:" + j);
                    JSONObject jsonObject = new JSONObject(j);
                    handlerKcpResponse(jsonObject);
                } catch (JSONException e) {
                    e.printStackTrace();
                }

            }

            @Override
            public void onException(String s) {

            }

            @Override
            public void onClose() {

            }
        };
        channelManager = new ChannelManager(this, homeID, kcpCallBack);
        channelManager.setChannelCallBack(channelCallBack);
        sdCardRecordManager = new SDCardRecordManager(this);
    }

    protected void handlerKcpResponse(JSONObject result) {
        String cmd = DDJSONUtil.getString(result, "cmd");
        int status = DDJSONUtil.getInt(result, "status");
        Map resultMap = Cmd.getDefaultResultMap(status == 1 ? true : false, cmd);
        Map tempResult = MapUtils.fromJson(result);
        resultMap.put("origin_status", status);
        if (tempResult != null && tempResult.size() > 0) {
            //防止getDefaultResultMap返回的status被tempResult中的status覆盖掉
            for (Object k : tempResult.keySet()) {
                resultMap.put(k, tempResult.get(k));
            }
        }

        // 判断是否是自己的消息
        final String messageId = DDJSONUtil.getString(result, "messageid");
        boolean owned;
        switch (cmd) {
            case DsCamCmd.DEL_RECORD_FILE:
            case DsCamCmd.GET_RECORD_LIST:
            case DsCamCmd.GET_RECORD_LIST_V2:
            case DsCamCmd.UPGRADE_FIRMWARE:
                owned = messageIdHolder.isMyMessageId(cmd, messageId) > 0;
                resultMap.put("owned", owned);
                tempResult.put("owned", owned);
                break;
        }

        // 失败结果处理
        if (status != 1) {
            resultMap.put("errorMessage", "cmd:" + cmd + " not work");
            dispatchResult(cmd, resultMap);
            return;
        }

        // 成功结果处理
        switch (cmd) {
            case DsCamCmd.GET_PARAMS:
                if (tempResult.containsKey("versions")) {
                    this.hd = (boolean) MapUtils.get(tempResult, "hd", false);
                    this.vflip = (boolean) MapUtils.get(tempResult, "vflip", false);
                    this.hflip = (boolean) MapUtils.get(tempResult, "hflip", false);
                    this.tz = (String) MapUtils.get(tempResult, "tz", "");
                    this.ssid = (String) MapUtils.get(tempResult, "ssid", "");
                    this.ip = (String) MapUtils.get(tempResult, "ip", "");
                    this.battery = DeviceHelper.getInt(tempResult, "battery", -1);
                    this.charging = (boolean) MapUtils.get(tempResult, "charging", false);
                    if (MapUtils.get(tempResult, "tf_capacity", -1) instanceof Integer) {
                        this.tf_capacity = DeviceHelper.getInt(tempResult, "tf_capacity", -1);

                    } else {
                        this.tf_capacity = DeviceHelper.getLong(tempResult, "tf_capacity", -1);
                    }

                    if (MapUtils.get(tempResult, "tf_capacity", -1) instanceof Integer) {
                        this.tf_capacity = DeviceHelper.getInt(tempResult, "tf_capacity", -1);
                    } else {
                        this.tf_capacity = DeviceHelper.getLong(tempResult, "tf_capacity", -1);
                    }

                    if (MapUtils.get(tempResult, "tf_capacity", -1) instanceof Integer) {
                        this.tf_used = DeviceHelper.getInt(tempResult, "tf_used", -1);
                    } else {
                        this.tf_used = DeviceHelper.getLong(tempResult, "tf_used", -1);
                    }
                    this.md = (boolean) MapUtils.get(tempResult, "md", false);
                    this.md_level = DeviceHelper.getInt(tempResult, "md_level", -1);
                    this.pir_level = DeviceHelper.getInt(tempResult, "pir_level", -1);
                    this.mdBeginTime = (String) MapUtils.get(tempResult, "md_begin_time", "08:00");
                    this.mdEndTime = (String) MapUtils.get(tempResult, "md_end_time", "23:00");
                    this.mac = (String) MapUtils.get(tempResult, "mac", "");
                    this.rssi = DeviceHelper.getInt(tempResult, "rssi", 0);
                    this.gray = (boolean) MapUtils.get(tempResult, "gray", false);
                    this.lanIp = this.ip;
                    if (MapUtils.get(tempResult, "tf_capacity", -1) instanceof Integer) {
                        this.lanPort = DeviceHelper.getInt(tempResult, "port", -1);
                    } else {
                        BigDecimal bigDecimal = new BigDecimal(DeviceHelper.getLong(tempResult, "port", -1));
                        this.lanPort = bigDecimal.intValue();
                    }
                    this.chip = (String) MapUtils.get(tempResult, "chip", "hi3518ev300");
                    //处理固件版本号
                    try {
                        com.alibaba.fastjson.JSONArray versionsArray = (com.alibaba.fastjson.JSONArray) MapUtils.get(tempResult, "versions", new JSONObject());
                        if (versionsArray != null && versionsArray.size() > 0) {
                            for (int i = 0; i < versionsArray.size(); i++) {
                                com.alibaba.fastjson.JSONObject jsonObject = versionsArray.getJSONObject(i);
                                versions.putAll(jsonObject.getInnerMap());
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        Map<String, String> v = (Map) MapUtils.get(tempResult, "versions", new LinkedHashMap<>());
                        if (v != null) {
                            ListIterator<Map.Entry<String, String>> li = new ArrayList<Map.Entry<String, String>>(v.entrySet()).listIterator(v.size());
                            while (li.hasPrevious()) {
                                Map.Entry<String, String> entry = li.previous();
                                versions.put(entry.getKey(), entry.getValue());
                            }
                        }
                    }
                    if (this.versions != null && this.versions.size() > 0) {
                        this.bleVersion = (String) MapUtils.get(this.versions, "ble", "");
                        this._3861Version = (String) MapUtils.get(this.versions, "3861", "");
                        this._3518AppVersion = (String) MapUtils.get(this.versions, "3518_app", "");
                        this._3518UbootVersion = (String) MapUtils.get(this.versions, "3518_uboot", "");
                        this._3518RootfsVersion = (String) MapUtils.get(this.versions, "3518_rootfs", "");
                    }
                    this.videoMode = (String) MapUtils.get(tempResult, "video_mode", "kcp");
                    //v006新增
                    this.floodlight = DeviceHelper.getInt(tempResult, "floodlight", -1);
                    this.auto_floodlight = DeviceHelper.getInt(tempResult, "auto_floodlight", 0);
                    this.md_alarm = (boolean) MapUtils.get(tempResult, "md_alarm", false);
                    // 1.2.6新增
                    this.panelFollow = (boolean) MapUtils.get(tempResult, "panel_follow", false);
                    this.alertConArm = false;
                    this.alertConDisarm = false;
                    this.alertConHomeArm = false;
                    this.alertConSos = false;
                    try {
                        com.alibaba.fastjson.JSONArray follow_status = (com.alibaba.fastjson.JSONArray) MapUtils.get(tempResult, "follow_status", new JSONObject());
                        if (follow_status != null && follow_status.size() > 0) {
                            for (int i = 0; i < follow_status.size(); i++) {
                                String stateStr = follow_status.getString(i);
                                if ("sos".equals(stateStr)) {
                                    this.alertConSos = true;
                                } else if ("arm".equalsIgnoreCase(stateStr)) {
                                    this.alertConArm = true;
                                } else if ("disarm".equalsIgnoreCase(stateStr)) {
                                    this.alertConDisarm = true;
                                } else if ("homearm".equalsIgnoreCase(stateStr)) {
                                    this.alertConHomeArm = true;
                                }
                            }
                        } else {
                            this.alertConSos = true;
                            this.alertConArm = true;
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    this.daily_memories = DDJSONUtil.getLong(result, "daily_memories");
                    this.scheduled = DDJSONUtil.getBoolean(result, "scheduled");
                    this.scheduled_time = new ArrayList<>();
                    try {
                        JSONArray scheduledTimeArray = DDJSONUtil.getJSONarray(result, "scheduled_time");
                        if (scheduledTimeArray != null && scheduledTimeArray.length() > 0) {
                            for (int i = 0; i < scheduledTimeArray.length(); i++) {
                                if (scheduledTimeArray.get(i) instanceof Integer) {
                                    this.scheduled_time.add(((Integer) scheduledTimeArray.get(i)).longValue());
                                } else {
                                    this.scheduled_time.add((Long) scheduledTimeArray.get(i));
                                }
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    convertToInfo();
                    startLanConnect(this.lanIp, this.lanPort);
                } else {
                    //插拔电通知只有一个属性
                    this.charging = (boolean) MapUtils.get(tempResult, "charging", false);
                    convertToInfo();
                }

                break;
            case DsCamCmd.SET_MD:
                this.md = DDJSONUtil.getBoolean(result, "md");
                convertToInfo();
                break;
            case DsCamCmd.SET_GRAY:
                this.gray = DDJSONUtil.getBoolean(result, "gray");
                convertToInfo();
                break;
            case DsCamCmd.SET_MD_LEVEL:
                this.md_level = DDJSONUtil.getInt(result, "md_level");
                convertToInfo();
                break;
            case DsCamCmd.SET_PIR_LEVEL:
                this.pir_level = DDJSONUtil.getInt(result, "pir_level");
                convertToInfo();
                break;
            case DsCamCmd.SET_VFLIP:
                this.vflip = DDJSONUtil.getBoolean(result, "vflip");
                convertToInfo();
                break;
            case DsCamCmd.SET_HFLIP:
                this.hflip = DDJSONUtil.getBoolean(result, "hflip");
                convertToInfo();
                break;
            case DsCamCmd.SET_TZ:
                this.tz = DDJSONUtil.getString(result, "tz");
                convertToInfo();
                break;
            case DsCamCmd.FORMAT_TF:
                break;
            case DsCamCmd.RESTORE_DEFAULT:
                this.hd = (boolean) MapUtils.get(tempResult, "hd", false);
                this.vflip = (boolean) MapUtils.get(tempResult, "vflip", false);
                this.hflip = (boolean) MapUtils.get(tempResult, "hflip", false);
                this.tz = (String) MapUtils.get(tempResult, "tz", "");
                this.md = (boolean) MapUtils.get(tempResult, "md", false);
                this.md_level = DeviceHelper.getInt(tempResult, "md_level", -1);
                this.pir_level = DeviceHelper.getInt(tempResult, "pir_level", -1);
                this.mdBeginTime = (String) MapUtils.get(tempResult, "md_begin_time", "08:00");
                this.mdEndTime = (String) MapUtils.get(tempResult, "md_end_time", "23:00");
                convertToInfo();
                break;
            case DsCamCmd.RESET:
                owned = messageIdHolder.isMyMessageId(cmd, messageId) > 0;
                isDelete = true;
                reboot();
                if (owned) {
                    delete();
                }
                break;
            case DsCamCmd.SET_MD_TIME:
                this.panelFollow = false;
                this.mdBeginTime = (String) MapUtils.get(tempResult, "md_begin_time", "08:00");
                this.mdEndTime = (String) MapUtils.get(tempResult, "md_end_time", "23:00");
                convertToInfo();
                break;
            case DsCamCmd.KEEPALIVE:
                break;
            case DsCamCmd.LED_TEST:
                break;
            case DsCamCmd.SET_FACTORY:
            case DsCamCmd.GET_REGTOKEN:
                break;
            case DsCamCmd.IPC_VER_NUM_UPDATED:
                try {
                    com.alibaba.fastjson.JSONArray versionsArray = (com.alibaba.fastjson.JSONArray) MapUtils.get(tempResult, "versions", new JSONObject());
                    if (versionsArray != null && versionsArray.size() > 0) {
                        for (int i = 0; i < versionsArray.size(); i++) {
                            com.alibaba.fastjson.JSONObject jsonObject = versionsArray.getJSONObject(i);
                            versions.putAll(jsonObject.getInnerMap());
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (this.versions != null && this.versions.size() > 0) {
                    this.bleVersion = (String) MapUtils.get(this.versions, "ble", "");
                    this._3861Version = (String) MapUtils.get(this.versions, "3861", "");
                    this._3518AppVersion = (String) MapUtils.get(this.versions, "3518_app", "");
                    this._3518UbootVersion = (String) MapUtils.get(this.versions, "3518_uboot", "");
                    this._3518RootfsVersion = (String) MapUtils.get(this.versions, "3518_rootfs", "");
                }
                convertToInfo();
                break;
            case DsCamCmd.SET_VIDEO_MODE:
                this.videoMode = DDJSONUtil.getString(result, "video_mode");
                convertToInfo();
                break;
            case DsCamCmd.SET_MD_ALARM:
                this.md_alarm = DDJSONUtil.getBoolean(result, "md_alarm");
                convertToInfo();
                break;
            case DsCamCmd.SET_MD_FOLLOW:
                this.panelFollow = true;
                this.alertConArm = false;
                this.alertConDisarm = false;
                this.alertConHomeArm = false;
                this.alertConSos = false;
                try {
                    com.alibaba.fastjson.JSONArray follow_status = (com.alibaba.fastjson.JSONArray) MapUtils.get(tempResult, "follow_status", new JSONObject());
                    if (follow_status != null && follow_status.size() > 0) {
                        for (int i = 0; i < follow_status.size(); i++) {
                            String stateStr = follow_status.getString(i);
                            if ("sos".equals(stateStr)) {
                                this.alertConSos = true;
                            } else if ("arm".equalsIgnoreCase(stateStr)) {
                                this.alertConArm = true;
                            } else if ("disarm".equalsIgnoreCase(stateStr)) {
                                this.alertConDisarm = true;
                            } else if ("homearm".equalsIgnoreCase(stateStr)) {
                                this.alertConHomeArm = true;
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                convertToInfo();
                break;
            case DsCamCmd.SET_DAILY_MEMORIES:
                this.daily_memories = DDJSONUtil.getLong(result, "daily_memories");
                convertToInfo();
                break;
            case DsCamCmd.SET_SCHEDULED:
                this.scheduled = DDJSONUtil.getBoolean(result, "scheduled");
                this.scheduled_time.clear();
                com.alibaba.fastjson.JSONArray scheduledTimeArray = (com.alibaba.fastjson.JSONArray) MapUtils.get(tempResult, "scheduled_time", new JSONArray());
                if (scheduledTimeArray != null && scheduledTimeArray.size() > 0) {
                    for (int i = 0; i < scheduledTimeArray.size(); i++) {
                        if (scheduledTimeArray.get(i) instanceof Integer) {
                            this.scheduled_time.add(((Integer) scheduledTimeArray.get(i)).longValue());
                        } else {
                            this.scheduled_time.add((Long) scheduledTimeArray.get(i));
                        }
                    }
                }
                convertToInfo();
                break;
            default:
                break;
        }
        dispatchResult(cmd, resultMap);
    }


    public BaseCamDevice(IMultipleSender sender, String homeID, DsCamListResponse.ResultBean deviceBean, String subCategory) {
        super();
        initFlagOnNetwork();
        this.sender = sender;
        this.setId(deviceBean.getPid());
        this.setCategory(2);
        this.setSubCategory(subCategory);
        this.name = deviceBean.getName();
        this.receiveID = deviceBean.getEnd_id();
        this.group_id = deviceBean.getGroup_id();
        this.addtime = deviceBean.getAddtime();
        this.homeID = homeID;
        if (TextUtils.isEmpty(name)) {
            name = "ipc";
        }
        if (receiveID == null) {
            receiveID = "";
        }
        if (group_id == null) {
            group_id = "";
        }
        if (this.homeID == null) {
            this.homeID = "";
        }
        convertToInfo();
        kcpCallBack = new IKcpCallBack() {
            @Override
            public void onMessage(int conv, byte[] bytes) {
                byte[] msgb = bytes;
                try {
                    String j = new String(msgb);
                    MsctLog.v(TAG, "receive decode:" + j);
                    JSONObject jsonObject = new JSONObject(j);
                    handlerKcpResponse(jsonObject);
                } catch (JSONException e) {
                    e.printStackTrace();
                }

            }

            @Override
            public void onException(String s) {

            }

            @Override
            public void onClose() {
                MsctLog.i(TAG, "onClose");
                disconnect();
            }
        };
        channelManager = new ChannelManager(this, homeID, kcpCallBack);
        channelManager.setChannelCallBack(channelCallBack);
        sdCardRecordManager = new SDCardRecordManager(this);
        postNameUpdateEvent();
    }

    private void reboot() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsCamCmd.REBOOT);
            byte[] msg = jsonObject.toString().getBytes();
            sendByte(msg);
        } catch (JSONException e) {

        }
    }


    protected void convertToInfo() {
//       有几个字段要暴露出去
//        插座：
//        1. name
//        2. networkState
        Map jsonObject = this.getInfo();
        jsonObject.put("name", name);
        jsonObject.put("networkState", connectStatus);
        jsonObject.put("addtime", this.addtime);
        jsonObject.put("hd", this.hd);
        jsonObject.put("vflip", this.vflip);
        jsonObject.put("hflip", this.hflip);
        jsonObject.put("tz", this.tz);
        jsonObject.put("ssid", this.ssid);
        jsonObject.put("ip", this.ip);
        jsonObject.put("battery", this.battery);
        jsonObject.put("charging", this.charging);
        jsonObject.put("tfCapacity", this.tf_capacity);
        jsonObject.put("tfUsed", this.tf_used);
        jsonObject.put("md", this.md);
        jsonObject.put("mdLevel", this.md_level);
        jsonObject.put("pirLevel", this.pir_level);
        jsonObject.put("mdBeginTime", this.mdBeginTime);
        jsonObject.put("mdEndTime", this.mdEndTime);
        jsonObject.put("alertMode", this.alertMode);
        jsonObject.put("cloudStorage", this.cloudStorage);
        jsonObject.put(DsCamConst.ALERT_CON_ARM, this.alertConArm);
        jsonObject.put(DsCamConst.ALERT_CON_DISARM, this.alertConDisarm);
        jsonObject.put(DsCamConst.ALERT_CON_HOME_ARM, this.alertConHomeArm);
        jsonObject.put(DsCamConst.ALERT_CON_SOS, this.alertConSos);
        jsonObject.put(DsCamConst.PANEL_FOLLOW, this.panelFollow);
        jsonObject.put("mac", this.mac);
        jsonObject.put("rssi", this.rssi);
        jsonObject.put("gray", this.gray);
        jsonObject.put("chip", this.chip);
        jsonObject.put("versions", this.versions);
        jsonObject.put("ble", this.bleVersion);
        jsonObject.put("3861", this._3861Version);
        jsonObject.put("3518_app", this._3518AppVersion);
        jsonObject.put("3518_uboot", this._3518UbootVersion);
        jsonObject.put("3518_rootfs", this._3518RootfsVersion);
        jsonObject.put("floodlight", this.floodlight);
        jsonObject.put("auto_floodlight", this.auto_floodlight);
        jsonObject.put("md_alarm", this.md_alarm);
        jsonObject.put("daily_memories", this.daily_memories);
        jsonObject.put("scheduled", this.scheduled);
        jsonObject.put("scheduled_time", this.scheduled_time);
    }

    @Override
    public void submit(Map arg) {
        String cmd = (String) arg.get("cmd");
        switch (cmd) {
            case DsCamCmd.SET_NAME:
                setName((String) arg.get("name"));
                break;
            case DsCamCmd.DELETE_DEVICE:
                delete();
                break;
            case DsCamCmd.CONNECT:
                final boolean discardCache = DeviceHelper.getBoolean(arg, "discardCache", false);
                connect(discardCache);
                break;
            case DsCamCmd.DISCONNECT:
                disconnect();
                break;
            case DsCamCmd.GET_PARAMS:
                getIPCInfo();
                break;
            case DsCamCmd.SET_ALERT_MODE:
                setAlertMode(arg);
                break;
            case DsCamCmd.SET_MD_FOLLOW:
                setMdFollow(arg);
                break;
            case DsCamCmd.GET_ALERT_MODE:
                getAlertMode();
                break;
            case DsCamCmd.SET_TZ:
                snycTimeZone();
                break;
            case DsCamCmd.GET_RECORD_LIST:
            case DsCamCmd.GET_RECORD_LIST_V2:
                getRecordList(cmd, arg);
                break;
            case DsCamCmd.GET_RECORD_FILE:
                sdCardRecordManager.requestRecordFile(arg);
                break;
            case DsCamCmd.STOP_RECORD_FILE_FETCH:
                sdCardRecordManager.clearAllDownloadTask();
                break;
            case DsCamCmd.DEL_RECORD_FILE:
                deleteRecordFile(arg);
                break;
            case DsCamCmd.SET_PIR_LEVEL:
                setPirLevel(arg);
                break;
            case DsCamCmd.UPGRADE_FIRMWARE:
                upgradeFirmware(arg);
                break;
            case DsCamCmd.RESET:
                reset(arg);
                break;
            case DsCamCmd.SET_SCHEDULED:
                setScheduled(arg);
                break;
            case DsCamCmd.SET_MD:
            case DsCamCmd.SET_MD_LEVEL:
            case DsCamCmd.SET_VFLIP:
            case DsCamCmd.SET_HFLIP:
            case DsCamCmd.FORMAT_TF:
            case DsCamCmd.RESTORE_DEFAULT:
            case DsCamCmd.SET_GRAY:
            case DsCamCmd.SET_MD_TIME:
            case DsCamCmd.LED_TEST:
            case DsCamCmd.SET_FACTORY:
            case DsCamCmd.GET_REGTOKEN:
            case DsCamCmd.SET_VIDEO_MODE:
            case DsCamCmd.REBOOT:
            case DsCamV006Cmd.SET_AUTO_FLOODLIGHT:
            case DsCamV006Cmd.SET_FLOODLIGHT:
            case DsCamCmd.SET_MD_ALARM:
            case DsCamCmd.SET_DAILY_MEMORIES:
                setCam(arg);
                break;
            default: {
                Map result = Cmd.getDefaultResultMap(false, null);
                result.put("errorMessage", "cmd:" + cmd + " not support");
                dispatchResult(cmd, result);
            }

        }
    }

    private void setScheduled(Map arg) {
        JSONObject jsonObject = new JSONObject();
        try {
            JSONArray timeArray = new JSONArray(((List<Long>) MapUtils.get(arg, "scheduled_time", new ArrayList<>())));
            jsonObject.put("cmd", DsCamCmd.SET_SCHEDULED);
            jsonObject.put("scheduled", MapUtils.get(arg, "scheduled", false));
            jsonObject.put("scheduled_time", timeArray);
            DDLog.i(TAG, "setScheduled: " + jsonObject.toString());
            byte[] msg = jsonObject.toString().getBytes();
            sendByte(msg);
        } catch (JSONException e) {
            Map result = Cmd.getDefaultResultMap(false, DsCamCmd.SET_SCHEDULED);
            result.put("errorMessage", "params error");
            dispatchResult(DsCamCmd.SET_SCHEDULED, result);
        }

    }

    /**
     * 设置移动帧测跟随主机状态
     */
    private void setMdFollow(Map arg) {
        final String panelStatus = DeviceHelper.getString(arg, DsCamConst.PANEL_STATUS, "disarm");
        JSONArray status = new JSONArray();
        if (arg.containsKey(DsCamConst.ALERT_CON_ARM)) {
            status.put("arm");
        }
        if (arg.containsKey(DsCamConst.ALERT_CON_DISARM)) {
            status.put("disarm");
        }
        if (arg.containsKey(DsCamConst.ALERT_CON_HOME_ARM)) {
            status.put("homearm");
        }
        if (arg.containsKey(DsCamConst.ALERT_CON_SOS)) {
            status.put("sos");
        }
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsCamCmd.SET_MD_FOLLOW);
            jsonObject.put("follow_status", status);
            jsonObject.put("panel_status", panelStatus);
            byte[] msg = jsonObject.toString().getBytes();
            sendByte(msg);
        } catch (JSONException e) {
            Map result = Cmd.getDefaultResultMap(false, DsCamCmd.SET_MD_FOLLOW);
            result.put("errorMessage", "params error");
            dispatchResult(DsCamCmd.SET_MD_FOLLOW, result);
        }
    }

    private void snycTimeZone() {
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("GMT"),
                Locale.getDefault());
        Date currentLocalTime = calendar.getTime();

        DateFormat date = new SimpleDateFormat("ZZZZZ", Locale.getDefault());
        String localTime = date.format(currentLocalTime);
        localTime = "tzn" + localTime + ":00";
        DDLog.i("timezone", localTime);
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsCamCmd.SET_TZ);
            jsonObject.put("tz", localTime);
            byte[] msg = jsonObject.toString().getBytes();
            sendByte(msg);
        } catch (JSONException e) {
            Map result = Cmd.getDefaultResultMap(false, DsCamCmd.SET_TZ);
            result.put("errorMessage", "params error");
            dispatchResult(DsCamCmd.SET_TZ, result);
        }

    }

    private void getAlertMode() {
        DsCamApi.getInstance().getAlertMode(homeID, getId(), getSubCategory().toUpperCase())
                .enqueue(new Callback<DsCamAlertModeResponse>() {
                    @Override
                    public void onResponse(Call<DsCamAlertModeResponse> call, Response<DsCamAlertModeResponse> response) {
                        DsCamAlertModeResponse dsCamAlertModeResponse = response.body();
                        if (null != dsCamAlertModeResponse && null != dsCamAlertModeResponse.getResult()) {
                            final DsCamAlertModeResponse.ResultBean result = dsCamAlertModeResponse.getResult();
                            alertMode = result.getAlert_mode();
                            cloudStorage = result.isCloud_storage();
                            convertToInfo();
                        }
                        Map result = Cmd.getDefaultResultMap(true, DsCamCmd.GET_ALERT_MODE);
                        result.put("alert_mode", alertMode);
                        result.put("cloud_storage", cloudStorage);
                        dispatchResult(DsCamCmd.GET_ALERT_MODE, result);
                    }

                    @Override
                    public void onFailure(Call<DsCamAlertModeResponse> call, Throwable t) {
                        Map result = Cmd.getDefaultResultMap(false, DsCamCmd.GET_ALERT_MODE);
                        result.put("errorMessage", "sender is null");
                        dispatchResult(DsCamCmd.GET_ALERT_MODE, result);
                    }
                });
    }

    private void setAlertMode(Map arg) {
        final String localAlertMode = DeviceHelper.getString(arg, "alert_mode", "");
        final boolean localCloudStorage = DeviceHelper.getBoolean(arg, "cloud_storage", false);
        DsCamApi.getInstance().setAlertMode(homeID, getId(), getSubCategory().toUpperCase(),
                        localAlertMode, localCloudStorage, null, null, null, null)
                .enqueue(new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                        alertMode = localAlertMode;
                        if ("critical".equals(alertMode)) {
                            alertMode = "normal";
                        }
                        cloudStorage = localCloudStorage;
                        convertToInfo();
                        Map result = Cmd.getDefaultResultMap(true, DsCamCmd.SET_ALERT_MODE);
                        dispatchResult(DsCamCmd.SET_ALERT_MODE, result);
                    }

                    @Override
                    public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                        Map result = Cmd.getDefaultResultMap(false, DsCamCmd.SET_ALERT_MODE);
                        result.put("errorMessage", "sender is null");
                        dispatchResult(DsCamCmd.SET_ALERT_MODE, result);
                    }
                });
    }

    //    直接把使用者传过来的数据，发给ipc即可，不需要中途处理
    private void setCam(Map arg) {
        String par = MapUtils.toJson(arg);
        //DDLog.i(TAG, String.format("%s send cmd: %s", getId(), par));
        byte[] msg = par.getBytes();
        sendByte(msg);
    }

    private void getIPCInfo() {

        if (!channelManager.isConnect()) {
            Map result = Cmd.getDefaultResultMap(false, DsCamCmd.GET_PARAMS);
            result.put("errorMessage", "cam is no connect");
            dispatchResult(DsCamCmd.GET_PARAMS, result);
            return;
        }
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsCamCmd.GET_PARAMS);
            jsonObject.put("client", "Android");
            byte[] msg = jsonObject.toString().getBytes();
            sendByte(msg);
        } catch (JSONException e) {
            Map result = Cmd.getDefaultResultMap(false, DsCamCmd.GET_PARAMS);
            result.put("errorMessage", "params error");
            dispatchResult(DsCamCmd.GET_PARAMS, result);
        }
    }

    /**
     * 获取SDK录像列表
     * "cmd":"set_pir_level",  // string, 指令
     * "pir_level": 1          // int PIR等级
     * // 1:低
     * // 2:中
     * // 3:高
     * }
     */
    public void setPirLevel(Map arg) {
        if (!channelManager.isConnect()) {
            Map result = Cmd.getDefaultResultMap(false, DsCamCmd.SET_PIR_LEVEL);
            result.put("errorMessage", "cam is no connect");
            dispatchResult(DsCamCmd.SET_PIR_LEVEL, result);
            return;
        }
        JSONObject jsonObject = new JSONObject();
        try {
            int pirLevel = DeviceHelper.getInt(arg, "pirLevel", 1);
            jsonObject.put("cmd", DsCamCmd.SET_PIR_LEVEL);
            jsonObject.put("pir_level", pirLevel);
            byte[] msg = jsonObject.toString().getBytes();
            sendByte(msg);
        } catch (JSONException e) {
            Map result = Cmd.getDefaultResultMap(false, DsCamCmd.SET_PIR_LEVEL);
            result.put("errorMessage", "params error");
            dispatchResult(DsCamCmd.SET_PIR_LEVEL, result);
        }
    }

    /**
     * 获取SDK录像列表
     * "cmd":"get_record_list_v2",   // string 指令
     * "begin": ""                // string，第一次进列表，值为空字符串，表示取最新的10条录像
     */
    public void getRecordList(String cmd, Map arg) {
        if (!channelManager.isConnect()) {
            Map result = Cmd.getDefaultResultMap(false, cmd);
            result.put("errorMessage", "cam is no connect");
            result.put("owned", true);
            dispatchResult(cmd, result);
            return;
        }
        JSONObject jsonObject = new JSONObject();
        try {
            final String messageId = messageIdHolder.createMessageId(cmd);
            String begin = DeviceHelper.getString(arg, "begin", "");
            jsonObject.put("cmd", cmd);
            jsonObject.put("begin", begin);
            jsonObject.put("messageid", messageId);
            byte[] msg = jsonObject.toString().getBytes();
            sendByte(msg);
        } catch (JSONException e) {
            Map result = Cmd.getDefaultResultMap(false, cmd);
            result.put("errorMessage", "params error");
            result.put("owned", true);
            dispatchResult(cmd, result);
        }
    }

    /**
     * 删除SDK录像
     * "cmd": "del_record_file",		// string, 指令
     * "file": "2021/11/17/08_41_34" // string 要删除的文件，ipc删除mp4和jpg
     */
    public void deleteRecordFile(Map arg) {
        if (!channelManager.isConnect()) {
            Map result = Cmd.getDefaultResultMap(false, DsCamCmd.DEL_RECORD_FILE);
            result.put("errorMessage", "cam is no connect");
            result.put("owned", true);
            dispatchResult(DsCamCmd.DEL_RECORD_FILE, result);
            return;
        }
        JSONObject jsonObject = new JSONObject();
        try {
            final String messageId = messageIdHolder.createMessageId(DsCamCmd.DEL_RECORD_FILE);
            String fileName = DeviceHelper.getString(arg, "file", "");
            jsonObject.put("cmd", DsCamCmd.DEL_RECORD_FILE);
            jsonObject.put("file", fileName);
            jsonObject.put("messageid", messageId);
            byte[] msg = jsonObject.toString().getBytes();
            sendByte(msg);
        } catch (JSONException e) {
            Map result = Cmd.getDefaultResultMap(false, DsCamCmd.DEL_RECORD_FILE);
            result.put("errorMessage", "params error");
            result.put("owned", true);
            dispatchResult(DsCamCmd.DEL_RECORD_FILE, result);
        }
    }

    /**
     * 升级固件
     *
     * @param arg
     */
    private void upgradeFirmware(Map arg) {
        if (!channelManager.isConnect()) {
            Map result = Cmd.getDefaultResultMap(false, DsCamCmd.UPGRADE_FIRMWARE);
            result.put("errorMessage", "cam is no connect");
            result.put("owned", true);
            dispatchResult(DsCamCmd.UPGRADE_FIRMWARE, result);
            return;
        }
        String url = DeviceHelper.getString(arg, "url", "");
        String md5 = DeviceHelper.getString(arg, "md5", "");
        String type = DeviceHelper.getString(arg, "type", "");
        if (TextUtils.isEmpty(url) || TextUtils.isEmpty(md5) || TextUtils.isEmpty(type)) {
            Map result = Cmd.getDefaultResultMap(false, DsCamCmd.UPGRADE_FIRMWARE);
            result.put("errorMessage", "request parm error");
            result.put("owned", true);
            dispatchResult(DsCamCmd.UPGRADE_FIRMWARE, result);
            return;
        }
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsCamCmd.UPGRADE_FIRMWARE);
            jsonObject.put("url", url);
            jsonObject.put("md5", md5);
            jsonObject.put("type", type);
            jsonObject.put("messageid", messageIdHolder.createMessageId(DsCamCmd.UPGRADE_FIRMWARE));
            MsctLog.d(TAG, "upgradeFirmware: " + jsonObject.toString());
            byte[] msg = jsonObject.toString().getBytes();
            sendByte(msg);
            this.bleVersion = "";
            this._3861Version = "";
            this._3518AppVersion = "";
            this._3518UbootVersion = "";
            this._3518RootfsVersion = "";
            this.versions = new LinkedHashMap();
            convertToInfo();
        } catch (JSONException e) {
            Map result = Cmd.getDefaultResultMap(false, DsCamCmd.UPGRADE_FIRMWARE);
            result.put("errorMessage", "params error");
            result.put("owned", true);
            dispatchResult(DsCamCmd.UPGRADE_FIRMWARE, result);
        }

    }

    /**
     * 重置IPC
     *
     * @param arg
     */
    private void reset(Map arg) {
        MsctLog.i(TAG, "reset");
        Map resetMap = arg;
        resetMap.put("messageid", messageIdHolder.createMessageId(DsCamCmd.RESET));
        String par = MapUtils.toJson(arg);
        byte[] msg = par.getBytes();
        sendByte(msg);
    }


    public void disconnect() {
        channelManager.disconnect();
    }

    private void delete() {
        MsctLog.i(TAG, " delete");
        if (getFlagDeleted()) {
            MsctLog.i(TAG, "删除ipc成功");
            Map result = Cmd.getDefaultResultMap(true, DsCamCmd.DELETE_DEVICE);
            dispatchResult(DsCamCmd.DELETE_DEVICE, result);
            isDelete = true;
            disconnect();
            remove();
            return;
        }

        DsCamApi.getInstance().deleteIpc(homeID, getId(), getSubCategory().toUpperCase()).enqueue(new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                MsctLog.i(TAG, "删除ipc成功");
                Map result = Cmd.getDefaultResultMap(true, DsCamCmd.DELETE_DEVICE);
                dispatchResult(DsCamCmd.DELETE_DEVICE, result);
                isDelete = true;
                disconnect();
                remove();
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                MsctLog.i(TAG, "删除ipc失败：" + t.getCause());
                Map result = Cmd.getDefaultResultMap(false, DsCamCmd.DELETE_DEVICE);
                if (t instanceof NetWorkException) {
                    result.put("errorMessage", ((NetWorkException) t).getMsgDes());
                    Cmd.putResultValue(result, "originStatus", ((NetWorkException) t).getStatus());
                } else {
                    result.put("errorMessage", "mes:" + t.getMessage());
                }
                dispatchResult(DsCamCmd.DELETE_DEVICE, result);
            }
        });
    }

    public void deleteDirect(final boolean notify) {
        isDelete = true;
        disconnect();
        destory();
        if (notify) {
            remove();
        }
    }


    private void setName(String newname) {
        DsCamApi.getInstance().setName(homeID, getId(), getSubCategory().toUpperCase(), newname).enqueue(new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                name = newname;
                convertToInfo();
                Map result = Cmd.getDefaultResultMap(true, DsCamCmd.SET_NAME);
                dispatchResult(DsCamCmd.SET_NAME, result);
                postNameUpdateEvent();
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                Map result = Cmd.getDefaultResultMap(false, DsCamCmd.SET_NAME);
                result.put("errorMessage", "sender is null");
                dispatchResult(DsCamCmd.SET_NAME, result);
            }
        });
    }

    public void needConnectAgain() {
        setFlagLoaded(false);
        setFlagLoading(false);
    }

    public void setNameDirect(String newname) {
        name = newname;
        if (TextUtils.isEmpty(name)) {
            name = "ipc";
        }
        convertToInfo();
        Map result = Cmd.getDefaultResultMap(true, DsCamCmd.SET_NAME);
        dispatchResult(DsCamCmd.SET_NAME, result);
        postNameUpdateEvent();
    }

    protected void sendByte(byte[] msg) {
        Channel channel = channelManager.getChannel();
        if (channel != null) {
            channel.kcpCmdSend(msg);
        } else {
            MsctLog.e(TAG, "want to send something but no channel connect");
        }

    }

    public void onSearchIpcInfo(@Nullable SearchIpcResponse.ResultBean.ListBean info, final boolean success) {
        MsctLog.i(TAG, getId() + ": onSearchIpcInfo, success: " + success);
        if (!success) {
            setFlagLoading(false);
            setFlagLoaded(false);
            realConnect();
            return;
        }

        setFlagLoading(false);
        final String currentId = getId();
        if (null != info && currentId.equals(info.getPid())) {
            addtime = info.getAddtime();
            receiveID = info.getEnd_id();
            group_id = info.getGroup_id();
            name = info.getName();
            convertToInfo();
            realConnect();
            postNameUpdateEvent();
        } else {
            setFlagDeleted(true);
        }

        // 这个要放在最后，否则外面会短时间切换为离线状态
        setFlagLoaded(true);
    }

    private void postNameUpdateEvent() {
        if (!TextUtils.isEmpty(name)) {
            EventBus.getDefault().post(new DsDeviceNameUpdateEvent(getId(), name));
        }
    }

    private void connect(final boolean discardCache) {
        if (discardCache) {
            MsctLog.i(TAG, getId() + ": connect- discardCache = true");
            setFlagLoaded(false);
            setFlagLoading(true);
            SearchIpcHelper.get().startSearchIpcSingle(homeID, this);
            return;
        }

        if (channelManager.isConnect()) {
            MsctLog.i(TAG, getId() + ": connect- is connected");
            return;
        }

        if (getFlagLoading()) {
            MsctLog.i(TAG, getId() + ": connect- loading flag is true");
            return;
        }

        if (getFlagLoaded()) {
            realConnect();
            return;
        }

        setFlagLoading(true);
        SearchIpcHelper.get().addTask(homeID, this);
    }

    private void realConnect() {
        MsctLog.e(TAG, "ipc connect groupID:" + group_id);
        Channel channel = channelManager.getChannel(Channel.PROXY);
        ProxyChannel proxyChannel = (ProxyChannel) channel;
        proxyChannel.setGroup_id(group_id);
        proxyChannel.setReceiveID(receiveID);
        channel.connect();
//        p2p通道得等proxy通道通了才可以
//        Channel p2pChannel = channelManager.getChannel(Channel.P2P);
//        p2pChannel.connect();
    }

    public void createKcp(int type, int sessionID, IKcpCreateCallBack kcpCreateCallBack) {
        channelManager.createKcp(type, sessionID, kcpCreateCallBack);
    }

    public void removeKcp(int conv) {
        channelManager.removeKcp(conv);
    }


    public IMultipleSender getMultiSender() {
        return this.sender;
    }

    public void startLanConnect(String ip, int port) {
//         开始连接lan
        LanChannel lanChannel = (LanChannel) channelManager.getChannel(Channel.LAN);
        if (lanChannel.isConnect()) {
            return;
        }
        if (TextUtils.isEmpty(ip) || port <= 0) {
            return;
        }
        this.lanIp = ip;
        this.lanPort = port;
        executorService.submit(new Runnable() {
            @Override
            public void run() {
                synchronized (channelCallBack) {
                    LanChannel lanChannel = (LanChannel) channelManager.getChannel(Channel.LAN);
                    lanChannel.setIp(lanIp);
                    lanChannel.setPort(lanPort);
//                    找到ip之后，尝试连，有可能失败，可能还没有chart_secret
                    if (channelManager.isConnect()) {
                        lanChannel.connect();
                    }
                }
            }
        });
    }

    @Override
    public void destory() {
        super.destory();
        sdCardRecordManager.clearAllDownloadTask();
        channelManager.destory();
        messageIdHolder.cleanAllMessageId();
    }

    public void onRemoteAddressUpdated() {
        MsctLog.i("RemoteAddressUpdated", getId() + "onRemoteAddressUpdated");
        // 断开并释放切换节点之前的连接
        disconnect();
        // 使用新节点进行连接
        connect(false);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BaseCamDevice cacheInfo = (BaseCamDevice) o;
        return Objects.equals(getId(), cacheInfo.getId())
                && Objects.equals(homeID, cacheInfo.homeID);
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId(), homeID);
    }

    public void updateSender(IMultipleSender multipleSender) {
        if (null != multipleSender && this.sender != multipleSender) {
            DDLog.i(TAG, "updateSender");
            final boolean needAutoConnect = 1 == connectStatus || 0 == connectStatus;
            if (null != channelManager) {
                channelManager.destory();
                channelManager.setChannelCallBack(null);
            }

            this.sender = multipleSender;
            channelManager = new ChannelManager(this, homeID, kcpCallBack);
            channelManager.setChannelCallBack(channelCallBack);
            if (needAutoConnect) {
                DDLog.i(TAG, "auto connect on updateSender");
                connect(false);
            }
        }
    }

    @Override
    @RestrictTo(RestrictTo.Scope.LIBRARY)
    public void dispatchResult(String cmd, Map result) {
        super.dispatchResult(cmd, result);
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY)
    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    @RestrictTo(RestrictTo.Scope.LIBRARY)
    public void setFlagDeleted(boolean isDeleted) {
        super.setFlagDeleted(isDeleted);
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY)
    @Override
    public void setFlagCache(boolean isCache) {
        super.setFlagCache(isCache);
    }
}
