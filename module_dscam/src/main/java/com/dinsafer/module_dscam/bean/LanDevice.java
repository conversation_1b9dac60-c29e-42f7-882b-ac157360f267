package com.dinsafer.module_dscam.bean;

import androidx.annotation.Nullable;

public class LanDevice {
    String ip;
    int port;
    String deviceID;

    public LanDevice(String ip, int port, String deviceID) {
        this.ip = ip;
        this.port = port;
        this.deviceID = deviceID;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getDeviceID() {
        return deviceID;
    }

    public void setDeviceID(String deviceID) {
        this.deviceID = deviceID;
    }

    @Override
    public int hashCode() {
        return this.port    ;
    }

    @Override
    public boolean equals(@Nullable Object obj) {
        return this.deviceID.equals(((LanDevice) obj).deviceID) &&
                this.ip.equals(((LanDevice) obj).ip) &&
                this.port == ((LanDevice) obj).port;
    }
}
