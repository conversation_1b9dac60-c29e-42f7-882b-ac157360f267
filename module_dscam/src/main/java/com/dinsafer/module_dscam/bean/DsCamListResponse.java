package com.dinsafer.module_dscam.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;
import java.util.List;

@Keep
public class DsCamListResponse extends BaseHttpEntry implements Serializable {


    /**
     * Cmd :
     * Result : [{"name":"","pid":"","group_id":"","end_id":"","addtime":1621580076757000000}]
     */

    private List<ResultBean> Result;

    public List<ResultBean> getResult() {
        return Result;
    }

    public void setResult(List<ResultBean> Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean implements Serializable {
        /**
         * name :
         * pid :
         * group_id :
         * end_id :
         * addtime : 1621580076757000000
         */

        private String name;
        private String pid;
        private String group_id;
        private String end_id;
        private String addr;
        private Long addtime;
        private String provider;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPid() {
            return pid;
        }

        public void setPid(String pid) {
            this.pid = pid;
        }

        public String getGroup_id() {
            return group_id;
        }

        public void setGroup_id(String group_id) {
            this.group_id = group_id;
        }

        public String getEnd_id() {
            return end_id;
        }

        public void setEnd_id(String end_id) {
            this.end_id = end_id;
        }

        public Long getAddtime() {
            return addtime;
        }

        public void setAddtime(Long addtime) {
            this.addtime = addtime;
        }

        public String getAddr() {
            return addr;
        }

        public void setAddr(String addr) {
            this.addr = addr;
        }

        public String getProvider() {
            return provider;
        }

        public void setProvider(String provider) {
            this.provider = provider;
        }
    }
}
