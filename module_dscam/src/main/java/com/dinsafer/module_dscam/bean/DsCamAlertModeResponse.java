package com.dinsafer.module_dscam.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

@Keep
public class DsCamAlertModeResponse extends BaseHttpEntry {

    /**
     * Cmd :
     * Result : {"alert_mode":"normal"}
     */

    private String Cmd;
    private ResultBean Result;

    public String getCmd() {
        return Cmd;
    }

    public void setCmd(String Cmd) {
        this.Cmd = Cmd;
    }

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        /**
         * alert_mode : normal
         */

        private String alert_mode;

        private boolean cloud_storage;

        private Boolean arm;
        private Boolean disarm;
        private Boolean home_arm;
        private Boolean sos;

        public String getAlert_mode() {
            return alert_mode;
        }

        public void setAlert_mode(String alert_mode) {
            this.alert_mode = alert_mode;
        }

        public boolean isCloud_storage() {
            return cloud_storage;
        }

        public void setCloud_storage(boolean cloud_storage) {
            this.cloud_storage = cloud_storage;
        }

        public Boolean getArm() {
            return arm;
        }

        public void setArm(Boolean arm) {
            this.arm = arm;
        }

        public Boolean getDisarm() {
            return disarm;
        }

        public void setDisarm(Boolean disarm) {
            this.disarm = disarm;
        }

        public Boolean getHome_arm() {
            return home_arm;
        }

        public void setHome_arm(Boolean home_arm) {
            this.home_arm = home_arm;
        }

        public Boolean getSos() {
            return sos;
        }

        public void setSos(Boolean sos) {
            this.sos = sos;
        }
    }
}
