package com.dinsafer.module_dscam.bean;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.dinsafer.dssupport.msctlib.netty.IMultipleSender;

@Keep
public class DsCamDevice extends BaseCamDevice {

    public DsCamDevice(IMultipleSender sender, String pid, String homeID, String receiveID, String group_id, Long addtime) {
        super(sender, pid, homeID, receiveID, group_id, addtime, "dscam");
    }

    public DsCamDevice(IMultipleSender sender, String homeID, DsCamListResponse.ResultBean deviceBean) {
        super(sender, homeID, deviceBean, "dscam");
    }

    public DsCamDevice(IMultipleSender sender, String homeID, @NonNull final DsCamCacheInfo.CacheInfo cacheInfo) {
        super(sender, homeID, cacheInfo, "dscam");
    }
}