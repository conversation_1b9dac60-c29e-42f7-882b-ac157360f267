package com.dinsafer.module_dscam.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;
import java.util.List;

@Keep
public class DsCamE2ELoginResponse extends BaseHttpEntry implements Serializable {


    /**
     * Cmd :
     * Result : [{"pid":"","group_id":"","end_id":"","end_secret":"","chat_secret":"","addtime":1621580076757000000}]
     */

    private List<ResultBean> Result;

    public List<ResultBean> getResult() {
        return Result;
    }

    public void setResult(List<ResultBean> Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean implements Serializable {
        /**
         * pid :
         * group_id :
         * end_id :
         * end_secret :
         * chat_secret :
         * addtime : 1621580076757000000
         */

        private String pid;
        private String group_id;
        private String end_id;
        private String end_secret;
        private String chat_secret;
        private Long addtime;

        public String getPid() {
            return pid;
        }

        public void setPid(String pid) {
            this.pid = pid;
        }

        public String getGroup_id() {
            return group_id;
        }

        public void setGroup_id(String group_id) {
            this.group_id = group_id;
        }

        public String getEnd_id() {
            return end_id;
        }

        public void setEnd_id(String end_id) {
            this.end_id = end_id;
        }

        public String getEnd_secret() {
            return end_secret;
        }

        public void setEnd_secret(String end_secret) {
            this.end_secret = end_secret;
        }

        public String getChat_secret() {
            return chat_secret;
        }

        public void setChat_secret(String chat_secret) {
            this.chat_secret = chat_secret;
        }

        public Long getAddtime() {
            return addtime;
        }

        public void setAddtime(Long addtime) {
            this.addtime = addtime;
        }
    }
}
