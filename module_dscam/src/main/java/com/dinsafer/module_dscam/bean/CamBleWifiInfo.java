package com.dinsafer.module_dscam.bean;

import androidx.annotation.Keep;

/**
 * Cam扫描到的WIFI信息
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2023/3/7 16:57
 */
@Keep
public class CamBleWifiInfo {
    private final String ssid;
    // 默认无信号强度
    private int rssi = 0;

    private boolean auth = false;

    public CamBleWifiInfo(String ssid) {
        this.ssid = ssid;
    }

    public String getSsid() {
        return ssid;
    }

    public void setAuth(boolean auth) {
        this.auth = auth;
    }

    public void setRssi(int rssi) {
        this.rssi = rssi;
    }

    public int getRssi() {
        return rssi;
    }

    public boolean isAuth() {
        return auth;
    }

    @Override
    public String toString() {
        return "CamBleWifiInfo{" +
                "ssid='" + ssid + '\'' +
                ", rssi=" + rssi +
                ", auth=" + auth +
                '}';
    }
}
