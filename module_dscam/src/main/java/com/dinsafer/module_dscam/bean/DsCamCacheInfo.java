package com.dinsafer.module_dscam.bean;

import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dincore.db.cache.ICacheInfo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/11/26 16:50
 */
@Keep
public class DsCamCacheInfo implements ICacheInfo {

    private static final long serialVersionUID = -987137068422315688L;

    private long dscamAddTime;
    private long dsDoorbellAddTime;
    private long dscamV006AddTime;

    private List<CacheInfo> cacheList;

    public DsCamCacheInfo() {
        this.cacheList = new ArrayList<>();
    }

    public boolean isCacheReady() {
        return dscamAddTime > 0 || dsDoorbellAddTime > 0 || dscamV006AddTime > 0;
    }

    public long getCommonAddTime() {
        return Math.min(Math.min(dscamAddTime, dscamV006AddTime), dsDoorbellAddTime);
    }

    public long getNewestAddTime() {
        long newestAddTime = 0L;
        if (0 < cacheList.size()) {
            for (CacheInfo cacheInfo : cacheList) {
                long addTime = cacheInfo.getAddTime();
                if (addTime > newestAddTime) {
                    newestAddTime = addTime;
                }
            }
        }
        return newestAddTime;
    }

    public void setCommonAddTime(long commonAddTime) {
        this.dscamAddTime = commonAddTime;
        this.dscamV006AddTime = commonAddTime;
        this.dsDoorbellAddTime = commonAddTime;
    }

    public long getDscamAddTime() {
        return dscamAddTime;
    }

    public void setDscamAddTime(long dscamAddTime) {
        this.dscamAddTime = dscamAddTime;
    }

    public long getDsDoorbellAddTime() {
        return dsDoorbellAddTime;
    }

    public void setDsDoorbellAddTime(long dsDoorbellAddTime) {
        this.dsDoorbellAddTime = dsDoorbellAddTime;
    }

    public long getDscamV006AddTime() {
        return dscamV006AddTime;
    }

    public void setDscamV006AddTime(long dscamV006AddTime) {
        this.dscamV006AddTime = dscamV006AddTime;
    }

    public boolean addDevice(final String pid, String provider) {
        final CacheInfo newCache = new CacheInfo(pid, provider);
        return addDevice(newCache);
    }

    public boolean addDevice(CacheInfo cacheInfo) {
        if (null != cacheInfo && !TextUtils.isEmpty(cacheInfo.getPid()) && !cacheList.contains(cacheInfo)) {
            cacheList.add(cacheInfo);
            return true;
        }
        return false;
    }

    public boolean removeDevice(final String pid, @Nullable final String provider) {
        CacheInfo newCache = new CacheInfo(pid, provider);
        if (!TextUtils.isEmpty(pid)) {
            Iterator<CacheInfo> it = cacheList.iterator();
            while (it.hasNext()) {
                if (it.next().getPid().equals(newCache.getPid())) {
                    it.remove();
                    return true;
                }
            }
        }
        return false;
    }

    public void updateFrom(@Nullable final DsCamCacheInfo src) {
        this.cacheList.clear();
        if (null == src) {
            dscamAddTime = 0;
            dscamV006AddTime = 0;
            dsDoorbellAddTime = 0;
            return;
        }

        dscamAddTime = src.dscamAddTime;
        dscamV006AddTime = src.dscamV006AddTime;
        dsDoorbellAddTime = src.dsDoorbellAddTime;
        if (null != src.cacheList) {
            this.cacheList.addAll(src.cacheList);
        }
        if (null == this.cacheList || cacheList.size() == 0) {
            dscamAddTime = 1;
            dscamV006AddTime = 1;
            dsDoorbellAddTime = 1;
        }
    }

    public List<CacheInfo> getCacheList() {
        return cacheList;
    }

    public boolean isCacheEmpty() {
        return null == cacheList || cacheList.size() == 0;
    }

    @Override
    public String toString() {
        return "DsCamCacheInfo{" +
                "dscamAddTime=" + dscamAddTime +
                ", dsDoorbellAddTime=" + dsDoorbellAddTime +
                ", dscamV006AddTime=" + dscamV006AddTime +
                ", cacheList=" + cacheList +
                '}';
    }

    @Override
    public boolean isNeedSaveCache() {
        return true;
    }

    /**
     * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
     * @version 1.0
     * @since 2022/11/26 19:31
     */
    @Keep
    public static final class CacheInfo implements Serializable {
        private static final long serialVersionUID = 8969352547991334864L;
        private String pid;
        private String provider;
        private String name;

        private long addTime;

        public CacheInfo() {
        }

        public CacheInfo(String pid, String provider) {
            this.pid = pid;
            this.provider = provider;
        }

        public CacheInfo(String pid, String provider, String name) {
            this.pid = pid;
            this.provider = provider;
            this.name = name;
        }

        public CacheInfo(String pid, String provider, String name, long addTime) {
            this.pid = pid;
            this.provider = provider;
            this.name = name;
            this.addTime = addTime;
        }

        public static CacheInfo createFrom(@NonNull CacheInfo info) {
            final CacheInfo newInfo = new CacheInfo(info.pid, info.provider);
            newInfo.setName(info.name);
            return newInfo;
        }

        public String getPid() {
            return pid;
        }

        public void setPid(String pid) {
            this.pid = pid;
        }

        public String getProvider() {
            return provider;
        }

        public void setProvider(String provider) {
            this.provider = provider;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public long getAddTime() {
            return addTime;
        }

        public void setAddTime(long addTime) {
            this.addTime = addTime;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            CacheInfo cacheInfo = (CacheInfo) o;
            return Objects.equals(pid, cacheInfo.pid);
        }

        @Override
        public int hashCode() {
            return Objects.hash(pid);
        }

        @Override
        public String toString() {
            return "CacheInfo{" +
                    "pid='" + pid + '\'' +
                    ", provider='" + provider + '\'' +
                    ", name='" + name + '\'' +
                    ", addTime=" + addTime +
                    '}';
        }
    }
}
