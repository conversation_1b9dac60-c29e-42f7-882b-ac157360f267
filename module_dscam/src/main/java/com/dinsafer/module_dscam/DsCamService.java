package com.dinsafer.module_dscam;

import android.app.Application;
import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.common.CommonCmdEvent;
import com.dinsafer.dincore.common.DeivceChangeEvent;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dincore.common.IService;
import com.dinsafer.dincore.db.cache.DeviceCacheHelper;
import com.dinsafer.dincore.user.bean.DinUser;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dincore.utils.MapUtils;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.msctlib.netty.IMultipleSender;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_dscam.bean.BaseCamDevice;
import com.dinsafer.module_dscam.bean.DsCamCacheInfo;
import com.dinsafer.module_dscam.bean.DsCamCmd;
import com.dinsafer.module_dscam.bean.DsCamConst;
import com.dinsafer.module_dscam.bean.DsCamDevice;
import com.dinsafer.module_dscam.bean.DsCamListResponse;
import com.dinsafer.module_dscam.bean.LanDevice;
import com.dinsafer.module_dscam.doorbell.DsDoorbellBinder;
import com.dinsafer.module_dscam.doorbell.DsDoorbellDevice;
import com.dinsafer.module_dscam.doorbell.DsDoorbellNetworkManager;
import com.dinsafer.module_dscam.event.DsDeviceNameUpdateEvent;
import com.dinsafer.module_dscam.http.DscamRepo;
import com.dinsafer.module_dscam.utils.LanDiscovery;
import com.dinsafer.module_dscam.utils.SearchIpcHelper;
import com.dinsafer.module_dscam.v006.DsCamV006Binder;
import com.dinsafer.module_dscam.v006.DsCamV006Device;
import com.dinsafer.module_dscam.v006.DsCamV006NetworkManager;
import com.dinsafer.module_dscam.v015.DsCamV015Binder;
import com.dinsafer.module_dscam.v015.DsCamV015Device;
import com.dinsafer.module_dscam.v015.DsCamV015NetworkManager;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Response;

public class DsCamService implements IService, IDefaultCallBack2<List<LanDevice>> {
    private static final String BINDER_KEY_DSCAM = "dscam_binder";
    private static final String BINDER_KEY_DSCAM_NETWORK_MANAGER = "dscam_network_binder";
    private static final String BINDER_KEY_DSCAM_V006 = "dscam_v006_binder";
    private static final String BINDER_KEY_DSCAM_V006_NETWORK_MANAGER = "dscam_v006_network_binder";
    private static final String BINDER_KEY_DSCAM_V015 = "dscam_v015_binder";
    private static final String BINDER_KEY_DSCAM_V015_NETWORK_MANAGER = "dscam_v015_network_binder";
    private static final String BINDER_KEY_DSDOORBELL = "dsdoorbell_binder";
    private static final String BINDER_KEY_DSDOORBELL_NETWORK_MANAGER = "dsdoorbell_network_binder";

    private static final int PAGE_SIZE_DEFAULT = 30; // 加载ipc列表一页的数量
    private static final String CACHE_IDENTIFY = "DsCamService"; // 缓存key后缀
    private static final String TAG = DsCamService.class.getSimpleName();

    //    list的操作要加锁，否则在遍历的时候，去remove，就会奔溃
    private final List<Device> mDsCamList = new ArrayList<>();
    private String currentHomeID;
    private IMultipleSender multipleSender;

    private Map<String, Object> configMap;
    private final static byte[] fetchDeviceLock = new byte[0];
    private DscamRepo dscamRepo = new DscamRepo();
    //    支持的配件类型
    private Map<String, String> supportDeivceType = new HashMap<>();
    private LanDiscovery lanDevice = new LanDiscovery();
    private final byte[] listLock = new byte[0];
    private final DsCamCacheInfo cacheInfo = new DsCamCacheInfo();
    private int e2ePort;
    private String e2eDomain;

    @Keep
    public DsCamService(Application application) {
    }

    @Override
    @Keep
    public void load() {
        EventBus.getDefault().register(this);
        lanDevice.setCallBack(this);
        supportDeivceType.put("dscam", "dscam");
        supportDeivceType.put("DSCAM_V006", "DSCAM_V006");
        supportDeivceType.put(DsCamConst.PROVIDER_DSCAM_V015, DsCamConst.PROVIDER_DSCAM_V015);
        supportDeivceType.put("dsdoorbell", "DSDOORBELL");

    }

    @Override
    @Keep
    public void unLoad() {
        EventBus.getDefault().unregister(this);
        lanDevice.destroy();
    }

    @Override
    @Keep
    public void config(Map<String, Object> configArg) {
        configMap = configArg;
        final String lastHomeId = currentHomeID;
        currentHomeID = (String) MapUtils.get(configArg, "homeID", "");
        SearchIpcHelper.get().setCurrentHomeId(currentHomeID);
        if (configArg.containsKey("multipleSender")) {
            Object temp = configArg.get("multipleSender");
            if (temp != null) {
                multipleSender = (IMultipleSender) temp;
            }
        }

        if (!TextUtils.isEmpty(lastHomeId) && !lastHomeId.equals(currentHomeID)) {
            // 切换了家庭
            cacheInfo.updateFrom(null);

            for (Device device : mDsCamList) {
                device.destory();
            }
            mDsCamList.clear();
        } else {
            // 没有切换家庭
            synchronized (listLock) {
                if (null != multipleSender && mDsCamList.size() > 0) {
                    for (Device device : mDsCamList) {
                        BaseCamDevice baseCamDevice = (BaseCamDevice) device;
                        baseCamDevice.updateSender(multipleSender);
                    }
                }
            }
        }
    }

    /**
     * 循环获取到最新时间的device
     */
    @Override
    @Keep
    public List<Device> fetchDevices() {
        synchronized (fetchDeviceLock) {
            createDeviceFromCache();
            markDevicesFromCache();
            requestDeviceCirculate(cacheInfo.getNewestAddTime());
            DDLog.i(TAG, "fetch DsCam Devices finish");
        }
        return new ArrayList<>(mDsCamList);
    }

    private void requestDeviceCirculate(final long startTimeStamp) {
        synchronized (fetchDeviceLock) {
            final String loadingHomeId = currentHomeID;
            long addTime = startTimeStamp;
            DDLog.i(TAG, "get dscam device start...,timeStamp=" + addTime);
            // !!!! 循环获取配件
            final List<DsCamListResponse.ResultBean> netDeviceList = new ArrayList<>();
            while (true) {
                if (!TextUtils.isEmpty(loadingHomeId) && !loadingHomeId.equals(currentHomeID)) {
                    break;
                }

                final List<DsCamListResponse.ResultBean> result = requestIpcOnPageSync(addTime, false);
                if (null == result || result.size() == 0) {
                    break;
                }

                netDeviceList.addAll(result);
                for (DsCamListResponse.ResultBean bean : result) {
                    if (null != bean.getAddtime() && bean.getAddtime() > addTime) {
                        addTime = bean.getAddtime();
                    }
                }

                if (result.size() < PAGE_SIZE_DEFAULT) {
                    break;
                }
            }
            // 切换了房间，返回空数据
            if (!TextUtils.isEmpty(loadingHomeId) && !loadingHomeId.equals(currentHomeID)) {
                return;
            }

            if (netDeviceList.size() > 0) {
                final List<Device> netDevices = createDeviceFromNet(netDeviceList);
                if (netDevices.size() > 0) {
                    saveDeviceCache();
                }
            }
        }
    }

    @Nullable
    private List<DsCamListResponse.ResultBean> requestIpcOnPageSync(final long addTime, final boolean orderDesc) {
        DDLog.d(TAG, "requestIpcOnPageSync, addTime: " + (addTime + 1));
        final Response<DsCamListResponse> response = dscamRepo.fetchDsCamSync(currentHomeID, PAGE_SIZE_DEFAULT, addTime + 1, orderDesc);
        if (null == response || !response.isSuccessful()) {
            return null;
        }
        final DsCamListResponse body = response.body();
        if (null == body) {
            return null;
        }

        final List<DsCamListResponse.ResultBean> result = body.getResult();
        if (null == result || result.size() == 0) {
            return null;
        }

        return result;
    }

    @Override
    @Keep
    public Device getDevice(String id) {
        for (Device device : mDsCamList) {
            if (device.getId().equals(id)) {
                return device;
            }
        }
        return null;
    }

    @Override
    public Device getDevice(String id, String sub) {
        return getDevice(id);
    }

    @Override
    @Keep
    public List<Device> getDeviceByType(String sub) {
        if (isSupportedDeviceType(sub)) {
//        113版本取消局域网查找ipc，直接通过get_params获取ip和port
//            startDiscoveryDevice();
            return fetchDevices();
        }
        return null;
    }

    @Override
    public List<Device> getDeviceByType(String sub, boolean cacheFirst) {
        if (!isSupportedDeviceType(sub)) {
            return null;
        }

        synchronized (fetchDeviceLock) {
            if (cacheFirst) {
                // 1. 该模式下，有缓存返回缓存，没有直接加载全部
                final List<Device> deviceFromCache = createDeviceFromCache();
                if (deviceFromCache.size() > 0) {
                    return deviceFromCache;
                } else {
                    return fetchDevices();
                }
            }

            // 2. 该模式下，仅加载下一页
            markDevicesFromCache();

            final String loadingHomeId = currentHomeID;
            final long addTime = cacheInfo.getNewestAddTime();
            final List<DsCamListResponse.ResultBean> netDeviceList = requestIpcOnPageSync(addTime, false);
            if (null != netDeviceList && netDeviceList.size() > 0) {
                long maxAddTime = addTime;
                for (DsCamListResponse.ResultBean bean : netDeviceList) {
                    if (null != bean.getAddtime() && bean.getAddtime() > maxAddTime) {
                        maxAddTime = bean.getAddtime();
                    }
                }

                final List<Device> netDevices = createDeviceFromNet(netDeviceList);

                // 切换了房间，返回空数据
                if (!TextUtils.isEmpty(loadingHomeId) && !loadingHomeId.equals(currentHomeID)) {
                    return null;
                }

                if (0 < netDevices.size()) {
                    saveDeviceCache();
                }
            }
        }

        return new ArrayList<>(mDsCamList);
    }

    @Nullable
    @Override
    public List<Device> getCacheDeviceByType(String type) {
        if (!isSupportedDeviceType(type)) {
            return null;
        }
        final List<Device> result = new ArrayList<>();
        final List<Device> cacheDevices = createDeviceFromCache();
        if (cacheDevices.size() > 0) {
            markDevicesFromCache();
            result.addAll(cacheDevices);
        }
        return result;
    }

    @Nullable
    @Override
    public List<Device> getLocalAndNewDeviceByType(String type) {
        if (!isSupportedDeviceType(type)) {
            return null;
        }
        return fetchDevices();
    }

    @Nullable
    @Override
    public List<Device> getAllDeviceByType(String type) {
        if (!isSupportedDeviceType(type)) {
            return null;
        }
        synchronized (fetchDeviceLock) {
            createDeviceFromCache();
            markDevicesFromCache();
            requestDeviceCirculate(0);
            DDLog.i(TAG, "getAllDeviceByType finish: dscam");
        }
        return new ArrayList<>(mDsCamList);
    }

    @Override
    public boolean isSupportedDeviceType(String type) {
        return !TextUtils.isEmpty(type) && supportDeivceType.containsKey(type);
    }

    @Override
    public boolean removeDeviceCacheById(String sub) {
        DDLog.i(TAG, "removeDeviceCacheById. id:" + sub);
        if (TextUtils.isEmpty(sub)) {
            return false;
        }

        synchronized (listLock) {
            boolean remove = false;
            for (Device device : mDsCamList) {
                if (sub.equals(device.getId())) {
                    remove = cacheInfo.removeDevice(sub, null);
                    if (remove) {
                        mDsCamList.remove(device);
                    }
                    saveDeviceCache();
                    break;
                }
            }
            DDLog.i(TAG, "removeDeviceCacheById. " + sub + " remove succeed ？" + remove);
            return remove;
        }
    }

    @Override
    public boolean removeDeviceCacheByIdAndSub(String id, String sub) {
        return removeDeviceCacheById(id);
    }

    @Override
    public boolean removeDeviceCacheByType(String sub) {
        if (isSupportedDeviceType(sub)) {
            clearDeviceCache();
            return true;
        }
        return false;
    }

    private void startDiscoveryDevice() {
        lanDevice.startDiscoveryDevice(5000);
    }


    @Override
    @Keep
    public boolean releaseDeviceByType(String sub) {
        if (sub.equals("dscam") || sub.equals("DSCAM_VOO6") || sub.equals("DSDOORBELL")) {
            for (Device device : mDsCamList) {
                BaseCamDevice dsCamDevice = (BaseCamDevice) device;
                dsCamDevice.disconnect();
            }
            return true;
        }
        return false;
    }

    @Override
    public Device acquireTemporaryDevices(String id, String model) {
        return null;
    }

    @Override
    public BasePluginBinder createPluginBinder(Context context, @NonNull String type) {
        switch (type) {
            case BINDER_KEY_DSCAM:
                return new DsCamBinder(context);
            case BINDER_KEY_DSCAM_NETWORK_MANAGER:
                return new DsCamNetworkManager(context);
            case BINDER_KEY_DSCAM_V006:
                return new DsCamV006Binder(context);
            case BINDER_KEY_DSCAM_V006_NETWORK_MANAGER:
                return new DsCamV006NetworkManager(context);
            case BINDER_KEY_DSCAM_V015:
                return new DsCamV015Binder(context);
            case BINDER_KEY_DSCAM_V015_NETWORK_MANAGER:
                return new DsCamV015NetworkManager(context);
            case BINDER_KEY_DSDOORBELL:
                return new DsDoorbellBinder(context);
            case BINDER_KEY_DSDOORBELL_NETWORK_MANAGER:
                return new DsDoorbellNetworkManager(context);
            default:
                return null;
        }
    }

    @Subscribe
    public void onEvent(DeivceChangeEvent event) {
        if (event.isAdd()) {

        } else if (event.isRemove()) {
            synchronized (listLock) {
                for (Device device : mDsCamList) {
                    if (device.getId().equals(event.getDevice().getId())) {
                        cacheInfo.removeDevice(device.getId(), null);
                        saveDeviceCache();
                        mDsCamList.remove(device);
                        DDLog.i(TAG, " DeivceChangeEvent: 删除ipc：" + mDsCamList.size());
                        break;
                    }
                }
            }
        }
    }

    @Subscribe
    public void onEvent(CommonCmdEvent commonCmdEvent) {
        synchronized (listLock) {
            DDLog.i(TAG, "on Event: commonCmdEvent " + commonCmdEvent.getCmd() + " /" + commonCmdEvent.getExtra());
            if ((CommonCmdEvent.CMD.DSCAM_ADD.equals(commonCmdEvent.getCmd()) || CommonCmdEvent.CMD.DSDOORBELL_ADD.equals(commonCmdEvent.getCmd()))
                    && !TextUtils.isEmpty(commonCmdEvent.getExtra())) {
//             手动插入device
                try {
                    JSONObject object = new JSONObject(commonCmdEvent.getExtra());
                    if (!"DSCAM".equalsIgnoreCase(DDJSONUtil.getString(object, "provider"))
                            && !"DSCAM_V006".equalsIgnoreCase(DDJSONUtil.getString(object, "provider"))
                            && !DsCamConst.PROVIDER_DSCAM_V015.equalsIgnoreCase(DDJSONUtil.getString(object, "provider"))
                            && !"DSDOORBELL".equals(DDJSONUtil.getString(object, "provider"))) {
                        return;
                    }
                    String pid = DDJSONUtil.getString(object, "pid");
                    for (Device device : mDsCamList) {
                        if (pid.equals(device.getId())) {
                            BaseCamDevice dsCamDevice = (BaseCamDevice) device;
                            dsCamDevice.setFlagDeleted(false);
                            dsCamDevice.setNameDirect(DDJSONUtil.getString(object, "name"));
                            DDLog.i(TAG, "on Event: 重新连接，添加ipc 但ipc已经存在了：" + mDsCamList.size());
                            HashMap<String, Object> par = new HashMap<>();
                            par.put("cmd", DsCamCmd.CONNECT);
                            par.put("discardCache", true);
                            device.submit(par);
                            return;
                        }
                    }

                    Device dscam = null;
                    DsCamCacheInfo.CacheInfo cache = null;
                    if ("DSCAM".equalsIgnoreCase(DDJSONUtil.getString(object, "provider"))) {
                        dscam = new DsCamDevice(multipleSender,
                                DDJSONUtil.getString(object, "pid"),
                                DDJSONUtil.getString(object, "homeID"),
                                DDJSONUtil.getString(object, "end_id"),
                                DDJSONUtil.getString(object, "group_id"),
                                System.currentTimeMillis());
                        cache = new DsCamCacheInfo.CacheInfo(DDJSONUtil.getString(object, "pid"), DsCamConst.PROVIDER_DSCAM);
                    } else if ("DSDOORBELL".equals(DDJSONUtil.getString(object, "provider"))) {
                        dscam = new DsDoorbellDevice(multipleSender,
                                DDJSONUtil.getString(object, "pid"),
                                DDJSONUtil.getString(object, "homeID"),
                                DDJSONUtil.getString(object, "end_id"),
                                DDJSONUtil.getString(object, "group_id"),
                                System.currentTimeMillis());
                        cache = new DsCamCacheInfo.CacheInfo(DDJSONUtil.getString(object, "pid"), DsCamConst.PROVIDER_DSDOORBELL);
                    } else if ("DSCAM_V006".equalsIgnoreCase(DDJSONUtil.getString(object, "provider"))) {
                        dscam = new DsCamV006Device(multipleSender,
                                DDJSONUtil.getString(object, "pid"),
                                DDJSONUtil.getString(object, "homeID"),
                                DDJSONUtil.getString(object, "end_id"),
                                DDJSONUtil.getString(object, "group_id"),
                                System.currentTimeMillis());
                        cache = new DsCamCacheInfo.CacheInfo(DDJSONUtil.getString(object, "pid"), DsCamConst.PROVIDER_DSCAM_V006);
                    } else if (DsCamConst.PROVIDER_DSCAM_V015.equalsIgnoreCase(DDJSONUtil.getString(object, "provider"))) {
                        dscam = new DsCamV015Device(multipleSender,
                                DDJSONUtil.getString(object, "pid"),
                                DDJSONUtil.getString(object, "homeID"),
                                DDJSONUtil.getString(object, "end_id"),
                                DDJSONUtil.getString(object, "group_id"),
                                System.currentTimeMillis());
                        cache = new DsCamCacheInfo.CacheInfo(DDJSONUtil.getString(object, "pid"), DsCamConst.PROVIDER_DSCAM_V015);
                    }
                    if (dscam == null) {
                        return;
                    }
                    DDLog.i(TAG, "on Event: 添加ipc：" + mDsCamList.size());
                    addDeviceIfNotExit(dscam);
                    cacheInfo.addDevice(cache);
                    saveDeviceCache();

                    DeivceChangeEvent event = new DeivceChangeEvent(dscam);
                    event.setAdd(true);
                    EventBus.getDefault().post(event);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            } else if ((CommonCmdEvent.CMD.DSCAM_DELETE.equals(commonCmdEvent.getCmd()) || CommonCmdEvent.CMD.DSDOORBELL_DELETE.equals(commonCmdEvent.getCmd()))
                    && !TextUtils.isEmpty(commonCmdEvent.getExtra())) {
                try {
                    JSONObject object = new JSONObject(commonCmdEvent.getExtra());
                    if (!"DSCAM".equalsIgnoreCase(DDJSONUtil.getString(object, "provider"))
                            && !"DSCAM_V006".equalsIgnoreCase(DDJSONUtil.getString(object, "provider"))
                            && !DsCamConst.PROVIDER_DSCAM_V015.equalsIgnoreCase(DDJSONUtil.getString(object, "provider"))
                            && !"DSDOORBELL".equals(DDJSONUtil.getString(object, "provider"))) {
                        return;
                    }
                    String pid = DDJSONUtil.getString(object, "pid");
                    for (Device device : mDsCamList) {
                        if (pid.equals(device.getId())) {
                            BaseCamDevice dsCamDevice = (BaseCamDevice) device;
                            dsCamDevice.deleteDirect(true);
                            DDLog.i(TAG, "on Event: 删除ipc：" + mDsCamList.size());
                            break;
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            } else if ((CommonCmdEvent.CMD.DSCAM_RENAME.equals(commonCmdEvent.getCmd()) || CommonCmdEvent.CMD.DSDOORBELL_RENAME.equals(commonCmdEvent.getCmd()))
                    && !TextUtils.isEmpty(commonCmdEvent.getExtra())) {
                try {
                    JSONObject object = new JSONObject(commonCmdEvent.getExtra());
                    if (!"DSCAM".equalsIgnoreCase(DDJSONUtil.getString(object, "provider"))
                            && !"DSCAM_V006".equalsIgnoreCase(DDJSONUtil.getString(object, "provider"))
                            && !DsCamConst.PROVIDER_DSCAM_V015.equalsIgnoreCase(DDJSONUtil.getString(object, "provider"))
                            && !"DSDOORBELL".equals(DDJSONUtil.getString(object, "provider"))) {
                        return;
                    }
                    String pid = DDJSONUtil.getString(object, "pid");
                    for (Device device : mDsCamList) {
                        if (pid.equals(device.getId())) {
                            BaseCamDevice dsCamDevice = (BaseCamDevice) device;
                            dsCamDevice.setNameDirect(DDJSONUtil.getString(object, "name"));
                            break;
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            } else if (CommonCmdEvent.CMD.REMOTE_ADDRESS_UPDATED.equals(commonCmdEvent.getCmd())
                    && !TextUtils.isEmpty(commonCmdEvent.getExtra())) {
                try {
                    JSONObject object = new JSONObject(commonCmdEvent.getExtra());
                    final String localHomeId = DDJSONUtil.getString(object, "home_id");
                    final int e2ePort = DDJSONUtil.getInt(object, "e2e_port");
                    final String e2eDomain = DDJSONUtil.getString(object, "e2e_domain");
                    if (!TextUtils.isEmpty(localHomeId) && localHomeId.equals(currentHomeID)
                            && (0 == e2ePort || e2ePort != this.e2ePort)
                            && !TextUtils.isEmpty(e2eDomain) && !e2eDomain.equals(this.e2eDomain)) {
                        this.e2ePort = e2ePort;
                        this.e2eDomain = e2eDomain;
                        notifyRemoteAddressUpdated();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else if (CommonCmdEvent.CMD.ON_BEFORE_HOME_DISCONNECT.equals(commonCmdEvent.getCmd())) {
                final List<Device> localList = mDsCamList;
                if (null != localList && localList.size() > 0) {
                    for (Device device : localList) {
                        BaseCamDevice baseCamDevice = (BaseCamDevice) device;
                        baseCamDevice.needConnectAgain();
                    }
                }
            } else if (CommonCmdEvent.CMD.ON_BEFORE_HOME_SWITCH.equals(commonCmdEvent.getCmd())) {
                SearchIpcHelper.get().release();
            } else if (CommonCmdEvent.CMD.LOGIN_SUCCESS.equals(commonCmdEvent.getCmd())) {
                cacheInfo.updateFrom(null);
                for (Device device : mDsCamList) {
                    device.destory();
                }
                mDsCamList.clear();
            }
        }
    }

    /**
     * 连接的服务器节点变更，所有设备需要重连
     */
    private void notifyRemoteAddressUpdated() {
        BaseCamDevice baseCamDevice;
        for (Device device : mDsCamList) {
            if (device instanceof BaseCamDevice) {
                baseCamDevice = (BaseCamDevice) device;
                baseCamDevice.onRemoteAddressUpdated();
            }
        }
    }

    @Override
    public void onSuccess(List<LanDevice> T) {
//        取消了
//        synchronized (fetchDeviceLock) {
//            for (LanDevice lanDevice : T) {
//                for (Device device : mDsCamList) {
//                    if (lanDevice.getDeviceID().equals(device.getId())) {
//                        DsCamDevice dsCamDevice = (DsCamDevice) device;
//                        dsCamDevice.startLanConnect(lanDevice.getIp(), lanDevice.getPort());
//                    }
//                }
//            }
//        }

    }

    @Override
    public void onError(int code, String error) {

    }

    private void markDevicesFromCache() {
        synchronized (listLock) {
            if (mDsCamList.size() > 0) {
                for (Device device : mDsCamList) {
                    ((BaseCamDevice) device).setFlagCache(true);
                }
            }
        }
    }

    @NonNull
    private List<Device> createDeviceFromNet(@NonNull List<DsCamListResponse.ResultBean> deviceBeanList) {
        List<Device> netDevices = new ArrayList<>();
        for (DsCamListResponse.ResultBean listBean : deviceBeanList) {
            // 已经在缓存中，修改状态
            final Device cacheDevice = getDevice(listBean.getPid());
            if (null != cacheDevice) {
                final BaseCamDevice d = (BaseCamDevice) cacheDevice;
                d.setFlagCache(true);
                d.setFlagDeleted(false);
                d.updateSender(multipleSender);
                continue;
            }

            // 不在缓存中，创建并缓存
            Device device;
            DsCamCacheInfo.CacheInfo cache = null;
            boolean added;
            if ("DSCAM".equals(listBean.getProvider())) {
                device = new DsCamDevice(multipleSender, currentHomeID, listBean);
                added = addDeviceIfNotExit(device);
                if (added) {
                    netDevices.add(device);
                    cache = new DsCamCacheInfo.CacheInfo(listBean.getPid(), DsCamConst.PROVIDER_DSCAM);
                }
            } else if ("DSCAM_V006".equals(listBean.getProvider())) {
                device = new DsCamV006Device(multipleSender, currentHomeID, listBean);
                added = addDeviceIfNotExit(device);
                if (added) {
                    netDevices.add(device);
                    cache = new DsCamCacheInfo.CacheInfo(listBean.getPid(), DsCamConst.PROVIDER_DSCAM_V006);
                }
            } else if (DsCamConst.PROVIDER_DSCAM_V015.equals(listBean.getProvider())) {
                device = new DsCamV015Device(multipleSender, currentHomeID, listBean);
                added = addDeviceIfNotExit(device);
                if (added) {
                    netDevices.add(device);
                    cache = new DsCamCacheInfo.CacheInfo(listBean.getPid(), DsCamConst.PROVIDER_DSCAM_V015);
                }
            } else if ("DSDOORBELL".equals(listBean.getProvider())) {
                device = new DsDoorbellDevice(multipleSender, currentHomeID, listBean);
                added = addDeviceIfNotExit(device);
                if (added) {
                    netDevices.add(device);
                    cache = new DsCamCacheInfo.CacheInfo(listBean.getPid(), DsCamConst.PROVIDER_DSDOORBELL);
                }
            }

            if (null != cache) {
                final Long addTime = listBean.getAddtime();
                if (null != addTime) {
                    cache.setAddTime(addTime);
                }
                cacheInfo.addDevice(cache);
            }
        }
        DDLog.i(TAG, CACHE_IDENTIFY + ":cache----netdevice: " + netDevices.size());
        return netDevices;
    }

    @NonNull
    private List<Device> createDeviceFromCache() {
        if (mDsCamList.size() > 0) {
            DDLog.d(TAG, CACHE_IDENTIFY + ":cache--------不从缓存创建");
            return new ArrayList<>(mDsCamList);
        }

        final String homeId = currentHomeID;
        DinUser user = DinCore.getUserInstance().getUser();
        final String userId = null != user ? user.getUser_id() : null;
        final DsCamCacheInfo cache = DeviceCacheHelper.getCache(homeId, userId, CACHE_IDENTIFY, DsCamCacheInfo.class);
        cacheInfo.updateFrom(cache);
        DDLog.d(TAG, CACHE_IDENTIFY + ":cache--------reedCache: " + cacheInfo);

        List<Device> cacheDevices = new ArrayList<>();
        if (!cacheInfo.isCacheEmpty()) {
            final List<DsCamCacheInfo.CacheInfo> cacheList = cacheInfo.getCacheList();
            Device device;
            for (DsCamCacheInfo.CacheInfo info : cacheList) {
                final String pid = info.getPid();
                final String provider = info.getProvider();
                if (TextUtils.isEmpty(pid) || TextUtils.isEmpty(provider)) {
                    continue;
                }

                final Device cacheDevice = getDevice(pid);
                if (null != cacheDevice) {
                    ((BaseCamDevice)cacheDevice).setFlagCache(true);
                    cacheDevices.add(cacheDevice);
                    continue;
                }

                if (DsCamConst.PROVIDER_DSCAM.equalsIgnoreCase(provider)) {
                    device = new DsCamDevice(multipleSender, currentHomeID, DsCamCacheInfo.CacheInfo.createFrom(info));
                    addDeviceIfNotExit(device);
                    cacheDevices.add(device);
                } else if (DsCamConst.PROVIDER_DSCAM_V006.equalsIgnoreCase(provider)) {
                    device = new DsCamV006Device(multipleSender, currentHomeID, DsCamCacheInfo.CacheInfo.createFrom(info));
                    addDeviceIfNotExit(device);
                    cacheDevices.add(device);
                } else if (DsCamConst.PROVIDER_DSCAM_V015.equalsIgnoreCase(provider)) {
                    device = new DsCamV015Device(multipleSender, currentHomeID, DsCamCacheInfo.CacheInfo.createFrom(info));
                    addDeviceIfNotExit(device);
                    cacheDevices.add(device);
                } else if (DsCamConst.PROVIDER_DSDOORBELL.equalsIgnoreCase(provider)) {
                    device = new DsDoorbellDevice(multipleSender, currentHomeID, DsCamCacheInfo.CacheInfo.createFrom(info));
                    addDeviceIfNotExit(device);
                    cacheDevices.add(device);
                }
            }
        }
        DDLog.i(TAG, CACHE_IDENTIFY + ":cache----缓存device: " + cacheDevices.size());
        return cacheDevices;
    }

    private boolean addDeviceIfNotExit(Device device) {
        if (null != device) {
            if (!mDsCamList.contains(device)) {
                DDLog.d(TAG, CACHE_IDENTIFY + ":添加Device到列表");
                mDsCamList.add(device);
                return true;
            } else {
                BaseCamDevice d = (BaseCamDevice) device;
                d.setFlagDeleted(false);
                d.updateSender(multipleSender);
                return false;
            }
        }
        return false;
    }

    @Subscribe
    public void onEvent(DsDeviceNameUpdateEvent event) {
        MsctLog.i(TAG, "DsDeviceNameUpdateEvent, " + event.toString());
        updateDsDeviceNameOnCache(event.getPid(), event.getName());
    }

    private void updateDsDeviceNameOnCache(final String pid, final String name) {
        if (TextUtils.isEmpty(pid) || TextUtils.isEmpty(name)) {
            MsctLog.w(TAG, "updateDsDeviceNameOnCache, failed, pid or name is null! pid=" + pid + ", name=" + name);
            return;
        }

        final List<DsCamCacheInfo.CacheInfo> cacheList = cacheInfo.getCacheList();
        boolean updated = false;
        if (null != cacheList && cacheList.size() > 0) {
            DsCamCacheInfo.CacheInfo cache;
            for (int i = 0; i < cacheList.size(); i++) {
                cache = cacheList.get(i);
                if (pid.equals(cache.getPid())) {
                    if (!name.equals(cache.getName())) {
                        cache.setName(name);
                        updated = true;
                    }
                    break;
                }
            }
        }

        if (updated) {
            saveDeviceCache();
        }
    }

    private void saveDeviceCache() {
        final String homeId = currentHomeID;
        DinUser user = DinCore.getUserInstance().getUser();
        final String userId = null != user ? user.getUser_id() : null;
        DeviceCacheHelper.saveCacheAsync(homeId, userId, CACHE_IDENTIFY, cacheInfo);
        DDLog.d(TAG, CACHE_IDENTIFY + ":cache--------savecache: " + cacheInfo);
    }

    private void clearDeviceCache() {
        final String homeId = currentHomeID;
        DinUser user = DinCore.getUserInstance().getUser();
        final String userId = null != user ? user.getUser_id() : null;
        DeviceCacheHelper.removeCacheASync(homeId, userId, CACHE_IDENTIFY);
    }
}
