package com.dinsafer.module_dscam.utils;

import android.os.Handler;

import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.msctlib.msct.Utils;
import com.dinsafer.module_dscam.bean.LanDevice;

import java.lang.reflect.Array;
import java.net.InetSocketAddress;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.PooledByteBufAllocator;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelOption;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.DatagramPacket;
import io.netty.channel.socket.nio.NioDatagramChannel;

public class LanDiscovery {
    private NioDatagramChannel channel;
    private NioEventLoopGroup nioEventLoopGroup;
    private List<LanDevice> devices = new ArrayList<>();
    private IDefaultCallBack2<List<LanDevice>> callBack;
    private Handler timoutHandler = new Handler();

    public LanDiscovery() {
        nioEventLoopGroup = new NioEventLoopGroup();
        Bootstrap bootstrap = new Bootstrap();
        bootstrap.option(ChannelOption.SO_BROADCAST, true);
        bootstrap.channel(NioDatagramChannel.class);
        bootstrap.group(nioEventLoopGroup);
        bootstrap.handler(new ChannelInitializer<NioDatagramChannel>() {

            @Override
            protected void initChannel(NioDatagramChannel ch) throws Exception {
                ChannelPipeline cp = ch.pipeline();
                cp.addLast(new ChannelInboundHandlerAdapter() {
                    @Override
                    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
                        DatagramPacket dp = (DatagramPacket) msg;
//                         找到对应的KCP回调回去
//                        MsctLog.i("udp", "收到消息：" + dp.content());
                        ByteBuf byteBuf = dp.copy().content();
                        byte[] bytes = new byte[byteBuf.readableBytes()];
                        byteBuf.readBytes(bytes);
                        if (bytes.length < 346) {
//                            大于122才合法
                            return;

                        }
                        int endIndex = -1;
                        for (int i = 90; i < 122; i++) {
                            if (bytes[i] == 0) {
                                endIndex = i;
                                break;
                            }
                        }
                        if (endIndex == -1) {
                            return;
                        }
                        byte[] deviceIDb = new byte[endIndex - 90];
                        for (int i = 0; i < deviceIDb.length; i++) {
                            deviceIDb[i] = bytes[90 + i];
                        }
                        String content = new String(bytes);
                        String deviceID = new String(deviceIDb);
                        MsctLog.i("udp", "收到消息,发送者："
                                + dp.sender().toString() + ",消息为：" + content);
                        MsctLog.i("udp", "deviceID:" + deviceID);

                        byte[] portB = new byte[4];
                        portB[0] = bytes[346];
                        portB[1] = bytes[347];
                        portB[2] = 0;
                        portB[3] = 0;


                        int port = Utils.byteArrayToLeInt(portB);
                        LanDevice lanDevice = new LanDevice(dp.sender().getHostName()
                                , port, deviceID);
                        MsctLog.i("udp", "deviceID:" + deviceID + " port:" + port);
                        if (!devices.contains(lanDevice)) {
                            devices.add(lanDevice);
                        }
                    }

                    @Override
                    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
//                        关闭所有KCP
//                        KcpClient.this.handleException(cause, null);
//                        KcpClient.this.close();
                        cause.printStackTrace();
                    }
                });
            }
        });
        ChannelFuture sync = bootstrap.bind(0).syncUninterruptibly();
        channel = (NioDatagramChannel) sync.channel();
        Runtime.getRuntime().addShutdownHook(new Thread(new Runnable() {
            @Override
            public void run() {
                nioEventLoopGroup.shutdownGracefully();
            }
        }));
    }

    public IDefaultCallBack2<List<LanDevice>> getCallBack() {
        return callBack;
    }

    public void setCallBack(IDefaultCallBack2<List<LanDevice>> callBack) {
        this.callBack = callBack;
    }

    public void startDiscoveryDevice(int timeout) {
        ByteBuf bb = PooledByteBufAllocator.DEFAULT.buffer(1500);
        byte[] msg = new byte[]{0x22, 0x11, 0x01, 0x01};
        bb.writeBytes(msg);
//        bb.writeBytes("helloworld".getBytes(Charset.forName("utf-8")));
        DatagramPacket temp = new DatagramPacket(bb, new InetSocketAddress("***************", 10715));
        this.channel.writeAndFlush(temp);
        this.channel.writeAndFlush(temp);
        this.channel.writeAndFlush(temp);

        timoutHandler.removeCallbacksAndMessages(null);
        devices.clear();
        timoutHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (callBack != null) {
                    callBack.onSuccess(devices);
                }
            }
        }, timeout);
    }

    public void destroy() {
        callBack = null;
        channel.disconnect();
        nioEventLoopGroup.shutdownGracefully();
    }

}
