package com.dinsafer.module_dscam.utils;

import org.json.JSONException;
import org.json.JSONObject;

public class AuthUtils {

    //    获取连接上去之后第一个字符串
    public static String getFirstMessage() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("__time", System.currentTimeMillis() / 1000);
            return jsonObject.toString();
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return "";
    }
}
