package com.dinsafer.module_dscam.utils;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.module_dscam.bean.BaseCamDevice;
import com.dinsafer.module_dscam.bean.SearchIpcParams;
import com.dinsafer.module_dscam.bean.SearchIpcResponse;
import com.dinsafer.module_dscam.http.DsCamApi;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2023/1/4 3:03 下午
 */
public class SearchIpcHelper {

    private final static String TAG = "SearchIpcHelper";
    private final static long LOAD_INTERVAL = 2L * 1000; // 加载时间间隔

    private String currentHomeId;
    private Call<SearchIpcResponse> mSearchIpcCall;

    private final ConcurrentHashMap<String, BaseCamDevice> loadingMap = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, BaseCamDevice> needLoadMap = new ConcurrentHashMap<>();

    private volatile boolean scheduled = false;
    private final Handler mHandler = new Handler(Looper.getMainLooper());
    private final Runnable searchTask = () -> {
        cancel(true);

        synchronized (SearchIpcHelper.this) {
            if (needLoadMap.size() > 0) {
                final List<SearchIpcParams> params = new ArrayList<>(needLoadMap.size());
                for (Map.Entry<String, BaseCamDevice> entry : needLoadMap.entrySet()) {
                    params.add(new SearchIpcParams(entry.getKey(), entry.getValue().getSubCategory()));
                }
                loadingMap.putAll(needLoadMap);
                needLoadMap.clear();
                startSearchIpc(currentHomeId, params);
            }
        }
        scheduled = false;
    };

    /**
     * 添加到查询DSCAM信息队列，等时间到时统一请求
     *
     * @param homeId
     * @param dsDevice
     */
    public void addTask(final String homeId, final BaseCamDevice dsDevice) {
        MsctLog.v(TAG, "addTask, homeId: " + homeId + ", deviceId: " + dsDevice.getId());
        if (TextUtils.isEmpty(currentHomeId) || !currentHomeId.equals(homeId)) {
            MsctLog.w(TAG, "addTask, currentHomeId is not equal to device's homeId, ignore!");
            return;
        }

        final String deviceId = dsDevice.getId();
        if (TextUtils.isEmpty(deviceId)) {
            MsctLog.w(TAG, "addTask, empty device's id!");
            return;
        }

        synchronized (this) {
            if (loadingMap.containsKey(deviceId)
                    || needLoadMap.containsKey(deviceId)) {
                MsctLog.w(TAG, "addTask, device's id is loading or needLoad!");
                return;
            }

            needLoadMap.put(deviceId, dsDevice);

            schedule(false);
        }
    }

    private void schedule(final boolean now) {
        synchronized (this) {
            if (needLoadMap.size() > 0 && !scheduled) {
                scheduled = true;
                mHandler.postDelayed(searchTask, now ? 0 : LOAD_INTERVAL);
            }
        }
    }

    public void setCurrentHomeId(final String homeId) {
        final String lastHomeId = currentHomeId;
        this.currentHomeId = homeId;
        if (!TextUtils.isEmpty(lastHomeId) && !lastHomeId.equals(currentHomeId)) {
            release();
        }
    }

    private void startSearchIpc(String homeID, @NonNull List<SearchIpcParams> params) {
        mSearchIpcCall = DsCamApi.getInstance().searchIpc(homeID, params);
        mSearchIpcCall.enqueue(new Callback<SearchIpcResponse>() {
            @Override
            public void onResponse(Call<SearchIpcResponse> call, Response<SearchIpcResponse> response) {
                SearchIpcResponse body = response.body();
                if (response.isSuccessful() && null != body && body.getResult() != null) {
                    SearchIpcResponse.ResultBean result = body.getResult();
                    synchronized (SearchIpcHelper.this) {
                        if (loadingMap.size() == 0) {
                            return;
                        }
                        final ConcurrentHashMap<String, BaseCamDevice> devices = new ConcurrentHashMap<>(loadingMap);
                        loadingMap.clear();
                        notifyResult(devices, result.getList(), true);
                    }
                } else {
                    synchronized (SearchIpcHelper.this) {
                        if (loadingMap.size() == 0) {
                            return;
                        }
                        final ConcurrentHashMap<String, BaseCamDevice> devices = new ConcurrentHashMap<>(loadingMap);
                        loadingMap.clear();
                        notifyResult(devices, null, false);
                    }
                }
                mSearchIpcCall = null;
                schedule(true);
            }

            @Override
            public void onFailure(Call<SearchIpcResponse> call, Throwable t) {
                t.printStackTrace();
                synchronized (SearchIpcHelper.this) {
                    if (loadingMap.size() > 0) {
                        final ConcurrentHashMap<String, BaseCamDevice> devices = new ConcurrentHashMap<>(loadingMap);
                        loadingMap.clear();
                        notifyResult(devices, null, false);
                    }
                }
                mSearchIpcCall = null;
                schedule(true);
            }
        });
    }

    /**
     * 直接查询指定的Dscam信息
     *
     * @param homeID
     * @param dsCamDevice
     */
    public void startSearchIpcSingle(String homeID, @NonNull final BaseCamDevice dsCamDevice) {
        final String deviceId = dsCamDevice.getId();
        final String provider = dsCamDevice.getSubCategory();
        if (TextUtils.isEmpty(homeID) || TextUtils.isEmpty(deviceId) || TextUtils.isEmpty(provider)) {
            MsctLog.e(TAG, "startSearchIpcempty homeId or empty deviceId or empty proviider");
            return;
        }
        final SearchIpcParams params = new SearchIpcParams(deviceId, provider);
        Call<SearchIpcResponse> searchIpcResponseCall = DsCamApi.getInstance().searchIpc(homeID, Collections.singletonList(params));
        searchIpcResponseCall.enqueue(new Callback<SearchIpcResponse>() {
            @Override
            public void onResponse(Call<SearchIpcResponse> call, Response<SearchIpcResponse> response) {
                SearchIpcResponse body = response.body();
                if (response.isSuccessful() && null != body && body.getResult() != null) {
                    SearchIpcResponse.ResultBean result = body.getResult();
                    List<SearchIpcResponse.ResultBean.ListBean> list = result.getList();
                    SearchIpcResponse.ResultBean.ListBean info = null;
                    if (null != list && list.size() > 0 && !TextUtils.isEmpty(deviceId)) {
                        for (int i = 0; i < list.size(); i++) {
                            SearchIpcResponse.ResultBean.ListBean listBean = list.get(i);
                            if (deviceId.equals(listBean.getPid())) {
                                info = listBean;
                                break;
                            }
                        }
                    }
                    dsCamDevice.onSearchIpcInfo(info, true);
                } else {
                    dsCamDevice.onSearchIpcInfo(null, true);
                }
            }

            @Override
            public void onFailure(Call<SearchIpcResponse> call, Throwable t) {
                t.printStackTrace();
                dsCamDevice.onSearchIpcInfo(null, false);
            }
        });
    }

    private void notifyResult(
            final @NonNull ConcurrentHashMap<String, BaseCamDevice> dsDevices,
            final @Nullable List<SearchIpcResponse.ResultBean.ListBean> list, boolean success) {
        if (dsDevices.size() == 0) {
            return;
        }

        for (BaseCamDevice dsDevice : dsDevices.values()) {
            SearchIpcResponse.ResultBean.ListBean info = null;
            final String deviceId = dsDevice.getId();
            if (null != list && list.size() > 0 && !TextUtils.isEmpty(deviceId)) {
                for (int i = 0; i < list.size(); i++) {
                    SearchIpcResponse.ResultBean.ListBean listBean = list.get(i);
                    if (deviceId.equals(listBean.getPid())) {
                        info = listBean;
                        break;
                    }
                }
            }
            dsDevice.onSearchIpcInfo(info, success);
        }
    }

    public void release() {
        mHandler.removeCallbacksAndMessages(null);
        synchronized (SearchIpcHelper.this) {
            if (loadingMap.size() == 0 && needLoadMap.size() == 0) {
                return;
            }
            final ConcurrentHashMap<String, BaseCamDevice> devices = new ConcurrentHashMap<>(loadingMap);
            devices.putAll(needLoadMap);
            notifyResult(devices, null, false);

            cancel(false);

            needLoadMap.clear();
            loadingMap.clear();
        }
    }

    /**
     * @param reload 是否需要重新加入等待队列
     */
    private void cancel(final boolean reload) {
        if (null != mSearchIpcCall && !mSearchIpcCall.isCanceled()) {
            if (reload) {
                synchronized (this) {
                    needLoadMap.putAll(loadingMap);
                    loadingMap.clear();
                }
            }

            mSearchIpcCall.cancel();
            mSearchIpcCall = null;
        }
    }

    private SearchIpcHelper() {
    }

    private final static class Holder {
        private static final SearchIpcHelper INSTANCE = new SearchIpcHelper();
    }

    public static SearchIpcHelper get() {
        return Holder.INSTANCE;
    }
}
