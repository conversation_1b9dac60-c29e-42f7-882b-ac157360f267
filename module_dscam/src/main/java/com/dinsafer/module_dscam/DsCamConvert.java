package com.dinsafer.module_dscam;

import com.dinsafer.dssupport.crypt.Encryption;
import com.dinsafer.dssupport.msctlib.msct.IConvert;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.dssupport.utils.HexUtil;

public class DsCamConvert implements IConvert {
    private static final String TAG = DsCamConvert.class.getSimpleName();

    private String iv = "3bf788a38df5c9da";
    private String key = "df0fce537e4190037ccd16a7f49207cc";

    @Override
    public byte[] decode(byte[] msg) {
        if (msg == null) {
            return new byte[0];
        }
        DDLog.d(TAG, "-------------------------解密前--------------------------");
        DDLog.d(TAG, "| " + new String(msg));
        DDLog.d(TAG, "-------------------------解密后Hex--------------------------");
        try {
            byte[] result = Encryption.decryptAes(iv, key, msg);
            DDLog.d(TAG, "| " + HexUtil.bytesToHexString(result));
            DDLog.d(TAG, "--------------------------------------------------------");
            return result;
        } catch (Exception e) {
            DDLog.d(TAG, "| 加密失败");
            DDLog.d(TAG, "--------------------------------------------------------");
            return msg;
        }
    }

    @Override
    public byte[] encode(byte[] msg) {
        if (msg == null) {
            return new byte[0];
        }
        DDLog.d(TAG, "-------------------------加密前--------------------------");
        DDLog.d(TAG, "| " + new String(msg));
        DDLog.d(TAG, "-------------------------加密后Hex--------------------------");
        try {
            byte[] result = Encryption.encryptAes(iv, key, msg);
            DDLog.d(TAG, "| " + HexUtil.bytesToHexString(result));
            DDLog.d(TAG, "--------------------------------------------------------");
            return result;
        } catch (Exception e) {
            DDLog.d(TAG, "| 加密失败");
            DDLog.d(TAG, "--------------------------------------------------------");
            return msg;
        }
    }
}
