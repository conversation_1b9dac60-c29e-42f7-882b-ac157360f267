package com.dinsafer.module_dscam;

import android.bluetooth.le.ScanRecord;
import android.content.Context;
import android.os.Handler;
import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.clj.fastble.callback.BleScanCallback;
import com.clj.fastble.data.BleDevice;
import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.activtor.bean.Plugin;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.utils.BleHelper;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.msctlib.utils.MsctJSONUtil;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_dscam.bean.CamBleWifiInfo;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Method;
import java.math.BigInteger;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;

/**
 * Cam添加器基类,蓝牙
 */
public abstract class BaseCamBinder extends BasePluginBinder {

    protected String mSsid, mSsidPassword, mHomeID;
    protected boolean mAuth;

    private boolean isTimeOutToFindDevice = false;

    protected String UUID;
    protected String WRITE_UUID;
    protected String READ_UUID;
    private boolean isNeedRecovery = false;

    private BleHelper.IMessageCallback callback = new BleHelper.IMessageCallback() {
        @Override
        public void onMessage(byte[] notifyData) {
            handlerMessage(notifyData);
        }
    };

    private IWifiListCallBack wifiListCallBack;

    private DsCamConvert camConvert = new DsCamConvert();


    @Keep
    public BaseCamBinder(@NonNull Context mContext) {
        super(mContext);
    }


    @Override
    @Keep
    public void bindDevice(Plugin plugin) {
        //
        if (TextUtils.isEmpty(mSsid)) {
            callBackBindResult(ErrorCode.DEFAULT, "ssid is null");
            return;
        }

        if (mAuth && TextUtils.isEmpty(mSsidPassword)) {
            callBackBindResult(ErrorCode.DEFAULT, "password is null");
            return;
        }

        if (TextUtils.isEmpty(mHomeID)) {
            callBackBindResult(ErrorCode.DEFAULT, "homeID is null");
            return;
        }
        if (isNeedRecovery) {
            setAppID();
        } else {
            setSsid();
        }
    }

    @Keep
    public void setSsid(String mSsid) {
        this.mSsid = mSsid;
    }

    @Keep
    public void setAuth(boolean mAuth) {
        this.mAuth = mAuth;
    }

    @Keep
    public void setSsidPassword(String mSsidPassword) {
        this.mSsidPassword = mSsidPassword;
    }

    @Keep
    public void setBindHomeID(String mHomeID) {
        this.mHomeID = mHomeID;
    }

    private void startConfigWifi() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsCamBleCmd.SET_NET);
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void bindDsCam() {
        setAppID();
    }

    @Keep
    public void discoveryDevice(long timeout, BleScanCallback bleScanCallback) {
        BleHelper.getInstance().setScanRuleWithUUID(timeout, UUID, WRITE_UUID, READ_UUID);
        BleHelper.getInstance().scanDevice(bleScanCallback);
    }

    @Keep
    public void stopDiscoveryDevice() {
        BleHelper.getInstance().cancelScan();
    }

    @Keep
    public void connectDevice(BleDevice dsCam,
                              BleHelper.ConnectCallback connectCallback) {
        isNeedRecovery = isNeedRecoveryDevice(dsCam);
        DDLog.d(TAG, "connectDevice-->isNeedRecovery:" + isNeedRecovery);
        BleHelper.getInstance().release();
        BleHelper.getInstance().addConnectCallBack(connectCallback);
        BleHelper.getInstance().addMessageCallBack(callback);
        BleHelper.getInstance().connected(dsCam, camConvert);
    }

    @Keep
    public void getWifiList() {
        wifiList.clear();
        supportPwdFreeNetwork = false;
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", "get_wifi_list");
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Keep
    public IWifiListCallBack getWifiListCallBack() {
        return wifiListCallBack;
    }

    @Keep
    public void setWifiListCallBack(IWifiListCallBack wifiListCallBack) {
        this.wifiListCallBack = wifiListCallBack;
    }

    private List<CamBleWifiInfo> wifiList = new ArrayList<>();
    private boolean supportPwdFreeNetwork;
    private static final int GET_WIFI_LIST_TIMEOUT = 3 * 1000;
    private boolean startCountdown;

    private void handlerMessage(byte[] notifyData) {
        String js = new String(notifyData);
        MsctLog.i(TAG, "handler rece:" + js);
        try {
            JSONObject jsonObject = new JSONObject(js);
            String cmd = MsctJSONUtil.getString(jsonObject, "cmd");
            switch (cmd) {
                case DsCamBleCmd.GET_WIFI_LIST:
                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
                        if (wifiListCallBack != null && startCountdown) {
                            wifiListCallBack.onWifiListCallBack(wifiList, supportPwdFreeNetwork);
                            startCountdown = false;
                        }
                    } else {
                        final String ssid = MsctJSONUtil.getString(jsonObject, "result");
                        CamBleWifiInfo wifiInfo = new CamBleWifiInfo(ssid);
                        if (MsctJSONUtil.has(jsonObject, "rssi")
                                && MsctJSONUtil.has(jsonObject, "auth")) {
                            final int rssi = MsctJSONUtil.getInt(jsonObject, "rssi");
                            final boolean auth = MsctJSONUtil.getBoolean(jsonObject, "auth");
                            wifiInfo.setRssi(rssi);
                            wifiInfo.setAuth(auth);
                            supportPwdFreeNetwork = true;
                        }
                        wifiList.add(wifiInfo);
                        // 有可能存在接受不到 "status":1 的情况，为兼容此状况做3s超时，3s后直接处理结果
                        if (!startCountdown) {
                            new Handler().postDelayed(() -> {
                                if (wifiListCallBack != null && startCountdown) {
                                    wifiListCallBack.onWifiListCallBack(wifiList, supportPwdFreeNetwork);
                                    startCountdown = false;
                                }
                            }, GET_WIFI_LIST_TIMEOUT);
                            startCountdown = true;
                        }
                    }
                    break;
                case DsCamBleCmd.SET_WIFI_NAME:
                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
                        setSsidPW();
                    } else {
                        callBackBindResult(ErrorCode.DEFAULT, "wifi ssid error");
                    }
                    break;
                case DsCamBleCmd.SET_WIFI_PASSWORD:
                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
                        startConfigWifi();
                    } else {
                        callBackBindResult(ErrorCode.DEFAULT, "wifi ssid error");
                    }
                    break;
                case DsCamBleCmd.SET_NET:
                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
                        if (isNeedRecovery) {
                            callBackBindResult(3, null);
                        } else {
                            bindDsCam();
                        }
                    } else {
                        callBackBindResult(ErrorCode.DEFAULT, "wifi connect error");
                    }
                    break;
                case DsCamBleCmd.SET_APP_ID:
                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
                        setAppSecret();
                    } else {
                        callBackBindResult(ErrorCode.DEFAULT, "wifi appid error");
                    }
                    break;
                case DsCamBleCmd.SET_APP_SECRET:
                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
                        setHTTPHost();
                    } else {
                        callBackBindResult(ErrorCode.DEFAULT, "wifi sec error");
                    }
                    break;
                case DsCamBleCmd.SET_HTTP_HOST:
                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
                        if (isNeedRecovery) {
                            setSsid();
                        } else {
                            setHomeID();
                        }
                    } else {
                        callBackBindResult(ErrorCode.DEFAULT, "wifi udp error");
                    }
                    break;
                case DsCamBleCmd.SET_HOME:
                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
                        register();
                    } else {
                        callBackBindResult(ErrorCode.DEFAULT, "wifi homeid error");
                    }
                    break;
                case DsCamBleCmd.REGISTER:
                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
                        handleRegisterSuccess(jsonObject);
                        callBackBindResult(1, MsctJSONUtil.getString(jsonObject, "pid"));
                    } else {
                        callBackBindResult(MsctJSONUtil.getInt(jsonObject, "status"), "bind ipc error");
                    }
                    break;
                case DsCamBleCmd.SET_TIMEZONE:
                    callBackBindResult(21, "set timezone success");
                    updateConfig();
                    break;
                case DsCamBleCmd.UPDATE_CONFIG:
                    callBackBindResult(22, "update config success");
                    break;
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    protected abstract void handleRegisterSuccess(JSONObject data) throws JSONException;

    private void setSsid() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsCamBleCmd.SET_WIFI_NAME);
            jsonObject.put("data", mSsid);
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void setSsidPW() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsCamBleCmd.SET_WIFI_PASSWORD);
            jsonObject.put("data", mSsidPassword);
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void setAppID() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsCamBleCmd.SET_APP_ID);
            jsonObject.put("data", DinCore.getInstance().getAppID());
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void setAppSecret() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsCamBleCmd.SET_APP_SECRET);
            jsonObject.put("data", DinCore.getInstance().getAppSecret());
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void setHTTPHost() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsCamBleCmd.SET_HTTP_HOST);
            jsonObject.put("data", DinCore.getInstance().getDomain());
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void setHomeID() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsCamBleCmd.SET_HOME);
            jsonObject.put("data", mHomeID);
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void register() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsCamBleCmd.REGISTER);
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void setTimeZone() {
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("GMT"),
                Locale.getDefault());
        Date currentLocalTime = calendar.getTime();

        DateFormat date = new SimpleDateFormat("ZZZZZ", Locale.getDefault());
        String localTime = date.format(currentLocalTime);
        localTime = "tzn" + localTime + ":00";
        DDLog.i("timezone", localTime);

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsCamBleCmd.SET_TIMEZONE);
            jsonObject.put("tz", localTime);
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }


    private void updateConfig() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", DsCamBleCmd.UPDATE_CONFIG);
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Keep
    public void syncConfig() {
        setTimeZone();
    }

    /**
     * 是否是变砖的IPC
     *
     * @param bleDevice
     * @return
     */
    private boolean isNeedRecoveryDevice(BleDevice bleDevice) {
        int dataByIndex = getDataByIndex(bleDevice, 1);
        return dataByIndex == 1;
    }

    /**
     * 通过ScanRecord,获取到他的ServiceData，获取到前两位
     * 第一个 1代表新的未被添加过或已被Reset的IPC  0代表旧的已被添加或配网的IPC
     * 第二位 1代表变砖 0代表正常
     */
    private int getDataByIndex(final BleDevice bleDevice, final int index) {
        int result = -1;
        try {
            ScanRecord scanRecord = parseScanRecordFromBytes(bleDevice.getScanRecord());
            if (scanRecord.getServiceData().size() <= 0) {
                return result;
            }
            byte[] data = scanRecord.getServiceData().entrySet().iterator().next().getValue();
            char[] dataChar = String.format("%08d", Integer.valueOf(new BigInteger(1, data).toString(2))).toCharArray();
            DDLog.d(TAG, "service data is" + Arrays.toString(dataChar));
            result = Character.getNumericValue(dataChar[index]);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on get panel data by index: " + index);
            e.printStackTrace();
        }
        return result;
    }

    @Keep
    private ScanRecord parseScanRecordFromBytes(byte[] bytes) {
        try {
            Method parseFromBytes = ScanRecord.class.getMethod("parseFromBytes", byte[].class);
            return (ScanRecord) parseFromBytes.invoke(null, (Object) bytes);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void stop() {
        super.stop();
        stopDiscoveryDevice();
        BleHelper.getInstance().disconnectAllDevice();
    }

    @Override
    @Keep
    public void destroyBinder() {
        super.destroyBinder();
        wifiListCallBack = null;
        BleHelper.getInstance().release();
        BleHelper.getInstance().disconnectAllDevice();
    }

    @Keep
    public interface IWifiListCallBack {
        void onWifiListCallBack(List<CamBleWifiInfo> wifis, boolean supportPwdFreeNetwork);
    }

}
