plugins {
    id 'com.android.library'
}

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    buildToolsVersion rootProject.ext.android.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode rootProject.ext.android.versionCode
        versionName rootProject.ext.android.versionName

        consumerProguardFiles "consumer-rules.pro"

        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a'
        }
    }

    buildTypes {
        release {
            debuggable rootProject.ext.debuggable
            minifyEnabled rootProject.ext.minifyEnabled
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility rootProject.ext.Java_Version
        targetCompatibility rootProject.ext.Java_Version
    }


    externalNativeBuild {
        cmake {
            path "src/main/cpp/CMakeLists.txt"
//            version "3.10.2"
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'androidx.appcompat:appcompat:1.0.0'
    implementation 'io.reactivex:rxjava:1.1.6'
    implementation 'io.reactivex:rxandroid:1.2.1'
    implementation files('libs/PhotoTextureView_V1.0.jar')
    if (isAar) {
        api 'com.dinsafer.dinsdk:dincore:' + rootProject.ext.android.versionName + rootProject.ext.dependlibVersionPostfixt
    } else {
        api project(':dincore')
    }

    //webrtc need
    implementation 'org.webrtc:google-webrtc:1.0.28513'
    implementation 'androidx.annotation:annotation:1.2.0'
    def aws_version = '2.16.5'
    implementation("com.amazonaws:aws-android-sdk-kinesisvideo:$aws_version@aar") { transitive = true }
    implementation("com.amazonaws:aws-android-sdk-kinesisvideo-signaling:$aws_version@jar") { transitive = true }
    implementation 'org.osgi:org.osgi.framework:1.9.0'
    implementation('org.glassfish.tyrus.bundles:tyrus-standalone-client:1.12') {
        exclude module: 'javax.inject'
    }
    implementation 'org.awaitility:awaitility:3.0.0'
    implementation 'com.google.code.gson:gson:2.8.6'
    implementation 'org.slf4j:slf4j-api:1.7.29'
    implementation 'org.apache.commons:commons-lang3:3.9'
    implementation 'com.alibaba:fastjson:1.1.67.android'
    implementation files('libs/guava-31.0.1-android.jar')
}

ext.modulePublishConfig = [
        artifactId: 'dscam',
]

// 使用maven插件上传
apply from: rootProject.file('gradle-scripts/upload.gradle')

// 使用maven-publish插件上传
//apply from: rootProject.file('gradle-scripts/publish.gradle')

