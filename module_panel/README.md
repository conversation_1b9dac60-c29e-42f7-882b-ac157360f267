# 额外配置
1. 主机coap操作需要在app module中添加以下配置

```groovy
android {
    packagingOptions {
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/INDEX.LIST'
        exclude 'META-INF/services/javax.annotation.processing.Processor'

        exclude '3rd-party/APACHE-LICENSE-2.0.txt'
        exclude '3rd-party/cc0-legalcode.html'
        exclude '3rd-party/BSD-3-Clause-LICENSE.txt'
        exclude '3rd-party/*.txt'
        exclude '3rd-party/*.html'

        pickFirst 'about.html'
        pickFirst 'edl-v10.html'
        pickFirst 'epl-v10.html'
        pickFirst 'notice.html'
        pickFirst 'META-INF/LICENSE'
        pickFirst 'META-INF/io.netty.versions.properties'
        pickFirst 'LICENSE-2.0.txt'
        exclude '**/*.jks'
    }
}
```