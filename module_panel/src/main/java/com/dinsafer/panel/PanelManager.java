package com.dinsafer.panel;

import android.app.Application;
import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.db.DBKey;
import com.dinsafer.dincore.db.cache.DeviceCacheHelper;
import com.dinsafer.dincore.user.bean.DinUser;
import com.dinsafer.dssupport.msctlib.db.KV;
import com.dinsafer.dssupport.plugin.PluginConstants;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.bean.PanelCacheInfo;
import com.dinsafer.panel.bean.device.PanelDevice;
import com.dinsafer.panel.common.IPanel;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.operate.PanelOperator;
import com.dinsafer.panel.operate.bean.ListPanelTokensResponse;
import com.dinsafer.panel.operate.bean.PanelInfoNew;
import com.dinsafer.panel.operate.callback.PanelCallbackHelper;
import com.dinsafer.panel.operate.net.PanelNetworkRequestManager;
import com.dinsafer.panel.operate.task.CheckTimeOutTask;
import com.dinsafer.panel.operate.task.DeviceCmdTimeoutCallback;
import com.dinsafer.panel.operate.task.DeviceCmdTimeoutChecker;
import com.dinsafer.panel.operate.task.DeviceWorkQueue;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 主机管理
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/10 11:47 AM
 */
@Keep
public class PanelManager implements IPanel, DeviceCmdTimeoutCallback {
    /**
     * 首页支持的配件列表
     */
    public static final String[] HOME_STYPE_DOOR_SENSORS = {PluginConstants.TYPE_38, PluginConstants.TYPE_3D}; // 门磁
    public static final String[] HOME_STYPE_SMART_PLUG = {PluginConstants.TYPE_3E}; // 插座
    public static final String[] HOME_STYPE_ALL = {PluginConstants.TYPE_38, PluginConstants.TYPE_3D
            , PluginConstants.TYPE_3E, PluginConstants.TYPE_4E}; // 全部类型

    private static final String CACHE_IDENTIFY = "PanelService"; // 缓存key后缀
    private final String TAG = PanelManager.class.getSimpleName();

    private Application mApp;

    private final PanelCallbackHelper mCallbackHelper = new PanelCallbackHelper();
    private final PanelNetworkRequestManager mNetworkRequestManager = new PanelNetworkRequestManager();
    private final PanelOperator mPanelOperator = new PanelOperator();
    private final DeviceCmdTimeoutChecker mTimeoutChecker = new DeviceCmdTimeoutChecker();

    private final Map<String, PanelDevice> mPanelDeviceMap = new HashMap<>();
    private String mCurrentPanelId; // 当前主机ID
    private String mCurrentPanelToken; // 当前主机Token
    private String mCurrentPanelName; // 当前主机名字
    private String mCurrentHomeId; // 当前家庭的ID
    private String mSimNetwork; // 2G、4G的主机

    private PanelManager() {
        mTimeoutChecker.addTaskTimeoutCallback(this);
    }

    private final PanelCacheInfo cacheInfo = new PanelCacheInfo();
    private final PanelCacheInfo lastCacheInfo = new PanelCacheInfo();
    private PanelDevice lastPanelDevice = null; // 当缓存的主机和返回的主机不一样时，上一次缓存的主机信息

    private static class Holder {
        private static final PanelManager INSTANCE = new PanelManager();
    }

    @Keep
    public static PanelManager getInstance() {
        return Holder.INSTANCE;
    }

    @Override
    public void initPanelManager(Application application, String wsDomain) {
        this.mApp = application;
        DeviceWorkQueue.init();
        mPanelOperator.initPanelOperator(application, wsDomain);
    }

    @Keep
    @Override
    public void destroyPanelManager() {
        mTimeoutChecker.removeTaskTimeoutCallback(this);
        mPanelOperator.destroyPanelOperator();
    }

    @Keep
    @Override
    public PanelCallbackHelper getPanelCallbackHelper() {
        return mCallbackHelper;
    }

    @Keep
    public void requestGetPluginStatus(String panelToken) {
        mNetworkRequestManager.requestHomePluginStatus(panelToken);
    }

    @Keep
    public void requestGetPanelInfo(final String deviceId) {
        mNetworkRequestManager.requestUpdatePanelInfo(mCurrentHomeId, deviceId);
    }

    public PanelNetworkRequestManager getNetworkRequestManager() {
        return mNetworkRequestManager;
    }

    public PanelOperator getPanelOperator() {
        return mPanelOperator;
    }


    public void setNeedLoadInfoAgain() {
        if (0 < mPanelDeviceMap.size()) {
            for (PanelDevice panelDevice : mPanelDeviceMap.values()) {
                panelDevice.setNeedLoadInfoAgain();
            }
        }
    }

    /**
     * 同步获取主机列表
     * <p>
     * 每次都会创建新的Device
     *
     * @return 主机列表
     */
    public ArrayList<Device> requestPanelDevicesSync() {
        lastPanelDevice = null;
        lastCacheInfo.updateFrom(null);

        mTimeoutChecker.cleanAllTask();
        DeviceWorkQueue.getInstance().clearTask();
        String panelId = KV.getString(DBKey.CURRENT_DEVICE_ID, null);

        // 更新token
        // 理想情况是直接通过listPanelTokensSync来判断是否有主机的
        // 但由于目前部分信息没有返回，依旧需要从home那里获取(如是否4G主机)
        // 所以已经直接使用home那里的主机信息，这里仅仅刷新token
        if (!TextUtils.isEmpty(panelId)) {
            List<ListPanelTokensResponse.ResultBean.TokensBean> tokensBeans = mNetworkRequestManager.listPanelTokensSync(mCurrentHomeId, 1, false, 20);
            if (null != tokensBeans && tokensBeans.size() > 0) {
                ListPanelTokensResponse.ResultBean.TokensBean tokensBean;
                for (int i = 0; i < tokensBeans.size(); i++) {
                    tokensBean = tokensBeans.get(i);
                    final String token = tokensBean.getToken();
                    if (panelId.equals(tokensBean.getId())) {
                        if (!TextUtils.isEmpty(token)) {
                            DDLog.i(TAG, "刷新Panel的token");
                            mCurrentPanelToken = token;
                        }
                        break;
                    }
                }
            }
        }

        // 1、当前家庭没有返回主机ID
        if (TextUtils.isEmpty(panelId)) {
            releaseAllDevices();
            mPanelDeviceMap.clear();
            mPanelOperator.toCloseWs();

            // 1.1 如果有缓存，需要创建缓存Device
            final ArrayList<Device> deviceList = new ArrayList<>();
            // 当前家庭已经没有主机了-不需要自动删除缓存的device
            final String cacheId = cacheInfo.getPanelId();

            if (!TextUtils.isEmpty(cacheId)) {
                DDLog.d(TAG, "创建缓存的主机Device");
                mCurrentPanelId = cacheId;
                final PanelDevice cachePanel = PanelDevice.createFromDeletedInfo(new PanelCacheInfo(cacheInfo));
                deviceList.add(cachePanel);
                mPanelDeviceMap.put(cacheId, cachePanel);
            }
            return deviceList;
        }

        // 2. 当前家庭返回了主机ID
        List<PanelInfoNew> panelInfoNews = mNetworkRequestManager.searchPanelsSync(mCurrentHomeId, Collections.singletonList(panelId));
        PanelInfoNew currentPanelInfo = null; // 当前家庭主机的信息
        if (null != panelInfoNews && panelInfoNews.size() > 0) {
            for (int i = 0; i < panelInfoNews.size(); i++) {
                PanelInfoNew temp = panelInfoNews.get(i);
                if (panelId.equals(temp.getDevice_id())) {
                    currentPanelInfo = temp;
                    break;
                }
            }
        }

        final ArrayList<Device> deviceList = new ArrayList<>();
        final String cacheId = cacheInfo.getPanelId();
        // 2.1 查询不到返回主机ID的具体信息
        if (null == currentPanelInfo) {
            // 先清除缓存中的主机
            PanelDevice panelDevice = mPanelDeviceMap.get(panelId);
            if (null == panelDevice) {
                releaseAllDevices();
                mPanelDeviceMap.clear();
            }

            // 看看有没有缓存的主机信息
            if (!TextUtils.isEmpty(cacheId)) {
                DDLog.i(TAG, "创建缓存的主机Device1");
                mCurrentPanelId = cacheId;
                final PanelDevice cachePanel = PanelDevice.createFromDeletedInfo(new PanelCacheInfo(cacheInfo));
                deviceList.add(cachePanel);
                mPanelDeviceMap.put(cacheId, cachePanel);
            }
            return deviceList;
        }

        // 2.2 查询到了当前主机的信息
        // 2.2.1 创建在线的主机
        PanelDevice panelDevice = mPanelDeviceMap.get(panelId);
        if (null == panelDevice) {
            releaseAllDevices();
            mPanelDeviceMap.clear();
            panelDevice = PanelDevice.createFromPanelInfo(mCurrentPanelToken, currentPanelInfo,
                    panelId.equals(cacheId));

        } else {
            panelDevice.updateInfoMap(currentPanelInfo, false);
            panelDevice.setDeleteByOther(false);
            panelDevice.setPanelToken(mCurrentPanelToken);
        }

        if (null != panelDevice) {
            panelDevice.markFlagLoaded();
            panelDevice.getInfo().put(PanelDataKey.Panel.DEVICE_TOKEN, mCurrentPanelToken);
            panelDevice.getInfo().put(PanelDataKey.Panel.NAME, mCurrentPanelName);
            panelDevice.getInfo().put(PanelDataKey.Panel.SIM_NETWORK, mSimNetwork);
            mCurrentPanelId = panelId;
            mPanelDeviceMap.put(panelDevice.getId(), panelDevice);
            deviceList.add(panelDevice);
            if (currentPanelInfo.isOnline()) {
                mPanelOperator.connectWs();
            } else {
                mPanelOperator.toCloseWs();
            }
            panelDevice.requestCareModeInfo();
        } else {
            mPanelOperator.toCloseWs();
        }

        // 2.2.2 看看有没有缓存的不同主机ID的主机
        if (!TextUtils.isEmpty(cacheId) && !cacheId.equals(panelId)) {
            DDLog.i(TAG, "创建缓存的主机Device2");
            final PanelDevice cachePanel = PanelDevice.createFromDeletedInfo(new PanelCacheInfo(cacheInfo));
            lastPanelDevice = cachePanel;
            lastCacheInfo.updateFrom(cacheInfo);
            deviceList.add(cachePanel);
        }

        // 家庭修改了主机，需要更新缓存
        if (TextUtils.isEmpty(cacheId) || !panelId.equals(cacheId)) {
            cacheInfo.updateFrom(new PanelCacheInfo(panelId));
            saveDeviceCache();
        }

        return deviceList;
    }

    /**
     * @param sub
     * @param cacheFirst 仅在加载首页配件的时候有区别
     *                   true: 有缓存，仅加载缓存；没有缓存等同于{{@link #requestDeviceByType(String)}}
     *                   false: 返回下一次加载的数据或缓存+下一次加载的数据
     * @return
     */
    public ArrayList<Device> requestDeviceByType(String sub, boolean cacheFirst) {
        if (!PanelConstant.DeviceType.HOME_PLUGIN.equals(sub)) {
            return requestDeviceByType(sub);
        }

        // 首页配件-
        final ArrayList<Device> devices = new ArrayList<>();
        if (!TextUtils.isEmpty(sub)
                && !TextUtils.isEmpty(mCurrentPanelId)) {
            final PanelDevice currentPanel = mPanelDeviceMap.get(mCurrentPanelId);
            if (null != currentPanel) {
                // 当前主机的首页配件
                final ArrayList<Device> currentList = currentPanel.requestHomePluginListSyncNew(cacheInfo, cacheFirst);
                if (null != currentList) {
                    devices.addAll(currentList);
                }
            }
            // 旧主机主机的首页配件
            if (null != lastPanelDevice) {
                DDLog.i(TAG, "读取旧主机首页缓存的配件列表");
                final ArrayList<Device> currentList = lastPanelDevice.requestHomePluginListSyncNew(lastCacheInfo, cacheFirst);
                if (null != currentList) {
                    devices.addAll(currentList);
                }
                lastPanelDevice.destory();
                lastCacheInfo.updateFrom(null);
            }
        }

        return devices;
    }

    /**
     * @param type 仅有首页的配件支持获取缓存数据
     * @return
     */
    @NonNull
    public List<Device> requestCacheDeviceByType(String type) {
        if (!PanelConstant.DeviceType.HOME_PLUGIN.equals(type)) {
            return new ArrayList<>();
        }

        // 首页配件-
        final ArrayList<Device> devices = new ArrayList<>();
        if (!TextUtils.isEmpty(type)
                && !TextUtils.isEmpty(mCurrentPanelId)) {
            final PanelDevice currentPanel = mPanelDeviceMap.get(mCurrentPanelId);
            if (null != currentPanel) {
                // 当前主机的首页配件
                final List<Device> currentList = currentPanel.requestCacheHomePlugin(cacheInfo);
                if (null != currentList) {
                    devices.addAll(currentList);
                }
            }
            // 旧主机主机的首页配件
            if (null != lastPanelDevice) {
                DDLog.i(TAG, "读取旧主机首页缓存的配件列表");
                final List<Device> currentList = lastPanelDevice.requestCacheHomePlugin(lastCacheInfo);
                if (null != currentList) {
                    devices.addAll(currentList);
                }
                lastPanelDevice.destory();
                lastCacheInfo.updateFrom(null);
            }
        }

        return devices;
    }

    @NonNull
    public List<Device> requestLocalAndNewDeviceByType(String type) {
        if (!PanelConstant.DeviceType.HOME_PLUGIN.equals(type)) {
            return requestDeviceByType(type);
        }

        // 首页配件-
        final ArrayList<Device> devices = new ArrayList<>();
        if (!TextUtils.isEmpty(type)
                && !TextUtils.isEmpty(mCurrentPanelId)) {
            final PanelDevice currentPanel = mPanelDeviceMap.get(mCurrentPanelId);
            if (null != currentPanel) {
                // 当前主机的首页配件
                final List<Device> currentList = currentPanel.requestLocalAndNewHomePlugin(cacheInfo);
                if (null != currentList) {
                    devices.addAll(currentList);
                }
            }
            // 旧主机主机的首页配件
            if (null != lastPanelDevice) {
                DDLog.i(TAG, "读取旧主机首页缓存的配件列表");
                final List<Device> currentList = lastPanelDevice.requestLocalAndNewHomePlugin(lastCacheInfo);
                if (null != currentList) {
                    devices.addAll(currentList);
                }
                lastPanelDevice.destory();
                lastCacheInfo.updateFrom(null);
            }
        }

        return devices;
    }

    @NonNull
    public List<Device> requestAllDeviceByType(String type) {
        if (!PanelConstant.DeviceType.HOME_PLUGIN.equals(type)) {
            return requestDeviceByType(type);
        }

        // 首页配件-
        final ArrayList<Device> devices = new ArrayList<>();
        if (!TextUtils.isEmpty(type)
                && !TextUtils.isEmpty(mCurrentPanelId)) {
            final PanelDevice currentPanel = mPanelDeviceMap.get(mCurrentPanelId);
            if (null != currentPanel) {
                // 当前主机的首页配件
                final List<Device> currentList = currentPanel.requestAllHomePlugin(cacheInfo);
                if (null != currentList) {
                    devices.addAll(currentList);
                }
            }
            // 旧主机主机的首页配件
            if (null != lastPanelDevice) {
                DDLog.i(TAG, "读取旧主机首页缓存的配件列表");
                final List<Device> currentList = lastPanelDevice.requestAllHomePlugin(lastCacheInfo);
                if (null != currentList) {
                    devices.addAll(currentList);
                }
                lastPanelDevice.destory();
                lastCacheInfo.updateFrom(null);
            }
        }

        return devices;
    }

    @NonNull
    public ArrayList<Device> requestDeviceByType(String sub) {
        ArrayList devices = new ArrayList();
        if (!TextUtils.isEmpty(sub)
                && !TextUtils.isEmpty(mCurrentPanelId)) {
            final PanelDevice currentPanel = mPanelDeviceMap.get(mCurrentPanelId);
            if (null != currentPanel) {
                switch (sub) {
                    case PanelConstant.DeviceType.SMART_BUTTON:
                        devices = currentPanel.requestSmartButtonListSync();
                        break;
                    case PanelConstant.DeviceType.SIGNAL_REPEATER_PLUG:
                        devices = currentPanel.requestSignalRepeaterPlugList();
                        break;
                    case PanelConstant.DeviceType.SMART_PLUG:
                        devices = currentPanel.requestSmartPlugListSync();
                        break;
                    case PanelConstant.DeviceType.ROLLER_SHUTTER:
                        devices = currentPanel.requestRollerShutterListSync();
                        break;
                    case PanelConstant.DeviceType.SECURITY_ACCESSORY:
                        devices = currentPanel.requestSecurityAccessoryResultSync();
                        break;
                    case PanelConstant.DeviceType.DOOR_WINDOW_SENSOR:
                        devices = currentPanel.requestDoorSensorResultSync();
                        break;
                    case PanelConstant.DeviceType.WIRELESS_SIREN:
                        devices = currentPanel.requestWirelessSirenResultSync();
                        break;
                    case PanelConstant.DeviceType.REMOTE_CONTROL:
                        devices = currentPanel.requestRemoteControlResultSync();
                        break;
                    case PanelConstant.DeviceType.KEYBOARD_KEY_TAGS:
                        devices = currentPanel.requestKeypadDeviceListSync();
                        break;
                    case PanelConstant.DeviceType.DOORBELL:
                        devices = currentPanel.requestListDoorBellSync();
                        break;
                    case PanelConstant.DeviceType.OTHER_PLUGIN:
                        devices = currentPanel.requestOtherPluginListSync();
                        break;
                    case PanelConstant.DeviceType.HOME_PLUGIN:
                        // 当前主机的首页配件
                        ArrayList currentList = currentPanel.requestHomePluginListSyncNew(cacheInfo);
                        if (null != currentList) {
                            devices.addAll(currentList);
                        }
                        // 旧主机主机的首页配件
                        if (null != lastPanelDevice) {
                            DDLog.i(TAG, "读取旧主机首页缓存的配件列表");
                            currentList = lastPanelDevice.requestHomePluginListSyncNew(lastCacheInfo);
                            if (null != currentList) {
                                devices.addAll(currentList);
                            }
                            lastPanelDevice.destory();
                            lastCacheInfo.updateFrom(null);
                        }
                        break;
                    default:
                        break;
                }
            }
        }
        return devices;
    }

    public boolean releaseDeviceByType(String sub) {
        if (!TextUtils.isEmpty(sub)
                && !TextUtils.isEmpty(mCurrentPanelId)) {
            final PanelDevice currentPanel = mPanelDeviceMap.get(mCurrentPanelId);
            if (null != currentPanel) {
                return currentPanel.releasePluginDeviceByType(sub);
            }
        }
        return false;
    }

    public boolean removeDeviceCacheById(String sub) {
        if (TextUtils.isEmpty(sub)) {
            return false;
        }
        DDLog.i(TAG, "removeDeviceCacheById. mCurrentPanelId:" + mCurrentPanelId + "   cacheInfo: " + cacheInfo);
        // 清除主机缓存`
        if (sub.equals(cacheInfo.getPanelId())) {
            cacheInfo.getPluginList().clear();
            cacheInfo.setPanelId(null);
            cacheInfo.setUpdateTime(0);
            saveDeviceCache();
        }
        PanelDevice panelDevice = mPanelDeviceMap.get(sub);
        if (null != panelDevice) {
            panelDevice.destory();
            mPanelDeviceMap.remove(panelDevice.getId());
        }

        // 清除配件缓存
        PanelDevice curPluginDevice = mPanelDeviceMap.get(mCurrentPanelId);
        if (null != curPluginDevice) {
            curPluginDevice.removeDeviceById(sub);
            removeCache(mCurrentPanelId, sub);
            return true;
        }

        return false;
    }

    /**
     * 获取主机或配件Device
     *
     * @param deviceId 需要获取的DeviceId
     * @return 主机Device或配件Device
     */
    public Device getPanelOrPluginDevice(String deviceId) {
        if (TextUtils.isEmpty(deviceId)
                || 0 >= mPanelDeviceMap.size()) {
            return null;
        }

        // 主机
        Device result = mPanelDeviceMap.get(deviceId);
        if (null != result) {
            return result;
        }

        // 配件
        Collection<PanelDevice> panelDevices = mPanelDeviceMap.values();
        for (PanelDevice panelDevice : panelDevices) {
            result = panelDevice.getDeviceById(deviceId);
            if (null != result) {
                return result;
            }
        }

        return null;
    }

    /**
     * 获取当前的主机Device
     *
     * @return 当前主机信息
     */
    public PanelDevice getCurrentPanelDevice() {
        if (!TextUtils.isEmpty(mCurrentPanelId)) {
            return mPanelDeviceMap.get(mCurrentPanelId);
        }
        return null;
    }

    /**
     * 获取当前主机的Token
     *
     * @return 主机Token
     */
    public String getCurrentPanelToken() {
        return mCurrentPanelToken;
    }

    public void setCurrentPanelToken(String token) {
        this.mCurrentPanelToken = token;
    }

    /**
     * 获取当前主机的ID
     *
     * @return 主机Id
     */
    public String getCurrentPanelId() {
        return mCurrentPanelId;
    }

    /**
     * 获取主机Device
     *
     * @return 主机Device
     */
    public PanelDevice getPanelDeviceById(String panelId) {
        if (!TextUtils.isEmpty(panelId)
                && 0 < mPanelDeviceMap.size()) {
            return mPanelDeviceMap.get(panelId);
        }
        return null;
    }

    public void releaseAllDevicesAncCache() {
        cacheInfo.updateFrom(null);
        lastPanelDevice = null;
        lastCacheInfo.updateFrom(null);
        releaseAllDevices();
        mPanelDeviceMap.clear();
    }

    /**
     * 释放Device的资源
     * <p>
     * 切换主机时需要调用
     */
    public void releaseAllDevices() {
        mTimeoutChecker.cleanAllTask();
        DeviceWorkQueue.getInstance().clearTask();
        if (0 < mPanelDeviceMap.size()) {
            for (PanelDevice panelDevice : mPanelDeviceMap.values()) {
                panelDevice.destory();
            }
        }
    }

    public void resetDeviceInfo() {
        mCurrentPanelId = "";
        mCurrentPanelToken = "";
        mCurrentPanelName = "";
        mSimNetwork = "";
    }

    public List<Device> getAllPanelDevices() {
        return new ArrayList<>(mPanelDeviceMap.values());
    }

    public void setCurrentHomeId(String currentHomeId) {
        final String lastHomeId = this.mCurrentHomeId;
        this.mCurrentHomeId = currentHomeId;

        // 切换了房间，清空缓存数据
        if (!TextUtils.isEmpty(lastHomeId) && !lastHomeId.equals(this.mCurrentHomeId)) {
            cacheInfo.updateFrom(null);
            lastPanelDevice = null;
            lastCacheInfo.updateFrom(null);
            releaseAllDevices();
        }
    }

    public String getCurrentHomeId() {
        return mCurrentHomeId;
    }

    public String getCurrentPanelName() {
        return mCurrentPanelName;
    }

    public void setCurrentPanelName(String currentPanelName) {
        this.mCurrentPanelName = currentPanelName;
    }

    public String getSimNetwork() {
        return mSimNetwork;
    }

    public void setSimNetwork(String simNetwork) {
        this.mSimNetwork = simNetwork;
    }

    public DeviceCmdTimeoutChecker getTimeoutChecker() {
        return mTimeoutChecker;
    }

    @Override
    public void onCmdTaskTimeout(CheckTimeOutTask timeOutTask) {
        DDLog.i(TAG, "onCmdTaskTimeout, 尝试重连WebSocket");
        mTimeoutChecker.cleanAllTask();
        mPanelOperator.connectWs();
    }

    public void readCacheInfo() {
        if (cacheInfo.getUpdateTime() > 0) {
            return;
        }

        final String homeId = mCurrentHomeId;
        DinUser user = DinCore.getUserInstance().getUser();
        final String userId = null != user ? user.getUser_id() : null;
        PanelCacheInfo cache = DeviceCacheHelper.getCache(homeId, userId, CACHE_IDENTIFY, PanelCacheInfo.class);
        this.cacheInfo.updateFrom(cache);
        DDLog.d(TAG, CACHE_IDENTIFY + ":cache--------readcache: " + cacheInfo);
    }

    public void saveDeviceCache() {
        final String homeId = PanelManager.getInstance().getCurrentHomeId();
        DinUser user = DinCore.getUserInstance().getUser();
        final String userId = null != user ? user.getUser_id() : null;
        DeviceCacheHelper.saveCacheAsync(homeId, userId, CACHE_IDENTIFY, cacheInfo);
        DDLog.d(TAG, CACHE_IDENTIFY + ":cache--------savecache: " + cacheInfo);
    }

    public void clearDeviceCache() {
        final String homeId = PanelManager.getInstance().getCurrentHomeId();
        DinUser user = DinCore.getUserInstance().getUser();
        final String userId = null != user ? user.getUser_id() : null;
        DeviceCacheHelper.removeCacheASync(homeId, userId, CACHE_IDENTIFY);
    }

    public void removeCache(String fatherId, String deviceId) {
        if (TextUtils.isEmpty(fatherId) || TextUtils.isEmpty(deviceId)) {
            DDLog.e(TAG, "Failed on removeCache, fatherId or deviceId is null");
            return;
        }

        if (fatherId.equals(cacheInfo.getPanelId())) {
            List<PanelCacheInfo.PluginCache> pluginList = cacheInfo.getPluginList();
            for (int i = 0; i < pluginList.size(); i++) {
                PanelCacheInfo.PluginCache pluginCache = pluginList.get(i);
                if (deviceId.equals(pluginCache.getId())) {
                    cacheInfo.getPluginList().remove(i);
                    break;
                }
            }
            saveDeviceCache();
        }
    }

    public boolean addPluginCache(final String fatherId, final PanelCacheInfo.AskPluginCache askPlugin) {
        if (null != fatherId && fatherId.equals(cacheInfo.getPanelId())) {
            final boolean success = cacheInfo.addPlugin(askPlugin);
            if (success) {
                saveDeviceCache();
            }
            return success;
        }
        return false;
    }

    public void updateAskPluginCacheName(final String fatherId, final String pluginId, final String name) {
        DDLog.i(TAG, "updateAskPluginCacheName pluginId : " + pluginId + "  name: " + name);
        if (TextUtils.isEmpty(pluginId) || TextUtils.isEmpty(name)) {
            return;
        }
        if (null != fatherId && fatherId.equals(cacheInfo.getPanelId())) {
            boolean update = false;
            for (PanelCacheInfo.PluginCache pluginCache : cacheInfo.getPluginList()) {
                if (!(pluginCache instanceof PanelCacheInfo.AskPluginCache)) {
                    continue;
                }
                if (pluginCache.getId().equals(pluginId)) {
                    ((PanelCacheInfo.AskPluginCache) pluginCache).setName(name);
                    update = true;
                    break;
                }
            }
            if (update) {
                saveDeviceCache();
            }
        }
    }
}
