package com.dinsafer.panel.util;

import com.dinsafer.dincore.common.NetKeyConstants;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dssupport.plugin.PluginConstants;
import com.dinsafer.dssupport.plugin.PluginTypeHelper;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.common.PanelConstant;

import org.json.JSONObject;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/6/27 16:24
 */
public class PanelPluginUtil {
    private static final String TAG = PanelPluginUtil.class.getSimpleName();

    /**
     * 获取旧配件的配件类型
     *
     * @param jsonObject
     * @return
     */
    public static int getOldPluginCategory(JSONObject jsonObject) {
        try {
            int category = DDJSONUtil.getInt(jsonObject, NetKeyConstants.NET_KEY_CATEGORY);
            if (DDJSONUtil.has(jsonObject, NetKeyConstants.NET_KEY_DECODE_ID)) {
                String id = PluginTypeHelper.getInstance().getSTypeByDecodeid(
                        jsonObject.getString(NetKeyConstants.NET_KEY_DECODE_ID));
                if (PluginConstants.NAME_VIBRATION_SENSOR.equals(id)
                        || PluginConstants.NAME_DOOR_WINDOW_SENSOR.equals(id)) {
                    category = PanelConstant.Category.DOOR_SENSOR;
                }
            } else {
                String id = PluginTypeHelper.getInstance().getSTypeByID(
                        jsonObject.getString(NetKeyConstants.NET_KEY_PLUGIN_ID));
                if (PluginConstants.NAME_VIBRATION_SENSOR.equals(id)
                        || PluginConstants.NAME_DOOR_WINDOW_SENSOR.equals(id)) {
                    category = PanelConstant.Category.DOOR_SENSOR;
                }
            }
            return category;
        } catch (Exception e) {
            DDLog.e(TAG, "getOldPluginType-ERROR.");
            e.printStackTrace();
        }
        return -1;
    }

    /**
     * 获取旧配件的配件类型
     *
     * @param stype
     * @return
     */
    public static int getAskPluginType(final String  stype) {
        try {
            int category = -1;
            if (PluginConstants.TYPE_4E.equals(stype)) {
                category = PanelConstant.Category.SIGNAL_REPEATER_PLUG;
            } else if (PluginConstants.TYPE_3E.equals(stype)) {
                // 新插座
                category = PanelConstant.Category.SMART_PLUGS;
            } else if (PluginConstants.TYPE_11.equals(stype)
                    || PluginConstants.TYPE_16.equals(stype)
                    || PluginConstants.TYPE_19.equals(stype)
                    || PluginConstants.TYPE_1C.equals(stype)
                    || PluginConstants.TYPE_25.equals(stype)
                    || PluginConstants.TYPE_2C.equals(stype)
                    || PluginConstants.TYPE_38.equals(stype)
                    || PluginConstants.TYPE_3D.equals(stype)) {
                // 新门磁
                category = PanelConstant.Category.DOOR_SENSOR;
            }
            return category;
        } catch (Exception e) {
            DDLog.e(TAG, "getOldPluginType-ERROR.");
            e.printStackTrace();
        }

        return -1;
    }
}
