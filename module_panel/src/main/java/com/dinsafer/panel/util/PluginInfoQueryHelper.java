package com.dinsafer.panel.util;

import android.text.TextUtils;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dssupport.plugin.PluginConstants;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.bean.device.BasePluginDevice;
import com.dinsafer.panel.bean.device.PanelDevice;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelResultBuilder;
import com.dinsafer.panel.operate.bean.CommonAccessoriesBean;
import com.dinsafer.panel.operate.bean.param.AccessoriesInfoParams;
import com.dinsafer.panel.operate.callback.PanelCallback;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 查询缓存配件额外信息的工具类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/12/1 00:30
 */
public class PluginInfoQueryHelper {
    private final PanelDevice panelDevice;

    public PluginInfoQueryHelper(@NotNull PanelDevice panelDevice) {
        this.panelDevice = panelDevice;
    }

    public void getPluginInfo(List<String> pluginIds) {
        if (null == pluginIds || pluginIds.size() == 0) {
            panelDevice.dispatchResult(PanelCmd.LOAD_PLUGIN_INFO,
                    PanelResultBuilder.success(PanelCmd.LOAD_PLUGIN_INFO, true, "").createResult());
            return;
        }

        final List<BasePluginDevice<?>> needLoadDevices = new ArrayList<>();
        final List<AccessoriesInfoParams> accessories = new ArrayList<>();
        final List<String> needStatusStypeList = new ArrayList<>();
        for (String pluginId : pluginIds) {
            if (TextUtils.isEmpty(pluginId)) {
                continue;
            }
            Device pluginDevice = panelDevice.getDeviceById(pluginId);
            if (!(pluginDevice instanceof BasePluginDevice)) {
                continue;
            }
            BasePluginDevice<?> basePluginDevice = (BasePluginDevice<?>) pluginDevice;
            String sendId = DeviceHelper.getString(basePluginDevice, PanelDataKey.SEND_ID, null);
            String sType = DeviceHelper.getString(basePluginDevice, PanelDataKey.S_TYPE, null);

            if (!TextUtils.isEmpty(sendId) && !TextUtils.isEmpty(sType)) {
                needLoadDevices.add(basePluginDevice);
                accessories.add(new AccessoriesInfoParams(sType, sendId));

                if (!needStatusStypeList.contains(sType)) {
                    needStatusStypeList.add(sType);
                }
            }
        }

        if (0 == accessories.size()) {
            panelDevice.dispatchResult(PanelCmd.LOAD_PLUGIN_INFO,
                    PanelResultBuilder.success(PanelCmd.LOAD_PLUGIN_INFO, true, "").createResult());
            return;
        }

        PanelManager.getInstance().getNetworkRequestManager()
                .searchAccessories(PanelManager.getInstance().getCurrentHomeId(),
                        panelDevice.getId(), accessories, 1, false,
                        new PanelCallback.NetworkResult<List<CommonAccessoriesBean>>() {
                            @Override
                            public void onSuccess(List<CommonAccessoriesBean> result) {
                                final ArrayList<Map<String, Object>> infoList = new ArrayList<>();
                                if (null == result || result.size() == 0) {
                                    for (BasePluginDevice<?> needLoadDevice : needLoadDevices) {
                                        needLoadDevice.setFlagDeleted(true);
                                        needLoadDevice.setLoaded(true);
                                    }
                                } else {
                                    for (BasePluginDevice<?> needLoadDevice : needLoadDevices) {
                                        boolean found = false;
                                        final String pluginId = needLoadDevice.getId();
                                        for (CommonAccessoriesBean bean : result) {
                                            String beanId = bean.getId();
                                            String stype = bean.getStype();
                                            String sendId = bean.getSendid();
                                            boolean isOn = PanelConstant.STATUS_OPENED_ASK_PLUG == bean.getEnable();
                                            int category = PanelPluginUtil.getAskPluginType(stype);

                                            // 缓存仅支持部分门磁和部分插座
                                            if (PanelConstant.Category.SMART_PLUGS != category
                                                    && PanelConstant.Category.SIGNAL_REPEATER_PLUG != category
                                                    && PanelConstant.Category.DOOR_SENSOR != category) {
                                                continue;
                                            }

                                            if (pluginId.equals(beanId)) {
                                                final Map<String, Object> pluginParams = new HashMap<>();
                                                if (PanelConstant.Category.SMART_PLUGS == category
                                                        || PanelConstant.Category.SIGNAL_REPEATER_PLUG == category) {
                                                    pluginParams.put(PanelDataKey.SmartPlug.IS_ASK_SMART_PLUG, true);
                                                    pluginParams.put(PanelDataKey.PLUGIN_SWITCH_STATE, isOn
                                                            ? PanelConstant.PluginSwitchState.OPENED
                                                            : PanelConstant.PluginSwitchState.CLOSED);
                                                    pluginParams.put(PanelDataKey.HAVE_LOADING_STATE, true);
                                                } else {
                                                    pluginParams.put(PanelDataKey.HAVE_LOADING_STATE, false);
                                                }

                                                pluginParams.put(PanelDataKey.CATEGORY, PluginConstants.CATEGORY_10);
                                                pluginParams.put(PanelDataKey.NAME, bean.getName());
                                                pluginParams.put(PanelDataKey.SEND_ID, sendId);
                                                pluginParams.put(PanelDataKey.S_TYPE, stype);

                                                pluginParams.put(PanelDataKey.LOADING_STATE, PanelConstant.PluginLoadingState.SUCCESS);
                                                pluginParams.put(PanelDataKey.HAVE_ONLINE_STATE, true);
                                                pluginParams.put(PanelDataKey.IS_ONLINE, bean.isOnline());

                                                final Map<String, Object> info = new HashMap<>();
                                                info.put(PanelDataKey.S_TYPE, stype);
                                                info.put(PanelDataKey.IS_ONLINE, bean.isOnline());
                                                info.put(PanelDataKey.SEND_ID, sendId);
                                                info.put(PanelDataKey.NAME, bean.getName());
                                                info.put(PanelDataKey.ID, bean.getId());
                                                infoList.add(info);
                                                needLoadDevice.updateInfo(pluginParams);
                                                found = true;
                                                break;
                                            }
                                        }
                                        needLoadDevice.setLoaded(true);
                                        needLoadDevice.setFlagDeleted(!found);
                                    }
                                }

                                Map<String, Object> args = new HashMap<>();
                                args.put(PanelDataKey.CMD, PanelCmd.LOAD_PLUGIN_INFO);
                                args.put(PanelDataKey.PLUGINS, new ArrayList<>(pluginIds));
                                args.put(PanelDataKey.PLUGIN_INFO, infoList);
                                panelDevice.dispatchResult(PanelCmd.LOAD_PLUGIN_INFO,
                                        PanelResultBuilder.success(PanelCmd.LOAD_PLUGIN_INFO,
                                                true, args).createResult());

                                // 查询配件电量等状态
                                PluginStateRoundRobinUtil pluginStateQueryUtil = panelDevice.getPluginStateQueryUtil();
                                if (null != pluginStateQueryUtil) {
                                    pluginStateQueryUtil.queryPluginStatusOnce(needStatusStypeList);
                                }
                            }

                            @Override
                            public void onError(int errorCode, String errorMsg) {
                                panelDevice.dispatchResult(PanelCmd.LOAD_PLUGIN_INFO,
                                        PanelResultBuilder.error(PanelCmd.LOAD_PLUGIN_INFO, true,
                                                "code: " + errorCode + ", msg: " + errorMsg).createResult());
                            }
                        });
    }
}
