package com.dinsafer.panel.util;

import android.text.TextUtils;

import com.dinsafer.dssupport.msctlib.db.KV;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.operate.bean.HomePluginQuantityEntry;
import com.dinsafer.panel.operate.bean.PanelInfo;
import com.google.gson.Gson;

/**
 * 数据缓存工具类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/21 6:15 PM
 */
public class PanelDBHelper {
    private static final String TAG = PanelDBHelper.class.getSimpleName();

    /**
     * 缓存主机信息
     */
    public static synchronized void savePanelInfo(PanelInfo panelInfo) {
        if (null == panelInfo
                || TextUtils.isEmpty(panelInfo.getDeviceId())) {
            DDLog.e(TAG, "device info id is empty, don't save.");
            return;
        }
        KV.putObj(PanelDBKey.getPanelInfoCacheKey(panelInfo.getDeviceId()), panelInfo);
    }

    /**
     * 移除本地缓存的主机信息
     *
     * @param deviceId 需要移除的主机ID
     */
    public static void removePanelInfo(String deviceId) {
        DDLog.d(TAG, "removeDeviceInfo, deviceId: " + deviceId);
        if (!TextUtils.isEmpty(deviceId)) {
            return;
        }

        KV.remove(PanelDBKey.getPanelInfoCacheKey(deviceId));
    }

    /**
     * 从本地缓存中读取主机信息
     *
     * @param deviceId 主机id
     * @return 本地缓存中保存的主机信息
     */
    public static PanelInfo resolveDeviceInfoFromCache(String deviceId) {
        DDLog.d(TAG, "resolveDeviceInfoFromCache");
        try {
            String event = KV.getString(PanelDBKey.getPanelInfoCacheKey(deviceId), null);
            if (!TextUtils.isEmpty(event)) {
                PanelInfo panel = new Gson().fromJson(event, PanelInfo.class);
                if (null != panel && deviceId.equals(panel.getDeviceId())) {
                    DDLog.i(TAG, "成功读取主机状态的缓存");
                    return panel;
                }
            }
        } catch (Exception ex) {
            DDLog.e(TAG, "Parse device info from cache ERROR.");
            ex.printStackTrace();
            DDLog.e(TAG, ex.getLocalizedMessage());
        }
        return null;
    }

    /**
     * 保存首页配件数据到本地
     */
    public static void saveHomePluginInfo(String panelId, String homePluginEntry) {
        if (TextUtils.isEmpty(panelId)
                || TextUtils.isEmpty(homePluginEntry)) {
            DDLog.e(TAG, "plugin info id is empty, don't save.");
            return;
        }

        KV.putString(PanelDBKey.getHomePluginInfoCacheKey(panelId), homePluginEntry);
    }

    /**
     * 移除本地缓存中的首页配件信息
     *
     * @param deviceId 需要移除的DeviceId
     */
    public static void removeHomePluginInfo(String deviceId) {
        DDLog.d(TAG, "removeHomePluginInfo, deviceId: " + deviceId);
        if (!TextUtils.isEmpty(deviceId)) {
            return;
        }

        KV.remove(PanelDBKey.getHomePluginInfoCacheKey(deviceId));
    }

    /**
     * 从本地恢复首页Panel的配件数据
     */
    public static String resolveHomePluginInfoFromCache(String deviceId) {
        DDLog.d(TAG, "resolveHomePluginInfoFromCache");
        try {
            return KV.getString(PanelDBKey.getHomePluginInfoCacheKey(deviceId), null);
        } catch (Exception ex) {
            DDLog.e(TAG, "Parse plugin info from cache ERROR.");
            ex.printStackTrace();
            DDLog.e(TAG, ex.getLocalizedMessage());
        }
        return null;
    }

    /**
     * 保存主机配件数量到本地
     */
    public static void savePanelPluginQuantityInfo(String panelId, HomePluginQuantityEntry pluginQuantityEntry) {
        if (null == pluginQuantityEntry
                || null == pluginQuantityEntry.getResult()) {
            DDLog.e(TAG, "plugin quantity id is empty, don't save.");
            return;
        }

        KV.putObj(PanelDBKey.getPanelPluginQuantityCacheKey(panelId), pluginQuantityEntry);
    }

    /**
     * 移除本地缓存中的主机配件数量信息
     *
     * @param deviceId 需要移除的DeviceId
     */
    public static void removePanelPluginQuantityInfo(String deviceId) {
        DDLog.d(TAG, "removePanelPluginQuantityInfo, deviceId: " + deviceId);
        if (!TextUtils.isEmpty(deviceId)) {
            return;
        }

        KV.remove(PanelDBKey.getPanelPluginQuantityCacheKey(deviceId));
    }

    /**
     * 从本地恢复首页Panel的配件数据
     */
    public static HomePluginQuantityEntry resolvePanelPluginQuantityInfo(String panelId) {
        DDLog.d(TAG, "resolvePanelPluginQuantityInfo");
        try {
            String event = KV.getString(PanelDBKey.getPanelPluginQuantityCacheKey(panelId), null);
            if (!TextUtils.isEmpty(event)) {
                HomePluginQuantityEntry pluginEntry = new Gson().fromJson(event, HomePluginQuantityEntry.class);
                if (null != pluginEntry && null != pluginEntry.getResult()) {
                    DDLog.d(TAG, "成功读取主机配件数量缓存");
                    return pluginEntry;
                }
            }
        } catch (Exception ex) {
            DDLog.e(TAG, "Parse plugin quantity from cache ERROR.");
            ex.printStackTrace();
            DDLog.e(TAG, ex.getLocalizedMessage());
        }
        return null;
    }

    /**
     * 清除主机相关的缓存信息
     *
     * @param panelId 主机ID
     */
    public static void removePanelCache(String panelId) {
        DDLog.d(TAG, "removePanelCache, panelId: " + panelId);
        if (TextUtils.isEmpty(panelId)) {
            return;
        }
        KV.remove(PanelDBKey.getPanelInfoCacheKey(panelId));
        KV.remove(PanelDBKey.getHomePluginInfoCacheKey(panelId));
        KV.remove(PanelDBKey.getPanelPluginQuantityCacheKey(panelId));
    }

}
