package com.dinsafer.panel.util;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/18 3:58 PM
 */
public class DDSystemUtil {
    /**
     * @return if versionServer > versionLocal, return 1, if equal, return 0, else return
     * -1
     */
    public static int VersionComparison(String versionServer, String versionLocal) {
        if (versionServer == null || versionServer.length() == 0 || versionLocal == null || versionLocal.length() == 0)
            throw new IllegalArgumentException("Invalid parameter!");

        int index1 = 0;
        int index2 = 0;
        while (index1 < versionServer.length() && index2 < versionLocal.length()) {
            int[] number1 = getValue(versionServer, index1);
            int[] number2 = getValue(versionLocal, index2);

            if (number1[0] < number2[0]) {
                return -1;
            } else if (number1[0] > number2[0]) {
                return 1;
            } else {

                index1 = number1[1] + 1;
                index2 = number2[1] + 1;
            }
        }
        if (index1 >= versionServer.length() && index2 >= versionLocal.length()) {
            return 0;
        }
        if (index1 < versionServer.length()) {
            return 1;
        } else {
            return -1;
        }
    }

    /**
     * @param version
     * @param index   the starting point
     * @return the number between two dots, and the index of the dot
     */
    public static int[] getValue(String version, int index) {
        int[] value_index = new int[2];
        StringBuilder sb = new StringBuilder();
        while (index < version.length() && version.charAt(index) != '.') {
            sb.append(version.charAt(index));
            index++;
        }
        value_index[0] = Integer.parseInt(sb.toString());
        value_index[1] = index;

        return value_index;
    }


}
