package com.dinsafer.panel.util;

import com.dinsafer.dincore.crypt.PanelCrypt;
import com.dinsafer.dincore.utils.RandomStringUtils;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.dssupport.utils.HexUtil;

import org.iq80.snappy.Snappy;

import java.util.ArrayList;

/**
 * 添加主机-蓝牙通讯加解密工具
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/11 3:45 PM
 */
public class PanelSecretUtil {
    private static final String TAG = PanelSecretUtil.class.getSimpleName();
    private final static PanelCrypt crypt = new PanelCrypt();

    public static String getSC(String aInput) {
        return HexUtil.bytesToHexString(crypt.encode(aInput.getBytes())).toUpperCase();
    }

    public static String getReverSCWithOutSnappy(String input) {
        return new String(crypt.decode(input.toUpperCase().getBytes()));
    }

    public static byte[] snappy(byte[] data) {
        if (data == null)
            return null;
        return Snappy.uncompress(data, 0, data.length);
    }

    public static String getReverSC(String input) {
        String result = new String(snappy(crypt.decode(input.toUpperCase().getBytes())));
        DDLog.d(TAG, "-------------------------解压缩后--------------------------");
        DDLog.d(TAG, "| " + result);
        return result;
    }


    /**
     * 这个只能用于配件id加解密
     *
     * @param m
     * @return
     */
    public static String hexStrToStr64(String m) {
        final String[] Pattern = new String[]{"G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "$", "@"};
        String binStr = "";
        String conStr = "";
        try {
            for (int i = 0; i < m.length(); i++) {
                conStr = Integer.toBinaryString(Integer.parseInt(m.substring(i, i + 1), 16));
                binStr += RandomStringUtils.addZeroForNum(conStr, 4);
            }
            String[] spStr = str_split(binStr, 6);
            String hex64 = "";
            for (String bStr : spStr) {
                hex64 += Pattern[Integer.valueOf(bStr, 2)];
            }
            return hex64;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String[] str_split(String binStr, int len) {
        int strlen = binStr.length();
        if (strlen <= len) {
            return new String[]{binStr};
        }
        int headlen = strlen % len;
        ArrayList<String> buf = new ArrayList<String>();
        String cutStr = "";
        if (headlen > 0) {
            cutStr = binStr.substring(0, headlen);
            buf.add(cutStr);
            binStr = binStr.substring(headlen);
        }
        strlen = strlen - headlen;
        while (strlen > 0) {
            cutStr = binStr.substring(0, len);
            buf.add(cutStr);
            strlen = strlen - len;
            binStr = binStr.substring(len);
        }
        String[] str = new String[buf.size()];
        str = (String[]) buf.toArray(str);
        return str;
    }

    public static String intToByte(int i) {
        String s = Integer.toBinaryString(i);
        if (s.length() < 2) {
            s = "0" + s;
        }
        return s;
    }

}
