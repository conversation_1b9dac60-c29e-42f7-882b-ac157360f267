package com.dinsafer.panel.util;

import android.text.TextUtils;

import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.utils.RandomStringUtils;
import com.dinsafer.dssupport.plugin.PluginConstants;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.http.PanelApi;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import rx.Observable;
import rx.Subscriber;
import rx.Subscription;
import rx.schedulers.Schedulers;

/**
 * 配件状态轮询工具类
 *
 * <AUTHOR>
 * @date 2021/6/3 22:09
 */
public class PluginStateRoundRobinUtil {
    private static final String TAG = PluginStateRoundRobinUtil.class.getSimpleName();

    private static final int ROUND_ROBIN_TIME_SECOND = 15; // 轮询时间间隔，单位：S

    private Subscription mRoundRobinSubscribe;

    private final Set<String> mStypeSet = new HashSet<>();
    private final ArrayList<String> mStypeTargets = new ArrayList<>(); // stype在这个列表内的，才需要轮询

    private String mDeviceToken, mUserId;
    private ArrayList<String> mStypeList;

    public PluginStateRoundRobinUtil(String panelToken, String userId) {
        this.mDeviceToken = panelToken;
        this.mUserId = userId;
        initFilterType();
    }

    private void initFilterType() {
        DDLog.i(TAG, "initFilterType");
        mStypeTargets.clear();
        mStypeTargets.add(PluginConstants.TYPE_2C);
        mStypeTargets.add(PluginConstants.TYPE_2F);
        mStypeTargets.add(PluginConstants.TYPE_3D);
        mStypeTargets.add(PluginConstants.TYPE_3E);
        mStypeTargets.add(PluginConstants.TYPE_4E);
        mStypeTargets.add(PluginConstants.TYPE_34);
        mStypeTargets.add(PluginConstants.TYPE_35);
        mStypeTargets.add(PluginConstants.TYPE_36);
        mStypeTargets.add(PluginConstants.TYPE_38);
        mStypeTargets.add(PluginConstants.TYPE_39);
        mStypeTargets.add(PluginConstants.TYPE_3A);
        mStypeTargets.add(PluginConstants.TYPE_3B);
        mStypeTargets.add(PluginConstants.TYPE_3C);
        mStypeTargets.add(PluginConstants.TYPE_3F);
        mStypeTargets.add(PluginConstants.TYPE_4A);
    }

    /**
     * 开启轮询
     */
    public void startQueryPluginStatusRoundRobin() {
        DDLog.i(TAG, "startQueryPluginStatusRoundRobin");
        if (null != mRoundRobinSubscribe
                && !mRoundRobinSubscribe.isUnsubscribed()) {
            DDLog.i(TAG, "正在轮询配件状态中，不重复开启轮询");
            return;
        }

        if (0 >= mStypeSet.size()) {
            DDLog.e(TAG, "需要轮询配件状态的stype列表为空，不开启轮询");
            return;
        }

        // 初始化定时轮询请求参数
        mStypeList = new ArrayList<>(mStypeSet);

        // 启动轮询
        mRoundRobinSubscribe = Observable.interval(ROUND_ROBIN_TIME_SECOND, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {
                        DDLog.d(TAG, "onCompleted");
                    }

                    @Override
                    public void onError(Throwable e) {
                        DDLog.e(TAG, "onError: ");
                        e.printStackTrace();
                    }

                    @Override
                    public void onNext(Object o) {
                        DDLog.d(TAG, "onNext");
                        onQueryPluginStatusRoundRobin();
                    }
                });
    }

    /**
     * 停止定时轮询
     */
    public void stopQueryPluginStatusRoundRobin() {
        DDLog.i(TAG, "stopQueryPluginStatusRoundRobin");
        if (null != mRoundRobinSubscribe
                && !mRoundRobinSubscribe.isUnsubscribed()) {
            mRoundRobinSubscribe.unsubscribe();
            mRoundRobinSubscribe = null;
        }
    }

    /**
     * 一轮定时轮询时间到，发送获取配件状态请求
     */
    private void onQueryPluginStatusRoundRobin() {
        DDLog.i(TAG, "onQueryPluginStatusRoundRobin");
        Call<StringResponseEntry> pluginStatus = PanelApi.getPanelApi()
                .getPluginStatus(RandomStringUtils.getMessageId(),
                        mDeviceToken, mUserId, mStypeList, 3);
        try {
            pluginStatus.execute();
            DDLog.i(TAG, "发送查询配件状态信息CMD请求，Thread: " + Thread.currentThread().getName());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 清空需要轮询的stype
     */
    public void cleanStype() {
        mStypeSet.clear();
    }

    /**
     * 添加需要轮询的Stype类型
     * 只有指定的stype才需要轮询配件状态
     *
     * @param stype 配件stype
     */
    public void addStypeWithFilter(String stype) {
        if (TextUtils.isEmpty(stype)) {
            return;
        }

        if (mStypeTargets.contains(stype)) {
            mStypeSet.add(stype);
        }
    }

    public void queryPluginStatusOnce(List<String> sTypeList) {
        ArrayList<String> queryList = new ArrayList<>();
        if (null != sTypeList && sTypeList.size() > 0) {
            for (String stype : sTypeList) {
                if (mStypeTargets.contains(stype) && !queryList.contains(stype)) {
                    queryList.add(stype);
                }
            }
        }
        if (queryList.size() == 0) {
            return;
        }
        DDLog.i(TAG, "queryPluginStatusOnce");
        Call<StringResponseEntry> pluginStatus = PanelApi.getPanelApi()
                .getPluginStatus(RandomStringUtils.getMessageId(),
                        mDeviceToken, mUserId, queryList, 3);
        pluginStatus.enqueue(new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                DDLog.i(TAG, "发送查询配件状态信息CMD请求成功 ");
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                DDLog.e(TAG, "发送查询配件状态信息CMD请求失败 ");
                t.printStackTrace();
            }
        });
    }
}
