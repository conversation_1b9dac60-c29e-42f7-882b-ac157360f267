package com.dinsafer.panel.util;

import android.text.TextUtils;

import com.dinsafer.dincore.common.NetKeyConstants;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dssupport.plugin.PluginConstants;
import com.dinsafer.dssupport.plugin.PluginTypeHelper;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.bean.CustomizeHomeArmResult;
import com.dinsafer.panel.bean.DoorSensorResult;
import com.dinsafer.panel.bean.SecurityPluginResult;
import com.dinsafer.panel.bean.SimplePluginResult;
import com.dinsafer.panel.bean.device.DoorBellDevice;
import com.dinsafer.panel.bean.device.DoorSensorDevice;
import com.dinsafer.panel.bean.device.KeypadDevice;
import com.dinsafer.panel.bean.device.OtherDevice;
import com.dinsafer.panel.bean.device.RelayDevice;
import com.dinsafer.panel.bean.device.RemoteControlDevice;
import com.dinsafer.panel.bean.device.SecurityDevice;
import com.dinsafer.panel.bean.device.SmartButtonDevice;
import com.dinsafer.panel.bean.device.SmartPlugDevice;
import com.dinsafer.panel.bean.device.WirelessSirenDevice;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.operate.bean.CategoryPlugsEntry;
import com.dinsafer.panel.operate.bean.CmsProtocolEntry;
import com.dinsafer.panel.operate.bean.CmsProtocolModel;
import com.dinsafer.panel.operate.bean.DoorBell;
import com.dinsafer.panel.operate.bean.EntryDelayModel;
import com.dinsafer.panel.operate.bean.EventListBean;
import com.dinsafer.panel.operate.bean.EventListEntry;
import com.dinsafer.panel.operate.bean.HomeArmStatueEntry;
import com.dinsafer.panel.operate.bean.NewAskPlugInfo;
import com.google.gson.Gson;

import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import okhttp3.ResponseBody;

/**
 * 网络数据转换工具类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/14 3:59 PM
 */
public class PanelDataTransformer {
    private static final String TAG = PanelDataTransformer.class.getSimpleName();

    // 由于目前键盘和遥控请求的是同一个接口，需要根据默认名字过滤配件列表数据
    private static final String KEYPAD_DEFAULT_NAME = "Wireless Keypad";
    private static final String RC_DEFAULT_NAME = "Remote Controller";
    private static final String RFID_DEFAULT_NAME = "RFID Tag";

    /**
     * 解析JsonObject为SmartButtonDevice
     *
     * @param panelId    主机ID
     * @param pluginType 配件类型
     * @param jsonObject 配件数据
     */
    private static SmartButtonDevice parseSmartButton(@NotNull String panelId, @NotNull String pluginType, @NotNull JSONObject jsonObject) {
        try {
            String pluginId = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_ID);
            String pluginName = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_NAME);

            SmartButtonDevice smartButtonDevice = new SmartButtonDevice(pluginId, panelId, pluginType);
            Map<String, Object> pluginParam = new HashMap();
//            if (TextUtils.isEmpty(pluginName)) {
//                String name = PluginTypeHelper.getInstance().getASKNameByBSType(pluginType);
//                pluginParam.put(PanelDataKey.NAME, name + "_" + pluginId);
//            } else {
            pluginParam.put(PanelDataKey.NAME, pluginName);
//            }
            pluginParam.put(PanelDataKey.ID, pluginId);
            pluginParam.put(PanelDataKey.HAVE_SIGNAL_LEVEL, PluginTypeHelper.getInstance().isPluginHadRssi(pluginType));
            pluginParam.put(PanelDataKey.CAN_TAMPER, PluginTypeHelper.getInstance().isPluginHadTamper(pluginType));
            pluginParam.put(PanelDataKey.CAN_CHARGING, PluginTypeHelper.getInstance().isPluginCanCharging(pluginType));

            pluginParam.put(PanelDataKey.HAVE_BATTERY_LEVEL, PluginTypeHelper.getInstance().isPluginHasBatteryLevel(pluginType));
            pluginParam.put(PanelDataKey.HAVE_WEB_SOCKET_LOADING, PluginTypeHelper.getInstance().isPluginHadWebsocketStatus(pluginType));
            pluginParam.put(PanelDataKey.ASK_DATA, jsonObject);

            smartButtonDevice.setInfo(pluginParam);
            return smartButtonDevice;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseSmartButton");
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 解析JsonObject为SmartButtonDevice
     *
     * @param panelId      主机ID
     * @param responseBody 服务器返回的数据
     */
    public static ArrayList<SmartButtonDevice> parseSmartButtonList(@NotNull String panelId, @NotNull ResponseBody responseBody) {
        ArrayList<SmartButtonDevice> smartButtonList = new ArrayList<>();
        try {
            JSONObject jsonObject = new JSONObject(responseBody.string());
            String result = PanelSecretUtil.getReverSC(String.valueOf(jsonObject.get(NetKeyConstants.NET_KEY_RESULT)));
            JSONObject resultJson = new JSONObject(result);
            JSONObject object = DDJSONUtil.getJSONObject(resultJson, NetKeyConstants.NET_KEY_DATAS);
            if (null == object) {
                return smartButtonList;
            }

            JSONArray plugins;
            SmartButtonDevice smartButtonDevice;
            for (String pluginType : PanelConstant.Plugin.ASK_SMART_BUTTON_IDS) {
                if (object.has(pluginType)) {
                    plugins = (JSONArray) object.get(pluginType);
                    if (plugins.length() > 0) {
                        for (int i = 0; i < plugins.length(); i++) {
                            smartButtonDevice = PanelDataTransformer
                                    .parseSmartButton(panelId, pluginType, (JSONObject) plugins.get(i));
                            if (null != smartButtonDevice) {
                                smartButtonList.add(smartButtonDevice);
                            }
                        }
                    }
                }
            }
            return smartButtonList;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseSmartButtonList");
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 解析JsonObject为旧SmartPlugDevice
     *
     * @param panelId    主机ID
     * @param pluginData 旧SmartPlug数据
     */
    private static SmartPlugDevice parseOldSmartPlug(@NotNull String panelId, int category,
                                                     @NotNull CategoryPlugsEntry.ResultBean.DatasBean pluginData) {
        try {
            String pluginId = pluginData.getId();
            String decodeId = pluginData.getDecodeid();
            String pluginName = pluginData.getName();
            String subcategory = pluginData.getSubcategory();

            SmartPlugDevice smartPlugDevice = new SmartPlugDevice(pluginId, category, subcategory, panelId);
            Map<String, Object> pluginParam = new HashMap<>();

//            if (TextUtils.isEmpty(pluginName)) {
//                String name;
//                if (!TextUtils.isEmpty(decodeId)) {
//                    name = PluginTypeHelper.getInstance().getSTypeByDecodeid(decodeId);
//                } else if (pluginId.startsWith("!")) {
//                    name = PluginTypeHelper.getInstance().getASKNameByBSType(subcategory);
//                } else {
//                    name = PluginTypeHelper.getInstance().getSTypeByID(pluginId);
//                }
//                pluginName = name + "_" + pluginId;
//            }
            pluginParam.put(PanelDataKey.NAME, pluginName);
            pluginParam.put(PanelDataKey.DECODE_ID, decodeId);
            pluginParam.put(PanelDataKey.SmartPlug.IS_ASK_SMART_PLUG, false);
            pluginParam.put(PanelDataKey.HAVE_ONLINE_STATE, false);
            pluginParam.put(PanelDataKey.HAVE_LOADING_STATE, true);
            pluginParam.put(PanelDataKey.LOADING_STATE, PanelConstant.PluginLoadingState.SUCCESS);
            pluginParam.put(PanelDataKey.PLUGIN_SWITCH_STATE, pluginData.isEnable()
                    ? PanelConstant.PluginSwitchState.OPENED
                    : PanelConstant.PluginSwitchState.CLOSED);

            smartPlugDevice.setInfo(pluginParam);
            return smartPlugDevice;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseOldSmartPlug");
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 解析JsonObject为新型ASK SmartPlugDevice
     *
     * @param panelId    主机ID
     * @param jsonObject 配件数据
     */
    private static SmartPlugDevice parseAskSmartPlug(@NotNull String panelId, @NotNull JSONObject jsonObject) {
        try {
            String pluginId = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_ID);
            String pluginName = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_NAME);
            boolean isOn = PanelConstant.STATUS_OPENED_ASK_PLUG
                    == DDJSONUtil.getInt(jsonObject,
                    NetKeyConstants.NET_KEY_PLUGIN__ITEM__SMART__PLUG__ENABLE);

            SmartPlugDevice smartPlugDevice = new SmartPlugDevice(pluginId, panelId);
            Map<String, Object> pluginParam = new HashMap<>();

//            if (TextUtils.isEmpty(pluginName)) {
//                String sType;
//                if (pluginId.startsWith("!")) {
//                    sType = PluginTypeHelper.getInstance().getASKNameByBSType(
//                            DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_S_TYPE));
//                } else {
//                    sType = PluginTypeHelper.getInstance().getSTypeByID(pluginId);
//                }
//                pluginName = sType + "_" + pluginId;
//            }
            pluginParam.put(PanelDataKey.NAME, pluginName);
            pluginParam.put(PanelDataKey.SEND_ID, DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_SEND_ID));
            pluginParam.put(PanelDataKey.S_TYPE, DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_S_TYPE));
            pluginParam.put(PanelDataKey.ASK_DATA, jsonObject);
            pluginParam.put(PanelDataKey.SmartPlug.IS_ASK_SMART_PLUG, true);
            pluginParam.put(PanelDataKey.HAVE_ONLINE_STATE, true);
            pluginParam.put(PanelDataKey.IS_ONLINE, DDJSONUtil.getBoolean(jsonObject, NetKeyConstants.NET_KEY_KEEP_LIVE));
            pluginParam.put(PanelDataKey.HAVE_LOADING_STATE, true);
            pluginParam.put(PanelDataKey.LOADING_STATE, PanelConstant.PluginLoadingState.SUCCESS);
            pluginParam.put(PanelDataKey.PLUGIN_SWITCH_STATE, isOn
                    ? PanelConstant.PluginSwitchState.OPENED
                    : PanelConstant.PluginSwitchState.CLOSED);

            smartPlugDevice.setInfo(pluginParam);
            return smartPlugDevice;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseAskSmartPlug");
            e.printStackTrace();
        }
        return null;
    }

    public static ArrayList<SmartPlugDevice> parseSignalRepeaterPlugList(@NotNull String panelId, @NotNull ResponseBody responseBody) {
        try {
            ArrayList<SmartPlugDevice> signalRepeaterPlugDeviceList = new ArrayList<>();
            SmartPlugDevice signalRepeaterPlugDevice;
            JSONObject jsonObject = new JSONObject(responseBody.string());
            String result = PanelSecretUtil.getReverSC(String.valueOf(jsonObject.get(NetKeyConstants.NET_KEY_RESULT)));
            JSONObject resultJson = new JSONObject(result);
            JSONObject datas = DDJSONUtil.getJSONObject(resultJson, NetKeyConstants.NET_KEY_NEW_ASK_DATAS);
            if (null != datas) {
                for (String pluginId : PanelConstant.Plugin.ASK_SIGNAL_REPEATER_PLUG_IDS) {
                    if (datas.has(pluginId)) {
                        JSONArray plugins = (JSONArray) datas.get(pluginId);
                        for (int i = 0; i < plugins.length(); i++) {
                            signalRepeaterPlugDevice = PanelDataTransformer.parseAskSmartPlug(panelId, (JSONObject) plugins.get(i));
                            if (null != signalRepeaterPlugDevice) {
                                signalRepeaterPlugDeviceList.add(signalRepeaterPlugDevice);
                            }
                        }
                    }
                }
            }
            return signalRepeaterPlugDeviceList;

        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseSignalRepeaterPlugList");
            e.printStackTrace();
            return null;
        }

    }

    /**
     * 解析JsonObject为新型SmartPlugDevice列表
     *
     * @param panelId      主机ID
     * @param responseBody 配件数据
     */
    public static ArrayList<SmartPlugDevice> parseSmartPlugList(@NotNull String panelId, @NotNull ResponseBody responseBody) {
        try {
            JSONObject jsonObject = new JSONObject(responseBody.string());
            String result = PanelSecretUtil.getReverSC(String.valueOf(jsonObject.get(NetKeyConstants.NET_KEY_RESULT)));
            JSONObject resultJson = new JSONObject(result);
            jsonObject.put(NetKeyConstants.NET_KEY_RESULT, resultJson);
            Gson gson = new Gson();
            CategoryPlugsEntry categoryPlugsEntry = gson.fromJson(jsonObject.toString(), CategoryPlugsEntry.class);
            ArrayList<SmartPlugDevice> smartPlugDeviceList = new ArrayList<>();
            SmartPlugDevice smartPlugDevice;
            // 旧插座
            if (categoryPlugsEntry.getResult() != null
                    && categoryPlugsEntry.getResult().getDatas() != null) {
                for (CategoryPlugsEntry.ResultBean.DatasBean plugin : categoryPlugsEntry.getResult().getDatas()) {
                    smartPlugDevice = PanelDataTransformer.parseOldSmartPlug(panelId,
                            categoryPlugsEntry.getResult().getPlugininfo().getCategory(), plugin);
                    if (null != smartPlugDevice) {
                        smartPlugDeviceList.add(smartPlugDevice);
                    }
                }
            }

            // 新ASK插座
            JSONObject newAskDatas = DDJSONUtil.getJSONObject(resultJson, NetKeyConstants.NET_KEY_NEW_ASK_DATAS);
            if (null != newAskDatas) {
                for (String pluginId : PanelConstant.Plugin.ASK_SMART_PLUG_IDS) {
                    if (newAskDatas.has(pluginId)) {
                        JSONArray plugins = (JSONArray) newAskDatas.get(pluginId);
                        for (int i = 0; i < plugins.length(); i++) {
                            smartPlugDevice = PanelDataTransformer.parseAskSmartPlug(panelId, (JSONObject) plugins.get(i));
                            if (null != smartPlugDevice) {
                                smartPlugDeviceList.add(smartPlugDevice);
                            }
                        }
                    }
                }
            }
            return smartPlugDeviceList;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseSmartPlugList");
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 解析JsonObject为RelayDevice
     *
     * @param panelId    主机ID
     * @param jsonObject 配件数据
     */
    private static RelayDevice parseRelay(@NotNull String panelId, @NotNull JSONObject jsonObject) {
        try {
            String pluginId = DDJSONUtil.getString(jsonObject, "id");
            String pluginName = DDJSONUtil.getString(jsonObject, "name");

            RelayDevice relayDevice = new RelayDevice(pluginId, panelId);
            Map<String, Object> pluginParams = new HashMap<>();

//            if (TextUtils.isEmpty(pluginName)) {
//                String sType;
//                if (pluginId.startsWith("!")) {
//                    sType = PluginTypeHelper.getInstance().getASKNameByBSType(
//                            DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_S_TYPE));
//                } else {
//                    sType = PluginTypeHelper.getInstance().getSTypeByID(pluginId);
//                }
//                pluginName = sType + "_" + pluginId;
//            }
            pluginParams.put(PanelDataKey.NAME, pluginName);
            pluginParams.put(PanelDataKey.S_TYPE, DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_S_TYPE));
            pluginParams.put(PanelDataKey.ASK_DATA, jsonObject);
            pluginParams.put(PanelDataKey.SEND_ID, DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_SEND_ID));

            relayDevice.setInfo(pluginParams);
            return relayDevice;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseRelay");
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 解析JsonObject为RelayDevice列表
     *
     * @param panelId      主机ID
     * @param responseBody 配件数据
     */
    public static ArrayList<RelayDevice> parseRelayList(@NotNull String panelId, @NotNull ResponseBody responseBody) {
        try {
            JSONObject jsonObject = new JSONObject(responseBody.string());
            String result = PanelSecretUtil.getReverSC(String.valueOf(jsonObject.get(NetKeyConstants.NET_KEY_RESULT)));
            JSONObject resultJson = new JSONObject(result);
            JSONArray resultArray = resultJson.getJSONArray(NetKeyConstants.NET_KEY_DATAS);
            ArrayList<RelayDevice> relayDeviceList = new ArrayList<>();
            if (resultArray.length() > 0) {
                RelayDevice relayDevice;
                for (int i = 0; i < resultArray.length(); i++) {
                    relayDevice = PanelDataTransformer.parseRelay(panelId, (JSONObject) resultArray.get(i));
                    if (null != relayDevice) {
                        relayDeviceList.add(relayDevice);
                    }
                }
            }
            return relayDeviceList;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseRelayList");
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 解析JsonObject为SecurityPluginResult
     *
     * @param panelId      主机ID
     * @param responseBody 配件数据
     */
    public static SecurityPluginResult parseSecurityPluginResult(@NotNull String panelId, @NotNull ResponseBody responseBody) {
        try {
            SecurityPluginResult securityPluginResult = new SecurityPluginResult();

            JSONObject jsonObject = new JSONObject(responseBody.string());
            String result = PanelSecretUtil.getReverSC(String.valueOf(jsonObject.get(NetKeyConstants.NET_KEY_RESULT)));
            JSONObject resultJson = new JSONObject(result);
            jsonObject.put(NetKeyConstants.NET_KEY_RESULT, resultJson);
            Gson gson = new Gson();
            CategoryPlugsEntry categoryPlugsEntry = gson.fromJson(jsonObject.toString(), CategoryPlugsEntry.class);

            if (null == categoryPlugsEntry || null == categoryPlugsEntry.getResult()) {
                securityPluginResult.getPluginMap().clear();
                return securityPluginResult;
            }
            categoryPlugsEntry.getResult().setOtherData(DDJSONUtil.getString(resultJson, NetKeyConstants.NET_KEY_NEW_ASK_DATAS));

            // 解析数据-有序
            // 1.旧配件
            ArrayList<SecurityDevice> pluginList;
            String pluginId, pluginName, subcategory, decodeId;
            SecurityDevice securityDevice;
            Map<String, Object> pluginParams;
            if (null != categoryPlugsEntry.getResult().getDatas() && 0 < categoryPlugsEntry.getResult().getDatas().size()) {
                for (CategoryPlugsEntry.ResultBean.DatasBean pluginData : categoryPlugsEntry.getResult().getDatas()) {
                    subcategory = pluginData.getSubcategory();
                    if (securityPluginResult.getPluginMap().containsKey(subcategory)) {
                        pluginList = securityPluginResult.getPluginMap().get(subcategory);
                    } else {
                        pluginList = new ArrayList<>();
                        securityPluginResult.getPluginMap().put(subcategory, pluginList);
                    }

                    pluginId = pluginData.getId();
                    decodeId = pluginData.getDecodeid();
                    pluginName = pluginData.getName();
                    securityDevice = new SecurityDevice(pluginId, 0, subcategory, panelId);
                    pluginParams = new HashMap<>();
//                    if (TextUtils.isEmpty(pluginName)) {
//                        String name;
//                        if (!TextUtils.isEmpty(decodeId)) {
//                            // 如果decodeid不为空，则一定要是旧二维码，但是！开头的二维码不一定是新的二维码，所以先判断decodeid，再判断！
//                            name = PluginTypeHelper.getInstance().getSTypeByDecodeid(decodeId);
//                        } else if (pluginId.startsWith("!")) {
//                            name = PluginTypeHelper.getInstance().getASKNameByBSType(pluginData.getSubcategory());
//                        } else {
//                            name = PluginTypeHelper.getInstance().getSTypeByID(pluginId);
//                        }
//                        pluginName = name + "_" + pluginId;
//                    }
                    pluginParams.put(PanelDataKey.NAME, pluginName);
                    pluginParams.put(PanelDataKey.DECODE_ID, decodeId);
                    pluginParams.put(PanelDataKey.SUBCATEGORY, subcategory);
                    securityDevice.setInfo(pluginParams);
                    pluginList.add(securityDevice);
                }
            }

            // 2.新的ASK类型配件
            if (!TextUtils.isEmpty(categoryPlugsEntry.getResult().getOtherData())) {
                JSONObject askData = new JSONObject(categoryPlugsEntry.getResult().getOtherData());
                // 新型红外
                for (String newPirId : PanelConstant.Plugin.NEW_PIR_SENSOR_IDS) {
                    if (askData.has(newPirId)) {
                        JSONArray plugins = (JSONArray) askData.get(newPirId);
                        if (plugins.length() > 0) {
                            securityPluginResult.getsTypeSet().add(newPirId);
                            for (int i = 0; i < plugins.length(); i++) {
                                if (newPirId.equals(PluginConstants.TYPE_4A)) {
                                    // stype为4A时，设为另外一组红外
                                    securityDevice = parseSecurityDevice(panelId, PluginConstants.TYPE_4A,
                                            newPirId, (JSONObject) plugins.get(i));
                                } else {
                                    securityDevice = parseSecurityDevice(panelId, PanelConstant.Plugin.OLD_PIR_SENSOR_09,
                                            newPirId, (JSONObject) plugins.get(i));
                                }
                                if (null != securityDevice) {
                                    securityPluginResult.getPluginMap().get(PanelConstant.Plugin.OLD_PIR_SENSOR_09).add(securityDevice);
                                }
                            }
                        }
                    }
                }
                // 新型普通门磁
                for (String newDoorSensorId : PanelConstant.Plugin.NEW_DOOR_WINDOW_SENSOR_IDS) {
                    if (askData.has(newDoorSensorId)) {
                        JSONArray plugins = (JSONArray) askData.get(newDoorSensorId);
                        if (plugins.length() > 0) {
                            securityPluginResult.getsTypeSet().add(newDoorSensorId);
                            for (int i = 0; i < plugins.length(); i++) {
                                securityDevice = parseSecurityDevice(panelId, PanelConstant.Plugin.OLD_DOOR_WINDOW_SENSOR_0B,
                                        newDoorSensorId, (JSONObject) plugins.get(i));
                                if (null != securityDevice) {
                                    securityPluginResult.getPluginMap().get(PanelConstant.Plugin.OLD_DOOR_WINDOW_SENSOR_0B).add(securityDevice);
                                }
                            }
                        }
                    }
                }
                // 新型水感
                for (String newLiquidId : PanelConstant.Plugin.NEW_LIQUID_SENSOR_IDS) {
                    if (askData.has(newLiquidId)) {
                        JSONArray plugins = (JSONArray) askData.get(newLiquidId);
                        if (plugins.length() > 0) {
                            securityPluginResult.getsTypeSet().add(newLiquidId);
                            for (int i = 0; i < plugins.length(); i++) {
                                securityDevice = parseSecurityDevice(panelId, PanelConstant.Plugin.OLD_LIQUID_SENSOR_0E,
                                        newLiquidId, (JSONObject) plugins.get(i));
                                if (null != securityDevice) {
                                    securityPluginResult.getPluginMap().get(PanelConstant.Plugin.OLD_LIQUID_SENSOR_0E).add(securityDevice);
                                }
                            }
                        }
                    }
                }
                // 新型震动门磁
                for (String newVibrationSensorId : PanelConstant.Plugin.NEW_VIBRATION_SENSOR_IDS) {
                    if (askData.has(newVibrationSensorId)) {
                        JSONArray plugins = (JSONArray) askData.get(newVibrationSensorId);
                        if (plugins.length() > 0) {
                            securityPluginResult.getsTypeSet().add(newVibrationSensorId);
                            for (int i = 0; i < plugins.length(); i++) {
                                securityDevice = parseSecurityDevice(panelId, PanelConstant.Plugin.OLD_VIBRATION_SENSOR_06,
                                        newVibrationSensorId, (JSONObject) plugins.get(i));
                                if (null != securityDevice) {
                                    securityPluginResult.getPluginMap().get(PanelConstant.Plugin.OLD_VIBRATION_SENSOR_06).add(securityDevice);
                                }
                            }
                        }
                    }
                }
                // 新型烟感
                for (String newSmokeSensorId : PanelConstant.Plugin.NEW_SMOKE_SENSOR_IDS) {
                    if (askData.has(newSmokeSensorId)) {
                        JSONArray plugins = (JSONArray) askData.get(newSmokeSensorId);
                        if (plugins.length() > 0) {
                            securityPluginResult.getsTypeSet().add(newSmokeSensorId);
                            for (int i = 0; i < plugins.length(); i++) {
                                securityDevice = parseSecurityDevice(panelId, PanelConstant.Plugin.OLD_SMOKE_SENSOR_05,
                                        newSmokeSensorId, (JSONObject) plugins.get(i));
                                if (null != securityDevice) {
                                    securityPluginResult.getPluginMap().get(PanelConstant.Plugin.OLD_SMOKE_SENSOR_05).add(securityDevice);
                                }
                            }
                        }
                    }
                }
                // 新型紧急按钮
                for (String newPanicButtonId : PanelConstant.Plugin.NEW_PANIC_BUTTON_IDS) {
                    if (askData.has(newPanicButtonId)) {
                        JSONArray plugins = (JSONArray) askData.get(newPanicButtonId);
                        if (plugins.length() > 0) {
                            securityPluginResult.getsTypeSet().add(newPanicButtonId);
                            for (int i = 0; i < plugins.length(); i++) {
                                securityDevice = parseSecurityDevice(panelId, PanelConstant.Plugin.OLD_PANIC_BUTTON_07,
                                        newPanicButtonId, (JSONObject) plugins.get(i));
                                if (null != securityDevice) {
                                    securityPluginResult.getPluginMap().get(PanelConstant.Plugin.OLD_PANIC_BUTTON_07).add(securityDevice);
                                }
                            }
                        }
                    }
                }
                // 新型紧急按钮
                for (String wiredBridgeId : PanelConstant.Plugin.WIRED_BRIDGE_IDS) {
                    if (askData.has(wiredBridgeId)) {
                        JSONArray plugins = (JSONArray) askData.get(wiredBridgeId);
                        if (plugins.length() > 0) {
                            securityPluginResult.getsTypeSet().add(wiredBridgeId);
                            for (int i = 0; i < plugins.length(); i++) {
                                securityDevice = parseSecurityDevice(panelId, PanelConstant.Plugin.WIRED_BRIDGE,
                                        wiredBridgeId, (JSONObject) plugins.get(i));
                                if (null != securityDevice) {
                                    securityPluginResult.getPluginMap().get(PanelConstant.Plugin.WIRED_BRIDGE).add(securityDevice);
                                }
                            }
                        }
                    }
                }
            }

            return securityPluginResult;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseSecurityPluginResult");
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 解析一个新型ASK安防配件信息
     *
     * @param panelId    主机ID
     * @param oldType    Ask配件对应的旧类型
     * @param newType    ASK配件类型
     * @param jsonObject 配件信息
     * @return SecurityDevice
     */
    private static SecurityDevice parseSecurityDevice(final String panelId, final String oldType,
                                                      final String newType, @NotNull JSONObject jsonObject) {
        try {
            String pluginId = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_ID);
            String pluginName = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_NAME);
//            if (TextUtils.isEmpty(pluginName)) {
//                String sType = PluginTypeHelper.getInstance()
//                        .getASKNameByBSType(DDJSONUtil.getString(
//                                jsonObject, NetKeyConstants.NET_KEY_S_TYPE));
//                pluginName = sType + "_" + pluginId;
//            }
            // TODO 跟新Category
            SecurityDevice securityDevice = new SecurityDevice(pluginId, 0, oldType, panelId);
            Map<String, Object> pluginParams = new HashMap<>();
            pluginParams.put(PanelDataKey.NAME, pluginName);
            pluginParams.put(PanelDataKey.SUBCATEGORY, oldType);
            pluginParams.put(PanelDataKey.HAVE_SIGNAL_LEVEL, PluginTypeHelper.getInstance().isPluginHadRssi(newType));
            pluginParams.put(PanelDataKey.CAN_TAMPER, PluginTypeHelper.getInstance().isPluginHadTamper(newType));
            pluginParams.put(PanelDataKey.CAN_CHARGING, PluginTypeHelper.getInstance().isPluginCanCharging(newType));
            pluginParams.put(PanelDataKey.HAVE_BATTERY_LEVEL, PluginTypeHelper.getInstance().isPluginHasBatteryLevel(newType));
            pluginParams.put(PanelDataKey.HAVE_WEB_SOCKET_LOADING, PluginTypeHelper.getInstance().isPluginHadWebsocketStatus(newType));
            pluginParams.put(PanelDataKey.ASK_DATA, jsonObject);
            securityDevice.setInfo(pluginParams);
            return securityDevice;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseSecurityPlugin");
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 解析JsonObject为DoorSensorResult
     * <p>
     * 新旧类型的DoorSensor数据-有序
     *
     * @param panelId      主机ID
     * @param responseBody 配件数据
     */
    public static DoorSensorResult parseDoorSensorResult(@NotNull String panelId, @NotNull ResponseBody responseBody) {
        try {
            DoorSensorResult doorSensorResult = new DoorSensorResult();
            JSONObject jsonObject = new JSONObject(responseBody.string());
            String result = PanelSecretUtil.getReverSC(String.valueOf(jsonObject.get(NetKeyConstants.NET_KEY_RESULT)));
            JSONObject resultJson = new JSONObject(result);
            jsonObject.put(NetKeyConstants.NET_KEY_RESULT, resultJson);
            Gson gson = new Gson();
            CategoryPlugsEntry categoryPlugsEntry = gson.fromJson(jsonObject.toString(), CategoryPlugsEntry.class);
            if (null == categoryPlugsEntry || null == categoryPlugsEntry.getResult()) {
                doorSensorResult.getPluginMap().clear();
                return doorSensorResult;
            }

            categoryPlugsEntry.getResult().setOtherData(DDJSONUtil.getString(resultJson, NetKeyConstants.NET_KEY_NEW_ASK_DATAS));
            // 旧配件
            String pluginId, pluginName, decodeId, subcategory;
            DoorSensorDevice doorSensorDevice;
            Map<String, Object> pluginParams;
            if (null != categoryPlugsEntry.getResult().getDatas() && 0 < categoryPlugsEntry.getResult().getDatas().size()) {
                for (CategoryPlugsEntry.ResultBean.DatasBean pluginData : categoryPlugsEntry.getResult().getDatas()) {
                    pluginId = pluginData.getId();
                    pluginName = pluginData.getName();
                    decodeId = pluginData.getDecodeid();
                    subcategory = pluginData.getSubcategory();
//                    if (TextUtils.isEmpty(pluginName)) {
//                        String name;
//                        if (!TextUtils.isEmpty(decodeId)) {
//                            //  如果decodeid不为空，则一定要是旧二维码，但是！开头的二维码不一定是新的二维码，所以先判断decodeid，再判断！
//                            name = PluginTypeHelper.getInstance().getSTypeByDecodeid(decodeId);
//                        } else if (pluginId.startsWith("!")) {
//                            name = PluginTypeHelper.getInstance().getASKNameByBSType(pluginData.getSubcategory());
//                        } else {
//                            name = PluginTypeHelper.getInstance().getSTypeByID(pluginId);
//                        }
//                        pluginName = name + "_" + pluginId;
//                    }
                    // TODO 初始化category
                    doorSensorDevice = new DoorSensorDevice(pluginId, 0, subcategory, panelId);
                    pluginParams = new HashMap<>();
                    pluginParams.put(PanelDataKey.NAME, pluginName);
                    pluginParams.put(PanelDataKey.DECODE_ID, decodeId);
                    pluginParams.put(PanelDataKey.DoorSensor.IS_SMART_DOOR_SENSOR, false);
                    doorSensorDevice.setInfo(pluginParams);
                    doorSensorResult.getOldDoorSensors().add(doorSensorDevice);
                }
            }

            // ASK配件
            if (!TextUtils.isEmpty(categoryPlugsEntry.getResult().getOtherData())) {
                final JSONObject askData = new JSONObject(categoryPlugsEntry.getResult().getOtherData());
                // 旧配件
                for (int a = 0; a < PanelConstant.Plugin.OLD_DOOR_SENSOR_CATEGORIES.length; a++) {
                    String category = PanelConstant.Plugin.OLD_DOOR_SENSOR_CATEGORIES[a];
                    if (askData.has(category)) {
                        JSONArray plugins = (JSONArray) askData.get(category);
                        for (int i = 0; i < plugins.length(); i++) {
                            pluginId = DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_ID);
                            pluginName = DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_NAME);
//                            if (TextUtils.isEmpty(pluginName)) {
//                                String sType;
//                                if (pluginId.startsWith("!")) {
//                                    sType = PluginTypeHelper.getInstance().getASKNameByBSType(
//                                            DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_S_TYPE));
//                                } else {
//                                    sType = PluginTypeHelper.getInstance().getSTypeByID(pluginId);
//                                }
//                                pluginName = sType + "_" + pluginId;
//                            }
                            // TODO 初始化category subcategory
                            String stype = DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_S_TYPE);
                            doorSensorDevice = new DoorSensorDevice(pluginId, 0, stype, panelId);
                            pluginParams = new HashMap<>();
                            pluginParams.put(PanelDataKey.NAME, pluginName);
                            pluginParams.put(PanelDataKey.S_TYPE, stype);
                            pluginParams.put(PanelDataKey.TIME_COMMON, DDJSONUtil.getLong((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_TIME));
                            pluginParams.put(PanelDataKey.DoorSensor.CAN_READY_TO_ARM, false);
                            pluginParams.put(PanelDataKey.ASK_DATA, plugins.get(i));
                            pluginParams.put(PanelDataKey.DoorSensor.IS_SMART_DOOR_SENSOR, false);
                            doorSensorDevice.setInfo(pluginParams);
                            doorSensorResult.getOldDoorSensors().add(doorSensorDevice);
                        }
                    }
                }
                // 新配件
                for (int a = 0; a < PanelConstant.Plugin.NEW_DOOR_SENSOR_CATEGORIES.length; a++) {
                    String category = PanelConstant.Plugin.NEW_DOOR_SENSOR_CATEGORIES[a];
                    if (askData.has(category)) {
                        JSONArray plugins = (JSONArray) askData.get(category);
                        if (plugins.length() > 0) {
                            doorSensorResult.getsTypeSet().add(category);
                            for (int i = 0; i < plugins.length(); i++) {
                                pluginId = DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_ID);
                                pluginName = DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_NAME);
//                                if (TextUtils.isEmpty(pluginName)) {
//                                    String sType;
//                                    if (pluginId.startsWith("!")) {
//                                        sType = PluginTypeHelper.getInstance().getASKNameByBSType(
//                                                DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_S_TYPE));
//                                    } else {
//                                        sType = PluginTypeHelper.getInstance().getSTypeByID(pluginId);
//                                    }
//
//                                    pluginName = sType + "_" + pluginId;
//                                }
                                // TODO 初始化category subcategory
                                String stype = DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_S_TYPE);
                                doorSensorDevice = new DoorSensorDevice(pluginId, 0, stype, panelId);
                                pluginParams = new HashMap<>();
                                pluginParams.put(PanelDataKey.NAME, pluginName);
                                pluginParams.put(PanelDataKey.S_TYPE, stype);
                                pluginParams.put(PanelDataKey.HAVE_SIGNAL_LEVEL, PluginTypeHelper.getInstance().isPluginHadRssi(category));
                                pluginParams.put(PanelDataKey.CAN_TAMPER, PluginTypeHelper.getInstance().isPluginHadTamper(category));
                                pluginParams.put(PanelDataKey.CAN_CHARGING, PluginTypeHelper.getInstance().isPluginCanCharging(category));
                                pluginParams.put(PanelDataKey.DoorSensor.CAN_READY_TO_ARM, PluginTypeHelper.getInstance().isPluginCanReady2Arm(category));
                                pluginParams.put(PanelDataKey.HAVE_BATTERY_LEVEL, PluginTypeHelper.getInstance().isPluginHasBatteryLevel(category));
                                pluginParams.put(PanelDataKey.HAVE_WEB_SOCKET_LOADING, PluginTypeHelper.getInstance().isPluginHadWebsocketStatus(category));
                                pluginParams.put(PanelDataKey.ASK_DATA, plugins.get(i));
                                pluginParams.put(PanelDataKey.DoorSensor.IS_SMART_DOOR_SENSOR, true);
                                doorSensorDevice.setInfo(pluginParams);
                                doorSensorResult.getNewDoorSensors().add(doorSensorDevice);
                            }
                        }
                    }
                }
            }
            return doorSensorResult;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseDoorSensorResult");
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 解析JsonObject为SimplePluginResult
     *
     * @param panelId      主机ID
     * @param responseBody 配件数据
     */
    public static SimplePluginResult<WirelessSirenDevice> parseWirelessSirenResult(@NotNull String panelId, @NotNull ResponseBody responseBody) {
        try {
            SimplePluginResult<WirelessSirenDevice> wirelessSirenResult = new SimplePluginResult<>();
            JSONObject jsonObject = new JSONObject(responseBody.string());
            String result = PanelSecretUtil.getReverSC(String.valueOf(jsonObject.get("Result")));
            JSONObject resultJson = new JSONObject(result);
            jsonObject.put(NetKeyConstants.NET_KEY_RESULT, resultJson);
            Gson gson = new Gson();
            CategoryPlugsEntry categoryPlugsEntry = gson.fromJson(jsonObject.toString(), CategoryPlugsEntry.class);
            if (null == categoryPlugsEntry || null == categoryPlugsEntry.getResult()) {
                return wirelessSirenResult;
            }

            String pluginId, pluginName, decodeId, subcategory;
            WirelessSirenDevice wirelessSirenDevice;
            Map<String, Object> pluginParams;
            // 旧警笛数据
            if (null != categoryPlugsEntry.getResult().getDatas()
                    && 0 < categoryPlugsEntry.getResult().getDatas().size()) {
                for (CategoryPlugsEntry.ResultBean.DatasBean pluginData : categoryPlugsEntry.getResult().getDatas()) {
                    pluginId = pluginData.getId();
                    pluginName = pluginData.getName();
                    decodeId = pluginData.getDecodeid();
                    subcategory = pluginData.getSubcategory();
//                    if (TextUtils.isEmpty(pluginName)) {
//                        String name;
//                        if (!TextUtils.isEmpty(decodeId)) {
//                            //  如果decodeid不为空，则一定要是旧二维码，但是！开头的二维码不一定是新的二维码，所以先判断decodeid，再判断！
//                            name = PluginTypeHelper.getInstance().getSTypeByDecodeid(decodeId);
//                        } else if (pluginId.startsWith("!")) {
//                            name = PluginTypeHelper.getInstance().getASKNameByBSType(pluginData.getSubcategory());
//                        } else {
//                            name = PluginTypeHelper.getInstance().getSTypeByID(pluginId);
//                        }
//                        pluginName = name + "_" + pluginId;
//                    }
                    // TODO 初始化category
                    wirelessSirenDevice = new WirelessSirenDevice(pluginId, 0, subcategory, panelId);
                    pluginParams = new HashMap<>();
                    pluginParams.put(PanelDataKey.NAME, pluginName);
                    pluginParams.put(PanelDataKey.DECODE_ID, decodeId);
                    pluginParams.put(PanelDataKey.WirelessSiren.SIREN_DATA, pluginData.getSiren_setting());
                    wirelessSirenDevice.setInfo(pluginParams);
                    wirelessSirenResult.getPluginList().add(wirelessSirenDevice);
                }
            }

            // ASK警笛数据
            JSONObject object = DDJSONUtil.getJSONObject(resultJson, NetKeyConstants.NET_KEY_NEW_ASK_DATAS);
            if (null != object) {
                for (String wirelessId : PanelConstant.Plugin.ASK_WIRELESS_IDS) {
                    if (object.has(wirelessId)) {
                        JSONArray plugins = (JSONArray) object.get(wirelessId);
                        if (plugins.length() > 0) {
                            wirelessSirenResult.getsTypeSet().add(wirelessId);
                            for (int i = 0; i < plugins.length(); i++) {
                                pluginId = DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_ID);
                                pluginName = DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_NAME);
                                decodeId = DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_DECODE_ID);
//                                if (TextUtils.isEmpty(pluginName)) {
//                                    String name = PluginTypeHelper.getInstance().getASKNameByBSType(wirelessId);
//                                    pluginName = name + "_" + pluginId;
//                                }
                                // TODO 初始化category subcategory
                                wirelessSirenDevice = new WirelessSirenDevice(pluginId, 0, wirelessId, panelId);
                                pluginParams = new HashMap<>();
                                pluginParams.put(PanelDataKey.NAME, pluginName);
                                pluginParams.put(PanelDataKey.DECODE_ID, decodeId);
                                pluginParams.put(PanelDataKey.HAVE_SIGNAL_LEVEL, PluginTypeHelper.getInstance().isPluginHadRssi(wirelessId));
                                pluginParams.put(PanelDataKey.CAN_TAMPER, PluginTypeHelper.getInstance().isPluginHadTamper(wirelessId));
                                pluginParams.put(PanelDataKey.CAN_CHARGING, PluginTypeHelper.getInstance().isPluginCanCharging(wirelessId));
                                pluginParams.put(PanelDataKey.HAVE_BATTERY_LEVEL, PluginTypeHelper.getInstance().isPluginHasBatteryLevel(wirelessId));
                                pluginParams.put(PanelDataKey.HAVE_WEB_SOCKET_LOADING, PluginTypeHelper.getInstance().isPluginHadWebsocketStatus(wirelessId));
                                pluginParams.put(PanelDataKey.ASK_DATA, plugins.get(i));
                                pluginParams.put(PanelDataKey.SEND_ID, DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_SEND_ID));
                                pluginParams.put(PanelDataKey.S_TYPE, DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_S_TYPE));
                                wirelessSirenDevice.setInfo(pluginParams);
                                wirelessSirenResult.getPluginList().add(wirelessSirenDevice);
                            }
                        }
                    }
                }
            }

            return wirelessSirenResult;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseWirelessSirenResult");
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 解析JsonObject为SimplePluginResult
     *
     * @param panelId      主机ID
     * @param responseBody 配件数据
     */
    public static SimplePluginResult<RemoteControlDevice> parseRemoteControlResult(@NotNull String panelId, @NotNull ResponseBody responseBody) {
        try {
            SimplePluginResult<RemoteControlDevice> remoteControlResult = new SimplePluginResult<>();
            JSONObject jsonObject = new JSONObject(responseBody.string());
            String result = PanelSecretUtil.getReverSC(String.valueOf(jsonObject.get(NetKeyConstants.NET_KEY_RESULT)));
            JSONObject resultJson = new JSONObject(result);
            jsonObject.put(NetKeyConstants.NET_KEY_RESULT, resultJson);
            Gson gson = new Gson();
            CategoryPlugsEntry categoryPlugsEntry = gson.fromJson(jsonObject.toString(), CategoryPlugsEntry.class);
            if (null == categoryPlugsEntry || null == categoryPlugsEntry.getResult()) {
                return remoteControlResult;
            }

            categoryPlugsEntry.getResult().setOtherData(DDJSONUtil.getString(resultJson, NetKeyConstants.NET_KEY_NEW_ASK_DATAS));
            String pluginId, pluginName, decodeId, subcategory, defaultName;
            RemoteControlDevice remoteControlDevice;
            Map<String, Object> pluginParams;
            boolean hasRemoteControl = false; // 目前仅当列表中包含stype为3A类型的五键遥控时，显示底部自定义视图
            if (null != categoryPlugsEntry.getResult().getDatas()
                    && 0 < categoryPlugsEntry.getResult().getDatas().size()) {
                for (CategoryPlugsEntry.ResultBean.DatasBean pluginData : categoryPlugsEntry.getResult().getDatas()) {
                    pluginId = pluginData.getId();
                    pluginName = pluginData.getName();
                    decodeId = pluginData.getDecodeid();
                    subcategory = pluginData.getSubcategory();
                    // 获取默认名字用于过滤配件和设置配件默认名字
                    if (!TextUtils.isEmpty(decodeId)) {
                        // 如果decodeid不为空，则一定要是旧二维码，但是！开头的二维码不一定是新的二维码，所以先判断decodeid，再判断！
                        defaultName = PluginTypeHelper.getInstance().getSTypeByDecodeid(decodeId);
                    } else if (pluginId.startsWith("!")) {
                        defaultName = PluginTypeHelper.getInstance().getASKNameByBSType(subcategory);
                    } else {
                        defaultName = PluginTypeHelper.getInstance().getSTypeByID(pluginId);
                    }

                    if (RC_DEFAULT_NAME.equals(defaultName)) {
                        DDLog.i(TAG, "是遥控器，添加到遥控器列表");
                        // 是遥控器，添加到列表
//                        if (TextUtils.isEmpty(pluginName)) {
//                            pluginName = defaultName + "_" + pluginId;
//
//                        }
                        // TODO 初始化category
                        remoteControlDevice = new RemoteControlDevice(pluginId, 0, subcategory, panelId);
                        pluginParams = new HashMap<>();
                        pluginParams.put(PanelDataKey.NAME, pluginName);
                        pluginParams.put(PanelDataKey.DECODE_ID, decodeId);
                        pluginParams.put(PanelDataKey.WirelessSiren.SIREN_DATA, pluginData.getSiren_setting());
                        remoteControlDevice.setInfo(pluginParams);
                        remoteControlResult.getPluginList().add(remoteControlDevice);
                    }
                    // 不是遥控器，不处理
                    DDLog.i(TAG, "不是遥控器，不处理");
                }
            }

            // 判断是否包含新的遥控器
            if (null != categoryPlugsEntry.getResult().getOtherData()) {
                JSONObject askData = new JSONObject(categoryPlugsEntry.getResult().getOtherData());
                for (String askId : PanelConstant.Plugin.ASK_REMOTE_CONTROL_IDS) {
                    if (askData.has(askId)) {
                        JSONArray plugins = (JSONArray) askData.get(askId);
                        if (plugins.length() > 0) {
                            if (!hasRemoteControl && PluginConstants.TYPE_3A.equals(askId)) {
                                hasRemoteControl = true;
                            }
                            remoteControlResult.getsTypeSet().add(askId);
                            for (int i = 0; i < plugins.length(); i++) {
                                pluginId = DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_ID);
                                pluginName = DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_NAME);
                                decodeId = DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_DECODE_ID);
//                                if (TextUtils.isEmpty(pluginName)) {
//                                    String name;
//                                    if (!TextUtils.isEmpty(decodeId)) {
//                                        // 如果decodeid不为空，则一定要是旧二维码，但是！开头的二维码不一定是新的二维码，所以先判断decodeid，再判断！
//                                        name = PluginTypeHelper.getInstance().getSTypeByDecodeid(decodeId);
//                                    } else if (pluginId.startsWith("!")) {
//                                        name = PluginTypeHelper.getInstance().getASKNameByBSType(
//                                                DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_S_TYPE));
//                                    } else {
//                                        name = PluginTypeHelper.getInstance().getSTypeByID(pluginId);
//                                    }
//                                    pluginName = name + "_" + pluginId;
//                                }
                                // TODO 初始化category subcategory
                                String stype = DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_S_TYPE);
                                remoteControlDevice = new RemoteControlDevice(pluginId, 0, stype, panelId);
                                pluginParams = new HashMap<>();
                                pluginParams.put(PanelDataKey.NAME, pluginName);
                                pluginParams.put(PanelDataKey.S_TYPE, stype);
                                pluginParams.put(PanelDataKey.DECODE_ID, decodeId);
                                pluginParams.put(PanelDataKey.TIME_COMMON, DDJSONUtil.getLong((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_TIME));
                                pluginParams.put(PanelDataKey.HAVE_SIGNAL_LEVEL, PluginTypeHelper.getInstance().isPluginHadRssi(askId));
                                pluginParams.put(PanelDataKey.CAN_TAMPER, PluginTypeHelper.getInstance().isPluginHadTamper(askId));
                                pluginParams.put(PanelDataKey.CAN_CHARGING, PluginTypeHelper.getInstance().isPluginCanCharging(askId));
                                pluginParams.put(PanelDataKey.HAVE_BATTERY_LEVEL, PluginTypeHelper.getInstance().isPluginHasBatteryLevel(askId));
                                pluginParams.put(PanelDataKey.HAVE_WEB_SOCKET_LOADING, PluginTypeHelper.getInstance().isPluginHadWebsocketStatus(askId));
                                pluginParams.put(PanelDataKey.DoorSensor.CAN_READY_TO_ARM, false);
                                pluginParams.put(PanelDataKey.ASK_DATA, plugins.get(i));
                                remoteControlDevice.setInfo(pluginParams);
                                remoteControlResult.getPluginList().add(remoteControlDevice);
                            }
                        }
                    }
                }
            }
            return remoteControlResult;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseRemoteControlResult");
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 解析JsonObject为SimplePluginResult
     *
     * @param panelId      主机ID
     * @param responseBody 配件数据
     */
    public static SimplePluginResult<KeypadDevice> parseKeypadResult(@NotNull String panelId, @NotNull ResponseBody responseBody) {
        try {
            SimplePluginResult<KeypadDevice> keypadResult = new SimplePluginResult<>();

            JSONObject jsonObject = new JSONObject(responseBody.string());
            String result = PanelSecretUtil.getReverSC(String.valueOf(jsonObject.get(NetKeyConstants.NET_KEY_RESULT)));
            JSONObject resultJson = new JSONObject(result);
            jsonObject.put(NetKeyConstants.NET_KEY_RESULT, resultJson);
            Gson gson = new Gson();
            CategoryPlugsEntry categoryPlugsEntry = gson.fromJson(jsonObject.toString(), CategoryPlugsEntry.class);
            if (null == categoryPlugsEntry || null == categoryPlugsEntry.getResult()) {
                return keypadResult;
            }

            categoryPlugsEntry.getResult().setOtherData(DDJSONUtil.getString(resultJson, NetKeyConstants.NET_KEY_NEW_ASK_DATAS));
            String pluginId, pluginName, decodeId, subcategory, defaultName;
            KeypadDevice keypadDevice;
            Map<String, Object> pluginParams;
            if (null != categoryPlugsEntry.getResult().getDatas()
                    && 0 < categoryPlugsEntry.getResult().getDatas().size()) {
                // 判断是否包含普通的键盘
                if (categoryPlugsEntry.getResult().getDatas() != null) {
                    for (CategoryPlugsEntry.ResultBean.DatasBean pluginData : categoryPlugsEntry.getResult().getDatas()) {
                        pluginId = pluginData.getId();
                        pluginName = pluginData.getName();
                        decodeId = pluginData.getDecodeid();
                        subcategory = pluginData.getSubcategory();
                        // 获取默认名字用于过滤配件和设置配件默认名字
                        if (!TextUtils.isEmpty(decodeId)) {
                            // 如果decodeid不为空，则一定要是旧二维码，但是！开头的二维码不一定是新的二维码，所以先判断decodeid，再判断！
                            defaultName = PluginTypeHelper.getInstance().getSTypeByDecodeid(decodeId);
                        } else if (pluginId.startsWith("!")) {
                            defaultName = PluginTypeHelper.getInstance().getASKNameByBSType(subcategory);
                        } else {
                            defaultName = PluginTypeHelper.getInstance().getSTypeByID(pluginId);
                        }

                        if (KEYPAD_DEFAULT_NAME.equals(defaultName) || RFID_DEFAULT_NAME.equals(defaultName)) {
                            DDLog.i(TAG, "是键盘或RFID，添加到键盘列表");
                            // 是键盘或RFID，添加到列表
//                            if (TextUtils.isEmpty(pluginName)) {
//                                pluginName = defaultName + "_" + pluginId;
//
//                            }
                            // TODO 初始化category
                            keypadDevice = new KeypadDevice(pluginId, 0, subcategory, panelId);
                            pluginParams = new HashMap<>();
                            pluginParams.put(PanelDataKey.NAME, pluginName);
                            pluginParams.put(PanelDataKey.DECODE_ID, decodeId);
                            pluginParams.put(PanelDataKey.WirelessSiren.SIREN_DATA, pluginData.getSiren_setting());
                            keypadDevice.setInfo(pluginParams);
                            keypadResult.getPluginList().add(keypadDevice);
                        }
                        // 不是键盘或RFID，不处理
                        DDLog.i(TAG, "不是键盘或RFID，不处理");
                    }
                }
            }
            // 判断是否包含新键盘
            String stype;
            if (null != categoryPlugsEntry.getResult().getOtherData()) {
                JSONObject askData = new JSONObject(categoryPlugsEntry.getResult().getOtherData());
                for (String askId : PanelConstant.Plugin.ASK_WIRELESS_KEYPAD_IDS) {
                    if (askData.has(askId)) {
                        JSONArray plugins = (JSONArray) askData.get(askId);
                        if (plugins.length() > 0) {
                            keypadResult.getsTypeSet().add(askId);
                            for (int i = 0; i < plugins.length(); i++) {
                                pluginId = DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_ID);
                                pluginName = DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_NAME);
                                decodeId = DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_DECODE_ID);
//                                if (TextUtils.isEmpty(pluginName)) {
//                                    String name;
//                                    if (!TextUtils.isEmpty(decodeId)) {
//                                        //  如果decodeid不为空，则一定要是旧二维码，但是！开头的二维码不一定是新的二维码，所以先判断decodeid，再判断！
//                                        name = PluginTypeHelper.getInstance().getSTypeByDecodeid(decodeId);
//                                    } else if (pluginId.startsWith("!")) {
//                                        name = PluginTypeHelper.getInstance().getASKNameByBSType(
//                                                DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_S_TYPE));
//                                    } else {
//                                        name = PluginTypeHelper.getInstance().getSTypeByID(pluginId);
//                                    }
//                                    pluginName = name + "_" + pluginId;
//                                }
                                // TODO 初始化category subcategory
                                stype = DDJSONUtil.getString((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_S_TYPE);
                                keypadDevice = new KeypadDevice(pluginId, 0, stype, panelId);
                                pluginParams = new HashMap<>();
                                pluginParams.put(PanelDataKey.NAME, pluginName);
                                pluginParams.put(PanelDataKey.S_TYPE, stype);
                                pluginParams.put(PanelDataKey.DECODE_ID, decodeId);
                                pluginParams.put(PanelDataKey.TIME_COMMON, DDJSONUtil.getLong((JSONObject) plugins.get(i), NetKeyConstants.NET_KEY_TIME));
                                pluginParams.put(PanelDataKey.HAVE_SIGNAL_LEVEL, PluginTypeHelper.getInstance().isPluginHadRssi(askId));
                                pluginParams.put(PanelDataKey.CAN_TAMPER, PluginTypeHelper.getInstance().isPluginHadTamper(askId));
                                pluginParams.put(PanelDataKey.CAN_CHARGING, PluginTypeHelper.getInstance().isPluginCanCharging(askId));
                                pluginParams.put(PanelDataKey.HAVE_BATTERY_LEVEL, PluginTypeHelper.getInstance().isPluginHasBatteryLevel(askId));
                                pluginParams.put(PanelDataKey.HAVE_WEB_SOCKET_LOADING, PluginTypeHelper.getInstance().isPluginHadWebsocketStatus(askId));
                                pluginParams.put(PanelDataKey.DoorSensor.CAN_READY_TO_ARM, false);
                                pluginParams.put(PanelDataKey.ASK_DATA, plugins.get(i));
                                keypadDevice.setInfo(pluginParams);
                                keypadResult.getPluginList().add(keypadDevice);
                            }
                        }
                    }
                }
            }
            return keypadResult;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseKeypadResult");
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 解析JsonObject为CustomizeHomeArmResult
     *
     * @param panelId            主机ID
     * @param homeArmStatueEntry 配件数据
     */
    public static CustomizeHomeArmResult parseCustomizeHomeArmResult(@NotNull String panelId, HomeArmStatueEntry homeArmStatueEntry) {
        try {
            CustomizeHomeArmResult result = new CustomizeHomeArmResult();
            if (null == homeArmStatueEntry || null == homeArmStatueEntry.getResult()) {
                result.getPluginMap().clear();
                return result;
            }

            int pirIndex = 0;
            String pluginId, pluginName, subcategory, stype;
            Map<String, Object> pluginInfoMap;
            // Official配件-旧
            if (null != homeArmStatueEntry.getResult().getHomearmsetting()
                    && 0 < homeArmStatueEntry.getResult().getHomearmsetting().size()) {
                for (HomeArmStatueEntry.ResultBean.HomearmsettingBean pluginData
                        : homeArmStatueEntry.getResult().getHomearmsetting()) {
                    pluginId = pluginData.getId();
                    pluginName = pluginData.getName();
                    subcategory = pluginData.getSubcategory();
                    if (PanelConstant.Plugin.OLD_PIR_SENSOR_09.equals(subcategory)) {
                        //   确定有多少个红外，方便插入心跳红外
                        pirIndex++;
                    }
                    // TODO 更新category
                    pluginInfoMap = new HashMap<>();
                    pluginInfoMap.put(PanelDataKey.ID, pluginId);
                    pluginInfoMap.put(PanelDataKey.SUBCATEGORY, subcategory);
                    pluginInfoMap.put(PanelDataKey.NAME, pluginName);
                    pluginInfoMap.put(PanelDataKey.IS_THIRD_PART_PLUGIN, false);
                    if (pluginData.isHomearmenable()) {
                        pluginInfoMap.put(PanelDataKey.PLUGIN_SWITCH_STATE, PanelConstant.PluginSwitchState.OPENED);
                    } else {
                        pluginInfoMap.put(PanelDataKey.PLUGIN_SWITCH_STATE, PanelConstant.PluginSwitchState.CLOSED);
                    }
                    result.getOfficialPlugin().add(pluginInfoMap);
                }
            }

            // Official配件-ask plugin
            if (null != homeArmStatueEntry.getResult().getNewaskplugin()
                    && 0 < homeArmStatueEntry.getResult().getNewaskplugin().size()) {
                try {
                    Gson gson = new Gson();
                    for (HomeArmStatueEntry.ResultBean.NewaskpluginBean pluginData
                            : homeArmStatueEntry.getResult().getNewaskplugin()) {
                        String askData = gson.toJson(pluginData);
                        stype = pluginData.getStype();
                        pluginId = pluginData.getId();
                        pluginName = pluginData.getName();

                        if (PluginConstants.TYPE_17.equals(stype)
                                || PluginConstants.TYPE_24.equals(stype)
                                || PluginConstants.TYPE_36.equals(stype)
                                || PluginConstants.TYPE_4A.equals(stype)) {
                            // TODO 更新category
                            pluginInfoMap = new HashMap<>();
                            pluginInfoMap.put(PanelDataKey.ID, pluginId);
                            pluginInfoMap.put(PanelDataKey.SUBCATEGORY, stype);
                            pluginInfoMap.put(PanelDataKey.NAME, pluginName);
                            pluginInfoMap.put(PanelDataKey.IS_THIRD_PART_PLUGIN, false);
                            pluginInfoMap.put(PanelDataKey.ASK_DATA, new JSONObject(askData));
                            if (pluginData.isHomearmenable()) {
                                pluginInfoMap.put(PanelDataKey.PLUGIN_SWITCH_STATE, PanelConstant.PluginSwitchState.OPENED);
                            } else {
                                pluginInfoMap.put(PanelDataKey.PLUGIN_SWITCH_STATE, PanelConstant.PluginSwitchState.CLOSED);
                            }
                            result.getAskPlugin().add(pluginInfoMap);
                            pirIndex++;
                        } else {
                            // TODO 更新category
                            pluginInfoMap = new HashMap<>();
                            pluginInfoMap.put(PanelDataKey.ID, pluginId);
                            pluginInfoMap.put(PanelDataKey.SUBCATEGORY, stype);
                            pluginInfoMap.put(PanelDataKey.NAME, pluginName);
                            pluginInfoMap.put(PanelDataKey.IS_THIRD_PART_PLUGIN, false);
                            pluginInfoMap.put(PanelDataKey.ASK_DATA, new JSONObject(askData));
                            if (pluginData.isHomearmenable()) {
                                pluginInfoMap.put(PanelDataKey.PLUGIN_SWITCH_STATE, PanelConstant.PluginSwitchState.OPENED);
                            } else {
                                pluginInfoMap.put(PanelDataKey.PLUGIN_SWITCH_STATE, PanelConstant.PluginSwitchState.CLOSED);
                            }
                            result.getAskPlugin().add(pluginInfoMap);
                        }
                    }
                } catch (JSONException e) {
                    DDLog.e(TAG, "Error");
                    e.printStackTrace();
                }
            }

            // ThirdPart plugin
            if (null != homeArmStatueEntry.getResult().getThirdpartyhomearmsetting()
                    && 0 < homeArmStatueEntry.getResult().getThirdpartyhomearmsetting().size()) {
                for (HomeArmStatueEntry.ResultBean.ThirdpartyhomearmsettingBean pluginData
                        : homeArmStatueEntry.getResult().getThirdpartyhomearmsetting()) {
                    subcategory = pluginData.getSubcategory();
                    pluginId = pluginData.getId();
                    pluginName = pluginData.getName();
                    pluginInfoMap = new HashMap<>();
                    pluginInfoMap.put(PanelDataKey.ID, pluginId);
                    pluginInfoMap.put(PanelDataKey.SUBCATEGORY, subcategory);
                    pluginInfoMap.put(PanelDataKey.NAME, pluginName);
                    pluginInfoMap.put(PanelDataKey.IS_THIRD_PART_PLUGIN, true);
                    if (pluginData.isHomearmenable()) {
                        pluginInfoMap.put(PanelDataKey.PLUGIN_SWITCH_STATE, PanelConstant.PluginSwitchState.OPENED);
                    } else {
                        pluginInfoMap.put(PanelDataKey.PLUGIN_SWITCH_STATE, PanelConstant.PluginSwitchState.CLOSED);
                    }
                    result.getThirdPartPlugin().add(pluginInfoMap);
                }
            }

            return result;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseCustomizeHomeArmResult");
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 解析JsonObject为CustomizeHomeArmResult
     *
     * @param panelId         主机ID
     * @param entryDelayModel 配件数据
     */
    public static CustomizeHomeArmResult parseEntryDelayResult(@NotNull String panelId, EntryDelayModel entryDelayModel) {
        try {
            CustomizeHomeArmResult result = new CustomizeHomeArmResult();
            if (null == entryDelayModel || null == entryDelayModel.getResult()) {
                result.getPluginMap().clear();
                return result;
            }

            int pirIndex = 0;
            String pluginId, pluginName, subcategory, stype;
            Map<String, Object> pluginInfoMap;
            // Official配件-旧
            if (null != entryDelayModel.getResult().getPlugins()
                    && 0 < entryDelayModel.getResult().getPlugins().size()) {
                for (EntryDelayModel.ResultBean.PluginsBean pluginData
                        : entryDelayModel.getResult().getPlugins()) {
                    pluginId = pluginData.getId();
                    pluginName = pluginData.getName();
                    subcategory = pluginData.getSubcategory();
                    if (PanelConstant.Plugin.OLD_PIR_SENSOR_09.equals(subcategory)) {
                        //   确定有多少个红外，方便插入心跳红外
                        pirIndex++;
                    }
                    // TODO 更新category
                    pluginInfoMap = new HashMap<>();
                    pluginInfoMap.put(PanelDataKey.ID, pluginId);
                    pluginInfoMap.put(PanelDataKey.SUBCATEGORY, subcategory);
                    pluginInfoMap.put(PanelDataKey.NAME, pluginName);
                    pluginInfoMap.put(PanelDataKey.IS_THIRD_PART_PLUGIN, false);
                    if (pluginData.isEntrydelayenable()) {
                        pluginInfoMap.put(PanelDataKey.PLUGIN_SWITCH_STATE, PanelConstant.PluginSwitchState.OPENED);
                    } else {
                        pluginInfoMap.put(PanelDataKey.PLUGIN_SWITCH_STATE, PanelConstant.PluginSwitchState.CLOSED);
                    }
                    result.getOfficialPlugin().add(pluginInfoMap);
                }
            }

            // Official配件-ask plugin
            if (null != entryDelayModel.getResult().getNewaskplugin()
                    && 0 < entryDelayModel.getResult().getNewaskplugin().size()) {
                try {
                    Gson gson = new Gson();
                    for (EntryDelayModel.ResultBean.NewaskpluginBean pluginData
                            : entryDelayModel.getResult().getNewaskplugin()) {
                        String askData = gson.toJson(pluginData);
                        stype = pluginData.getStype();
                        pluginId = pluginData.getId();
                        pluginName = pluginData.getName();

                        if (PluginConstants.TYPE_17.equals(stype)
                                || PluginConstants.TYPE_24.equals(stype)
                                || PluginConstants.TYPE_36.equals(stype)
                                || PluginConstants.TYPE_4A.equals(stype)) {
                            // TODO 更新category
                            pluginInfoMap = new HashMap<>();
                            pluginInfoMap.put(PanelDataKey.ID, pluginId);
                            pluginInfoMap.put(PanelDataKey.SUBCATEGORY, PluginConstants.TYPE_09);
                            pluginInfoMap.put(PanelDataKey.NAME, pluginName);
                            pluginInfoMap.put(PanelDataKey.IS_THIRD_PART_PLUGIN, false);
                            pluginInfoMap.put(PanelDataKey.ASK_DATA, new JSONObject(askData));
                            if (pluginData.isEntrydelayenable()) {
                                pluginInfoMap.put(PanelDataKey.PLUGIN_SWITCH_STATE, PanelConstant.PluginSwitchState.OPENED);
                            } else {
                                pluginInfoMap.put(PanelDataKey.PLUGIN_SWITCH_STATE, PanelConstant.PluginSwitchState.CLOSED);
                            }
                            result.getAskPlugin().add(pluginInfoMap);
                            pirIndex++;
                        } else {
                            // TODO 更新category
                            pluginInfoMap = new HashMap<>();
                            pluginInfoMap.put(PanelDataKey.ID, pluginId);
                            pluginInfoMap.put(PanelDataKey.SUBCATEGORY, stype);
                            pluginInfoMap.put(PanelDataKey.NAME, pluginName);
                            pluginInfoMap.put(PanelDataKey.IS_THIRD_PART_PLUGIN, false);
                            pluginInfoMap.put(PanelDataKey.ASK_DATA, new JSONObject(askData));
                            if (pluginData.isEntrydelayenable()) {
                                pluginInfoMap.put(PanelDataKey.PLUGIN_SWITCH_STATE, PanelConstant.PluginSwitchState.OPENED);
                            } else {
                                pluginInfoMap.put(PanelDataKey.PLUGIN_SWITCH_STATE, PanelConstant.PluginSwitchState.CLOSED);
                            }
                            result.getAskPlugin().add(pluginInfoMap);
                        }
                    }
                } catch (JSONException e) {
                    DDLog.e(TAG, "Error");
                    e.printStackTrace();
                }
            }

            // ThirdPart plugin
            if (null != entryDelayModel.getResult().getThirdpartyplugins()
                    && 0 < entryDelayModel.getResult().getThirdpartyplugins().size()) {
                for (EntryDelayModel.ResultBean.ThirdpartypluginsBean pluginData
                        : entryDelayModel.getResult().getThirdpartyplugins()) {
                    subcategory = pluginData.getSubcategory();
                    pluginId = pluginData.getId();
                    pluginName = pluginData.getName();
                    pluginInfoMap = new HashMap<>();
                    pluginInfoMap.put(PanelDataKey.ID, pluginId);
                    pluginInfoMap.put(PanelDataKey.SUBCATEGORY, subcategory);
                    pluginInfoMap.put(PanelDataKey.NAME, pluginName);
                    pluginInfoMap.put(PanelDataKey.IS_THIRD_PART_PLUGIN, true);
                    if (pluginData.isEntrydelayenable()) {
                        pluginInfoMap.put(PanelDataKey.PLUGIN_SWITCH_STATE, PanelConstant.PluginSwitchState.OPENED);
                    } else {
                        pluginInfoMap.put(PanelDataKey.PLUGIN_SWITCH_STATE, PanelConstant.PluginSwitchState.CLOSED);
                    }
                    result.getThirdPartPlugin().add(pluginInfoMap);
                }
            }
            if (null != entryDelayModel.getResult()) {
                result.setTime(entryDelayModel.getResult().getTime());
                result.setEntrydelaysound(entryDelayModel.getResult().isEntrydelaysound());
                result.setOptions(entryDelayModel.getResult().getOptions());
            }

            return result;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseCustomizeHomeArmResult");
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 解析JsonObject为PanelDevice列表
     *
     * @param panelId            主机ID
     * @param categoryPlugsEntry 配件数据
     */
    public static ArrayList<OtherDevice> parseOtherPluginList(@NotNull String panelId, @NotNull CategoryPlugsEntry categoryPlugsEntry) {
        try {
            ArrayList<OtherDevice> panelDeviceList = new ArrayList<>();
            if (null == categoryPlugsEntry.getResult()
                    || null == categoryPlugsEntry.getResult().getDatas()
                    || 0 < categoryPlugsEntry.getResult().getDatas().size()) {
                return panelDeviceList;
            }

            OtherDevice otherDevice;
            Map<String, Object> pluginParams;
            for (CategoryPlugsEntry.ResultBean.DatasBean pluginData : categoryPlugsEntry.getResult().getDatas()) {
                // TODO 初始化category
                otherDevice = new OtherDevice(pluginData.getId(), 0, pluginData.getPlugin_item_sub_category(), panelId);
                pluginParams = new HashMap<>();
                pluginParams.put(PanelDataKey.NAME, pluginData.getName());
                pluginParams.put(PanelDataKey.DECODE_ID, pluginData.getDecodeid());
                pluginParams.put(PanelDataKey.WirelessSiren.SIREN_DATA, pluginData.getSiren_setting());
                otherDevice.setInfo(pluginParams);
                panelDeviceList.add(otherDevice);
            }
            return panelDeviceList;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseRelayList");
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 解析JsonObject为DoorBellDevice列表
     *
     * @param panelId      主机ID
     * @param responseBody 配件数据
     */
    public static ArrayList<DoorBellDevice> parseDoorbellResult(@NotNull String panelId, @NotNull ResponseBody responseBody) {
        try {
            ArrayList<DoorBellDevice> doorBellDeviceList = new ArrayList<>();
            JSONObject jsonObject = new JSONObject(responseBody.string());
            String result = PanelSecretUtil.getReverSC(String.valueOf(jsonObject.get(NetKeyConstants.NET_KEY_RESULT)));
            JSONObject resultJson = new JSONObject(result);
            jsonObject.put(NetKeyConstants.NET_KEY_RESULT, resultJson);
            JSONArray resultArray = DDJSONUtil.getJSONarray(resultJson, "doorbelldata");
            if (null == resultArray || 0 >= resultArray.length()) {
                return doorBellDeviceList;
            }

            String pluginId, pluginName, decodeId;
            DoorBellDevice doorBellDevice;
            Map<String, Object> pluginParams;
            JSONObject pluginData;
            for (int i = 0; i < resultArray.length(); i++) {
                pluginData = resultArray.getJSONObject(i);
                pluginId = DDJSONUtil.getString(pluginData, NetKeyConstants.NET_KEY_ID);
                pluginName = DDJSONUtil.getString(resultArray.getJSONObject(i), NetKeyConstants.NET_KEY_NAME);
                decodeId = DDJSONUtil.getString(resultArray.getJSONObject(i), NetKeyConstants.NET_KEY_DECODE_ID);
                // TODO 初始化category subcategory
                doorBellDevice = new DoorBellDevice(pluginId, 0, null, panelId);
                pluginParams = new HashMap<>();
                pluginParams.put(PanelDataKey.NAME, pluginName);
                pluginParams.put(PanelDataKey.DECODE_ID, decodeId);
                doorBellDevice.setInfo(pluginParams);
                doorBellDeviceList.add(doorBellDevice);
            }
            return doorBellDeviceList;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseWirelessSirenResult");
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 解析JsonObject为DoorBellDevice列表
     *
     * @param panelId  主机ID
     * @param doorBell 配件数据
     */
    public static ArrayList<DoorBellDevice> parseDoorbellResult(@NotNull String panelId, @NotNull DoorBell doorBell) {
        try {
            ArrayList<DoorBellDevice> doorBellDeviceList = new ArrayList<>();
            if (null == doorBell.getResult() || 0 >= doorBell.getResult().size()) {
                return doorBellDeviceList;
            }

            DoorBellDevice doorBellDevice;
            Map<String, Object> pluginParams;

            for (DoorBell.ResultBean pluginData : doorBell.getResult()) {
                // TODO 初始化category subcategory
                doorBellDevice = new DoorBellDevice(pluginData.getId(), 0, null, panelId);
                pluginParams = new HashMap<>();
                pluginParams.put(PanelDataKey.NAME, pluginData.getDoorbellname());
                pluginParams.put(PanelDataKey.Doorbell.RECORD_TIME, pluginData.getRecordtime());
                pluginParams.put(PanelDataKey.Doorbell.IMAGE, pluginData.getImg());
                doorBellDevice.setInfo(pluginParams);
                doorBellDeviceList.add(doorBellDevice);
            }
            return doorBellDeviceList;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseWirelessSirenResult");
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 解析JsonObject为Map数据
     *
     * @param newAskPlugInfo 配件数据
     */
    public static Map<String, Object> parseNewAskPlugInfo(@NotNull NewAskPlugInfo newAskPlugInfo) {
        try {
            Map<String, Object> newAskPlugInfoMap = new HashMap<>();
            NewAskPlugInfo.ResultBean pluginData = newAskPlugInfo.getResult();
            if (null == pluginData) {
                return newAskPlugInfoMap;
            }

            newAskPlugInfoMap.put(PanelDataKey.D_TYPE, pluginData.getDtype());
            newAskPlugInfoMap.put(PanelDataKey.ID, pluginData.getId());
            newAskPlugInfoMap.put(PanelDataKey.S_TYPE, pluginData.getStype());
            newAskPlugInfoMap.put(PanelDataKey.SEND_ID, pluginData.getSendid());

            return newAskPlugInfoMap;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseWirelessSirenResult");
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 解析JsonObject为Map数据
     *
     * @param responseBody 配件数据
     */
    public static Map<String, Object> parseCustomizeSmartPlugs(@NotNull ResponseBody responseBody) {
        try {
            Map<String, Object> resultMap = new HashMap<>();

            JSONObject jsonObject = new JSONObject(responseBody.string());
            String result = PanelSecretUtil.getReverSC(String.valueOf(jsonObject.get(NetKeyConstants.NET_KEY_RESULT)));
            JSONObject resultJson = new JSONObject(result);

            Map<String, Object> plugMap;
            // 当前设置的插座信息
            JSONObject customizeRemoteControlData = DDJSONUtil.getJSONObject(resultJson, "customize_remote_control_data");
            if (null != customizeRemoteControlData) {
                plugMap = new HashMap<>();
                plugMap.put(PanelDataKey.ID, DDJSONUtil.getString(customizeRemoteControlData, "pluginid"));
                plugMap.put(PanelDataKey.D_TYPE, DDJSONUtil.getInt(customizeRemoteControlData, "dtype"));
                plugMap.put(PanelDataKey.SEND_ID, DDJSONUtil.getString(customizeRemoteControlData, "sendid"));
                plugMap.put(PanelDataKey.S_TYPE, DDJSONUtil.getString(customizeRemoteControlData, "stype"));
                resultMap.put(PanelDataKey.RemoteControl.CUSTOMIZE_PLUG, plugMap);
            }

            ArrayList<Map<String, Object>> plugList = new ArrayList<>();

            // 旧的插座信息
            JSONArray datas = DDJSONUtil.getJSONarray(resultJson, "datas");
            if (null != datas && 0 < datas.length()) {
                JSONObject oldPlug;
                for (int i = 0; i < datas.length(); i++) {
                    oldPlug = (JSONObject) datas.get(i);
                    plugMap = new HashMap<>();
                    plugMap.put(PanelDataKey.ID, DDJSONUtil.getString(oldPlug, "id"));
                    plugMap.put(PanelDataKey.NAME, DDJSONUtil.getString(oldPlug, "name"));
                    plugList.add(plugMap);
                }
            }

            // 新的ASK插座信息
            JSONObject newaskdatas = DDJSONUtil.getJSONObject(resultJson, "newaskdatas");
            if (null != newaskdatas) {
                Iterator<String> newAskPlugStypeKeys = newaskdatas.keys();
                String stypeKey;
                JSONArray stypePlugs;
                JSONObject stypePlug;
                while (newAskPlugStypeKeys.hasNext()) {
                    stypeKey = newAskPlugStypeKeys.next();
                    stypePlugs = DDJSONUtil.getJSONarray(newaskdatas, stypeKey);
                    if (null != stypePlugs && 0 < stypePlugs.length()) {
                        for (int i = 0; i < stypePlugs.length(); i++) {
                            stypePlug = (JSONObject) stypePlugs.get(i);
                            plugMap = new HashMap<>();
                            plugMap.put(PanelDataKey.ID, DDJSONUtil.getString(stypePlug, "id"));
                            plugMap.put(PanelDataKey.NAME, DDJSONUtil.getString(stypePlug, "name"));
                            plugMap.put(PanelDataKey.SEND_ID, DDJSONUtil.getString(stypePlug, "sendid"));
                            plugList.add(plugMap);
                        }
                    }
                }
            }

            resultMap.put(PanelDataKey.RemoteControl.PLUG_LIST, plugList);
            return resultMap;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseWirelessSirenResult");
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 解析JsonObject为String数据
     *
     * @param responseBody 配件数据
     */
    public static String parseCustomizeSmartPlugsStr(@NotNull ResponseBody responseBody) {
        try {
            JSONObject jsonObject = new JSONObject(responseBody.string());
            return PanelSecretUtil.getReverSC(String.valueOf(jsonObject.get(NetKeyConstants.NET_KEY_RESULT)));
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseWirelessSirenResult");
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 解析JsonObject为Map数据
     * <p>
     * 安防状态
     * "0":{
     * "scenary":"SecurityCMD",
     * "cmd": "TASK_ARM"
     * }
     * <p>
     * // 门铃
     * "0":{
     * "scenary":"Doorbell",
     * "pluginid":"" // string, 门铃id
     * "sendid":"",  // string，门铃sendid
     * "stype":"",   // string，门铃stype
     * "name":"",    // string，门铃名称
     * "volume":,    // int 门铃音量
     * "music":,     // int 门铃歌曲
     * }
     * <p>
     * // 涂鸦灯泡
     * "0":{
     * "scenary":"Bulb",
     * "pluginid":"" // string, 涂鸦id
     * "name":"",    // string，名称
     * "action":,    // int 0:开 1:关 2:反转
     * }
     * <p>
     * // 涂鸦插座
     * "0":{
     * "scenary":"Plug",
     * "pluginid":"" // string, 涂鸦id
     * "name":"",    // string，名称
     * "action":,    // int 0:开 1:关 2:反转
     * }
     * <p>
     * // 自研插座
     * "0":{
     * "scenary":"Plug",
     * "pluginid":"" // string, 门铃id
     * "sendid":"",  // string，门铃的sendid
     * "stype":"",   // string，门铃的stype
     * "name":"",    // string，名称
     * "action":,    // int 0:开 1:关 2:反转
     * }
     *
     * @param responseBody 配件数据
     */
    public static Map<String, Object> parseSmartButtonConfig(@NotNull ResponseBody responseBody) {
        try {
            Map<String, Object> resultMap = new HashMap<>();
            JSONObject jsonObject = new JSONObject(responseBody.string());
            String result = PanelSecretUtil.getReverSC(String.valueOf(jsonObject.get("Result")));
            JSONObject actionSource = new JSONObject(result);
            JSONObject actionJson;

            Map<String, Object> actionItemMap;
            for (String action : PanelConstant.SmartButton.SERVICE_ACTIONS) {
                if (actionSource.has(action)) {
                    try {
                        actionJson = actionSource.getJSONObject(action);
                        actionItemMap = new HashMap<>();
                        actionItemMap.put(PanelDataKey.SmartButton.SCENARY, DDJSONUtil.getString(actionJson, NetKeyConstants.NET_KEY_SCENARY));
                        if (DDJSONUtil.has(actionJson, NetKeyConstants.NET_KEY_CMD)) {
                            actionItemMap.put(PanelDataKey.SmartButton.CMD, DDJSONUtil.getString(actionJson, NetKeyConstants.NET_KEY_CMD));
                        }
                        if (DDJSONUtil.has(actionJson, NetKeyConstants.NET_KEY_MUSIC)) {
                            actionItemMap.put(PanelDataKey.SmartButton.MUSIC, DDJSONUtil.getInt(actionJson, NetKeyConstants.NET_KEY_MUSIC));
                        }
                        if (DDJSONUtil.has(actionJson, NetKeyConstants.NET_KEY_VOLUME)) {
                            actionItemMap.put(PanelDataKey.SmartButton.VOLUME, DDJSONUtil.getInt(actionJson, NetKeyConstants.NET_KEY_MUSIC));
                        }
                        if (DDJSONUtil.has(actionJson, NetKeyConstants.NET_KEY_PLUGIN_ID)) {
                            actionItemMap.put(PanelDataKey.ID, DDJSONUtil.getString(actionJson, NetKeyConstants.NET_KEY_PLUGIN_ID));
                        }
                        if (DDJSONUtil.has(actionJson, NetKeyConstants.NET_KEY_NAME)) {
                            actionItemMap.put(PanelDataKey.NAME, DDJSONUtil.getString(actionJson, NetKeyConstants.NET_KEY_NAME));
                        }
                        if (DDJSONUtil.has(actionJson, NetKeyConstants.NET_KEY_SEND_ID)) {
                            actionItemMap.put(PanelDataKey.SEND_ID, DDJSONUtil.getString(actionJson, NetKeyConstants.NET_KEY_SEND_ID));
                        }
                        if (DDJSONUtil.has(actionJson, NetKeyConstants.NET_KEY_ACTION)) {
                            actionItemMap.put(PanelDataKey.SmartButton.ACTION, DDJSONUtil.getString(actionJson, NetKeyConstants.NET_KEY_ACTION));
                        }
                        if (DDJSONUtil.has(actionJson, NetKeyConstants.NET_KEY_S_TYPE)) {
                            actionItemMap.put(PanelDataKey.S_TYPE, DDJSONUtil.getString(actionJson, NetKeyConstants.NET_KEY_S_TYPE));
                        }
                        if (DDJSONUtil.has(actionJson, NetKeyConstants.NET_KEY_D_TYPE)) {
                            actionItemMap.put(PanelDataKey.D_TYPE, DDJSONUtil.getInt(actionJson, NetKeyConstants.NET_KEY_D_TYPE));
                        }
                        resultMap.put(action, actionItemMap);
                    } catch (JSONException e) {
                        DDLog.e(TAG, "Parse action item ERROR.");
                        e.printStackTrace();
                    }
                }
            }

            return resultMap;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseWirelessSirenResult");
            e.printStackTrace();
            return null;
        }
    }

    public static String parseSmartButtonConfigStr(@NotNull ResponseBody responseBody) {
        try {
            JSONObject jsonObject = new JSONObject(responseBody.string());
            return PanelSecretUtil.getReverSC(String.valueOf(jsonObject.get("Result")));
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseWirelessSirenResult");
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 解析JsonObject为Map数据
     *
     * @param responseBody 配件数据
     */
    public static Map<String, Object> parseCareModeData(@NotNull ResponseBody responseBody) {
        try {
            Map<String, Object> resultMap = new HashMap<>();
            JSONObject jsonObject = new JSONObject(responseBody.string());
            String result = PanelSecretUtil.getReverSC(String.valueOf(jsonObject.get(NetKeyConstants.NET_KEY_RESULT)));
            JSONObject careMoreJsonData = new JSONObject(result);

            Map<String, Object> itemMap;
            // CareMode配置
            if (DDJSONUtil.has(careMoreJsonData, NetKeyConstants.NET_KEY_CARE__MODE)) {
                JSONObject careMode = DDJSONUtil.getJSONObject(careMoreJsonData, NetKeyConstants.NET_KEY_CARE__MODE);
                itemMap = new HashMap<>();
                if (DDJSONUtil.has(careMode, NetKeyConstants.NET_KEY_CARE__MODE)) {
                    itemMap.put(PanelDataKey.CareMode.CARE_MODE, DDJSONUtil.getBoolean(careMode, NetKeyConstants.NET_KEY_CARE__MODE));
                }
                if (DDJSONUtil.has(careMode, NetKeyConstants.NET_KEY_ALARM__DELAY__TIME)) {
                    itemMap.put(PanelDataKey.CareMode.ALARM__DELAY__TIME, DDJSONUtil.getInt(careMode, NetKeyConstants.NET_KEY_ALARM__DELAY__TIME));
                }
                if (DDJSONUtil.has(careMode, NetKeyConstants.NET_KEY_NO__ACTION__TIME)) {
                    itemMap.put(PanelDataKey.CareMode.NO__ACTION__TIME, DDJSONUtil.getInt(careMode, NetKeyConstants.NET_KEY_NO__ACTION__TIME));
                }
                resultMap.put(PanelDataKey.CareMode.CARE_MODE_CONFIG, itemMap);
            }

            // 配件列表
            if (DDJSONUtil.has(careMoreJsonData, NetKeyConstants.NET_KEY_PLUGIN__INFO)) {
                JSONObject pluginInfoJObj = DDJSONUtil.getJSONObject(careMoreJsonData, NetKeyConstants.NET_KEY_PLUGIN__INFO);
                Map<String, Object> pluginListMap = new HashMap<>();
                ArrayList<Map<String, Object>> pluginList;

                JSONArray doors = DDJSONUtil.getJSONarray(pluginInfoJObj, NetKeyConstants.NET_KEY_DOOR__WINDOW);
                if (doors != null && doors.length() > 0) {
                    pluginList = new ArrayList<>();
                    for (int i = 0; i < doors.length(); i++) {
                        itemMap = parseCareModePluginData(doors.getJSONObject(i));
                        if (null != itemMap) {
                            pluginList.add(itemMap);
                        }
                    }
                    if (0 < pluginList.size()) {
                        pluginListMap.put(PanelDataKey.CareMode.DOOR_WINDOW, pluginList);
                    }
                }

                JSONArray pir = DDJSONUtil.getJSONarray(pluginInfoJObj, NetKeyConstants.NET_KEY_PIR);
                if (pir != null && pir.length() > 0) {
                    pluginList = new ArrayList<>();
                    for (int i = 0; i < pir.length(); i++) {
                        itemMap = parseCareModePluginData(pir.getJSONObject(i));
                        if (null != itemMap) {
                            pluginList.add(itemMap);
                        }
                    }
                    if (0 < pluginList.size()) {
                        pluginListMap.put(PanelDataKey.CareMode.PIR, pluginList);
                    }
                }

                JSONArray smartPlug = DDJSONUtil.getJSONarray(pluginInfoJObj, NetKeyConstants.NET_KEY_SMART__PLUG);
                if (smartPlug != null && smartPlug.length() > 0) {
                    pluginList = new ArrayList<>();
                    for (int i = 0; i < smartPlug.length(); i++) {
                        itemMap = parseCareModePluginData(smartPlug.getJSONObject(i));
                        if (null != itemMap) {
                            pluginList.add(itemMap);
                        }
                    }
                    if (0 < pluginList.size()) {
                        pluginListMap.put(PanelDataKey.CareMode.SMART_PLUG, pluginList);
                    }
                }

                JSONArray vibration = DDJSONUtil.getJSONarray(pluginInfoJObj, NetKeyConstants.NET_KEY_VIBRATION);
                if (vibration != null && vibration.length() > 0) {
                    pluginList = new ArrayList<>();
                    for (int i = 0; i < vibration.length(); i++) {
                        itemMap = parseCareModePluginData(vibration.getJSONObject(i));
                        if (null != itemMap) {
                            pluginList.add(itemMap);
                        }
                    }
                    if (0 < pluginList.size()) {
                        pluginListMap.put(PanelDataKey.CareMode.VIBRATION, pluginList);
                    }
                }
                resultMap.put(PanelDataKey.CareMode.PLUGIN_INFO, pluginListMap);
            }

            return resultMap;
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseWirelessSirenResult");
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 解析JsonObject为Map数据
     *
     * @param responseBody 配件数据
     */
    public static String parseCareModeDataStr(@NotNull ResponseBody responseBody) {
        try {
            JSONObject jsonObject = new JSONObject(responseBody.string());
            return PanelSecretUtil.getReverSC(String.valueOf(jsonObject.get(NetKeyConstants.NET_KEY_RESULT)));
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseWirelessSirenResult");
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 解析JsonObject为CareMode通用的配件信息
     *
     * @param pluginJObj 配件数据
     */
    public static Map<String, Object> parseCareModePluginData(@NotNull JSONObject pluginJObj) {
        Map<String, Object> pluginMap = new HashMap<>();
        try {
            if (DDJSONUtil.has(pluginJObj, NetKeyConstants.NET_KEY_ID)) {
                pluginMap.put(PanelDataKey.ID, DDJSONUtil.getString(pluginJObj, NetKeyConstants.NET_KEY_ID));
            }
            if (DDJSONUtil.has(pluginJObj, NetKeyConstants.NET_KEY_NAME)) {
                pluginMap.put(PanelDataKey.NAME, DDJSONUtil.getString(pluginJObj, NetKeyConstants.NET_KEY_NAME));
            }
            if (DDJSONUtil.has(pluginJObj, NetKeyConstants.NET_KEY_CATEGORY)) {
                pluginMap.put(PanelDataKey.CATEGORY, DDJSONUtil.getInt(pluginJObj, NetKeyConstants.NET_KEY_CATEGORY));
            }
            if (DDJSONUtil.has(pluginJObj, NetKeyConstants.NET_KEY_SUB__CATEGORY)) {
                pluginMap.put(PanelDataKey.SUBCATEGORY, DDJSONUtil.getString(pluginJObj, NetKeyConstants.NET_KEY_SUB__CATEGORY));
            }
            if (DDJSONUtil.has(pluginJObj, NetKeyConstants.NET_KEY_TIME)) {
                pluginMap.put(PanelDataKey.TIME_COMMON, DDJSONUtil.getLong(pluginJObj, NetKeyConstants.NET_KEY_TIME));
            }
            if (DDJSONUtil.has(pluginJObj, NetKeyConstants.NET_KEY_SEND_ID)) {
                pluginMap.put(PanelDataKey.SEND_ID, DDJSONUtil.getString(pluginJObj, NetKeyConstants.NET_KEY_SEND_ID));
            }
            if (DDJSONUtil.has(pluginJObj, NetKeyConstants.NET_KEY_S_TYPE)) {
                pluginMap.put(PanelDataKey.S_TYPE, DDJSONUtil.getString(pluginJObj, NetKeyConstants.NET_KEY_S_TYPE));
            }
            if (DDJSONUtil.has(pluginJObj, NetKeyConstants.NET_KEY_DECODE__ID)) {
                pluginMap.put(PanelDataKey.DECODE_ID, DDJSONUtil.getString(pluginJObj, NetKeyConstants.NET_KEY_DECODE__ID));
            }
            if (DDJSONUtil.has(pluginJObj, NetKeyConstants.NET_KEY_CARE__MODE)) {
                pluginMap.put(PanelDataKey.CareMode.CARE_MODE, DDJSONUtil.getBoolean(pluginJObj, NetKeyConstants.NET_KEY_CARE__MODE));
            }
        } catch (Exception e) {
            DDLog.e(TAG, "Error on: parseWirelessSirenResult");
            e.printStackTrace();
        }
        return pluginMap.keySet().size() > 0 ? pluginMap : null;
    }

    public static ArrayList<Map<String, Object>> parseCmsData(CmsProtocolEntry body) {
        ArrayList<Map<String, Object>> arrayList = new ArrayList<>();
        if (null != body && null != body.getResult() && 0 < body.getResult().size()) {
            Map<String, Object> tempMap;
            CmsProtocolModel.CmsProtocolInfo protocolInfo;
            for (CmsProtocolModel cmsProtocolModel : body.getResult()) {
                tempMap = new HashMap<>();
                tempMap.put(PanelDataKey.Cms.PROTOCOL_NAME, cmsProtocolModel.getProtocolName());
                protocolInfo = cmsProtocolModel.getInfo();
                if (null != protocolInfo) {
                    tempMap.put(PanelDataKey.Cms.PRIMARY_IP, protocolInfo.getPrimary_ip());
                    tempMap.put(PanelDataKey.Cms.PRIMARY_PORT, protocolInfo.getPrimary_port());
                    tempMap.put(PanelDataKey.Cms.SECONDARY_IP, protocolInfo.getSecondary_ip());
                    tempMap.put(PanelDataKey.Cms.SECONDARY_PORT, protocolInfo.getSecondary_port());
                    tempMap.put(PanelDataKey.Cms.ACCOUNT_NUMBER, protocolInfo.getAccount_number());
                    tempMap.put(PanelDataKey.Cms.ENCRYPTION, protocolInfo.isEncryption());
                    tempMap.put(PanelDataKey.Cms.ENCRYPTION_KEY, protocolInfo.getEncryption_key());
                    tempMap.put(PanelDataKey.Cms.NETWORK, protocolInfo.getNetwork());
                }
                arrayList.add(tempMap);
            }
        }
        return arrayList;
    }

    /**
     * 解析EventList的数据
     */
    public static List<Map<String, Object>> parseEventList(EventListEntry body) {
        if (null == body || null == body.getResult()) {
            return null;
        }

        ArrayList<Map<String, Object>> eventList = new ArrayList<>();
        if (0 < body.getResult().size()) {
            Map<String, Object> eventMap, dataMap;
            EventListBean.Data eventData;
            for (EventListBean eventBean : body.getResult()) {
                eventMap = new HashMap<>();
                eventMap.put(PanelDataKey.EventList.USER, eventBean.getUser());
                eventMap.put(PanelDataKey.EventList.CMD_NAME, eventBean.getCmdname());
                eventMap.put(PanelDataKey.EventList.PHOTO, eventBean.getPhoto());
                eventMap.put(PanelDataKey.EventList.TYPE, eventBean.getType());
                eventMap.put(PanelDataKey.EventList.TIME, eventBean.getTime());
                eventMap.put(PanelDataKey.EventList.DURATION, eventBean.getDuration());
                eventMap.put(PanelDataKey.EventList.MESSAGE_ID, eventBean.getMessageid());
                eventMap.put(PanelDataKey.EventList.RESULT, eventBean.getResult());
                eventMap.put(PanelDataKey.EventList.CMD_TYPE, eventBean.getCmdType());
                eventMap.put(PanelDataKey.EventList.PLUGIN_ID, eventBean.getPluginid());
                eventMap.put(PanelDataKey.EventList.CATEGORY, eventBean.getCategory());
                eventMap.put(PanelDataKey.EventList.SUBCATEGORY, eventBean.getSubcategory());

                eventData = eventBean.getData();
                if (null != eventData) {
                    dataMap = new HashMap<>();
                    dataMap.put(PanelDataKey.EventList.UID, eventData.getUid());
                    dataMap.put(PanelDataKey.EventList.NEW_PERMISSION, eventData.getNewpermission());
                    dataMap.put(PanelDataKey.EventList.OLD_PERMISSION, eventData.getOldpermission());
                    dataMap.put(PanelDataKey.EventList.POWER_STATUS, eventData.isPowerstatus());
                    eventMap.put(PanelDataKey.EventList.DATA, dataMap);
                }
                eventList.add(eventMap);
            }
        }
        return eventList;
    }
}
