package com.dinsafer.panel.util;

/**
 * 数据缓存key
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/21 6:12 PM
 */
public class PanelDBKey {

    private static final String CURRENT_DEVICE_INFO = "current_device_info";
    private static final String DEVICE_PLUGIN_INFO = "device_plugin_info";
    private static final String DEVICE_PLUGIN_QUANTITY = "device_plugin_quantity";

    /**
     * 获取保存主机信息的KEY
     */
    public static String getPanelInfoCacheKey(String deviceId) {
        return CURRENT_DEVICE_INFO + "-" + deviceId;
    }

    /**
     * 获取保存主页配件信息的KEY
     */
    public static String getHomePluginInfoCacheKey(String deviceId) {
        return DEVICE_PLUGIN_INFO + "-" + deviceId;
    }

    /**
     * 获取保存主机配件数量
     */
    public static String getPanelPluginQuantityCacheKey(String deviceId) {
        return DEVICE_PLUGIN_QUANTITY + "-" + deviceId;
    }
}
