package com.dinsafer.panel.operate.net;

import android.text.TextUtils;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.http.HttpHelper;
import com.dinsafer.dincore.user.bean.LogoutEvent;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.operate.PanelOperatorConstant;
import com.dinsafer.panel.operate.bean.EventListBean;
import com.dinsafer.panel.operate.bean.UnCloseDoorEntry;
import com.dinsafer.panel.operate.bean.event.DeviceCmdAckEvent;
import com.dinsafer.panel.operate.bean.event.DeviceEventListEvent;
import com.dinsafer.panel.operate.bean.event.DeviceResultEvent;
import com.dinsafer.panel.operate.bean.event.DeviceSimStatueEvent;
import com.dinsafer.panel.operate.bean.event.EventListDataFixTime;
import com.dinsafer.panel.operate.bean.event.OfflineEvent;
import com.dinsafer.panel.operate.bean.event.PingUpdataEvent;
import com.dinsafer.panel.operate.bean.event.ShowBlockToastEvent;
import com.dinsafer.panel.operate.bean.event.UserNetworkEvent;
import com.dinsafer.panel.operate.bean.event.WebSocketEvent;
import com.dinsafer.panel.util.PanelSecretUtil;
import com.google.gson.Gson;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;

import okhttp3.Response;
import okhttp3.WebSocket;


/**
 * Created by Rinfon on 16/8/23.
 */
public class PanelConnectManager implements IPanelWebSocketCallBack {

    public static final String TAG = "PanelConnectManager";

    private final HashMap<String, String> mHasHandleMessageId = new HashMap<String, String>();
    private boolean isConnect = false;
    private boolean isDisConnectByServer = false;
    //    最后一次关闭的原因，错误原因
    private String lastClose = "";
    private PanelWebSocketManager mWebSocketManager;

    public PanelConnectManager() {
        DDLog.i(TAG, "Create new PanelConnectManager");
        mWebSocketManager = new PanelWebSocketManager(true, true, true,
                PanelManager.getInstance().getPanelOperator().getWsIp());
        mWebSocketManager.addCallBack(this);
    }

    @Override
    public void onOpen(WebSocket webSocket, Response response) {
        try {
            if (null == DinCore.getUserInstance().getUser()
                    || TextUtils.isEmpty(DinCore.getUserInstance().getUser().getToken())
                    || TextUtils.isEmpty(PanelManager.getInstance().getCurrentPanelToken())) {
                DDLog.i(TAG, "send ws text error,token is null");
//                CommonDataUtil.getInstance().logUser();
                return;
            }

            String message = DinCore.getUserInstance().getUser().getToken()
                    + "&" +
                    PanelManager.getInstance().getCurrentPanelToken()
                    + "_" + System.currentTimeMillis() * 1000;
            webSocket.send(new String(PanelSecretUtil.getSC(message)));
        } catch (Exception e) {
            e.printStackTrace();
            DDLog.e(TAG, "Unable to send messages: " + e.getMessage());
        }
    }


    public synchronized void connectWebSocket() {
        DDLog.i(TAG, "connectWebSocket PanelManager.getInstance().getCurrentDeviceInfo() != null:" +
                (PanelManager.getInstance().getCurrentPanelToken() != null));

        if (mWebSocketManager.getWebSocket() != null) {
            mWebSocketManager.stop();
        }

        isConnect = false;
        if (!TextUtils.isEmpty(PanelManager.getInstance().getCurrentPanelToken())) {
            startConnectWebSocket();
        }
    }

    private void startConnectWebSocket() {
        try {
            DDLog.i(TAG, "startConnectWebSocket");
            mWebSocketManager.start();
        } catch (Exception ex) {
            DDLog.e(TAG, "exception " + ex.getMessage());
            ex.printStackTrace();
        }
    }

    @Override
    public void onFailure(WebSocket webSocket, Throwable t, Response response) {
        //        不能关闭，否者不能进行重连，要在关闭app的时候才关闭
//        writeExecutor.shutdown();
//        服务器关闭eofexception
        if (isDisConnectByServer) {
            isDisConnectByServer = false;
            return;
        }

        isConnect = false;
        lastClose = t.getMessage();

        DDLog.e(TAG, "ws 意外断开，准备离线，" + t.getMessage());
        toSendOffline();
    }

    @Override
    public void onMessage(String messageStr) {
        DDLog.i(TAG, "MESSAGE: " + messageStr);
        if ("1".equals(messageStr)) {
            isConnect = true;
            EventBus.getDefault().post(new WebSocketEvent(WebSocketEvent.CONNET_SUCCESS));
        } else if ("-1".equals(messageStr)) {
            isDisConnectByServer = true;
            DDLog.i(TAG, "ws 接收-1，直接退出登录");
            EventBus.getDefault().post(new LogoutEvent());
        } else if ("-2".equals(messageStr)) {
            isConnect = false;
            DDLog.i(TAG, "ws 接收-2，准备离线");
            toSendOffline();
        } else {
            try {
                JSONObject jsonObject = new JSONObject(messageStr);
                final String currentAction = jsonObject.getString(PanelOperatorConstant.KEY.Action);
                if (PanelOperatorConstant.ACTION.EVENT_REVICE.equals(currentAction)) {
                    String result = jsonObject.getString(PanelOperatorConstant.KEY.RESULT);
                    result = PanelSecretUtil.getReverSC(result);
                    DDLog.d(TAG, "ack: " + result);
                    Gson gson = new Gson();
                    EventListBean deviceCmdEntry = gson.fromJson(result, EventListBean.class);
                    if (isCMDArmDisarmHomeArm(DDJSONUtil.getString(jsonObject, PanelOperatorConstant.KEY.CMD))) {
                        if (!mHasHandleMessageId.containsKey(deviceCmdEntry.getMessageid())) {
                            doRevice(deviceCmdEntry);
                            mHasHandleMessageId.put(deviceCmdEntry.getMessageid(), deviceCmdEntry.getCmdType());

                            String result1 = null;
                            if (!TextUtils.isEmpty(DDJSONUtil.getString(jsonObject, PanelOperatorConstant.KEY.RESULT))) {
                                result1 = PanelSecretUtil.getReverSC(jsonObject.getString("Result"));
                                if (HttpHelper.checkIsJsonObject(result1)) {
                                    JSONObject resultJson = new JSONObject(result1);
                                    String isStringValue = HttpHelper.checkIsOnlyGMSTimeAndData(resultJson);
                                    if (TextUtils.isEmpty(isStringValue)) {
                                        result1 = resultJson.toString();
                                    } else {
                                        result1 = isStringValue;
                                    }
                                }

                            }

                            JSONObject resultJson = new JSONObject(result1);
                            if (TextUtils.isEmpty(DDJSONUtil.getString(resultJson, "plugins"))) {
                                createResultEvent(jsonObject, deviceCmdEntry.getTime());
                            }
                        } else {
                            EventBus.getDefault().post(new EventListDataFixTime(deviceCmdEntry));
                        }
                    } else {
                        doRevice(deviceCmdEntry);
                    }
                } else if (PanelOperatorConstant.ACTION.EVENT_RESULT.equals(currentAction)) {
                    String cmdType = DDJSONUtil.getString(jsonObject, PanelOperatorConstant.KEY.CMD);
                    DeviceResultEvent event = new DeviceResultEvent(cmdType,
                            DDJSONUtil.getInt(jsonObject, PanelOperatorConstant.KEY.STATUS)
                            , DDJSONUtil.getString(jsonObject, PanelOperatorConstant.KEY.MESSAGE_ID));
                    if (isCMDArmDisarmHomeArm(cmdType)) {
                        String result = null;
                        if (!TextUtils.isEmpty(DDJSONUtil.getString(jsonObject, PanelOperatorConstant.KEY.RESULT))) {
                            result = PanelSecretUtil.getReverSC(jsonObject.getString("Result"));
                            DDLog.i(TAG, "ws:decode result:" + result);
                            if (HttpHelper.checkIsJsonObject(result)) {
                                JSONObject resultJson = new JSONObject(result);
                                String isStringValue = HttpHelper.checkIsOnlyGMSTimeAndData(cmdType, resultJson);
                                if (TextUtils.isEmpty(isStringValue)) {
                                    result = resultJson.toString();
                                } else {
                                    result = isStringValue;
                                }
                            }

                        }

                        event.setReslut(result);
                        JSONObject resultJson = new JSONObject(result);
                        if (PanelOperatorConstant.CMD.ARM_KEY.equals(cmdType)
                                || PanelOperatorConstant.CMD.HOMEARM_KEY.equals(cmdType)) {
                            handlerShowToast(cmdType, resultJson);
                        }
                        if (!mHasHandleMessageId.containsKey(
                                DDJSONUtil.getString(jsonObject, PanelOperatorConstant.KEY.MESSAGE_ID))) {
                            doResult(jsonObject, event);
                            mHasHandleMessageId.put(DDJSONUtil.getString(jsonObject, PanelOperatorConstant.KEY.MESSAGE_ID), cmdType);
                        } else if (DDJSONUtil.getJSONarray(resultJson, "plugins") != null
                                && DDJSONUtil.getJSONarray(resultJson, "plugins").length() > 0) {
                            /**
                             * 上面的if是对messageid进行过滤。如果是自己去arm就不做处理，
                             * 但是，现在增加一个逻辑：当ready to arm开启时，即使是自己的arm也要处理。
                             * 而区别就在于，如果plugins有数据，即要显示ready to arm 弹窗，即需要处理
                             */
                            Gson gson = new Gson();
                            UnCloseDoorEntry.ResultBean unCloseDoorEntry = gson.fromJson(result, UnCloseDoorEntry.ResultBean.class);
                            if (unCloseDoorEntry.getPlugins().size() > 0) {
                                doResult(jsonObject, event);
                                mHasHandleMessageId.put(DDJSONUtil.getString(jsonObject, PanelOperatorConstant.KEY.MESSAGE_ID), cmdType);
                            }
                        }
                    } else {
                        doResult(jsonObject, event);
                    }
                } else if (PanelOperatorConstant.ACTION.EVENT_PING.equals(currentAction)) {
                    String result = jsonObject.getString("Result");
                    result = PanelSecretUtil.getReverSC(result);
                    JSONObject jsonObject1 = new JSONObject(result);
                    PingUpdataEvent pingUpdataEvent = new PingUpdataEvent(jsonObject1.getBoolean("ischarge"),
                            jsonObject1.getInt("batterylevel"), jsonObject1.getInt("nettype"),
                            jsonObject1.getInt("wifi_rssi"),
                            jsonObject1.getString("ipaddr"));
                    pingUpdataEvent.setOriginalResult(result);
                    EventBus.getDefault().post(pingUpdataEvent);
                } else if (PanelOperatorConstant.ACTION.SIM_ACTION.equals(currentAction)) {
                    String result = jsonObject.getString("Result");
                    result = PanelSecretUtil.getReverSC(result);
                    try {
                        JSONObject resultJson = new JSONObject(result);
                        String isStringValue = HttpHelper.checkIsOnlyGMSTimeAndData(resultJson);
                        if (!TextUtils.isEmpty(isStringValue)) {
                            int resultCode = Integer.valueOf(result);
                            EventBus.getDefault().post(new DeviceSimStatueEvent(resultCode));
                        }

                    } catch (Exception ex) {
                        int resultCode = Integer.valueOf(result);
                        EventBus.getDefault().post(new DeviceSimStatueEvent(resultCode));
                    }
                } else if (PanelOperatorConstant.ACTION.OFFLINE.equals(currentAction)) {
                    toCloseWs();
                    toReconect();
                } else if (PanelOperatorConstant.ACTION.CMD_ACK.equals(currentAction)) {
                    String cmdType = DDJSONUtil.getString(jsonObject, PanelOperatorConstant.KEY.CMD);
                    DeviceCmdAckEvent event = new DeviceCmdAckEvent(cmdType,
                            DDJSONUtil.getInt(jsonObject, PanelOperatorConstant.KEY.STATUS)
                            , DDJSONUtil.getString(jsonObject, PanelOperatorConstant.KEY.MESSAGE_ID));
                    if (TextUtils.isEmpty(DDJSONUtil.getString(jsonObject, PanelOperatorConstant.KEY.RESULT))) {
                        event.setReslut("");
                    } else {
                        event.setReslut(PanelSecretUtil.getReverSC(DDJSONUtil.getString(jsonObject, PanelOperatorConstant.KEY.RESULT)));
                    }
                    EventBus.getDefault().post(event);
                }
            } catch (Exception e) {
                e.printStackTrace();
                DDLog.e(TAG, "收到未知ws信息，" +
                        "以前会弹网络错误弹窗，现在已注释,msg is：" + messageStr);
            }
        }
    }

    private void handlerShowToast(String cmdType, JSONObject result) {
        if (DDJSONUtil.getJSONarray(result, "plugins") != null
                && DDJSONUtil.getJSONarray(result, "plugins").length() > 0
                && DDJSONUtil.getBoolean(result, "force")) {
            return;
        }
        JSONArray block = DDJSONUtil.getJSONarray(result, "block");
        if (block == null) {
            EventBus.getDefault().post(new ShowBlockToastEvent(cmdType));
            return;
        }
        String blockStr = "";
        for (int i = 0; i < block.length(); i++) {
            try {
                blockStr = blockStr + block.get(i) + " ";
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        EventBus.getDefault().post(new ShowBlockToastEvent(cmdType, blockStr));
    }

    public void doRevice(EventListBean deviceCmdEntry) {
        DeviceEventListEvent deviceEventListEvent = new DeviceEventListEvent();
        deviceEventListEvent.setEntry(deviceCmdEntry);
        EventBus.getDefault().post(deviceEventListEvent);
    }

    public void doResult(JSONObject jsonObject, DeviceResultEvent event) throws JSONException {
        if (TextUtils.isEmpty(jsonObject.getString("Result"))) {
            event.setReslut("");
        } else {
            String result = PanelSecretUtil.getReverSC(jsonObject.getString("Result"));
            DDLog.i(TAG, "ws:decode result:" + result);
            String type = jsonObject.getString("Cmd");
            if (HttpHelper.checkIsJsonObject(result)) {
                JSONObject resultJson = new JSONObject(result);
                String isStringValue = HttpHelper.checkIsOnlyGMSTimeAndData(type, resultJson);
                if (TextUtils.isEmpty(isStringValue)) {
                    event.setReslut(resultJson.toString());
                } else {
                    event.setReslut(isStringValue);
                }
            } else {
                event.setReslut(result);
            }
        }

        EventBus.getDefault().post(event);
    }

    private boolean isCMDArmDisarmHomeArm(String type) {
        return PanelOperatorConstant.CMD.ARM_KEY.equals(type)
                || PanelOperatorConstant.CMD.DISARM_KEY.equals(type)
                || PanelOperatorConstant.CMD.HOMEARM_KEY.equals(type);
    }

    private void createResultEvent(JSONObject jsonObject, long gmtime) throws JSONException {
        String type = jsonObject.getString("Cmd");
        DeviceResultEvent event = new DeviceResultEvent(type, jsonObject.getInt("Status")
                , jsonObject.getString("MessageId"));
        JSONObject json = new JSONObject();
        json.put("gmtime", gmtime);
        event.setReslut(json.toString());
        EventBus.getDefault().post(event);
    }

    private void toReconect() {
        DDLog.d(TAG, "toReconect ");
        EventBus.getDefault().post(new OfflineEvent());
    }

    @Override
    public void onClosing(WebSocket webSocket, int code, String reason) {
        DDLog.d(TAG, "onClose: " + code + ", reason: " + reason);
        isConnect = false;
        lastClose = reason;
    }

    private synchronized void toSendOffline() {
        EventBus.getDefault().post(new UserNetworkEvent());
    }

    public boolean isConnect() {
        return isConnect;
    }

    public String getCloseReason() {
        return lastClose;
    }

    public void toCloseWs() {
        DDLog.d(TAG, "toCloseWs");
        mWebSocketManager.close();
    }

    public void destroyPanelConnection() {
        DDLog.d(TAG, "destroyPanelConnection");
        mWebSocketManager.removeCallBack(this);
        mWebSocketManager.close();
        mWebSocketManager.release();
    }


    public void addCallBack(IPanelWebSocketCallBack callBack) {
        if (null != mWebSocketManager) {
            mWebSocketManager.addCallBack(callBack);
        }
    }

    public void removeCallBack(IPanelWebSocketCallBack callBack) {
        if (null != mWebSocketManager) {
            mWebSocketManager.removeCallBack(callBack);
        }
    }

}
