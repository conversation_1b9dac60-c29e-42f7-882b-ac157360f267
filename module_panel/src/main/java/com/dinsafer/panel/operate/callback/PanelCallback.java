package com.dinsafer.panel.operate.callback;

/**
 * 主机回调接口
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/13 12:17 PM
 */
public class PanelCallback {

    /**
     * 主机状态回调
     */
    public interface PanelState {
        void onPanelNotBelongUser();

        void onPanelOffline();
    }

    /**
     * 获取首页配件数据结果回调
     */
    public interface GetHomePluginInfo {
        void onGetHomePlugin(boolean success);

        void onStartGetHomePlugin(String deviceId);
    }

    /**
     * 获取主机已添加的配件数据结果回调
     */
    public interface GetPluginQuantity {
        void onGetPanelPluginQuantity(boolean isSuccess);

        void onStartGetPanelPluginQuantity();
    }

    /**
     * 获取配件数据结果回调
     */
    public interface NetworkResult<T> {
        void onSuccess(T result);

        void onError(int errorCode, String errorMsg);
    }
}
