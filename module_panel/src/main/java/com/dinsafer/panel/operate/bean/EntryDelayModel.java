package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.util.List;

/**
 * Created by Rinfon on 16/8/19.
 */
@Keep
public class EntryDelayModel extends BaseHttpEntry {


    /**
     * Cmd :
     * Result : {"gmtime":23511234342234,"options":[0,10,20,30,40,50,60,70,80,90],"time":30,"plugins":[{"entrydelayenable":true,"id":"id_000388888","name":"iidd_11661","subcategory":"0B","time":1455956598408813139},{"entrydelayenable":true,"id":"id_0003888800","name":"iidd_11661","subcategory":"0B","time":1455956399408813139}],"thirdpartyplugins":[{"entrydelayenable":true,"id":"id_0003888800","name":"iidd_11661","subcategory":"0B","time":1455956399408813139}],"newaskplugin":[{"id":"","sendid":"ABCD","stype":"16","name":"newplugin","time":1455956399408813139,"entrydelayenable":true},{"id":"","sendid":"AB12","stype":"17","name":"new2","time":1455956399408813139,"entrydelayenable":true}]}
     */

    private String Cmd;
    private ResultBean Result;

    public String getCmd() {
        return Cmd;
    }

    public void setCmd(String Cmd) {
        this.Cmd = Cmd;
    }

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        /**
         * gmtime : 23511234342234
         * options : [0,10,20,30,40,50,60,70,80,90]
         * time : 30
         * plugins : [{"entrydelayenable":true,"id":"id_000388888","name":"iidd_11661","subcategory":"0B","time":1455956598408813139},{"entrydelayenable":true,"id":"id_0003888800","name":"iidd_11661","subcategory":"0B","time":1455956399408813139}]
         * thirdpartyplugins : [{"entrydelayenable":true,"id":"id_0003888800","name":"iidd_11661","subcategory":"0B","time":1455956399408813139}]
         * newaskplugin : [{"id":"","sendid":"ABCD","stype":"16","name":"newplugin","time":1455956399408813139,"entrydelayenable":true},{"id":"","sendid":"AB12","stype":"17","name":"new2","time":1455956399408813139,"entrydelayenable":true}]
         */

        private long gmtime;
        private int time;
        private List<Integer> options;
        private List<PluginsBean> plugins;
        private List<ThirdpartypluginsBean> thirdpartyplugins;
        private List<NewaskpluginBean> newaskplugin;
        private boolean entrydelaysound;

        public boolean isEntrydelaysound() {
            return entrydelaysound;
        }

        public void setEntrydelaysound(boolean entrydelaysound) {
            this.entrydelaysound = entrydelaysound;
        }

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        public int getTime() {
            return time;
        }

        public void setTime(int time) {
            this.time = time;
        }

        public List<Integer> getOptions() {
            return options;
        }

        public void setOptions(List<Integer> options) {
            this.options = options;
        }

        public List<PluginsBean> getPlugins() {
            return plugins;
        }

        public void setPlugins(List<PluginsBean> plugins) {
            this.plugins = plugins;
        }

        public List<ThirdpartypluginsBean> getThirdpartyplugins() {
            return thirdpartyplugins;
        }

        public void setThirdpartyplugins(List<ThirdpartypluginsBean> thirdpartyplugins) {
            this.thirdpartyplugins = thirdpartyplugins;
        }

        public List<NewaskpluginBean> getNewaskplugin() {
            return newaskplugin;
        }

        public void setNewaskplugin(List<NewaskpluginBean> newaskplugin) {
            this.newaskplugin = newaskplugin;
        }

        @Keep
        public static class PluginsBean {
            /**
             * entrydelayenable : true
             * id : id_000388888
             * name : iidd_11661
             * subcategory : 0B
             * time : 1455956598408813139
             */

            private boolean entrydelayenable;
            private String id;
            private String name;
            private String subcategory;
            private long time;

            public boolean isEntrydelayenable() {
                return entrydelayenable;
            }

            public void setEntrydelayenable(boolean entrydelayenable) {
                this.entrydelayenable = entrydelayenable;
            }

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getSubcategory() {
                return subcategory;
            }

            public void setSubcategory(String subcategory) {
                this.subcategory = subcategory;
            }

            public long getTime() {
                return time;
            }

            public void setTime(long time) {
                this.time = time;
            }
        }

        @Keep
        public static class ThirdpartypluginsBean {
            /**
             * entrydelayenable : true
             * id : id_0003888800
             * name : iidd_11661
             * subcategory : 0B
             * time : 1455956399408813139
             */

            private boolean entrydelayenable;
            private String id;
            private String name;
            private String subcategory;
            private long time;

            public boolean isEntrydelayenable() {
                return entrydelayenable;
            }

            public void setEntrydelayenable(boolean entrydelayenable) {
                this.entrydelayenable = entrydelayenable;
            }

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getSubcategory() {
                return subcategory;
            }

            public void setSubcategory(String subcategory) {
                this.subcategory = subcategory;
            }

            public long getTime() {
                return time;
            }

            public void setTime(long time) {
                this.time = time;
            }
        }

        @Keep
        public static class NewaskpluginBean {
            /**
             * id :
             * sendid : ABCD
             * stype : 16
             * name : newplugin
             * time : 1455956399408813139
             * entrydelayenable : true
             */

            private String id;
            private String sendid;
            private String stype;
            private String name;
            private long time;
            private boolean entrydelayenable;

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getSendid() {
                return sendid;
            }

            public void setSendid(String sendid) {
                this.sendid = sendid;
            }

            public String getStype() {
                return stype;
            }

            public void setStype(String stype) {
                this.stype = stype;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public long getTime() {
                return time;
            }

            public void setTime(long time) {
                this.time = time;
            }

            public boolean isEntrydelayenable() {
                return entrydelayenable;
            }

            public void setEntrydelayenable(boolean entrydelayenable) {
                this.entrydelayenable = entrydelayenable;
            }
        }
    }
}
