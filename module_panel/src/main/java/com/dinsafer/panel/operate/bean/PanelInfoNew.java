package com.dinsafer.panel.operate.bean;

import android.content.Context;
import androidx.annotation.Keep;

import com.dinsafer.panel.R;

import java.io.Serializable;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/11/23 16:17
 */
@Keep
public class PanelInfoNew implements Serializable {
    /**
     * 网络类型
     */
    public static final int NET_TYPE_LAN = 0;
    public static final int NET_TYPE_WIFI = 1;
    public static final int NET_TYPE_4G = 2;

    private String deviceId; // 数据关联的主机ID
    private boolean isDeviceOffline = true; // 主机是否离线

    private int arm_state;
    private int battery_level;
    private boolean charge;
    private String device_id;
    private long entrydelay;
    private long exitdelay;
    private String ip;
    private int network;
    private boolean online;
    private int sim;
    private boolean sos;
    private String ssid;
    private String timezone;
    private boolean update;
    private String wifi_mac_addr;
    private int wifi_rssi;
    private boolean has_device_text_set;
    private String firmware_version;

    private long last_no_action_time;
    private long alarm_delay_time;
    private long no_action_time;


    /**
     * 主机SIM卡PIN码是否错误
     *
     * @return true：主机PIN码错误
     */
    public boolean isSimPinError() {
        return 3 == getSim();
    }

    /**
     * sim卡故障
     *
     * @return
     */
    public boolean isSimWrong() {
        return getSim() != 0 && getSim() != 1;
    }

    public int getSimStatusText() {
        if (getSim() == 0) {
            return R.string.sim_not_exit;
        } else if (getSim() == 1) {
            return R.string.sim_normal;
        } else {
            return R.string.sim_wrong;
        }
    }

    public boolean isSimNormal() {
        return 1 == getSim();
    }

    public String geNetworkName(Context context) {
        if (getNetwork() == NET_TYPE_LAN) {
            return context.getResources().getString(R.string.advanced_setting_net_cable);
        } else if (getNetwork() == NET_TYPE_4G) {
            return context.getResources().getString(R.string.ap_step_wifi_connect_result_4g);
        } else {
            return getSsid();
        }
    }

    public int getBatteryStatusText() {
        if (isCharge()) {
            return R.string.power_connected;
        } else {
            return R.string.power_disconnected;
        }
    }


    public int getArm_state() {
        return arm_state;
    }

    public void setArm_state(int arm_state) {
        this.arm_state = arm_state;
    }

    public int getBattery_level() {
        return battery_level;
    }

    public void setBattery_level(int battery_level) {
        this.battery_level = battery_level;
    }

    public boolean isCharge() {
        return charge;
    }

    public void setCharge(boolean charge) {
        this.charge = charge;
    }

    public String getDevice_id() {
        return device_id;
    }

    public void setDevice_id(String device_id) {
        this.device_id = device_id;
    }

    public long getEntrydelay() {
        return entrydelay;
    }

    public void setEntrydelay(long entrydelay) {
        this.entrydelay = entrydelay;
    }

    public long getExitdelay() {
        return exitdelay;
    }

    public void setExitdelay(long exitdelay) {
        this.exitdelay = exitdelay;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public int getNetwork() {
        return network;
    }

    public void setNetwork(int network) {
        this.network = network;
    }

    public boolean isOnline() {
        return online;
    }

    public void setOnline(boolean online) {
        this.online = online;
    }

    public int getSim() {
        return sim;
    }

    public void setSim(int sim) {
        this.sim = sim;
    }

    public boolean isSos() {
        return sos;
    }

    public void setSos(boolean sos) {
        this.sos = sos;
    }

    public String getSsid() {
        return ssid;
    }

    public void setSsid(String ssid) {
        this.ssid = ssid;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public boolean isUpdate() {
        return update;
    }

    public void setUpdate(boolean update) {
        this.update = update;
    }

    public String getWifi_mac_addr() {
        return wifi_mac_addr;
    }

    public void setWifi_mac_addr(String wifi_mac_addr) {
        this.wifi_mac_addr = wifi_mac_addr;
    }

    public int getWifi_rssi() {
        return wifi_rssi;
    }

    public void setWifi_rssi(int wifi_rssi) {
        this.wifi_rssi = wifi_rssi;
    }

    public boolean isHas_device_text_set() {
        return has_device_text_set;
    }

    public void setHas_device_text_set(boolean has_device_text_set) {
        this.has_device_text_set = has_device_text_set;
    }

    public String getFirmware_version() {
        return firmware_version;
    }

    public void setFirmware_version(String firmware_version) {
        this.firmware_version = firmware_version;
    }

    public long getLast_no_action_time() {
        return last_no_action_time;
    }

    public void setLast_no_action_time(long last_no_action_time) {
        this.last_no_action_time = last_no_action_time;
    }

    public long getAlarm_delay_time() {
        return alarm_delay_time;
    }

    public void setAlarm_delay_time(long alarm_delay_time) {
        this.alarm_delay_time = alarm_delay_time;
    }

    public long getNo_action_time() {
        return no_action_time;
    }

    public void setNo_action_time(long no_action_time) {
        this.no_action_time = no_action_time;
    }
}
