package com.dinsafer.panel.operate.bean.param;

import com.dinsafer.panel.operate.PanelOperatorConstant;

import java.util.ArrayList;

/**
 * 定制在家布防或延时报警设置请求参数
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/18 2:21 PM
 */
public class CustomizeHomeArmParams {
    private static final String KEY_HOME_ARM = "homearmenable";
    private static final String KEY_ENTRY_DELAY = "entrydelayenable";

    private ArrayList<PluginInfo> officialPlugins;
    private ArrayList<AskPluginInfo> askPlugins;
    private ArrayList<PluginInfo> thirdPartPlugins;
    private String messageId;
    private int entryDelayTime = -1;
    private boolean entryDelaySoundEnable;
    private final String cmd;
    private final String stateConfirmKey;
    private String pluginsStr, thirdPartPluginsStr, askPluginsStr;

    public ArrayList<PluginInfo> getOfficialPlugins() {
        return officialPlugins;
    }

    public ArrayList<AskPluginInfo> getAskPlugins() {
        return askPlugins;
    }

    public ArrayList<PluginInfo> getThirdPartPlugins() {
        return thirdPartPlugins;
    }

    public int getEntryDelayTime() {
        return entryDelayTime;
    }

    public boolean isEntryDelaySoundEnable() {
        return entryDelaySoundEnable;
    }

    public String getCmd() {
        return cmd;
    }

    public String getStateConfirmKey() {
        return stateConfirmKey;
    }

    public String getMessageId() {
        return messageId;
    }

    public String getPluginsStr() {
        return pluginsStr;
    }

    public String getThirdPartPluginsStr() {
        return thirdPartPluginsStr;
    }

    public String getAskPluginsStr() {
        return askPluginsStr;
    }

    @Override
    public String toString() {
        return "CustomizeHomeArmParams{" +
                "officialPlugins=" + officialPlugins +
                ", askPlugins=" + askPlugins +
                ", thirdPartPlugins=" + thirdPartPlugins +
                ", messageId='" + messageId + '\'' +
                ", entryDelayTime=" + entryDelayTime +
                ", entryDelayEnable=" + entryDelaySoundEnable +
                ", cmd='" + cmd + '\'' +
                ", stateConfirmKey='" + stateConfirmKey + '\'' +
                '}';
    }

    /**
     * 定制在家布防设置数据初始化
     */
    public CustomizeHomeArmParams(String messageId, ArrayList<PluginInfo> officialPlugins, ArrayList<AskPluginInfo> askPlugins,
                                  ArrayList<PluginInfo> thirdPartPlugins) {
        cmd = PanelOperatorConstant.CMD.SET_HOMEARM;
        stateConfirmKey = KEY_HOME_ARM;
        this.messageId = messageId;
        this.officialPlugins = officialPlugins;
        this.askPlugins = askPlugins;
        this.thirdPartPlugins = thirdPartPlugins;
    }

    /**
     * 延时报警设置数据初始化
     */
    public CustomizeHomeArmParams(String messageId, ArrayList<PluginInfo> officialPlugins, ArrayList<AskPluginInfo> askPlugins,
                                  ArrayList<PluginInfo> thirdPartPlugins, int entryDelayTime, boolean entryDelaySoundEnable) {
        cmd = PanelOperatorConstant.CMD.SET_ENTRYDELAY;
        stateConfirmKey = KEY_ENTRY_DELAY;
        this.messageId = messageId;
        this.officialPlugins = officialPlugins;
        this.askPlugins = askPlugins;
        this.thirdPartPlugins = thirdPartPlugins;
        this.entryDelayTime = entryDelayTime;
        this.entryDelaySoundEnable = entryDelaySoundEnable;
    }


    /**
     * 定制在家布防设置数据初始化
     */
    public CustomizeHomeArmParams(String messageId, String officialPlugins, String askPlugins,
                                  String thirdPartPlugins) {
        cmd = PanelOperatorConstant.CMD.SET_HOMEARM;
        stateConfirmKey = KEY_HOME_ARM;
        this.messageId = messageId;
        this.pluginsStr = officialPlugins;
        this.askPluginsStr = askPlugins;
        this.thirdPartPluginsStr = thirdPartPlugins;
    }

    /**
     * 延时报警设置数据初始化
     */
    public CustomizeHomeArmParams(String messageId, String officialPlugins, String askPlugins,
                                  String thirdPartPlugins, int entryDelayTime, boolean entryDelaySoundEnable) {
        cmd = PanelOperatorConstant.CMD.SET_ENTRYDELAY;
        stateConfirmKey = KEY_ENTRY_DELAY;
        this.messageId = messageId;
        this.pluginsStr = officialPlugins;
        this.askPluginsStr = askPlugins;
        this.thirdPartPluginsStr = thirdPartPlugins;
        this.entryDelayTime = entryDelayTime;
        this.entryDelaySoundEnable = entryDelaySoundEnable;
    }

    /**
     * 普通配件信息
     */
    public static class PluginInfo {
        private final String id;
        private final boolean isOpened;

        public PluginInfo(String id, boolean isOpened) {
            this.id = id;
            this.isOpened = isOpened;
        }

        public String getId() {
            return id;
        }

        public boolean isOpened() {
            return isOpened;
        }

        @Override
        public String toString() {
            return "PluginInfo{" +
                    "id='" + id + '\'' +
                    ", isOpened=" + isOpened +
                    '}';
        }
    }

    /**
     * Ask配件信息
     */
    public static class AskPluginInfo {
        private final String sType;
        private final String sendId;
        private final boolean isOpened;

        public AskPluginInfo(String sType, String sendId, boolean isOpened) {
            this.sType = sType;
            this.sendId = sendId;
            this.isOpened = isOpened;
        }

        public String getsType() {
            return sType;
        }

        public String getSendId() {
            return sendId;
        }

        public boolean isOpened() {
            return isOpened;
        }

        @Override
        public String toString() {
            return "AskPluginInfo{" +
                    "sType='" + sType + '\'' +
                    ", sendId='" + sendId + '\'' +
                    ", isOpened=" + isOpened +
                    '}';
        }
    }
}
