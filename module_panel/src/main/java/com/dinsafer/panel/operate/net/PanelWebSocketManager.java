package com.dinsafer.panel.operate.net;

import com.dinsafer.dincore.http.MyDns;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.PanelManager;

import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLSession;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;
import okhttp3.internal.ws.RealWebSocket;
import okhttp3.internal.ws.WebSocketReader;
import okhttp3.internal.ws.WebSocketWriter;
import okio.ByteString;

/**
 * 主机websocket连接管理
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/12 3:23 PM
 */
public class PanelWebSocketManager extends WebSocketListener implements WebSocketReaderFrameCallbackWrapper.OnReadPong {
    private static final String TAG = PanelWebSocketManager.class.getSimpleName();

    private static final int PING_INTERVAL_SECONDS = 10; // Ping的时间间隔
    private static final int PING_MAX_RESEND_COUNT = 3; // Ping重发次数
    private static final int FAILED_MAX_RECONNECT_COUNT = 3; // 重连次数

    private final OkHttpClient client;
    private WebSocket mWebSocket;
    private boolean isUserClose;

    private ScheduledThreadPoolExecutor mService;
    private WebSocketWriter mWriter;
    private int mPongTimeoutCount, mReconnectCount;
    private boolean mAwaitingPong = false;

    private final boolean mSendPingPong; // 是否发送心跳包
    private final boolean mAutoReconnect; // 是否错误重连

    Request request;

    List<IPanelWebSocketCallBack> msgCallBackList = new ArrayList<>();

    String url;

    public PanelWebSocketManager(boolean setDns, boolean setPingPong, boolean autoReconnect, String url) {
        mSendPingPong = setPingPong;
        mAutoReconnect = autoReconnect;
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
                .readTimeout(0, TimeUnit.MILLISECONDS)
                .hostnameVerifier(new HostnameVerifier() {
                    @Override
                    public boolean verify(String hostname, SSLSession session) {
                        return true;
                    }
                });
        if (setDns) {
            builder.dns(new MyDns());
        }

        this.url = url;
        client = builder.build();
        Request.Builder rb = new Request.Builder()
                .url(url);
        if (setDns) {
            rb.addHeader("Host", PanelManager.getInstance().getPanelOperator().getWsDomain());
        }
        request = rb.build();
    }

    @Override
    public void onOpen(WebSocket webSocket, Response response) {
        DDLog.i(TAG, "onOpen: " + webSocket.toString());

        mWebSocket = webSocket;

        if (mSendPingPong) {
            reflectInitReaderAndWriter();
            scheduleWritPing();
        }

        for (IPanelWebSocketCallBack callback :
                msgCallBackList) {
            callback.onOpen(webSocket, response);
        }
    }

    @Override
    public void onMessage(@NotNull WebSocket webSocket, @NotNull String messageStr) {
        DDLog.i(TAG, "onMessage, messageStr: " + messageStr + ", websocket: " + webSocket.toString());
        if (mWebSocket != webSocket) {
            return;
        }

        if ("-1".equals(messageStr) && mReconnectCount > 0) {
            DDLog.e(TAG, "重试过程收到-1不通知");
            return;
        } else if ("1".equals(messageStr)) {
            mReconnectCount = 0;
        }

        callBackOnMessage(messageStr);
    }


    @Override
    public void onMessage(@NotNull WebSocket webSocket, @NotNull ByteString bytes) {
    }

    @Override
    public void onClosing(@NotNull WebSocket webSocket, int code, @NotNull String reason) {
        DDLog.i(TAG, "onClosing, code: " + code + ", reason: " + reason);
        stopScheduleWritePing();

        if (mReconnectCount > 0 || mWebSocket != webSocket) {
            return;
        }

        for (IPanelWebSocketCallBack callback :
                msgCallBackList) {
            callback.onClosing(webSocket, code, reason);
        }
        webSocket.close(1000, null);
    }

    @Override
    public void onFailure(@NotNull WebSocket webSocket, @NotNull Throwable t, Response response) {
        DDLog.e(TAG, "onFailure: " + webSocket.toString());
        if (null != mWebSocket && mWebSocket != webSocket) {
            return;
        }

        t.printStackTrace();

        if (null != mWebSocket
                && mAutoReconnect
                && mReconnectCount < FAILED_MAX_RECONNECT_COUNT) {
            mWebSocket = null;
            tryReconnectWs();
        } else {
            notifyFailure(webSocket, t, response);
        }
    }

    public synchronized void send(String msg) {
        if (mWebSocket == null) {
            return;
        }
        mWebSocket.send(msg);
    }

    public void start() {
        DDLog.i(TAG, "start");
        mAwaitingPong = false;
        mReconnectCount = 0;
        stopScheduleWritePing();

        WebSocket ws = client.newWebSocket(request, this);
    }

    public synchronized void stop() {
        DDLog.i(TAG, "stop");
        isUserClose = false;
        closeWs(false);
    }

    public synchronized void close() {
        DDLog.i(TAG, "close");
        isUserClose = true;
        closeWs(false);
    }

    /**
     * 关闭ws
     *
     * @param cancel true: 会触发回调onFailure; false: 会触发回调onClosing
     */
    private synchronized void closeWs(boolean cancel) {
        stopScheduleWritePing();
        if (mWebSocket != null) {
            if (cancel) {
                mWebSocket.cancel();
            }
            mWebSocket.close(1000, "Normal close");
            mWebSocket = null;
        }
        mWriter = null;
        mAwaitingPong = false;
        mPongTimeoutCount = 0;
    }

    public void setUserClose(boolean userClose) {
        isUserClose = userClose;
    }

    public boolean isUserClose() {
        return isUserClose;
    }

    public void release() {
        stopScheduleWritePing();
        msgCallBackList.clear();
        client.dispatcher().executorService().shutdown();
    }


    public WebSocket getWebSocket() {
        return mWebSocket;
    }

    private void callBackOnMessage(String msg) {
        for (IPanelWebSocketCallBack callback :
                msgCallBackList) {
            callback.onMessage(msg);
        }
    }


    public void addCallBack(IPanelWebSocketCallBack callBack) {
        if (msgCallBackList.contains(callBack)) {
            return;
        }

        msgCallBackList.add(callBack);
    }

    public void removeCallBack(IPanelWebSocketCallBack callBack) {
        if (!msgCallBackList.contains(callBack)) {
            return;
        }

        msgCallBackList.remove(callBack);
    }

    /**
     * 反射修改Ping/Pong的Writer和Reader
     */
    private synchronized void reflectInitReaderAndWriter() {
        try {
            Class<?> realWebSocketClass = RealWebSocket.class;

            Field writerFiled = realWebSocketClass.getDeclaredField("writer");
            writerFiled.setAccessible(true);
            mWriter = (WebSocketWriter) writerFiled.get(mWebSocket);

            Field readerFiled = realWebSocketClass.getDeclaredField("reader");
            readerFiled.setAccessible(true);
            Object reader = readerFiled.get(mWebSocket);

            Class<?> webSocketReaderClass = WebSocketReader.class;
            Field frameCallback = webSocketReaderClass.getDeclaredField("frameCallback");
            frameCallback.setAccessible(true);

            WebSocketReaderFrameCallbackWrapper wsReaderFrameCallbackWrapper = new WebSocketReaderFrameCallbackWrapper(this);
            wsReaderFrameCallbackWrapper.setSrcCallback((WebSocketReader.FrameCallback) frameCallback.get(reader));
            frameCallback.set(reader, wsReaderFrameCallbackWrapper);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on reflectInitReaderAndWriter");
            e.printStackTrace();
        }
    }

    /**
     * 开启定时发送Ping的任务
     */
    private synchronized void scheduleWritPing() {
        if (null == mService) {
            mService = new ScheduledThreadPoolExecutor(1);
        }

        mService.scheduleAtFixedRate(() -> {
            DDLog.i(TAG, "SendPing..............schedule");
            writePingFrame();
        }, PING_INTERVAL_SECONDS, PING_INTERVAL_SECONDS, TimeUnit.SECONDS);
    }

    private synchronized void stopScheduleWritePing() {
        if (null != mWriter) {
            mWriter.close();
            mWriter = null;
        }
        if (null != mService) {
            mService.shutdownNow();
            mService = null;
        }
    }

    /**
     * 发送Ping
     */
    private void writePingFrame() {
        synchronized (this) {
            if (null == mWriter) {
                return;
            }

            if (mAwaitingPong) {
                if (mPongTimeoutCount < PING_MAX_RESEND_COUNT) {
                    mPongTimeoutCount++;
                    DDLog.i(TAG, "Ping失败重发次数: " + mPongTimeoutCount);
                } else {
                    DDLog.e(TAG, "Max ping count...");
                    closeWs(true);
                    return;
                }
            }
            mAwaitingPong = true;

            try {
                DDLog.i(TAG, "SendPing..............send");
                mWriter.writePing(ByteString.EMPTY);
            } catch (Exception e) {
                DDLog.e(TAG, "Error on writePingFrame");
                e.printStackTrace();
            }
        }
    }

    private void notifyFailure(WebSocket webSocket, Throwable t, Response response) {
        DDLog.i(TAG, "通知Ws失败");
        stopScheduleWritePing();

        for (IPanelWebSocketCallBack callback :
                msgCallBackList) {
            callback.onFailure(webSocket, t, response);
        }
    }


    /**
     * 重连WebSocket
     */
    private synchronized void tryReconnectWs() {
        mAwaitingPong = false;
        mPongTimeoutCount = 0;
        mReconnectCount++;
        stopScheduleWritePing();

        DDLog.i(TAG, "tryReconnectWs次数: " + mReconnectCount);
        client.newWebSocket(request, this);
    }

    @Override
    public void onReadPong(@NotNull ByteString byteString) {
        DDLog.i(TAG, "onReadPong..............");
        mPongTimeoutCount = 0;
        mAwaitingPong = false;
    }
}
