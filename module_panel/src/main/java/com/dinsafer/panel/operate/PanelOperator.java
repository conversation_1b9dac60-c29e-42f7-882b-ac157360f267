package com.dinsafer.panel.operate;

import android.app.Application;
import android.text.TextUtils;

import androidx.annotation.Keep;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.common.Cmd;
import com.dinsafer.dincore.common.CommonCmdEvent;
import com.dinsafer.dincore.common.DeivceChangeEvent;
import com.dinsafer.dincore.db.DBKey;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.utils.RandomStringUtils;
import com.dinsafer.dssupport.msctlib.db.KV;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.common.IPanelOperator;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.panel.operate.bean.event.DeviceCmdAckEvent;
import com.dinsafer.panel.operate.bean.event.DeviceEventListEvent;
import com.dinsafer.panel.operate.bean.event.DeviceResultEvent;
import com.dinsafer.panel.operate.bean.event.DeviceSimStatueEvent;
import com.dinsafer.panel.operate.bean.event.EventListDataFixTime;
import com.dinsafer.panel.operate.bean.event.EventListTimeOutCheck;
import com.dinsafer.panel.operate.bean.event.OfflineEvent;
import com.dinsafer.panel.operate.bean.event.PingUpdataEvent;
import com.dinsafer.panel.operate.bean.event.ShowBlockToastEvent;
import com.dinsafer.panel.operate.bean.event.UserNetworkEvent;
import com.dinsafer.panel.operate.bean.event.WebSocketEvent;
import com.dinsafer.panel.operate.callback.PanelCallback;
import com.dinsafer.panel.operate.net.IPanelWebSocketCallBack;
import com.dinsafer.panel.operate.net.PanelConnectManager;
import com.dinsafer.panel.operate.task.DeviceCmdTask;
import com.dinsafer.panel.operate.task.DeviceWorkQueue;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.concurrent.TimeUnit;

import rx.Observable;
import rx.Subscriber;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;

/**
 * 主机操作管理
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/12 4:29 PM
 */
public class PanelOperator implements IPanelOperator {
    private static final String TAG = PanelOperator.class.getSimpleName();

    private Application mApp;
    private String mWsDomain;
    private boolean mCanCoap = false;
    private Subscription mTaskTimeoutSubscription;

    private PanelConnectManager mPanelConnectManager;

    public PanelOperator() {
        EventBus.getDefault().register(this);
    }

    private void checkInit() {
        if (null == mApp
                || TextUtils.isEmpty(mWsDomain)) {
            throw new NullPointerException("You must call method initPanelOperator before use.");
        }
    }

    public String getWsDomain() {
        return mWsDomain;
    }


    public boolean isCanCoap() {
        return mCanCoap;
    }

    public void setCanCoap(boolean canCoap) {
        this.mCanCoap = canCoap;
    }

    @Keep
    @Override
    public synchronized void initPanelOperator(Application application, String wsDomain) {
        if (null != mPanelConnectManager) {
            DDLog.e(TAG, "Error on initPanelOperator because PanelOperator can only init one time.");
            return;
        }
        this.mApp = application;
        this.mWsDomain = wsDomain;
        mPanelConnectManager = new PanelConnectManager();
        startCheckTaskTimeOut();
    }

    public void destroyPanelOperator() {
        stopCheckTaskTimeOut();
        if (null != mPanelConnectManager) {
            mPanelConnectManager.destroyPanelConnection();
            mPanelConnectManager = null;
        }
    }

    @Keep
    @Override
    public void connectWs() {
        checkInit();
        if (mPanelConnectManager != null)
            mPanelConnectManager.connectWebSocket();
    }

    @Keep
    @Override
    public void toCloseWs() {
        checkInit();
        if (mPanelConnectManager != null)
            mPanelConnectManager.toCloseWs();
    }

    @Keep
    @Override
    public boolean isWsConnect() {
        checkInit();
        if (mPanelConnectManager != null)
            return mPanelConnectManager.isConnect();
        return false;
    }

    @Keep
    @Override
    public String getWsCloseReason() {
        checkInit();
        if (mPanelConnectManager != null)
            return mPanelConnectManager.getCloseReason();
        return "";
    }

    public void startCheckTaskTimeOut() {
        if (null != mTaskTimeoutSubscription
                && !mTaskTimeoutSubscription.isUnsubscribed()) {
            return;
        }
        mTaskTimeoutSubscription = Observable.interval(1, TimeUnit.SECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Subscriber<Object>() {
                    @Override
                    public void onCompleted() {
                    }

                    @Override
                    public void onError(Throwable e) {
                    }

                    @Override
                    public void onNext(Object o) {
                        DeviceWorkQueue.getInstance().checkTaskIsTimeOut();
                    }
                });
    }

    public void stopCheckTaskTimeOut() {
        if (null != mTaskTimeoutSubscription
                && !mTaskTimeoutSubscription.isUnsubscribed()) {
            mTaskTimeoutSubscription.unsubscribe();
            mTaskTimeoutSubscription = null;
        }
    }

    public String getWsIp() {
        checkInit();
        String domain = KV.getString(DBKey.APIKEY, mWsDomain);
        return "wss://" + domain + "/device/ws/v2/" + DinCore.getInstance().getAppID();
    }

    public void doDeviceCmd(String messageid, String type) {
        DDLog.d(TAG, "doDeviceCmd, messageId: " + messageid + "type: " + type);
        if (null == PanelManager.getInstance().getCurrentPanelDevice()
                || null == PanelManager.getInstance().getCurrentPanelDevice().getPanelInfo()) {
            return;
        }
        DeviceWorkQueue.getInstance().addTask(new DeviceCmdTask(messageid, type));
    }

    /**
     * @param type CMD
     * @return messageId
     */
    public String doDeviceCmd(String type, boolean force) {
        DDLog.d(TAG, "doDeviceCmd, type: " + type);
        String messageId = RandomStringUtils.getMessageId();
        DeviceWorkQueue.getInstance().addTask(new DeviceCmdTask(messageId, type, force));
        return messageId;
    }

    /**
     * 发送Arm指令
     *
     * @return messageId, null 表示需要先设置时区
     */
    public String sendCmdArm(boolean force) {
        DDLog.i(TAG, "sendCmdArm");
        if (null == PanelManager.getInstance().getCurrentPanelDevice()
                || null == PanelManager.getInstance().getCurrentPanelDevice().getPanelInfo()) {
            return null;
        }

        return doDeviceCmd(PanelOperatorConstant.CMD.ARM_KEY, force);
    }

    /**
     * 发送DisArm指令
     *
     * @return messageId
     */
    public String sendCmdDisArm(boolean force) {
        DDLog.i(TAG, "sendCmdDisArm");
        if (null == PanelManager.getInstance().getCurrentPanelDevice()
                || null == PanelManager.getInstance().getCurrentPanelDevice().getPanelInfo()) {
            return null;
        }

        return doDeviceCmd(PanelOperatorConstant.CMD.DISARM_KEY, force);
    }

    /**
     * 发送HomeArm指令
     *
     * @return messageId, null 表示需要先设置
     */
    public String sendCmdHomeArm(boolean force) {
        DDLog.i(TAG, "sendCmdHomeArm");
        if (null == PanelManager.getInstance().getCurrentPanelDevice()
                || null == PanelManager.getInstance().getCurrentPanelDevice().getPanelInfo()) {
            return null;
        }

        return doDeviceCmd(PanelOperatorConstant.CMD.HOMEARM_KEY, force);
    }

    /**
     * 发送SOS指令
     *
     * @return messageId, null 表示需要先设置时区
     */
    public String sendCmdSos() {
        DDLog.i(TAG, "sendCmdSos");
        if (null == PanelManager.getInstance().getCurrentPanelDevice()
                || null == PanelManager.getInstance().getCurrentPanelDevice().getPanelInfo()) {
            return null;
        }

        return doDeviceCmd(PanelOperatorConstant.CMD.TASK_SOS, false);
    }

    @Subscribe
    public void onEvent(final EventListTimeOutCheck checkTimeEvent) {
        DDLog.i(TAG, "onEvent, EventListTimeOutCheck");
        if (null != PanelManager.getInstance().getCurrentPanelDevice()) {
            PanelManager.getInstance().getCurrentPanelDevice().onEventListTimeOutCheckEvent(checkTimeEvent);
        }
    }

    @Subscribe
    public void onEvent(final EventListDataFixTime fixTimeEvent) {
        DDLog.i(TAG, "onEvent, EventListDataFixTime");
        if (null != PanelManager.getInstance().getCurrentPanelDevice()) {
            PanelManager.getInstance().getCurrentPanelDevice().onEventListDataFixTimeEvent(fixTimeEvent);
        }
    }

    @Subscribe
    public void onEvent(final DeviceEventListEvent eventListEvent) {
        DDLog.i(TAG, "onEvent, DeviceEventListEvent");
        if (null != PanelManager.getInstance().getCurrentPanelDevice()) {
            PanelManager.getInstance().getCurrentPanelDevice().onDeviceEventListEvent(eventListEvent);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(final DeviceCmdAckEvent deviceCmdAckEvent) {
        DDLog.i(TAG, "onEvent, deviceCmdAckEvent");
        if (null != PanelManager.getInstance().getCurrentPanelDevice()) {
            PanelManager.getInstance().getCurrentPanelDevice().onDeviceCmdAckEvent(deviceCmdAckEvent);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(final DeviceSimStatueEvent simStatueEvent) {
        DDLog.i(TAG, "onEvent, DeviceSimStatueEvent");
        if (null != PanelManager.getInstance().getCurrentPanelDevice()) {
            PanelManager.getInstance().getCurrentPanelDevice().onDeviceSimStatueEvent(simStatueEvent);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(final ShowBlockToastEvent ev) {
        DDLog.i(TAG, "onEvent, ShowBlockToastEvent");
        if (null != PanelManager.getInstance().getCurrentPanelDevice()) {
            PanelManager.getInstance().getCurrentPanelDevice().onShowBlockToastEvent(ev);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(final DeviceResultEvent deviceResultEvent) {
        DDLog.i(TAG, "onEvent, DeviceResultEvent");
        if (null != PanelManager.getInstance().getCurrentPanelDevice()) {
            PanelManager.getInstance().getCurrentPanelDevice().onDeviceResultEvent(deviceResultEvent);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(OfflineEvent ev) {
        DDLog.i(TAG, "onEvent, OfflineEvent");
        if (null != PanelManager.getInstance().getCurrentPanelDevice()) {
            PanelManager.getInstance().getCurrentPanelDevice().onOfflineEvent(ev);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(UserNetworkEvent ev) {
        DDLog.i(TAG, "onEvent, UserNetworkEvent");
        if (null != PanelManager.getInstance().getCurrentPanelDevice()) {
            PanelManager.getInstance().getCurrentPanelDevice().onUserNetworkEvent(ev);
        }
    }

    @Subscribe()
    public void onEvent(WebSocketEvent webSocketEvent) {
        DDLog.i(TAG, "onEvent, WebSocketEvent");
        if (null != PanelManager.getInstance().getCurrentPanelDevice()) {
            PanelManager.getInstance().getCurrentPanelDevice().onWebSocketEvent(webSocketEvent);
        }
        if (webSocketEvent.getType() == WebSocketEvent.CONNET_SUCCESS) {
            //连上WebSocket后获取首页的配件状态
            PanelManager.getInstance().requestGetPluginStatus(
                    PanelManager.getInstance().getCurrentPanelToken());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(PingUpdataEvent pingUpdataEvent) {
        DDLog.i(TAG, "onEvent, PingUpdataEvent");
        if (null != PanelManager.getInstance().getCurrentPanelDevice()) {
            PanelManager.getInstance().getCurrentPanelDevice().onPingUpdataEvent(pingUpdataEvent);
        }
    }

    @Subscribe
    public void onEvent(CommonCmdEvent event) {
        if (Cmd.UPDATE_TUYA_DEVICE_NAME.equals(event.getCmd())) {
            try {
                if (TextUtils.isEmpty(PanelManager.getInstance().getCurrentPanelToken())) {
                    return;
                }
                JSONObject jsonObject = new JSONObject(event.getExtra());
                PanelManager.getInstance().getNetworkRequestManager().requestUpdateTuyaPluginName(
                        PanelManager.getInstance().getCurrentPanelToken(),
                        RandomStringUtils.getMessageId(),
                        jsonObject.getString("pluginid"), jsonObject.getString("name"),
                        new PanelCallback.NetworkResult<StringResponseEntry>() {
                            @Override
                            public void onSuccess(StringResponseEntry result) {

                            }

                            @Override
                            public void onError(int errorCode, String errorMsg) {

                            }
                        });
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }

    @Subscribe
    public void onEvent(DeivceChangeEvent event) {
        DDLog.i(TAG, "onEvent, DeivceChangeEvent");
        if (null != PanelManager.getInstance().getCurrentPanelDevice()) {
            PanelManager.getInstance().getCurrentPanelDevice().onDeivceChangeEvent(event);
        }
    }

    /**
     * 将操作主机的CMD转换成外部使用的cmd
     */
    public static String transformFromPanelOperatorCmd(String cmd) {
        if (null == cmd) {
            return null;
        }

        String result;
        switch (cmd) {
            case PanelOperatorConstant.CMD.ARM_KEY:
            case PanelOperatorConstant.CMD.DISARM_KEY:
            case PanelOperatorConstant.CMD.HOMEARM_KEY:
                result = PanelCmd.OPERATION_ARM;
                break;
            case PanelOperatorConstant.CMD.TASK_SOS:
            case PanelOperatorConstant.CMD.TASK_INTIMIDATIONALARM_SOS:
            case PanelOperatorConstant.CMD.TASK_ANTIINTERFER_SOS:
            case PanelOperatorConstant.CMD.TASK_FC_SOS:
            case PanelOperatorConstant.CMD.TASK_FC_SOS_PANEL:
                result = PanelCmd.OPERATION_SOS;
                break;
            case PanelOperatorConstant.CMD.SET_EXITDELAY:
                result = PanelCmd.SET_EXITDELAY;
                break;
            case PanelOperatorConstant.CMD.SET_ENTRYDELAY:
                result = PanelCmd.SET_ENTRYDELAY;
                break;
            case PanelOperatorConstant.CMD.SET_HOMEARM:
                result = PanelCmd.SET_HOMEARM_INFO;
                break;
            case PanelOperatorConstant.CMD.SIRENTIME_KEY:
                result = PanelCmd.SET_SIRENTIME;
                break;
            case PanelOperatorConstant.CMD.SET_INTIMIDATIONALARM_DATA_ON:
                result = PanelCmd.INIT_DURESS_INFO;
                break;
            case PanelOperatorConstant.CMD.SET_INTIMIDATIONALARM_ENABLE:
                result = PanelCmd.ENABLE_DURESS;
                break;
            case PanelOperatorConstant.CMD.SET_INTIMIDATIONALARM_PASSWORD:
                result = PanelCmd.SET_DURESS_PASSWORD;
                break;
            case PanelOperatorConstant.CMD.SET_INTIMIDATIONALARM_PUSH_TXT:
                result = PanelCmd.SET_DURESS_SMS;
                break;
            case PanelOperatorConstant.CMD.SET_DEVICE_TEXT_NEW:
                result = PanelCmd.SET_MESSAGE_LANGUAGE;
                break;
            case PanelOperatorConstant.CMD.SET_DEVICE_TEXT:
                result = PanelCmd.SET_MESSAGE_TEMPLATE;
                break;
            case PanelOperatorConstant.CMD.SET_CONTACTID:
                result = PanelCmd.SET_PANEL_CIDDATA;
                break;
            case PanelOperatorConstant.CMD.SET_DEVICE_TIMEZONE:
                result = PanelCmd.SET_TIMEZONE;
                break;
            case PanelOperatorConstant.CMD.SET_READYTOARM:
                result = PanelCmd.SET_READYTOARM_STATUS;
                break;
            case PanelOperatorConstant.CMD.SET_PLAY_SOUND:
                result = PanelCmd.SET_ARM_SOUND;
                break;
            case PanelOperatorConstant.CMD.SET_RESTRICT_MODE_SMS:
                result = PanelCmd.SET_RESTRICT_MODE;
                break;
            case PanelOperatorConstant.CMD.CMD_SET_BT:
                result = PanelCmd.OPEN_PANEL_BLUETOOTH;
                break;
            case PanelOperatorConstant.CMD.SET_PASSWORD:
                result = PanelCmd.SET_PANEL_PASSWORD;
                break;
            case PanelOperatorConstant.CMD.CMD_SET_4G:
                result = PanelCmd.SET_4G_INFO;
                break;
            case PanelOperatorConstant.CMD.SET_CMS_INFO:
                result = PanelCmd.SET_CMS_INFO;
                break;
            case PanelOperatorConstant.CMD.SET_CAREMODE_DATA:
                result = PanelCmd.SET_CAREMODE;
                break;
            case PanelOperatorConstant.CMD.SET_CAREMODE_PLUGIN:
                result = PanelCmd.SET_CAREMODE_PLUGINS;
                break;
            case PanelOperatorConstant.CMD.UPDATE_EVENTLIST_SETTING:
                result = PanelCmd.SET_EVENTLIST_SETTING;
                break;
            case PanelOperatorConstant.CMD.CANCEL_NO_ACTION_SOS:
                result = PanelCmd.CARE_MODE_CANCEL_SOS;
                break;
            case PanelOperatorConstant.CMD.NO_ACTION_SOS:
                result = PanelCmd.CAREMODE_NOACTION_SOS;
                break;
            case PanelOperatorConstant.CMD.RESET_DEVICE:
                result = PanelCmd.RESET_PANEL;
                break;
            case PanelOperatorConstant.CMD.GET_HOME_EXCEPTION_ACCESSORY:
                result = PanelCmd.GET_EXCEPTION_PLUGS;
                break;
            case PanelOperatorConstant.CMD.TASK_DS_STATUS_OP:
                result = PanelCmd.GET_UNCLOSE_PLUGS;
                break;
            case PanelOperatorConstant.CMD.CUSTOMIZE_REMOTE_CONTROL:
                result = PanelCmd.SAVE_CUSTOMIZE_SMART_PLUGS;
                break;
            case PanelOperatorConstant.CMD.NO_ACTION_NOTICE:
                result = PanelCmd.CAREMODE_NOACTION;
                break;
            case PanelOperatorConstant.CMD.LOW_BATTERY:
                result = PanelCmd.PANEL_LOWPOWER;
                break;
            case PanelOperatorConstant.CMD.UPDATEING_SYSTEM:
            case PanelOperatorConstant.CMD.SYSTEM_UPDATERESET:
                result = PanelCmd.PANEL_UPGRADING;
                break;
            case PanelOperatorConstant.CMD.UPDATE_AUTH:
                result = PanelCmd.PANEL_AUTHORITY_CHANGED;
                break;
            case PanelOperatorConstant.CMD.UP_POWER:
                result = PanelCmd.PANEL_POWERCHANGED;
                break;
            case PanelOperatorConstant.CMD.SET_PLUGINDATA:
            case PanelOperatorConstant.CMD.SET_NEWASKPLUGINDATA:
                result = PluginCmd.PLUGIN_SETNAME;
                break;
            case PanelOperatorConstant.CMD.DELETE_PLUGIN:
            case PanelOperatorConstant.CMD.DELETE_NEWASKPLUGIN:
                result = PluginCmd.PLUGIN_DELETE;
                break;
            case PanelOperatorConstant.CMD.UPDATE_PLUGIN_CONF:
                result = PluginCmd.UPDATE_PLUGIN_CONFIG;
                break;
            case PanelOperatorConstant.CMD.TEST_SIREN:
                result = PluginCmd.TEST_SIREN;
                break;
            case PanelOperatorConstant.CMD.EVENT_FULLPOWER:
            case PanelOperatorConstant.CMD.EVENT_LOWERPOWER:
                result = PluginCmd.PLUGIN_POWER_CHANGE;
                break;
            case PanelOperatorConstant.CMD.PLUGIN_OFFLINE:
            case PanelOperatorConstant.CMD.PLUGIN_ONLINE:
                result = PluginCmd.PLUGIN_ONLINE_CHANGE;
                break;
            case PanelOperatorConstant.CMD.TASK_PLUGIN_STATUS:
                result = PluginCmd.PLUGIN_STATE_CHANGE;
                break;
            case PanelOperatorConstant.CMD.SET_PLUGIN_BLOCK:
                result = PluginCmd.PLUGIN_CONFIG_BLOCK;
                break;
            case PanelOperatorConstant.CMD.SET_SMART_PLUG_ENABLE:
            case PanelOperatorConstant.CMD.SET_NEW_SMART_PLUG_ON:
                result = PluginCmd.PLUG_CHANGE_ON;
                break;
            case PanelOperatorConstant.CMD.RELAY_ACTION:
                result = PluginCmd.CONTROL_RELY_ACTION;
                break;
            case PanelOperatorConstant.CMD.SET_NEWASKSIRENDATA:
            case PanelOperatorConstant.CMD.SET_WIRELESS_SIREN_ADVANCED_SETTING:
                result = PluginCmd.CHANGE_SIREN_SETTING;
                break;
            case PanelOperatorConstant.CMD.SET_DOOR_WINDOW_PUSH_STATUS:
                result = PluginCmd.SET_DOOR_WINDOW_PUSH_STATUS;
                break;
            case PanelOperatorConstant.CMD.BYPASS_PLUGIN_5_MIN:
                result = PluginCmd.BYPASS_PLUGIN_5_MIN;
                break;
            case PanelOperatorConstant.CMD.SET_SENSITIVITY:
                result = PluginCmd.SET_SENSITIVITY;
                break;
            case PanelOperatorConstant.CMD.EXIT_PIR_SETTING_MODE:
                result = PluginCmd.EXIT_PIR_SETTING_MODE;
                break;
            default:
                result = cmd;
                break;
        }
        return result;
    }

    public void addCallBack(IPanelWebSocketCallBack callBack) {
        if (null != mPanelConnectManager) {
            mPanelConnectManager.addCallBack(callBack);
        }
    }

    public void removeCallBack(IPanelWebSocketCallBack callBack) {
        if (null != mPanelConnectManager) {
            mPanelConnectManager.removeCallBack(callBack);
        }
    }
}
