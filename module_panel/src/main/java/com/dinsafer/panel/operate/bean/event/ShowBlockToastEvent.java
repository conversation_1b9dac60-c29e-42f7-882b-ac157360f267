package com.dinsafer.panel.operate.bean.event;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2020/11/30
 */
public class ShowBlockToastEvent {

    String plugin;

    String cmdType;

    public ShowBlockToastEvent(String cmdType) {
        this.cmdType = cmdType;
        this.plugin = "";
    }

    public ShowBlockToastEvent(String cmdType, String plugin) {
        this.cmdType = cmdType;
        this.plugin = plugin;
    }

    public String getPlugin() {
        return plugin;
    }

    public void setPlugin(String plugin) {
        this.plugin = plugin;
    }

    public String getCmdType() {
        return cmdType;
    }

    public void setCmdType(String cmdType) {
        this.cmdType = cmdType;
    }
}
