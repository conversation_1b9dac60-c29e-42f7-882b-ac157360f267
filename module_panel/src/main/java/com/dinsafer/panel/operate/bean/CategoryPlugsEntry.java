package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;
import java.util.List;

/**
 * Created by Rinfon on 16/8/19.
 */
@Keep
public class CategoryPlugsEntry extends BaseHttpEntry implements Serializable {


    /**
     * plugininfo : {"category":1,"liferelated":1,"type":0,"smartplugid":""}
     * datas : [{"time":1234567890,"id":"","name":"","ipcdata":"xxxxxx","enable":false,"subcategory":"01"},{"time":1234567890,"id":"","name":"liang<PERSON><PERSON>un","ipcdata":"xxxxxx","switch":true,"subcategory":"04"}]
     * smartplug : [{"id":"","name":"","time":1234567890},{"id":"","name":"","time":1234567890}]
     */

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean implements Serializable {
        /**
         * category : 1
         * liferelated : 1
         * type : 0
         * smartplugid :
         */

        private PlugininfoBean plugininfo;
        /**
         * time : 1234567890
         * id :
         * name :
         * ipcdata : xxxxxx
         * enable : false
         * subcategory : 01
         */

        private List<DatasBean> datas;
        /**
         * id :
         * name :
         * time : 1234567890
         */

//        另外的数据，用于心跳配件
        private String otherData;

        private List<SmartplugBean> smartplug;

        public PlugininfoBean getPlugininfo() {
            return plugininfo;
        }

        public void setPlugininfo(PlugininfoBean plugininfo) {
            this.plugininfo = plugininfo;
        }

        public List<DatasBean> getDatas() {
            return datas;
        }

        public void setDatas(List<DatasBean> datas) {
            this.datas = datas;
        }

        public List<SmartplugBean> getSmartplug() {
            return smartplug;
        }

        public void setSmartplug(List<SmartplugBean> smartplug) {
            this.smartplug = smartplug;
        }

        public String getOtherData() {
            return otherData;
        }

        public void setOtherData(String otherData) {
            this.otherData = otherData;
        }

        @Keep
        public static class PlugininfoBean implements Serializable {
            private int category;
            private int liferelated;
            private int type;
            private String smartplugid;

            public int getCategory() {
                return category;
            }

            public void setCategory(int category) {
                this.category = category;
            }

            public int getLiferelated() {
                return liferelated;
            }

            public void setLiferelated(int liferelated) {
                this.liferelated = liferelated;
            }

            public int getType() {
                return type;
            }

            public void setType(int type) {
                this.type = type;
            }

            public String getSmartplugid() {
                return smartplugid;
            }

            public void setSmartplugid(String smartplugid) {
                this.smartplugid = smartplugid;
            }
        }

        @Keep
        public static class DatasBean implements Serializable {
            private long time;
            private String id;
            private String name;
            private String ipcdata;
            private boolean enable;
            private String siren_setting;
            private String subcategory;
            //            2019/10/12
//            新增decodeid，要兼容配件id和二维码id不一致问题，下面那个plugin_item_decode_id，是第三方配件的decodeid，理论上来说是一样的。
            private String decodeid;
            private String plugin_item_decode_id;
            private String plugin_item_sub_category;


            public long getTime() {
                return time;
            }

            public void setTime(long time) {
                this.time = time;
            }

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getIpcdata() {
                return ipcdata;
            }

            public void setIpcdata(String ipcdata) {
                this.ipcdata = ipcdata;
            }

            public boolean isEnable() {
                return enable;
            }

            public void setEnable(boolean enable) {
                this.enable = enable;
            }

            public String getSubcategory() {
                return subcategory;
            }

            public void setSubcategory(String subcategory) {
                this.subcategory = subcategory;
            }

            public String getSiren_setting() {
                return siren_setting;
            }

            public void setSiren_setting(String siren_setting) {
                this.siren_setting = siren_setting;
            }

            public String getPlugin_item_decode_id() {
                return plugin_item_decode_id;
            }

            public void setPlugin_item_decode_id(String plugin_item_decode_id) {
                this.plugin_item_decode_id = plugin_item_decode_id;
            }

            public String getPlugin_item_sub_category() {
                return plugin_item_sub_category;
            }

            public void setPlugin_item_sub_category(String plugin_item_sub_category) {
                this.plugin_item_sub_category = plugin_item_sub_category;
            }

            public String getDecodeid() {
                return decodeid;
            }

            public void setDecodeid(String decodeid) {
                this.decodeid = decodeid;
            }
        }

        @Keep
        public static class SmartplugBean implements Serializable {
            private String id;
            private String name;
            private long time;

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public long getTime() {
                return time;
            }

            public void setTime(long time) {
                this.time = time;
            }
        }

    }
}
