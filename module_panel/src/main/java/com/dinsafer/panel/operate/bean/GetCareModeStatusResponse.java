package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/11/23 15:16
 */
@Keep
public class GetCareModeStatusResponse extends BaseHttpEntry {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        private long gmtime;
        private long last_no_action_time;
        private SettingBean setting;

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        public long getLast_no_action_time() {
            return last_no_action_time;
        }

        public void setLast_no_action_time(long last_no_action_time) {
            this.last_no_action_time = last_no_action_time;
        }

        public SettingBean getSetting() {
            return setting;
        }

        public void setSetting(SettingBean setting) {
            this.setting = setting;
        }

        @Keep
        public static class SettingBean {
            private long alarm_delay_time;
            private boolean enabled;
            private long no_action_time;

            public long getAlarm_delay_time() {
                return alarm_delay_time;
            }

            public void setAlarm_delay_time(long alarm_delay_time) {
                this.alarm_delay_time = alarm_delay_time;
            }

            public boolean isEnabled() {
                return enabled;
            }

            public void setEnabled(boolean enabled) {
                this.enabled = enabled;
            }

            public long getNo_action_time() {
                return no_action_time;
            }

            public void setNo_action_time(long no_action_time) {
                this.no_action_time = no_action_time;
            }
        }
    }
}
