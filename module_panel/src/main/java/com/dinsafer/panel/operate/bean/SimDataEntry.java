package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2019/3/31
 */
@Keep
public class SimDataEntry extends BaseHttpEntry {

    /**
     * Cmd :
     * Result : {"imei":"","imsi":"","csq":"","pin":"","gmtime":23511234342234}
     */

    private String Cmd;
    private ResultBean Result;

    public String getCmd() {
        return Cmd;
    }

    public void setCmd(String Cmd) {
        this.Cmd = Cmd;
    }

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        /**
         * imei :
         * imsi :
         * csq :
         * pin :
         * gmtime : 23511234342234
         */

        private String imei;
        private String imsi;
        private String csq;
        private String pin;
        private String d_phone;
        //  int,sim卡状态(0:无卡，1:有卡，2:有问题)
        private int sim;
        private long gmtime;

        public String getImei() {
            return imei;
        }

        public void setImei(String imei) {
            this.imei = imei;
        }

        public String getImsi() {
            return imsi;
        }

        public void setImsi(String imsi) {
            this.imsi = imsi;
        }

        public String getCsq() {
            return csq;
        }

        public void setCsq(String csq) {
            this.csq = csq;
        }

        public String getPin() {
            return pin;
        }

        public void setPin(String pin) {
            this.pin = pin;
        }

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        public String getD_phone() {
            return d_phone;
        }

        public void setD_phone(String d_phone) {
            this.d_phone = d_phone;
        }

        public int getSim() {
            return sim;
        }

        public void setSim(int sim) {
            this.sim = sim;
        }

        public boolean isSimNormal() {
            return getSim() == 1;
        }

        @Override
        public String toString() {
            return "ResultBean{" +
                    "imei='" + imei + '\'' +
                    ", imsi='" + imsi + '\'' +
                    ", csq='" + csq + '\'' +
                    ", pin='" + pin + '\'' +
                    ", gmtime=" + gmtime +
                    '}';
        }
    }
}
