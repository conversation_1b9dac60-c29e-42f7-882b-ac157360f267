package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

/**
 * 获取指定主机的布撤防提示声设置的返回
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/11/23 15:22
 */
@Keep
public class GetVoicePromptResponse extends BaseHttpEntry {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        private long gmtime;
        private long siren_duration;
        private boolean vioce_prompt;

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        public long getSiren_duration() {
            return siren_duration;
        }

        public void setSiren_duration(long siren_duration) {
            this.siren_duration = siren_duration;
        }

        public boolean isVioce_prompt() {
            return vioce_prompt;
        }

        public void setVioce_prompt(boolean vioce_prompt) {
            this.vioce_prompt = vioce_prompt;
        }
    }
}
