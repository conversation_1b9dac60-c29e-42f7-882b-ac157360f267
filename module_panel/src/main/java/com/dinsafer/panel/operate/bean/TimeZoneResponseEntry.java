package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.util.List;

/**
 * Created by chenyufeng on 2017/8/23.
 */
@Keep
public class TimeZoneResponseEntry extends BaseHttpEntry {


    /**
     * Result : {"gmtime":23511234342234,"timezonelist":["cn","us","jp","kr"],"timezone":""}
     */

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        /**
         * gmtime : 23511234342234
         * timezonelist : ["cn","us","jp","kr"]
         * timezone :
         */

        private long gmtime;
        private String timezone;
        private List<String> timezonelist;

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        public String getTimezone() {
            return timezone;
        }

        public void setTimezone(String timezone) {
            this.timezone = timezone;
        }

        public List<String> getTimezonelist() {
            return timezonelist;
        }

        public void setTimezonelist(List<String> timezonelist) {
            this.timezonelist = timezonelist;
        }
    }
}

