package com.dinsafer.panel.operate.bean;


import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.util.List;

/**
 * EventList数据
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2020/12/27 5:25 PM
 */
@Keep
public class EventListEntry extends BaseHttpEntry {

    /**
     * Cmd :
     * Result : [{"cmdname":"TASK_ARM","duration":-1,"photo":"","messageid":"da410f26edf96559a0ba63377983a26f","time":0,"type":"Action","user":"dayunsz009","result":0},{"cmdname":"TASK_ARM","duration":-1,"photo":"","messageid":"8da3b650870fff14e4652065f5b62dd4","time":0,"type":"Action","user":"lxy","result":0}]
     */

    private String Cmd;
    /**
     * cmdname : TASK_ARM
     * duration : -1
     * photo :
     * messageid : da410f26edf96559a0ba63377983a26f
     * time : 0
     * type : Action
     * user : dayunsz009
     * result : 0
     */

    private List<EventListBean> Result;

    public String getCmd() {
        return Cmd;
    }

    public void setCmd(String Cmd) {
        this.Cmd = Cmd;
    }

    public List<EventListBean> getResult() {
        return Result;
    }

    public void setResult(List<EventListBean> Result) {
        this.Result = Result;
    }
}
