package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

/**
 * Created by LT on 2018/8/26.
 */
@Keep
public class ReadyToArmSwitchStatusEntry extends BaseHttpEntry {

    /**
     * Result : {"url":"","enable":false}
     */

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        /**
         * url :
         * enable : false
         */

        private String url;
        private boolean enable;

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }

        private int count;

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public boolean isEnable() {
            return enable;
        }

        public void setEnable(boolean enable) {
            this.enable = enable;
        }
    }
}
