package com.dinsafer.panel.operate.bean;

/**
 * 扩展属性
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2020/12/23 8:58 PM
 */
public class TuyaItemPlus extends TuyaItem {
    // 加载状态定义
    public final static int LOADING_STATUS_LOADING = 0;
    public final static int LOADING_STATUS_SUCCESS = 1;
    public final static int LOADING_STATUS_ERROR = 2;

    // 是否包含需要从Websocket返回的状态
    private boolean needLoading = true;
    // loading状态
    private int loadingStatus = LOADING_STATUS_LOADING;
    // 是否在线
    private boolean online;
    private boolean needOnlineState = true; // 是否有在线状态
    private int category;
    private String sub_category;
    private boolean haveApart; // 是否有开合状态
    private boolean isApart; // 是否开合
    private int block; // 是否屏蔽
    private boolean isEmptyLoadingView; // 是否空的loading占位布局

    public TuyaItemPlus() {
        super("", 0);
        this.isEmptyLoadingView = true;
    }

    public TuyaItemPlus(String name, int type) {
        super(name, type);
    }

    public TuyaItemPlus(String id, String name, int type) {
        super(id, name, type);
    }

    public TuyaItemPlus(String id, String name, int type, String pid) {
        super(id, name, type, pid);
    }

    public TuyaItemPlus(String name, String id) {
        super(name, id);
    }

    public TuyaItemPlus(String id, String name, boolean isOn) {
        super(id, name, isOn);
    }

    /**
     * 是否正在加载中
     *
     * @return
     */
    public boolean isLoading() {
        return needLoading
                && LOADING_STATUS_LOADING == loadingStatus;
    }

    /**
     * 是否加载错误
     *
     * @return
     */
    public boolean isLoadedError() {
        return needLoading
                && LOADING_STATUS_ERROR == loadingStatus;
    }

    /**
     * 是否正常状态
     *
     * @return
     */
    public boolean isLoadedSuccess() {
        return !needLoading
                || LOADING_STATUS_SUCCESS == loadingStatus;
    }

    public boolean isNeedLoading() {
        return needLoading;
    }

    public void setNeedLoading(boolean needLoading) {
        this.needLoading = needLoading;
    }

    public int getLoadingStatus() {
        return loadingStatus;
    }

    public void setLoadingStatus(int loadingStatus) {
        this.loadingStatus = loadingStatus;
    }

    public boolean isOnline() {
        return online;
    }

    public void setOnline(boolean online) {
        this.online = online;
    }

    public int getCategory() {
        return category;
    }

    public void setCategory(int category) {
        this.category = category;
    }

    public String getSub_category() {
        return sub_category;
    }

    public void setSub_category(String sub_category) {
        this.sub_category = sub_category;
    }

    public boolean isNeedOnlineState() {
        return needOnlineState;
    }

    public void setNeedOnlineState(boolean needOnlineState) {
        this.needOnlineState = needOnlineState;
    }

    public boolean isHaveApart() {
        return haveApart;
    }

    public void setHaveApart(boolean haveApart) {
        this.haveApart = haveApart;
    }

    public boolean isApart() {
        return isApart;
    }

    public void setApart(boolean apart) {
        isApart = apart;
    }

    public int getBlock() {
        return block;
    }

    public void setBlock(int block) {
        this.block = block;
    }

    /**
     * 是否是屏蔽状态
     *
     * @return
     */
    public boolean isNeedBlock() {
        return 2 == block;
    }

    public boolean isEmptyLoadingView() {
        return isEmptyLoadingView;
    }

    public void setEmptyLoadingView(boolean emptyLoadingView) {
        isEmptyLoadingView = emptyLoadingView;
    }
}
