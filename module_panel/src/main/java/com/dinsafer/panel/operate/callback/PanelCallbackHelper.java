package com.dinsafer.panel.operate.callback;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.dinsafer.dssupport.utils.DDLog;

import java.util.ArrayList;
import java.util.List;

/**
 * 主机信息回调工具类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/13 2:56 PM
 */
public class PanelCallbackHelper {
    private static final String TAG = PanelCallbackHelper.class.getSimpleName();

    private final List<PanelCallback.PanelState> mPanelStateListener = new ArrayList<>();
    private final List<PanelCallback.GetHomePluginInfo> mPanelHomePluginListener = new ArrayList<>();
    private final List<PanelCallback.GetPluginQuantity> mPanelPluginQuantityListener = new ArrayList<>();

    public PanelCallbackHelper() {
    }

    @Keep
    public void addPanelStateCallback(@NonNull PanelCallback.PanelState callback) {
        if (mPanelStateListener.contains(callback)) {
            return;
        }
        synchronized (mPanelStateListener) {
            mPanelStateListener.add(callback);
        }
    }

    @Keep
    public void removePanelStateCallback(@NonNull PanelCallback.PanelState callback) {
        if (!mPanelStateListener.contains(callback)) {
            return;
        }
        synchronized (mPanelStateListener) {
            mPanelStateListener.remove(callback);
        }
    }

    @Keep
    public void cleanPanelStateCallback() {
        if (mPanelStateListener.size() > 0) {
            synchronized (mPanelStateListener) {
                mPanelStateListener.clear();
            }
        }
    }

    /**
     * 通知当前获取的主机信息不属于当前登录的用户
     */
    @Keep
    public void onPanelNotBelongUser() {
        DDLog.i(TAG, "onPanelNotBelongUser");
        if (mPanelStateListener.size() <= 0) {
            return;
        }
        synchronized (mPanelStateListener) {
            for (PanelCallback.PanelState panelStateListener : mPanelStateListener) {
                panelStateListener.onPanelNotBelongUser();
            }
        }
    }

    /**
     * 通知主机已离线
     */
    @Keep
    public void onPanelOffline() {
        DDLog.i(TAG, "onPanelOffline");
        if (mPanelStateListener.size() <= 0) {
            return;
        }
        synchronized (mPanelStateListener) {
            for (PanelCallback.PanelState panelStateListener : mPanelStateListener) {
                panelStateListener.onPanelOffline();
            }
        }
    }

    @Keep
    public void addGetHomePluginInfoCallback(@NonNull PanelCallback.GetHomePluginInfo callback) {
        if (mPanelHomePluginListener.contains(callback)) {
            return;
        }
        synchronized (mPanelHomePluginListener) {
            mPanelHomePluginListener.add(callback);
        }
    }

    @Keep
    public void removeGetHomePluginInfoCallback(@NonNull PanelCallback.GetHomePluginInfo callback) {
        if (!mPanelHomePluginListener.contains(callback)) {
            return;
        }
        synchronized (mPanelHomePluginListener) {
            mPanelHomePluginListener.remove(callback);
        }
    }

    @Keep
    public void cleanGetHomePluginInfoCallback() {
        if (mPanelHomePluginListener.size() > 0) {
            synchronized (mPanelHomePluginListener) {
                mPanelHomePluginListener.clear();
            }
        }
    }

    public void onGetHomePlugin(boolean success) {
        DDLog.i(TAG, "onGetHomePlugin, success: " + success);
        if (mPanelHomePluginListener.size() <= 0) {
            return;
        }
        synchronized (mPanelHomePluginListener) {
            for (PanelCallback.GetHomePluginInfo callback : mPanelHomePluginListener) {
                callback.onGetHomePlugin(success);
            }
        }
    }

    public void onStartGetHomePlugin(String deviceId) {
        DDLog.i(TAG, "onStartGetHomePlugin, deviceId: " + deviceId);
        if (mPanelHomePluginListener.size() <= 0) {
            return;
        }
        synchronized (mPanelHomePluginListener) {
            for (PanelCallback.GetHomePluginInfo callback : mPanelHomePluginListener) {
                callback.onStartGetHomePlugin(deviceId);
            }
        }
    }


    @Keep
    public void addGetPluginQuantityCallback(@NonNull PanelCallback.GetPluginQuantity callback) {
        if (mPanelPluginQuantityListener.contains(callback)) {
            return;
        }
        synchronized (mPanelPluginQuantityListener) {
            mPanelPluginQuantityListener.add(callback);
        }
    }

    @Keep
    public void removeGetPluginQuantityCallback(@NonNull PanelCallback.GetPluginQuantity callback) {
        if (!mPanelPluginQuantityListener.contains(callback)) {
            return;
        }
        synchronized (mPanelPluginQuantityListener) {
            mPanelPluginQuantityListener.remove(callback);
        }
    }

    @Keep
    public void cleanGetPluginQuantityCallback() {
        if (mPanelPluginQuantityListener.size() > 0) {
            synchronized (mPanelPluginQuantityListener) {
                mPanelPluginQuantityListener.clear();
            }
        }
    }

    public void onGetPanelPluginQuantity(boolean isSuccess) {
        DDLog.i(TAG, "onGetPanelPluginQuantity, success: " + isSuccess);
        if (mPanelPluginQuantityListener.size() <= 0) {
            return;
        }
        synchronized (mPanelPluginQuantityListener) {
            for (PanelCallback.GetPluginQuantity callback : mPanelPluginQuantityListener) {
                callback.onGetPanelPluginQuantity(isSuccess);
            }
        }
    }

    public void onStartGetPanelPluginQuantity() {
        DDLog.i(TAG, "onStartGetPanelPluginQuantity");
        if (mPanelPluginQuantityListener.size() <= 0) {
            return;
        }
        synchronized (mPanelPluginQuantityListener) {
            for (PanelCallback.GetPluginQuantity callback : mPanelPluginQuantityListener) {
                callback.onStartGetPanelPluginQuantity();
            }
        }
    }


    /**
     * 清空所有Callback
     */
    @Keep
    public void cleanAllCallback() {
        DDLog.i(TAG, "cleanAllCallback");
        cleanPanelStateCallback();
    }
}
