package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

/**
 * Created by Rinfon on 16/8/17.
 */
@Keep
public class AppMessageEntry extends BaseHttpEntry {


    /**
     * Cmd :
     * Result : {"device_text":"xxx","lang":"xxxx"}
     */

    private String Cmd;
    /**
     * device_text : xxx
     * lang : xxxx
     */

    private ResultBean Result;

    public String getCmd() {
        return Cmd;
    }

    public void setCmd(String Cmd) {
        this.Cmd = Cmd;
    }

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        private String device_text;
        private String lang;

        public String getDevice_text() {
            return device_text;
        }

        public void setDevice_text(String device_text) {
            this.device_text = device_text;
        }

        public String getLang() {
            return lang;
        }

        public void setLang(String lang) {
            this.lang = lang;
        }
    }
}
