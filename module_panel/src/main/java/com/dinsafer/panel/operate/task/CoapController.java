package com.dinsafer.panel.operate.task;

import android.text.TextUtils;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.utils.RandomStringUtils;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.operate.PanelOperatorConstant;
import com.dinsafer.panel.util.PanelSecretUtil;

import org.eclipse.californium.core.CoapClient;
import org.eclipse.californium.core.CoapHandler;
import org.eclipse.californium.core.CoapResponse;
import org.eclipse.californium.core.coap.CoAP;
import org.eclipse.californium.core.coap.MediaTypeRegistry;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by LT on 2019/3/27.
 */
public class CoapController {
    private static CoapController instance;

    private final String TAG = this.getClass().getSimpleName();

    private final long TIMEOUT = 60 * 000;

    private String url;

    private CoapClient receiveClient;
    private CoapClient coapClient;

    public CoapController() {
        if (PanelManager.getInstance().getCurrentPanelDevice() == null
                || PanelManager.getInstance().getCurrentPanelDevice().getPanelInfo() == null
                || TextUtils.isEmpty(PanelManager.getInstance().getCurrentPanelDevice().getPanelInfo().getIp())) {
            return;
        }

        url = "coap://" + PanelManager.getInstance().getCurrentPanelDevice().getPanelInfo().getIp() + ":5683/action";
    }

    public static CoapController getInstance() {
        if (instance == null) {
            instance = new CoapController();
        }
        return instance;
    }

    public void setIp(String ip) {
        if (TextUtils.isEmpty(ip)) {
            return;
        }
        url = "coap://" + ip + ":5683/action";

    }

    public interface CallBack {
        void onSuccess();

        void onFail();
    }

    private void call(String data, CallBack callBack) {
        DDLog.d(TAG, "call by coap: " + "data is " + data);
        if (coapClient == null) {
            return;
        }
        coapClient.post(new CoapHandler() {
            @Override
            public void onLoad(CoapResponse response) {
                DDLog.d(TAG, "call success " + response.getResponseText());
                callBack.onSuccess();
            }

            @Override
            public void onError() {
                DDLog.d(TAG, "call onError ");
                callBack.onFail();
            }
        }, data.getBytes(), MediaTypeRegistry.TEXT_PLAIN);
    }

    public interface IsCanCoapListener {
        void canCoap();

        void cannotCoap();
    }

    public void getIsCanCoap(CallBack callBack) {
        if (TextUtils.isEmpty(url)) {
            callBack.onFail();
            return;
        }
        isClean = false;
        DDLog.d(TAG, "url " + url);
        if (coapGetTask != null) {
            coapGetTask = null;
        }
        coapGetTask = new CoapGetTask(url);
        coapGetTask.start();
        if (coapClient != null) {
            coapClient = null;
        }
        coapClient = new CoapClient()
                .setURI(url)
                .setTimeout(TIMEOUT);

        coapCheckMessageId = RandomStringUtils.getMessageId();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("messageid", coapCheckMessageId);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        DDLog.d(TAG, "call by coap: " + "data is " + jsonObject.toString());
        String data = PanelSecretUtil.getSC(jsonObject.toString());
//        data = "";

        coapClient.post(new CoapHandler() {
            @Override
            public void onLoad(CoapResponse response) {
                DDLog.d(TAG, "call success " + response.getResponseText());
                if (response.advanced().getType() == CoAP.Type.ACK) {
                    DDLog.d(TAG, "call success ack");
                    callBack.onSuccess();
                }
            }

            @Override
            public void onError() {
                DDLog.d(TAG, "call onError ");
                clean();
                callBack.onFail();
            }
        }, data.getBytes(), MediaTypeRegistry.TEXT_PLAIN);
    }

    public void callArmDisarmHomearm(String taskId, String cmd, boolean force, CallBack callBack) {
        if (TextUtils.isEmpty(url)
                || null == DinCore.getUserInstance().getUser()
                || TextUtils.isEmpty(DinCore.getUserInstance().getUser().getUid())
                || TextUtils.isEmpty(PanelManager.getInstance().getCurrentPanelToken())) {
            callBack.onFail();
            return;
        }

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", DinCore.getUserInstance().getUser().getUid());
            jsonObject.put("devtoken", PanelManager.getInstance().getCurrentPanelToken());
            jsonObject.put("messageid", taskId);
            jsonObject.put("cmd", cmd);
            jsonObject.put("force", force);
            jsonObject.put("gmtime", System.currentTimeMillis() * 1000);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        DDLog.d(TAG, "call by coap: " + "data is " + jsonObject.toString());
        String data = PanelSecretUtil.getSC(jsonObject.toString());
        call(data, callBack);
    }

    public void callRelayControl(String userid, String devicetoken,
                                 String messageid, String action, String sendid, CallBack callBack) {
        if (TextUtils.isEmpty(url)) {
            callBack.onFail();
            return;
        }

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("devtoken", devicetoken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("action", action);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.RELAY_ACTION);
            jsonObject.put("userid", userid);
            jsonObject.put("sendid", sendid);

        } catch (JSONException e) {
            e.printStackTrace();
        }
        DDLog.d(TAG, "call by coap: " + "data is " + jsonObject.toString());
        String data = PanelSecretUtil.getSC(jsonObject.toString());
        call(data, callBack);
    }

    public void callSmartPlugsStatusChange(String uid, String deviceToken
            , String plugId, int isOn, boolean isNewAskPlug, String sendid, String stype, CallBack callBack) {
        if (TextUtils.isEmpty(url)) {
            callBack.onFail();
            return;
        }

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", RandomStringUtils.getMessageId());
            jsonObject.put("cmd", isNewAskPlug
                    ? PanelOperatorConstant.CMD.SET_NEW_SMART_PLUG_ON
                    : PanelOperatorConstant.CMD.SET_SMART_PLUG_ENABLE);
            jsonObject.put("pluginid", plugId);
            jsonObject.put("plugin_item_smart_plug_enable", isOn);
            if (isNewAskPlug) {
                // 新ASK智能插座新增字段
                jsonObject.put("sendid", sendid);
                jsonObject.put("stype", stype);
                jsonObject.put("dtype", 10);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        DDLog.d(TAG, "call by coap: " + "data is " + jsonObject.toString());
        String data = PanelSecretUtil.getSC(jsonObject.toString());
        call(data, callBack);
    }

    private String coapCheckMessageId = "";

    public String getCoapCheckMessageId() {
        return coapCheckMessageId;
    }

    public void setCoapCheckMessageId(String coapCheckMessageId) {
        this.coapCheckMessageId = coapCheckMessageId;
    }

    class CoapGetTask extends Thread {

        String url;

        public CoapGetTask(String url) {
            this.url = url;
        }

        @Override
        public void run() {
            receiveClient = null;
            receiveClient = new CoapClient()
                    .setURI(url)
                    .setTimeout(TIMEOUT);
            try {
                if (isClean) {
                    return;
                } else {
                    CoapResponse response = receiveClient.get();
                    if (response != null) {
                        DDLog.d(TAG, "on back setCanCoap true");
                        PanelManager.getInstance().getPanelOperator().setCanCoap(true);
                        isClean = true;
                    } else {
                        DDLog.d(TAG, "on back No response");
                    }
                }
            } catch (Exception e) {
                return;
            }
        }
    }

    private CoapGetTask coapGetTask;

    boolean isClean = false;

    public void clean() {
        if (coapGetTask != null) {
            coapGetTask.interrupt();
        }
        isClean = true;
    }

}
