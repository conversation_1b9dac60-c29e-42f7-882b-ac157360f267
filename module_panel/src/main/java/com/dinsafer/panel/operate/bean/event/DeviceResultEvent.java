package com.dinsafer.panel.operate.bean.event;

/**
 * Created by Rinfon on 16/8/25.
 */
public class DeviceResultEvent {

    String cmdType;

    int status;

    String messageId;

    String reslut;


    public DeviceResultEvent(String cmdType, int status, String messageId) {
        this.cmdType = cmdType;
        this.status = status;
        this.messageId = messageId;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getCmdType() {
        return cmdType;
    }

    public void setCmdType(String cmdType) {
        this.cmdType = cmdType;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getReslut() {
        return reslut;
    }

    public void setReslut(String reslut) {
        this.reslut = reslut;
    }

    @Override
    public String toString() {
        return "DeviceResultEvent{" +
                "cmdType='" + cmdType + '\'' +
                ", status=" + status +
                ", messageId='" + messageId + '\'' +
                ", reslut='" + reslut + '\'' +
                '}';
    }
}

