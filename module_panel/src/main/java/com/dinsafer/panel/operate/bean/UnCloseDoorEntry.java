package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.util.List;

/**
 * Created by LT on 2018/8/26.
 */
@Keep
public class UnCloseDoorEntry extends BaseHttpEntry {

    /**
     * Cmd : TASK_DS_STATUS_OP
     * Result : {"plugins":[{"name":"a","id":""}]}
     */

    private String Cmd;
    private ResultBean Result;

    public String getCmd() {
        return Cmd;
    }

    public void setCmd(String Cmd) {
        this.Cmd = Cmd;
    }

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {

        public boolean isForce() {
            return force;
        }

        public void setForce(boolean force) {
            this.force = force;
        }

        private boolean force;

        private List<PluginsBean> plugins;

        public List<PluginsBean> getPlugins() {
            return plugins;
        }

        public void setPlugins(List<PluginsBean> plugins) {
            this.plugins = plugins;
        }

        @Keep
        public static class PluginsBean {
            /**
             * name : a
             * id :
             */

            private String name;
            private String id;

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }
        }
    }
}
