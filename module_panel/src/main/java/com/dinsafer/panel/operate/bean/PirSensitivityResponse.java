package com.dinsafer.panel.operate.bean;


import androidx.annotation.Keep;

/**
 * @describe：
 * @date：2023/9/12
 * @author: create by Sydnee
 */

@Keep
public class PirSensitivityResponse {

    /**
     * {
     * "gmtime": 1658649822246802200,
     * "plugin_id": "!OHIY7wn",
     * "sendid": "1234",
     * "sensitivity": 0,
     * "stype": "4A"
     * }
     */
    private long gmtime;
    private String pluginId;
    private String sendid;
    private int sensitivity;
    private String stype;

    public long getGmtime() {
        return gmtime;
    }

    public void setGmtime(long gmtime) {
        this.gmtime = gmtime;
    }

    public String getPluginId() {
        return pluginId;
    }

    public void setPluginId(String pluginId) {
        this.pluginId = pluginId;
    }

    public String getSendid() {
        return sendid;
    }

    public void setSendid(String sendid) {
        this.sendid = sendid;
    }

    public int getSensitivity() {
        return sensitivity;
    }

    public void setSensitivity(int sensitivity) {
        this.sensitivity = sensitivity;
    }

    public String getStype() {
        return stype;
    }

    public void setStype(String stype) {
        this.stype = stype;
    }
}
