package com.dinsafer.panel.operate.net;

import org.jetbrains.annotations.NotNull;

import java.io.IOException;

import okhttp3.internal.ws.WebSocketReader;
import okio.ByteString;

/**
 * WebSocketReader.FrameCallback包装类
 * <p>
 * 处理pong
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/7/20 4:48 PM
 */
public class WebSocketReaderFrameCallbackWrapper implements WebSocketReader.FrameCallback {

    private WebSocketReader.FrameCallback srcCallback;
    private final OnReadPong mOnReadPong;

    public WebSocketReaderFrameCallbackWrapper(OnReadPong onReadPong) {
        this.mOnReadPong = onReadPong;
    }

    public WebSocketReader.FrameCallback getSrcCallback() {
        return srcCallback;
    }

    public void setSrcCallback(WebSocketReader.FrameCallback srcCallback) {
        this.srcCallback = srcCallback;
    }

    @Override
    public void onReadClose(int i, @NotNull String s) {
        if (null != srcCallback) {
            srcCallback.onReadClose(i, s);
        }
    }

    @Override
    public void onReadMessage(@NotNull String s) throws IOException {
        if (null != srcCallback) {
            srcCallback.onReadMessage(s);
        }
    }

    @Override
    public void onReadMessage(@NotNull ByteString byteString) throws IOException {
        if (null != srcCallback) {
            srcCallback.onReadMessage(byteString);
        }
    }

    @Override
    public void onReadPing(@NotNull ByteString byteString) {
        if (null != srcCallback) {
            srcCallback.onReadPing(byteString);
        }
    }

    @Override
    public void onReadPong(@NotNull ByteString byteString) {
        if (null != srcCallback) {
            srcCallback.onReadPong(byteString);
        }
        if (null != mOnReadPong) {
            mOnReadPong.onReadPong(byteString);
        }
    }

    public interface OnReadPong {
        void onReadPong(@NotNull ByteString byteString);
    }
}
