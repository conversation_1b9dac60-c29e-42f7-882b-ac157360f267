package com.dinsafer.panel.operate.bean;


import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

/**
 * EventList设置数据类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2020/12/27 2:38 PM
 */
@Keep
public class EventListSettingEntry extends BaseHttpEntry {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        private boolean dw_event_log; // door window在disarm下的eventlist设置
        private boolean tamper_event_log; //alarm在disarm下的eventlist设置

        public boolean isDw_event_log() {
            return dw_event_log;
        }

        public void setDw_event_log(boolean dw_event_log) {
            this.dw_event_log = dw_event_log;
        }

        public boolean isTamper_event_log() {
            return tamper_event_log;
        }

        public void setTamper_event_log(boolean tamper_event_log) {
            this.tamper_event_log = tamper_event_log;
        }

        @Override
        public String toString() {
            return "ResultBean{" +
                    "dw_event_log=" + dw_event_log +
                    ", tamper_event_log=" + tamper_event_log +
                    '}';
        }
    }
}
