package com.dinsafer.panel.operate.bean;


import androidx.annotation.Keep;

/**
 * @describe：
 * @date：2023/9/12
 * @author: create by Sydnee
 */

@Keep
public class PirSettingEnabledStatueEntry {


    /**
     * {
     * "gmtime": 1658649822246802200,
     * "pir_setting_enabled": false,
     * "plugin_id": "!OHIY7wn",
     * "sendid": "1234",
     * "stype": "4A"
     * }
     */
    private long gmtime;
    private boolean pirSettingEnabled;
    private String pluginId;
    private String sendid;
    private String stype;


    public long getGmtime() {
        return gmtime;
    }

    public void setGmtime(long gmtime) {
        this.gmtime = gmtime;
    }

    public boolean getPirSettingEnabled() {
        return pirSettingEnabled;
    }

    public void setPirSettingEnabled(boolean pirSettingEnabled) {
        this.pirSettingEnabled = pirSettingEnabled;
    }

    public String getPluginId() {
        return pluginId;
    }

    public void setPluginId(String pluginId) {
        this.pluginId = pluginId;
    }

    public String getSendid() {
        return sendid;
    }

    public void setSendid(String sendid) {
        this.sendid = sendid;
    }

    public String getStype() {
        return stype;
    }

    public void setStype(String stype) {
        this.stype = stype;
    }
}
