package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

@Keep
public class GetAdvancedSettingResult extends BaseHttpEntry {


    /**
     * Result : {"gmtime":23456786789,"ison":false,"offline_sms":false}
     */

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        /**
         * gmtime : 23456786789
         * ison : false
         * offline_sms : false
         */

        private long gmtime;
        private boolean ison;
        private boolean offline_sms;
        private String name;

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        public boolean isIson() {
            return ison;
        }

        public void setIson(boolean ison) {
            this.ison = ison;
        }

        public boolean isOffline_sms() {
            return offline_sms;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public void setOffline_sms(boolean offline_sms) {
            this.offline_sms = offline_sms;
        }
    }
}
