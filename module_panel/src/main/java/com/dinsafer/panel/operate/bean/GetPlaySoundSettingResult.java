package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;
import android.text.TextUtils;

import com.dinsafer.dincore.http.BaseHttpEntry;

@Keep
public class GetPlaySoundSettingResult extends BaseHttpEntry {

    private String Result;

    public String getResult() {
        return Result;
    }

    public void setResult(String result) {
        Result = result;
    }

    public boolean isOn() {
        return !TextUtils.isEmpty(getResult()) ? Boolean.parseBoolean(getResult()) : false;
    }
}
