package com.dinsafer.panel.operate.task;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import androidx.annotation.NonNull;
import android.text.TextUtils;

import com.dinsafer.dssupport.utils.DDLog;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 主机向主机发送CMD指令超时检查
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/7/26 3:05 PM
 */
public class DeviceCmdTimeoutChecker {
    private static final String TAG = DeviceCmdTimeoutChecker.class.getSimpleName();

    // 线程名称
    private static final String THREAD_NAME = "cmd-timeout-checker";
    //任务限定等待时间，即任务超时时间
    private static final long ACK_TIME_OUT = 20 * 1000;
    // handler msg id
    private final static int MSG_TYPE_TIMEOUT = 100;

    private HandlerThread mHandlerThread;
    private Handler mHandler;
    private final HashMap<String, CheckTimeOutTask> mTimeoutTask = new HashMap<>();//超时集合
    private List<DeviceCmdTimeoutCallback> mCmdTimeoutCallbacks;

    public DeviceCmdTimeoutChecker() {
        init();
    }

    private void init() {
        mCmdTimeoutCallbacks = new ArrayList<>();
        mHandlerThread = new HandlerThread(THREAD_NAME);
        mHandlerThread.start();
        mHandler = new Handler(mHandlerThread.getLooper()) {
            @Override
            public void handleMessage(@NonNull Message msg) {
                int type = msg.what;
                if (MSG_TYPE_TIMEOUT == type) {
                    try {
                        final String messageId = (String) msg.obj;
                        onTaskTimeOut(messageId);
                    } catch (Exception e) {
                        DDLog.e(TAG, "Error on handle message!!!");
                        e.printStackTrace();
                    }
                }
            }
        };
    }

    /**
     * 处理超时任务
     *
     * @param messageId sendcmd提交的messageId
     */
    private void onTaskTimeOut(String messageId) {
        final CheckTimeOutTask timeOutTask = mTimeoutTask.get(messageId);
        if (TextUtils.isEmpty(messageId)
                || null == timeOutTask) {
            DDLog.e(TAG, "Got a empty messageId or empty task!!!");
            return;
        }

        DDLog.i(TAG, "发现超时任务, messageId: " + messageId);
        notifyTaskTimeout(timeOutTask);

        removeTaskById(messageId);
    }

    public synchronized void addTask(@NotNull CheckTimeOutTask checkTimeOutTask) {
        Message msg = Message.obtain();
        msg.what = MSG_TYPE_TIMEOUT;
        msg.obj = checkTimeOutTask.getMessageId();
        mTimeoutTask.put(checkTimeOutTask.getMessageId(), checkTimeOutTask);
        mHandler.sendMessageDelayed(msg, ACK_TIME_OUT);
        DDLog.i(TAG, "addTask: " + checkTimeOutTask.getMessageId());
    }

    public synchronized boolean removeTaskById(String messageId) {
        if (TextUtils.isEmpty(messageId)
                || null == mTimeoutTask
                || mTimeoutTask.size() <= 0) {
            return false;
        }

        CheckTimeOutTask task = mTimeoutTask.remove(messageId);
        if (null != task) {
            mHandler.removeMessages(MSG_TYPE_TIMEOUT, task.getMessageId());
            DDLog.i(TAG, "removeTaskById: " + messageId);
            return true;
        }
        return false;
    }

    public synchronized void cleanAllTask() {
        mTimeoutTask.clear();
        mHandler.removeCallbacksAndMessages(null);
    }

    public void addTaskTimeoutCallback(DeviceCmdTimeoutCallback callback) {
        if (null == callback || mCmdTimeoutCallbacks.contains(callback)) {
            return;
        }

        mCmdTimeoutCallbacks.add(callback);
    }

    public void removeTaskTimeoutCallback(DeviceCmdTimeoutCallback callback) {
        if (null != callback && mCmdTimeoutCallbacks.size() > 0) {
            mCmdTimeoutCallbacks.remove(callback);
        }
    }

    private synchronized void notifyTaskTimeout(@NotNull CheckTimeOutTask timeOutTask) {
        if (null == mCmdTimeoutCallbacks || 0 >= mCmdTimeoutCallbacks.size()) {
            return;
        }

        for (DeviceCmdTimeoutCallback mCmdTimeoutCallback : mCmdTimeoutCallbacks) {
            mCmdTimeoutCallback.onCmdTaskTimeout(timeOutTask);
        }
    }

}
