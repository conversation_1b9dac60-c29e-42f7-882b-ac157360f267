package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

import java.io.Serializable;

/**
 * CMS协议信息
 *
 * <AUTHOR>
 * @date 2019-12-03$ 14:52$
 */
@Keep
public class CmsProtocolModel implements Serializable {

    private String protocol_name;
    private CmsProtocolInfo info;
    private String infoStr;

    public String getProtocolName() {
        return protocol_name;
    }

    public void setProtocolName(String protocolName) {
        this.protocol_name = protocolName;
    }

    public CmsProtocolInfo getInfo() {
        return info;
    }

    public void setInfo(CmsProtocolInfo info) {
        this.info = info;
    }

    public String getInfoStr() {
        return infoStr;
    }

    public void setInfoStr(String infoStr) {
        this.infoStr = infoStr;
    }

    public CmsProtocolModel() {
    }

    public CmsProtocolModel(String protocol_name, String infoStr) {
        this.protocol_name = protocol_name;
        this.infoStr = infoStr;
    }

    @Keep
    public static class CmsProtocolInfo implements Serializable {
        private String primary_ip;
        private int primary_port;
        private String secondary_ip;
        private int secondary_port;
        private String account_number;
        private boolean encryption;
        private String encryption_key;
        private String network;

        public String getPrimary_ip() {
            return primary_ip;
        }

        public void setPrimary_ip(String primary_ip) {
            this.primary_ip = primary_ip;
        }

        public int getPrimary_port() {
            return primary_port;
        }

        public void setPrimary_port(int primary_port) {
            this.primary_port = primary_port;
        }

        public String getSecondary_ip() {
            return secondary_ip;
        }

        public void setSecondary_ip(String secondary_ip) {
            this.secondary_ip = secondary_ip;
        }

        public int getSecondary_port() {
            return secondary_port;
        }

        public void setSecondary_port(int secondary_port) {
            this.secondary_port = secondary_port;
        }

        public String getAccount_number() {
            return account_number;
        }

        public void setAccount_number(String account_number) {
            this.account_number = account_number;
        }

        public boolean isEncryption() {
            return encryption;
        }

        public void setEncryption(boolean encryption) {
            this.encryption = encryption;
        }

        public String getEncryption_key() {
            return encryption_key;
        }

        public void setEncryption_key(String encryption_key) {
            this.encryption_key = encryption_key;
        }

        public void setNetwork(String network) {
            this.network = network;
        }

        public String getNetwork() {
            return network;
        }
    }
}
