package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

/**
 * Created by Rinfon on 16/8/17.
 */
@Keep
public class ContactIdResponseEntry extends BaseHttpEntry {


    /**
     * Cmd : SET_CONTACTID
     * Result : {"enable":true,"countrycode":"+86","phone":"10086","contactidcode":"1011","gmtime":123456789}
     */

    private String Cmd;
    private ResultBean Result;

    public String getCmd() {
        return Cmd;
    }

    public void setCmd(String Cmd) {
        this.Cmd = Cmd;
    }

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        /**
         * enable : true
         * countrycode : +86
         * phone : 10086
         * contactidcode : 1011
         * gmtime : 123456789
         */

        private boolean enable;
        private String countrycode;
        private String phone;
        private String contactidcode;

        public boolean isEnable() {
            return enable;
        }

        public void setEnable(boolean enable) {
            this.enable = enable;
        }

        public String getCountrycode() {
            return countrycode;
        }

        public void setCountrycode(String countrycode) {
            this.countrycode = countrycode;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getContactidcode() {
            return contactidcode;
        }

        public void setContactidcode(String contactidcode) {
            this.contactidcode = contactidcode;
        }
    }
}
