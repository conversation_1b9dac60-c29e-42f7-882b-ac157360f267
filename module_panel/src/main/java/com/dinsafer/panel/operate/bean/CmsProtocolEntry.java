package com.dinsafer.panel.operate.bean;


import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-12-03$ 16:35$
 */
@Keep
public class CmsProtocolEntry extends BaseHttpEntry {

    private List<CmsProtocolModel> Result;

    public List<CmsProtocolModel> getResult() {
        return Result;
    }

    public void setResult(List<CmsProtocolModel> result) {
        Result = result;
    }
}
