package com.dinsafer.panel.operate;

import androidx.annotation.Keep;

/**
 * 主机操作常量定义
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/12 3:45 PM
 */
@Keep
public class PanelOperatorConstant {
    public static class ACTION {
        public static final String EVENT_REVICE = "/device/revice";
        public static final String EVENT_RESULT = "/device/result";
        public static final String EVENT_PING = "/device/ping";
        public static final String SIM_ACTION = "/device/sim";
        public static final String OFFLINE = "/device/offline";
        public static final String CMD_ACK = "/device/cmdack";
    }

    @Keep
    public static class CMD {
        public static final String ARM_KEY = "TASK_ARM";
        public static final String DISARM_KEY = "TASK_DISARM";
        public static final String HOMEARM_KEY = "TASK_HOMEARM";
        public static final String TASK_SOS = "TASK_SOS";
        public static final String SET_SMART_PLUG_ENABLE = "SET_SMART_PLUG_ENABLE";
        public static final String SET_SMART_PLUG_ENABLE_ON = "SET_SMART_PLUG_ENABLE_ON";
        public static final String SET_NEW_SMART_PLUG_ON = "SET_NEW_SMART_PLUG_ON";
        public static final String SET_SMART_PLUG_ENABLE_OFF = "SET_SMART_PLUG_ENABLE_OFF";
        public static final String RELAY_ACTION = "RELAY_ACTION";
        /**
         * 获取配件电量值
         */
        public static final String TASK_PLUGIN_STATUS = "TASK_PLUGIN_STATUS";
        public static final String SET_ENTRYDELAY = "SET_ENTRYDELAY_2";
        public static final String SET_HOMEARM = "SET_HOMEARM";
        public static final String SET_DEVICE_TEXT_NEW = "SET_DEVICE_TEXT_NEW";
        public static final String SET_DEVICE_TEXT = "SET_DEVICE_TEXT";
        public static final String SET_INTIMIDATIONALARM_DATA_ON = "SET_INTIMIDATIONALARM_DATA_ON";
        public static final String SET_INTIMIDATIONALARM_ENABLE = "SET_INTIMIDATIONALARM_ENABLE";
        public static final String SET_INTIMIDATIONALARM_PASSWORD = "SET_INTIMIDATIONALARM_PASSWORD";
        public static final String SET_INTIMIDATIONALARM_PUSH_TXT = "SET_INTIMIDATIONALARM_PUSH_TXT";
        public static final String SET_CMS_INFO = "SET_CMS_INFO";
        public static final String SET_READYTOARM = "SET_READYTOARM";
        public static final String SET_EXITDELAY = "SET_EXITDELAY_2";
        public static final String SIRENTIME_KEY = "SET_SIRENTIME";
        //
        public static final String UPDATE_TUYA_NAME = "UPDATE_TUYA_NAME";
        public static final String SET_PLAY_SOUND = "SET_PLAYSOUND";
        public static final String SET_DEVICE_TIMEZONE = "SET_DEVICE_TIMEZONE_2";
        public static final String CMD_SET_BT = "SET_BT";
        public static final String SET_PASSWORD = "RESET_PASSWORD";
        public static final String RESET_DEVICE = "RESET_DEVICE";
        public static final String CMD_SET_4G = "SET_4G_INFO";
        public static final String SET_RESTRICT_MODE_SMS = "SET_OFFLINE_MODE_SMS";
        public static final String SET_CONTACTID = "SET_CONTACTID";
        public static final String DELETE_PLUGIN = "DELETE_PLUGIN";
        public static final String UPDATE_EVENTLIST_SETTING = "UPDATE_EVENTLIST_SETTING_2";
        public static final String ADD_PLUGIN = "ADD_PLUGIN";
        public static final String DELETE_NEWASKPLUGIN = "DELETE_NEWASKPLUGIN";
        public static final String SET_PLUGINDATA = "SET_PLUGINDATA";
        public static final String SET_NEWASKSIRENDATA = "SET_NEWASKSIRENDATA";
        public static final String SET_WIRELESS_SIREN_ADVANCED_SETTING = "SET_WIRELESS_SIREN_ADVANCED_SETTING";
        public static final String ADD_NEWASKPLUGIN = "ADD_NEWASKPLUGIN";
        public static final String SET_NEWASKPLUGINDATA = "SET_NEWASKPLUGINDATA";
        public static final String TEST_SIREN = "TEST_SIREN";
        public static final String TASK_DS_STATUS_OP = "TASK_DS_STATUS_OP";
        public static final String CUSTOMIZE_REMOTE_CONTROL = "CUSTOMIZE_REMOTE_CONTROL";
        public static final String UPDATE_PLUGIN_CONF = "UPDATE_PLUGIN_CONF";
        public static final String SET_PLUGIN_BLOCK = "SET_PLUGIN_BLOCK";
        public static final String SET_CAREMODE_DATA = "SET_CAREMODE_DATA";
        public static final String SET_CAREMODE_PLUGIN = "SET_CAREMODE_PLUGIN";
        public static final String CANCEL_NO_ACTION_SOS = "CANCEL_NO_ACTION_SOS";
        public static final String NO_ACTION_SOS = "NO_ACTION_SOS";
        public static final String GET_HOME_EXCEPTION_ACCESSORY = "TASK_PLUGIN_EXCETION";
        public static final String TASK_INTIMIDATIONALARM_SOS = "TASK_INTIMIDATIONALARM_SOS";
        public static final String TASK_ANTIINTERFER_SOS = "TASK_ANTIINTERFER_SOS";
        /**
         * 暂时，是门磁的防拆报警
         */
        public static final String TASK_FC_SOS = "TASK_FC_SOS";
        public static final String TASK_FC_SOS_PANEL = "TASK_FC_SOS_PANEL";
        public static final String EVENT_LOWERPOWER = "EVENT_LOWERPOWER";
        public static final String EVENT_FULLPOWER = "EVENT_FULLPOWER";
        public static final String PLUGIN_OFFLINE = "PLUGIN_OFFLINE";
        public static final String PLUGIN_ONLINE = "PLUGIN_ONLINE";
        /**
         * 老人模式，配件没响应
         */
        public static final String NO_ACTION_NOTICE = "NO_ACTION_NOTICE";
        public static final String LOW_BATTERY = "LOW_BATTERY";
        public static final String UPDATEING_SYSTEM = "UPDATEING_SYSTEM";
        public static final String UPDATE_AUTH = "UPDATE_AUTH";
        public static final String UP_POWER = "UP_POWER";
        public static final String SYSTEM_UPDATERESET = "SYSTEM_UPDATERESET";
        public static final String SET_DOOR_WINDOW_PUSH_STATUS = "SET_DOOR_WINDOW_PUSH_STATUS";
        /**
         * 可调节灵敏度红外相关设置
         */
        public static final String BYPASS_PLUGIN_5_MIN = "BYPASS_PLUGIN_5_MIN";
        public static final String SET_SENSITIVITY = "SET_SENSITIVITY";
        public static final String EXIT_PIR_SETTING_MODE = "EXIT_PIR_SETTING_MODE";
    }

    @Keep
    public static class KEY {
        public static final String Action = "Action";
        public static final String RESULT = "Result";
        public static final String CMD = "Cmd";
        public static final String STATUS = "Status";
        public static final String MESSAGE_ID = "MessageId";
        public static final int DISARM_STATUS = 2;
        public static final int ARM_STATUS = 0;
        public static final int HOME_STATUS = 1;

    }

    @Keep
    public static class PERMISSION {
        public static final int ADMIN = 30;
        public static final int USER = 20;
        public static final int GUEST = 10;
    }
}
