package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.util.ArrayList;

/**
 * Created by rinfon on 2018/3/28.
 */
@Keep
public class DoorBell extends BaseHttpEntry {


    /**
     * Cmd :
     * Result : [{"id":"5aba0875789df15df7000025","doorbellname":"Visual Doorbell-!DHGepXg","recordtime":1522141301110963083,"img":"Flyd2_kDEFTFbKBH9RMAMnAWmADm"},{"id":"5ab9f379789df15df7000023","doorbellname":"Visual Doorbell-!DHGepXg","recordtime":1522135929362435679,"img":"FmJIxfqJHYYxt-54J0j4sM4VdCeJ"},{"id":"5ab9f1b7789df15df7000022","doorbellname":"Visual Doorbell-!DHGepXg","recordtime":1522135479309064854,"img":"Fl11ugWHZ_xvTqdO4WF7m0MK2uT6"},{"id":"5ab9bfa9789df15df7000021","doorbellname":"Visual Doorbell-!DHGepXg","recordtime":1522122665092608330,"img":"Fu8SHro8UaHubRnkviJUW3W31rgS"},{"id":"5ab9bf88789df15df7000020","doorbellname":"Visual Doorbell-!DHGepXg","recordtime":1522122632613323467,"img":"FipDQfsT8O1up2eEmEGk8zBcQi3n"},{"id":"5ab9bf70789df15df700001f","doorbellname":"Visual Doorbell-!DHGepXg","recordtime":1522122608208153073,"img":"FmGirJynK6t_jVKKFOs7vdBnjN7F"},{"id":"5ab9b86c789df15df700001e","doorbellname":"Visual Doorbell-!DHGepXg","recordtime":1522120812378452041,"img":"FuCBs7vbzAG-DkQFVDttfJKnlH4p"},{"id":"5ab9b839789df15df700001d","doorbellname":"Visual Doorbell-!DHGepXg","recordtime":1522120761982071018,"img":"FvL4eWM85GY5N59HWZSDKCUuyoOS"},{"id":"5ab9b82c789df15df700001c","doorbellname":"Visual Doorbell-!DHGepXg","recordtime":1522120748593823836,"img":"FsWVMsWy0SiPIGwKMT32S2qLccU4"},{"id":"5ab9b809789df15df700001b","doorbellname":"Visual Doorbell-!DHGepXg","recordtime":1522120713780070387,"img":"Fslx3ro8NTypUtT9PTIC8aD50PUc"},{"id":"5ab9b7ab789df15df700001a","doorbellname":"Visual Doorbell-!DHGepXg","recordtime":1522120619669203483,"img":"Fh6FFqOZ54OLIktLAxz4Bu2D9lhC"},{"id":"5ab9b7a1789df15df7000019","doorbellname":"Visual Doorbell-!DHGepXg","recordtime":1522120609345978335,"img":"FvHiISOm0F2A_C-fpdElTWSuBozR"},{"id":"5ab9b798789df15df7000018","doorbellname":"Visual Doorbell-!DHGepXg","recordtime":1522120600258512585,"img":"Ft13jlvtjYh0DQZtoFfWTwjxT3Xw"},{"id":"5ab9b746789df15df7000017","doorbellname":"Visual Doorbell-!DHGepXg","recordtime":1522120518581156809,"img":"FnMLTN2_R29Jy4YEG3UlgRWG5kjp"},{"id":"5ab9b038789df15df7000016","doorbellname":"Visual Doorbell-!DHGepXg","recordtime":1522118712660738766,"img":"Fogm_vL0HpDYBH9QqNAFqsZZhb4V"},{"id":"5ab8c1f3789df15df7000015","doorbellname":"Visual Doorbell-!DHGepXg","recordtime":1522057715028823757,"img":"Fry0IbkXoq8xCHqgh4nRQZaJ6ZXv"},{"id":"5ab8be6b789df15df7000014","doorbellname":"Visual Doorbell-!DHGepXg","recordtime":1522056811110322515,"img":"FkQv_LLT_dRPoiTWj4zrtedH4TYQ"},{"id":"5ab8a7b1789df15df7000013","doorbellname":"Visual Doorbell-!DHGepXg","recordtime":1522050993397178569,"img":"FqLrpmE6OuPdK5HNcL8JG61vVQ2f"},{"id":"5ab87b74789df15df7000012","doorbellname":"Visual Doorbell-!DHGepXg","recordtime":1522039668219228664,"img":"FhhGlMOd-twTG8_wU2-GRSfEFT7m"},{"id":"5ab87b69789df15df7000011","doorbellname":"Visual Doorbell-!DHGepXg","recordtime":1522039657986262465,"img":"FhouxUAfK8xU85-c_Hgg5qq_uSB7"}]
     */

    private String Cmd;
    private ArrayList<ResultBean> Result;

    public String getCmd() {
        return Cmd;
    }

    public void setCmd(String Cmd) {
        this.Cmd = Cmd;
    }

    public ArrayList<ResultBean> getResult() {
        return Result;
    }

    public void setResult(ArrayList<ResultBean> Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        /**
         * id : 5aba0875789df15df7000025
         * doorbellname : Visual Doorbell-!DHGepXg
         * recordtime : 1522141301110963083
         * img : Flyd2_kDEFTFbKBH9RMAMnAWmADm
         */

        private String id;
        private String doorbellname;
        private long recordtime;
        private String img;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getDoorbellname() {
            return doorbellname;
        }

        public void setDoorbellname(String doorbellname) {
            this.doorbellname = doorbellname;
        }

        public long getRecordtime() {
            return recordtime;
        }

        public void setRecordtime(long recordtime) {
            this.recordtime = recordtime;
        }

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }
    }
}
