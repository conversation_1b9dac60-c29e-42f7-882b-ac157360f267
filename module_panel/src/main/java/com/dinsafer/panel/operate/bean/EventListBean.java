package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/12 4:04 PM
 */
@Keep
public class EventListBean {
    private String user;
    private String cmdname;
    private String photo;
    private String type;
    private long time;
    private long duration;
    private String messageid;
    private int result;
    private String cmdType;
    private String pluginid;
    private int category;
    private String subcategory;
    private Data data;

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getCmdname() {
        return cmdname;
    }

    public void setCmdname(String cmdname) {
        this.cmdname = cmdname;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public long getDuration() {
        return duration;
    }

    public void setDuration(long duration) {
        this.duration = duration;
    }

    public String getMessageid() {
        return messageid;
    }

    public void setMessageid(String messageid) {
        this.messageid = messageid;
    }

    public int getResult() {
        return result;
    }

    public void setResult(int result) {
        this.result = result;
    }

    public String getCmdType() {
        return cmdType;
    }

    public void setCmdType(String cmdType) {
        this.cmdType = cmdType;
    }

    public String getPluginid() {
        return pluginid;
    }

    public void setPluginid(String pluginid) {
        this.pluginid = pluginid;
    }

    public int getCategory() {
        return category;
    }

    public void setCategory(int category) {
        this.category = category;
    }

    public String getSubcategory() {
        return subcategory;
    }

    public void setSubcategory(String subcategory) {
        this.subcategory = subcategory;
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    @Keep
    public static class Data {
        private String uid;
        private int newpermission;
        private int oldpermission;
        private boolean powerstatus;

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public int getNewpermission() {
            return newpermission;
        }

        public void setNewpermission(int newpermission) {
            this.newpermission = newpermission;
        }

        public int getOldpermission() {
            return oldpermission;
        }

        public void setOldpermission(int oldpermission) {
            this.oldpermission = oldpermission;
        }

        public boolean isPowerstatus() {
            return powerstatus;
        }

        public void setPowerstatus(boolean powerstatus) {
            this.powerstatus = powerstatus;
        }
    }
}
