package com.dinsafer.panel.operate.bean;

import android.content.Context;
import androidx.annotation.Keep;

import com.dinsafer.panel.R;

/**
 * 主机信息
 * 更新后使用{@link PanelInfoNew}
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/12 5:30 PM
 */
@Deprecated
@Keep
public class PanelInfo {
    /**
     * 网络类型
     */
    public static final int NET_TYPE_LAN = 0;
    public static final int NET_TYPE_WIFI = 1;
    public static final int NET_TYPE_4G = 2;

    private String deviceId; // 数据关联的主机ID
    private boolean isDeviceOffline = true; // 主机是否离线

    private int arm_status;
    private int battery_level;
    private int entrydelay;
    private boolean entrydelay_sound;
    private int exitdelay;
    private boolean exitdelay_sound;
    private String firmware_version;
    private long gmtime;
    private boolean has_device_text_set;
    private boolean has_homearm_set;
    private boolean has_sos_text_set;
    private String ip;
    private boolean is_charge;
    private boolean is_update_mode;
    private boolean knock_over_to_sos;
    private int network;
    private int permission;
    private int sim;
    private int siren_duration;
    private boolean sos;
    private String ssid;
    private String timezone;
    private String token;
    /**
     * countrycode : 49
     * passwd : 31a4fa3e2f4ee1c0a7b05688c3ae9d07
     * uid : eu1607590840023LB6Pn
     * username : 9c6795df32ae2e445d1f16104bc77ec5
     */

    private TuyaBean tuya;

    private long last_no_action_time;
    private int alarm_delay_time;
    private int no_action_time;

    private int wifi_rssi; // 无线信号强度格数
    private String wifi_mac_addr; // 无线mac地址


    /**
     * 主机SIM卡PIN码是否错误
     *
     * @return true：主机PIN码错误
     */
    public boolean isSimPinError() {
        return 3 == getSim();
    }

    /**
     * sim卡故障
     *
     * @return
     */
    public boolean isSimWrong() {
        return getSim() != 0 && getSim() != 1;
    }

    public int getSimStatusText() {
        if (getSim() == 0) {
            return R.string.sim_not_exit;
        } else if (getSim() == 1) {
            return R.string.sim_normal;
        } else {
            return R.string.sim_wrong;
        }
    }

    public boolean isSimNormal() {
        return 1 == getSim();
    }

    public String geNetworkName(Context context) {
        if (getNetwork() == NET_TYPE_LAN) {
            return context.getResources().getString(R.string.advanced_setting_net_cable);
        } else if (getNetwork() == NET_TYPE_4G) {
            return context.getResources().getString(R.string.ap_step_wifi_connect_result_4g);
        } else {
            return getSsid();
        }
    }

    public int getBatteryStatusText() {
        if (isIs_charge()) {
            return R.string.power_connected;
        } else {
            return R.string.power_disconnected;
        }
    }

    public int getArm_status() {
        return arm_status;
    }

    public void setArm_status(int arm_status) {
        this.arm_status = arm_status;
    }

    public int getBattery_level() {
        return battery_level;
    }

    public void setBattery_level(int battery_level) {
        this.battery_level = battery_level;
    }

    public int getEntrydelay() {
        return entrydelay;
    }

    public void setEntrydelay(int entrydelay) {
        this.entrydelay = entrydelay;
    }

    public boolean isEntrydelay_sound() {
        return entrydelay_sound;
    }

    public void setEntrydelay_sound(boolean entrydelay_sound) {
        this.entrydelay_sound = entrydelay_sound;
    }

    public int getExitdelay() {
        return exitdelay;
    }

    public void setExitdelay(int exitdelay) {
        this.exitdelay = exitdelay;
    }

    public boolean isExitdelay_sound() {
        return exitdelay_sound;
    }

    public void setExitdelay_sound(boolean exitdelay_sound) {
        this.exitdelay_sound = exitdelay_sound;
    }

    public String getFirmware_version() {
        return firmware_version;
    }

    public void setFirmware_version(String firmware_version) {
        this.firmware_version = firmware_version;
    }

    public long getGmtime() {
        return gmtime;
    }

    public void setGmtime(long gmtime) {
        this.gmtime = gmtime;
    }

    public boolean isHas_device_text_set() {
        return has_device_text_set;
    }

    public void setHas_device_text_set(boolean has_device_text_set) {
        this.has_device_text_set = has_device_text_set;
    }

    public boolean isHas_homearm_set() {
        return has_homearm_set;
    }

    public void setHas_homearm_set(boolean has_homearm_set) {
        this.has_homearm_set = has_homearm_set;
    }

    public boolean isHas_sos_text_set() {
        return has_sos_text_set;
    }

    public void setHas_sos_text_set(boolean has_sos_text_set) {
        this.has_sos_text_set = has_sos_text_set;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public boolean isIs_charge() {
        return is_charge;
    }

    public void setIs_charge(boolean is_charge) {
        this.is_charge = is_charge;
    }

    public boolean isIs_update_mode() {
        return is_update_mode;
    }

    public void setIs_update_mode(boolean is_update_mode) {
        this.is_update_mode = is_update_mode;
    }

    public boolean isKnock_over_to_sos() {
        return knock_over_to_sos;
    }

    public void setKnock_over_to_sos(boolean knock_over_to_sos) {
        this.knock_over_to_sos = knock_over_to_sos;
    }

    public int getNetwork() {
        return network;
    }

    public void setNetwork(int network) {
        this.network = network;
    }

    public int getPermission() {
        return permission;
    }

    public void setPermission(int permission) {
        this.permission = permission;
    }

    public int getSim() {
        return sim;
    }

    public void setSim(int sim) {
        this.sim = sim;
    }

    public int getSiren_duration() {
        return siren_duration;
    }

    public void setSiren_duration(int siren_duration) {
        this.siren_duration = siren_duration;
    }

    public boolean isSos() {
        return sos;
    }

    public void setSos(boolean sos) {
        this.sos = sos;
    }

    public String getSsid() {
        return ssid;
    }

    public void setSsid(String ssid) {
        this.ssid = ssid;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public TuyaBean getTuya() {
        return tuya;
    }

    public void setTuya(TuyaBean tuya) {
        this.tuya = tuya;
    }

    public static class TuyaBean {
        private String countrycode;
        private String passwd;
        private String uid;
        private String username;

        public String getCountrycode() {
            return countrycode;
        }

        public void setCountrycode(String countrycode) {
            this.countrycode = countrycode;
        }

        public String getPasswd() {
            return passwd;
        }

        public void setPasswd(String passwd) {
            this.passwd = passwd;
        }

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }
    }

    public long getLast_no_action_time() {
        return last_no_action_time;
    }

    public void setLast_no_action_time(long last_no_action_time) {
        this.last_no_action_time = last_no_action_time;
    }

    public int getAlarm_delay_time() {
        return alarm_delay_time;
    }

    public void setAlarm_delay_time(int alarm_delay_time) {
        this.alarm_delay_time = alarm_delay_time;
    }

    public int getNo_action_time() {
        return no_action_time;
    }

    public void setNo_action_time(int no_action_time) {
        this.no_action_time = no_action_time;
    }

    public String getWifi_mac_addr() {
        return wifi_mac_addr;
    }

    public void setWifi_mac_addr(String wifi_mac_addr) {
        this.wifi_mac_addr = wifi_mac_addr;
    }

    public int getWifi_rssi() {
        return wifi_rssi;
    }

    public void setWifi_rssi(int wifi_rssi) {
        this.wifi_rssi = wifi_rssi;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public boolean isDeviceOffline() {
        return isDeviceOffline;
    }

    public void setDeviceOffline(boolean deviceOffline) {
        isDeviceOffline = deviceOffline;
    }
}
