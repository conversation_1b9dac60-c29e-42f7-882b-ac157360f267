package com.dinsafer.panel.operate.task;

import android.os.Message;

import org.jetbrains.annotations.NotNull;

/**
 * 向主机发送CMD的超时任务
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/7/26 3:37 PM
 */
public class CheckTimeOutTask {
    private final String cmd;
    private final String messageId;

    public CheckTimeOutTask(@NotNull String cmd, @NotNull String messageId) {
        this.cmd = cmd;
        this.messageId = messageId;
    }

    public String getCmd() {
        return cmd;
    }

    public String getMessageId() {
        return messageId;
    }

    @Override
    public String toString() {
        return "TimeOutTask{" +
                "cmd='" + cmd + '\'' +
                ", messageId='" + messageId + '\'' +
                '}';
    }
}
