package com.dinsafer.panel.operate.bean;

import java.io.Serializable;

/**
 * Created by rinfon on 2018/5/29.
 */

public class TuyaItem implements Serializable {

    public static final int SMARTPLUGIN_ON = 0;

    public static final int SMARTPLUGIN_OFF = 1;

    public static final int SMARTPLUGIN_LOADING = 2;

    public static final int SMARTPLUGIN_OFFLINE = 3;

    public static final int LIGHT_ON = 4;

    public static final int LIGHT_OFF = 5;

    public static final int LIGHT_LOADING = 6;

    public static final int LIGHT_OFFLINE = 7;

    public static final int IPC = 8;

    public static final int SOS = 9;

    public static final int ADDACCESSORY = 10;

    public static final int TUYA_SMARTPLUGIN_ON = 11;

    public static final int TUYA_SMARTPLUGIN_OFF = 12;

    public static final int TUYA_SMARTPLUGIN_LOADING = 13;

    public static final int TUYA_SMARTPLUGIN_OFFLINE = 14;

    public static final int SMARTPLUGIN = 15;

    public static final int TUYA_SMARTPLUGIN = 16;

    public static final int TUYA_LIGHT = 18;

    public static final int DOOR_SENSOR_OLD = 19;
    public static final int DOOR_SENSOR_NEW = 20;
    public static final int DOOR_SENSOR_VIBRATION_NEW = 21;
    public static final int DOOR_SENSOR_VIBRATION_OLD = 22;

    public static final int MOTION_RECORD = 111;



    String name;
    int type;
    String id;
    String pid;
    //   用于判断在编辑页面的显示选中状态
    boolean isShow = true;
    // 是否是新型ASK 智能插座
    private boolean isAskPlug;
    private String sendid;
    private String stype;

    //    包皮配件使用
    private String decodeid;

    public TuyaItem(String name, int type) {
        this.name = name;
        this.type = type;
    }

    public TuyaItem(String name, String id) {
        this.name = name;
        this.id = id;
    }

    public TuyaItem(String id, String name, int type) {
        this.id = id;
        this.name = name;
        this.type = type;
    }

    public TuyaItem(String id, String name, int type, String pid) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.pid = pid;
    }

    public boolean isTuyaOffline() {
        return type == TuyaItem.TUYA_SMARTPLUGIN_OFFLINE || type == LIGHT_OFFLINE;
    }

    public boolean isTuya() {
        return isTuyaSmartPlugin() || isTuyaColorLight();
    }

    public boolean isTuyaSmartPlugin() {
        return type == TUYA_SMARTPLUGIN_ON || type == TUYA_SMARTPLUGIN_OFF ||
                type == TUYA_SMARTPLUGIN_LOADING || type == TUYA_SMARTPLUGIN_OFFLINE;
    }

    public boolean isTuyaColorLight() {
        return type == LIGHT_ON || type == LIGHT_OFF || type == LIGHT_OFFLINE;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
        if (type == SWITCH_BOT_TWO_ON) {
            setOn(true);
        } else if (type == SWITCH_BOT_TWO_OFF) {
            setOn(false);
        }
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public boolean isShow() {
        return isShow;
    }

    public void setShow(boolean show) {
        isShow = show;
    }

    public boolean isAskPlug() {
        return isAskPlug;
    }

    public void setAskPlug(boolean askPlug) {
        isAskPlug = askPlug;
    }

    public String getSendid() {
        return sendid;
    }

    public void setSendid(String sendid) {
        this.sendid = sendid;
    }

    public String getStype() {
        return stype;
    }

    public void setStype(String stype) {
        this.stype = stype;
    }

    public static final int SWITCH_BOT_NO_STATUS = 19;

    public static final int SWITCH_BOT_ONE = 20;

    public static final int SWITCH_BOT_ONE_OFFLINE = 21;

    public static final int SWITCH_BOT_ONE_LOADING = 22;

    public static final int SWITCH_BOT_TWO_ON = 23;

    public static final int SWITCH_BOT_TWO_OFF = 24;

    public static final int SWITCH_BOT_TWO_OFFLINE = 25;

    public static final int SWITCH_BOT_LOADING_ON = 26;

    public static final int SWITCH_BOT_LOADING_OFF = 27;


    public boolean isSwitchBotOffline() {
        return type == TuyaItem.SWITCH_BOT_ONE_OFFLINE || type == SWITCH_BOT_TWO_OFFLINE;
    }

    public boolean isSwitchBot() {
        return isSwitchBotOne() || isSwitchBotTwo() || type == SWITCH_BOT_LOADING_OFF ||
                type == SWITCH_BOT_LOADING_ON || type == SWITCH_BOT_ONE_LOADING || type == SWITCH_BOT_NO_STATUS;
    }

    public boolean isSwitchBotOne() {
        return type == SWITCH_BOT_ONE ||
                type == SWITCH_BOT_ONE_OFFLINE || type == SWITCH_BOT_ONE_LOADING;
    }

    public boolean isSwitchBotTwo() {
        return type == SWITCH_BOT_TWO_ON || type == SWITCH_BOT_TWO_OFF ||
                type == SWITCH_BOT_TWO_OFFLINE || type == SWITCH_BOT_LOADING_OFF ||
                type == SWITCH_BOT_LOADING_ON;
    }


    public boolean isCannotClick() {
        return type == SWITCH_BOT_ONE_LOADING || type == SWITCH_BOT_NO_STATUS ||
                type == SWITCH_BOT_ONE_OFFLINE ||
                type == SWITCH_BOT_TWO_OFFLINE || type == SWITCH_BOT_LOADING_OFF ||
                type == SWITCH_BOT_LOADING_ON;
    }


    public boolean isSwitchBotLoading() {
        return type == SWITCH_BOT_ONE_LOADING || type == SWITCH_BOT_LOADING_OFF ||
                type == SWITCH_BOT_LOADING_ON;
    }


    private boolean isOn;

    public boolean isOn() {
        return isOn;
    }

    public void setOn(boolean on) {
        isOn = on;
    }


    public TuyaItem(String id, String name, boolean isOn) {
        this.id = id;
        this.name = name;
        this.isOn = isOn;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getDecodeid() {
        return decodeid;
    }

    public void setDecodeid(String decodeid) {
        this.decodeid = decodeid;
    }
}
