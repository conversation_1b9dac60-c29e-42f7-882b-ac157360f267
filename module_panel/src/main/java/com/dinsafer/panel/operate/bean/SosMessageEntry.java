package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

/**
 * Created by Rinfon on 16/8/20.
 */
@Keep
public class SosMessageEntry extends BaseHttpEntry {

    /**
     * enable : false
     * password : true
     * sms : 1234567
     */

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        private boolean enable;
        private boolean password;
        private String sms;

        public boolean isEnable() {
            return enable;
        }

        public void setEnable(boolean enable) {
            this.enable = enable;
        }

        public boolean isPassword() {
            return password;
        }

        public void setPassword(boolean password) {
            this.password = password;
        }

        public String getSms() {
            return sms;
        }

        public void setSms(String sms) {
            this.sms = sms;
        }
    }
}
