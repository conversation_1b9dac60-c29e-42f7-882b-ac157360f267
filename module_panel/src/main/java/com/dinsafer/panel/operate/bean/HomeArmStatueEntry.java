package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.util.List;

/**
 * Created by Rinfon on 16/8/22.
 */
@Keep
public class HomeArmStatueEntry extends BaseHttpEntry {


    /**
     * Result : {"gmtime":23511234342234,"homearmsetting":[{"homearmenable":false,"id":"id_000062","name":"iidd_44111","subcategory":"09","time":1455956195418813239},{"homearmenable":false,"id":"id_0003888800","name":"iidd_11661","subcategory":"0B","time":1455956199408813139},{"homearmenable":false,"id":"id_00061","name":"iidd_6661","subcategory":"0B","time":1455956195409813149},{"homearmenable":true,"id":"id_000388888","name":"iidd_11661","subcategory":"0B","time":1455956198408813139}],"thirdpartyhomearmsetting":[{"homearmenable":false,"id":"id_000062","name":"iidd_44111","subcategory":"09","time":1455956195418813239},{"homearmenable":false,"id":"id_0003888800","name":"iidd_11661","subcategory":"0B","time":1455956199408813139}],"newaskplugin":[{"homearmenable":false,"sendid":"ABCD","stype":"16","name":"newplugin","time":1455956399408813139},{"homearmenable":false,"sendid":"AB12","stype":"17","name":"new2","time":1455956399408813139}]}
     */

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        /**
         * gmtime : 23511234342234
         * homearmsetting : [{"homearmenable":false,"id":"id_000062","name":"iidd_44111","subcategory":"09","time":1455956195418813239},{"homearmenable":false,"id":"id_0003888800","name":"iidd_11661","subcategory":"0B","time":1455956199408813139},{"homearmenable":false,"id":"id_00061","name":"iidd_6661","subcategory":"0B","time":1455956195409813149},{"homearmenable":true,"id":"id_000388888","name":"iidd_11661","subcategory":"0B","time":1455956198408813139}]
         * thirdpartyhomearmsetting : [{"homearmenable":false,"id":"id_000062","name":"iidd_44111","subcategory":"09","time":1455956195418813239},{"homearmenable":false,"id":"id_0003888800","name":"iidd_11661","subcategory":"0B","time":1455956199408813139}]
         * newaskplugin : [{"homearmenable":false,"sendid":"ABCD","stype":"16","name":"newplugin","time":1455956399408813139},{"homearmenable":false,"sendid":"AB12","stype":"17","name":"new2","time":1455956399408813139}]
         */

        private long gmtime;
        private List<HomearmsettingBean> homearmsetting;
        private List<ThirdpartyhomearmsettingBean> thirdpartyhomearmsetting;
        private List<NewaskpluginBean> newaskplugin;

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        public List<HomearmsettingBean> getHomearmsetting() {
            return homearmsetting;
        }

        public void setHomearmsetting(List<HomearmsettingBean> homearmsetting) {
            this.homearmsetting = homearmsetting;
        }

        public List<ThirdpartyhomearmsettingBean> getThirdpartyhomearmsetting() {
            return thirdpartyhomearmsetting;
        }

        public void setThirdpartyhomearmsetting(List<ThirdpartyhomearmsettingBean> thirdpartyhomearmsetting) {
            this.thirdpartyhomearmsetting = thirdpartyhomearmsetting;
        }

        public List<NewaskpluginBean> getNewaskplugin() {
            return newaskplugin;
        }

        public void setNewaskplugin(List<NewaskpluginBean> newaskplugin) {
            this.newaskplugin = newaskplugin;
        }

        @Keep
        public static class HomearmsettingBean {
            /**
             * homearmenable : false
             * id : id_000062
             * name : iidd_44111
             * subcategory : 09
             * time : 1455956195418813239
             */

            private boolean homearmenable;
            private String id;
            private String name;
            private String subcategory;
            private long time;

            public boolean isHomearmenable() {
                return homearmenable;
            }

            public void setHomearmenable(boolean homearmenable) {
                this.homearmenable = homearmenable;
            }

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getSubcategory() {
                return subcategory;
            }

            public void setSubcategory(String subcategory) {
                this.subcategory = subcategory;
            }

            public long getTime() {
                return time;
            }

            public void setTime(long time) {
                this.time = time;
            }
        }

        @Keep
        public static class ThirdpartyhomearmsettingBean {
            /**
             * homearmenable : false
             * id : id_000062
             * name : iidd_44111
             * subcategory : 09
             * time : 1455956195418813239
             */

            private boolean homearmenable;
            private String id;
            private String name;
            private String subcategory;
            private long time;

            public boolean isHomearmenable() {
                return homearmenable;
            }

            public void setHomearmenable(boolean homearmenable) {
                this.homearmenable = homearmenable;
            }

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getSubcategory() {
                return subcategory;
            }

            public void setSubcategory(String subcategory) {
                this.subcategory = subcategory;
            }

            public long getTime() {
                return time;
            }

            public void setTime(long time) {
                this.time = time;
            }
        }

        @Keep
        public static class NewaskpluginBean {
            /**
             * homearmenable : false
             * sendid : ABCD
             * stype : 16
             * name : newplugin
             * time : 1455956399408813139
             */

            private String id;
            private boolean homearmenable;
            private String sendid;
            private String stype;
            private String name;
            private long time;

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public boolean isHomearmenable() {
                return homearmenable;
            }

            public void setHomearmenable(boolean homearmenable) {
                this.homearmenable = homearmenable;
            }

            public String getSendid() {
                return sendid;
            }

            public void setSendid(String sendid) {
                this.sendid = sendid;
            }

            public String getStype() {
                return stype;
            }

            public void setStype(String stype) {
                this.stype = stype;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public long getTime() {
                return time;
            }

            public void setTime(long time) {
                this.time = time;
            }
        }
    }
}
