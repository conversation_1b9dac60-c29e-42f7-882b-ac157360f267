package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.util.List;

/**
 * Created by Rinfon on 16/8/24.
 */
@Keep
public class TimePickerEntry extends BaseHttpEntry {

    /**
     * Cmd :
     * Result : {"options":[0,1,2,3,5,10,15,20,25,30],"time":10}
     */

    private String Cmd;
    /**
     * options : [0,1,2,3,5,10,15,20,25,30]
     * time : 10
     */

    private ResultBean Result;

    public String getCmd() {
        return Cmd;
    }

    public void setCmd(String Cmd) {
        this.Cmd = Cmd;
    }

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        private int time;
        private List<Integer> options;
        private boolean exitdelaysound;

        public int getTime() {
            return time;
        }

        public void setTime(int time) {
            this.time = time;
        }

        public List<Integer> getOptions() {
            return options;
        }

        public void setOptions(List<Integer> options) {
            this.options = options;
        }

        public boolean isExitdelaysound() {
            return exitdelaysound;
        }

        public void setExitdelaysound(boolean exitdelaysound) {
            this.exitdelaysound = exitdelaysound;
        }
    }
}
