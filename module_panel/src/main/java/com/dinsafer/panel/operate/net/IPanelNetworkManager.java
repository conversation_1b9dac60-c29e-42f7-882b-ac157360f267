package com.dinsafer.panel.operate.net;

import com.dinsafer.dincore.activtor.AddPlugsBuilder;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.panel.bean.CustomizeHomeArmResult;
import com.dinsafer.panel.bean.DoorSensorResult;
import com.dinsafer.panel.bean.HomePluginResult;
import com.dinsafer.panel.bean.SecurityPluginResult;
import com.dinsafer.panel.bean.SimplePluginResult;
import com.dinsafer.panel.bean.device.DoorBellDevice;
import com.dinsafer.panel.bean.device.KeypadDevice;
import com.dinsafer.panel.bean.device.OtherDevice;
import com.dinsafer.panel.bean.device.RelayDevice;
import com.dinsafer.panel.bean.device.RemoteControlDevice;
import com.dinsafer.panel.bean.device.SmartButtonDevice;
import com.dinsafer.panel.bean.device.SmartPlugDevice;
import com.dinsafer.panel.bean.device.WirelessSirenDevice;
import com.dinsafer.panel.operate.bean.AppMessageEntry;
import com.dinsafer.panel.operate.bean.CmsProtocolModel;
import com.dinsafer.panel.operate.bean.CommonAccessoriesBean;
import com.dinsafer.panel.operate.bean.ContactIdResponseEntry;
import com.dinsafer.panel.operate.bean.EventListSettingEntry;
import com.dinsafer.panel.operate.bean.FourGInfoEntry;
import com.dinsafer.panel.operate.bean.GetAdvancedSettingResult;
import com.dinsafer.panel.operate.bean.GetArmRulesResponse;
import com.dinsafer.panel.operate.bean.GetCareModeStatusResponse;
import com.dinsafer.panel.operate.bean.GetVoicePromptResponse;
import com.dinsafer.panel.operate.bean.HomePluginEntry;
import com.dinsafer.panel.operate.bean.HomePluginQuantityEntry;
import com.dinsafer.panel.operate.bean.ListPanelTokensResponse;
import com.dinsafer.panel.operate.bean.PanelInfo;
import com.dinsafer.panel.operate.bean.PanelInfoNew;
import com.dinsafer.panel.operate.bean.PirSensitivityEntry;
import com.dinsafer.panel.operate.bean.PirSensitivityResponse;
import com.dinsafer.panel.operate.bean.PirSettingEnabledStatueEntry;
import com.dinsafer.panel.operate.bean.ReadyToArmSwitchStatusEntry;
import com.dinsafer.panel.operate.bean.SimDataEntry;
import com.dinsafer.panel.operate.bean.SosMessageEntry;
import com.dinsafer.panel.operate.bean.SosStatusEntry;
import com.dinsafer.panel.operate.bean.TimePickerEntry;
import com.dinsafer.panel.operate.bean.TimeZoneResponseEntry;
import com.dinsafer.panel.operate.bean.param.AccessoriesInfoParams;
import com.dinsafer.panel.operate.bean.param.CustomizeHomeArmParams;
import com.dinsafer.panel.operate.callback.PanelCallback;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 主机网络请求接口
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/13 6:10 PM
 */
public interface IPanelNetworkManager {

    /**
     * 获取主机信息
     *
     * @param panelId 主机ID
     */
    void requestUpdatePanelInfo(final String homeId, String panelId);

    /**
     * 获取主机信息
     *
     * @param panelId 主机ID
     */
    PanelInfo requestGetPanelInfoSync(final String homeId, String panelId);

    /**
     * 获取设置页配件列表的配件数量
     *
     * @param panelId 主机ID
     */
    void requestGetPluginQuantityInfo(final String homeId, String panelId, @NotNull PanelCallback.NetworkResult<HomePluginQuantityEntry.PluginBean> resultCallback);

    /**
     * 获取首页的配件列表数据
     *
     * @param panelId 主机ID
     */
    void requestUpdatePluginInfo(String panelId, @NotNull PanelCallback.NetworkResult<String> resultCallback);

    /**
     * 获取首页的配件列表数据
     *
     * @param panelId 主机ID
     */
    HomePluginResult requestGetPluginInfoSync(final String panelId);

    /**
     * 获取首页配件列表的状态
     * 结果通过websocket返回
     */
    void requestHomePluginStatus(String panelToken);

    /**
     * 获取SmartButton列表数据
     *
     * @param panelId 主机ID
     */
    void requestSmartButtonList(String panelId, @NotNull PanelCallback.NetworkResult<ArrayList<SmartButtonDevice>> resultCallback);

    /**
     * 获取SmartButton列表数据
     *
     * @param panelId 主机ID
     */
    ArrayList<SmartButtonDevice> requestSmartButtonListSync(String panelId);

    /**
     * 获取SmartPlug列表数据
     *
     * @param panelId 主机ID
     */
    void requestSmartPlugList(String panelId, @NotNull PanelCallback.NetworkResult<ArrayList<SmartPlugDevice>> resultCallback);

    /**
     * 获取SignalRepeaterPlug列表数据
     * @param panelId
     * @return
     */
    ArrayList<SmartPlugDevice> requestSignalRepeaterPlugListSync(String panelId);

    /**
     * 获取SmartPlug列表数据
     *
     * @param panelId 主机ID
     */
    ArrayList<SmartPlugDevice> requestSmartPlugListSync(String panelId);

    /**
     * 获取RollerShutter列表数据
     *
     * @param panelId 主机ID
     */
    ArrayList<RelayDevice> requestRollerShutterListSync(String panelId);

    /**
     * 获取RollerShutter列表数据
     *
     * @param panelId 主机ID
     */
    void requestRollerShutterList(String panelId, @NotNull PanelCallback.NetworkResult<ArrayList<RelayDevice>> resultCallback);

    /**
     * 获取SecurityAccessory Map数据
     *
     * @param panelId 主机ID
     */
    void requestSecurityAccessoryResult(String panelId, @NotNull PanelCallback.NetworkResult<SecurityPluginResult> resultCallback);

    /**
     * 获取SecurityAccessory Map数据
     *
     * @param panelId 主机ID
     */
    SecurityPluginResult requestSecurityAccessoryResultSync(String panelId);

    /**
     * 获取DoorSensor/门窗探测器列表数据
     *
     * @param panelId 主机ID
     */
    void requestDoorSensorResult(String panelId, @NotNull PanelCallback.NetworkResult<DoorSensorResult> resultCallback);

    /**
     * 获取DoorSensor/门窗探测器列表数据
     *
     * @param panelId 主机ID
     */
    DoorSensorResult requestDoorSensorResultSync(String panelId);

    /**
     * 获取WirelessSiren/无线警笛列表数据
     *
     * @param panelId 主机ID
     */
    void requestWirelessSirenResult(String panelId, @NotNull PanelCallback.NetworkResult<SimplePluginResult<WirelessSirenDevice>> resultCallback);

    /**
     * 获取WirelessSiren/无线警笛列表数据
     *
     * @param panelId 主机ID
     */
    SimplePluginResult<WirelessSirenDevice> requestWirelessSirenResultSync(String panelId);

    /**
     * 获取RemoteControl/无线遥控器列表数据
     *
     * @param panelId 主机ID
     */
    void requestRemoteControlResult(String panelId, @NotNull PanelCallback.NetworkResult<SimplePluginResult<RemoteControlDevice>> resultCallback);

    /**
     * 获取RemoteControl/无线遥控器列表数据
     *
     * @param panelId 主机ID
     */
    SimplePluginResult<RemoteControlDevice> requestRemoteControlResultSync(String panelId);

    /**
     * 获取Keypad/无线键盘列表数据
     *
     * @param panelId 主机ID
     */
    void requestKeypadResult(String panelId, @NotNull PanelCallback.NetworkResult<SimplePluginResult<KeypadDevice>> resultCallback);

    /**
     * 获取Keypad/无线键盘列表数据
     *
     * @param panelId 主机ID
     */
    SimplePluginResult<KeypadDevice> requestKeypadResultSync(String panelId);

    /**
     * 获取定制在家布防列表数据
     *
     * @param panelId 主机ID
     */
    void requestCustomizeHomeArmResult(String panelId, @NotNull PanelCallback.NetworkResult<CustomizeHomeArmResult> resultCallback);

    /**
     * 获取延时报警列表数据
     *
     * @param panelId 主机ID
     */
    void requestEntryDelayResult(String panelId, @NotNull PanelCallback.NetworkResult<CustomizeHomeArmResult> resultCallback);

    /**
     * 提交定制在家布防或延时报警配件列表数据
     *
     * @param panelId 主机ID
     */
    void requestConfirmCustomizeHomeArm(String panelId, String panelToken, @NotNull CustomizeHomeArmParams params,
                                        @NotNull PanelCallback.NetworkResult<String> resultCallback);

    /**
     * 获取安全设置-消息设置-获取当前设置的通知语言
     *
     * @param panelId 主机ID
     */
    void requestPanelNotificationLanguage(String panelId, @NotNull PanelCallback.NetworkResult<AppMessageEntry> resultCallback);

    /**
     * 安全设置-消息设置-设置推送语言
     *
     * @param lang 设置的推送语言
     */
    void requestChangePanelNotificationLanguage(String panelToken, String deviceText, String messageId, String lang,
                                                @NotNull PanelCallback.NetworkResult<String> resultCallback);

    /**
     * 安全设置-胁迫报警-获取当前胁迫报警开关状态
     *
     * @param panelId 主机ID
     */
    void requestCurrentPanelIntimidateSosState(String panelId, String panelToken, @NotNull PanelCallback.NetworkResult<SosMessageEntry> resultCallback);

    /**
     * 安全设置-胁迫报警设置-第一次打开取胁迫报警开关
     */
    void requestOpenPanelIntimidateSosFirst(String panelToken, String messageId, String pushliterary, String password,
                                            @NotNull PanelCallback.NetworkResult<String> resultCallback);

    /**
     * 安全设置-胁迫报警设置-修改胁迫报警开关转态（非第一次打开）
     */
    void requestChangePanelIntimidateSosState(String panelToken, String messageId, boolean enable,
                                              @NotNull PanelCallback.NetworkResult<String> resultCallback);

    /**
     * 安全设置-胁迫报警设置-修改胁迫报警密码
     */
    void requestChangePanelIntimidateSosPassword(String panelToken, String messageId, String password,
                                                 @NotNull PanelCallback.NetworkResult<String> resultCallback);

    /**
     * 安全设置-胁迫报警设置-修改胁迫报警报警信息
     */
    void requestChangePanelIntimidateSosMessage(String panelToken, String messageId, String pushliterary,
                                                @NotNull PanelCallback.NetworkResult<String> resultCallback);

    /**
     * 安全设置-设置CID
     */
    void requestGetContactId(String panelId, @NotNull PanelCallback.NetworkResult<ContactIdResponseEntry> resultCallback);

    /**
     * 消息设置-修改CID设置
     */
    void requestChangeContactIdCall(String panelToken, String messageId, boolean enable, String contactIdCode, String countryCode,
                                    String phone, @NotNull PanelCallback.NetworkResult<String> resultCallback);

    /**
     * 获取CMS配置信息
     */
    void requestGetCmsData(String panelId, @NotNull PanelCallback.NetworkResult<List<Map<String, Object>>> resultCallback);

    /**
     * 修改CMS配置信息
     */
    void requestModifyCms(String panelToken, String messageId, CmsProtocolModel protocolModel,
                          @NotNull PanelCallback.NetworkResult<String> resultCallback);

    /**
     * 获取ReadyToArm的开关状态
     */
    void requestCurrentReadyToArmStatus(String panelId, @NotNull PanelCallback.NetworkResult<ReadyToArmSwitchStatusEntry> resultCallback);

    /**
     * 设置ReadyToArm的开关状态
     */
    void requestChangeReadyToArmState(String panelToken, String messageId, boolean enable,
                                      @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 高级设置-获取之前设置的延时布防时间
     */
    void requestGetExitDelayTime(String panelId, @NotNull PanelCallback.NetworkResult<TimePickerEntry> resultCallback);

    /**
     * 高级设置-设置的延时布防时间
     * <p>
     * 2.1.6 增加exitdelaysound设置
     */
    void requestChangeExitDelayTime(String panelToken, String messageId, int time, boolean exitdelaysound,
                                    @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 高级设置-获取之前设置的主机报警鸣响时长
     */
    void requestGetSirenTime(String panelId, @NotNull PanelCallback.NetworkResult<TimePickerEntry> resultCallback);

    /**
     * 高级设置-设置的主机报警鸣响时长
     */
    void requestChangeSirenTime(String panelToken, String messageid, int time,
                                @NotNull final PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 高级设置-设置的主机报警鸣响时长
     */
    void requestUpdateTuyaPluginName(String panelToken, String messageid,
                                     String pluginId, String newName,
                                     @NotNull final PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 高级设置-获取之前设置的值
     */
    void requestPanelAdvancedSetting(String panelId, @NotNull PanelCallback.NetworkResult<GetAdvancedSettingResult> resultCallback);

    /**
     * 高级设置-获取主机布撤防提示音
     */
    void requestGetPlaySoundSetting(String panelId, @NotNull PanelCallback.NetworkResult<Boolean> resultCallback);

    /**
     * 高级设置-设置主机布撤防提示音
     */
    void requestChangePlaySoundSetting(String panelToken, String messageId, boolean isOn,
                                       @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 高级设置-获取设置的时区
     */
    void requestGetPanelTimeZone(String panelId, @NotNull PanelCallback.NetworkResult<TimeZoneResponseEntry> resultCallback);

    /**
     * 高级设置-设置的时区
     */
    void requestChangePanelTimeZone(String panelToken, String messageId, String timezone, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 高级设置-获取4G配置
     */
    void requestGet4GInfo(String panelId, @NotNull PanelCallback.NetworkResult<FourGInfoEntry> resultCallback);

    /**
     * 高级设置-配置4G设置
     */
    void requestSet4GInfo(String panelToken, String messageId, FourGInfoEntry.ResultBean bean,
                          @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 请求主机打开蓝牙
     */
    void requestCallDeviceOpenBle(String panelToken, String messageId, boolean isOpen,
                                  @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 高级设置-修改主机密码
     */
    void requestChangePanelPasswordCall(String panelToken, String messageId, String old_password, String password,
                                        @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 高级设置-重置主机
     */
    void requestResetPanel(String panelToken, String messageId, String password, boolean retainPlugins,
                           @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 获取主机sim卡信息
     */
    void requestGetSimData(String panelId, @NotNull PanelCallback.NetworkResult<SimDataEntry> resultCallback);

    /**
     * 高级设置-设置是否打开限制模式
     */
    void requestChangeRestrictModeState(String panelToken, String messageId, boolean isOn,
                                        @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 重命名主机
     */
    void requestChangePanelName(String panelId, String name, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 获取EventList的数据
     *
     * @param limit     分页条数
     * @param timestamp 记录开始时间戳, <= 0 获取最新的数据
     */
    void requestGetEventList(String panelId, String panelToken, int limit, long timestamp, String filters,
                             @NotNull PanelCallback.NetworkResult<List<Map<String, Object>>> resultCallback);

    /**
     * 获取eventlist的设置
     */
    void requestGetEventListSetting(String panelId, @NotNull PanelCallback.NetworkResult<EventListSettingEntry> resultCallback);

    /**
     * 设置EventList的记录情况
     *
     * @param dwLog     disarm时是否记录门磁的开/合事件
     * @param tamperLog disarm时是否记录主机防拆事件
     */
    void requestUpdateEventListSetting(String panelToken, String messageId, boolean dwLog, boolean tamperLog,
                                       @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 获取主机的报警状态
     */
    void requestGetSosStatus(String panelId, @NotNull PanelCallback.NetworkResult<SosStatusEntry> resultCallback);

    /**
     * 其他类型配件-OtherPluginListFragment
     */
    void requestOtherPluginList(String panelId, @NotNull PanelCallback.NetworkResult<ArrayList<OtherDevice>> resultCallback);

    /**
     * 其他类型配件-OtherPluginListFragment
     */
    ArrayList<OtherDevice> requestOtherPluginListSync(String panelId);

    /**
     * 删除其他类型配件
     */
    void requestDeleteOtherPlugin(String panelToken, String messageId, String plugId,
                                  @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 添加配件
     */
    void requestAddPlugs(String panelToken, AddPlugsBuilder builder, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 添加非官方配件
     */
    void requestAddNotOfficialPlugs(String panelToken, AddPlugsBuilder builder, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    void requestDeletePlugs(String panelToken, String messageid, String plugId, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    void requestDeletePlugs(String panelToken, String messageid, String qrcode, String plugId, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    void requestDeleteASKPlugs(String panelToken, String messageid, String sendid, String stype, String id,
                               @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    void requestChangePlugName(String panelToken, String messageid, String qrcode, String pluginId, String plugName,
                               @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    void requestChangeOtherPlugName(String panelToken, String messageid, String plugId, String plugName,
                                    @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * @param isNewAskPlug 是否新型的ASK智能插座
     * @param sendid       新型插座需要传，旧插座不传
     * @param stype        新型插座需要传，旧插座不传
     */
    void requestSmartPlugsStatusChange(String plugId, String panelToken, int isOn, boolean isNewAskPlug, String sendid,
                                       String stype, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    void requestAskSirenSetting(String panelToken, String messageid, String sendid, String stype, String advancesetting,
                                @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    void requestSirenSetting(String panelToken, String messageid, String pluginid,
                             String sirensetting, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    void requestAddNewASKPlugin(String panelToken, String messageid, String plugin, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    void requestASKPluginModify(String panelToken, String messageid, String datas, String name,
                                @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    void requestListDoorBell(String panelId, String panelToken, @NotNull PanelCallback.NetworkResult<ArrayList<DoorBellDevice>> resultCallback);

    ArrayList<DoorBellDevice> requestListDoorBellSync(String panelId, String panelToken);

    void requestModifyDoorBell(String panelToken, JSONObject data, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    void requestAddDoorBell(String panelToken, JSONObject data, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    void requestDeleteDoorBell(String panelToken, String id, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    void requestDoorBellCap(String panelId, String panelToken, String id, long timestamp, @NotNull PanelCallback.NetworkResult<ArrayList<DoorBellDevice>> resultCallback);

    void requestDeleteDoorBellCap(String id, String panelToken, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    void requestTestSiren(String panelToken, String sendid, String stype, int music, int volume,
                          @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    void requestNotClosedDoorListData(String panelToken, String messageId, String task,
                                      @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    void requestNewAskPlugInfo(String panelId, String panelToken, String stype, String sendid,
                               @NotNull PanelCallback.NetworkResult<String> resultCallback);

    /**
     * 获取自定义五键遥控器控制的智能插座配件列表
     */
    void requestCustomizeSmartPlugs(String panelId, String panelToken, @NotNull PanelCallback.NetworkResult<String> resultCallback);

    /**
     * @param pluginId 配件id
     *                 dtype       如果是新插座，则dtype为10；旧插座，则dtype为3
     *                 stype       旧插座时，sendid为空字符串,目前新插座传3E
     * @param sendid   旧插座时，stype为空字符串
     */
    void requestSetCustomizeSmartPlugs(String panelToken, String messageId, String pluginId, String sendid,
                                       @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 获取SmartButton的配置信息
     */
    void requestSmartButtonConfig(String panelId, String panelToken, String sendid, String stype,
                                  @NotNull PanelCallback.NetworkResult<String> resultCallback);

    /**
     * 修改或保存SmartButton的控制对象配置信息
     *
     * @param smartButtonSendId SmartButton的sendid
     * @param smartButtonStype  SmartButton的stype
     * @param actionConfig      SmartButton执行的指令信息
     */
    void requestUpdateAndSaveSmartButtonConfig(String panelToken, String messageId, String smartButtonSendId, String smartButtonStype,
                                               @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback,
                                               ArrayList<JSONObject> actionConfig);

    /**
     * 修改配件的block模式
     *
     * @param pluginId 涂鸦配件ID
     * @param block    // int，配件屏蔽设置，0：不屏蔽；1、屏蔽tamper alarm；2：屏蔽整个配件；3：chime
     */
    void requestModifyPluginBlock(String panelToken, String messageId, String pluginId, String sendid, String stype,
                                  int block, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 获取caremode数据
     */
    void requestCareModeData(String panelId, String panelToken, @NotNull PanelCallback.NetworkResult<String> resultCallback);

    /**
     * 修改care mode模式开关
     *
     * @param isOn 是否打开care mode
     */
    void requestModifyCareMode(String panelToken, String messageId, boolean isOn, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 修改care mode模式开关
     *
     * @param noActionTime 没有响应时间
     */
    void requestModifyCareModeNoActionTime(String panelToken, String messageId, int noActionTime, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 修改care mode模式开关
     */
    void requestModifyCareModeAlarmTime(String panelToken, String messageId, int alarmTime, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 修改care mode模式开关
     */
    void requestModifyCareModePlugin(String panelToken, String messageId, String data, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 取消老人模式倒计时
     */
    void requestCancelCareModeNoAction(String panelToken, String messageId, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 取消老人模式倒计时
     */
    void requestCareModeNoActionSos(String panelToken, String messageId, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 获取设备转态详情页的异常配件信息指令
     * 结果通过websocket返回
     */
    void requestHomeExceptionAccessoryInfo(String panelToken, String messageId,
                                           @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 获取SmartButton控制配件信息
     */
    void requestSmartButtonTargetPluginList(String panelId, int category, @NotNull PanelCallback.NetworkResult<String> resultCallback);

    /**
     * 操作继电器
     */
    void requestControlRelay(String panelToken, String messageId, String action, String sendId,
                             @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback);

    /**
     * 获取首页配件具体信息-用于跳转改名页
     * 目前请求时根据是否有id来判断新旧配件，id为空为新配件
     *
     * @param panelId  设备ID
     * @param category 新旧配件都需要
     * @param sendid   新配件要
     * @param stype    新配件要
     * @param id       旧配件要
     * @return
     */
    void requestHomePluginDetails(String panelId, int category, String stype, String sendid,
                                  String id, @NotNull PanelCallback.NetworkResult<String> resultCallback);

    void requestDeletePanel(String panelId, @NotNull PanelCallback.NetworkResult<String> resultCallback);

    void requestUpdatePanelFirmware(String panelToken, String messageId, @NotNull PanelCallback.NetworkResult<String> resultCallback);

    void requestSetDoorWindowPushStatus(String deviceToken, String messageid, String pluginId,
                                        String sendid, String stype, boolean pushStatus, @NotNull PanelCallback.NetworkResult<String> resultCallback);

    /**
     * 获取指定主机的布防规则
     */
    void getArmRules(String homeId, String deviceId, PanelCallback.NetworkResult<GetArmRulesResponse.ResultBean> callback);

    /**
     * 获取 care mode 当前状态
     */
    void getCareModeStatus(String homeId, String deviceId, PanelCallback.NetworkResult<GetCareModeStatusResponse.ResultBean> callback);

    /**
     * 获取指定主机的布撤防提示声设置
     */
    void getVoicePrompt(String homeId, String deviceId, PanelCallback.NetworkResult<GetVoicePromptResponse.ResultBean> callback);

    /**
     * 按时间获取配件的信息
     */
    @Nullable
    List<CommonAccessoriesBean> listAccessoriesSync(String homeId, String deviceId, long addTime, List<String> sTypeList, boolean orderDesc);

    /**
     * 查找指定配件的数据
     *
     * @param addTime   时间戳-筛选开始时间
     * @param orderDesc true为倒序
     */
    void searchAccessories(String homeId, String deviceId, List<AccessoriesInfoParams> accessories,
                           long addTime, boolean orderDesc, PanelCallback.NetworkResult<List<CommonAccessoriesBean>> callback);

    /**
     * 获取指定主机的信息
     */
    @Nullable
    List<PanelInfoNew> searchPanelsSync(String homeId, @NotNull List<String> panelIds);

    /**
     * 按时间获取配件的信息
     */
    @Nullable
    List<ListPanelTokensResponse.ResultBean.TokensBean> listPanelTokensSync(String homeID, long addTime, boolean orderDesc, int pageSize);

    /**
     * 临时屏蔽红外触发
     */
    void setPirSettingEnabledStatue(String deviceToken, String homeId, String messageId
            , String pluginId, String sendId, String sType, @NotNull PanelCallback.NetworkResult<PirSettingEnabledStatueEntry> resultCallback);


    /**
     * 设置红外灵敏度设置模式
     */
    void setPirSensitivity(String deviceToken, String homeId, String messageId, String pluginId
            , String sendId, String sType, int sensitivity, @NotNull PanelCallback.NetworkResult<PirSensitivityResponse> callback);

    /**
     * 退出红外设置模式
     */
    void exitPirSettingMode(String deviceToken, String homeId, String messageId, String pluginId
            , String sendId, String sType, @NotNull PanelCallback.NetworkResult<PirSensitivityEntry> callback);
}
