package com.dinsafer.panel.operate.bean.event;

/**
 * Created by Rinfon on 16/9/20.
 */
public class PingUpdataEvent {
    private boolean isCharging;
    private int batteryLevel;
    private int netType;
    private int wifi_rssi;
    private String ipAddress;

    private String originalResult;

    public PingUpdataEvent(boolean isCharging, int batteryLevel, int netType, int wifi_rssi, String ipAddress) {
        this.isCharging = isCharging;
        this.batteryLevel = batteryLevel;
        this.netType = netType;
        this.wifi_rssi = wifi_rssi;
        this.ipAddress = ipAddress;
    }

    public PingUpdataEvent(String originalResult) {
        this.originalResult = originalResult;
    }

    public boolean isCharging() {
        return isCharging;
    }

    public int getBatteryLevel() {
        return batteryLevel;
    }

    public int getNetType() {
        return netType;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public int getWifi_rssi() {
        return wifi_rssi;
    }

    public String getOriginalResult() {
        return originalResult;
    }

    public void setOriginalResult(String originalResult) {
        this.originalResult = originalResult;
    }
}
