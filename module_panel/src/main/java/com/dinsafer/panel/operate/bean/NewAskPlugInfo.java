package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;
@Keep
public class NewAskPlugInfo extends BaseHttpEntry {


    /**
     * Cmd :
     * Result : {"alarm_volume":3,"arm_light":0,"arm_tone":false,"disarm_light":0,"disarm_tone":false,"dtype":10,"entry_delay_enable":false,"home_arm_enable":"false","homearm_light":0,"homearm_tone":false,"id":"!OHGbUEL","life":"false","pid":"","prompt_volume":3,"sendid":"6AEA","sos_light":2,"sos_time":1,"stype":"22","test":true}
     */

    private String Cmd;
    private ResultBean Result;

    public String getCmd() {
        return Cmd;
    }

    public void setCmd(String Cmd) {
        this.Cmd = Cmd;
    }

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        /**
         * alarm_volume : 3
         * arm_light : 0
         * arm_tone : false
         * disarm_light : 0
         * disarm_tone : false
         * dtype : 10
         * entry_delay_enable : false
         * home_arm_enable : false
         * homearm_light : 0
         * homearm_tone : false
         * id : !OHGbUEL
         * life : false
         * pid :
         * prompt_volume : 3
         * sendid : 6AEA
         * sos_light : 2
         * sos_time : 1
         * stype : 22
         * test : true
         */

        private int alarm_volume;
        private int arm_light;
        private boolean arm_tone;
        private int disarm_light;
        private boolean disarm_tone;
        private int dtype;
        private boolean entry_delay_enable;
        private boolean home_arm_enable;
        private int homearm_light;
        private boolean homearm_tone;
        private String id;
        private boolean life;
        private String pid;
        private int prompt_volume;
        private String sendid;
        private int sos_light;
        private int sos_time;
        private String stype;
        private boolean test;
        private boolean keeplive;

        public int getAlarm_volume() {
            return alarm_volume;
        }

        public void setAlarm_volume(int alarm_volume) {
            this.alarm_volume = alarm_volume;
        }

        public int getArm_light() {
            return arm_light;
        }

        public void setArm_light(int arm_light) {
            this.arm_light = arm_light;
        }

        public boolean isArm_tone() {
            return arm_tone;
        }

        public void setArm_tone(boolean arm_tone) {
            this.arm_tone = arm_tone;
        }

        public int getDisarm_light() {
            return disarm_light;
        }

        public void setDisarm_light(int disarm_light) {
            this.disarm_light = disarm_light;
        }

        public boolean isDisarm_tone() {
            return disarm_tone;
        }

        public void setDisarm_tone(boolean disarm_tone) {
            this.disarm_tone = disarm_tone;
        }

        public int getDtype() {
            return dtype;
        }

        public void setDtype(int dtype) {
            this.dtype = dtype;
        }

        public boolean isEntry_delay_enable() {
            return entry_delay_enable;
        }

        public void setEntry_delay_enable(boolean entry_delay_enable) {
            this.entry_delay_enable = entry_delay_enable;
        }

        public boolean getHome_arm_enable() {
            return home_arm_enable;
        }

        public void setHome_arm_enable(boolean home_arm_enable) {
            this.home_arm_enable = home_arm_enable;
        }

        public int getHomearm_light() {
            return homearm_light;
        }

        public void setHomearm_light(int homearm_light) {
            this.homearm_light = homearm_light;
        }

        public boolean isHomearm_tone() {
            return homearm_tone;
        }

        public void setHomearm_tone(boolean homearm_tone) {
            this.homearm_tone = homearm_tone;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public boolean getLife() {
            return life;
        }

        public void setLife(boolean life) {
            this.life = life;
        }

        public String getPid() {
            return pid;
        }

        public void setPid(String pid) {
            this.pid = pid;
        }

        public int getPrompt_volume() {
            return prompt_volume;
        }

        public void setPrompt_volume(int prompt_volume) {
            this.prompt_volume = prompt_volume;
        }

        public String getSendid() {
            return sendid;
        }

        public void setSendid(String sendid) {
            this.sendid = sendid;
        }

        public int getSos_light() {
            return sos_light;
        }

        public void setSos_light(int sos_light) {
            this.sos_light = sos_light;
        }

        public int getSos_time() {
            return sos_time;
        }

        public void setSos_time(int sos_time) {
            this.sos_time = sos_time;
        }

        public String getStype() {
            return stype;
        }

        public void setStype(String stype) {
            this.stype = stype;
        }

        public boolean isTest() {
            return test;
        }

        public void setTest(boolean test) {
            this.test = test;
        }

        public boolean isKeeplive() {
            return keeplive;
        }

        public void setKeeplive(boolean keeplive) {
            this.keeplive = keeplive;
        }
    }
}
