package com.dinsafer.panel.operate.net;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.dinsafer.dincore.activtor.AddPlugsBuilder;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.common.NetKeyConstants;
import com.dinsafer.dincore.http.BaseHttpEntry;
import com.dinsafer.dincore.http.NetWorkException;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.dincore.websocket.HomeWebSocketManager;
import com.dinsafer.dssupport.plugin.PluginConstants;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.bean.CustomizeHomeArmResult;
import com.dinsafer.panel.bean.DoorSensorResult;
import com.dinsafer.panel.bean.HomePluginResult;
import com.dinsafer.panel.bean.SecurityPluginResult;
import com.dinsafer.panel.bean.SimplePluginResult;
import com.dinsafer.panel.bean.device.DoorBellDevice;
import com.dinsafer.panel.bean.device.KeypadDevice;
import com.dinsafer.panel.bean.device.OtherDevice;
import com.dinsafer.panel.bean.device.PanelDevice;
import com.dinsafer.panel.bean.device.RelayDevice;
import com.dinsafer.panel.bean.device.RemoteControlDevice;
import com.dinsafer.panel.bean.device.SmartButtonDevice;
import com.dinsafer.panel.bean.device.SmartPlugDevice;
import com.dinsafer.panel.bean.device.WirelessSirenDevice;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.http.PanelRepository;
import com.dinsafer.panel.operate.bean.AppMessageEntry;
import com.dinsafer.panel.operate.bean.CategoryPlugsEntry;
import com.dinsafer.panel.operate.bean.CmsProtocolEntry;
import com.dinsafer.panel.operate.bean.CmsProtocolModel;
import com.dinsafer.panel.operate.bean.CommonAccessoriesBean;
import com.dinsafer.panel.operate.bean.ContactIdResponseEntry;
import com.dinsafer.panel.operate.bean.DoorBell;
import com.dinsafer.panel.operate.bean.EntryDelayModel;
import com.dinsafer.panel.operate.bean.EventListEntry;
import com.dinsafer.panel.operate.bean.EventListSettingEntry;
import com.dinsafer.panel.operate.bean.FourGInfoEntry;
import com.dinsafer.panel.operate.bean.GetAdvancedSettingResult;
import com.dinsafer.panel.operate.bean.GetArmRulesResponse;
import com.dinsafer.panel.operate.bean.GetCareModeStatusResponse;
import com.dinsafer.panel.operate.bean.GetPlaySoundSettingResult;
import com.dinsafer.panel.operate.bean.GetVoicePromptResponse;
import com.dinsafer.panel.operate.bean.HomeArmStatueEntry;
import com.dinsafer.panel.operate.bean.HomeDeviceInfoEntry;
import com.dinsafer.panel.operate.bean.HomePluginEntry;
import com.dinsafer.panel.operate.bean.HomePluginQuantityEntry;
import com.dinsafer.panel.operate.bean.ListAccessoriesResponse;
import com.dinsafer.panel.operate.bean.ListPanelTokensResponse;
import com.dinsafer.panel.operate.bean.PanelInfo;
import com.dinsafer.panel.operate.bean.PanelInfoNew;
import com.dinsafer.panel.operate.bean.PirSensitivityEntry;
import com.dinsafer.panel.operate.bean.PirSensitivityResponse;
import com.dinsafer.panel.operate.bean.PirSettingEnabledStatueEntry;
import com.dinsafer.panel.operate.bean.ReadyToArmSwitchStatusEntry;
import com.dinsafer.panel.operate.bean.SearchAccessoriesResponse;
import com.dinsafer.panel.operate.bean.SearchPanelsResponse;
import com.dinsafer.panel.operate.bean.SimDataEntry;
import com.dinsafer.panel.operate.bean.SosMessageEntry;
import com.dinsafer.panel.operate.bean.SosStatusEntry;
import com.dinsafer.panel.operate.bean.TimePickerEntry;
import com.dinsafer.panel.operate.bean.TimeZoneResponseEntry;
import com.dinsafer.panel.operate.bean.event.OfflineEvent;
import com.dinsafer.panel.operate.bean.event.UserNetworkEvent;
import com.dinsafer.panel.operate.bean.param.AccessoriesInfoParams;
import com.dinsafer.panel.operate.bean.param.CustomizeHomeArmParams;
import com.dinsafer.panel.operate.callback.PanelCallback;
import com.dinsafer.panel.util.DDSystemUtil;
import com.dinsafer.panel.util.PanelDBHelper;
import com.dinsafer.panel.util.PanelDataTransformer;
import com.dinsafer.panel.util.PanelSecretUtil;

import org.greenrobot.eventbus.EventBus;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import javax.net.ssl.SSLHandshakeException;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 获取主机相关的数据管理
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/13 5:58 PM
 */
public class PanelNetworkRequestManager implements IPanelNetworkManager {
    private static final String TAG = PanelNetworkRequestManager.class.getSimpleName();

    // 网络请求结果码
    private static final int RESPONSE_CODE_SUCCESS = 1; // 成功
    private static final int RESPONSE_CODE_DEVICE_OFFLINE = -24; // 主机离线

    private final PanelRepository mPanelRepository;

    public PanelNetworkRequestManager() {
        mPanelRepository = new PanelRepository();
    }

    /**
     * 统一处理获取配件列表出错的情况
     *
     * @param methodName     当前执行请求的方法名，用于日志输出
     * @param t              异常信息
     * @param resultCallback 结果回调
     */
    private synchronized <T> void onRequestError(String methodName, @NotNull Throwable t, PanelCallback.NetworkResult<T> resultCallback) {
        DDLog.e(TAG, "Error on " + methodName);
        t.printStackTrace();
        if (null != resultCallback) {
            if (t instanceof NetWorkException) {
                NetWorkException exception = (NetWorkException) t;
                resultCallback.onError(exception.getStatus(), exception.getMsgDes());
            } else {
                resultCallback.onError(ErrorCode.DEFAULT, "UNKNOWN ERROR!");
            }
        }
    }

    private synchronized <T extends BaseHttpEntry> boolean checkResponseSuccess(Response<T> response, PanelCallback.NetworkResult<?> resultCallback) {
        if (null == response) {
            if (null != resultCallback) {
                resultCallback.onError(ErrorCode.DEFAULT, "Empty Response!");
            }
            return false;
        }

        T body = response.body();
        if (!response.isSuccessful() || null == body) {
            if (null != resultCallback) {
                resultCallback.onError(ErrorCode.DEFAULT, "Network Error!");
            }
            return false;
        }

        if (body.getStatus() != 1) {
            if (null != resultCallback) {
                resultCallback.onError(body.getStatus(), "Server Error, msg: " + body.getErrorMessage());
            }
            return false;
        }

        return true;
    }

    /**
     * 检查userId和DeviceToken是否为空
     * 如果为空，不能进行网络请求
     *
     * @return true : userId或DeviceToken是否为空
     */
    private <T> boolean checkEmptyUserOrPanel(String panelToken, @NotNull PanelCallback.NetworkResult<T> resultCallback) {
        if (null == UserManager.getInstance().getUser()
                || TextUtils.isEmpty(UserManager.getInstance().getUser().getUid())
                || TextUtils.isEmpty(panelToken)) {
            DDLog.e(TAG, "Error because user or device is null.");
            resultCallback.onError(ErrorCode.PARAM_ERROR, "User or device is null");
            return true;
        }
        return false;
    }

    /**
     * 获取首页主机信息
     *
     * @param deviceId
     */
    @Override
    public void requestUpdatePanelInfo(final String homeId, final String deviceId) {
        DDLog.i(TAG, "requestUpdatePanelInfo, deviceId: " + deviceId);
        if (TextUtils.isEmpty(deviceId)
                || null == PanelManager.getInstance().getPanelOrPluginDevice(deviceId)) {
            DDLog.e(TAG, "panel or panel's id is empty.");
            return;
        }

        mPanelRepository.cancelGetPanelInfo();
        mPanelRepository.searchPanelsAsync(homeId, Collections.singletonList(deviceId), new Callback<SearchPanelsResponse>() {
            @Override
            public void onResponse(@NotNull Call<SearchPanelsResponse> call,
                                   @NotNull Response<SearchPanelsResponse> response) {
                DDLog.i(TAG, "requestGetDeviceInfo - SUCCESS");

                if (response.isSuccessful() && null != response.body()
                        && RESPONSE_CODE_SUCCESS == response.body().getStatus()
                        && null != response.body().getResult()) {
                    List<PanelInfoNew> devices = response.body().getResult().getDevices();
                    if (null != devices && devices.size() > 0) {
                        for (int i = 0; i < devices.size(); i++) {
                            PanelInfoNew panelInfoNew = devices.get(i);
                            if (deviceId.equals(panelInfoNew.getDevice_id())) {
                                PanelDevice panelDevice = PanelManager.getInstance().getPanelDeviceById(deviceId);
                                if (null != panelDevice) {
                                    DDLog.d(TAG, "Device info update and try reconnect websocket.");
                                    if (!panelInfoNew.isOnline()) {
                                        PanelManager.getInstance().getPanelOperator().toCloseWs();
                                    } else {
                                        PanelManager.getInstance().getPanelOperator().connectWs();
                                    }
                                }
                                break;
                            }
                        }
                    }
                }
            }

            @Override
            public void onFailure(Call<SearchPanelsResponse> call, Throwable t) {
                DDLog.e(TAG, "requestGetDeviceInfo - ERROR");
                t.printStackTrace();
                PanelDevice panelDevice = PanelManager.getInstance().getPanelDeviceById(deviceId);
                if (null != panelDevice) {
                    if (t instanceof SSLHandshakeException
                            || t instanceof UnknownHostException) {
                        EventBus.getDefault().post(new UserNetworkEvent());
                    } else if (t instanceof NetWorkException) {
                        NetWorkException netWorkException = (NetWorkException) t;
                        if (RESPONSE_CODE_DEVICE_OFFLINE == netWorkException.getStatus()) {
                            EventBus.getDefault().post(new OfflineEvent());
                        }
                    }
                }
            }
        });
    }

    /**
     * 获取首页主机信息（同步）
     *
     * @param panelId
     */
    @Override
    public PanelInfo requestGetPanelInfoSync(final String homeId, final String panelId) {
        DDLog.i(TAG, "requestGetPanelInfoSync, panelId: " + panelId);
        PanelInfo panelInfo;
        try {
            Response<HomeDeviceInfoEntry> response = mPanelRepository.getPanelInfoSync(homeId, panelId);
            if (null != response.body()
                    && RESPONSE_CODE_SUCCESS == response.body().getStatus()
                    && null != response.body().getResult()) {
                // 成功，通知获取数据成功
                DDLog.d(TAG, "Device info update.");
                panelInfo = response.body().getResult();
                panelInfo.setDeviceId(panelId);
                panelInfo.setDeviceOffline(false);
                PanelDBHelper.savePanelInfo(panelInfo);
            } else {
                DDLog.e(TAG, "Device info is null, resolve from cache.");
                panelInfo = PanelDBHelper.resolveDeviceInfoFromCache(panelId);
                if (null != panelInfo) {
                    panelInfo.setDeviceOffline(true);
                }
            }
        } catch (Exception t) {
            DDLog.e(TAG, "Error on requestGetPanelInfoSync");
            t.printStackTrace();
            panelInfo = PanelDBHelper.resolveDeviceInfoFromCache(panelId);
            if (null != panelInfo) {
                panelInfo.setDeviceOffline(true);
            }
        }
        return panelInfo;
    }

    /**
     * 获取设置页面中配件数据等信息
     *
     * @param deviceId
     */
    @Override
    public void requestGetPluginQuantityInfo(final String homeId, final String deviceId,
                                             @NotNull PanelCallback.NetworkResult<HomePluginQuantityEntry.PluginBean> resultCallback) {
        DDLog.i(TAG, "requestGetPluginQuantityInfo, deviceId: " + deviceId);
        mPanelRepository.cancelGetPluginQuantityEntry();

        mPanelRepository.getPluginQuantityEntry(homeId, deviceId, new Callback<HomePluginQuantityEntry>() {
            @Override
            public void onResponse(@NotNull Call<HomePluginQuantityEntry> call,
                                   @NotNull Response<HomePluginQuantityEntry> response) {
                DDLog.i(TAG, "requestGetPluginQuantityInfo - SUCCESS");
                HomePluginQuantityEntry pluginQuantityEntry = response.body();
                if (null != pluginQuantityEntry
                        && RESPONSE_CODE_SUCCESS == pluginQuantityEntry.getStatus()
                        && null != pluginQuantityEntry.getResult()) {
                    // 成功，通知获取数据成功
                    DDLog.d(TAG, "Plugin quantity info update.");
                    PanelDBHelper.savePanelPluginQuantityInfo(deviceId, pluginQuantityEntry);
                } else {
                    DDLog.e(TAG, "Plugin quantity info ERROR.");
                    pluginQuantityEntry = PanelDBHelper.resolvePanelPluginQuantityInfo(deviceId);
                }

                if (null == pluginQuantityEntry || null == pluginQuantityEntry.getResult()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                    PanelManager.getInstance().getPanelCallbackHelper().onGetPanelPluginQuantity(false);

                } else {
                    resultCallback.onSuccess(pluginQuantityEntry.getResult());
                    PanelManager.getInstance().getPanelCallbackHelper().onGetPanelPluginQuantity(true);
                }
            }

            @Override
            public void onFailure(Call<HomePluginQuantityEntry> call, Throwable t) {
                DDLog.e(TAG, "requestGetPluginQuantityInfo - ERROR");
                t.printStackTrace();
                HomePluginQuantityEntry pluginQuantityEntry = PanelDBHelper.resolvePanelPluginQuantityInfo(deviceId);
                if (null == pluginQuantityEntry || null == pluginQuantityEntry.getResult()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                    PanelManager.getInstance().getPanelCallbackHelper().onGetPanelPluginQuantity(false);

                } else {
                    resultCallback.onSuccess(pluginQuantityEntry.getResult());
                    PanelManager.getInstance().getPanelCallbackHelper().onGetPanelPluginQuantity(true);
                }
            }
        });

        PanelManager.getInstance().getPanelCallbackHelper().onStartGetPanelPluginQuantity();
    }

    /**
     * 获取首页配件信息
     *
     * @param deviceId
     */
    @Override
    public void requestUpdatePluginInfo(final String deviceId, @NotNull PanelCallback.NetworkResult<String> resultCallback) {
        DDLog.i(TAG, "requestGetPluginInfo, deviceId: " + deviceId);

        mPanelRepository.cancelGetHomePluginInfo();

        mPanelRepository.getHomePluginInfo(deviceId, new Callback<ResponseBody>() {
            @Override
            public void onResponse(@NotNull Call<ResponseBody> call, @NotNull Response<ResponseBody> response) {
                DDLog.i(TAG, "requestGetPluginInfo - SUCCESS");
                try {
                    JSONObject jsonObject = new JSONObject(response.body().string());
                    String result = PanelSecretUtil.getReverSC(String.valueOf(jsonObject.get("Result")));
                    DDLog.d(TAG, "plugin info, RESULT: " + result);
                    JSONObject resultJson = new JSONObject(result);
                    HomePluginEntry homePluginEntry = HomePluginEntry.parseFromJson(resultJson);
//                    PanelDBHelper.saveHomePluginInfo(homePluginEntry);
                    if (null != PanelManager.getInstance().getCurrentPanelDevice()) {
                        PanelManager.getInstance().getCurrentPanelDevice().updateHomePluginInfo(homePluginEntry);
                    }
                    PanelManager.getInstance().getPanelCallbackHelper().onGetHomePlugin(true);
                    resultCallback.onSuccess(result);
                } catch (Exception e) {
                    DDLog.e(TAG, "ERROR.");
                    e.printStackTrace();
                    PanelDBHelper.resolveHomePluginInfoFromCache(deviceId);
                    PanelManager.getInstance().getPanelCallbackHelper().onGetHomePlugin(false);
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                }
            }

            @Override
            public void onFailure(Call<ResponseBody> call, Throwable t) {
                DDLog.e(TAG, "requestGetPluginInfo - ERROR");
                t.printStackTrace();
                PanelManager.getInstance().getPanelCallbackHelper().onGetHomePlugin(false);
                resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
            }
        });
        PanelManager.getInstance().getPanelCallbackHelper().onStartGetHomePlugin(deviceId);
    }

    @Override
    public HomePluginResult requestGetPluginInfoSync(final String panelId) {
        DDLog.i(TAG, "requestGetPluginInfoSync, panelId: " + panelId);
        HomePluginResult homePluginResult;
        String resultStr;
        try {
            Response<ResponseBody> response = mPanelRepository.getHomePluginInfoSync(panelId);
            if (null != response.body()) {
                // 成功，通知获取数据成功
                JSONObject jsonObject = new JSONObject(response.body().string());
                resultStr = PanelSecretUtil.getReverSC(String.valueOf(jsonObject.get("Result")));
                PanelDBHelper.saveHomePluginInfo(panelId, resultStr);
                DDLog.d(TAG, "plugin info, RESULT: " + resultStr);
            } else {
                DDLog.e(TAG, "Device plugin info is null, resolve from cache.");
                resultStr = PanelDBHelper.resolveHomePluginInfoFromCache(panelId);
            }
        } catch (Exception t) {
            DDLog.e(TAG, "Error on requestGetPanelInfoSync");
            t.printStackTrace();
            resultStr = PanelDBHelper.resolveHomePluginInfoFromCache(panelId);
        }
        JSONObject params = null;
        try {
            params = new JSONObject(resultStr);
        } catch (Exception e) {
            DDLog.e(TAG, "Error ");
            e.printStackTrace();
        }
        homePluginResult = HomePluginResult.parseFromHomePluginEntry(panelId, params);
        return homePluginResult;
    }

    /**
     * 取消获取主机和配件数量相关信息的网络请求
     */
    public void cancelHomeNetworkRequest() {
        DDLog.d(TAG, "cancelNetworkRequest");
        mPanelRepository.cancelGetPanelInfo();
        mPanelRepository.cancelGetPluginQuantityEntry();
        mPanelRepository.cancelGetHomePluginInfo();
    }

    /**
     * 获取首页的配件状态
     * 需要在连接上主机之后再调用该方法获取首页的配件状态
     */
    @Override
    public void requestHomePluginStatus(String panelToken) {
        DDLog.d(TAG, "requestHomePluginStatus");
        final ArrayList<String> sTtypeTargets = new ArrayList<>(); // stype在这个列表内的，才需要轮询
        sTtypeTargets.add(PluginConstants.TYPE_2C);
        sTtypeTargets.add(PluginConstants.TYPE_2F);
        sTtypeTargets.add(PluginConstants.TYPE_3D);
        sTtypeTargets.add(PluginConstants.TYPE_3E);
        sTtypeTargets.add(PluginConstants.TYPE_4E);
        sTtypeTargets.add(PluginConstants.TYPE_34);
        sTtypeTargets.add(PluginConstants.TYPE_35);
        sTtypeTargets.add(PluginConstants.TYPE_36);
        sTtypeTargets.add(PluginConstants.TYPE_38);
        sTtypeTargets.add(PluginConstants.TYPE_39);
        sTtypeTargets.add(PluginConstants.TYPE_3A);
        sTtypeTargets.add(PluginConstants.TYPE_3B);
        sTtypeTargets.add(PluginConstants.TYPE_3C);
        sTtypeTargets.add(PluginConstants.TYPE_4A);
        mPanelRepository.getHomePluginState(panelToken, sTtypeTargets);
    }

    @Override
    public void requestSmartButtonList(String panelId, @NotNull final PanelCallback.NetworkResult<ArrayList<SmartButtonDevice>> resultCallback) {
        DDLog.d(TAG, "requestSmartButtonList");

        mPanelRepository.getSmartButtonList(panelId, new Callback<ResponseBody>() {
            @Override
            public void onResponse(@NotNull Call<ResponseBody> call, @NotNull Response<ResponseBody> response) {
                DDLog.i(TAG, "Success on requestSmartButtonList");
                if (null == response.body()) {
                    resultCallback.onSuccess(null);
                    return;
                }

                ArrayList<SmartButtonDevice> smartButtonDeviceList = PanelDataTransformer.parseSmartButtonList(panelId, response.body());
                if (null == smartButtonDeviceList) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(smartButtonDeviceList);
                }
            }

            @Override
            public void onFailure(@NotNull Call<ResponseBody> call, @NotNull Throwable t) {
                onRequestError("requestSmartButtonList", t, resultCallback);
            }
        });
    }

    @Override
    public ArrayList<SmartButtonDevice> requestSmartButtonListSync(String panelId) {
        ArrayList<SmartButtonDevice> result = null;
        try {
            Response<ResponseBody> response = mPanelRepository.getSmartButtonListSync(panelId);
            if (null != response.body()) {
                result = PanelDataTransformer.parseSmartButtonList(panelId, response.body());
            }
        } catch (Exception e) {
            DDLog.e(TAG, "Error on requestSmartButtonListSync");
            e.printStackTrace();
        }
        if (null == result) {
            result = new ArrayList<>();
        }
        return result;
    }

    @Override
    public ArrayList<SmartPlugDevice> requestSignalRepeaterPlugListSync(String panelId) {
        ArrayList<SmartPlugDevice> result = null;
        try {
            Response<ResponseBody> response = mPanelRepository.getSpecifyPluginListSync(panelId, PanelConstant.Category.SIGNAL_REPEATER_PLUG);
            if (null != response.body()) {
                result = PanelDataTransformer.parseSignalRepeaterPlugList(panelId, response.body());
            }
        } catch (Exception e) {
            DDLog.e(TAG, "Error on requestSignalRepeaterPlugListSync");
            e.printStackTrace();
        }
        if (null == result) {
            result = new ArrayList<>();
        }
        return result;
    }

    @Override
    public void requestSmartPlugList(String panelId, @NotNull PanelCallback.NetworkResult<ArrayList<SmartPlugDevice>> resultCallback) {
        DDLog.d(TAG, "requestSmartPlugList");
        mPanelRepository.getSpecifyPluginList(panelId, PanelConstant.Category.SMART_PLUGS, new Callback<ResponseBody>() {
            @Override
            public void onResponse(@NotNull Call<ResponseBody> call, @NotNull Response<ResponseBody> response) {
                DDLog.i(TAG, "Success on requestSmartPlugList");
                if (null == response.body()) {
                    resultCallback.onSuccess(null);
                    return;
                }

                ArrayList<SmartPlugDevice> smartPlugDeviceList = PanelDataTransformer.parseSmartPlugList(panelId, response.body());
                if (null == smartPlugDeviceList) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(smartPlugDeviceList);
                }
            }

            @Override
            public void onFailure(@NotNull Call<ResponseBody> call, @NotNull Throwable t) {
                onRequestError("requestSmartPlugList", t, resultCallback);
            }
        });
    }

    @Override
    public ArrayList<SmartPlugDevice> requestSmartPlugListSync(String panelId) {
        ArrayList<SmartPlugDevice> result = null;
        try {
            Response<ResponseBody> response = mPanelRepository.getSpecifyPluginListSync(panelId, PanelConstant.Category.SMART_PLUGS);
            if (null != response.body()) {
                result = PanelDataTransformer.parseSmartPlugList(panelId, response.body());
            }
        } catch (Exception e) {
            DDLog.e(TAG, "Error on requestSmartPlugListSync");
            e.printStackTrace();
        }
        if (null == result) {
            result = new ArrayList<>();
        }
        return result;
    }

    @Override
    public void requestRollerShutterList(String panelId, @NotNull PanelCallback.NetworkResult<ArrayList<RelayDevice>> resultCallback) {
        DDLog.d(TAG, "requestRollerShutterList");
        mPanelRepository.getRelayPluginList(panelId, new Callback<ResponseBody>() {
            @Override
            public void onResponse(@NotNull Call<ResponseBody> call, @NotNull Response<ResponseBody> response) {
                DDLog.i(TAG, "requestRollerShutterList");
                if (null == response.body()) {
                    resultCallback.onSuccess(null);
                    return;
                }

                ArrayList<RelayDevice> relayDevices = PanelDataTransformer.parseRelayList(panelId, response.body());
                if (null == relayDevices) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(relayDevices);
                }
            }

            @Override
            public void onFailure(@NotNull Call<ResponseBody> call, @NotNull Throwable t) {
                onRequestError("requestRollerShutterList", t, resultCallback);
            }
        });
    }

    @Override
    public ArrayList<RelayDevice> requestRollerShutterListSync(String panelId) {
        DDLog.i(TAG, "requestRollerShutterListSync");
        ArrayList<RelayDevice> result = null;
        try {
            Response<ResponseBody> response = mPanelRepository.getRelayPluginListSync(panelId);
            if (null != response.body()) {
                result = PanelDataTransformer.parseRelayList(panelId, response.body());
            }
        } catch (Exception e) {
            DDLog.e(TAG, "Error on requestRollerShutterListSync");
            e.printStackTrace();
        }
        if (null == result) {
            result = new ArrayList<>();
        }
        return result;
    }

    @Override
    public void requestSecurityAccessoryResult(String panelId, @NotNull PanelCallback.NetworkResult<SecurityPluginResult> resultCallback) {
        DDLog.d(TAG, "requestSecurityAccessoryResult");
        mPanelRepository.getSecurityAccessoryList(panelId, PanelConstant.Category.SECURITY, new Callback<ResponseBody>() {
            @Override
            public void onResponse(@NotNull Call<ResponseBody> call, @NotNull Response<ResponseBody> response) {
                DDLog.i(TAG, "Success on: requestSecurityAccessoryResult");
                if (null == response.body()) {
                    resultCallback.onSuccess(null);
                    return;
                }

                SecurityPluginResult securityDevices = PanelDataTransformer.parseSecurityPluginResult(panelId, response.body());
                if (null == securityDevices) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(securityDevices);
                }
            }

            @Override
            public void onFailure(@NotNull Call<ResponseBody> call, @NotNull Throwable t) {
                onRequestError("requestSecurityAccessoryResult", t, resultCallback);
            }
        });
    }

    @Override
    public SecurityPluginResult requestSecurityAccessoryResultSync(String panelId) {
        DDLog.i(TAG, "requestSecurityAccessoryResultSync");
        SecurityPluginResult result = null;
        try {
            Response<ResponseBody> response = mPanelRepository.getSecurityAccessoryListSync(panelId, PanelConstant.Category.SECURITY);
            if (null != response.body()) {
                result = PanelDataTransformer.parseSecurityPluginResult(panelId, response.body());
            }
        } catch (Exception e) {
            DDLog.e(TAG, "Error on requestSecurityAccessoryResultSync");
            e.printStackTrace();
        }
        return result;
    }

    @Override
    public void requestDoorSensorResult(String panelId, @NotNull PanelCallback.NetworkResult<DoorSensorResult> resultCallback) {
        DDLog.d(TAG, "requestDoorSensorResult");
        mPanelRepository.getDoorSensorList(panelId, new Callback<ResponseBody>() {
            @Override
            public void onResponse(@NotNull Call<ResponseBody> call, @NotNull Response<ResponseBody> response) {
                DDLog.i(TAG, "Success on: requestDoorSensorResult");
                if (null == response.body()) {
                    resultCallback.onSuccess(null);
                    return;
                }

                DoorSensorResult doorSensorDevices = PanelDataTransformer.parseDoorSensorResult(panelId, response.body());
                if (null == doorSensorDevices) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(doorSensorDevices);
                }
            }

            @Override
            public void onFailure(@NotNull Call<ResponseBody> call, @NotNull Throwable t) {
                onRequestError("requestDoorSensorResult", t, resultCallback);
            }
        });
    }

    @Override
    public DoorSensorResult requestDoorSensorResultSync(String panelId) {
        DDLog.i(TAG, "requestDoorSensorResultSync");
        DoorSensorResult result = null;
        try {
            Response<ResponseBody> response = mPanelRepository.getDoorSensorListSync(panelId);
            if (null != response.body()) {
                result = PanelDataTransformer.parseDoorSensorResult(panelId, response.body());
            }
        } catch (Exception e) {
            DDLog.e(TAG, "Error on requestDoorSensorResultSync");
            e.printStackTrace();
        }
        return result;
    }

    @Override
    public void requestWirelessSirenResult(String panelId, @NotNull PanelCallback.NetworkResult<SimplePluginResult<WirelessSirenDevice>> resultCallback) {
        DDLog.d(TAG, "requestDoorSensorResult");
        mPanelRepository.getSpecifyPluginList(panelId, PanelConstant.Category.WIRELESS, new Callback<ResponseBody>() {
            @Override
            public void onResponse(@NotNull Call<ResponseBody> call, @NotNull Response<ResponseBody> response) {
                DDLog.i(TAG, "Success on requestWirelessSirenResult");
                if (null == response.body()) {
                    resultCallback.onSuccess(null);
                    return;
                }

                SimplePluginResult<WirelessSirenDevice> wirelessSirenResult = PanelDataTransformer.parseWirelessSirenResult(panelId, response.body());
                if (null == wirelessSirenResult) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(wirelessSirenResult);
                }
            }

            @Override
            public void onFailure(@NotNull Call<ResponseBody> call, @NotNull Throwable t) {
                onRequestError("requestWirelessSirenResult", t, resultCallback);
            }
        });
    }

    @Override
    public SimplePluginResult<WirelessSirenDevice> requestWirelessSirenResultSync(String panelId) {
        DDLog.i(TAG, "requestWirelessSirenResultSync");
        SimplePluginResult<WirelessSirenDevice> result = null;
        try {
            Response<ResponseBody> response = mPanelRepository.getSpecifyPluginListSync(panelId, PanelConstant.Category.WIRELESS);
            if (null != response.body()) {
                result = PanelDataTransformer.parseWirelessSirenResult(panelId, response.body());
            }
        } catch (Exception e) {
            DDLog.e(TAG, "Error on requestWirelessSirenResultSync");
            e.printStackTrace();
        }
        return result;
    }

    @Override
    public void requestRemoteControlResult(String panelId, @NotNull PanelCallback.NetworkResult<SimplePluginResult<RemoteControlDevice>> resultCallback) {
        DDLog.d(TAG, "requestRemoteControlResult");
        mPanelRepository.getSpecifyPluginList(panelId, PanelConstant.Category.RC_KEY, new Callback<ResponseBody>() {
            @Override
            public void onResponse(@NotNull Call<ResponseBody> call, @NotNull Response<ResponseBody> response) {
                DDLog.i(TAG, "Success on requestRemoteControlResult");
                if (null == response.body()) {
                    resultCallback.onSuccess(null);
                    return;
                }

                SimplePluginResult<RemoteControlDevice> remoteControlResult = PanelDataTransformer.parseRemoteControlResult(panelId, response.body());
                if (null == remoteControlResult) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(remoteControlResult);
                }
            }

            @Override
            public void onFailure(@NotNull Call<ResponseBody> call, @NotNull Throwable t) {
                onRequestError("requestRemoteControlResult", t, resultCallback);
            }
        });
    }

    @Override
    public SimplePluginResult<RemoteControlDevice> requestRemoteControlResultSync(String panelId) {
        DDLog.d(TAG, "requestRemoteControlResult");
        SimplePluginResult<RemoteControlDevice> result = null;
        try {
            Response<ResponseBody> response = mPanelRepository.getSpecifyPluginListSync(panelId, PanelConstant.Category.RC_KEY);
            if (null != response.body()) {
                result = PanelDataTransformer.parseRemoteControlResult(panelId, response.body());
            }
        } catch (Exception e) {
            DDLog.e(TAG, "Error on requestRemoteControlResult");
            e.printStackTrace();
        }
        return result;
    }

    @Override
    public void requestKeypadResult(String panelId, @NotNull PanelCallback.NetworkResult<SimplePluginResult<KeypadDevice>> resultCallback) {
        DDLog.d(TAG, "requestKeypadResult");
        mPanelRepository.getSpecifyPluginList(panelId, PanelConstant.Category.RC_KEY, new Callback<ResponseBody>() {
            @Override
            public void onResponse(@NotNull Call<ResponseBody> call, @NotNull Response<ResponseBody> response) {
                DDLog.i(TAG, "Success on requestKeypadResult");
                if (null == response.body()) {
                    resultCallback.onSuccess(null);
                    return;
                }

                SimplePluginResult<KeypadDevice> keypadResult = PanelDataTransformer.parseKeypadResult(panelId, response.body());
                if (null == keypadResult) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(keypadResult);
                }
            }

            @Override
            public void onFailure(@NotNull Call<ResponseBody> call, @NotNull Throwable t) {
                onRequestError("requestKeypadResult", t, resultCallback);
            }
        });
    }

    @Override
    public SimplePluginResult<KeypadDevice> requestKeypadResultSync(String panelId) {
        DDLog.d(TAG, "requestKeypadResultSync");
        SimplePluginResult<KeypadDevice> result = null;
        try {
            Response<ResponseBody> response = mPanelRepository.getSpecifyPluginListSync(panelId, PanelConstant.Category.RC_KEY);
            if (null != response.body()) {
                result = PanelDataTransformer.parseKeypadResult(panelId, response.body());
            }
        } catch (Exception e) {
            DDLog.e(TAG, "Error on requestKeypadResultSync");
            e.printStackTrace();
        }
        return result;
    }

    @Override
    public void requestCustomizeHomeArmResult(String panelId, @NotNull PanelCallback.NetworkResult<CustomizeHomeArmResult> resultCallback) {
        DDLog.d(TAG, "requestCustomizeHomeArmResult");
        mPanelRepository.getCustomizeHomeArmResult(panelId, new Callback<HomeArmStatueEntry>() {
            @Override
            public void onResponse(@NotNull Call<HomeArmStatueEntry> call, @NotNull Response<HomeArmStatueEntry> response) {
                DDLog.i(TAG, "Success on requestCustomizeHomeArmResult");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onSuccess(null);
                    return;
                }

                CustomizeHomeArmResult result = PanelDataTransformer.parseCustomizeHomeArmResult(panelId, response.body());
                if (null == result) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(result);
                }
            }

            @Override
            public void onFailure(@NotNull Call<HomeArmStatueEntry> call, @NotNull Throwable t) {
                onRequestError("requestCustomizeHomeArmResult", t, resultCallback);
            }
        });
    }

    @Override
    public void requestEntryDelayResult(String panelId, @NotNull PanelCallback.NetworkResult<CustomizeHomeArmResult> resultCallback) {
        DDLog.d(TAG, "requestEntryDelayResult");
        mPanelRepository.getEntryDelayResult(panelId, new Callback<EntryDelayModel>() {
            @Override
            public void onResponse(@NotNull Call<EntryDelayModel> call, @NotNull Response<EntryDelayModel> response) {
                DDLog.i(TAG, "Success on requestEntryDelayResult");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onSuccess(null);
                    return;
                }

                CustomizeHomeArmResult result = PanelDataTransformer.parseEntryDelayResult(panelId, response.body());
                if (null == result) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(result);
                }
            }

            @Override
            public void onFailure(@NotNull Call<EntryDelayModel> call, @NotNull Throwable t) {
                onRequestError("requestEntryDelayResult", t, resultCallback);
            }
        });
    }

    @Override
    public void requestConfirmCustomizeHomeArm(String panelId, String panelToken, @NotNull CustomizeHomeArmParams params,
                                               @NotNull PanelCallback.NetworkResult<String> resultCallback) {
        DDLog.d(TAG, "requestConfirmCustomizeHomeArm");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }

        JSONArray jsonArray = new JSONArray();
        JSONArray thirdPartyjsonArray = new JSONArray();
        JSONArray askjsonArray = new JSONArray();

        final String stateKey = params.getStateConfirmKey();

        JSONObject pluginObj;
        if (null != params.getOfficialPlugins() && 0 < params.getOfficialPlugins().size()) {
            for (CustomizeHomeArmParams.PluginInfo officialPlugin : params.getOfficialPlugins()) {
                pluginObj = new JSONObject();
                try {
                    pluginObj.put(NetKeyConstants.NET_KEY_ID, officialPlugin.getId());
                    pluginObj.put(stateKey, officialPlugin.isOpened());
                    jsonArray.put(pluginObj);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }

        if (null != params.getAskPlugins() && 0 < params.getAskPlugins().size()) {
            for (CustomizeHomeArmParams.AskPluginInfo askPluginInfo : params.getAskPlugins()) {
                pluginObj = new JSONObject();
                try {
                    pluginObj.put(NetKeyConstants.NET_KEY_SEND_ID, askPluginInfo.getSendId());
                    pluginObj.put(NetKeyConstants.NET_KEY_S_TYPE, askPluginInfo.getsType());
                    pluginObj.put(stateKey, askPluginInfo.isOpened());
                    askjsonArray.put(pluginObj);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }

        if (null != params.getThirdPartPlugins() && 0 < params.getThirdPartPlugins().size()) {
            for (CustomizeHomeArmParams.PluginInfo thirdPartPlugin : params.getThirdPartPlugins()) {
                pluginObj = new JSONObject();
                try {
                    pluginObj.put(NetKeyConstants.NET_KEY_ID, thirdPartPlugin.getId());
                    pluginObj.put(stateKey, thirdPartPlugin.isOpened());
                    thirdPartyjsonArray.put(pluginObj);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }

        String plugins, askPlugins, thirdPartPlugins;
        if (jsonArray.length() > 0) {
            plugins = jsonArray.toString();
        } else {
            plugins = params.getPluginsStr();
        }
        if (thirdPartyjsonArray.length() > 0) {
            thirdPartPlugins = thirdPartyjsonArray.toString();
        } else {
            thirdPartPlugins = params.getThirdPartPluginsStr();
        }
        if (askjsonArray.length() > 0) {
            askPlugins = askjsonArray.toString();
        } else {
            askPlugins = params.getAskPluginsStr();
        }

        mPanelRepository.getConfirmCustomizeHomeArmResult(UserManager.getInstance().getUser().getUid(),
                panelToken, params.getMessageId(),
                params.getCmd(), params.getEntryDelayTime(), plugins, thirdPartPlugins,
                askPlugins, params.isEntryDelaySoundEnable()
                , new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestConfirmCustomizeHomeArm");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body().getResult());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestConfirmCustomizeHomeArm", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestPanelNotificationLanguage(String panelId, @NotNull PanelCallback.NetworkResult<AppMessageEntry> resultCallback) {
        DDLog.d(TAG, "requestPanelNotificationLanguage");
        mPanelRepository.getPanelNotificationLanguage(panelId, new Callback<AppMessageEntry>() {
            @Override
            public void onResponse(@NotNull Call<AppMessageEntry> call, @NotNull Response<AppMessageEntry> response) {
                DDLog.i(TAG, "Success on requestPanelNotificationLanguage");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(response.body());
                }
            }

            @Override
            public void onFailure(@NotNull Call<AppMessageEntry> call, @NotNull Throwable t) {
                onRequestError("requestPanelNotificationLanguage", t, resultCallback);
            }
        });
    }

    @Override
    public void requestChangePanelNotificationLanguage(String panelToken, String deviceText, String messageId, String lang,
                                                       @NotNull PanelCallback.NetworkResult<String> resultCallback) {
        DDLog.d(TAG, "requestChangePanelNotificationLanguage");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }

        final Callback<StringResponseEntry> callback = new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on requestChangePanelNotificationLanguage");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(response.body().getResult());
                }
            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                onRequestError("requestChangePanelNotificationLanguage", t, resultCallback);
            }
        };

        String[] currentVersions = PanelManager.getInstance().getCurrentPanelDevice()
                .getPanelInfo().getFirmware_version().split("/");
        if (-1 != DDSystemUtil.VersionComparison(currentVersions[0], PanelConstant.MIN_CHANGEMESSAGE_VERSION)) {
            mPanelRepository.setPanelNotificationLanguage(
                    UserManager.getInstance().getUser().getUid(),
                    panelToken,
                    messageId, lang, callback);
        } else {
            mPanelRepository.setPanelNotificationLanguage(
                    UserManager.getInstance().getUser().getUid(),
                    panelToken,
                    messageId, deviceText, lang, callback);
        }
    }

    @Override
    public void requestCurrentPanelIntimidateSosState(String panelId, String panelToken,
                                                      @NotNull PanelCallback.NetworkResult<SosMessageEntry> resultCallback) {
        DDLog.d(TAG, "requestCurrentPanelIntimidateSosState");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getPanelIntimidateSosState(panelId, new Callback<SosMessageEntry>() {
            @Override
            public void onResponse(@NotNull Call<SosMessageEntry> call, @NotNull Response<SosMessageEntry> response) {
                DDLog.i(TAG, "Success on requestCurrentPanelIntimidateSosState");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(response.body());
                }
            }

            @Override
            public void onFailure(@NotNull Call<SosMessageEntry> call, @NotNull Throwable t) {
                onRequestError("requestCurrentPanelIntimidateSosState", t, resultCallback);
            }
        });
    }

    @Override
    public void requestOpenPanelIntimidateSosFirst(String panelToken, String messageId, String pushliterary, String password,
                                                   @NotNull PanelCallback.NetworkResult<String> resultCallback) {
        DDLog.d(TAG, "requestOpenPanelIntimidateSosFirst");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.openPanelIntimidateSosFirst(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, pushliterary, password, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestOpenPanelIntimidateSosFirst");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body().getResult());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestOpenPanelIntimidateSosFirst", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestChangePanelIntimidateSosState(String panelToken, String messageId, boolean enable, @NotNull PanelCallback.NetworkResult<String> resultCallback) {
        DDLog.d(TAG, "requestChangePanelIntimidateSosState");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.changePanelIntimidateSosState(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, enable, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestChangePanelIntimidateSosState");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body().getResult());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestChangePanelIntimidateSosState", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestChangePanelIntimidateSosPassword(String panelToken, String messageId, String password,
                                                        @NotNull PanelCallback.NetworkResult<String> resultCallback) {
        DDLog.d(TAG, "requestChangePanelIntimidateSosPassword");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.changePanelIntimidateSosPassword(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, password, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestChangePanelIntimidateSosPassword");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body().getResult());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestChangePanelIntimidateSosPassword", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestChangePanelIntimidateSosMessage(String panelToken, String messageId, String pushliterary,
                                                       @NotNull PanelCallback.NetworkResult<String> resultCallback) {
        DDLog.d(TAG, "requestChangePanelIntimidateSosMessage");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.changePanelIntimidateSosMessage(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, pushliterary, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestChangePanelIntimidateSosMessage");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body().getResult());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestChangePanelIntimidateSosMessage", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestGetContactId(String panelId, @NotNull PanelCallback.NetworkResult<ContactIdResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestGetContactId");
        mPanelRepository.getContactId(panelId, new Callback<ContactIdResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<ContactIdResponseEntry> call, @NotNull Response<ContactIdResponseEntry> response) {
                DDLog.i(TAG, "Success on requestGetContactId");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(response.body());
                }
            }

            @Override
            public void onFailure(@NotNull Call<ContactIdResponseEntry> call, @NotNull Throwable t) {
                onRequestError("requestGetContactId", t, resultCallback);
            }
        });
    }

    @Override
    public void requestChangeContactIdCall(String panelToken, String messageId, boolean enable, String contactIdCode, String countryCode,
                                           String phone, @NotNull PanelCallback.NetworkResult<String> resultCallback) {
        DDLog.d(TAG, "requestChangeContactIdCall");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.updateContactIdCall(
                UserManager.getInstance().getUser().getUid(),
                panelToken, messageId, enable,
                contactIdCode, countryCode, phone, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestChangeContactIdCall");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body().getResult());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestChangeContactIdCall", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestGetCmsData(String panelId, @NotNull PanelCallback.NetworkResult<List<Map<String, Object>>> resultCallback) {
        DDLog.d(TAG, "requestGetCmsData");
        mPanelRepository.getCmsData(panelId, new Callback<CmsProtocolEntry>() {
            @Override
            public void onResponse(@NotNull Call<CmsProtocolEntry> call, @NotNull Response<CmsProtocolEntry> response) {
                DDLog.i(TAG, "Success on requestGetCmsData");
                ArrayList<Map<String, Object>> cmsData = PanelDataTransformer.parseCmsData(response.body());
                if (null == cmsData) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(cmsData);
                }
            }

            @Override
            public void onFailure(@NotNull Call<CmsProtocolEntry> call, @NotNull Throwable t) {
                onRequestError("requestGetCmsData", t, resultCallback);
            }
        });
    }

    @Override
    public void requestModifyCms(String panelToken, String messageId, CmsProtocolModel protocolModel,
                                 @NotNull PanelCallback.NetworkResult<String> resultCallback) {
        DDLog.d(TAG, "requestModifyCms");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.modifyCms(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, protocolModel, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestModifyCms");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body().getResult());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestModifyCms", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestCurrentReadyToArmStatus(String panelId, @NotNull PanelCallback.NetworkResult<ReadyToArmSwitchStatusEntry> resultCallback) {
        DDLog.d(TAG, "requestCurrentReadyToArmStatus");
        mPanelRepository.getReadyToArmStatus(panelId, new Callback<ReadyToArmSwitchStatusEntry>() {
            @Override
            public void onResponse(@NotNull Call<ReadyToArmSwitchStatusEntry> call, @NotNull Response<ReadyToArmSwitchStatusEntry> response) {
                DDLog.i(TAG, "Success on requestCurrentReadyToArmStatus");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(response.body());
                }
            }

            @Override
            public void onFailure(@NotNull Call<ReadyToArmSwitchStatusEntry> call, @NotNull Throwable t) {
                onRequestError("requestCurrentReadyToArmStatus", t, resultCallback);
            }
        });
    }

    @Override
    public void requestChangeReadyToArmState(String panelToken, String messageId, boolean enable, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestChangeReadyToArmState");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.ChangeReadyToArmState(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, enable, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestChangeReadyToArmState");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestChangeReadyToArmState", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestGetExitDelayTime(String panelId, @NotNull PanelCallback.NetworkResult<TimePickerEntry> resultCallback) {
        DDLog.d(TAG, "requestGetExitDelayTime");
        mPanelRepository.getExitDelayTime(panelId, new Callback<TimePickerEntry>() {
            @Override
            public void onResponse(@NotNull Call<TimePickerEntry> call, @NotNull Response<TimePickerEntry> response) {
                DDLog.i(TAG, "Success on requestGetExitDelayTime");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(response.body());
                }
            }

            @Override
            public void onFailure(@NotNull Call<TimePickerEntry> call, @NotNull Throwable t) {
                onRequestError("requestGetExitDelayTime", t, resultCallback);
            }
        });
    }

    @Override
    public void requestChangeExitDelayTime(String panelToken, String messageId, int time, boolean exitdelaysound,
                                           @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestChangeExitDelayTime");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.changeExitDelayTime(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, time, exitdelaysound, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestChangeExitDelayTime");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestChangeExitDelayTime", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestGetSirenTime(String panelId, @NotNull PanelCallback.NetworkResult<TimePickerEntry> resultCallback) {
        DDLog.d(TAG, "requestGetSirenTime");
        mPanelRepository.getSirenTime(panelId, new Callback<TimePickerEntry>() {
            @Override
            public void onResponse(@NotNull Call<TimePickerEntry> call, @NotNull Response<TimePickerEntry> response) {
                DDLog.i(TAG, "Success on requestGetSirenTime");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(response.body());
                }
            }

            @Override
            public void onFailure(@NotNull Call<TimePickerEntry> call, @NotNull Throwable t) {
                onRequestError("requestGetSirenTime", t, resultCallback);
            }
        });
    }

    @Override
    public void requestChangeSirenTime(String panelToken, String messageId, int time,
                                       @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestChangeSirenTime");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.changeSirenTime(
                UserManager.getInstance().getUser().getUid(),
                panelToken, messageId, time,
                new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestChangeSirenTime");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestChangeSirenTime", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestUpdateTuyaPluginName(String panelToken, String messageid, String pluginId, String newName,
                                            @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestChangeSirenTime");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.updateTuyaPluginName(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageid, pluginId, newName,
                new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestUpdateTuyaPluginName");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestUpdateTuyaPluginName", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestPanelAdvancedSetting(String panelId, @NotNull PanelCallback.NetworkResult<GetAdvancedSettingResult> resultCallback) {
        DDLog.d(TAG, "requestPanelAdvancedSetting");
        mPanelRepository.getAdvancedSetting(panelId, new Callback<GetAdvancedSettingResult>() {
            @Override
            public void onResponse(@NotNull Call<GetAdvancedSettingResult> call, @NotNull Response<GetAdvancedSettingResult> response) {
                DDLog.i(TAG, "Success on requestPanelAdvancedSetting");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(response.body());
                }
            }

            @Override
            public void onFailure(@NotNull Call<GetAdvancedSettingResult> call, @NotNull Throwable t) {
                onRequestError("requestPanelAdvancedSetting", t, resultCallback);
            }
        });
    }

    @Override
    public void requestGetPlaySoundSetting(String panelId, @NotNull PanelCallback.NetworkResult<Boolean> resultCallback) {
        DDLog.d(TAG, "requestGetPlaySoundSetting");
        mPanelRepository.getPlaySoundSetting(panelId, new Callback<GetPlaySoundSettingResult>() {
            @Override
            public void onResponse(@NotNull Call<GetPlaySoundSettingResult> call, @NotNull Response<GetPlaySoundSettingResult> response) {
                DDLog.i(TAG, "Success on requestGetPlaySoundSetting");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(response.body().isOn());
                }
            }

            @Override
            public void onFailure(@NotNull Call<GetPlaySoundSettingResult> call, @NotNull Throwable t) {
                onRequestError("requestGetPlaySoundSetting", t, resultCallback);
            }
        });
    }

    @Override
    public void requestChangePlaySoundSetting(String panelToken, String messageId, boolean isOn,
                                              @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestChangePlaySoundSetting");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.setPlaySoundSetting(
                UserManager.getInstance().getUser().getUid(),
                panelToken, messageId, isOn,
                new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestChangePlaySoundSetting");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestChangePlaySoundSetting", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestGetPanelTimeZone(String panelId, @NotNull PanelCallback.NetworkResult<TimeZoneResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestGetPanelTimeZone");
        mPanelRepository.getPanelTimeZone(panelId, new Callback<TimeZoneResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<TimeZoneResponseEntry> call, @NotNull Response<TimeZoneResponseEntry> response) {
                DDLog.i(TAG, "Success on requestGetPanelTimeZone");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(response.body());
                }
            }

            @Override
            public void onFailure(@NotNull Call<TimeZoneResponseEntry> call, @NotNull Throwable t) {
                onRequestError("requestGetPanelTimeZone", t, resultCallback);
            }
        });
    }

    @Override
    public void requestChangePanelTimeZone(String panelToken, String messageId, String timezone, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestChangePanelTimeZone");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.changePanelTimeZone(
                UserManager.getInstance().getUser().getUid(),
                panelToken, messageId, timezone,
                new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestChangePanelTimeZone");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestChangePanelTimeZone", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestGet4GInfo(String panelId, @NotNull PanelCallback.NetworkResult<FourGInfoEntry> resultCallback) {
        DDLog.d(TAG, "requestGet4GInfo");
        mPanelRepository.get4GInfo(panelId, new Callback<FourGInfoEntry>() {
            @Override
            public void onResponse(@NotNull Call<FourGInfoEntry> call, @NotNull Response<FourGInfoEntry> response) {
                DDLog.i(TAG, "Success on requestGet4GInfo");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(response.body());
                }
            }

            @Override
            public void onFailure(@NotNull Call<FourGInfoEntry> call, @NotNull Throwable t) {
                onRequestError("requestGet4GInfo", t, resultCallback);
            }
        });
    }

    @Override
    public void requestSet4GInfo(String panelToken, String messageId, FourGInfoEntry.ResultBean bean,
                                 @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestSet4GInfo");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.set4GInfo(
                UserManager.getInstance().getUser().getUid(),
                panelToken, messageId, bean,
                new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestSet4GInfo");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestSet4GInfo", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestCallDeviceOpenBle(String panelToken, String messageId, boolean isOpen,
                                         @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestCallDeviceOpenBle");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.callDeviceOpenBle(
                UserManager.getInstance().getUser().getUid(),
                panelToken, messageId, isOpen,
                new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestCallDeviceOpenBle");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestCallDeviceOpenBle", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestChangePanelPasswordCall(String panelToken, String messageId, String old_password, String password,
                                               @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestChangePanelPasswordCall");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.changeDevicePassword(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, old_password, password, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestChangePanelPasswordCall");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestChangePanelPasswordCall", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestResetPanel(String panelToken, String messageId, String password, boolean retainPlugins,
                                  @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestResetPanel");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.resetPanel(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, password, retainPlugins, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestResetPanel");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestResetPanel", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestGetSimData(String panelId, @NotNull PanelCallback.NetworkResult<SimDataEntry> resultCallback) {
        DDLog.d(TAG, "requestGetSimData");
        mPanelRepository.getSimData(panelId, new Callback<SimDataEntry>() {
            @Override
            public void onResponse(@NotNull Call<SimDataEntry> call, @NotNull Response<SimDataEntry> response) {
                DDLog.i(TAG, "Success on requestGetSimData");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(response.body());
                }
            }

            @Override
            public void onFailure(@NotNull Call<SimDataEntry> call, @NotNull Throwable t) {
                onRequestError("requestGetSimData", t, resultCallback);
            }
        });
    }

    @Override
    public void requestChangeRestrictModeState(String panelToken, String messageId, boolean isOn,
                                               @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestChangeRestrictModeState");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.changeRestrictModeState(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, isOn, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestChangeRestrictModeState");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestChangeRestrictModeState", t, resultCallback);
                    }
                });
    }


    @Override
    public void requestChangePanelName(String panelId, String name, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestChangePanelName");
        mPanelRepository.changePanelName(panelId, name, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on requestChangePanelName");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(response.body());
                }
            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                onRequestError("requestChangePanelName", t, resultCallback);
            }
        });
    }

    @Override
    public void requestGetEventList(String panelId, String panelToken, int limit, long timestamp, String filters,
                                    @NotNull PanelCallback.NetworkResult<List<Map<String, Object>>> resultCallback) {
        DDLog.d(TAG, "requestGetEventList");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        if (timestamp <= 0) {
            timestamp = System.currentTimeMillis() * 1000 * 1000;
        }
        if (limit <= 1) {
            limit = 20;
        }
        mPanelRepository.getEventListData(panelId, limit, timestamp, filters, new Callback<EventListEntry>() {
            @Override
            public void onResponse(@NotNull Call<EventListEntry> call,
                                   @NotNull Response<EventListEntry> response) {
                DDLog.i(TAG, "Success on requestGetEventList");
                List<Map<String, Object>> eventList = PanelDataTransformer.parseEventList(response.body());
                if (null == eventList) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(eventList);
                }
            }

            @Override
            public void onFailure(@NotNull Call<EventListEntry> call, @NotNull Throwable t) {
                onRequestError("requestGetEventList", t, resultCallback);
            }
        });
    }

    @Override
    public void requestGetEventListSetting(String panelId, @NotNull PanelCallback.NetworkResult<EventListSettingEntry> resultCallback) {
        DDLog.d(TAG, "requestGetEventListSetting");
        mPanelRepository.getEventListSetting(panelId, new Callback<EventListSettingEntry>() {
            @Override
            public void onResponse(@NotNull Call<EventListSettingEntry> call,
                                   @NotNull Response<EventListSettingEntry> response) {
                DDLog.i(TAG, "Success on requestGetEventListSetting");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(response.body());
                }
            }

            @Override
            public void onFailure(@NotNull Call<EventListSettingEntry> call, @NotNull Throwable t) {
                onRequestError("requestGetEventListSetting", t, resultCallback);
            }
        });
    }

    @Override
    public void requestUpdateEventListSetting(String panelToken, String messageId, boolean dwLog, boolean tamperLog,
                                              @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestUpdateEventListSetting");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.updateEventListSetting(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, dwLog, tamperLog, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestUpdateEventListSetting");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestUpdateEventListSetting", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestGetSosStatus(String panelId, @NotNull PanelCallback.NetworkResult<SosStatusEntry> resultCallback) {
        DDLog.d(TAG, "requestGetSosStatus");
        mPanelRepository.getSosStatus(panelId, new Callback<SosStatusEntry>() {
            @Override
            public void onResponse(@NotNull Call<SosStatusEntry> call, @NotNull Response<SosStatusEntry> response) {
                DDLog.i(TAG, "Success on requestGetSosStatus");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(response.body());
                }
            }

            @Override
            public void onFailure(@NotNull Call<SosStatusEntry> call, @NotNull Throwable t) {
                onRequestError("requestGetSosStatus", t, resultCallback);
            }
        });
    }

    @Override
    public void requestOtherPluginList(String panelId, @NotNull PanelCallback.NetworkResult<ArrayList<OtherDevice>> resultCallback) {
        DDLog.d(TAG, "requestOtherPluginList");
        mPanelRepository.getCategoryPlugsCallV3(panelId, PanelConstant.Category.SECURITY, new Callback<CategoryPlugsEntry>() {
            @Override
            public void onResponse(@NotNull Call<CategoryPlugsEntry> call, @NotNull Response<CategoryPlugsEntry> response) {
                DDLog.i(TAG, "requestOtherPluginList");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onSuccess(null);
                    return;
                }

                ArrayList<OtherDevice> otherDevices = PanelDataTransformer.parseOtherPluginList(panelId, response.body());
                if (null == otherDevices) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(otherDevices);
                }
            }

            @Override
            public void onFailure(@NotNull Call<CategoryPlugsEntry> call, @NotNull Throwable t) {
                onRequestError("requestOtherPluginList", t, resultCallback);
            }
        });
    }

    @Override
    public ArrayList<OtherDevice> requestOtherPluginListSync(String panelId) {
        DDLog.i(TAG, "requestOtherPluginListSync");
        ArrayList<OtherDevice> result = null;
        try {
            Response<CategoryPlugsEntry> response = mPanelRepository.getCategoryPlugsCallV3Sync(panelId, PanelConstant.Category.SECURITY);
            if (null != response.body()) {
                result = PanelDataTransformer.parseOtherPluginList(panelId, response.body());
            }
        } catch (Exception e) {
            DDLog.e(TAG, "Error on requestOtherPluginListSync");
            e.printStackTrace();
        }
        if (null == result) {
            result = new ArrayList<>();
        }
        return result;
    }

    @Override
    public void requestDeleteOtherPlugin(String panelToken, String messageId, String plugId, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestDeleteOtherPlugin");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.deleteOtherPlugs(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, plugId, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestDeleteOtherPlugin");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestDeleteOtherPlugin", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestAddPlugs(String panelToken, AddPlugsBuilder builder, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestAddPlugs");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        builder.setDeviceToken(panelToken);
        mPanelRepository.addPlugs(builder, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call,
                                   @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on requestAddPlugs");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(response.body());
                }
            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                onRequestError("requestAddPlugs", t, resultCallback);
            }
        });
    }

    @Override
    public void requestAddNotOfficialPlugs(String panelToken, AddPlugsBuilder builder, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestAddNotOfficialPlugs");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.addNotOfficialPlugs(builder, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call,
                                   @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on requestAddNotOfficialPlugs");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(response.body());
                }
            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                onRequestError("requestAddNotOfficialPlugs", t, resultCallback);
            }
        });
    }

    @Override
    public void requestDeletePlugs(String panelToken, String messageid, String plugId,
                                   @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestDeletePlugs");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.deletePlugs(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageid, plugId, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestDeletePlugs");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestDeletePlugs", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestDeletePlugs(String panelToken, String messageid, String qrcode, String plugId,
                                   @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestDeletePlugs");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.deletePlugs(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageid, qrcode, plugId, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestDeletePlugs");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestDeletePlugs", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestDeleteASKPlugs(String panelToken, String messageid, String sendid, String stype, String id,
                                      @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestDeleteASKPlugs");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.deleteASKPlugs(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageid, sendid, stype, id, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestDeleteASKPlugs");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestDeleteASKPlugs", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestChangePlugName(String panelToken, String messageid, String qrcode, String pluginId, String plugName,
                                      @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestChangePlugName");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.changePlugName(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageid, qrcode, pluginId, plugName, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestChangePlugName");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestChangePlugName", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestChangeOtherPlugName(String panelToken, String messageid, String plugId, String plugName,
                                           @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestChangeOtherPlugName");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.changeOtherPlugName(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageid, plugId, plugName, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestChangeOtherPlugName");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestChangeOtherPlugName", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestSmartPlugsStatusChange(String plugId, String panelToken, int isOn, boolean isNewAskPlug, String sendid,
                                              String stype, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestSmartPlugsStatusChange");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getSmartPlugsStatusChangeCall(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                plugId, isOn, isNewAskPlug, sendid, stype, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestSmartPlugsStatusChange");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestSmartPlugsStatusChange", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestAskSirenSetting(String panelToken, String messageid, String sendid, String stype, String advancesetting,
                                       @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestAskSirenSetting");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getAskSirenSettingCall(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageid, sendid, stype, advancesetting, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestAskSirenSetting");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestAskSirenSetting", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestSirenSetting(String panelToken, String messageid, String pluginid,
                                    String sirensetting, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestSirenSetting");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getSirenSettingCall(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageid, pluginid, sirensetting, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestSirenSetting");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestSirenSetting", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestAddNewASKPlugin(String panelToken, String messageid, String plugin, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestAddNewASKPlugin");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getAddNewASKPluginCall(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageid, plugin, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestAddNewASKPlugin");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestAddNewASKPlugin", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestASKPluginModify(String panelToken, String messageid, String datas, String name,
                                       @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestASKPluginModify");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getASKPluginModifyCall(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageid, datas, name, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestASKPluginModify");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestASKPluginModify", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestListDoorBell(String panelId, String panelToken,
                                    @NotNull PanelCallback.NetworkResult<ArrayList<DoorBellDevice>> resultCallback) {
        DDLog.d(TAG, "requestListDoorBell");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getListDoorBellCall(panelId, new Callback<ResponseBody>() {
            @Override
            public void onResponse(@NotNull Call<ResponseBody> call,
                                   @NotNull Response<ResponseBody> response) {
                DDLog.i(TAG, "Success on requestListDoorBell");
                if (null == response.body()) {
                    resultCallback.onSuccess(null);
                    return;
                }

                ArrayList<DoorBellDevice> doorBellDevices = PanelDataTransformer.parseDoorbellResult(panelId, response.body());
                if (null == doorBellDevices) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(doorBellDevices);
                }
            }

            @Override
            public void onFailure(@NotNull Call<ResponseBody> call, @NotNull Throwable t) {
                onRequestError("requestListDoorBell", t, resultCallback);
            }
        });
    }

    @Override
    public ArrayList<DoorBellDevice> requestListDoorBellSync(String panelId, String panelToken) {
        DDLog.i(TAG, "requestListDoorBellSync");
        ArrayList<DoorBellDevice> result = null;
        try {
            Response<ResponseBody> response = mPanelRepository.getListDoorBellCallSync(panelId);
            if (null != response.body()) {
                result = PanelDataTransformer.parseDoorbellResult(panelId, response.body());
            }
        } catch (Exception e) {
            DDLog.e(TAG, "Error on requestListDoorBellSync");
            e.printStackTrace();
        }
        if (null == result) {
            result = new ArrayList<>();
        }
        return result;
    }

    @Override
    public void requestModifyDoorBell(String panelToken, JSONObject data, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestModifyDoorBell");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getModifyDoorBellCall(panelToken,
                data, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestModifyDoorBell");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestModifyDoorBell", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestAddDoorBell(String panelToken, JSONObject data, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestAddDoorBell");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getAddDoorBellCall(data, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call,
                                   @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on requestAddDoorBell");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(response.body());
                }
            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                onRequestError("requestAddDoorBell", t, resultCallback);
            }
        });
    }

    @Override
    public void requestDeleteDoorBell(String panelToken, String id, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestDeleteDoorBell");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getDeleteDoorBellCall(panelToken,
                id, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestDeleteDoorBell");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestDeleteDoorBell", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestDoorBellCap(String panelId, String panelToken, String id, long timestamp, @NotNull PanelCallback.NetworkResult<ArrayList<DoorBellDevice>> resultCallback) {
        DDLog.d(TAG, "requestDoorBellCap");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getDoorBellCapCall(id, timestamp, new Callback<DoorBell>() {
            @Override
            public void onResponse(@NotNull Call<DoorBell> call,
                                   @NotNull Response<DoorBell> response) {
                DDLog.i(TAG, "Success on requestDoorBellCap");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onSuccess(null);
                    return;
                }

                ArrayList<DoorBellDevice> doorBellDevices = PanelDataTransformer.parseDoorbellResult(panelId, response.body());
                if (null == doorBellDevices) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(doorBellDevices);
                }
            }

            @Override
            public void onFailure(@NotNull Call<DoorBell> call, @NotNull Throwable t) {
                onRequestError("requestDoorBellCap", t, resultCallback);
            }
        });
    }

    @Override
    public void requestDeleteDoorBellCap(String id, String panelToken, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestDeleteDoorBellCap");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getDeleteDoorBellCapCall(id, panelToken,
                new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestDeleteDoorBellCap");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestDeleteDoorBellCap", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestTestSiren(String panelToken, String sendid, String stype, int music, int volume,
                                 @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestTestSiren");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getTestSirenCall(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                sendid, stype, music, volume, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestTestSiren");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestTestSiren", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestNotClosedDoorListData(String panelToken, String messageId, String task,
                                             @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestNotClosedDoorListData");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getNotClosedDoorListDataCall(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, task, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestNotClosedDoorListData");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestNotClosedDoorListData", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestNewAskPlugInfo(String panelId, String panelToken, String stype, String sendid,
                                      @NotNull PanelCallback.NetworkResult<String> resultCallback) {
        DDLog.d(TAG, "requestNewAskPlugInfo");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getNewAskPlugInfo(panelId, stype, sendid, new Callback<ResponseBody>() {
            @Override
            public void onResponse(@NotNull Call<ResponseBody> call,
                                   @NotNull Response<ResponseBody> response) {
                DDLog.i(TAG, "Success on requestNewAskPlugInfo");
                if (null == response.body()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Empty body.");
                    return;
                }
                JSONObject jsonObject;
                String result = null;
                try {
                    jsonObject = new JSONObject(response.body().string());
                    result = PanelSecretUtil.getReverSC(String.valueOf(jsonObject.get("Result")));
                } catch (Exception e) {
                    e.printStackTrace();
                }

                if (null == result) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(result);
                }
            }

            @Override
            public void onFailure(@NotNull Call<ResponseBody> call, @NotNull Throwable t) {
                onRequestError("requestNewAskPlugInfo", t, resultCallback);
            }
        });
    }

    @Override
    public void requestCustomizeSmartPlugs(String panelId, String panelToken, @NotNull PanelCallback.NetworkResult<String> resultCallback) {
        DDLog.d(TAG, "requestCustomizeSmartPlugs");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getCustomizeSmartPlugs(panelId, new Callback<ResponseBody>() {
            @Override
            public void onResponse(@NotNull Call<ResponseBody> call,
                                   @NotNull Response<ResponseBody> response) {
                DDLog.i(TAG, "Success on requestCustomizeSmartPlugs");
                if (null == response.body()) {
                    resultCallback.onSuccess(null);
                    return;
                }

                String result = PanelDataTransformer.parseCustomizeSmartPlugsStr(response.body());
                if (TextUtils.isEmpty(result)) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(result);
                }
            }

            @Override
            public void onFailure(@NotNull Call<ResponseBody> call, @NotNull Throwable t) {
                onRequestError("requestCustomizeSmartPlugs", t, resultCallback);
            }
        });
    }

    @Override
    public void requestSetCustomizeSmartPlugs(String panelToken, String messageId, String pluginId, String sendid,
                                              @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestSetCustomizeSmartPlugs");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.setCustomizeSmartPlugs(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, pluginId, sendid, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestSetCustomizeSmartPlugs");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestSetCustomizeSmartPlugs", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestSmartButtonConfig(String panelId, String panelToken, String sendid, String stype,
                                         @NotNull PanelCallback.NetworkResult<String> resultCallback) {
        DDLog.d(TAG, "requestSmartButtonConfig");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getSmartButtonConfig(panelId, sendid, stype, new Callback<ResponseBody>() {
            @Override
            public void onResponse(@NotNull Call<ResponseBody> call,
                                   @NotNull Response<ResponseBody> response) {
                DDLog.i(TAG, "Success on requestSmartButtonConfig");
                if (null == response.body()) {
                    resultCallback.onSuccess(null);
                    return;
                }

                String result = PanelDataTransformer.parseSmartButtonConfigStr(response.body());
                if (null == result) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(result);
                }
            }

            @Override
            public void onFailure(@NotNull Call<ResponseBody> call, @NotNull Throwable t) {
                onRequestError("requestSmartButtonConfig", t, resultCallback);
            }
        });
    }

    @Override
    public void requestUpdateAndSaveSmartButtonConfig(String panelToken, String messageId, String smartButtonSendId, String smartButtonStype,
                                                      @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback,
                                                      ArrayList<JSONObject> actionConfig) {
        DDLog.d(TAG, "requestUpdateAndSaveSmartButtonConfig");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.updateAndSaveSmartButtonConfig(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, smartButtonSendId, smartButtonStype, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestUpdateAndSaveSmartButtonConfig");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestUpdateAndSaveSmartButtonConfig", t, resultCallback);
                    }
                }, actionConfig);
    }

    @Override
    public void requestModifyPluginBlock(String panelToken, String messageId, String pluginId, String sendid, String stype,
                                         int block, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestModifyPluginBlock");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getModifyPluginBlockCall(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, pluginId, sendid, stype, block, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestModifyPluginBlock");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestModifyPluginBlock", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestCareModeData(String panelId, String panelToken,
                                    @NotNull PanelCallback.NetworkResult<String> resultCallback) {
        DDLog.d(TAG, "requestCareModeData");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getCareModeData(panelId, new Callback<ResponseBody>() {
            @Override
            public void onResponse(@NotNull Call<ResponseBody> call,
                                   @NotNull Response<ResponseBody> response) {
                DDLog.i(TAG, "Success on requestCareModeData");
                if (null == response.body()) {
                    resultCallback.onSuccess(null);
                    return;
                }

                String result = PanelDataTransformer.parseCareModeDataStr(response.body());
                if (null == result) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(result);
                }
            }

            @Override
            public void onFailure(@NotNull Call<ResponseBody> call, @NotNull Throwable t) {
                onRequestError("requestCareModeData", t, resultCallback);
            }
        });
    }

    @Override
    public void requestModifyCareMode(String panelToken, String messageId, boolean isOn,
                                      @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestModifyCareMode");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getModifyCareModeCall(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, isOn, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestModifyCareMode");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestModifyCareMode", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestModifyCareModeNoActionTime(String panelToken, String messageId, int noActionTime,
                                                  @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestModifyCareModeNoActionTime");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getModifyCareModeNoActionTimeCall(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, noActionTime, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestModifyCareModeNoActionTime");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestModifyCareModeNoActionTime", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestModifyCareModeAlarmTime(String panelToken, String messageId, int alarmTime,
                                               @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestModifyCareModeAlarmTime");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getModifyCareModeAlarmTimeCall(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, alarmTime, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestModifyCareModeAlarmTime");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestModifyCareModeAlarmTime", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestModifyCareModePlugin(String panelToken, String messageId, String data,
                                            @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestModifyCareModePlugin");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getModifyCareModePluginCall(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, data, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestModifyCareModePlugin");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestModifyCareModePlugin", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestCancelCareModeNoAction(String panelToken, String messageId,
                                              @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestCancelCareModeNoAction");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getCancelCareModeNoActionCall(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestCancelCareModeNoAction");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestCancelCareModeNoAction", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestCareModeNoActionSos(String panelToken, String messageId, @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestCareModeNoActionSos");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getCareModeNoActionSosCall(
                UserManager.getInstance().getUser().getUid(),
                panelToken,
                messageId, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestCareModeNoActionSos");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestCareModeNoActionSos", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestHomeExceptionAccessoryInfo(String panelToken, String messageId,
                                                  @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestHomeExceptionAccessoryInfo");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getHomeExceptionAccessoryInfo(
                UserManager.getInstance().getUser().getUid(),
                panelToken, messageId, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestHomeExceptionAccessoryInfo");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestHomeExceptionAccessoryInfo", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestSmartButtonTargetPluginList(String panelId, int category, @NotNull PanelCallback.NetworkResult<String> resultCallback) {
        DDLog.d(TAG, "requestSmartButtonTargetPluginList");
        mPanelRepository.getSpecifyPluginList(panelId, category, new Callback<ResponseBody>() {
            @Override
            public void onResponse(@NotNull Call<ResponseBody> call, @NotNull Response<ResponseBody> response) {
                DDLog.i(TAG, "Success on requestSmartPlugList");
                if (null == response.body()) {
                    resultCallback.onSuccess(null);
                    return;
                }

                String result = null;
                try {
                    JSONObject jsonObject = new JSONObject(response.body().string());
                    result = PanelSecretUtil.getReverSC(String.valueOf(jsonObject.get(NetKeyConstants.NET_KEY_RESULT)));
                } catch (Exception e) {
                    e.printStackTrace();
                }

                if (TextUtils.isEmpty(result)) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(result);
                }
            }

            @Override
            public void onFailure(@NotNull Call<ResponseBody> call, @NotNull Throwable t) {
                onRequestError("requestSmartPlugList", t, resultCallback);
            }
        });
    }

    @Override
    public void requestControlRelay(String panelToken, String messageId, String action, String sendId,
                                    @NotNull PanelCallback.NetworkResult<StringResponseEntry> resultCallback) {
        DDLog.d(TAG, "requestControlRelay");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.getRelayControlCall(
                UserManager.getInstance().getUser().getUid(),
                panelToken, messageId, action, sendId, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestControlRelay");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestControlRelay", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestHomePluginDetails(String panelId, int category, String stype, String sendid, String id,
                                         @NotNull PanelCallback.NetworkResult<String> resultCallback) {
        DDLog.i(TAG, "requestHomePluginDetails, panelId: " + panelId);
        mPanelRepository.getHomePluginDetails(panelId, category, stype, sendid, id, new Callback<ResponseBody>() {
            @Override
            public void onResponse(@NotNull Call<ResponseBody> call, @NotNull Response<ResponseBody> response) {
                DDLog.i(TAG, "Success on requestHomePluginDetails");
                String result = null;
                try {
                    JSONObject jsonObject = new JSONObject(response.body().string());
                    result = PanelSecretUtil.getReverSC(String.valueOf(jsonObject.get(NetKeyConstants.NET_KEY_RESULT)));
                } catch (Exception e) {
                    DDLog.e(TAG, "Error on parse json data.");
                    e.printStackTrace();
                }

                if (TextUtils.isEmpty(result)) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(result);
                }
            }

            @Override
            public void onFailure(@NotNull Call<ResponseBody> call, @NotNull Throwable t) {
                onRequestError("requestHomePluginDetails", t, resultCallback);
            }
        });
    }

    @Override
    public void requestDeletePanel(String panelId, @NotNull PanelCallback.NetworkResult<String> resultCallback) {
        DDLog.i(TAG, "requestDeletePanel, panelId: " + panelId);
        mPanelRepository.deletePanel(panelId, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on requestDeletePanel");
                if (null == response.body() || 1 != response.body().getStatus()) {
                    resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                } else {
                    resultCallback.onSuccess(response.body().getResult());
                }
            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                onRequestError("requestDeletePanel", t, resultCallback);
            }
        });
    }

    @Override
    public void requestUpdatePanelFirmware(String panelToken, String messageId,
                                           @NotNull PanelCallback.NetworkResult<String> resultCallback) {
        DDLog.d(TAG, "requestUpdatePanelFirmware");
        if (checkEmptyUserOrPanel(panelToken, resultCallback)) {
            return;
        }
        mPanelRepository.updatePanelFirmware(
                UserManager.getInstance().getUser().getUid(),
                panelToken, messageId, new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestUpdatePanelFirmware");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body().getResult());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestUpdatePanelFirmware", t, resultCallback);
                    }
                });
    }

    @Override
    public void requestSetDoorWindowPushStatus(String deviceToken, String messageid, String pluginId,
                                               String sendid, String stype, boolean pushStatus,
                                               @NotNull PanelCallback.NetworkResult<String> resultCallback) {
        DDLog.d(TAG, "requestSetDoorWindowPushStatus");
        if (checkEmptyUserOrPanel(deviceToken, resultCallback)) {
            return;
        }
        mPanelRepository.setDoorWindowPushStatus(
                UserManager.getInstance().getUser().getUid(),
                deviceToken, messageid, pluginId, sendid, stype, pushStatus,
                new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "Success on requestSetDoorWindowPushStatus");
                        if (null == response.body() || 1 != response.body().getStatus()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body().getResult());
                        }
                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        onRequestError("requestSetDoorWindowPushStatus", t, resultCallback);
                    }
                });
    }

    @Override
    public void getArmRules(String homeId, String deviceId, PanelCallback.NetworkResult<GetArmRulesResponse.ResultBean> callback) {
        DDLog.d(TAG, "getArmRules");
        if (checkEmptyUserOrPanel(deviceId, callback)) {
            return;
        }
        mPanelRepository.getArmRules(homeId, deviceId, new Callback<GetArmRulesResponse>() {
            @Override
            public void onResponse(@NotNull Call<GetArmRulesResponse> call,
                                   @NotNull Response<GetArmRulesResponse> response) {
                DDLog.i(TAG, "Success on getArmRules");
                if (checkResponseSuccess(response, callback)) {
                    if (null != callback) {
                        callback.onSuccess(response.body().getResult());
                    }
                }
            }

            @Override
            public void onFailure(@NotNull Call<GetArmRulesResponse> call, @NotNull Throwable t) {
                onRequestError("getArmRules", t, callback);
            }
        });
    }

    @Override
    public void getCareModeStatus(String homeId, String deviceId, PanelCallback.NetworkResult<GetCareModeStatusResponse.ResultBean> callback) {
        DDLog.d(TAG, "getCareModeStatus");
        if (checkEmptyUserOrPanel(deviceId, callback)) {
            return;
        }
        mPanelRepository.getCareModeStatus(homeId, deviceId, new Callback<GetCareModeStatusResponse>() {
            @Override
            public void onResponse(@NotNull Call<GetCareModeStatusResponse> call,
                                   @NotNull Response<GetCareModeStatusResponse> response) {
                DDLog.i(TAG, "Success on getCareModeStatus");
                if (checkResponseSuccess(response, callback)) {
                    if (null != callback) {
                        callback.onSuccess(response.body().getResult());
                    }
                }
            }

            @Override
            public void onFailure(@NotNull Call<GetCareModeStatusResponse> call, @NotNull Throwable t) {
                onRequestError("getCareModeStatus", t, callback);
            }
        });
    }

    @Override
    public void getVoicePrompt(String homeId, String deviceId, PanelCallback.NetworkResult<GetVoicePromptResponse.ResultBean> callback) {
        DDLog.d(TAG, "getVoicePrompt");
        if (checkEmptyUserOrPanel(deviceId, callback)) {
            return;
        }
        mPanelRepository.getVoicePrompt(homeId, deviceId, new Callback<GetVoicePromptResponse>() {
            @Override
            public void onResponse(@NotNull Call<GetVoicePromptResponse> call,
                                   @NotNull Response<GetVoicePromptResponse> response) {
                DDLog.i(TAG, "Success on getVoicePrompt");
                if (checkResponseSuccess(response, callback)) {
                    if (null != callback) {
                        callback.onSuccess(response.body().getResult());
                    }
                }
            }

            @Override
            public void onFailure(@NotNull Call<GetVoicePromptResponse> call, @NotNull Throwable t) {
                onRequestError("getVoicePrompt", t, callback);
            }
        });
    }

    @Nullable
    @Override
    public List<CommonAccessoriesBean> listAccessoriesSync(String homeId, String deviceId, long addTime, List<String> sTypeList, boolean orderDesc) {
        DDLog.d(TAG, "listAccessoriesSync");
        ListAccessoriesResponse response = mPanelRepository.listAccessories(homeId, deviceId, addTime, sTypeList, orderDesc);
        if (null != response && null != response.getResult() && null != response.getResult().getAccessories()) {
            return response.getResult().getAccessories();
        }
        return null;
    }

    @Override
    public void searchAccessories(String homeId, String deviceId, List<AccessoriesInfoParams> accessories, long addTime,
                                  boolean orderDesc, PanelCallback.NetworkResult<List<CommonAccessoriesBean>> callback) {
        DDLog.d(TAG, "searchAccessories");
        if (checkEmptyUserOrPanel(deviceId, callback)) {
            return;
        }
        mPanelRepository.searchAccessories(homeId, deviceId, accessories, addTime, orderDesc, new Callback<SearchAccessoriesResponse>() {
            @Override
            public void onResponse(@NotNull Call<SearchAccessoriesResponse> call,
                                   @NotNull Response<SearchAccessoriesResponse> response) {
                DDLog.i(TAG, "Success on searchAccessories");
                if (checkResponseSuccess(response, callback)) {
                    if (null != callback) {
                        callback.onSuccess(response.body().getResult().getAccessories());
                    }
                }
            }

            @Override
            public void onFailure(@NotNull Call<SearchAccessoriesResponse> call, @NotNull Throwable t) {
                onRequestError("searchAccessories", t, callback);
            }
        });
    }

    @Nullable
    @Override
    public List<PanelInfoNew> searchPanelsSync(String homeId, @NotNull List<String> panelIds) {
        DDLog.d(TAG, "searchPanelsSync");
        SearchPanelsResponse response = mPanelRepository.searchPanelsSync(homeId, panelIds);
        if (null != response && null != response.getResult() && null != response.getResult().getDevices()) {
            return response.getResult().getDevices();
        }
        return null;
    }

    @Nullable
    @Override
    public List<ListPanelTokensResponse.ResultBean.TokensBean> listPanelTokensSync(String homeID, long addTime, boolean orderDesc, int pageSize) {
        DDLog.d(TAG, "listPanelTokensSync");
        ListPanelTokensResponse response = mPanelRepository.listPanelTokensSync(homeID, addTime, orderDesc, pageSize);
        if (null != response && null != response.getResult() && null != response.getResult().getTokens()) {
            return response.getResult().getTokens();
        }
        return null;
    }

    @Override
    public void setPirSettingEnabledStatue(String deviceToken, String homeId
            , String messageId, String pluginId, String sendId, String sType
            , @NotNull PanelCallback.NetworkResult<PirSettingEnabledStatueEntry> resultCallback) {
        DDLog.d(TAG, "setPirSettingEnabledStatue");
        if (checkEmptyUserOrPanel(deviceToken, resultCallback)) {
            return;
        }
        mPanelRepository.setPirSettingEnabledStatue(deviceToken, homeId, messageId, pluginId, sendId
                , sType, UserManager.getInstance().getUser().getUid(), new Callback<PirSettingEnabledStatueEntry>() {
                    @Override
                    public void onResponse(Call<PirSettingEnabledStatueEntry> call, Response<PirSettingEnabledStatueEntry> response) {
                        DDLog.i(TAG, "Success on setPirSettingEnabledStatue");
                        if (null == response.body()) {
                            resultCallback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            resultCallback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(Call<PirSettingEnabledStatueEntry> call, Throwable t) {
                        onRequestError("setPirSettingEnabledStatue", t, resultCallback);
                    }
                });
    }

    @Override
    public void setPirSensitivity(String deviceToken, String homeId, String messageId, String pluginId, String sendId, String sType, int sensitivity, @NonNull PanelCallback.NetworkResult<PirSensitivityResponse> callback) {
        DDLog.d(TAG, "setPirSensitivity");
        if (checkEmptyUserOrPanel(deviceToken, callback)) {
            return;
        }
        mPanelRepository.setPirSensitivity(deviceToken, homeId, messageId, pluginId, sendId, sType
                ,  UserManager.getInstance().getUser().getUid(), sensitivity
                , new Callback<PirSensitivityResponse>() {
                    @Override
                    public void onResponse(Call<PirSensitivityResponse> call, Response<PirSensitivityResponse> response) {
                        DDLog.i(TAG, "Success on setPirSensitivity");
                        if (null == response.body()) {
                            callback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            callback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(Call<PirSensitivityResponse> call, Throwable t) {
                        onRequestError("setPirSensitivity", t, callback);

                    }
                });
    }

    @Override
    public void exitPirSettingMode(String deviceToken, String homeId, String messageId, String pluginId, String sendId, String sType, @NonNull PanelCallback.NetworkResult<PirSensitivityEntry> callback) {
        DDLog.d(TAG, "exitPirSettingMode");
        if (checkEmptyUserOrPanel(deviceToken, callback)) {
            return;
        }
        mPanelRepository.exitPirSettingMode(deviceToken, homeId, messageId, pluginId, sendId, sType
                , UserManager.getInstance().getUser().getUid()
                , new Callback<PirSensitivityEntry>() {
                    @Override
                    public void onResponse(Call<PirSensitivityEntry> call, Response<PirSensitivityEntry> response) {
                        DDLog.i(TAG, "Success on exitPirSettingMode");
                        if (null == response.body()) {
                            callback.onError(ErrorCode.DEFAULT, "Unknown error.");
                        } else {
                            callback.onSuccess(response.body());
                        }
                    }

                    @Override
                    public void onFailure(Call<PirSensitivityEntry> call, Throwable t) {
                        onRequestError("exitPirSettingMode", t, callback);
                    }
                });
    }


}
