package com.dinsafer.panel.operate.bean;


import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.dinsafer.dincore.common.NetKeyConstants;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dssupport.plugin.PluginConstants;
import com.dinsafer.dssupport.plugin.PluginTypeHelper;
import com.dinsafer.dssupport.utils.DDLog;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;

/**
 * 首页面板的配件信息数据封装类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/1/4 2:11 PM
 */
public class HomePluginEntry {
    private final static String TAG = HomePluginEntry.class.getSimpleName();

    private String targetDeviceId; // 数据关联的主机ID
    private ArrayList<TuyaItemPlus> signalRepeaterPlugs; // 中继插座
    private ArrayList<TuyaItemPlus> smartPlugs; // 插座
    private ArrayList<TuyaItemPlus> doorSensors; // 门磁
    private ArrayList<TuyaItemPlus> vibrationSensors; // 震动门磁
    private ArrayList<String> ipcList; // Ipc

    public HomePluginEntry() {
        signalRepeaterPlugs = new ArrayList<>();
        smartPlugs = new ArrayList<>();
        doorSensors = new ArrayList<>();
        vibrationSensors = new ArrayList<>();
        ipcList = new ArrayList<>();
    }

    public HomePluginEntry(@NonNull ArrayList<TuyaItemPlus> signalRepeaterPlugs,
                           @NonNull ArrayList<TuyaItemPlus> smartPlugs,
                           @NonNull ArrayList<TuyaItemPlus> doorSensors,
                           @NonNull ArrayList<TuyaItemPlus> vibrationSensors,
                           @NonNull ArrayList<String> ipcList) {
        this.signalRepeaterPlugs = signalRepeaterPlugs;
        this.smartPlugs = smartPlugs;
        this.doorSensors = doorSensors;
        this.vibrationSensors = vibrationSensors;
        this.ipcList = ipcList;
    }

    public String getTargetDeviceId() {
        return targetDeviceId;
    }

    public void setTargetDeviceId(String targetDeviceId) {
        this.targetDeviceId = targetDeviceId;
    }

    public ArrayList<TuyaItemPlus> getSignalRepeaterPlugs() {
        return signalRepeaterPlugs;
    }

    public void setSignalRepeaterPlugs(ArrayList<TuyaItemPlus> signalRepeaterPlugs) {
        this.signalRepeaterPlugs = signalRepeaterPlugs;
    }

    public ArrayList<TuyaItemPlus> getSmartPlugs() {
        return smartPlugs;
    }

    public void setSmartPlugs(ArrayList<TuyaItemPlus> smartPlugs) {
        this.smartPlugs = smartPlugs;
    }

    public ArrayList<TuyaItemPlus> getDoorSensors() {
        return doorSensors;
    }

    public void setDoorSensors(ArrayList<TuyaItemPlus> doorSensors) {
        this.doorSensors = doorSensors;
    }

    public ArrayList<TuyaItemPlus> getVibrationSensors() {
        return vibrationSensors;
    }

    public void setVibrationSensors(ArrayList<TuyaItemPlus> vibrationSensors) {
        this.vibrationSensors = vibrationSensors;
    }

    public ArrayList<String> getIpcList() {
        return ipcList;
    }

    public void setIpcList(ArrayList<String> ipcList) {
        this.ipcList = ipcList;
    }

    /**
     * 从服务器返回的Json文件中解析并创建对象
     * {
     * "plugin_info": {
     * "door_window": [],
     * "smart_plug": [],
     * "vibration": []
     * }
     * }
     *
     * @param jsonObject 服务器返回的Json数据
     * @return
     */
    public static HomePluginEntry parseFromJson(JSONObject jsonObject) {
        DDLog.d(TAG, "parseFromJson");

        if (null == jsonObject) {
            DDLog.e(TAG, "jsonObject is empty.");
            return new HomePluginEntry();
        }

        JSONObject pluginInfo = DDJSONUtil.getJSONObject(jsonObject, NetKeyConstants.NET_KEY_PLUGIN__INFO);
        if (null == pluginInfo) {
            DDLog.e(TAG, "pluginInfo is empty.");
            return new HomePluginEntry();
        }

        // 1、中继插座
        JSONArray jsonArray = DDJSONUtil.getJSONarray(pluginInfo, NetKeyConstants.NET_KEY_SIGNAL_REPEATER__PLUG);
        ArrayList<TuyaItemPlus> repeaters = new ArrayList<>();
        JSONObject repeaterItemJson;
        TuyaItemPlus repeaterItem;
        if (null != jsonArray) {
            for (int i = 0; i < jsonArray.length(); i++) {
                try {
                    repeaterItemJson = (JSONObject) jsonArray.get(i);
                    repeaterItem = parseNewPlugFromJson(repeaterItemJson);
                    boolean isOn = DDJSONUtil.getBoolean(repeaterItemJson, NetKeyConstants.NET_KEY_ENABLE);
                    if (null != repeaterItem) {
                        repeaterItem.setLoadingStatus(TuyaItemPlus.LOADING_STATUS_SUCCESS);
                        repeaterItem.setAskPlug(PluginTypeHelper.getInstance().isSmartPlugAsk(repeaterItem.getStype()));
                        repeaterItem.setNeedOnlineState(true);
                        repeaterItem.setType(isOn ? TuyaItem.SMARTPLUGIN_ON
                                : TuyaItem.SMARTPLUGIN_OFF);
                        repeaters.add(repeaterItem);
                    }

                } catch (Exception e) {
                    DDLog.d(TAG, "Error on parse plug item, i: " + i);
                    e.printStackTrace();
                }
            }
        }

        // 2、自家插座
        jsonArray = DDJSONUtil.getJSONarray(pluginInfo, NetKeyConstants.NET_KEY_SMART__PLUG);
        ArrayList<TuyaItemPlus> plugs = new ArrayList<>();
        JSONObject pluginItemJson;
        TuyaItemPlus pluginItem;
        if (null != jsonArray) {
            for (int i = 0; i < jsonArray.length(); i++) {
                try {
                    pluginItemJson = (JSONObject) jsonArray.get(i);
                    int category = DDJSONUtil.getInt(pluginItemJson, NetKeyConstants.NET_KEY_CATEGORY);
                    if (PluginConstants.CATEGORY_10 == category) {
                        // 新插座
                        pluginItem = parseNewPlugFromJson(pluginItemJson);
                        if (null != pluginItem) {
                            pluginItem.setLoadingStatus(TuyaItemPlus.LOADING_STATUS_SUCCESS);
                            pluginItem.setAskPlug(PluginTypeHelper.getInstance().isSmartPlugAsk(pluginItem.getStype()));
                            pluginItem.setNeedOnlineState(true);
                        }
                    } else {
                        // 旧插座
                        pluginItem = parseOldPlugFromJson(pluginItemJson);
                        if (null != pluginItem) {
                            pluginItem.setAskPlug(false);
                            pluginItem.setNeedOnlineState(false);
                            pluginItem.setLoadingStatus(TuyaItemPlus.LOADING_STATUS_SUCCESS);
                        }
                    }

                    if (null != pluginItem) {
                        boolean isOn = DDJSONUtil.getBoolean(pluginItemJson, NetKeyConstants.NET_KEY_ENABLE);
                        pluginItem.setType(isOn ? TuyaItem.SMARTPLUGIN_ON
                                : TuyaItem.SMARTPLUGIN_OFF);
                        plugs.add(pluginItem);
                    }
                } catch (Exception e) {
                    DDLog.d(TAG, "Error on parse plug item, i: " + i);
                    e.printStackTrace();
                }
            }
        }

        // 3、doorSensor
        jsonArray = DDJSONUtil.getJSONarray(pluginInfo, NetKeyConstants.NET_KEY_DOOR__WINDOW);
        ArrayList<TuyaItemPlus> localDoorSensors = new ArrayList<>();
        if (null != jsonArray) {
            for (int i = 0; i < jsonArray.length(); i++) {
                try {
                    pluginItemJson = (JSONObject) jsonArray.get(i);
                    int category = DDJSONUtil.getInt(pluginItemJson, NetKeyConstants.NET_KEY_CATEGORY);
                    if (PluginConstants.CATEGORY_10 == category) {
                        // 新门磁
                        pluginItem = parseNewPlugFromJson(pluginItemJson);
                        if (null != pluginItem) {
                            pluginItem.setHaveApart(pluginItem.isNeedBlock());
                            pluginItem.setType(TuyaItem.DOOR_SENSOR_NEW);
                            pluginItem.setNeedLoading(false);
                            pluginItem.setNeedOnlineState(true);
                            pluginItem.setLoadingStatus(TuyaItemPlus.LOADING_STATUS_SUCCESS);
                        }
                    } else {
                        // 旧门磁
                        pluginItem = parseOldPlugFromJson(pluginItemJson);
                        if (null != pluginItem) {
                            pluginItem.setType(TuyaItem.DOOR_SENSOR_OLD);
                            pluginItem.setNeedLoading(false);
                            pluginItem.setNeedOnlineState(false);
                            pluginItem.setOnline(true);
                            pluginItem.setLoadingStatus(TuyaItemPlus.LOADING_STATUS_SUCCESS);
                        }
                    }

                    if (null != pluginItem) {
                        localDoorSensors.add(pluginItem);
                    }
                } catch (Exception e) {
                    DDLog.d(TAG, "Error on parse door sensor item, i: " + i);
                    e.printStackTrace();
                }
            }
        }

        // 4、VibrationSensor
        jsonArray = DDJSONUtil.getJSONarray(pluginInfo, NetKeyConstants.NET_KEY_VIBRATION);
        ArrayList<TuyaItemPlus> localVibrationSensors = new ArrayList<>();
        if (null != jsonArray) {
            for (int i = 0; i < jsonArray.length(); i++) {
                try {
                    pluginItemJson = (JSONObject) jsonArray.get(i);
                    int category = DDJSONUtil.getInt(pluginItemJson, NetKeyConstants.NET_KEY_CATEGORY);
                    if (PluginConstants.CATEGORY_10 == category) {
                        // 新震动门磁
                        pluginItem = parseNewPlugFromJson(pluginItemJson);
                        if (null != pluginItem) {
                            pluginItem.setHaveApart(pluginItem.isNeedBlock());
                            pluginItem.setType(TuyaItem.DOOR_SENSOR_VIBRATION_NEW);
                            pluginItem.setNeedLoading(false);
                            pluginItem.setNeedOnlineState(true);
                            pluginItem.setLoadingStatus(TuyaItemPlus.LOADING_STATUS_SUCCESS);
                        }
                    } else {
                        // 旧震动门磁
                        pluginItem = parseOldPlugFromJson(pluginItemJson);
                        if (null != pluginItem) {
                            pluginItem.setType(TuyaItem.DOOR_SENSOR_VIBRATION_OLD);
                            pluginItem.setNeedLoading(false);
                            pluginItem.setNeedOnlineState(false);
                            pluginItem.setOnline(true);
                            pluginItem.setLoadingStatus(TuyaItemPlus.LOADING_STATUS_SUCCESS);
                        }
                    }

                    if (null != pluginItem) {
                        localVibrationSensors.add(pluginItem);
                    }
                } catch (Exception e) {
                    DDLog.d(TAG, "Error on parse vibration door sensor item, i: " + i);
                    e.printStackTrace();
                }
            }
        }

        // 5、Ipc
        ArrayList<String> ipcListTemp = new ArrayList<>();
        // TODO 模拟数据
        for (int i = 0; i < 3; i++) {
            ipcListTemp.add("Ipc: " + i);
        }

        return new HomePluginEntry(repeaters, plugs, localDoorSensors, localVibrationSensors, ipcListTemp);
    }

    /**
     * 解析并创建新类型的配件
     * id
     * name
     * block
     * category
     * keeplive
     * sendid
     * stype
     *
     * @param jsonObject
     * @return
     */
    public static TuyaItemPlus parseNewPlugFromJson(JSONObject jsonObject) {
        TuyaItemPlus result;
        try {
            result = new TuyaItemPlus(DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_NAME),
                    DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_ID));
            result.setCategory(DDJSONUtil.getInt(jsonObject, NetKeyConstants.NET_KEY_CATEGORY));
            result.setBlock(DDJSONUtil.getInt(jsonObject, NetKeyConstants.NET_KEY_BLOCK));
            result.setSendid(DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_SEND_ID));
            result.setStype(DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_S_TYPE));
            result.setOnline(DDJSONUtil.getBoolean(jsonObject, NetKeyConstants.NET_KEY_KEEP_LIVE));
        } catch (Exception e) {
            DDLog.e(TAG, "Error on parseNewPlugFromJson");
            e.printStackTrace();
            return null;
        }
        return result;
    }

    /**
     * 解析并创建旧类型的配件
     * block
     * category
     * decodeid
     * sub_category
     * keeplive
     *
     * @param jsonObject
     * @return
     */
    public static TuyaItemPlus parseOldPlugFromJson(JSONObject jsonObject) {
        TuyaItemPlus result;
        try {
            result = new TuyaItemPlus(DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_NAME),
                    DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_ID));
            result.setBlock(DDJSONUtil.getInt(jsonObject, NetKeyConstants.NET_KEY_BLOCK));
            result.setCategory(DDJSONUtil.getInt(jsonObject, NetKeyConstants.NET_KEY_CATEGORY));
            result.setDecodeid(DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_DECODE_ID));
            result.setSub_category(DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_SUB__CATEGORY));
            result.setOnline(DDJSONUtil.getBoolean(jsonObject, NetKeyConstants.NET_KEY_KEEP_LIVE));
        } catch (Exception e) {
            DDLog.e(TAG, "Error on parseOldPlugFromJson");
            e.printStackTrace();
            return null;
        }
        return result;
    }

}
