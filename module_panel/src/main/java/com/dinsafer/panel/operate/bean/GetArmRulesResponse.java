package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

/**
 * 获取指定主机的布防规则返回结果
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/11/23 15:09
 */
@Keep
public class GetArmRulesResponse extends BaseHttpEntry {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        private long entrydelay;
        private boolean entrydelay_sound;
        private long exitdelay;
        private boolean exitdelay_sound;
        private long gmtime;

        public long getEntrydelay() {
            return entrydelay;
        }

        public void setEntrydelay(long entrydelay) {
            this.entrydelay = entrydelay;
        }

        public boolean isEntrydelay_sound() {
            return entrydelay_sound;
        }

        public void setEntrydelay_sound(boolean entrydelay_sound) {
            this.entrydelay_sound = entrydelay_sound;
        }

        public long getExitdelay() {
            return exitdelay;
        }

        public void setExitdelay(long exitdelay) {
            this.exitdelay = exitdelay;
        }

        public boolean isExitdelay_sound() {
            return exitdelay_sound;
        }

        public void setExitdelay_sound(boolean exitdelay_sound) {
            this.exitdelay_sound = exitdelay_sound;
        }

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }
    }
}
