package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;

/**
 * Created by Rinfon on 16/9/7.
 */
@Keep
public class SosStatusEntry extends BaseHttpEntry implements Serializable {


    /**
     * Cmd : TASK_SOS
     * Result : {"sosalarm":true,"time":1473142699715135245,"uid":"dayunsz009","photo":"FnplcsNHesZsLMNaIH1tTNov1-XF","pluginid":"HZHqKV@@","PLUGIN_NAME":"garden","isdevice":"true"}
     */

    private String Cmd;
    /**
     * sosalarm : true
     * time : 1473142699715135245
     * uid : dayunsz009
     * photo : FnplcsNHesZsLMNaIH1tTNov1-XF
     * pluginid : HZHqKV@@
     * PLUGIN_NAME : garden
     * isdevice : true
     */

    private ResultBean Result;

    public String getCmd() {
        return Cmd;
    }

    public void setCmd(String Cmd) {
        this.Cmd = Cmd;
    }

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        private boolean sosalarm;
        private long time;
        private String uid;
        private String photo;
        private String pluginid;
        private String pluginname;
        private boolean isdevice;
        private String intimidationmessage;
        private String subcategory;
        private String category;
        private String sostype;

        public boolean isSosalarm() {
            return sosalarm;
        }

        public void setSosalarm(boolean sosalarm) {
            this.sosalarm = sosalarm;
        }

        public long getTime() {
            return time;
        }

        public void setTime(long time) {
            this.time = time;
        }

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getPhoto() {
            return photo;
        }

        public void setPhoto(String photo) {
            this.photo = photo;
        }

        public String getPluginid() {
            return pluginid;
        }

        public void setPluginid(String pluginid) {
            this.pluginid = pluginid;
        }

        public String getPluginname() {
            return pluginname;
        }

        public void setPluginname(String pluginname) {
            this.pluginname = pluginname;
        }

        public boolean getIsdevice() {
            return isdevice;
        }

        public void setIsdevice(boolean isdevice) {
            this.isdevice = isdevice;
        }

        public String getIntimidationmessage() {
            return intimidationmessage;
        }

        public void setIntimidationmessage(String intimidationmessage) {
            this.intimidationmessage = intimidationmessage;
        }

        public boolean isdevice() {
            return isdevice;
        }

        public String getSubcategory() {
            return subcategory;
        }

        public void setSubcategory(String subcategory) {
            this.subcategory = subcategory;
        }

        public String getCategory() {
            return category;
        }

        public void setCategory(String category) {
            this.category = category;
        }

        public String getSostype() {
            return sostype;
        }

        public void setSostype(String sostype) {
            this.sostype = sostype;
        }
    }
}
