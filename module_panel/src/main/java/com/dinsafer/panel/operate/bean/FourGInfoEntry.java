package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;
import com.google.gson.annotations.SerializedName;

/**
 * 4G配置信息类
 *
 * <AUTHOR>
 */
@Keep
public class FourGInfoEntry extends BaseHttpEntry {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        Result = result;
    }

    @Keep
    public static class ResultBean {

        private boolean automatic;
        @SerializedName("node_name")
        private String nodeName;
        @SerializedName("user_name")
        private String userName;
        private String password;

        public boolean isAutomatic() {
            return automatic;
        }

        public void setAutomatic(boolean automatic) {
            this.automatic = automatic;
        }

        public String getNodeName() {
            return nodeName;
        }

        public void setNodeName(String nodeName) {
            this.nodeName = nodeName;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public ResultBean() {
        }

        public ResultBean(boolean automatic, String nodeName, String userName, String password) {
            this.automatic = automatic;
            this.nodeName = nodeName;
            this.userName = userName;
            this.password = password;
        }
    }

}
