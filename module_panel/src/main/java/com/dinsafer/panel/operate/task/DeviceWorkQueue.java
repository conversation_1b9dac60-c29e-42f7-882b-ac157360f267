package com.dinsafer.panel.operate.task;

import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.operate.PanelOperatorConstant;
import com.dinsafer.panel.operate.bean.event.EventListTimeOutCheck;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.LinkedList;

/**
 * Created by Rinfon on 16/8/24.
 */
public class DeviceWorkQueue {
    private static final int TASK_TIMEOUT = 20;//秒
    private static final int TASK_RESEND_TIME = 5;//秒
    private static final int TASK_EXCUTE_TIME = 0;//毫秒，任务间隔
    private static DeviceWorkQueue instance;
    //     任务队列备份，不会因为任务的执行，而删除。
    //     除非有结果返回了，需要手动删除；
    private final LinkedList<AbsBaseTask> queueBackUp;
    private final LinkedList<AbsBaseTask> queue;// 任务队列
    private final PoolWorker threads;// 用数组实现线程池

    public DeviceWorkQueue() {
        queue = new LinkedList<AbsBaseTask>();
        queueBackUp = new LinkedList<AbsBaseTask>();
        threads = new PoolWorker();
        threads.start();
    }

    public static void init() {
        if (instance == null) {
            synchronized (DeviceWorkQueue.class) {
                if (instance == null) {
                    instance = new DeviceWorkQueue();
                }
            }
        }
    }

    public static DeviceWorkQueue getInstance() {
        return instance;
    }

    public void addTask(AbsBaseTask task) {
        synchronized (queue) {
            queue.addLast(task);
            queue.notify();
            queueBackUp.addLast(task);
        }
    }

    public void redoTask(AbsBaseTask task) {
        synchronized (queue) {
            queue.addLast(task);
            queue.notify();
        }
    }

    public void clearTask() {
        synchronized (queue) {
            queue.clear();
            queue.notify();
            queueBackUp.clear();
        }
    }

    public boolean removeTaskById(String id) {
        synchronized (queue) {
            boolean isRemove = false;
            for (int i = 0; i < queueBackUp.size(); i++) {
                if (queueBackUp.get(i).taskId.equals(id)) {
                    queueBackUp.remove(i);
                    isRemove = true;
                    break;
                }
            }
            return isRemove;
        }
    }

    public void setAckTaskById(String id) {
        synchronized (queue) {
            for (int i = 0; i < queueBackUp.size(); i++) {
                if (queueBackUp.get(i).taskId.equals(id)) {
                    queueBackUp.get(i).hasReviceAck = true;
                    break;
                }
            }
        }
    }

    public void removeTaskByIndex(int index) {
        synchronized (queue) {
            queueBackUp.remove(index);
        }
    }

    public int getQueueBackUpSize() {
        synchronized (queue) {
            return queueBackUp.size();
        }
    }

    public LinkedList<AbsBaseTask> getQueueBackUp() {
        synchronized (queue) {
            return queueBackUp;
        }
    }

    public void checkTaskIsTimeOut() {
        if (queueBackUp.size() > 0) {
            synchronized (queueBackUp) {
                ArrayList messageIdList = new ArrayList();
                long currentTime = System.currentTimeMillis();
                if (PanelManager.getInstance().getCurrentPanelDevice() != null
                        && PanelManager.getInstance().getCurrentPanelDevice().getPanelInfo() != null) {
                    long entryDelayTime = PanelManager.getInstance().getCurrentPanelDevice().getPanelInfo().getExitdelay();
                    for (int i = 0; i < queueBackUp.size(); i++) {
//                    arm homearm 的超时需要加上延时布放的时间
                        long time = (currentTime - queueBackUp.get(i).starTime) / (1000);
                        if (entryDelayTime > 0 && (queueBackUp.get(i).type.equals(PanelOperatorConstant.CMD.ARM_KEY)
                                || queueBackUp.get(i).type.equals(PanelOperatorConstant.CMD.HOMEARM_KEY))) {
                            if (queueBackUp.get(i).starTime > 0 &&
                                    time >= TASK_TIMEOUT + entryDelayTime) {
                                messageIdList.add(queueBackUp.get(i).taskId);
                                queueBackUp.remove(i);
                                i--;
                            } else if (queueBackUp.get(i).starTime > 0 && !queueBackUp.get(i).hasReviceAck &&
                                    time > 0 && ((time % TASK_RESEND_TIME) == 0)
                                    && queueBackUp.get(i).mResendCount < 3) {
                                queueBackUp.get(i).mResendCount++;
                                redoTask(queueBackUp.get(i));
                            }
                        } else {
                            if (queueBackUp.get(i).starTime > 0 &&
                                    (currentTime - queueBackUp.get(i).starTime) / (1000) >= TASK_TIMEOUT) {
                                messageIdList.add(queueBackUp.get(i).taskId);
                                queueBackUp.remove(i);
                                i--;
                            } else if (queueBackUp.get(i).starTime > 0 && !queueBackUp.get(i).hasReviceAck &&
                                    time > 0 && ((time % TASK_RESEND_TIME) == 0)
                                    && queueBackUp.get(i).mResendCount < 3) {
                                queueBackUp.get(i).mResendCount++;
                                redoTask(queueBackUp.get(i));
                            }
                        }
                    }
                    if (messageIdList.size() > 0) {
                        EventBus.getDefault().post(new EventListTimeOutCheck(messageIdList));
                    }
                }
            }
        }
    }


    private class PoolWorker extends Thread {// 工作线程类

        public void run() {
            AbsBaseTask r;
            while (true) {
                synchronized (queue) {
                    while (queue.isEmpty()) {// 如果任务队列中没有任务，等待
                        try {
                            queue.wait();
                        } catch (InterruptedException ignored) {
                        }
                    }
                    r = queue.removeFirst();// 有任务时，取出任务
                }
                try {
                    r.run();
                    try {
                        Thread.sleep(TASK_EXCUTE_TIME);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }

                } catch (RuntimeException e) {
                    // You might want to log something here
                }
            }
        }
    }

}
