package com.dinsafer.panel.operate.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

/**
 * 设置页面配件数量信息数据封装类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2020/12/22 2:53 PM
 */
@Keep
public class HomePluginQuantityEntry extends BaseHttpEntry {

    /**
     * plugin : {"door_window":1,"keypad":0,"relay":0,"remote_controller":0,"security_accessories":1,"siren":1,"smart_button":1,"smart_plug":1}
     * user : {"count":4,"info":[{"avatar":"","uid":"gfplustest"},{"avatar":"","uid":"heliotest"},{"avatar":"","uid":"zbcheliosmart"}]}
     */

    private PluginBean Result;

    public PluginBean getResult() {
        return Result;
    }

    public void setResult(PluginBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class PluginBean {
        private int door_bell;
        private int third_party_accessory;
        private int ipc;
        private int door_window;
        private int keypad;
        private int roller_shutter;
        private int remote_controller;
        private int security_accessories;
        private int siren;
        private int smart_button;
        private int smart_plug;
        private int signal_repeater_plug;

        public int getDoor_bell() {
            return door_bell;
        }

        public void setDoor_bell(int door_bell) {
            this.door_bell = door_bell;
        }

        public int getThird_party_accessory() {
            return third_party_accessory;
        }

        public void setThird_party_accessory(int third_party_accessory) {
            this.third_party_accessory = third_party_accessory;
        }

        public int getIpc() {
            return ipc;
        }

        public void setIpc(int ipc) {
            this.ipc = ipc;
        }

        public int getDoor_window() {
            return door_window;
        }

        public void setDoor_window(int door_window) {
            this.door_window = door_window;
        }

        public int getKeypad() {
            return keypad;
        }

        public void setKeypad(int keypad) {
            this.keypad = keypad;
        }

        public int getRoller_shutter() {
            return roller_shutter;
        }

        public void setRoller_shutter(int roller_shutter) {
            this.roller_shutter = roller_shutter;
        }

        public int getRemote_controller() {
            return remote_controller;
        }

        public void setRemote_controller(int remote_controller) {
            this.remote_controller = remote_controller;
        }

        public int getSecurity_accessories() {
            return security_accessories;
        }

        public void setSecurity_accessories(int security_accessories) {
            this.security_accessories = security_accessories;
        }

        public int getSiren() {
            return siren;
        }

        public void setSiren(int siren) {
            this.siren = siren;
        }

        public int getSmart_button() {
            return smart_button;
        }

        public void setSmart_button(int smart_button) {
            this.smart_button = smart_button;
        }

        public int getSmart_plug() {
            return smart_plug;
        }

        public void setSmart_plug(int smart_plug) {
            this.smart_plug = smart_plug;
        }

        public int getSignal_repeater_plug() {
            return signal_repeater_plug;
        }

        public void setSignal_repeater_plug(int signal_repeater_plug) {
            this.signal_repeater_plug = signal_repeater_plug;
        }
    }
}
