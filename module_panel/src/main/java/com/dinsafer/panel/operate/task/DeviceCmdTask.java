package com.dinsafer.panel.operate.task;

import android.text.TextUtils;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.http.PanelApi;
import com.dinsafer.panel.operate.PanelOperatorConstant;
import com.dinsafer.panel.operate.bean.event.DeviceCmdAckEvent;

import org.greenrobot.eventbus.EventBus;
import org.jetbrains.annotations.NotNull;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Created by Rinfon on 16/8/24.
 */
public class DeviceCmdTask extends AbsBaseTask {

    private static final String TAG = DeviceCmdTask.class.getName();

    private String cmd;

    private boolean force;

    public DeviceCmdTask(String _taskId, String _cmd) {
        taskId = _taskId;
        cmd = _cmd;
        force = false;
    }

    public DeviceCmdTask(String _taskId, String _cmd, boolean _force) {
        taskId = _taskId;
        cmd = _cmd;
        force = _force;
    }

    @Override
    void run() {
        if (starTime <= 0) {
            starTime = System.currentTimeMillis();
        }
        type = cmd;
        if (PanelManager.getInstance().getCurrentPanelDevice() != null
                && null != PanelManager.getInstance().getCurrentPanelDevice().getPanelInfo()) {
            if (PanelManager.getInstance().getPanelOperator().isCanCoap()) {
                CoapController.getInstance().callArmDisarmHomearm(taskId, cmd, force, new CoapController.CallBack() {
                    @Override
                    public void onSuccess() {
                        if (cmd.equals(PanelOperatorConstant.CMD.ARM_KEY)
                                && PanelManager.getInstance().getCurrentPanelDevice().getPanelInfo().getExitdelay() > 0) {
                            EventBus.getDefault().post(new DeviceCmdAckEvent(cmd, 1, taskId));
                        }
                    }

                    @Override
                    public void onFail() {
                        DDLog.d(TAG, "call by coap: " + "onError");
                        PanelManager.getInstance().getPanelOperator().setCanCoap(false);
                    }
                });
            } else {
                if (null == DinCore.getUserInstance().getUser()
                        || TextUtils.isEmpty(DinCore.getUserInstance().getUser().getUid())
                        || TextUtils.isEmpty(PanelManager.getInstance().getCurrentPanelToken())) {
                    DDLog.e(TAG, "Error, user id or device token is null.");
                    return;
                }

                Call<StringResponseEntry> call = PanelApi.getPanelApi()
                        .getDeviceCmdCall(DinCore.getUserInstance().getUser().getUid()
                                , PanelManager.getInstance().getCurrentPanelToken()
                                , taskId, cmd, force);
                call.enqueue(new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {

                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        DDLog.e(TAG, "Error...");
                        t.printStackTrace();
                    }
                });
            }
        }
    }
}
