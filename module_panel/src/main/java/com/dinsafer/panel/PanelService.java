package com.dinsafer.panel;

import android.app.Application;
import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.common.CommonCmdEvent;
import com.dinsafer.dincore.common.DeivceChangeEvent;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dincore.common.IService;
import com.dinsafer.dincore.db.DBKey;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dssupport.msctlib.db.KV;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.add.PanelBinder;
import com.dinsafer.panel.add.plugin.DinPluginBinder;
import com.dinsafer.panel.add.plugin.TriggerDeviceBinder;
import com.dinsafer.panel.bean.device.PanelDevice;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.operate.task.DeviceWorkQueue;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 主机模块管理类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/20 4:10 PM
 */
@Keep
public class PanelService implements IService {
    private static final String BINDER_KEY_PANEL = "panel_binder";
    private static final String BINDER_KEY_DIN_PLUGIN = "din_plugin_binder";
    private static final String BINDER_KEY_TRIGGER_PLUGIN = "trigger_device_binder";

    private final static String TAG = PanelService.class.getSimpleName();

    private final Application application;
    //    支持的配件类型
    private Map<String, String> supportDeviceType = new HashMap<>();
    private final String wsDomain;

    public PanelService(Application application, String wsDomain) {
        this.application = application;
        this.wsDomain = wsDomain;
        EventBus.getDefault().register(this);
    }

    @Override
    public void load() {
        supportDeviceType.put(PanelConstant.DeviceType.PANEL, PanelConstant.DeviceType.PANEL);
        supportDeviceType.put(PanelConstant.DeviceType.SMART_BUTTON, PanelConstant.DeviceType.SMART_BUTTON);
        supportDeviceType.put(PanelConstant.DeviceType.SIGNAL_REPEATER_PLUG, PanelConstant.DeviceType.SIGNAL_REPEATER_PLUG);
        supportDeviceType.put(PanelConstant.DeviceType.SMART_PLUG, PanelConstant.DeviceType.SMART_PLUG);
        supportDeviceType.put(PanelConstant.DeviceType.ROLLER_SHUTTER, PanelConstant.DeviceType.ROLLER_SHUTTER);
        supportDeviceType.put(PanelConstant.DeviceType.SECURITY_ACCESSORY, PanelConstant.DeviceType.SECURITY_ACCESSORY);
        supportDeviceType.put(PanelConstant.DeviceType.DOOR_WINDOW_SENSOR, PanelConstant.DeviceType.DOOR_WINDOW_SENSOR);
        supportDeviceType.put(PanelConstant.DeviceType.WIRELESS_SIREN, PanelConstant.DeviceType.WIRELESS_SIREN);
        supportDeviceType.put(PanelConstant.DeviceType.REMOTE_CONTROL, PanelConstant.DeviceType.REMOTE_CONTROL);
        supportDeviceType.put(PanelConstant.DeviceType.KEYBOARD_KEY_TAGS, PanelConstant.DeviceType.KEYBOARD_KEY_TAGS);
        supportDeviceType.put(PanelConstant.DeviceType.DOORBELL, PanelConstant.DeviceType.DOORBELL);
        supportDeviceType.put(PanelConstant.DeviceType.OTHER_PLUGIN, PanelConstant.DeviceType.OTHER_PLUGIN);
        supportDeviceType.put(PanelConstant.DeviceType.HOME_PLUGIN, PanelConstant.DeviceType.HOME_PLUGIN);
        PanelManager.getInstance().initPanelManager(application, wsDomain);
    }

    @Override
    public void unLoad() {
        EventBus.getDefault().unregister(this);
        PanelManager.getInstance().destroyPanelManager();
    }

    @Subscribe
    public void onEvent(CommonCmdEvent commonCmdEvent) {
        DDLog.i(TAG, "on Event: commonCmdEvent " + commonCmdEvent.getCmd() + " /" + commonCmdEvent.getExtra());
        if (CommonCmdEvent.CMD.GET_HOME_INFO.equals(commonCmdEvent.getCmd())
                && !TextUtils.isEmpty(commonCmdEvent.getExtra())) {
            DDLog.i(TAG, "更新当前主机ID");
            try {
                JSONObject homeInfo = new JSONObject(commonCmdEvent.getExtra());
                JSONObject panelInfo = DDJSONUtil.getJSONObject(homeInfo, "panelInfo");
                String panelId = DDJSONUtil.getString(panelInfo, "deviceid");
                String token = DDJSONUtil.getString(panelInfo, "token");
                String name = DDJSONUtil.getString(panelInfo, "name");
                String sim_network = DDJSONUtil.getString(panelInfo, "sim_network");
                PanelManager.getInstance().setCurrentPanelToken(token);
                PanelManager.getInstance().setCurrentPanelName(name);
                PanelManager.getInstance().setSimNetwork(sim_network);
                if (!TextUtils.isEmpty(panelId)) {
                    KV.putString(DBKey.CURRENT_DEVICE_ID, panelId);
                } else {
                    KV.remove(DBKey.CURRENT_DEVICE_ID);
                    PanelManager.getInstance().getPanelOperator().toCloseWs();
                }
            } catch (Exception e) {
                e.printStackTrace();
                PanelManager.getInstance().getPanelOperator().toCloseWs();
            }
        } else if (CommonCmdEvent.CMD.LOGOUT_SUCCESS.equals(commonCmdEvent.getCmd())) {
            DDLog.i(TAG, "退出登录成功");
            KV.remove(DBKey.CURRENT_DEVICE_ID);
            PanelManager.getInstance().getPanelOperator().toCloseWs();
            PanelManager.getInstance().releaseAllDevices();
            PanelManager.getInstance().resetDeviceInfo();
            PanelManager.getInstance().setCurrentHomeId("");
        } else if (CommonCmdEvent.CMD.DELETE_OFFLINE_PANEL.equals(commonCmdEvent.getCmd())) {
            DDLog.i(TAG, "删除离线主机");
            try {
                PanelDevice tempDevice = PanelManager.getInstance().getCurrentPanelDevice();
                KV.remove(DBKey.CURRENT_DEVICE_ID);
                PanelManager.getInstance().getPanelOperator().toCloseWs();
                PanelManager.getInstance().releaseAllDevices();
                PanelManager.getInstance().resetDeviceInfo();
                PanelManager.getInstance().clearDeviceCache();

                DeivceChangeEvent event = new DeivceChangeEvent(tempDevice);
                event.setRemove(true);
                EventBus.getDefault().post(event);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (CommonCmdEvent.CMD.ON_BEFORE_HOME_DISCONNECT.equals(commonCmdEvent.getCmd())) {
            PanelManager.getInstance().setNeedLoadInfoAgain();
        } else if (CommonCmdEvent.CMD.LOGIN_SUCCESS.equals(commonCmdEvent.getCmd())) {
            PanelManager.getInstance().releaseAllDevicesAncCache();
        }
    }

    @Override
    public void config(Map<String, Object> arg) {
        if (null != arg) {
            if (arg.containsKey("homeID")) {
                PanelManager.getInstance().setCurrentHomeId((String) arg.get("homeID"));
            } else {
                PanelManager.getInstance().setCurrentHomeId("");
            }
        }
        PanelManager.getInstance().getTimeoutChecker().cleanAllTask();
        DeviceWorkQueue.getInstance().clearTask();
    }

    @Override
    public List<Device> fetchDevices() {
        return null;
    }

    @Override
    public Device getDevice(String id) {
        return PanelManager.getInstance().getPanelOrPluginDevice(id);
    }

    @Override
    public Device getDevice(String id, String sub) {
        return getDevice(id);
    }

    @Override
    public List<Device> getDeviceByType(String sub) {
        if (TextUtils.isEmpty(sub)) {
            return null;
        }
        if (isSupportedDeviceType(sub)) {
            PanelManager.getInstance().readCacheInfo();

            ArrayList<Device> devices;
            if (PanelConstant.DeviceType.PANEL.equals(sub)) {
                devices = PanelManager.getInstance().requestPanelDevicesSync();
            } else {
                devices = PanelManager.getInstance().requestDeviceByType(sub);
            }
            return devices;
        }
        return null;
    }

    @Override
    public List<Device> getDeviceByType(String sub, boolean cacheFirst) {
        if (!isSupportedDeviceType(sub)) {
            return null;
        }
        if (PanelConstant.DeviceType.HOME_PLUGIN.equals(sub)) {
            PanelManager.getInstance().readCacheInfo();
            return PanelManager.getInstance().requestDeviceByType(sub, cacheFirst);
        }
        return getDeviceByType(sub);
    }

    @Nullable
    @Override
    public List<Device> getCacheDeviceByType(String type) {
        if (!isSupportedDeviceType(type)) {
            return null;
        }
        return PanelManager.getInstance().requestCacheDeviceByType(type);
    }

    @Nullable
    @Override
    public List<Device> getLocalAndNewDeviceByType(String type) {
        if (!isSupportedDeviceType(type)) {
            return null;
        }
        return PanelManager.getInstance().requestLocalAndNewDeviceByType(type);
    }

    @Nullable
    @Override
    public List<Device> getAllDeviceByType(String type) {
        if (!isSupportedDeviceType(type)) {
            return null;
        }
        return PanelManager.getInstance().requestAllDeviceByType(type);
    }

    @Override
    public boolean isSupportedDeviceType(String type) {
        return !TextUtils.isEmpty(type) && supportDeviceType.containsKey(type);
    }

    @Override
    public boolean removeDeviceCacheById(String sub) {
        DDLog.i(TAG, "removeDeviceCacheById. id:" + sub);
        if (TextUtils.isEmpty(sub)) {
            return false;
        }
        return PanelManager.getInstance().removeDeviceCacheById(sub);
    }

    @Override
    public boolean removeDeviceCacheByIdAndSub(String id, String sub) {
        return removeDeviceCacheById(id);
    }

    @Override
    public boolean removeDeviceCacheByType(String sub) {
        if (isSupportedDeviceType(sub)) {
            PanelManager.getInstance().clearDeviceCache();
        }
        return false;
    }

    @Override
    public boolean releaseDeviceByType(String sub) {
        if (TextUtils.isEmpty(sub)) {
            return false;
        }
        if (isSupportedDeviceType(sub)) {
            if (PanelConstant.DeviceType.PANEL.equals(sub)) {
                // TODO release panel
            } else {
                return PanelManager.getInstance().releaseDeviceByType(sub);
            }
            return true;
        }
        return false;
    }

    @Override
    public Device acquireTemporaryDevices(String id, String model) {
        return null;
    }

    @Override
    public BasePluginBinder createPluginBinder(Context context, @NonNull String type) {
        if (BINDER_KEY_PANEL.equals(type)) {
            return new PanelBinder(context);
        } else if (BINDER_KEY_DIN_PLUGIN.equals(type)) {
            return new DinPluginBinder(context, PanelManager.getInstance().getCurrentPanelId(), PanelManager.getInstance().getCurrentPanelToken());
        } else if (BINDER_KEY_TRIGGER_PLUGIN.equals(type)) {
            return new TriggerDeviceBinder(context);
        }
        return null;
    }
}
