package com.dinsafer.panel.common;

import androidx.annotation.Keep;

import com.dinsafer.dincore.common.Cmd;

/**
 * 操作主机的CMD集合
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/20 4:59 PM
 */
@Keep
public final class PanelCmd extends Cmd {

    /**
     * 主机offline
     * 主动通知
     */
    public static final String PANEL_OFFLINE = "panel_offline";

    /**
     * ws通知的连接失败
     */
    public static final String USER_NETWORK_ERROR = "user_network_error";

    /**
     * 操作主机WebSocket
     */
    public static final String OPERATION_WS_CONNECTION = "operation_ws_connection";

    /**
     * 请求服务器刷新主机
     * <p>
     * 主动回调
     * <p>
     * 主机连接websocket失败时（会有三次重连），请求服务器刷新主机token重连，刷新之后的通知
     */
    public static final String PANEL_RENEW = "panel_renew";

    /**
     * 布撤防
     */
    public static final String OPERATION_ARM = "operation_arm";

    /**
     * SOS
     */
    public static final String OPERATION_SOS = "operation_sos";

    /**
     * 获取主机的Sim卡信息
     */
    public static final String GET_SIM_CARD_INFO = "get_simcard_info";

    /**
     * 制作主机布撤防的短信内容
     */
    public static final String RESTRICT_SMS_STRING = "restrict_sms_string";

    /**
     * 获取延迟布防信息
     */
    public static final String GET_EXITDELAY = "get_exitdelay";

    /**
     * 设置延迟布防信息
     */
    public static final String SET_EXITDELAY = "set_exitdelay";

    /**
     * 获取延迟报警信息
     */
    public static final String GET_ENTRYDELAY = "get_entrydelay";

    /**
     * 设置延迟报警信息
     */
    public static final String SET_ENTRYDELAY = "set_entrydelay";

    /**
     * 获取警笛鸣响时间
     */
    public static final String GET_SIRENTIME = "get_sirentime";

    /**
     * 设置警笛鸣响时间
     */
    public static final String SET_SIRENTIME = "set_sirentime";

    /**
     * 获取HomeArm配置信息
     */
    public static final String GET_HOMEARM_INFO = "get_homearm_info";

    /**
     * 设置HomeArm配置信息
     */
    public static final String SET_HOMEARM_INFO = "set_homearm_info";

    /**
     * 获取胁迫报警设置
     */
    public static final String GET_DURESS_INFO = "get_duress_info";

    /**
     * 初始化胁迫报警设置
     */
    public static final String INIT_DURESS_INFO = "init_duress_info";

    /**
     * 胁迫报警开关
     */
    public static final String ENABLE_DURESS = "enable_duress";

    /**
     * 设置胁迫报警密码
     */
    public static final String SET_DURESS_PASSWORD = "set_duress_password";

    /**
     * 设置胁迫报信息
     */
    public static final String SET_DURESS_SMS = "set_duress_sms";

    /**
     * 删除主机
     */
    public static final String DELETE_PANEL = "delete_panel";

    /**
     * 重命名主机
     */
    public static final String RENAME_PANEL = "rename_panel";

    /**
     * 重置主机
     */
    public static final String RESET_PANEL = "reset_panel";

    /**
     * 获取EventList
     */
    public static final String GET_PANEL_EVENTLIST = "get_panel_eventlist";

    /**
     * 获取EventList设置
     */
    public static final String GET_EVENTLIST_SETTING = "get_eventlist_setting";

    /**
     * 设置EventList设置
     */
    public static final String SET_EVENTLIST_SETTING = "set_eventlist_setting";

    /**
     * 获取主机报警信息
     */
    public static final String GET_PANEL_SOSINFO = "get_panel_sosinfo";

    /**
     * 获取cid内容
     */
    public static final String GET_PANEL_CIDDATA = "get_panel_ciddata";

    /**
     * 获取cid内容
     */
    public static final String SET_PANEL_CIDDATA = "set_panel_ciddata";

    /**
     * 获取时区配置
     */
    public static final String GET_TIMEZONE = "get_timezone";

    /**
     * 获取时区配置
     */
    public static final String SET_TIMEZONE = "set_timezone";

    /**
     * 获取ReadyToArm状态
     */
    public static final String GET_READYTOARM_STATUS = "get_readytoarm_status";

    /**
     * 设置ReadyToArm配置
     */
    public static final String SET_READYTOARM_STATUS = "set_readytoarm_status";

    /**
     * 获取AdvancedSetting信息
     */
    public static final String GET_ADVANCED_SETTING = "get_advanced_setting";

    /**
     * 设置布撤防声音
     */
    public static final String SET_ARM_SOUND = "set_arm_sound";

    /**
     * 设置限制模式开关
     */
    public static final String SET_RESTRICT_MODE = "set_restrict_mode";

    /**
     * 修改主机密码
     */
    public static final String SET_PANEL_PASSWORD = "set_panel_password";

    /**
     * 设置主机蓝牙开关
     */
    public static final String OPEN_PANEL_BLUETOOTH = "open_panel_bluetooth";

    /**
     * 获取4G设置
     */
    public static final String GET_4G_INFO = "get_4g_info";

    /**
     * 设置4G
     */
    public static final String SET_4G_INFO = "set_4g_info";

    /**
     * 获取CMS设置
     */
    public static final String GET_CMS_INFO = "get_cms_info";

    /**
     * 设置4G
     */
    public static final String SET_CMS_INFO = "set_cms_info";

    /**
     * 获取守护模式配置
     */
    public static final String GET_CAREMODE = "get_caremode";

    /**
     * 设置守护模式配置
     */
    public static final String SET_CAREMODE = "set_caremode";

    /**
     * 设置守护模式配件列表
     */
    public static final String SET_CAREMODE_PLUGINS = "set_caremode_plugins";

    /**
     * 取消看护模式的NoAction报警
     */
    public static final String CARE_MODE_CANCEL_SOS = "caremode_cancel_sos";

    /**
     * 看护模式 报警
     */
    public static final String CAREMODE_NOACTION_SOS = "caremode_noaction_sos";

    /**
     * 获取未闭合的门磁列表（ready to arm）
     */
    public static final String GET_UNCLOSE_PLUGS = "get_unclose_plugs";

    /**
     * 获取异常配件列表(低电及打开的)
     */
    public static final String GET_EXCEPTION_PLUGS = "get_exception_plugs";

    /**
     * ping值更新
     */
    public static final String PANEL_PING_INFO = "panel_ping_info";

    /**
     * 获取推送语言
     */
    public static final String GET_PANEL_MESSAGE = "get_panel_message";

    /**
     * 设置通知的短信，push内容（设置推送语言）
     */
    public static final String SET_MESSAGE_LANGUAGE = "set_message_language";

    /**
     * 设置通知的短信，push内容(设置推送语言-旧)
     */
    public static final String SET_MESSAGE_TEMPLATE = "set_message_template";

    /**
     * 获取配件数量和用户列表
     */
    public static final String GET_PLUGSANDMEMBERS = "get_plugsandmembers";

    /**
     * 获取配件信息
     */
    public static final String GET_PLUGS_INFO = "get_plugs_info";

    /**
     * 开始轮询配件状态
     */
    public static final String START_ROUND_ROBIN_PLUGIN_STATE = "start_round_robin_plugin_state";

    /**
     * 停止轮询配件状态
     */
    public static final String STOP_ROUND_ROBIN_PLUGIN_STATE = "stop_round_robin_plugin_state";

    /**
     * 获取五建遥控可控制的插座数据
     */
    public static final String GET_CUSTOMIZE_SMART_PLUGS = "get_customize_smart_plugs";

    /**
     * 保存五建遥控控制的插座信息
     */
    public static final String SAVE_CUSTOMIZE_SMART_PLUGS = "save_customize_smart_plugs";

    /**
     * CareMode配件无响应
     */
    public static final String CAREMODE_NOACTION = "caremode_noaction";

    /**
     * 主机低电
     */
    public static final String PANEL_LOWPOWER = "panel_lowpower";

    /**
     * 主机升级中
     */
    public static final String PANEL_UPGRADING = "panel_upgrading";

    /**
     * 授权改变
     */
    public static final String PANEL_AUTHORITY_CHANGED = "panel_authority_changed";

    /**
     * 主机配电方式改变通知
     */
    public static final String PANEL_POWERCHANGED = "panel_powerchanged";

    /**
     * 主机
     */
    public static final String SHOW_BLOCK_TOAST = "show_block_toast";

    /**
     * 请求回滚布防状态
     */
    public static final String ROLLBACK_ARM_STATE = "rollback_arm_state";
    /**
     * 布防状态回滚
     */
    public static final String ON_ARM_STATE_ROLLBACK = "on_arm_state_rollback";

    /**
     * 获取缓存配件的额外信息
     */
    public static final String LOAD_PLUGIN_INFO = "load_plugin_info";

}
