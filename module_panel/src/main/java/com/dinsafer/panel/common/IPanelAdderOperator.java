package com.dinsafer.panel.common;

/**
 * 添加主机-通过蓝牙向主机发送指令接口
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/11 3:27 PM
 */
public interface IPanelAdderOperator {

    void getWifiList();

    void setDeviceName(String deviceName);

    void setDevicePassword(String devicePassword);

    void verifyDevicePassword(String deviceName);

    void setWifiName(String wifiName);

    void setWifiPassword(String wifiPassword);

    void setWifi();

    void setWifi(String wifiName, String wifiPassword);

    void setDHCP();

    void setIP(String ip);

    void setNetmask(String netmask);

    void setGateway(String gateway);

    void setDNS(String dns);

    void setIP();

    void setIP(String ip, String netmask, String gateway, String dns);

    void bindDevice();

    void stopBle();

    void setStaticIP(String ip, String netmask, String gateway, String dns);

    void setStaticIP();

    void getSim();

    void set4GInfo();

    void set4GInfo(String nodeName, String username, String password);

    void set4G();

    void get4G();

    void runZT();
}
