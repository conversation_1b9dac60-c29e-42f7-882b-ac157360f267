package com.dinsafer.panel.common;

import android.app.Application;

/**
 * 主机操作接口
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/12 4:30 PM
 */
public interface IPanelOperator {

    /**
     * @param application APP
     * @param wsDomain    ws连接的地址
     */
    void initPanelOperator(Application application, String wsDomain);

    void connectWs();

    void toCloseWs();

    boolean isWsConnect();

    String getWsCloseReason();
}
