package com.dinsafer.panel.common;

import androidx.annotation.IntRange;
import android.text.TextUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 主机回调结果构建类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/20 5:14 PM
 */
public class PanelResultBuilder {
    protected static final int SUCCESS = PanelDataKey.CmdResult.SUCCESS;
    protected static final int FAIL = PanelDataKey.CmdResult.FAIL;

    private String cmd;
    private boolean operateSelf = true; // 是否自己触发的
    private int resultType = -1; //  0:ACK, 1:Result
    private int status = SUCCESS; // 结果状态 1表示成功
    private int originstatus = SUCCESS; // 结果状态 1表示成功
    private String errorMsg = ""; // 错误提示信息
    private Map<String, Object> result = new HashMap<>(); // 其他额外的数据
    private String resultStr;


    public PanelResultBuilder withCmd(String cmd) {
        this.cmd = cmd;
        return this;
    }

    /**
     * @param resultType 0:ACK, 1:Result
     */
    public PanelResultBuilder withResultType(@IntRange(from = 0, to = 1) int resultType) {
        if (0 == resultType || 1 == resultType) {
            this.resultType = resultType;
        }
        return this;
    }

    public PanelResultBuilder withOperateBySelf(boolean operateBySelf) {
        this.operateSelf = operateBySelf;
        return this;
    }

    public PanelResultBuilder withStatus(int status) {
        this.status = status;
        return this;
    }

    public PanelResultBuilder withOriginStatus(int status) {
        this.originstatus = status;
        return this;
    }

    public PanelResultBuilder withErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
        return this;
    }

    public PanelResultBuilder withResult(Map<String, Object> result) {
        if (null != result) {
            this.result = result;
        }
        return this;
    }

    public PanelResultBuilder withResult(String result) {
        if (null != result) {
            this.resultStr = result;
        }
        return this;
    }

    public Map<String, Object> createResult() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CmdResult.CMD, cmd);
        result.put(PanelDataKey.CmdResult.STATUS, status);
        result.put(PanelDataKey.CmdResult.ORIGIN_STATUS, originstatus);
        result.put(PanelDataKey.CmdResult.ERROR_MESSAGE, errorMsg);
        result.put(PanelDataKey.CmdResult.RESULT, TextUtils.isEmpty(resultStr) ? this.result : resultStr);
        result.put(PanelDataKey.CmdResult.OWNER, operateSelf);
        if (0 == resultType || 1 == resultType) {
            result.put(PanelDataKey.CmdResult.RESULT_TYPE, resultType);
        }
        return result;
    }

    public static PanelResultBuilder success(String cmd, boolean operateSelf,
                                             @IntRange(from = 0, to = 1) int resultType, Map<String, Object> result) {
        return new PanelResultBuilder().withCmd(cmd)
                .withStatus(SUCCESS)
                .withOperateBySelf(operateSelf)
                .withResultType(resultType)
                .withResult(result);
    }

    public static PanelResultBuilder success(String cmd, boolean operateSelf, @IntRange(from = 0, to = 1) int resultType, String result) {
        return new PanelResultBuilder().withCmd(cmd)
                .withStatus(SUCCESS)
                .withOperateBySelf(operateSelf)
                .withResultType(resultType)
                .withResult(result);
    }

    public static PanelResultBuilder success(String cmd, boolean operateSelf, String result) {
        return new PanelResultBuilder().withCmd(cmd)
                .withStatus(SUCCESS)
                .withOperateBySelf(operateSelf)
                .withResult(result);
    }


    public static PanelResultBuilder success(String cmd, boolean operateSelf, Map<String, Object> result) {
        return new PanelResultBuilder().withCmd(cmd)
                .withStatus(SUCCESS)
                .withOperateBySelf(operateSelf)
                .withResult(result);
    }

    public static PanelResultBuilder error(String cmd, boolean operateSelf,
                                           @IntRange(from = 0, to = 1) int resultType, String errorMsg) {
        return new PanelResultBuilder().withCmd(cmd)
                .withStatus(FAIL)
                .withOperateBySelf(operateSelf)
                .withResultType(resultType)
                .withErrorMsg(errorMsg);
    }

    public static PanelResultBuilder error(String cmd, boolean operateSelf, String errorMsg) {
        return new PanelResultBuilder().withCmd(cmd)
                .withStatus(FAIL)
                .withOperateBySelf(operateSelf)
                .withErrorMsg(errorMsg);
    }
}
