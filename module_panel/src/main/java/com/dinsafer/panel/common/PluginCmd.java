package com.dinsafer.panel.common;

import androidx.annotation.Keep;

import com.dinsafer.dincore.common.Cmd;

/**
 * 配件CMD
 *
 * <AUTHOR>
 * @date 2021/6/1 17:43
 */
@Keep
public class PluginCmd extends Cmd {

    /**
     * 修改配件名字
     */
    public static final String PLUGIN_SETNAME = "plugin_setname";

    /**
     * 删除配件
     */
    public static final String PLUGIN_DELETE = "plugin_delete";

    /**
     * 获取smartButton之前的配置信息
     */
    public static final String GET_SMART_BUTTON_CONFIG = "get_smart_button_config";

    /**
     * 更新smartButton的配置信息
     */
    public static final String UPDATE_PLUGIN_CONFIG = "update_plugin_config";

    /**
     * 测试警笛
     */
    public static final String TEST_SIREN = "test_siren";

    /**
     * 获取SmartButton控制配件信息
     */
    public static final String GET_TARGET_LIST = "get_target_list";

    /**
     * 配件状态改变通知(EVENT_FULLPOWER/EVENT_LOWERPOWER)
     */
    public static final String PLUGIN_POWER_CHANGE = "plugin_power_change";

    /**
     * 配件状态改变通知(PLUGIN_OFFLINE/PLUGIN_ONLINE)
     */
    public static final String PLUGIN_ONLINE_CHANGE = "plugin_online_change";

    /**
     * 配件状态改变通知(TASK_PLUGIN_STATUS)
     */
    public static final String PLUGIN_STATE_CHANGE = "plugin_state_change";

    /**
     * 修改配件绕开方式
     */
    public static final String PLUGIN_CONFIG_BLOCK = "plugin_configblock";

    /**
     * 修改插座开关状态
     */
    public static final String PLUG_CHANGE_ON = "plug_change_on";

    /**
     * 操作继电器
     */
    public static final String CONTROL_RELY_ACTION = "control_rely_action";

    /**
     * 修改警笛设置
     */
    public static final String CHANGE_SIREN_SETTING = "change_siren_setting";

    /**
     * 获取配件详情信息-首页配件详情页
     */
    public static final String GET_PLUGIN_DETAIL = "get_plugin_detail";

    /**
     * 设置随时推送门磁状态变化开关状态
     */
    public static final String SET_DOOR_WINDOW_PUSH_STATUS = "set_door_window_push_status";

    /**
     * 主机屏蔽红外触发（5min）
     */
    public static final String BYPASS_PLUGIN_5_MIN = "BYPASS_PLUGIN_5_MIN";

    /**
     * 通知红外已经进入配置模式
     */
    public static final String PIR_SETTING_ENABLED = "PIR_SETTING_ENABLED";

    /**
     * 设置红外灵敏度设置模式
     */
    public static final String SET_SENSITIVITY = "SET_SENSITIVITY";

    /**
     * 退出红外设置模式
     */
    public static final String EXIT_PIR_SETTING_MODE = "EXIT_PIR_SETTING_MODE";
}
