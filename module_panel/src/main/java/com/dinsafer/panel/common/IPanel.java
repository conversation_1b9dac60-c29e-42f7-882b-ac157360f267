package com.dinsafer.panel.common;

import android.app.Application;

import com.dinsafer.panel.add.PanelAdderOperator;
import com.dinsafer.panel.operate.bean.PanelInfo;
import com.dinsafer.panel.operate.callback.PanelCallbackHelper;

/**
 * 主机接口
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/10 12:04 PM
 */
public interface IPanel {

    void initPanelManager(Application application, String wsDomain);

    void destroyPanelManager();

    /**
     * 获取回调管理器
     *
     * @return 主机回调管理器
     */
    PanelCallbackHelper getPanelCallbackHelper();


}
