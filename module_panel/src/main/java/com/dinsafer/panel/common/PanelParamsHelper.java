package com.dinsafer.panel.common;

import androidx.annotation.IntDef;
import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.StringDef;

import com.dinsafer.panel.operate.PanelOperatorConstant;

import org.json.JSONObject;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 操作主机参数构建工具类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/20 5:57 PM
 */
@Keep
public class PanelParamsHelper {
    public static final int WEB_SOCKET_CONNECT = 1; // 连接
    public static final int WEB_SOCKET_DISCONNECT = -1; // 断开连接

    @IntDef({WEB_SOCKET_CONNECT, WEB_SOCKET_DISCONNECT})
    @Retention(RetentionPolicy.SOURCE)
    public @interface WebSocketConnectOperate {
    }

    public static final int OPERATE_ARM_ARM = 0; // 布防
    public static final int OPERATE_ARM_HOME_ARM = 1; // 在家布防
    public static final int OPERATE_ARM_DISARM = 2; // 撤防
    public static final int OPERATE_ARM_HOME_CUSTOMIZE = 3; // 自定义场景模式状态

    @IntDef({OPERATE_ARM_ARM, OPERATE_ARM_HOME_ARM, OPERATE_ARM_DISARM, OPERATE_ARM_HOME_CUSTOMIZE})
    @Retention(RetentionPolicy.SOURCE)
    public @interface ArmOperate {
    }

    public static final int ARM_TYPE_ARM = 0; // 布防
    public static final int ARM_TYPE_HOME_ARM = 1; // 在家布防
    public static final int ARM_TYPE_DISARM = 2; // 撤防
    public static final int ARM_TYPE_SOS = 3; // SOS

    @IntDef({ARM_TYPE_ARM, ARM_TYPE_HOME_ARM, ARM_TYPE_DISARM, ARM_TYPE_SOS})
    @Retention(RetentionPolicy.SOURCE)
    public @interface ArmType {
    }

    public static final int ROLE_GUEST = PanelOperatorConstant.PERMISSION.GUEST; // 访客
    public static final int ROLE_USER = PanelOperatorConstant.PERMISSION.USER; // 用户
    public static final int ROLE_ADMIN = PanelOperatorConstant.PERMISSION.ADMIN; // 管理员

    @IntDef({ROLE_GUEST, ROLE_USER, ROLE_ADMIN})
    @Retention(RetentionPolicy.SOURCE)
    public @interface RoleType {
    }


    @IntDef({PanelConstant.PluginBlockType.BLOCK_TYPE_NULL,
            PanelConstant.PluginBlockType.BLOCK_TYPE_TAMPER,
            PanelConstant.PluginBlockType.BLOCK_TYPE_PLUGIN,
            PanelConstant.PluginBlockType.BLOCK_TYPE_CHIME,})
    @Retention(RetentionPolicy.SOURCE)
    public @interface PluginBlockType {
    }

    @StringDef({PanelConstant.RelayAction.RELAY_ACTION_STOP,
            PanelConstant.RelayAction.RELAY_ACTION_UP,
            PanelConstant.RelayAction.RELAY_ACTION_DOWN,})
    @Retention(RetentionPolicy.SOURCE)
    public @interface RelyActionType {
    }

    @IntDef({PanelConstant.PirSensitivityType.PIR_SENSITIVITY_LOW,
            PanelConstant.PirSensitivityType.PIR_SENSITIVITY_MIDDLE,
            PanelConstant.PirSensitivityType.PIR_SENSITIVITY_HIGH})
    @Retention(RetentionPolicy.SOURCE)
    public @interface PirSensitivityType {

    }


    /**
     * 连接主机或断开主机WebSocket
     *
     * @param connectOperate 1 连接主机WebSocket; -1 断开连接主机WebSocket
     */
    @Keep
    public static Map<String, Object> operationWsConnection(@WebSocketConnectOperate int connectOperate) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.OPERATION_WS_CONNECTION);
        result.put(PanelDataKey.CONNECTION, connectOperate);
        return result;
    }

    /**
     * 主机布撤防
     *
     * @param arm          0:arm状态, 1:homearm状态, 2:disarm状态, 3:自定义场景模式状态
     * @param force        是否强制执行
     * @param recordDisarm 记录disarm的messageid做匹配。目前用于在sos的时候，撤防之后需要询问用户是否需要布防
     */
    @Keep
    public static Map<String, Object> operationArm(@ArmOperate int arm, boolean force, boolean recordDisarm) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.OPERATION_ARM);
        result.put(PanelDataKey.ARM_STATUS, arm);
        result.put(PanelDataKey.FORCE, force);
        result.put(PanelDataKey.RECORD_DISARM, recordDisarm);
        return result;
    }

    public static Map<String, Object> operationArm(@ArmOperate int arm, boolean recordDisarm) {
        return operationArm(arm, false, recordDisarm);
    }

    /**
     * 主机SOS
     */
    @Keep
    public static Map<String, Object> operationSos() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.OPERATION_SOS);
        return result;
    }

    /**
     * 获取主机的Sim卡信息
     */
    @Keep
    public static Map<String, Object> getSimCardInfo() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_SIM_CARD_INFO);
        return result;
    }

    /**
     * 制作主机布撤防的短信内容
     * <p>
     * 0:arm, 1:homarm, 2:disarm, 3:sos
     */
    @Keep
    public static Map<String, Object> restrictSmsString(@ArmType int armType) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.RESTRICT_SMS_STRING);
        result.put(PanelDataKey.ARM_TYPE, armType);
        return result;
    }

    /**
     * 获取延迟布防信息
     */
    @Keep
    public static Map<String, Object> getExitDelay() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_EXITDELAY);
        return result;
    }

    /**
     * 设置延迟布防信息
     */
    @Keep
    public static Map<String, Object> setExitDelay(int delayTime, boolean soundEnable) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SET_EXITDELAY);
        result.put(PanelDataKey.TIME_COMMON, delayTime);
        result.put(PanelDataKey.SOUND_ENABLE_EXIT_DELAY, soundEnable);
        return result;
    }

    /**
     * 获取延迟报警信息
     */
    @Keep
    public static Map<String, Object> getEntryDelay() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_ENTRYDELAY);
        return result;
    }

    /**
     * 设置延迟布防信息
     */
    @Keep
    public static Map<String, Object> setEntryDelay(int time, boolean entryDelaySound,
                                                    String plugins, String askPlugs, String thirdPartPlugins) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SET_ENTRYDELAY);
        result.put(PanelDataKey.TIME_COMMON, time);
        result.put(PanelDataKey.ENTRY_DELAY_SOUND, entryDelaySound);
        result.put(PanelDataKey.DATAS_ENTRY_DELAY, plugins);
        result.put(PanelDataKey.THIRD_PARTY_DATAS_ENTRY_DELAY, thirdPartPlugins);
        result.put(PanelDataKey.NEW_ASK_DATAS_ENTRY_DELAY, askPlugs);
        return result;
    }

    /**
     * 获取警笛鸣响时间
     */
    @Keep
    public static Map<String, Object> getSirenTime() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_SIRENTIME);
        return result;
    }

    /**
     * 设置警笛鸣响时间
     */
    @Keep
    public static Map<String, Object> setSirenTime(int time) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SET_SIRENTIME);
        result.put(PanelDataKey.TIME_COMMON, time);
        return result;
    }

    /**
     * 获取HomeArm配置信息
     */
    @Keep
    public static Map<String, Object> getHomeArmInfo() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_HOMEARM_INFO);
        return result;
    }

    /**
     * 设置HomeArm配置信息
     */
    @Keep
    public static Map<String, Object> setHomeArmInfo(String plugins, String askPlugs, String thirdPartPlugins) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SET_HOMEARM_INFO);
        result.put(PanelDataKey.DATAS_ENTRY_DELAY, plugins);
        result.put(PanelDataKey.THIRD_PARTY_DATAS_ENTRY_DELAY, thirdPartPlugins);
        result.put(PanelDataKey.NEW_ASK_DATAS_ENTRY_DELAY, askPlugs);
        return result;
    }

    /**
     * 获取胁迫报警设置
     */
    @Keep
    public static Map<String, Object> getDuressInfo() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_DURESS_INFO);
        return result;
    }

    /**
     * 初始化胁迫报警设置
     */
    @Keep
    public static Map<String, Object> initDuressInfo(String password, String sms) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.INIT_DURESS_INFO);
        result.put(PanelDataKey.IntimidateSos.PASSWORD, password);
        result.put(PanelDataKey.IntimidateSos.SMS, sms);
        return result;
    }

    /**
     * 胁迫报警开关
     */
    @Keep
    public static Map<String, Object> enableDuress(boolean enable) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.ENABLE_DURESS);
        result.put(PanelDataKey.IntimidateSos.ENABLE, enable);
        return result;
    }

    /**
     * 设置胁迫报警密码
     */
    @Keep
    public static Map<String, Object> setDuressPassword(String password) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SET_DURESS_PASSWORD);
        result.put(PanelDataKey.IntimidateSos.PASSWORD, password);
        return result;
    }

    /**
     * 设置胁迫报信息
     */
    @Keep
    public static Map<String, Object> setDuressSms(String sms) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SET_DURESS_SMS);
        result.put(PanelDataKey.IntimidateSos.SMS, sms);
        return result;
    }

    /**
     * 删除主机
     */
    @Keep
    public static Map<String, Object> deletePanel() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.DELETE_PANEL);
        return result;
    }

    /**
     * 重命名主机
     */
    @Keep
    public static Map<String, Object> renamePanel(String panelName) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.RENAME_PANEL);
        result.put(PanelDataKey.Panel.NAME, panelName);
        return result;
    }

    /**
     * 重命名主机
     */
    @Keep
    public static Map<String, Object> resetPanel(String password, boolean retainPlugins) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.RESET_PANEL);
        result.put(PanelDataKey.Panel.PASSWORD, password);
        result.put(PanelDataKey.Panel.RETAIN_PLUGINS, retainPlugins);
        return result;
    }

    /**
     * 获取特定时间开始的EventList
     */
    @Keep
    public static Map<String, Object> getPanelEventList(long timestamp, String filters) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_PANEL_EVENTLIST);
        result.put(PanelDataKey.TIMESTAMP, timestamp);
        result.put(PanelDataKey.FILTERS, filters);
        return result;
    }

    /**
     * 获取EventList设置
     */
    @Keep
    public static Map<String, Object> getEventListSetting() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_EVENTLIST_SETTING);
        return result;
    }

    /**
     * 设置EventList设置
     */
    @Keep
    public static Map<String, Object> setEventListSetting(boolean doorWindow, boolean tamper) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SET_EVENTLIST_SETTING);
        result.put(PanelDataKey.EventList.DOOR_WINDOW, doorWindow);
        result.put(PanelDataKey.EventList.TAMPER, tamper);
        return result;
    }

    /**
     * 获取主机报警信息
     */
    @Keep
    public static Map<String, Object> getPanelSosInfo() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_PANEL_SOSINFO);
        return result;
    }

    /**
     * 获取cid内容
     */
    @Keep
    public static Map<String, Object> getPanelCidData() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_PANEL_CIDDATA);
        return result;
    }

    /**
     * 设置cid内容
     */
    @Keep
    public static Map<String, Object> setPanelCidData(boolean enable, String contactIdCode,
                                                      String countryCode, String phone) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SET_PANEL_CIDDATA);
        result.put(PanelDataKey.Cid.ENABLE, enable);
        result.put(PanelDataKey.Cid.CONTACT_ID_CODE, contactIdCode);
        result.put(PanelDataKey.Cid.COUNTRY_CODE, countryCode);
        result.put(PanelDataKey.Cid.PHONE, phone);
        return result;
    }

    /**
     * 获取时区配置
     */
    @Keep
    public static Map<String, Object> getTimezone() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_TIMEZONE);
        return result;
    }

    /**
     * 获取时区配置
     */
    @Keep
    public static Map<String, Object> setTimezone(String timeZone) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SET_TIMEZONE);
        result.put(PanelDataKey.Timezone.TIMEZONE, timeZone);
        return result;
    }

    /**
     * 获取ReadyToArm状态
     */
    @Keep
    public static Map<String, Object> getReadyToArmStatus() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_READYTOARM_STATUS);
        return result;
    }

    /**
     * 设置ReadyToArm配置
     */
    @Keep
    public static Map<String, Object> setReadyToArmStatus(boolean enable) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SET_READYTOARM_STATUS);
        result.put(PanelDataKey.ReadyToArm.ENABLE, enable);
        return result;
    }

    /**
     * 获取AdvancedSetting信息
     */
    @Keep
    public static Map<String, Object> getAdvancedSetting() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_ADVANCED_SETTING);
        return result;
    }

    /**
     * 设置布撤防声音
     */
    @Keep
    public static Map<String, Object> setArmSound(boolean on) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SET_ARM_SOUND);
        result.put(PanelDataKey.ArmSoundSetting.ON, on);
        return result;
    }

    /**
     * 设置限制模式开关
     */
    @Keep
    public static Map<String, Object> setRestrictMode(boolean on) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SET_RESTRICT_MODE);
        result.put(PanelDataKey.RestrictMode.ON, on);
        return result;
    }

    /**
     * 设置主机密码
     */
    @Keep
    public static Map<String, Object> setPanelPassword(String oldPassword, String newPassword) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SET_PANEL_PASSWORD);
        result.put(PanelDataKey.Panel.OLD_PASSWORD, oldPassword);
        result.put(PanelDataKey.Panel.PASSWORD, newPassword);
        return result;
    }

    /**
     * 设置主机蓝牙开关
     */
    @Keep
    public static Map<String, Object> openPanelBluetooth(boolean on) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.OPEN_PANEL_BLUETOOTH);
        result.put(PanelDataKey.ON, on);
        return result;
    }

    /**
     * 获取4G设置
     */
    @Keep
    public static Map<String, Object> get4gInfo() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_4G_INFO);
        return result;
    }

    /**
     * 设置4G
     */
    @Keep
    public static Map<String, Object> set4gInfo(boolean automatic, String node_name, String password, String userName) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SET_4G_INFO);
        result.put(PanelDataKey.Panel4GInfo.AUTOMATIC, automatic);
        result.put(PanelDataKey.Panel4GInfo.NODE_NAME, node_name);
        result.put(PanelDataKey.Panel4GInfo.PASSWORD, password);
        result.put(PanelDataKey.Panel4GInfo.USER_NAME, userName);
        return result;
    }

    /**
     * 获取CMS设置
     */
    @Keep
    public static Map<String, Object> getCmsInfo() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_CMS_INFO);
        return result;
    }

    /**
     * 设置CMS
     */
    @Keep
    public static Map<String, Object> setCmsInfo(String protocolName, String info) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SET_CMS_INFO);
        result.put(PanelDataKey.Cms.PROTOCOL_NAME, protocolName);
        result.put(PanelDataKey.Cms.INFO, info);
        return result;
    }

    /**
     * 获取守护模式配置
     */
    @Keep
    public static Map<String, Object> getCareMode() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_CAREMODE);
        return result;
    }

    /**
     * 设置守护模式配置
     */
    @Keep
    public static Map<String, Object> setCareModeEnable(boolean enable) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SET_CAREMODE);
        result.put(PanelDataKey.CareMode.ON, enable);
        return result;
    }

    /**
     * 设置守护模式配置
     */
    @Keep
    public static Map<String, Object> setCareModeNoAction(int noActionTime) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SET_CAREMODE);
        result.put(PanelDataKey.CareMode.NO_ACTION_TIME, noActionTime);
        return result;
    }

    /**
     * 设置守护模式配置
     */
    @Keep
    public static Map<String, Object> setCareModeAlarmDelayTime(int alarmDelayTime) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SET_CAREMODE);
        result.put(PanelDataKey.CareMode.ALARM_DELAY_TIME, alarmDelayTime);
        return result;
    }

    /**
     * 设置守护模式配件列表
     */
    @Keep
    public static Map<String, Object> setCareModePlugins(String plugins) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SET_CAREMODE_PLUGINS);
        result.put(PanelDataKey.CareMode.PLUGINS, plugins);
        return result;
    }

    /**
     * 取消看护模式的NoAction报警
     */
    @Keep
    public static Map<String, Object> cancelCareModeSos() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.CARE_MODE_CANCEL_SOS);
        return result;
    }

    /**
     * 看护模式 报警
     */
    @Keep
    public static Map<String, Object> noActionCareModeSos() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.CAREMODE_NOACTION_SOS);
        return result;
    }

    /**
     * 获取未闭合的门磁列表（ready to arm）
     *
     * @param isHomeArm true: TASK_HOMEARM
     *                  false: TASK_ARM
     */
    @Keep
    public static Map<String, Object> getUnClosePlugs(boolean isHomeArm) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_UNCLOSE_PLUGS);
        result.put(PanelDataKey.ReadyToArm.TASK, isHomeArm ? "TASK_HOMEARM" : "TASK_ARM");
        return result;
    }

    /**
     * 获取异常配件列表(低电及打开的)
     */
    @Keep
    public static Map<String, Object> getExceptionPlugs() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_EXCEPTION_PLUGS);
        return result;
    }

    /**
     * 获取推送语言
     */
    @Keep
    public static Map<String, Object> getPanelMessage() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_PANEL_MESSAGE);
        return result;
    }

    /**
     * 设置通知的短信，push内容（设置推送语言）
     */
    @Keep
    public static Map<String, Object> setMessageLanguage(String langId) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SET_MESSAGE_LANGUAGE);
        result.put(PanelDataKey.PushLanguage.LANG_ID, langId);
        return result;
    }

    /**
     * 设置通知的短信，push内容(设置推送语言-旧)
     *
     * @param message JSONObject.toString()
     */
    @Keep
    public static Map<String, Object> setMessageTemplate(String langId, String message) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SET_MESSAGE_TEMPLATE);
        result.put(PanelDataKey.PushLanguage.LANG_ID, langId);
        result.put(PanelDataKey.PushLanguage.MESSAGE, message);
        return result;
    }

    /**
     * 获取配件数量和用户列表
     */
    @Keep
    public static Map<String, Object> getPlugsAndMembers() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_PLUGSANDMEMBERS);
        return result;
    }

    @Keep
    public static Map<String, Object> getPlugsInfo() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_PLUGS_INFO);
        return result;
    }

    /**
     * 开始轮询配件状态
     */
    @Keep
    public static Map<String, Object> startRoundRobinPluginState() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.START_ROUND_ROBIN_PLUGIN_STATE);
        return result;
    }

    /**
     * 停止轮询配件状态
     */
    @Keep
    public static Map<String, Object> stopRoundRobinPluginState() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.STOP_ROUND_ROBIN_PLUGIN_STATE);
        return result;
    }

    /**
     * 获取五建遥控可控制的插座数据
     */
    @Keep
    public static Map<String, Object> getCustomizeSmartPlugs() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.GET_CUSTOMIZE_SMART_PLUGS);
        return result;
    }

    /**
     * 保存五建遥控控制的插座信息
     */
    @Keep
    public static Map<String, Object> saveCustomizeSmartPlugs(String pluginId, String sendId) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.SAVE_CUSTOMIZE_SMART_PLUGS);
        result.put(PanelDataKey.ID, pluginId);
        result.put(PanelDataKey.SEND_ID, sendId);
        return result;
    }

    /**
     * 请求回滚布防状态
     */
    @Keep
    public static Map<String, Object> rollbackArmState() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.ROLLBACK_ARM_STATE);
        return result;
    }

    /**
     * 修改配件名字
     */
    @Keep
    public static Map<String, Object> setPluginName(String newName) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PluginCmd.PLUGIN_SETNAME);
        result.put(PanelDataKey.NAME, newName);
        return result;
    }

    /**
     * 删除配件
     */
    @Keep
    public static Map<String, Object> deletePlugin() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PluginCmd.PLUGIN_DELETE);
        return result;
    }

    /**
     * 获取SmartButton之前的配置信息
     */
    @Keep
    public static Map<String, Object> getSmartButtonConfig() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PluginCmd.GET_SMART_BUTTON_CONFIG);
        return result;
    }

    /**
     * 更新smartButton的配置信息
     */
    @Keep
    public static Map<String, Object> updateSmartButtonConfig(ArrayList<JSONObject> actionConfigs) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PluginCmd.UPDATE_PLUGIN_CONFIG);
        result.put(PanelDataKey.SmartButton.ACTION_CONFIG, actionConfigs);
        return result;
    }

    /**
     * 测试警笛-SmartButton设置页
     */
    @Keep
    public static Map<String, Object> testSirenSmartButton(String sendid, String stype, int music, int volume) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PluginCmd.TEST_SIREN);
        result.put(PanelDataKey.SEND_ID, sendid);
        result.put(PanelDataKey.S_TYPE, stype);
        result.put(PanelDataKey.WirelessSiren.MUSIC, music);
        result.put(PanelDataKey.WirelessSiren.VOLUME, volume);
        return result;
    }

    /**
     * 测试警笛
     */
    @Keep
    public static Map<String, Object> testSiren(int music, int volume) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PluginCmd.TEST_SIREN);
        result.put(PanelDataKey.WirelessSiren.MUSIC, music);
        result.put(PanelDataKey.WirelessSiren.VOLUME, volume);
        return result;
    }

    /**
     * 获取SmartButton控制配件信息
     */
    @Keep
    public static Map<String, Object> getSmartButtonTargetList(int category) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PluginCmd.GET_TARGET_LIST);
        result.put(PanelDataKey.CATEGORY, category);
        return result;
    }

    /**
     * 修改配件绕开方式
     * 0:不屏蔽, 1:屏蔽tamper alarm, 2:屏蔽整个配件, 3:chime
     */
    @Keep
    public static Map<String, Object> configPluginBlock(@PluginBlockType int block) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PluginCmd.PLUGIN_CONFIG_BLOCK);
        result.put(PanelDataKey.DoorSensor.BLOCK_TYPE, block);
        return result;
    }

    /**
     * 修改插座的开关状态
     */
    @Keep
    public static Map<String, Object> changePlugOn(boolean on) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PluginCmd.PLUG_CHANGE_ON);
        result.put(PanelDataKey.SmartPlug.ON, on);
        return result;
    }

    /**
     * 继电器动作
     */
    @Keep
    public static Map<String, Object> controlRelyAction(@RelyActionType String action) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PluginCmd.CONTROL_RELY_ACTION);
        result.put(PanelDataKey.Relay.ACTION, action);
        return result;
    }

    /**
     * 修改警笛设置
     */
    @Keep
    public static Map<String, Object> changeSirenSetting(String sirenSetting) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PluginCmd.CHANGE_SIREN_SETTING);
        result.put(PanelDataKey.WirelessSiren.SIREN_DATA, sirenSetting);
        return result;
    }

    /**
     * 获取配件详情信息-首页配件详情页
     */
    @Keep
    public static Map<String, Object> getPluginDetail() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PluginCmd.GET_PLUGIN_DETAIL);
        return result;
    }

    /**
     * 修改门磁开合状态通知设置
     */
    @Keep
    public static Map<String, Object> setDoorWindowPushStatus(boolean opened) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PluginCmd.SET_DOOR_WINDOW_PUSH_STATUS);
        result.put(PanelDataKey.DoorSensor.PUSH_STATUS, opened);
        return result;
    }

    /**
     * 查询缓存配件的额外信息
     */
    @Keep
    public static Map<String, Object> loadPluginInfo(List<String> pluginIds) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PanelCmd.LOAD_PLUGIN_INFO);
        result.put(PanelDataKey.PLUGINS, pluginIds);
        return result;
    }

    /**
     * 主机屏蔽红外触发（5min）
     */
    @Keep
    public static Map<String, Object> setPirSettingEnabledStatue() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PluginCmd.BYPASS_PLUGIN_5_MIN);
        return result;
    }

    /**
     * 设置红外灵敏度设置模式
     */
    @Keep
    public static Map<String, Object> setPirSensitivity(@PirSensitivityType int sensitivity) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PluginCmd.SET_SENSITIVITY);
        result.put(PanelDataKey.PirSensitivityInfo.SENSITIVITY, sensitivity);
        return result;
    }

    /**
     * 退出红外设置模式
     */
    @Keep
    public static Map<String, Object> exitPirSettingMode() {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CMD, PluginCmd.EXIT_PIR_SETTING_MODE);
        return result;
    }

}
