package com.dinsafer.panel.common;

import android.app.Application;

import com.clj.fastble.data.BleDevice;
import com.dinsafer.panel.add.callback.IPanelCmdCallback;
import com.dinsafer.panel.add.callback.IPanelConnectListener;
import com.dinsafer.panel.add.callback.IPanelScanListener;

/**
 * 主机蓝牙添加接口
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/11 5:04 PM
 */
public interface IPanelAdder {
    boolean isOpenedBluetooth();

    void init(Application app, String uid, String userId, String familyId);

    void initScanRule(long bleScanTime, String[] serviceUuids, String writeUuid, String notifyUuid);

    void startScanPanel(IPanelScanListener scanListener);

    void stopScanPanel();

    boolean isScanningPanel();

    void connect(BleDevice device, IPanelConnectListener connectListener);

    void disconnectAllBle();

    void destroyAdder();

    void addPanelCmdResultListener(IPanelCmdCallback callback);

    void removePanelCmdResultListener(IPanelCmdCallback callback);

    BleDevice getConnectedDevice();
}
