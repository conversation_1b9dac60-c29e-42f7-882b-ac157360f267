package com.dinsafer.panel.common;

import androidx.annotation.Keep;

import com.dinsafer.dssupport.plugin.PluginConstants;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/14 2:28 PM
 */
@Keep
public class PanelConstant {
    /**
     * 配件类型
     */
    @Keep
    public static class Category {
        public static final int SECURITY = 0;
        public static final int RC_KEY = 1; // 遥控
        public static final int KEYPAD_KEY = 6; // 键盘
        public static final int IPC_KEY = 2;
        public static final int SMART_PLUGS = 3;
        public static final int WIRELESS = 4;
        public static final int SMART_BUTTON = 5;
        public static final int DOOR_BELL = 11;
        public static final int OTHER_PLUGIN = 20;

        public static final int DOOR_SENSOR = 111;
        public static final int RELAY = 12;
        public static final int SWITCH_BOT = 15;

        public static final int SIGNAL_REPEATER_PLUG = 160;
    }

    /**
     * 配件类型ID列表
     */
    public static class Plugin {
        public static final String[] ASK_SMART_BUTTON_IDS = {PluginConstants.TYPE_3B};
        /**
         * 新自家Ask插座
         */
        public static final String[] ASK_SMART_PLUG_IDS = {PluginConstants.TYPE_3E};
        /**
         * 中继插座
         */
        public static final String[] ASK_SIGNAL_REPEATER_PLUG_IDS = {PluginConstants.TYPE_4E};


        /**
         * SecuritySensor
         */
        public static final String OLD_PIR_SENSOR_09 = PluginConstants.TYPE_09; // 红外
        public static final String OLD_DOOR_WINDOW_SENSOR_0B = PluginConstants.TYPE_0B; // 普通门磁
        public static final String OLD_LIQUID_SENSOR_0E = PluginConstants.TYPE_0E; // 水感
        public static final String OLD_VIBRATION_SENSOR_06 = PluginConstants.TYPE_06; // 震动门磁
        public static final String OLD_PANIC_BUTTON_07 = PluginConstants.TYPE_07; // 紧急按钮
        public static final String OLD_SMOKE_SENSOR_05 = PluginConstants.TYPE_05; // 烟感
        public static final String WIRED_BRIDGE = PluginConstants.TYPE_3F; // Wired Bridge
        public static final String[] NEW_PIR_SENSOR_IDS = {PluginConstants.TYPE_17, PluginConstants.TYPE_24
                , PluginConstants.TYPE_36, PluginConstants.TYPE_4A};
        public static final String[] NEW_DOOR_WINDOW_SENSOR_IDS = {PluginConstants.TYPE_16}; // 新型普通门磁
        public static final String[] NEW_VIBRATION_SENSOR_IDS = {PluginConstants.TYPE_19, PluginConstants.TYPE_2C}; // 新型震动门磁ID
        public static final String[] NEW_LIQUID_SENSOR_IDS = {PluginConstants.TYPE_18, PluginConstants.TYPE_2E, PluginConstants.TYPE_39}; // 新型水感ID
        public static final String[] NEW_PANIC_BUTTON_IDS = {PluginConstants.TYPE_23, PluginConstants.TYPE_3B}; // 新型紧急按钮ID
        public static final String[] NEW_SMOKE_SENSOR_IDS = {PluginConstants.TYPE_2D, PluginConstants.TYPE_3C}; // 新型烟感ID
        public static final String[] WIRED_BRIDGE_IDS = {PluginConstants.TYPE_3F}; //Wired Bridge

        /**
         * DoorSensor
         */
        public static final String[] OLD_DOOR_SENSOR_CATEGORIES = {PluginConstants.TYPE_16, PluginConstants.TYPE_19};
        public static final String[] NEW_DOOR_SENSOR_CATEGORIES = {PluginConstants.TYPE_1C, PluginConstants.TYPE_11,
                PluginConstants.TYPE_25, PluginConstants.TYPE_2C, PluginConstants.TYPE_38, PluginConstants.TYPE_3D};

        /**
         * WirelessSiren
         */
        public static final String[] ASK_WIRELESS_IDS = {PluginConstants.TYPE_21, PluginConstants.TYPE_22, PluginConstants.TYPE_34, PluginConstants.TYPE_35};

        /**
         * RemoteControl
         */
        public static final String[] ASK_REMOTE_CONTROL_IDS = {PluginConstants.TYPE_1E, PluginConstants.TYPE_3A};

        /**
         * Keypad
         */
        public static final String[] ASK_WIRELESS_KEYPAD_IDS = {PluginConstants.TYPE_2F};
    }

    /**
     * 配件加载状态
     */
    @Keep
    public static class PluginLoadingState {
        public final static int LOADING = 0;
        public final static int SUCCESS = 1;
        public final static int ERROR = 2;
    }

    /**
     * 配件开关状态
     */
    @Keep
    public static class PluginSwitchState {
        public final static int CLOSED = 0;
        public final static int OPENED = 1;
        public final static int LOADING = 2;
    }

    /**
     * SmartButton
     */
    @Keep
    public static class SmartButton {
        // 服务器Action类型定义
        public static final String SERVICE_ACTION_SINGLE_PRESS = "0";
        public static final String SERVICE_ACTION_DOUBLE_PRESS = "1";
        public static final String SERVICE_ACTION_LONG_PRESS = "2";
        public static final String[] SERVICE_ACTIONS = {SERVICE_ACTION_SINGLE_PRESS,
                SERVICE_ACTION_DOUBLE_PRESS, SERVICE_ACTION_LONG_PRESS};

        // 服务器场景常量定义
        public static final String SERVICE_SCENE_SECURITY_CME = "SecurityCMD";
        public static final String SERVICE_SCENE_RING_BELL = "Doorbell";
        public static final String SERVICE_SCENE_SWITCH_BULB = "Bulb";
        public static final String SERVICE_SCENE_SWITCH_PLUG = "Plug";
        public static final String SERVICE_SCENE_CONTROL_SHUTTER = "Shutter";

        // 服务器返回的安防指令类型定义
        private static final String SERVICE_CMD_ARM = "TASK_ARM";
        private static final String SERVICE_CMD_HOMEARM = "TASK_HOMEARM";
        private static final String SERVICE_CMD_SOS = "TASK_SOS";
    }


    /**
     * 新型ASK智能插座开关状态判断，1为开
     */
    public static final int STATUS_OPENED_ASK_PLUG = 1;

    public static final String MIN_CHANGEMESSAGE_VERSION = "0.1";

    /**
     * 设备类型
     */
    @Keep
    public static class DeviceType {
        public static final String PANEL = "PANEL";
        public static final String SMART_BUTTON = "SmartButton";
        public static final String SIGNAL_REPEATER_PLUG = "SignalRepeaterPlug";
        public static final String SMART_PLUG = "SmartPlug";
        public static final String ROLLER_SHUTTER = "RollerShutter";
        public static final String SECURITY_ACCESSORY = "SecurityAccessory";
        public static final String DOOR_WINDOW_SENSOR = "DoorWindowSensor";
        public static final String WIRELESS_SIREN = "WirelessSiren";
        public static final String REMOTE_CONTROL = "RemoteControl";
        public static final String KEYBOARD_KEY_TAGS = "KeyboardKeyTags";
        public static final String DOORBELL = "Doorbell";
        public static final String OTHER_PLUGIN = "OtherPlugin";
        public static final String HOME_PLUGIN = "HomePlugin";
    }

    /**
     * 配件屏蔽类型
     */
    @Keep
    public static class PluginBlockType {
        public static final int BLOCK_TYPE_NULL = 0; //不屏蔽
        public static final int BLOCK_TYPE_TAMPER = 1; //屏蔽tamper alarm
        public static final int BLOCK_TYPE_PLUGIN = 2; //屏蔽整个配件
        public static final int BLOCK_TYPE_CHIME = 3; // chime
    }

    /**
     * 继电器操作类型
     */
    @Keep
    public static class RelayAction {
        public static final String RELAY_ACTION_UP = "01";
        public static final String RELAY_ACTION_STOP = "00";
        public static final String RELAY_ACTION_DOWN = "02";
    }

    /**
     * 红外灵敏度对应的值
     * 低：100
     * 中：30
     * 高：20
     */
    @Keep
    public static class PirSensitivityType {
        public static final int PIR_SENSITIVITY_LOW = 50;
        public static final int PIR_SENSITIVITY_MIDDLE = 30;
        public static final int PIR_SENSITIVITY_HIGH = 20;
    }
}
