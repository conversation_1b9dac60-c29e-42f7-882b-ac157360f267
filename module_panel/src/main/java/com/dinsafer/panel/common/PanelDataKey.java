package com.dinsafer.panel.common;

import androidx.annotation.Keep;

/**
 * Device json数据的key
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/14 3:27 PM
 */
@Keep
public class PanelDataKey {

    /**
     * 名字
     * String
     */
    public static final String NAME = "NAME";

    /**
     * id
     * String
     */
    public static final String ID = "ID";

    /**
     * 是否有信号量
     * boolean
     */
    public static final String HAVE_SIGNAL_LEVEL = "HAVE_SIGNAL_LEVEL";

    /**
     * 信号量
     * int
     */
    public static final String SIGNAL_VALUE = "SIGNAL_VALUE";

    /**
     * 是否有防拆功能
     * boolean
     */
    public static final String CAN_TAMPER = "CAN_TAMPER";
    /**
     * 防拆状态
     * boolean
     */
    public static final String IS_TAMPER = "IS_TAMPER";

    /**
     * 是否有充电状态
     * boolean
     */
    public static final String CAN_CHARGING = "CAN_CHARGING";
    /**
     * 是否在充电
     * boolean
     */
    public static final String IS_CHARGING = "IS_CHARGING";

    /**
     * 是否有电量状态
     * boolean
     */
    public static final String HAVE_BATTERY_LEVEL = "HAVE_BATTERY_LEVEL";
    /**
     * 电量值
     * int
     */
    public static final String BATTERY_VALUE = "BATTERY_VALUE";

    /**
     * 是否有需要等待webSocket返回的状态
     * boolean
     */
    public static final String HAVE_WEB_SOCKET_LOADING = "HAVE_WEB_SOCKET_LOADING";

    /**
     * 配件ASK数据
     * JsonObject
     */
    public static final String ASK_DATA = "ASK_DATA";

    /**
     * decode_id
     * String
     */
    public static final String DECODE_ID = "DECODE_ID";

    /**
     * 配件的加载状态
     * {@link PanelConstant.PluginLoadingState}
     */
    public static final String LOADING_STATE = "LOADING_STATE";

    /**
     * 配件是否有加载状态
     * boolean
     */
    public static final String HAVE_LOADING_STATE = "HAVE_LOADING_STATE";

    /**
     * 配件dType
     * String
     */
    public static final String D_TYPE = "D_TYPE";

    /**
     * 配件sType
     * String
     */
    public static final String S_TYPE = "S_TYPE";

    /**
     * 配件category
     * int
     */
    public static final String CATEGORY = "CATEGORY";

    /**
     * 配件subCategory
     * String
     */
    public static final String SUBCATEGORY = "SUBCATEGORY";

    /**
     * isCanReadyToArm
     * boolean
     */
    public static final String IS_CAN_READY_TO_ARM = "IS_CAN_READY_TO_ARM";

    /**
     * 配件enable状态
     * boolean
     */
    public static final String ENABLE = "ENABLE";

    /**
     * 配件send_id
     * String
     */
    public static final String SEND_ID = "SEND_ID";

    /**
     * 配件plugin_id
     * String
     */
    public static final String PLUGIN_ID = "plugin_id";

    /**
     * 配件是否有在线状态
     * boolean
     */
    public static final String HAVE_ONLINE_STATE = "HAVE_ONLINE_STATE";

    /**
     * 时间
     * long/int
     */
    public static final String TIME_COMMON = "time";

    /**
     * 是否第三方配件
     * boolean
     */
    public static final String IS_THIRD_PART_PLUGIN = "IS_THIRD_PART_PLUGIN";

    /**
     * 配件开关状态
     * {@link PanelConstant.PluginSwitchState}
     */
    public static final String PLUGIN_SWITCH_STATE = "PLUGIN_SWITCH_STATE";

    /**
     * 震动门磁
     * List
     */
    public static final String VIBRATION = "VIBRATION";

    /**
     * 插座
     * List
     */
    public static final String SMART_PLUG = "SMART_PLUG";

    /**
     * 红外
     * List
     */
    public static final String PIR = "PIR";

    /**
     * 门窗探测器
     * List
     */
    public static final String DOOR_WINDOW = "DOOR_WINDOW";

    /**
     * 配件信息
     * Map
     */
    public static final String PLUGIN_INFO = "PLUGIN_INFO";

    /**
     * cmd
     * String
     */
    public static final String CMD = "cmd";

    /**
     * 是否在线
     * boolean
     */
    public static final String IS_ONLINE = "isonline";

    /**
     * 连接状态
     * int
     */
    public static final String CONNECTION = "connection";

    /**
     * 布撤防
     * int
     */
    public static final String ARM_STATUS = "armStatus";

    /**
     * boolean
     */
    public static final String RECORD_DISARM = "recordDisarm";

    /**
     * int
     */
    public static final String ARM_TYPE = "armType";

    /**
     * boolean
     */
    public static final String EXIT_DELAY_SOUND = "exitdelaysound";

    /**
     * List<int>
     */
    public static final String OPTIONS = "options";

    /**
     * boolean
     */
    public static final String SOUND_ENABLE_EXIT_DELAY = "soundEnable";

    /**
     * boolean
     */
    public static final String ENTRY_DELAY_SOUND = "entrydelaysound";
    /**
     * list
     */
    public static final String PLUGINS = "plugins";
    /**
     * String
     */
    public static final String PLUGIN = "plugin";
    /**
     * list
     */
    public static final String THIRD_PARTY_PLUGINS = "thirdpartyplugins";
    /**
     * list
     */
    public static final String NEW_ASK_PLUGIN = "newaskplugin";
    /**
     * String
     */
    public static final String DATAS_ENTRY_DELAY = "datas";
    /**
     * String
     */
    public static final String THIRD_PARTY_DATAS_ENTRY_DELAY = "thirdpartydatas";
    /**
     * String
     */
    public static final String NEW_ASK_DATAS_ENTRY_DELAY = "newaskdatas";
    /**
     * String
     */
    public static final String TIMESTAMP = "timestamp";
    /**
     * String
     */
    public static final String FILTERS = "filters";

    /**
     * boolean
     */
    public static final String ON = "on";
    /**
     * int
     */
    public static final String BLOCK = "block";
    /**
     * boolean
     */
    public static final String FORCE = "force";


    /**
     * SmartPlug独有KEY
     */
    @Keep
    public static final class SmartPlug {
        /**
         * 是否是ASK类型的SmartPlug
         * boolean
         */
        public static final String IS_ASK_SMART_PLUG = "IS_ASK_SMART_PLUG";
        /**
         * 开关状态
         * boolean
         */
        public static final String ON = "on";
    }

    /**
     * DoorSensor独有KEY
     */
    @Keep
    public static final class DoorSensor {
        /**
         * 是否可以ReadyToArm
         * boolean
         */
        public static final String CAN_READY_TO_ARM = "CAN_READY_TO_ARM";

        /**
         * 是否新型门磁
         * boolean
         */
        public static final String IS_SMART_DOOR_SENSOR = "IS_SMART_DOOR_SENSOR";

        /**
         * 屏蔽状态
         * int
         */
        public static final String BLOCK_TYPE = "block";
        /**
         * 门磁开合状态通知
         * boolean
         */
        public static final String PUSH_STATUS = "push_status";
    }

    /**
     * WirelessSiren独有KEY
     */
    @Keep
    public static final class WirelessSiren {
        /**
         * 警笛设置数据
         * String
         */
        public static final String SIREN_DATA = "SIREN_DATA";

        /**
         * 警笛报警提示音
         * int
         */
        public static final String MUSIC = "music";

        /**
         * 警笛声音大小
         * int
         */
        public static final String VOLUME = "volume";


    }

    /**
     * Doorbell独有KEY
     */
    @Keep
    public static final class Doorbell {
        /**
         * long
         */
        public static final String RECORD_TIME = "RECORD_TIME";

        /**
         * String
         */
        public static final String IMAGE = "IMAGE";
    }

    /**
     * RemoteControl-5键遥控独有key
     */
    @Keep
    public static final class RemoteControl {
        /**
         * 当前控制的插座信息
         * Map
         */
        public static final String CUSTOMIZE_PLUG = "CUSTOMIZE_PLUG";

        /**
         * 可以控制的插座列表
         * List
         */
        public static final String PLUG_LIST = "PLUG_LIST";
    }

    /**
     * SmartButton
     */
    @Keep
    public static final class SmartButton {
        /**
         * 单击指令
         * Map
         */
        public static final String ACTION_SINGLE_PRESS = PanelConstant.SmartButton.SERVICE_ACTION_SINGLE_PRESS;

        /**
         * 双击指令
         * Map
         */
        public static final String ACTION_DOUBLE_PRESS = PanelConstant.SmartButton.SERVICE_ACTION_DOUBLE_PRESS;

        /**
         * 长按指令
         * Map
         */
        public static final String ACTION_LONG_PRESS = PanelConstant.SmartButton.SERVICE_ACTION_LONG_PRESS;

        /**
         * 场景
         * String
         */
        public static final String SCENARY = "SCENARY";

        /**
         * 警笛声音大小-下标
         * int
         */
        public static final String VOLUME = "VOLUME";

        /**
         * 警笛音乐-下标
         * int
         */
        public static final String MUSIC = "MUSIC";

        /**
         * 插座、灯泡的开关状态
         * int 0:开 1:关 2:反转
         */
        public static final String ACTION = "ACTION";

        /**
         * 安防指令
         * String
         */
        public static final String CMD = "CMD";

        /**
         * 更新Action信息
         * List<JsonObject>
         */
        public static final String ACTION_CONFIG = "actionConfig";
    }

    @Keep
    public static final class CareMode {
        /**
         * 当前的CareMode配置
         * Map
         */
        public static final String CARE_MODE_CONFIG = "CARE_MODE_CONFIG";

        /**
         * CareMode的开关状态
         * boolean
         */
        public static final String CARE_MODE = "care_mode";

        /**
         * 延时报警时长
         * int
         */
        public static final String ALARM__DELAY__TIME = "alarm_delay_time";

        /**
         * 无活动报警时长
         * int
         */
        public static final String NO__ACTION__TIME = "no_action_time";

        /**
         * 延时报警时长
         * long
         */
        public static final String ALARM_DELAY_TIME = "alarmDelayTime";

        /**
         * 无活动报警时长
         * long
         */
        public static final String NO_ACTION_TIME = "noActionTime";


        /**
         * 门窗探测器
         * List
         */
        public static final String DOOR_WINDOW = "door_window";

        /**
         * 配件信息
         * Map
         */
        public static final String PLUGIN_INFO = "plugin_info";

        /**
         * 震动门磁
         * List
         */
        public static final String VIBRATION = "vibration";

        /**
         * 插座
         * List
         */
        public static final String SMART_PLUG = "smart_plug";

        /**
         * 红外
         * List
         */
        public static final String PIR = "pir";

        public static final String ON = "on";

        /**
         * String
         */
        public static final String PLUGINS = "plugins";
    }

    /**
     * 主机
     */
    @Keep
    public static final class Panel {
        /**
         * 主机是否在线
         * boolean
         */
        public static final String IS_ONLINE = "isOnline";
        /**
         * 主机名
         * String
         */
        public static final String NAME = "name";
        /**
         * 主机布撤防状态
         * int 0 : arm状态 1 : home arm状态 2 : disarm状态 3 : 自定义场景模式状态
         */
        public static final String ARM_STATUS = "armStatus";
        /**
         * 是否在报警
         * boolean
         */
        public static final String SOS = "sos";
        /**
         * 延时布防的秒数
         * int
         */
        public static final String EXIT_DELAY = "exitDelay";
        /**
         * 延时报警的秒数
         * int
         */
        public static final String ENTRY_DELAY = "entryDelay";
        /**
         * 用户权限
         * int 10:guest, 20:user, 30:admin
         */
        public static final String ROLE = "role";
        /**
         * 是否已经设置过设置通知的短信，push内容
         * String
         */
        public static final String IS_MESSAGE_SET = "isMessageSet";
        /**
         * 主机时区
         * String
         */
        public static final String TIMEZONE = "timezone";
        /**
         * 是否插电
         * boolean
         */
        public static final String IS_CHARGE = "isCharge";
        /**
         * 主机电量
         * int
         */
        public static final String BATTERY_LEVEL = "batteryLevel";
        /**
         * 网络类型
         * int 0:有线；1:无线
         */
        public static final String NET_TYPE = "netType";
        /**
         * 主机Ip
         * String
         */
        public static final String LAN_IP = "lanIP";
        /**
         * Sim卡状态
         * int
         */
        public static final String SIM_STATUS = "simStatus";
        /**
         * 是否在更新
         * boolean
         */
        public static final String UPGRADING = "upgrading";
        /**
         * String
         */
        public static final String DEVICE_TOKEN = "deviceToken";
        /**
         * String
         */
        public static final String PASSWORD = "password";
        /**
         * string
         */
        public static final String OLD_PASSWORD = "old_password";
        /**
         * boolean
         */
        public static final String RETAIN_PLUGINS = "retainPlugins";
        /**
         * String
         */
        public static final String FIRMWARE_VERSION = "firmwareVersion";

        /**
         * String
         */
        public static final String OPERATE_QUEUE_SIZE = "backup_queue_size";
        /**
         * boolean -当前收到的cmd result 是否是最新的
         */
        public static final String IS_LATEST_CMD = "is_latest_cmd";
        /**
         * String wifi ssid
         */
        public static final String SSID = "ssid";
        /**
         * String 2g/4g主机
         */
        public static final String SIM_NETWORK = "sim_network";
        /**
         * WIFI地址
         */
        public static final String WIFI_MAC_ADDRESS = "wifi_mac_addr";
        /**
         * WIFI信号强度
         */
        public static final String WIFI_RSSI = "wifi_rssi";

        /**
         * CareMode相关
         */
        public static final String LAST_NO_ACTION_TIME = "last_no_action_time";
        public static final String ALARM_DELAY_TIME = "alarm_delay_time";
        public static final String NO_ACTION_TIME = "no_action_time";
    }

    /**
     * 主机配件数量信息
     */
    @Keep
    public static class PanelPluginQuantity {
        /**
         * Map
         */
        public static final String QUANTITY_INFO = "quantity_info";

        /**
         * int
         */
        public static final String DOOR_BELL_COUNT = "doorbellCount";
        public static final String THIRD_PARTY_ACCESSORY_COUNT = "thirdPartyAccessoryCount";
        public static final String IP_CAMERA_COUNT = "ipCameraCount";
        public static final String DOOR_SENSOR_COUNT = "doorSensorCount";
        public static final String KEYPAD_ACCESSORY_COUNT = "keypadAccessoryCount";
        public static final String RELAY_ACCESSORY_COUNT = "relayAccessoryCount";
        public static final String REMOTE_CONTROL_COUNT = "remoteControlCount";
        public static final String SECURITY_ACCESSORY_COUNT = "securityAccessoryCount";
        public static final String SIREN_COUNT = "sirenCount";
        public static final String SMART_BUTTON_COUNT = "smartButtonCount";
        public static final String SMART_PLUG_COUNT = "smartPlugCount";
        public static final String SIGNAL_REPEATER_PLUG_COUNT = "signalRepeaterPlugCount";
    }

    /**
     * 胁迫报警
     */
    @Keep
    public static final class IntimidateSos {
        /**
         * boolean
         */
        public static final String ENABLE = "enable";
        /**
         * String
         */
        public static final String PASSWORD = "password";

        /**
         * boolean
         */
        public static final String HAD_SET_PASSWORD = "password";

        /**
         * String
         */
        public static final String SMS = "sms";
    }

    /**
     * Cid
     */
    @Keep
    public static final class Cid {
        /**
         * boolean
         */
        public static final String ENABLE = "enable";
        /**
         * String
         */
        public static final String COUNTRY_CODE = "countrycode";

        /**
         * String
         */
        public static final String CONTACT_ID_CODE = "contactidcode";

        /**
         * String
         */
        public static final String PHONE = "phone";
    }

    /**
     * Timezone
     */
    @Keep
    public static final class Timezone {
        /**
         * String
         */
        public static final String TIMEZONE = "timezone";
        /**
         * List
         */
        public static final String TIMEZONE_LIST = "timezonelist";
    }

    /**
     * ReadyToArm
     */
    @Keep
    public static final class ReadyToArm {
        /**
         * String
         */
        public static final String URL = "url";
        /**
         * int
         */
        public static final String COUNT = "count";
        /**
         * boolean
         */
        public static final String ENABLE = "enable";

        /**
         * String
         */
        public static final String TASK = "task";
    }

    /**
     * AdvancedSetting-高级设置相关开关
     */
    @Keep
    public static final class AdvancedSetting {
        /**
         * boolean
         */
        public static final String IS_ON = "ison";
        /**
         * boolean
         */
        public static final String OFFLINE_SMS = "offline_sms";
        /**
         * String
         */
        public static final String PANEL_NAME = "panel_name";
    }

    /**
     * ArmSoundSetting-设置布撤防声音
     */
    @Keep
    public static final class ArmSoundSetting {
        /**
         * boolean
         */
        public static final String ON = "on";
    }

    /**
     * RestrictMode-限制模式
     */
    @Keep
    public static final class RestrictMode {
        /**
         * boolean
         */
        public static final String ON = "on";
    }

    /**
     * 4g-4g设置
     */
    @Keep
    public static final class Panel4GInfo {
        /**
         * boolean
         */
        public static final String AUTOMATIC = "automatic";
        /**
         * String
         */
        public static final String NODE_NAME = "node_name";
        /**
         * String
         */
        public static final String PASSWORD = "password";
        /**
         * String
         */
        public static final String USER_NAME = "user_name";
    }

    /**
     * cms-cms设置
     */
    @Keep
    public static final class Cms {
        /**
         * String
         */
        public static final String DATAS = "datas";
        /**
         * String
         */
        public static final String PROTOCOL_NAME = "protocolName";
        /**
         * Map
         */
        public static final String INFO = "info";
        /**
         * String
         */
        public static final String PRIMARY_IP = "primary_ip";
        /**
         * int
         */
        public static final String PRIMARY_PORT = "primary_port";
        /**
         * String
         */
        public static final String SECONDARY_IP = "secondary_ip";
        /**
         * int
         */
        public static final String SECONDARY_PORT = "secondary_port";
        /**
         * String
         */
        public static final String ACCOUNT_NUMBER = "account_number";
        /**
         * boolean
         */
        public static final String ENCRYPTION = "encryption";
        /**
         * String
         */
        public static final String ENCRYPTION_KEY = "encryption_key";
        /**
         * String
         */
        public static final String NETWORK = "network";
    }

    /**
     * EventList
     */
    @Keep
    public static final class EventList {
        /**
         * String
         */
        public static final String EVENT_LIST = "eventlist";
        /**
         * boolean
         */
        public static final String DOOR_WINDOW = "doorWindow";
        /**
         * boolean
         */
        public static final String TAMPER = "tamper";
        /**
         * Map
         */
        public static final String EVENT_LIST_SETTING = "eventlist_setting";
        /**
         * boolean
         */
        public static final String dw_event_log = "dw_event_log";
        /**
         * boolean
         */
        public static final String TAMPER_EVENT_LOG = "tamper_event_log";

        /**
         * String
         */
        public static final String USER = "user";
        public static final String CMD_NAME = "cmdname";
        public static final String PHOTO = "photo";
        public static final String TYPE = "type";
        public static final String MESSAGE_ID = "messageid";
        public static final String CMD_TYPE = "cmdType";
        public static final String PLUGIN_ID = "pluginid";
        public static final String SUBCATEGORY = "subcategory";

        /**
         * long
         */
        public static final String TIME = "time";
        public static final String DURATION = "duration";

        /**
         * int
         */
        public static final String RESULT = "result";
        public static final String CATEGORY = "category";

        /**
         * Map
         */
        public static final String DATA = "data";
        /**
         * String
         */
        public static final String UID = "uid";
        /**
         * int
         */
        public static final String NEW_PERMISSION = "newpermission";
        public static final String OLD_PERMISSION = "oldpermission";
        /**
         * boolean
         */
        public static final String POWER_STATUS = "powerstatus";
    }

    /**
     * 分享主机
     */
    @Keep
    public static final class SharePanel {
        /**
         * int
         */
        public static final String ROLE = "role";
        /**
         * String
         */
        public static final String SHARE_CODE = "sharecode";
    }

    /**
     * 主机用户、紧急联系人
     */
    @Keep
    public static final class PanelMember {
        /**
         * String
         */
        public static final String DEVICE_CONTACTS = "devicecontacts";
        /**
         * String
         */
        public static final String USER_CONTACTS = "usercontacts";
        /**
         * String
         */
        public static final String CONTACTS = "contacts";
        /**
         * String
         */
        public static final String USER_ID = "userid";

        /**
         * String
         */
        public static final String ROLE = "role";

        /**
         * boolean
         */
        public static final String PUSH = "push";
        /**
         * boolean
         */
        public static final String SMS = "sms";
        /**
         * boolean
         */
        public static final String PUSH_SYSTEM = "pushSystem";
        /**
         * boolean
         */
        public static final String PUSH_STATUS = "pushStatus";
        /**
         * boolean
         */
        public static final String PUSH_ALARM = "pushAlarm";
        /**
         * boolean
         */
        public static final String SMS_SYSTEM = "smsSystem";
        /**
         * boolean
         */
        public static final String SMS_STATUS = "smsStatus";
        /**
         * boolean
         */
        public static final String SMS_ALARM = "smsAlarm";
        /**
         * String
         */
        public static final String CONTACT_ID = "contactid";
        /**
         * String
         */
        public static final String NAME = "name";
        /**
         * String
         */
        public static final String PHONE = "phone";
    }

    /**
     * 推送语言
     */
    @Keep
    public static final class PushLanguage {
        /**
         * String
         */
        public static final String LANG_ID = "langID";
        /**
         * String
         */
        public static final String MESSAGE = "message";
        /**
         * String
         */
        public static final String DEVICE_TEXT = "device_text";
        /**
         * String
         */
        public static final String LANG = "lang";

    }

    /**
     * 继电器
     */
    @Keep
    public static final class Relay {
        /**
         * String
         */
        public static final String ACTION = "action";
    }

    /**
     * CMD操作结果
     */
    @Keep
    public static final class CmdResult {
        /**
         * cmd
         * String
         */
        public static final String CMD = "cmd";
        /**
         * operate cmd
         * String
         */
        public static final String OPERATION_CMD = "operationCMD";

        /**
         * resultType
         * int
         */
        public static final String RESULT_TYPE = "resultType";

        /**
         * status
         * int
         */
        public static final String STATUS = "status";

        /**
         * originStatus
         * int
         */
        public static final String ORIGIN_STATUS = "originStatus";
        /**
         * errorMessage
         * String
         */
        public static final String ERROR_MESSAGE = "errorMessage";

        /**
         * errorMessage
         * String
         */
        public static final String RESULT = "result";

        /**
         * 是否自己触发的
         * boolean
         */
        public static final String OWNER = "owner";

        public static final int SUCCESS = 1;

        public static final int FAIL = 0;
    }

    /**
     * 当前报警信息
     */
    @Keep
    public static final class SosInfo {
        /**
         * boolean
         */
        public static final String SOS_ALARM = "sosalarm";
        public static final String IS_DEVICE = "isdevice";
        /**
         * long
         */
        public static final String TIME = "time";
        /**
         * String
         */
        public static final String UID = "uid";
        public static final String PHOTO = "photo";
        public static final String PLUGIN_ID = "pluginid";
        public static final String PLUGIN_NAME = "pluginname";
        public static final String INTIMIDATION_MESSAGE = "intimidationmessage";
        public static final String SUBCATEGORY = "subcategory";
        public static final String CATEGORY = "category";
        public static final String SOS_TYPE = "sostype";
    }

    /**
     * Sim卡信息
     */
    @Keep
    public static final class SimInfo {
        /**
         * String
         */
        public static final String D_PHONE = "d_phone";
        public static final String IMEI = "imei";
        public static final String IMSI = "imsi";
        public static final String CSQ = "csq";
        public static final String PIN = "pin";

        /**
         * int
         */
        public static final String SIM = "sim";
    }

    /**
     * 可调节灵敏度红外信息
     */
    @Keep
    public static final class PirSensitivityInfo {
        public static final String GMTIME = "gmtime";
        public static final String PIR_SETTING_ENABLED = "pir_setting_enabled";
        public static final String SENSITIVITY = "sensitivity";
    }
}
