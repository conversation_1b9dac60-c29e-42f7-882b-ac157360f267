package com.dinsafer.panel.add.callback;

import android.bluetooth.BluetoothGatt;
import androidx.annotation.Keep;

import com.clj.fastble.data.BleDevice;
import com.clj.fastble.exception.BleException;

/**
 * 蓝牙连接状态监听
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/2 2:04 PM
 */
@Keep
public interface IPanelConnectListener {
    void onStartConnect();

    void onConnectFail(BleDevice bleDevice, BleException exception);

    void onConnectSuccess(BleDevice bleDevice, BluetoothGatt gatt, int status);

    void onDisConnected(BleDevice bleDevice, boolean isActiveDisConnected, BluetoothGatt gatt, int status);

    void onNotifySuccess();

    void onNotifyFailure(BleDevice bleDevice, BleException exception);
}
