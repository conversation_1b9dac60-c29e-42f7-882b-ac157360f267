package com.dinsafer.panel.add.plugin;

import android.content.Context;
import androidx.annotation.Keep;
import android.text.TextUtils;

import com.dinsafe.Dinsafe;
import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.activtor.AddPlugsBuilder;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.activtor.bean.Plugin;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.utils.BinaryUtils;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dincore.utils.RandomStringUtils;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.operate.PanelOperatorConstant;
import com.dinsafer.panel.operate.callback.PanelCallback;
import com.dinsafer.panel.operate.net.IPanelWebSocketCallBack;
import com.dinsafer.panel.util.PanelSecretUtil;

import org.json.JSONException;
import org.json.JSONObject;

import okhttp3.Response;
import okhttp3.WebSocket;

/**
 * 自家配件绑定
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/8 4:36 PM
 */
@Keep
public class DinPluginBinder extends BasePluginBinder implements IPanelWebSocketCallBack {
    private String mMessageID;
    private final String mDeviceId, mDeviceToken;

    public DinPluginBinder(Context mContext, String deviceId, String deviceToken) {
        super(mContext);
        this.mDeviceId = deviceId;
        this.mDeviceToken = deviceToken;
        PanelManager.getInstance().getPanelOperator().addCallBack(this);
    }

    @Override
    public void bindDevice(Plugin plugin) {
        if (plugin.getPluginID().startsWith("!") &&
                !TextUtils.isEmpty(plugin.getSourceData())) {
            if ("D".equals(String.valueOf(plugin.getPluginID().charAt(1)))) {
                toAddDoorBell(plugin);
                return;
            }

            toBindNewAskPlugin(plugin);
            return;
        }

        toBindOldPlugin(plugin);
    }

    @Override
    public void destroyBinder() {
        DDLog.i(TAG, "destroyBinder");
        PanelManager.getInstance().getPanelOperator().removeCallBack(this);
        super.destroyBinder();
    }

    private void toAddDoorBell(Plugin plugin) {
        DDLog.i(TAG, "toAddDoorBell");
        try {
            JSONObject data = new JSONObject(plugin.getSourceData());
            data.put("name", plugin.getPluginName());
            data.put("devtoken", PanelManager.getInstance().getCurrentPanelToken());
            PanelManager.getInstance().getNetworkRequestManager().requestAddDoorBell(
                    PanelManager.getInstance().getCurrentPanelToken(), data,
                    new PanelCallback.NetworkResult<StringResponseEntry>() {
                        @Override
                        public void onSuccess(StringResponseEntry result) {
                            callBackBindResult(1, null);
                        }

                        @Override
                        public void onError(int errorCode, String errorMsg) {
                            DDLog.e(TAG, "Error on toAddDoorBell, code: " + errorCode + ", msg: " + errorMsg);
                            callBackBindResult(ErrorCode.ACTIVTOR_BIND_DEVICE_FAIL, errorMsg);
                        }
                    }
            );
        } catch (Exception e) {
            DDLog.e(TAG, "Error on add doorbell!!!");
            e.printStackTrace();
            callBackBindResult(ErrorCode.ACTIVTOR_BIND_DEVICE_FAIL, "未知错误");
        }
    }

    /**
     * @param plugin
     */
    private void toBindOldPlugin(Plugin plugin) {
        DDLog.i(TAG, "toBindOldPlugin, PLUGIN: " + plugin);
        mMessageID = RandomStringUtils.getMessageId();
        String decodeId;
        String id;
        if (isOfficialPlugin(plugin)) {
            decodeId = Dinsafe.str64ToHexStr(plugin.getPluginID());
            id = plugin.getPluginID();
        } else {
            decodeId = plugin.getPluginID();
            id = decodeId.substring(0, 4);
        }

        // 所有配件添加都有这个数据，但服务器会过滤掉这个，只用于警笛
        String sirenData = "0,0,0,1,0,2,1,1,10,10";

        AddPlugsBuilder builder = AddPlugsBuilder.newInstance()
                .setUid(DinCore.getUserInstance().getUser().getUid())
                .setDecodeid(decodeId)
                .setDeviceToken(mDeviceToken)
                .setMessageid(mMessageID)
                .setPlugid(id)
                .setPlugName(plugin.getPluginName())
                .setSirenSetting(sirenData);

        if (isOfficialPlugin(plugin)) {
            // 如果有qrcode，证明配件二维码上面的id和配件真实id不一致，那么，就用二维码上面的id提交到服务器
            if (!TextUtils.isEmpty(plugin.getQrCode())) {
                builder.setPlugid(plugin.getQrCode());
            }

            PanelManager.getInstance().getNetworkRequestManager().requestAddPlugs(
                    PanelManager.getInstance().getCurrentPanelToken(),
                    builder, new PanelCallback.NetworkResult<StringResponseEntry>() {
                        @Override
                        public void onSuccess(StringResponseEntry result) {

                        }

                        @Override
                        public void onError(int errorCode, String errorMsg) {
                            DDLog.e(TAG, "Error on toAddOfficalPlugin, code: " + errorCode + ", msg: " + errorMsg);
                            callBackBindResult(ErrorCode.ACTIVTOR_BIND_DEVICE_FAIL, errorMsg);
                        }
                    });
        } else {
            PanelManager.getInstance().getNetworkRequestManager().requestAddNotOfficialPlugs(
                    PanelManager.getInstance().getCurrentPanelToken(), builder, new PanelCallback.NetworkResult<StringResponseEntry>() {
                        @Override
                        public void onSuccess(StringResponseEntry result) {

                        }

                        @Override
                        public void onError(int errorCode, String errorMsg) {
                            DDLog.e(TAG, "Error on toAddNotOfficalPlugin, code: " + errorCode + ", msg: " + errorMsg);
                            callBackBindResult(ErrorCode.ACTIVTOR_BIND_DEVICE_FAIL, errorMsg);
                        }
                    }
            );
        }
    }

    private void toBindNewAskPlugin(Plugin plugin) {
        DDLog.i(TAG, "toBindNewAskPlugin, PLUGIN: " + plugin);
        mMessageID = RandomStringUtils.getMessageId();
        JSONObject data = null;
        try {
            data = new JSONObject(plugin.getSourceData());
            data.put("name", plugin.getPluginName());
            if (DDJSONUtil.getString(data, "stype").equals("21")
                    || DDJSONUtil.getString(data, "stype").equals("22")
                    || DDJSONUtil.getString(data, "stype").equals("34")
                    || DDJSONUtil.getString(data, "stype").equals("35")) {
                // 新警笛
                toGetSirentData(data);
            }
            // 新的插座，需要添加字段
            boolean isNewAskPlug = DDJSONUtil.getString(data, "stype").equals("3E");
            if (isNewAskPlug) {
                data.put("plugin_item_smart_plug_enable", 0);
            }
            plugin.setSourceData(data.toString());

            PanelManager.getInstance().getNetworkRequestManager().requestAddNewASKPlugin(
                    PanelManager.getInstance().getCurrentPanelToken(), mMessageID,
                    plugin.getSourceData(), new PanelCallback.NetworkResult<StringResponseEntry>() {
                        @Override
                        public void onSuccess(StringResponseEntry result) {
                        }

                        @Override
                        public void onError(int errorCode, String errorMsg) {
                            DDLog.e(TAG, "Error on toBindNewAskPlugin, code: " + errorCode + ", msg: " + errorMsg);
                            callBackBindResult(ErrorCode.ACTIVTOR_BIND_DEVICE_FAIL, errorMsg);
                        }
                    }
            );
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void toGetSirentData(JSONObject data) {
        int disArmTone = DDJSONUtil.getBoolean(data, "disarm_tone") ? 1 : 0;
        int homeTone = DDJSONUtil.getBoolean(data, "homearm_tone") ? 1 : 0;
        int armTone = DDJSONUtil.getBoolean(data, "arm_tone") ? 1 : 0;
        int sosTime = DDJSONUtil.getInt(data, "sos_time");
        int disArmLight = DDJSONUtil.getInt(data, "disarm_light");
        int sosLight = DDJSONUtil.getInt(data, "sos_light");
        int homeArmTime = DDJSONUtil.getInt(data, "homearm_light");
        int armTime = DDJSONUtil.getInt(data, "arm_light");
        int promptVolume = DDJSONUtil.getInt(data, "prompt_volume");
        int alarmVolume = DDJSONUtil.getInt(data, "alarm_volume");

        // 默认int类型数据是-1时重置为0，否则二进制拼接后可能出现长度大于32bit导致错误
        if (-1 == sosTime) {
            sosTime = 0;
        }
        if (-1 == disArmLight) {
            disArmLight = 0;
        }
        if (-1 == sosLight) {
            sosLight = 0;
        }
        if (-1 == homeArmTime) {
            homeArmTime = 0;
        }
        if (-1 == armTime) {
            armTime = 0;
        }
        if (-1 == promptVolume) {
            promptVolume = 0;
        }
        if (-1 == alarmVolume) {
            alarmVolume = 0;
        }
//                    第一项


        String sosTimeString = Integer.toBinaryString(sosTime);
        if (sosTimeString.length() < 5) {
            int length = 5 - sosTimeString.length();
            for (int i = 0; i < length; i++) {
                sosTimeString = "0" + sosTimeString;
            }
        }

        String firstByte = Integer.toBinaryString(disArmTone) +
                Integer.toBinaryString(homeTone) +
                Integer.toBinaryString(armTone) +
                sosTimeString;

        String firstHex = Integer.toHexString(Integer.valueOf(firstByte, 2));
        if (firstHex.length() < 2) {
//                        前面补0
            firstHex = "0" + firstHex;
        }

        String twoByte = BinaryUtils.intToByte(disArmLight) +
                BinaryUtils.intToByte(sosLight) +
                BinaryUtils.intToByte(homeArmTime) +
                BinaryUtils.intToByte(armTime);

        String twoHex = Integer.toHexString(Integer.valueOf(twoByte, 2));
        if (twoHex.length() < 2) {
//                        前面补0
            twoHex = "0" + twoHex;
        }

//                    4位0为扩展位
        String threeByte = BinaryUtils.intToByte(promptVolume) +
                BinaryUtils.intToByte(alarmVolume) + "0000";

        String threeHex = Integer.toHexString(Integer.valueOf(threeByte, 2));
        if (threeHex.length() < 2) {
//                        前面补0
            threeHex = "0" + threeHex;
        }

        try {
            data.put("advancesetting", firstHex + "," + twoHex + "," + threeHex);
            data.remove("disarm_tone");
            data.remove("homearm_tone");
            data.remove("arm_tone");
            data.remove("sos_time");
            data.remove("disarm_light");
            data.remove("sos_light");
            data.remove("homearm_light");
            data.remove("arm_light");
            data.remove("prompt_volume");
            data.remove("alarm_volume");
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * TODO 是否官方配件
     * <p>
     * 当前默认都是官方配件
     *
     * @param plugin 配件信息
     * @return true: 官方配件
     */
    private boolean isOfficialPlugin(Plugin plugin) {
        return true;
    }

    @Override
    protected void callBackBindResult(int code, String msg) {
        super.callBackBindResult(code, msg);
        destroyBinder();
    }

    @Override
    public void onOpen(WebSocket webSocket, Response response) {

    }

    @Override
    public void onFailure(WebSocket webSocket, Throwable t, Response response) {

    }

    @Override
    public void onClosing(WebSocket webSocket, int code, String reason) {

    }

    @Override
    public void onMessage(String msg) {
        DDLog.i(TAG, "onMessage, MSG: " + msg);
        try {
            JSONObject jsonObject = new JSONObject(msg);
            final int status = DDJSONUtil.getInt(jsonObject, "Status");
            final String errorMsg = DDJSONUtil.getString(jsonObject, "ErrorMessage");
            final String operateCmd = DDJSONUtil.getString(jsonObject, "Cmd");
            final String messageId = DDJSONUtil.getString(jsonObject, "MessageId");
            final String action = DDJSONUtil.getString(jsonObject, "Action");

            if (PanelOperatorConstant.ACTION.EVENT_RESULT.equals(action)
                    && mMessageID.equals(messageId)
                    && (PanelOperatorConstant.CMD.ADD_PLUGIN.equals(operateCmd)
                    || PanelOperatorConstant.CMD.ADD_NEWASKPLUGIN.equals(operateCmd))) {
                mMessageID = "";
                if (1 == status) {
                    DDLog.i(TAG, "成功添加配件");
                    String result = jsonObject.getString(PanelOperatorConstant.KEY.RESULT);
                    if (!TextUtils.isEmpty(result)) {
                        result = PanelSecretUtil.getReverSC(result);
                        JSONObject resultJson = new JSONObject(result);
                        jsonObject.put("Result", resultJson.toString());
                    }
                    callBackBindResult(1, jsonObject.toString());
                } else if (-50 == status) {
                    DDLog.i(TAG, "配件已存在");
                    callBackBindResult(ErrorCode.ACTIVTOR_ALREAD_HAS_PLUGIN, errorMsg);
                } else {
                    DDLog.e(TAG, "添加配件失败");
                    callBackBindResult(status, errorMsg);
                }
            }
        } catch (JSONException e) {
            MsctLog.e(TAG, "Error on onMessage.");
            e.printStackTrace();
        }
    }
}
