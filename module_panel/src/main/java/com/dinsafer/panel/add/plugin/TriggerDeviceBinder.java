package com.dinsafer.panel.add.plugin;

import android.content.Context;
import androidx.annotation.Keep;

import com.dinsafer.dincore.activtor.api.base.IPluginScanCallback;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.activtor.bean.Plugin;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dincore.utils.MapUtils;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.operate.callback.PanelCallback;
import com.dinsafer.panel.operate.net.IPanelWebSocketCallBack;
import com.dinsafer.panel.operate.net.PanelWebSocketManager;

import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;

import java.util.Map;

import okhttp3.Response;
import okhttp3.WebSocket;
import rx.Observable;
import rx.Subscriber;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * 触发配对添加器
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/6/16 15:30
 */
@Keep
public class TriggerDeviceBinder extends BasePluginBinder
        implements IPanelWebSocketCallBack {
    private static final String TAG = TriggerDeviceBinder.class.getSimpleName();

    private PanelWebSocketManager webSocketManager;
    private String panelIp;
    private boolean isOfficialPlugin = true;
    private String sType;
    ITriggerDeviceCallback mTriggerCallback;
    IPluginScanCallback mPluginScanCallback;

    private final Observable<String> wsObservable = Observable.create(new Observable.OnSubscribe<String>() {
        @Override
        public void call(Subscriber<? super String> subscriber) {
            subscriber.onNext(toConnectWS());
            subscriber.onCompleted();
        }
    }).subscribeOn(Schedulers.computation()).observeOn(AndroidSchedulers.mainThread());

    public TriggerDeviceBinder(Context mContext, @NotNull String panelIp) {
        super(mContext);
        this.panelIp = panelIp;
    }

    public TriggerDeviceBinder(Context mContext) {
        super(mContext);
    }

    public void setPanelIp(String panelIp) {
        this.panelIp = panelIp;
    }

    @Override
    public void configBinder(Map<String, Object> args) {
        super.configBinder(args);
        if (null == args) {
            return;
        }

        final String ip = (String) MapUtils.get(args, "panelIp", "");
        setPanelIp(ip);
    }

    private String toConnectWS() {
        if (webSocketManager == null) {
            String url = "ws://" + panelIp + ":1949/ws";
            DDLog.i(TAG, "toConnectWS:" + url);
            webSocketManager = new PanelWebSocketManager(false, false, false, url);
            webSocketManager.addCallBack(this);
        }

        startConnectWebSocket();
        return "";
    }

    private void startConnectWebSocket() {
        if (webSocketManager != null && webSocketManager.getWebSocket() != null) {
            webSocketManager.stop();
        }
        webSocketManager.start();
    }

    @Override
    public void destroyBinder() {
        super.destroyBinder();
        if (webSocketManager != null && webSocketManager.getWebSocket() != null) {
            webSocketManager.stop();
            webSocketManager.removeCallBack(this);
            webSocketManager = null;
        }
        mTriggerCallback = null;
        mPluginScanCallback = null;
    }

    public void toStartScan(boolean isOfficialPlugin, String sType,
                            ITriggerDeviceCallback triggerCallback) {
        this.isOfficialPlugin = isOfficialPlugin;
        this.sType = sType;
        this.mTriggerCallback = triggerCallback;
        wsObservable.subscribe(s -> {
        }, throwable -> {
            if (null != mTriggerCallback) {
                mTriggerCallback.onError(throwable);
            }
        });
    }

    @Override
    public void onOpen(final WebSocket webSocket, Response response) {
        DDLog.i(TAG, "onOpen, isofficial: " + isOfficialPlugin + ", stype: " + sType);

        try {
            webSocket.send("{\"cmd\":\"CMD_TRIGGERPAIR\",\"isofficial\":" + isOfficialPlugin + ",\"stype\":\"" + sType + "\"}");
        } catch (Exception e) {
            DDLog.i(TAG, "Unable to send messages: " + e.getMessage());
        }

        if (null != mTriggerCallback) {
            mTriggerCallback.onWsOpen(response);
        }
    }

    @Override
    public void onFailure(WebSocket webSocket, Throwable e, Response response) {
        DDLog.e(TAG, "onFailure");
        e.printStackTrace();
        if (null != mTriggerCallback) {
            mTriggerCallback.onWsFailure(e, response);
        }
    }

    @Override
    public void onClosing(WebSocket webSocket, int code, String reason) {
        DDLog.i(TAG, "onClosing, code: " + code + ", reason: " + reason);
        if (null != mTriggerCallback) {
            mTriggerCallback.onWsClosing(code, reason);
        }
    }

    @Override
    public void onMessage(String messageStr) {
        DDLog.i(TAG, "onMessage： " + messageStr);
        if (null != mTriggerCallback) {
            mTriggerCallback.onWsMessage(messageStr);
        }
    }

    @Override
    public void bindDevice(Plugin plugin) {
    }

    /**
     * NewAsk配件需要从后台获取ID
     */
    public void getNewAskPlugInfo(String stype, String sendId, final IPluginScanCallback pluginScanCallback) {
        DDLog.i(TAG, "getNewAskPlugInfo, stype: " + stype + ", sendId: " + sendId);
        PanelManager.getInstance().getNetworkRequestManager().requestNewAskPlugInfo(
                PanelManager.getInstance().getCurrentPanelId(),
                PanelManager.getInstance().getCurrentPanelToken(),
                stype, sendId, new PanelCallback.NetworkResult<String>() {
                    @Override
                    public void onSuccess(String result) {
                        DDLog.i(TAG, "Success on getNewAskPlugInfo, result: " + result);
                        if (null != pluginScanCallback) {
                            JSONObject jsonObject = null;
                            try {
                                jsonObject = new JSONObject(result);
                            } catch (Exception e) {
                                DDLog.e(TAG, "Error on parse plugin info");
                                e.printStackTrace();
                            }
                            Plugin plugin = new Plugin(DDJSONUtil.getString(jsonObject, "id"));
                            plugin.setSourceData(result);
                            pluginScanCallback.onScanResult(1, plugin);
                        }
                    }

                    @Override
                    public void onError(int errorCode, String errorMsg) {
                        DDLog.e(TAG, "Error on getNewAskPlugInfo, code: " + errorCode + ", msg: " + errorMsg);
                        if (null != pluginScanCallback) {
                            pluginScanCallback.onScanResult(ErrorCode.DEFAULT, null);
                        }
                    }
                }
        );
    }

}
