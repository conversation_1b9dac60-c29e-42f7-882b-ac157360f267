package com.dinsafer.panel.add.bean;

import androidx.annotation.Keep;

/**
 * 添加主机时，发送数据到主机后主机回复数据
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/11 6:36 PM
 */
@Keep
public class PanelCmdResult {
    String cmd;
    String result;
    int status;
    int rssi;
    boolean auth;

    public PanelCmdResult(String cmd, String result, int status) {
        this.cmd = cmd;
        this.result = result;
        this.status = status;
    }

    public String getCmd() {
        return cmd;
    }

    public void setCmd(String cmd) {
        this.cmd = cmd;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public void setRssi(int rssi) {
        this.rssi = rssi;
    }

    public int getRssi() {
        return rssi;
    }

    public void setAuth(boolean auth) {
        this.auth = auth;
    }

    public boolean isAuth() {
        return auth;
    }

    @Override
    public String toString() {
        return "PanelCmdResult{" +
                "cmd='" + cmd + '\'' +
                ", result='" + result + '\'' +
                ", status=" + status +
                ", rssi=" + rssi +
                ", auth=" + auth +
                '}';
    }
}
