package com.dinsafer.panel.add;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/10 2:55 PM
 */
public class PanelAdderConstants {
    public static class CMD {
        public static final String BLE_CMD_IS_NEW_DEVICE = "IsNewDevice";
        public static final String BLE_CMD_GET_WIFI_LIST = "GetWifiList";
        public static final String BLE_CMD_SET_DEVICE_NAME = "SetDeviceName";
        public static final String BLE_CMD_SET_DEVICE_PASSWORD = "SetDevicePassword";
        public static final String BLE_CMD_VERIFY_DEVICE_PASSWORD = "VerifyDevicePassword";
        public static final String BLE_CMD_SET_WIFI_NAME = "SetWifiName";
        public static final String BLE_CMD_SET_WIFI_PASSWORD = "SetWifiPassword";
        public static final String BLE_CMD_SET_WIFI = "SetWifi";
        public static final String BLE_CMD_SET_DHCP = "SetDHCP";
        public static final String BLE_CMD_SET_IP = "SetIP";
        public static final String BLE_CMD_SET_NETMASK = "SetNetmask";
        public static final String BLE_CMD_SET_GATEWAY = "SetGateway";
        public static final String BLE_CMD_SET_DNS = "SetDNS";
        public static final String BLE_CMD_SET_STATIC_IP = "SetSIP";
        public static final String BLE_CMD_BIND_DEVICE = "BindDevice";
        public static final String BLE_CMD_DEVICE_ONLINE = "DeviceOnline";
        public static final String BLE_CMD_STOP_BLE = "StopBLE";
        public static final String BLE_CMD_GET_SIM = "GetSIM";
        public static final String BLE_CMD_SET_4G_INFO = "Set4GInfo";
        public static final String BLE_CMD_SET_4G = "Set4G";
        public static final String BLE_CMD_GET_4G = "Get4G";
        public static final String BLE_CMD_RUN_ZT = "RunZT";
    }

    public static class KEY {
        public static final String BLE_KEY_CMD = "cmd";
        public static final String BLE_KEY_STATUS = "status";
        public static final String BLE_KEY_RESULT = "result";
        public static final String BLE_KEY_AUTH = "auth";
        public static final String BLE_KEY_RSSI = "rssi";
        public static final String BLE_KEY_UID = "uid";
        public static final String BLE_KEY_USER_ID = "userid";
        public static final String BLE_KEY_SYSTEM = "system";
        public static final String BLE_KEY_HOME_ID = "home_id";
        public static final String BLE_KEY_4G_A = "a";
        public static final String BLE_KEY_4G_N = "n";
        public static final String BLE_KEY_4G_U = "u";
        public static final String BLE_KEY_4G_P = "p";
    }

    public static final int BLE_ADD_DEVICE_MODE = 0;
    public static final int BLE_CONFIG_DEVICE_NET_MODE = 1;

    public static final boolean IS_BLE_DATA_ENCRYPT = true;

    public static final int BLE_SCAN_TIMEOUT = Integer.MAX_VALUE;
    public static final int BLE_CONNECT_TIMEOUT = 50 * 1000;
    public static final int BLE_OPERATE_TIMEOUT = 50 * 1000;
}
