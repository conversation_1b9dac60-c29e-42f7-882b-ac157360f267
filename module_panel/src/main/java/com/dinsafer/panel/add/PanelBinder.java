package com.dinsafer.panel.add;

import android.app.Application;
import android.bluetooth.le.ScanRecord;
import android.content.Context;
import androidx.annotation.Keep;

import com.clj.fastble.data.BleDevice;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.activtor.bean.Plugin;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.add.callback.IPanelCmdCallback;
import com.dinsafer.panel.add.callback.IPanelConnectListener;
import com.dinsafer.panel.add.callback.IPanelScanListener;
import com.dinsafer.panel.add.plugin.BleController;
import com.dinsafer.panel.common.IPanelAdder;
import com.dinsafer.panel.common.IPanelAdderOperator;

import java.lang.reflect.Method;
import java.math.BigInteger;
import java.util.Arrays;

/**
 * 主机蓝牙添加管理
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/11 5:05 PM
 */
@Keep
public class PanelBinder extends BasePluginBinder implements IPanelAdder, IPanelAdderOperator {
    private static final String TAG = PanelBinder.class.getSimpleName();

    private final PanelAdderOperator mPanelAdderOperator = new PanelAdderOperator();

    @Keep
    public PanelBinder(Context mContext) {
        super(mContext);
    }

    @Keep
    @Override
    public boolean isOpenedBluetooth() {
        return BleController.getInstance().isOpenBlue();
    }

    @Keep
    @Override
    public void init(Application app, String uid, String userId, String familyId) {
        BleController.getInstance().initBle(app);
        mPanelAdderOperator.init(uid, userId, familyId);
    }

    @Keep
    @Override
    public void initScanRule(long bleScanTime, String[] serviceUuids, String writeUuid, String notifyUuid) {
        BleController.getInstance().setScanRuleWithUUID(bleScanTime, serviceUuids, writeUuid, notifyUuid);
    }

    @Keep
    @Override
    public void startScanPanel(IPanelScanListener scanListener) {
        if (BleController.getInstance().isScanning()) {
            DDLog.i(TAG, "当前正在扫描，先停止上一次的扫描");
            BleController.getInstance().stopScan();
        }

        BleController.getInstance().startScan(scanListener);
    }

    @Keep
    @Override
    public void stopScanPanel() {
        BleController.getInstance().stopScan();
    }

    @Keep
    @Override
    public boolean isScanningPanel() {
        return BleController.getInstance().isScanning();
    }

    @Keep
    @Override
    public void connect(BleDevice device, IPanelConnectListener connectListener) {
        BleController.getInstance().startConnect(device, connectListener);
    }

    @Keep
    @Override
    public void destroyAdder() {
        BleController.getInstance().destroy();
    }

    @Keep
    @Override
    public void disconnectAllBle() {
        BleController.getInstance().disconnectAllBle();
    }

    @Keep
    @Override
    public void addPanelCmdResultListener(IPanelCmdCallback callback) {
        BleController.getInstance().addPanelCmdResultListener(callback);
    }

    @Keep
    @Override
    public void removePanelCmdResultListener(IPanelCmdCallback callback) {
        BleController.getInstance().removePanelCmdResultListener(callback);
    }

    @Keep
    public static Boolean isNewDevice(BleDevice bleDevice) {
        int newStatus = getDataByIndex(bleDevice, 0);
        return newStatus == 1;
    }

    @Keep
    public static boolean isDeviceOnline(BleDevice bleDevice) {
        int onlineStatus = getDataByIndex(bleDevice, 1);
        return onlineStatus == 1;
    }

    /**
     * 是否是可以设置家庭的主机
     *
     * @param bleDevice
     * @return
     */
    @Keep
    public static boolean isDeviceWithFamily(BleDevice bleDevice) {
        int onlineStatus = getDataByIndex(bleDevice, 2);
        return onlineStatus == 1;
    }

    /**
     * 通过ScanRecord,获取到他的ServiceData，获取到前两位
     * 第一个 1代表新的未被添加过或已被Reset的主机  0代表旧的已被添加或配网的主机
     * 第二位 1代表在线 0代表离线
     * 第三位 1代表支持家庭的主机 0代表不支持家庭的主机
     * 第四位 1代表4g主机 0代表2G主机或者是旧主机，不支持GetSIM指令
     */
    private static int getDataByIndex(final BleDevice bleDevice, final int index) {
        int result = -1;
        try {
            ScanRecord scanRecord = parseScanRecordFromBytes(bleDevice.getScanRecord());
            if (scanRecord.getServiceData().size() <= 0) {
                return result;
            }
            byte[] data = scanRecord.getServiceData().entrySet().iterator().next().getValue();
            char[] dataChar = String.format("%08d", Integer.valueOf(new BigInteger(1, data).toString(2))).toCharArray();
            DDLog.d(TAG, "service data is" + Arrays.toString(dataChar));
            result = Character.getNumericValue(dataChar[index]);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on get panel data by index: " + index);
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 是否是可以设置家庭的主机
     *
     * @param bleDevice
     * @return
     */
    @Keep
    public static boolean isDeviceSupport4G(BleDevice bleDevice) {
        int support4G = getDataByIndex(bleDevice, 3);
        return support4G == 1;
    }

    @Keep
    public static ScanRecord parseScanRecordFromBytes(byte[] bytes) {
        try {
            Method parseFromBytes = ScanRecord.class.getMethod("parseFromBytes", byte[].class);
            return (ScanRecord) parseFromBytes.invoke(null, (Object) bytes);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Keep
    @Override
    public BleDevice getConnectedDevice() {
        return BleController.getInstance().getConnectedDevice();
    }

    @Keep
    @Override
    public void bindDevice(Plugin plugin) {
    }

    @Keep
    @Override
    public void getWifiList() {
        mPanelAdderOperator.getWifiList();
    }

    @Keep
    @Override
    public void setDeviceName(String deviceName) {
        mPanelAdderOperator.setDeviceName(deviceName);
    }

    @Keep
    @Override
    public void setDevicePassword(String devicePassword) {
        mPanelAdderOperator.setDevicePassword(devicePassword);
    }

    @Keep
    @Override
    public void verifyDevicePassword(String deviceName) {
        mPanelAdderOperator.verifyDevicePassword(deviceName);
    }

    @Keep
    @Override
    public void setWifiName(String wifiName) {
        mPanelAdderOperator.setWifiName(wifiName);
    }

    @Keep
    @Override
    public void setWifiPassword(String wifiPassword) {
        mPanelAdderOperator.setWifiPassword(wifiPassword);
    }

    @Keep
    @Override
    public void setWifi() {
        mPanelAdderOperator.setWifi();
    }

    @Keep
    @Override
    public void setWifi(String wifiName, String wifiPassword) {
        mPanelAdderOperator.setWifi(wifiName, wifiPassword);
    }

    @Keep
    @Override
    public void setDHCP() {
        mPanelAdderOperator.setDHCP();
    }

    @Keep
    @Override
    public void setIP(String ip) {
        mPanelAdderOperator.setIP(ip);
    }

    @Keep
    @Override
    public void setNetmask(String netmask) {
        mPanelAdderOperator.setNetmask(netmask);
    }

    @Keep
    @Override
    public void setGateway(String gateway) {
        mPanelAdderOperator.setGateway(gateway);
    }

    @Keep
    @Override
    public void setDNS(String dns) {
        mPanelAdderOperator.setDNS(dns);
    }

    @Keep
    @Override
    public void setIP() {
        mPanelAdderOperator.setIP();
    }

    @Keep
    @Override
    public void setIP(String ip, String netmask, String gateway, String dns) {
        mPanelAdderOperator.setIP(ip, netmask, gateway, dns);
    }

    @Keep
    @Override
    public void bindDevice() {
        mPanelAdderOperator.bindDevice();
    }

    @Keep
    @Override
    public void stopBle() {
        mPanelAdderOperator.stopBle();
    }

    @Keep
    @Override
    public void setStaticIP(String ip, String netmask, String gateway, String dns) {
        mPanelAdderOperator.setStaticIP(ip, netmask, gateway, dns);
    }

    @Keep
    @Override
    public void setStaticIP() {
        mPanelAdderOperator.setStaticIP();
    }

    @Override
    public void getSim() {
        mPanelAdderOperator.getSim();
    }

    @Override
    public void set4GInfo() {
        mPanelAdderOperator.set4GInfo();
    }

    @Override
    public void set4GInfo(String nodeName, String username, String password) {
        mPanelAdderOperator.set4GInfo(nodeName, username, password);
    }

    @Override
    public void set4G() {
        mPanelAdderOperator.set4G();
    }

    @Override
    public void get4G() {
        mPanelAdderOperator.get4G();
    }

    @Override
    public void runZT() {
        mPanelAdderOperator.runZT();
    }
}
