package com.dinsafer.panel.add;

import android.os.Build;
import android.os.Handler;
import android.text.TextUtils;

import com.dinsafer.panel.add.plugin.BleController;
import com.dinsafer.panel.common.IPanelAdderOperator;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 添加主机-通过蓝牙向主机发送指令
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/11 3:27 PM
 */
public class PanelAdderOperator implements IPanelAdderOperator {
    private volatile String mUid;
    private volatile String mUserId;
    private volatile String mFamilyId;

    public void init(String uid, String userId, String familyId) {
        mUid = uid;
        this.mUserId = userId;
        mFamilyId = familyId;
    }

    public void isNewDevice(String uid) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", PanelAdderConstants.CMD.BLE_CMD_IS_NEW_DEVICE);
            jsonObject.put("uid", uid);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        BleController.getInstance().write(jsonObject);
    }

    public void isNewDeviceWithoutUid() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", PanelAdderConstants.CMD.BLE_CMD_IS_NEW_DEVICE);
            jsonObject.put("uid", "");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        BleController.getInstance().write(jsonObject);
    }


    @Override
    public void getWifiList() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_CMD, PanelAdderConstants.CMD.BLE_CMD_GET_WIFI_LIST);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        BleController.getInstance().write(jsonObject);
    }

    @Override
    public void setDeviceName(String deviceName) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_CMD, PanelAdderConstants.CMD.BLE_CMD_SET_DEVICE_NAME);
            jsonObject.put("name", deviceName);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        BleController.getInstance().write(jsonObject);
    }

    @Override
    public void setDevicePassword(String devicePassword) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_CMD, PanelAdderConstants.CMD.BLE_CMD_SET_DEVICE_PASSWORD);
            jsonObject.put("password", devicePassword);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        BleController.getInstance().write(jsonObject);
    }

    @Override
    public void verifyDevicePassword(String deviceName) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_CMD, PanelAdderConstants.CMD.BLE_CMD_VERIFY_DEVICE_PASSWORD);
            jsonObject.put("password", deviceName);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        BleController.getInstance().write(jsonObject);
    }

    @Override
    public void setWifiName(String wifiName) {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_CMD, PanelAdderConstants.CMD.BLE_CMD_SET_WIFI_NAME);
                    jsonObject.put("name", wifiName);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                BleController.getInstance().write(jsonObject);

            }
        }, 100);

    }

    @Override
    public void setWifiPassword(String wifiPassword) {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_CMD, PanelAdderConstants.CMD.BLE_CMD_SET_WIFI_PASSWORD);
                    jsonObject.put("password", wifiPassword);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                BleController.getInstance().write(jsonObject);
            }
        }, 200);
    }

    @Override
    public void setWifi() {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_CMD, PanelAdderConstants.CMD.BLE_CMD_SET_WIFI);
                    if (TextUtils.isEmpty(mFamilyId)) {
                        jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_UID, TextUtils.isEmpty(mUid) ? "" : mUid);
                    } else {
                        jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_USER_ID, TextUtils.isEmpty(mUserId) ? "" : mUserId);
                        jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_HOME_ID, TextUtils.isEmpty(mFamilyId) ? "" : mFamilyId);
                    }
                    jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_SYSTEM, getSystemInfo());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                BleController.getInstance().write(jsonObject);
            }
        }, 300);
    }

    @Override
    public void setWifi(String wifiName, String wifiPassword) {
        setWifiName(wifiName);
        setWifiPassword(wifiPassword);
        setWifi();
    }

    @Override
    public void setDHCP() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_CMD, PanelAdderConstants.CMD.BLE_CMD_SET_DHCP);
            if (TextUtils.isEmpty(mFamilyId)) {
                jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_UID, TextUtils.isEmpty(mUid) ? "" : mUid);
            } else {
                jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_USER_ID, TextUtils.isEmpty(mUserId) ? "" : mUserId);
                jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_HOME_ID, TextUtils.isEmpty(mFamilyId) ? "" : mFamilyId);
            }
            jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_SYSTEM, getSystemInfo());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        BleController.getInstance().write(jsonObject);
    }

    @Override
    public void setIP(String ip) {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_CMD, PanelAdderConstants.CMD.BLE_CMD_SET_IP);
                    jsonObject.put("ip", ip);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                BleController.getInstance().write(jsonObject);
            }
        }, 100);
    }

    @Override
    public void setNetmask(String netmask) {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_CMD, PanelAdderConstants.CMD.BLE_CMD_SET_NETMASK);
                    jsonObject.put("netmask", netmask);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                BleController.getInstance().write(jsonObject);

            }
        }, 200);
    }

    @Override
    public void setGateway(String gateway) {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_CMD, PanelAdderConstants.CMD.BLE_CMD_SET_GATEWAY);
                    jsonObject.put("gateway", gateway);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                BleController.getInstance().write(jsonObject);

            }
        }, 300);
    }

    @Override
    public void setDNS(String dns) {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_CMD, PanelAdderConstants.CMD.BLE_CMD_SET_DNS);
                    jsonObject.put("dns", dns);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                BleController.getInstance().write(jsonObject);

            }
        }, 400);
    }

    @Override
    public void setIP() {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_CMD, PanelAdderConstants.CMD.BLE_CMD_SET_STATIC_IP);
                    if (TextUtils.isEmpty(mFamilyId)) {
                        jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_UID, TextUtils.isEmpty(mUid) ? "" : mUid);
                    } else {
                        jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_USER_ID, TextUtils.isEmpty(mUserId) ? "" : mUserId);
                        jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_HOME_ID, TextUtils.isEmpty(mFamilyId) ? "" : mFamilyId);
                    }
                    jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_SYSTEM, getSystemInfo());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                BleController.getInstance().write(jsonObject);

            }
        }, 500);
    }

    @Override
    public void setIP(String ip, String netmask, String gateway, String dns) {
        setIP(ip);
        setNetmask(netmask);
        setGateway(gateway);
        setDNS(dns);
        setIP();
    }

    @Override
    public void bindDevice() {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_CMD, PanelAdderConstants.CMD.BLE_CMD_BIND_DEVICE);
                    jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_UID, TextUtils.isEmpty(mUid) ? "" : mUid);
                    if (TextUtils.isEmpty(mFamilyId)) {
                        jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_UID, TextUtils.isEmpty(mUid) ? "" : mUid);
                    } else {
                        jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_USER_ID, TextUtils.isEmpty(mUserId) ? "" : mUserId);
                        jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_HOME_ID, TextUtils.isEmpty(mFamilyId) ? "" : mFamilyId);
                    }
                    jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_SYSTEM, getSystemInfo());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                BleController.getInstance().write(jsonObject);

            }
        }, 500);
    }

    @Override
    public void stopBle() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_CMD, PanelAdderConstants.CMD.BLE_CMD_STOP_BLE);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        BleController.getInstance().write(jsonObject);
    }

    @Override
    public void setStaticIP(String ip, String netmask, String gateway, String dns) {
        setIP(ip);
        setNetmask(netmask);
        setGateway(gateway);
        setDNS(dns);
        setStaticIP();
    }

    @Override
    public void setStaticIP() {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {

                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_CMD, PanelAdderConstants.CMD.BLE_CMD_SET_STATIC_IP);
                    if (TextUtils.isEmpty(mFamilyId)) {
                        jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_UID, TextUtils.isEmpty(mUid) ? "" : mUid);
                    } else {
                        jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_USER_ID, TextUtils.isEmpty(mUserId) ? "" : mUserId);
                        jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_HOME_ID, TextUtils.isEmpty(mFamilyId) ? "" : mFamilyId);
                    }
                    jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_SYSTEM, getSystemInfo());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                BleController.getInstance().write(jsonObject);

            }
        }, 500);
    }

    public String getSystemInfo() {
        return "android," + Build.VERSION.SDK_INT;
    }

    @Override
    public void getSim() {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_CMD, PanelAdderConstants.CMD.BLE_CMD_GET_SIM);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                BleController.getInstance().write(jsonObject);
            }
        }, 500);
    }

    @Override
    public void set4GInfo() {
        set4GInfo(true, null, null, null);
    }

    @Override
    public void set4GInfo(final String nodeName, final String username, final String password) {
        set4GInfo(false, nodeName, username, password);
    }

    @Override
    public void set4G() {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_CMD, PanelAdderConstants.CMD.BLE_CMD_SET_4G);
                    if (TextUtils.isEmpty(mFamilyId)) {
                        jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_UID, TextUtils.isEmpty(mUid) ? "" : mUid);
                    } else {
                        jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_USER_ID, TextUtils.isEmpty(mUserId) ? "" : mUserId);
                        jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_HOME_ID, TextUtils.isEmpty(mFamilyId) ? "" : mFamilyId);
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                BleController.getInstance().write(jsonObject);

            }
        }, 500);
    }

    /**
     * @param automatic 对应automatic "1":true, "0":false 由于之前定义的都是字符串类型
     *                  <p>
     *                  当a==false时，需要传以下字段
     * @param nodeName  对应node_name
     * @param username  对应user_name
     * @param password  对应password
     */
    private void set4GInfo(final boolean automatic, final String nodeName, final String username, final String password) {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_CMD, PanelAdderConstants.CMD.BLE_CMD_SET_4G_INFO);
                    if (automatic) {
                        jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_4G_A, "1");
                    } else {
                        jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_4G_A, "0");
                        jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_4G_N, nodeName);
                        jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_4G_U, username);
                        jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_4G_P, password);
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                BleController.getInstance().write(jsonObject);
            }
        }, 500);
    }

    /**
     * 获取是否4G主机
     * 返回结果结果
     * "status":1,	  // int 1:正常，-1:该主机版本不支持这个指令
     * "result": "0" // string ,0:2G, 1:4G, 2:未知
     */
    @Override
    public void get4G() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_CMD, PanelAdderConstants.CMD.BLE_CMD_GET_4G);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        BleController.getInstance().write(jsonObject);
    }

    @Override
    public void runZT() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(PanelAdderConstants.KEY.BLE_KEY_CMD, PanelAdderConstants.CMD.BLE_CMD_RUN_ZT);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        BleController.getInstance().write(jsonObject);
    }
}
