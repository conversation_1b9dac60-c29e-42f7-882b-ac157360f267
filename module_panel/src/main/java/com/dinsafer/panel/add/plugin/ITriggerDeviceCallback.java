package com.dinsafer.panel.add.plugin;

import androidx.annotation.Keep;

import okhttp3.Response;

/**
 * 触发配对结果回调
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/6/16 17:04
 */
@Keep
public interface ITriggerDeviceCallback {
    void onWsOpen(Response response);

    void onWsFailure(Throwable t, Response response);

    void onWsClosing(int code, String reason);

    void onWsMessage(String msg);

    void onError(Throwable throwable);
}
