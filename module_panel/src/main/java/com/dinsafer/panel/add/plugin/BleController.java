package com.dinsafer.panel.add.plugin;

import android.annotation.TargetApi;
import android.app.Application;
import android.bluetooth.BluetoothGatt;
import android.os.Build;
import android.os.Handler;
import android.text.TextUtils;

import com.clj.fastble.BleManager;
import com.clj.fastble.callback.BleGattCallback;
import com.clj.fastble.callback.BleMtuChangedCallback;
import com.clj.fastble.callback.BleNotifyCallback;
import com.clj.fastble.callback.BleScanCallback;
import com.clj.fastble.callback.BleWriteCallback;
import com.clj.fastble.data.BleDevice;
import com.clj.fastble.exception.BleException;
import com.clj.fastble.scan.BleScanRuleConfig;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.dssupport.utils.HexUtil;
import com.dinsafer.panel.add.PanelAdderConstants;
import com.dinsafer.panel.add.bean.PanelCmdResult;
import com.dinsafer.panel.add.callback.IPanelCmdCallback;
import com.dinsafer.panel.add.callback.IPanelConnectListener;
import com.dinsafer.panel.add.callback.IPanelScanListener;
import com.dinsafer.panel.util.PanelSecretUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import androidx.annotation.NonNull;

/**
 * Created by LT on 2019/2/22.
 */
public class BleController {
    public static String TAG = "BleController";

    private BleController() {
    }

    private static class Holder {
        static BleController instance = new BleController();
    }

    public static BleController getInstance() {
        return Holder.instance;
    }

    private final List<IPanelCmdCallback> mPanelCmdCallbackList = new ArrayList<>();
    private volatile boolean isScanning;
    private String[] mServiceUuids;
    private String mWriteUuid, mNotifyUuid;

    /**
     * 连接失败标记
     * <p>
     * 重置主机时一般需要连接两次才能连接成功。
     * 所以在第一次连接失败时，需要进行连接失败的标记而不是弹窗提示连接失败，
     * 只有第二次连接失败才提示连接失败信息。
     * </p>
     */
    private boolean isFail;

    /**
     * 初始化
     */
    public void initBle(Application application) {
        BleManager.getInstance()
                .enableLog(false)
                .setReConnectCount(2, 5000)
                .setConnectOverTime(PanelAdderConstants.BLE_CONNECT_TIMEOUT)
                .setOperateTimeout(PanelAdderConstants.BLE_OPERATE_TIMEOUT);
    }

    /**
     * 设置扫描规则
     *
     * @param bleScanTime  扫描时间
     * @param serviceUuids 需要扫描的uuid
     * @param writeUuid    蓝牙写uuid
     * @param notifyUuid   蓝牙通知uuid
     */
    public void setScanRuleWithUUID(long bleScanTime, String[] serviceUuids, String writeUuid, String notifyUuid) {
        DDLog.i(TAG, "setScanRuleWithUUID, bleScanTime: " + bleScanTime);
        this.mServiceUuids = serviceUuids;
        this.mWriteUuid = writeUuid;
        this.mNotifyUuid = notifyUuid;

        if (bleScanTime <= 0) {
            bleScanTime = PanelAdderConstants.BLE_SCAN_TIMEOUT;
        }
        UUID[] uuids = new UUID[serviceUuids.length];
        for (int i = 0; i < serviceUuids.length; i++) {
            uuids[i] = UUID.fromString(mServiceUuids[i]);
        }

        BleScanRuleConfig scanRuleConfig = new BleScanRuleConfig.Builder()
                .setServiceUuids(uuids)
                .setScanTimeOut(bleScanTime)
                .build();
        BleManager.getInstance().initScanRule(scanRuleConfig);
    }

    /**
     * 蓝牙是否已打开
     *
     * @return true: 蓝牙已打开
     */
    public boolean isOpenBlue() {
        return BleManager.getInstance().isBlueEnable();
    }

    /**
     * 蓝牙是否可用
     *
     * @return true: 是否有蓝牙功能
     */
    public boolean isSupportBlue() {
        return BleManager.getInstance().isSupportBle();
    }

    /**
     * 开始扫描
     */
    public void startScan(final IPanelScanListener scanListener) {
        DDLog.i(TAG, "startScan");
        checkInitScanRule();
        BleManager.getInstance().scan(new BleScanCallback() {
            @Override
            public void onScanFinished(List<BleDevice> scanResultList) {
                isScanning = false;
                if (null != scanListener) {
                    scanListener.onScanFinished(scanResultList);
                }
            }

            @Override
            public void onScanStarted(boolean success) {
                if (null != scanListener) {
                    scanListener.onScanStarted(success);
                }
            }

            @Override
            public void onScanning(BleDevice bleDevice) {
                if (null != scanListener) {
                    scanListener.onScanning(bleDevice);
                }
            }
        });
        isScanning = true;
    }

    /**
     * 停止扫描
     */
    public void stopScan() {
        DDLog.i(TAG, "stopScan");
        try {
            if (isScanning) {
                BleManager.getInstance().cancelScan();
                isScanning = false;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isScanning() {
        return isScanning;
    }

    /**
     * 开始连接设备
     * 在初始化的时候设置了重连次数为2，
     * 所以第二次失败才是真正的失败
     *
     * @param bleDevice 蓝牙设备
     */
    public void startConnect(BleDevice bleDevice, @NonNull final IPanelConnectListener connectListener) {
        DDLog.i(TAG, "startConnect");
        if (null == bleDevice) {
            DDLog.e(TAG, "BLEDevice is null.");
            return;
        }
        BleManager.getInstance().connect(bleDevice, new BleGattCallback() {
            @Override
            public void onStartConnect() {
                DDLog.d(TAG, "开始连接");
                connectListener.onStartConnect();
            }

            @Override
            public void onConnectFail(BleDevice bleDevice, BleException exception) {
                DDLog.d(TAG, "连接失败 " + exception.toString());
//                 连接失败
                if (isFail) {
                    // 第二次连接失败，提示连接失败对话框
                    DDLog.e(TAG, "正真连接失败了！！！！！！！");
                    connectListener.onConnectFail(bleDevice, exception);
                    return;
                }
                // 第一次连接失败，标记连接失败
                isFail = true;
            }

            @Override
            public void onConnectSuccess(BleDevice bleDevice, BluetoothGatt gatt, int status) {
                DDLog.d(TAG, "连接成功");
                connectListener.onConnectSuccess(bleDevice, gatt, status);
                // 连接成功，BleDevice即为所连接的BLE设备
                // 连接成功后，清除连接失败的标记
                setMtu(bleDevice);

                new Handler().postDelayed(() -> openNotify(bleDevice, connectListener), 500);
            }

            @Override
            public void onDisConnected(boolean isActiveDisConnected, BleDevice bleDevice,
                                       BluetoothGatt gatt, int status) {
                DDLog.d(TAG, "连接中断,是否为主动端口 " + isActiveDisConnected + " ,status is " + status);
                /**
                 * 如果是主动断开，即连接完成的时候等。就不必弹出主机蓝牙断开窗口
                 * 如果是手机蓝牙没开的情况也是
                 */
                if (isActiveDisConnected) {
                    return;
                }
                if (!BleController.getInstance().isOpenBlue()) {
                    return;
                }

                connectListener.onDisConnected(bleDevice,
                        isActiveDisConnected, gatt, status);
                BleManager.getInstance().disconnect(bleDevice);
            }
        });
    }

    /**
     * 设置MTU
     * 在连接成功之后调用
     */
    public void setMtu(BleDevice bleDevice) {
        DDLog.i(TAG, "setMtu");

        BleManager.getInstance().setMtu(bleDevice, 512, new BleMtuChangedCallback() {
            @Override
            public void onSetMTUFailure(BleException exception) {
                DDLog.e(TAG, "设置MTU失败:" + exception.toString());
            }

            @Override
            public void onMtuChanged(int mtu) {
                DDLog.d(TAG, "设置MTU成功，并获得当前设备传输支持的MTU值:" + mtu);
            }
        });
    }

    /**
     * 打开通知，准备接收从蓝牙返回的数据
     */
    public void openNotify(BleDevice bleDevice, @NonNull final IPanelConnectListener connectListener) {
        DDLog.i(TAG, "openNotify");
        BleManager.getInstance().notify(bleDevice, mServiceUuids[0], mNotifyUuid,
                new BleNotifyCallback() {
                    @Override
                    public void onNotifySuccess() {
                        // 打开通知操作成功
                        DDLog.d(TAG, "打开通知操作成功");
                        connectListener.onNotifySuccess();
                        isFail = false;
                    }

                    @Override
                    public void onNotifyFailure(BleException exception) {
                        // 打开通知操作失败
                        DDLog.d(TAG, "打开通知操作失败, " + exception);
                        if (isFail) {
                            // 第二次连接失败，提示连接失败对话框
                            DDLog.e(TAG, "正真打开连接失败了！！！！！！！");
                            connectListener.onNotifyFailure(bleDevice, exception);
                            return;
                        }
                        // 第一次连接失败，标记连接失败
                        isFail = true;
                    }

                    @Override
                    public void onCharacteristicChanged(byte[] data) {
                        // 打开通知后，设备发过来的数据将在这里出现
                        String tmp = new String(data);
                        DDLog.d(TAG, "打开通知后，设备发过来的数据将在这里出现:" + tmp);
                        if (tmp.equals("ack") || tmp.equals("NON")) {
                            return;
                        }

                        String handleData = PanelAdderConstants.IS_BLE_DATA_ENCRYPT ? PanelSecretUtil.getReverSCWithOutSnappy(tmp) : tmp;
                        if (TextUtils.isEmpty(handleData)) {
                            return;
                        }

                        handleNotifyData(bleDevice, handleData);
                    }
                });
    }

    public void stopNotify() {
        try {
            BleManager.getInstance().stopNotify(getConnectedDevice(), mServiceUuids[0], mNotifyUuid);
        } catch (Exception ignore) {
        }
    }

    private void handleNotifyData(BleDevice bleDevice, String data) {
        DDLog.d(TAG, "handleNotifyData, data: " + data);
        if (TextUtils.isEmpty(data) || mPanelCmdCallbackList.size() <= 0) {
            return;
        }

        synchronized (mPanelCmdCallbackList) {
            JSONObject result = null;
            try {
                result = new JSONObject(data);
            } catch (JSONException e) {
                DDLog.e(TAG, "Error on parse notify result.");
                e.printStackTrace();
            }

            if (null == result) {
                return;
            }

            PanelCmdResult cmdResult = new PanelCmdResult(
                    DDJSONUtil.getString(result, PanelAdderConstants.KEY.BLE_KEY_CMD),
                    DDJSONUtil.getString(result, PanelAdderConstants.KEY.BLE_KEY_RESULT),
                    DDJSONUtil.getInt(result, PanelAdderConstants.KEY.BLE_KEY_STATUS));
            if (DDJSONUtil.has(result, PanelAdderConstants.KEY.BLE_KEY_RSSI)) {
                cmdResult.setRssi(DDJSONUtil.getInt(result, PanelAdderConstants.KEY.BLE_KEY_RSSI));
                cmdResult.setAuth(DDJSONUtil.getBoolean(result, PanelAdderConstants.KEY.BLE_KEY_AUTH));
            }

            for (IPanelCmdCallback panelCmdCallback : mPanelCmdCallbackList) {
                panelCmdCallback.onPanelResult(cmdResult);
            }
        }
    }

    public void write(JSONObject jsonObject) {
        DDLog.d(TAG, "write");
        checkInitScanRule();
        BleDevice bleDevice = getConnectedDevice();
        if (null == bleDevice) {
            DDLog.e(TAG, "招不到需要操作的设备");
            return;
        }

        String data = PanelAdderConstants.IS_BLE_DATA_ENCRYPT ? PanelSecretUtil.getSC(jsonObject.toString()) : jsonObject.toString();
        byte[] bytes = HexUtil.hexStringToBytes(str2HexStr(data));

        DDLog.d(TAG, "发送的数据：" + jsonObject.toString());
        DDLog.d(TAG, "加密的数据：" + data);

        BleManager.getInstance().write(bleDevice, mServiceUuids[0], mWriteUuid, bytes, false,
                new BleWriteCallback() {
                    @Override
                    public void onWriteSuccess(int current, int total, byte[] justWrite) {
                        DDLog.d(TAG, "写入成功");
                    }

                    @Override
                    public void onWriteFailure(final BleException exception) {
                        DDLog.d(TAG, "写入失败:" + exception.toString());
                    }
                });
    }


    /**
     * 当前是否有已连接的设备
     *
     * @return true:有连接的设备
     */
    public boolean isHasDeviceConnect() {
        return BleManager.getInstance().getAllConnectedDevice().size() > 0;
    }

    /**
     * 断开连接
     */
    public void disconnectAllBle() {
        DDLog.d(TAG, "disconnectAllBle");
        stopNotify();
        BleManager.getInstance().disconnectAllDevice();
        deleteAllCache();
    }

    public void deleteAllCache() {
        DDLog.d(TAG, "deleteAllCache");
        if (null != BleManager.getInstance().getAllConnectedDevice()
                && BleManager.getInstance().getAllConnectedDevice().size() > 1) {
            DDLog.d(TAG, "BleManager.getInstance().getAllConnectedDevice().size() > 1");
            for (BleDevice bleDevice : BleManager.getInstance().getAllConnectedDevice()) {
                refreshGattCache(BleManager.getInstance().getBluetoothGatt(bleDevice));
            }
        }

        mPanelCmdCallbackList.clear();
    }

    @TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR2)
    public boolean refreshGattCache(BluetoothGatt gatt) {
        boolean result = false;
        try {
            if (gatt != null) {
                Method refresh = BluetoothGatt.class.getMethod("refresh");
                if (refresh != null) {
                    refresh.setAccessible(true);
                    result = (boolean) refresh.invoke(gatt, new Object[0]);
                }
            }
        } catch (Exception e) {
            DDLog.e(TAG, "refreshGattCache, Error!!!!!!!");
            e.printStackTrace();
        }

        return result;
    }

    public void destroy() {
        deleteAllCache();
        BleManager.getInstance().destroy();
    }

    /**
     * 字符串转换成为16进制(无需Unicode编码)
     *
     * @param str
     * @return
     */
    private String str2HexStr(String str) {
        char[] chars = "0123456789ABCDEF".toCharArray();
        StringBuilder sb = new StringBuilder("");
        byte[] bs = str.getBytes();
        int bit;
        for (byte b : bs) {
            bit = (b & 0x0f0) >> 4;
            sb.append(chars[bit]);
            bit = b & 0x0f;
            sb.append(chars[bit]);
            // sb.append(' ');
        }
        return sb.toString().trim();
    }

    public void addPanelCmdResultListener(IPanelCmdCallback callback) {
        DDLog.i(TAG, "addPanelCmdResultListener");
        synchronized (mPanelCmdCallbackList) {
            if (null == callback
                    || mPanelCmdCallbackList.contains(callback)) {
                return;
            }

            mPanelCmdCallbackList.add(callback);
        }
    }

    public void removePanelCmdResultListener(IPanelCmdCallback callback) {
        DDLog.i(TAG, "removePanelCmdResultListener");
        synchronized (mPanelCmdCallbackList) {
            if (null == callback || !mPanelCmdCallbackList.contains(callback)) {
                return;
            }

            mPanelCmdCallbackList.remove(callback);
        }
    }

    public BleDevice getConnectedDevice() {
        if (BleManager.getInstance().getAllConnectedDevice().size() <= 0) {
            return null;
        }
        return BleManager.getInstance().getAllConnectedDevice().get(0);
    }

    private void checkInitScanRule() {
        if (null == mServiceUuids || mServiceUuids.length <= 0 || TextUtils.isEmpty(mWriteUuid) || TextUtils.isEmpty(mNotifyUuid)) {
            throw new NullPointerException("Bluetooth's uuid is null, you must call method setScanRuleWithUUID first.");
        }
    }
}
