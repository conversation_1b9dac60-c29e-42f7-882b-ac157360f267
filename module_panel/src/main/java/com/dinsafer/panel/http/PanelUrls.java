package com.dinsafer.panel.http;

/**
 * 主机相关网络请求路径
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/12 3:05 PM
 */
class PanelUrls {
    static final String URL_DEVICE_INFO = "/device/get-device-info/v2/";
    static final String URL_PLUGIN_QUANTITY_INFO = "/device/count-plugin/";
    static final String URL_HOME_PLUGIN_INFO = "/device/get-plugin-info/";
    static final String URL_LIST_SMART_BUTTON = "/device/list-smart-button/";
    static final String URL_LIST_SPECIFY_PLUGIN = "/device/listspecifypluginsetting/v4/";
    static final String URL_LIST_RELAY = "/device/listrelaysetting/";
    static final String URL_LIST_ACCESSORY_V2 = "/device/listaccessorysetting/v2/";
    static final String URL_LIST_DOOR_SENSOR = "/device/listdoorsensorsetting/v2/";
    static final String URL_LIST_CUSTOMIZE_HOME_ARM = "/device/listhomearmsetting/v4/";
    static final String URL_LIST_ENTRY_DELAY = "/device/listentrydelaysetting/v4/";
    static final String URL_GET_DEVICE_TEXT = "/device/getdevicetext/";
    static final String URL_GET_INTIMIDATION_ALARM = "/device/getintimidationalarmsetting/";
    static final String URL_GET_CID_DATA = "/device/getciddata/";
    static final String URL_GET_CMS_DATA = "/device/getcmsdata/";
    static final String URL_GET_READY_TO_ARM_STATE = "/device/getrdasetting/";
    static final String URL_GET_EXIT_DELAY_TIME = "/device/exitdelaytime/";
    static final String URL_GET_SIREN_TIME = "/device/sirentime/";
    static final String URL_GET_ADVANCED_SETTING = "/device/get-advanced-settings/";
    static final String URL_GET_PLAY_SOUND_ARM_DISARM = "/device/getplaysoundsetting/";
    static final String URL_GET_TIMEZONE = "/device/gettimezonesetting/";
    static final String URL_GET_4G_SETTING = "/device/getsim4gdata/";
    static final String URL_GET_SIM_DATA = "/device/getsimdata/";
    static final String URL_RENAME_DEVICE = "/device/modifydevicename/";
    static final String URL_GET_EVENT_LIST = "/device/list-eventlists/";
    static final String URL_GET_EVENT_LIST_SETTING = "/device/get-eventlist-setting/";
    static final String URL_GET_SOS_STATE = "/device/getsosstate/";
    static final String URL_LIST_SPECIFY_PLUGIN_V3 = "/device/listspecifypluginsetting/v3/";
    static final String URL_LIST_DOORBELL_DATA = "/device/listdoorbelldata/";
    static final String URL_MODIFY_DOORBELL = "/device/modifydoorbell/";
    static final String URL_ADD_DOORBELL = "/device/adddoorbell/";
    static final String URL_DELETE_DOORBELL = "/device/deletedoorbell/";
    static final String URL_LIST_DOORBELL_CAPTURE = "/device/listdoorbellcapture/";
    static final String URL_DELETE_DOORBELL_CAPTURE = "/device/deletedoorbellcapture/";
    static final String URL_GET_NEW_ASK_DATA = "/pluginqrcode/getnewaskdata/";
    static final String URL_LIST_CUSTOMIZE_SMART_PLUGS = "/device/list-customize-smartplugs/";
    static final String URL_GET_SMART_BUTTON_CONF = "/device/get-smart-button-conf/";
    static final String URL_GET_CARE_MODE_DATA = "/device/get-care-mode-data/";
    static final String URL_GET_PLUGIN_DETAILS = "/device/get-plugin-details/";
    static final String URL_DELETE_DEVICE = "/device/delete-device/";
    static final String URL_GET_ARM_RULES = "/device/get-arm-rules/"; // 获取指定主机的布防规则
    static final String URL_GET_CARE_MODE_STATUS = "/device/get-care-mode-status/"; // 获取 care mode 当前状态
    static final String URL_GET_VOICE_PROMPT = "/device/get-vioce-prompt/"; // 获取指定主机的布撤防提示声设置
    static final String URL_LIST_ACCESSORIES = "/device/list-accessories/"; // 按时间获取配件的信息
    static final String URL_SEARCH_ACCESSORIES = "/device/search-accessories/"; // 查找指定配件的数据
    static final String URL_SEARCH_PANELS = "/device/search-devices/"; // 获取指定主机的信息
    static final String URL_LIST_PANEL_TOKENS = "/device/list-device-tokens/"; // 获取指定主机的信息
    static final String URL_SEND_CMD = "/device/sendcmd/"; // 临时屏蔽红外触发 BYPASS_PLUGIN_5_MIN
}
