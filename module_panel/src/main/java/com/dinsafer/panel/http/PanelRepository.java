package com.dinsafer.panel.http;

import android.text.TextUtils;

import com.dinsafer.dincore.activtor.AddPlugsBuilder;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.dincore.utils.RandomStringUtils;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.operate.bean.AppMessageEntry;
import com.dinsafer.panel.operate.bean.CategoryPlugsEntry;
import com.dinsafer.panel.operate.bean.CmsProtocolEntry;
import com.dinsafer.panel.operate.bean.CmsProtocolModel;
import com.dinsafer.panel.operate.bean.ContactIdResponseEntry;
import com.dinsafer.panel.operate.bean.DoorBell;
import com.dinsafer.panel.operate.bean.EntryDelayModel;
import com.dinsafer.panel.operate.bean.EventListEntry;
import com.dinsafer.panel.operate.bean.EventListSettingEntry;
import com.dinsafer.panel.operate.bean.FourGInfoEntry;
import com.dinsafer.panel.operate.bean.GetAdvancedSettingResult;
import com.dinsafer.panel.operate.bean.GetArmRulesResponse;
import com.dinsafer.panel.operate.bean.GetCareModeStatusResponse;
import com.dinsafer.panel.operate.bean.GetPlaySoundSettingResult;
import com.dinsafer.panel.operate.bean.GetVoicePromptResponse;
import com.dinsafer.panel.operate.bean.HomeArmStatueEntry;
import com.dinsafer.panel.operate.bean.HomeDeviceInfoEntry;
import com.dinsafer.panel.operate.bean.HomePluginQuantityEntry;
import com.dinsafer.panel.operate.bean.ListAccessoriesResponse;
import com.dinsafer.panel.operate.bean.ListPanelTokensResponse;
import com.dinsafer.panel.operate.bean.PirSensitivityEntry;
import com.dinsafer.panel.operate.bean.PirSensitivityResponse;
import com.dinsafer.panel.operate.bean.PirSettingEnabledStatueEntry;
import com.dinsafer.panel.operate.bean.ReadyToArmSwitchStatusEntry;
import com.dinsafer.panel.operate.bean.SearchAccessoriesResponse;
import com.dinsafer.panel.operate.bean.SearchPanelsResponse;
import com.dinsafer.panel.operate.bean.SimDataEntry;
import com.dinsafer.panel.operate.bean.SosMessageEntry;
import com.dinsafer.panel.operate.bean.SosStatusEntry;
import com.dinsafer.panel.operate.bean.TimePickerEntry;
import com.dinsafer.panel.operate.bean.TimeZoneResponseEntry;
import com.dinsafer.panel.operate.bean.param.AccessoriesInfoParams;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/10 11:54 AM
 */
public class PanelRepository {
    private static final String TAG = PanelRepository.class.getSimpleName();

    private Call<SearchPanelsResponse> mGetDeviceInfoCall;
    private Call<HomePluginQuantityEntry> mGetPluginQuantityCall;
    private Call<ResponseBody> mGetPluginInfoCall;


    /**
     * 获取主机的信息
     *
     * @param deviceId deviceId
     */
    public Response<HomeDeviceInfoEntry> getPanelInfoSync(final String homeId, String deviceId) throws IOException {
        DDLog.i(TAG, "getPanelInfo, deviceId: " + deviceId);
        return PanelApi.getPanelApi().getHomeDeviceInfo(homeId, deviceId).execute();
    }

    /**
     * 取消获取主机信息
     */
    public void cancelGetPanelInfo() {
        if (null != mGetDeviceInfoCall) {
            mGetDeviceInfoCall.cancel();
        }
    }

    /**
     * 获取配件数量的信息
     *
     * @param deviceId deviceId
     */
    public void getPluginQuantityEntry(String homeId, String deviceId, Callback<HomePluginQuantityEntry> callback) {
        DDLog.i(TAG, "getPanelInfo, deviceId: " + deviceId);
        mGetPluginQuantityCall = PanelApi.getPanelApi().getHomePluginQuantityInfo(homeId, deviceId);
        mGetPluginQuantityCall.enqueue(callback);
    }

    /**
     * 取消获取配件数量信息
     */
    public void cancelGetPluginQuantityEntry() {
        if (null != mGetPluginQuantityCall) {
            mGetPluginQuantityCall.cancel();
        }
    }

    /**
     * 获取首页配件的信息
     *
     * @param deviceId deviceId
     */
    public void getHomePluginInfo(String deviceId, Callback<ResponseBody> callback) {
        DDLog.i(TAG, "getPanelInfo, deviceId: " + deviceId);
        mGetPluginInfoCall = PanelApi.getPanelApi().getHomePluginInfo(deviceId);
        mGetPluginInfoCall.enqueue(callback);
    }

    /**
     * 获取首页配件的信息
     *
     * @param deviceId deviceId
     */
    public Response<ResponseBody> getHomePluginInfoSync(String deviceId) throws IOException {
        DDLog.i(TAG, "getPanelInfo, deviceId: " + deviceId);
        return PanelApi.getPanelApi().getHomePluginInfo(deviceId).execute();
    }

    /**
     * 取消获取首页配件信息
     */
    public void cancelGetHomePluginInfo() {
        if (null != mGetPluginInfoCall) {
            mGetPluginInfoCall.cancel();
        }
    }

    public void getHomePluginState(String panelToken, final ArrayList<String> targetTypeList) {
        if (TextUtils.isEmpty(panelToken)
                || null == UserManager.getInstance().getUser()) {
            DDLog.e(TAG, "Error on getHomePluginState because user of device is null");
            return;
        }

        PanelApi.getPanelApi().getPluginStatus(RandomStringUtils.getMessageId(),
                        panelToken,
                        UserManager.getInstance().getUser().getUid(), targetTypeList, 3)
                .enqueue(new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NotNull Call<StringResponseEntry> call,
                                           @NotNull Response<StringResponseEntry> response) {

                    }

                    @Override
                    public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                        DDLog.e(TAG, "Error on getHomePluginState");
                        t.printStackTrace();
                    }
                });
    }

    /**
     * 获取SmartButton列表数据
     *
     * @param panelId 主机ID
     */
    public void getSmartButtonList(String panelId, Callback<ResponseBody> callback) {
        PanelApi.getPanelApi().getSmartButtonListCall(panelId).enqueue(callback);
    }

    /**
     * 获取SmartButton列表数据
     *
     * @param panelId 主机ID
     */
    public Response<ResponseBody> getSmartButtonListSync(String panelId) throws IOException {
        return PanelApi.getPanelApi().getSmartButtonListCall(panelId).execute();
    }

    /**
     * 获取配件列表数据
     *
     * @param panelId  主机ID
     * @param category 配件类型
     */
    public void getSpecifyPluginList(String panelId, int category, Callback<ResponseBody> callback) {
        PanelApi.getPanelApi().getSpecifyPlugin(panelId, category).enqueue(callback);
    }

    /**
     * 获取配件列表数据
     *
     * @param panelId  主机ID
     * @param category 配件类型
     */
    public Response<ResponseBody> getSpecifyPluginListSync(String panelId, int category) throws IOException {
        return PanelApi.getPanelApi().getSpecifyPlugin(panelId, category).execute();
    }

    /**
     * 获取继电器列表数据
     *
     * @param panelId 主机ID
     */
    public void getRelayPluginList(String panelId, Callback<ResponseBody> callback) {
        PanelApi.getPanelApi().getRelayListCall(panelId).enqueue(callback);
    }

    /**
     * 获取继电器列表数据
     *
     * @param panelId 主机ID
     */
    public Response<ResponseBody> getRelayPluginListSync(String panelId) throws IOException {
        return PanelApi.getPanelApi().getRelayListCall(panelId).execute();
    }

    /**
     * 获取安防配件列表数据
     *
     * @param panelId 主机ID
     */
    public void getSecurityAccessoryList(String panelId, int category, Callback<ResponseBody> callback) {
        PanelApi.getPanelApi().getCategoryPlugsCallV4(panelId, category).enqueue(callback);
    }

    /**
     * 获取安防配件列表数据
     *
     * @param panelId 主机ID
     */
    public Response<ResponseBody> getSecurityAccessoryListSync(String panelId, int category) throws IOException {
        return PanelApi.getPanelApi().getCategoryPlugsCallV4(panelId, category).execute();
    }

    /**
     * 获取门窗探测器配件列表数据
     *
     * @param panelId 主机ID
     */
    public void getDoorSensorList(String panelId, Callback<ResponseBody> callback) {
        PanelApi.getPanelApi().getDoorSensorListData(panelId).enqueue(callback);
    }

    /**
     * 获取门窗探测器配件列表数据
     *
     * @param panelId 主机ID
     */
    public Response<ResponseBody> getDoorSensorListSync(String panelId) throws IOException {
        return PanelApi.getPanelApi().getDoorSensorListData(panelId).execute();
    }

    /**
     * 获取定制在家布防配件列表数据
     *
     * @param panelId 主机ID
     */
    public void getCustomizeHomeArmResult(String panelId, Callback<HomeArmStatueEntry> callback) {
        PanelApi.getPanelApi().getHomeArmStatusCall(panelId).enqueue(callback);
    }

    /**
     * 获取延时报警配件列表数据
     *
     * @param panelId 主机ID
     */
    public void getEntryDelayResult(String panelId, Callback<EntryDelayModel> callback) {
        PanelApi.getPanelApi().getEntryDelayCall(panelId).enqueue(callback);
    }

    /**
     * 提交定制在家布防配件列表数据
     */
    public void getConfirmCustomizeHomeArmResult(String uid, String deviceToken, String messageid,
                                                 String type, int time, String data, String thirdParty,
                                                 String askData, boolean entrydelaysound,
                                                 Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getChangeEntryDelayCall(uid, messageid, deviceToken, type, time, data,
                thirdParty, askData, entrydelaysound).enqueue(callback);
    }


    /**
     * 获取安全设置-消息设置-获取当前设置的通知语言
     *
     * @param panelId 主机ID
     */
    public void getPanelNotificationLanguage(String panelId, Callback<AppMessageEntry> callback) {
        PanelApi.getPanelApi().getDeviceTextCall(panelId).enqueue(callback);
    }

    /**
     * 安全设置-消息设置-设置推送语言
     *
     * @param messageId messageId
     * @param newLang   当前设置的推送语言
     */
    public void setPanelNotificationLanguage(String userId, String deviceToken, String messageId,
                                             String newLang, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getNewPushMessageCall(userId, deviceToken, messageId, newLang).enqueue(callback);
    }

    /**
     * 安全设置-消息设置-设置推送语言
     *
     * @param messageId messageId
     * @param newLang   当前设置的推送语言
     */
    public void setPanelNotificationLanguage(String userId, String deviceToken, String messageId, String deviceText,
                                             String newLang, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getSaveMessage(userId, deviceToken, messageId, deviceText, newLang).enqueue(callback);
    }

    /**
     * 安全设置-胁迫报警-获取当前胁迫报警开关状态
     *
     * @param panelId 主机ID
     */
    public void getPanelIntimidateSosState(String panelId, Callback<SosMessageEntry> callback) {
        PanelApi.getPanelApi().getSosMessageCall(panelId).enqueue(callback);
    }

    /**
     * 安全设置-胁迫报警设置-第一次打开取胁迫报警开关
     */
    public void openPanelIntimidateSosFirst(String uid, String devtoken, String messageid, String pushliterary,
                                            String password, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getFirstChangeSos(uid, messageid, devtoken, pushliterary, password).enqueue(callback);
    }

    /**
     * 安全设置-胁迫报警设置-修改胁迫报警开关转态（非第一次打开）
     */
    public void changePanelIntimidateSosState(String uid, String devtoken, String messageid,
                                              boolean enable, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getChangeSosEnable(uid, messageid, devtoken, enable).enqueue(callback);
    }

    /**
     * 安全设置-胁迫报警设置-修改胁迫报警密码
     */
    public void changePanelIntimidateSosPassword(String uid, String devtoken, String messageid,
                                                 String password, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getChangeSosPassword(uid, messageid, devtoken, password).enqueue(callback);
    }

    /**
     * 安全设置-胁迫报警设置-修改胁迫报警报警信息
     */
    public void changePanelIntimidateSosMessage(String uid, String devtoken, String messageid,
                                                String pushliterary, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getChangeSosMessage(uid, messageid, devtoken, pushliterary).enqueue(callback);
    }

    /**
     * 安全设置-设置CID
     */
    public void getContactId(String panelId, Callback<ContactIdResponseEntry> callback) {
        PanelApi.getPanelApi().getContactIdCall(panelId).enqueue(callback);
    }

    /**
     * 消息设置-修改CID设置
     */
    public void updateContactIdCall(String uid, String deviceToken, String messageid, boolean enable,
                                    String contactidcode, String countrycode, String phone, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getUpdateContactIdCall(uid, deviceToken, messageid, enable, contactidcode, countrycode, phone).enqueue(callback);
    }

    /**
     * 获取CMS配置信息
     *
     * @return CMS配置信息
     */
    public void getCmsData(String panelId, Callback<CmsProtocolEntry> callback) {
        PanelApi.getPanelApi().getCmsData(panelId).enqueue(callback);
    }

    /**
     * 修改CMS配置信息
     */
    public void modifyCms(String userId, String devToken, String messageId, CmsProtocolModel protocolModel,
                          Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().modifyCms(messageId, devToken, protocolModel, userId).enqueue(callback);
    }

    /**
     * 获取ReadyToArm的开关状态
     */
    public void getReadyToArmStatus(String panelId, Callback<ReadyToArmSwitchStatusEntry> callback) {
        PanelApi.getPanelApi().getReadyToArmSwitchStatus(panelId).enqueue(callback);
    }

    /**
     * 设置ReadyToArm的开关状态
     */
    public void ChangeReadyToArmState(String uid, String deviceToken, String messageid, boolean enable,
                                      Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().ChangeReadyToArmSwitchCall(uid, deviceToken, messageid, enable).enqueue(callback);
    }


    /**
     * 高级设置-获取之前设置的延时布防时间
     */
    public void getExitDelayTime(String panelId, Callback<TimePickerEntry> callback) {
        PanelApi.getPanelApi().getDelayTimeCall(panelId).enqueue(callback);
    }

    /**
     * 高级设置-设置的延时布防时间
     * <p>
     * 2.1.6 增加exitdelaysound设置
     */
    public void changeExitDelayTime(String uid, String deviceToken, String messageid,
                                    int time, boolean exitdelaysound, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getChangeExitDelayTimeCall(uid, deviceToken, messageid, time, exitdelaysound).enqueue(callback);
    }


    /**
     * 高级设置-获取之前设置的主机报警鸣响时长
     */
    public void getSirenTime(String panelId, Callback<TimePickerEntry> callback) {
        PanelApi.getPanelApi().getSirenTimeCall(panelId).enqueue(callback);
    }

    /**
     * 高级设置-设置的主机报警鸣响时长
     */
    public void changeSirenTime(String uid, String deviceToken, String messageid,
                                int time, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getChangeSirenTimeCall(uid, deviceToken, messageid, time).enqueue(callback);
    }

    public void updateTuyaPluginName(String uid, String deviceToken,
                                     String messageid, String pluginId, String newName,
                                     Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().sendUpdateTuyaPluginNameCmd(messageid, deviceToken, uid,
                pluginId, newName).enqueue(callback);
    }

    /**
     * 高级设置-获取之前设置的值
     */
    public void getAdvancedSetting(String panelId, Callback<GetAdvancedSettingResult> callback) {
        PanelApi.getPanelApi().getAdvancedSetting(panelId).enqueue(callback);
    }

    /**
     * 高级设置-获取主机布撤防提示音
     */
    public void getPlaySoundSetting(String panelId, Callback<GetPlaySoundSettingResult> callback) {
        PanelApi.getPanelApi().getPlaySoundSetting(panelId).enqueue(callback);
    }

    /**
     * 高级设置-设置主机布撤防提示音
     */
    public void setPlaySoundSetting(String uid, String deviceToken, String messageid, boolean isOn,
                                    Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().setPlaySoundSettingCmd(uid, messageid, deviceToken, isOn).enqueue(callback);
    }

    /**
     * 高级设置-获取设置的时区
     */
    public void getPanelTimeZone(String panelId, Callback<TimeZoneResponseEntry> callback) {
        PanelApi.getPanelApi().getTimeZoneSettingCall(panelId).enqueue(callback);
    }

    /**
     * 高级设置-设置的时区
     */
    public void changePanelTimeZone(String uid, String deviceToken, String messageid, String timezone,
                                    Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getSetTimeZoneCall(uid, deviceToken, messageid, timezone).enqueue(callback);
    }

    /**
     * 高级设置-获取4G配置
     */
    public void get4GInfo(String panelId, Callback<FourGInfoEntry> callback) {
        PanelApi.getPanelApi().get4GInfo(panelId).enqueue(callback);
    }

    /**
     * 高级设置-配置4G设置
     */
    public void set4GInfo(String uid, String deviceToken, String messageId, FourGInfoEntry.ResultBean bean,
                          Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().set4GInfo(uid, deviceToken, messageId, bean).enqueue(callback);
    }

    /**
     * 请求主机打开蓝牙
     */
    public void callDeviceOpenBle(String uid, String deviceToken, String messageid, boolean isOpen,
                                  Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().callDeviceOpenBle(uid, deviceToken, messageid, isOpen).enqueue(callback);
    }

    /**
     * 高级设置-修改主机密码
     */
    public void changeDevicePassword(String uid, String deviceToken, String messageid, String old_password, String password,
                                     Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getChangeDevicePasswordCall(uid, messageid, deviceToken, old_password, password).enqueue(callback);
    }

    /**
     * 高级设置-重置主机
     */
    public void resetPanel(String uid, String deviceToken, String messageid, String password,
                           boolean retainplugins, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getResetDeviceCall(uid, deviceToken, messageid, password, retainplugins).enqueue(callback);
    }

    /**
     * 获取主机sim卡信息
     */
    public void getSimData(String panelId, Callback<SimDataEntry> callback) {
        PanelApi.getPanelApi().getSimData(panelId).enqueue(callback);
    }

    /**
     * 高级设置-设置是否打开限制模式
     */
    public void changeRestrictModeState(String uid, String deviceToken, String messageId, boolean isOn,
                                        Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().setRestrictModeSmsCmd(uid, deviceToken, messageId, isOn).enqueue(callback);
    }

    /**
     * 重命名主机
     */
    public void changePanelName(String panelId, String name, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getChangeDeviceNameCall(panelId, name).enqueue(callback);
    }

    /**
     * 获取EventList的数据
     */
    public void getEventListData(String panelId, int limit, long timestamp, String filters, Callback<EventListEntry> callback) {
        PanelApi.getPanelApi().getEventListData(panelId, limit, timestamp, filters).enqueue(callback);
    }

    /**
     * 获取eventlist的设置
     */
    public void getEventListSetting(String panelId, Callback<EventListSettingEntry> callback) {
        PanelApi.getPanelApi().getEventListSetting(panelId).enqueue(callback);
    }

    /**
     * 设置EventList的记录情况
     *
     * @param dwLog     disarm时是否记录门磁的开/合事件
     * @param tamperLog disarm时是否记录主机防拆事件
     * @return
     */
    public void updateEventListSetting(String uid, String deviceToken, String messageid, boolean dwLog,
                                       boolean tamperLog, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().updateEventListSetting(uid, deviceToken, messageid, dwLog, tamperLog).enqueue(callback);
    }

    /**
     * 获取主机的报警状态
     */
    public void getSosStatus(String panelId, Callback<SosStatusEntry> callback) {
        PanelApi.getPanelApi().getSOSStatusCall(panelId).enqueue(callback);
    }

    /**
     * 其他类型配件-OtherPluginListFragment
     */
    public void getCategoryPlugsCallV3(String panelId, int categoryId, Callback<CategoryPlugsEntry> callback) {
        PanelApi.getPanelApi().getCategoryPlugsCallV3(panelId, categoryId).enqueue(callback);
    }

    /**
     * 其他类型配件-OtherPluginListFragment
     */
    public Response<CategoryPlugsEntry> getCategoryPlugsCallV3Sync(String panelId, int categoryId) throws IOException {
        return PanelApi.getPanelApi().getCategoryPlugsCallV3(panelId, categoryId).execute();
    }

    /**
     * 删除其他类型配件
     */
    public void deleteOtherPlugs(String uid, String deviceToken, String messageId, String plugId, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getDeleteOtherPlugsCmdCall(uid, deviceToken, messageId, plugId).enqueue(callback);
    }

    /**
     * 添加配件
     */
    public void addPlugs(AddPlugsBuilder builder, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getAddPlugsCmdCall(builder).enqueue(callback);
    }

    /**
     * 添加非官方配件
     */
    public void addNotOfficialPlugs(AddPlugsBuilder builder, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getAddNotOfficalPlugsCmdCall(builder).enqueue(callback);
    }

    public void deletePlugs(String uid, String deviceToken, String messageid, String plugId,
                            Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getDeletePlugsCmdCall(uid, deviceToken, messageid, plugId).enqueue(callback);
    }

    public void deletePlugs(String uid, String deviceToken, String messageid,
                            String qrcode, String plugId, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getDeletePlugsCmdCall(uid, deviceToken, messageid, qrcode, plugId).enqueue(callback);
    }

    public void deleteASKPlugs(String uid, String deviceToken, String messageid, String sendid,
                               String stype, String id, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getDeleteASKPlugsCmdCall(uid, deviceToken, messageid, sendid, stype, id).enqueue(callback);
    }

    public void changePlugName(String uid, String deviceToken, String messageid, String qrcode,
                               String pluginId, String plugName, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getChangePlugNameCall(uid, deviceToken, messageid, qrcode, pluginId, plugName).enqueue(callback);
    }

    public void changeOtherPlugName(String uid, String deviceToken, String messageid, String plugId,
                                    String plugName, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getChangeOtherPlugNameCall(uid, deviceToken, messageid, plugId, plugName).enqueue(callback);
    }

    /**
     * @param isNewAskPlug 是否新型的ASK智能插座
     * @param sendid       新型插座需要传，旧插座不传
     * @param stype        新型插座需要传，旧插座不传
     * @return
     */
    public void getSmartPlugsStatusChangeCall(String uid, String deviceToken, String plugId, int isOn, boolean isNewAskPlug,
                                              String sendid, String stype, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getSmartPlugsStatusChangeCall(uid, deviceToken, plugId, isOn, isNewAskPlug, sendid, stype).enqueue(callback);
    }

    public void getAskSirenSettingCall(String userid, String deviceToken, String messageid, String sendid,
                                       String stype, String advancesetting, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getAskSirenSettingCall(userid, deviceToken, messageid, sendid, stype, advancesetting).enqueue(callback);
    }

    public void getSirenSettingCall(String userid, String deviceToken, String messageid, String pluginid,
                                    String sirensetting, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getSirenSettingCall(userid, deviceToken, messageid, pluginid, sirensetting).enqueue(callback);
    }


    public void getAddNewASKPluginCall(String userid, String devicetoken, String messageid, String plugin,
                                       Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getAddNewASKPluginCall(userid, devicetoken, messageid, plugin).enqueue(callback);
    }

    public void getASKPluginModifyCall(String userId, String deviceToken, String messageid, String datas,
                                       String name, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getASKPluginModifyCall(userId, deviceToken, messageid, datas, name).enqueue(callback);
    }

    public void getListDoorBellCall(String deviceid, Callback<ResponseBody> callback) {
        PanelApi.getPanelApi().getListDoorBellCall(deviceid).enqueue(callback);
    }

    public Response<ResponseBody> getListDoorBellCallSync(String deviceid) throws IOException {
        return PanelApi.getPanelApi().getListDoorBellCall(deviceid).execute();
    }

    public void getModifyDoorBellCall(String deviceToken, JSONObject data, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getModifyDoorBellCall(deviceToken, data).enqueue(callback);
    }

    public void getAddDoorBellCall(JSONObject data, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getAddDoorBellCall(data).enqueue(callback);
    }

    public void getDeleteDoorBellCall(String deviceToken, String id, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getDeleteDoorBellCall(deviceToken, id).enqueue(callback);
    }

    public void getDoorBellCapCall(String id, long timestamp, Callback<DoorBell> callback) {
        PanelApi.getPanelApi().getDoorBellCapCall(id, timestamp).enqueue(callback);
    }

    public void getDeleteDoorBellCapCall(String id, String deviceToken, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getDeleteDoorBellCapCall(id, deviceToken).enqueue(callback);
    }

    public void getTestSirenCall(String userid, String devicetoken, String sendid, String stype,
                                 int music, int volume, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getTestSirenCall(userid, devicetoken, sendid, stype, music, volume).enqueue(callback);
    }

    public void getNotClosedDoorListDataCall(String userId, String deviceToken, String messageId,
                                             String task, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getNotClosedDoorListDataCall(userId, deviceToken, messageId, task).enqueue(callback);
    }

    public void getNewAskPlugInfo(String deviceid, String stype, String sendid, Callback<ResponseBody> callback) {
        PanelApi.getPanelApi().getNewAskPlugInfo(deviceid, stype, sendid).enqueue(callback);
    }

    /**
     * 获取自定义五键遥控器控制的智能插座配件列表
     */
    public void getCustomizeSmartPlugs(String deviceId, Callback<ResponseBody> callback) {
        PanelApi.getPanelApi().getCustomizeSmartPlugs(deviceId).enqueue(callback);
    }

    /**
     * @param pluginId 配件id
     *                 dtype       如果是新插座，则dtype为10；旧插座，则dtype为3
     *                 stype       旧插座时，sendid为空字符串,目前新插座传3E
     * @param sendid   旧插座时，stype为空字符串
     */
    public void setCustomizeSmartPlugs(String userId, String deviceToken, String messageId, String pluginId,
                                       String sendid, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().setCustomizeSmartPlugs(userId, deviceToken, messageId, pluginId, sendid).enqueue(callback);
    }

    /**
     * 获取SmartButton的配置信息
     */
    public void getSmartButtonConfig(String deviceid, String sendid, String stype, Callback<ResponseBody> callback) {
        PanelApi.getPanelApi().getSmartButtonConfig(deviceid, sendid, stype).enqueue(callback);
    }

    /**
     * 修改或保存SmartButton的控制对象配置信息
     *
     * @param smartButtonSendId SmartButton的sendid
     * @param smartButtonStype  SmartButton的stype
     * @param actionConfig      SmartButton执行的指令信息
     */
    public void updateAndSaveSmartButtonConfig(String userId, String deviceToken, String messageId, String smartButtonSendId,
                                               String smartButtonStype, Callback<StringResponseEntry> callback,
                                               ArrayList<JSONObject> actionConfig) {
        PanelApi.getPanelApi().updateAndSaveSmartButtonConfig(userId, deviceToken, messageId,
                smartButtonSendId, smartButtonStype, actionConfig).enqueue(callback);
    }

    /**
     * 修改配件的block模式
     *
     * @param pluginId 涂鸦配件ID
     * @param block    // int，配件屏蔽设置，0：不屏蔽；1、屏蔽tamper alarm；2：屏蔽整个配件；3：chime
     */
    public void getModifyPluginBlockCall(String userId, String deviceToken, String messageId, String pluginId,
                                         String sendid, String stype, int block, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getModifyPluginBlockCall(userId, deviceToken, messageId, pluginId, sendid, stype, block).enqueue(callback);
    }

    /**
     * 获取caremode数据
     */
    public void getCareModeData(String deviceid, Callback<ResponseBody> callback) {
        PanelApi.getPanelApi().getCareModeData(deviceid).enqueue(callback);
    }

    /**
     * 修改care mode模式开关
     *
     * @param isOn 是否打开care mode
     */
    public void getModifyCareModeCall(String userId, String deviceToken, String messageId,
                                      boolean isOn, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getModifyCareModeCall(userId, deviceToken, messageId, isOn).enqueue(callback);
    }

    /**
     * 修改care mode模式开关
     *
     * @param noActionTime 没有响应时间
     */
    public void getModifyCareModeNoActionTimeCall(String userId, String deviceToken, String messageId,
                                                  int noActionTime, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getModifyCareModeNoActionTimeCall(userId, deviceToken, messageId, noActionTime).enqueue(callback);
    }

    /**
     * 修改care mode模式开关
     */
    public void getModifyCareModeAlarmTimeCall(String userId, String deviceToken, String messageId,
                                               int alarmTime, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getModifyCareModeAlarmTimeCall(userId, deviceToken, messageId, alarmTime).enqueue(callback);
    }

    /**
     * 修改care mode模式开关
     */
    public void getModifyCareModePluginCall(String userId, String deviceToken, String messageId,
                                            String data, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getModifyCareModePluginCall(userId, deviceToken, messageId, data).enqueue(callback);
    }

    /**
     * 取消老人模式倒计时
     */
    public void getCancelCareModeNoActionCall(String userId, String deviceToken, String messageId,
                                              Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getCancelCareModeNoActionCall(userId, deviceToken, messageId).enqueue(callback);
    }

    /**
     * 取消老人模式倒计时
     */
    public void getCareModeNoActionSosCall(String userId, String deviceToken, String messageId, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getCareModeNoActionSosCall(userId, deviceToken, messageId).enqueue(callback);
    }

    /**
     * 获取设备转态详情页的异常配件信息指令，结果通过websocket返回
     */
    public void getHomeExceptionAccessoryInfo(String userId, String panelToken, String messageId, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getHomeExceptionAccessoryInfo(messageId, panelToken, userId).enqueue(callback);
    }

    /**
     * 操作继电器
     */
    public void getRelayControlCall(String userid, String devicetoken, String messageid, String action,
                                    String sendid, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().getRelayControlCall(userid, devicetoken, messageid, action, sendid).enqueue(callback);
    }

    /**
     * 获取首页配件具体信息-用于跳转改名页
     */
    public void getHomePluginDetails(String panelId, int category, String stype, String sendid,
                                     String id, Callback<ResponseBody> callback) {
        PanelApi.getPanelApi().getHomePluginDetails(panelId, category, stype, sendid, id).enqueue(callback);
    }

    /**
     * 删除离线主机
     */
    public void deletePanel(String panelId, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().deletePanel(panelId).enqueue(callback);
    }

    public void updatePanelFirmware(String userid, String deviceToken, String messageid,
                                    Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().updatePanelFirmware(userid, deviceToken, messageid).enqueue(callback);
    }

    public void setDoorWindowPushStatus(String userid, String deviceToken, String messageid, String pluginId,
                                        String sendid, String stype, boolean pushStatus, Callback<StringResponseEntry> callback) {
        PanelApi.getPanelApi().setDoorWindowPushStatus(userid, deviceToken, messageid, pluginId, sendid, stype, pushStatus).enqueue(callback);
    }

    /**
     * 获取指定主机的布防规则
     */
    public void getArmRules(String homeId, String deviceId, Callback<GetArmRulesResponse> callback) {
        PanelApi.getPanelApi().getArmRules(homeId, deviceId).enqueue(callback);
    }

    /**
     * 获取 care mode 当前状态
     */
    public void getCareModeStatus(String homeId, String deviceId, Callback<GetCareModeStatusResponse> callback) {
        PanelApi.getPanelApi().getCareModeStatus(homeId, deviceId).enqueue(callback);
    }

    /**
     * 获取指定主机的布撤防提示声设置
     */
    public void getVoicePrompt(String homeId, String deviceId, Callback<GetVoicePromptResponse> callback) {
        PanelApi.getPanelApi().getVoicePrompt(homeId, deviceId).enqueue(callback);
    }

    /**
     * 按时间获取配件的信息
     */
    @Nullable
    public ListAccessoriesResponse listAccessories(String homeId, String deviceId, long addTime, List<String> sTypeList, boolean orderDesc) {
        ListAccessoriesResponse response = null;
        try {
            Response<ListAccessoriesResponse> r = PanelApi.getPanelApi().listAccessories(homeId, deviceId, addTime, sTypeList, orderDesc).execute();
            if (r.isSuccessful() && null != r.body()) {
                response = r.body();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return response;
    }

    /**
     * 查找指定配件的数据
     *
     * @param addTime   时间戳-筛选开始时间
     * @param orderDesc true为倒序
     */
    public void searchAccessories(String homeId, String deviceId, List<AccessoriesInfoParams> accessories,
                                  long addTime, boolean orderDesc, Callback<SearchAccessoriesResponse> callback) {
        PanelApi.getPanelApi().searchAccessories(homeId, deviceId, accessories, addTime, orderDesc).enqueue(callback);
    }

    /**
     * 获取指定主机的信息
     */
    public SearchPanelsResponse searchPanelsSync(String homeId, @NotNull List<String> panelIds) {
        SearchPanelsResponse response = null;
        try {
            Response<SearchPanelsResponse> r = PanelApi.getPanelApi().searchPanels(homeId, panelIds).execute();
            if (r.isSuccessful() && null != r.body()) {
                response = r.body();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return response;
    }

    public void searchPanelsAsync(String homeId, @NotNull List<String> panelIds, Callback<SearchPanelsResponse> callback) {
        mGetDeviceInfoCall = PanelApi.getPanelApi().searchPanels(homeId, panelIds);
        mGetDeviceInfoCall.enqueue(callback);
    }

    @Nullable
    public ListPanelTokensResponse listPanelTokensSync(String homeID, long addTime, boolean orderDesc, int pageSize) {
        ListPanelTokensResponse response = null;
        try {
            Response<ListPanelTokensResponse> r = PanelApi.getPanelApi().listPanelTokens(homeID, addTime, orderDesc, pageSize).execute();
            if (r.isSuccessful() && null != r.body()) {
                response = r.body();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return response;
    }

    /**
     *  临时屏蔽红外触发
     *  */
    public void setPirSettingEnabledStatue(String deviceToken, String homeId, String messageId
            , String pluginId, String sendId, String sType, String userId, Callback<PirSettingEnabledStatueEntry> callback) {
        PanelApi.getPanelApi().setPirSettingEnabledStatue(deviceToken, homeId, messageId, pluginId
                , sendId, sType, userId).enqueue(callback);
    }

    /**
     * 修改灵敏度
     */
    public void setPirSensitivity(String deviceToken, String homeId, String messageId, String pluginId
            , String sendId, String sType, String userId, int sensitivity, Callback<PirSensitivityResponse> callback) {
        PanelApi.getPanelApi().setPirSensitivity(deviceToken, homeId, messageId, pluginId, sendId
                , sType, userId, sensitivity).enqueue(callback);
    }

    /**
     * 退出红外设置模式
     */
    public void exitPirSettingMode(String deviceToken, String homeId
            , String messageId, String pluginId, String sendId, String sType, String userId, Callback<PirSensitivityEntry> callback) {
        PanelApi.getPanelApi().exitPirSettingMode(deviceToken, homeId, messageId, pluginId, sendId, sType, userId).enqueue(callback);
    }
}
