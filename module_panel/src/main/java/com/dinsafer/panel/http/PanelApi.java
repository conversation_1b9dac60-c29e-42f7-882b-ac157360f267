package com.dinsafer.panel.http;

import android.text.TextUtils;

import com.dinsafe.Dinsafe;
import com.dinsafer.dincore.activtor.AddPlugsBuilder;
import com.dinsafer.dincore.http.Api;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.http.Urls;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.dincore.utils.RandomStringUtils;
import com.dinsafer.dssupport.plugin.PluginTypeHelper;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.operate.PanelOperatorConstant;
import com.dinsafer.panel.operate.bean.AppMessageEntry;
import com.dinsafer.panel.operate.bean.CategoryPlugsEntry;
import com.dinsafer.panel.operate.bean.CmsProtocolEntry;
import com.dinsafer.panel.operate.bean.CmsProtocolModel;
import com.dinsafer.panel.operate.bean.ContactIdResponseEntry;
import com.dinsafer.panel.operate.bean.DoorBell;
import com.dinsafer.panel.operate.bean.EntryDelayModel;
import com.dinsafer.panel.operate.bean.EventListEntry;
import com.dinsafer.panel.operate.bean.EventListSettingEntry;
import com.dinsafer.panel.operate.bean.FourGInfoEntry;
import com.dinsafer.panel.operate.bean.GetAdvancedSettingResult;
import com.dinsafer.panel.operate.bean.GetArmRulesResponse;
import com.dinsafer.panel.operate.bean.GetCareModeStatusResponse;
import com.dinsafer.panel.operate.bean.GetPlaySoundSettingResult;
import com.dinsafer.panel.operate.bean.GetVoicePromptResponse;
import com.dinsafer.panel.operate.bean.HomeArmStatueEntry;
import com.dinsafer.panel.operate.bean.HomeDeviceInfoEntry;
import com.dinsafer.panel.operate.bean.HomePluginQuantityEntry;
import com.dinsafer.panel.operate.bean.ListAccessoriesResponse;
import com.dinsafer.panel.operate.bean.ListPanelTokensResponse;
import com.dinsafer.panel.operate.bean.PirSensitivityEntry;
import com.dinsafer.panel.operate.bean.PirSensitivityResponse;
import com.dinsafer.panel.operate.bean.PirSettingEnabledStatueEntry;
import com.dinsafer.panel.operate.bean.ReadyToArmSwitchStatusEntry;
import com.dinsafer.panel.operate.bean.SearchAccessoriesResponse;
import com.dinsafer.panel.operate.bean.SearchPanelsResponse;
import com.dinsafer.panel.operate.bean.SimDataEntry;
import com.dinsafer.panel.operate.bean.SosMessageEntry;
import com.dinsafer.panel.operate.bean.SosStatusEntry;
import com.dinsafer.panel.operate.bean.TimePickerEntry;
import com.dinsafer.panel.operate.bean.TimeZoneResponseEntry;
import com.dinsafer.panel.operate.bean.param.AccessoriesInfoParams;
import com.dinsafer.panel.util.PanelSecretUtil;
import com.google.gson.Gson;

import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import okhttp3.ResponseBody;
import retrofit2.Call;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/12 2:55 PM
 */
public class PanelApi {
    private static final String ORDER_DESC = "desc";
    private static final String ORDER_ASC = "asc";

    private final IPanelApi services;
    public static final String GM = "gm";
    public static final String GMTIME = "gmtime";

    private PanelApi() {
        services = Api.getApi().getRetrofit().create(IPanelApi.class);
    }

    private static class Holder {
        private static final PanelApi INSTANT = new PanelApi();
    }

    public static PanelApi getPanelApi() {
        return Holder.INSTANT;
    }

    private Map<String, Object> getGM(Map<String, Object> map) {
        map.put(GM, 1);
        return map;
    }

    private JSONObject getGMTime(JSONObject jsonObject) {
        try {
            jsonObject.put(GMTIME, System.currentTimeMillis() * 1000);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObject;
    }

    public Call<StringResponseEntry> getDeviceCmdCall(String uid, String deviceToken, String messageid, String cmd, Boolean force) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", cmd);
            jsonObject.put("force", force);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    /**
     * 获取主机信息
     *
     * @param deviceId 设备ID
     */
    public Call<HomeDeviceInfoEntry> getHomeDeviceInfo(final String homeId, String deviceId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
            jsonObject.put("device_id", deviceId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getDeviceInfo(Api.getApi().getUrl(PanelUrls.URL_DEVICE_INFO), getGM(map));
    }

    /**
     * 获取设置页面中配件数据等信息
     *
     * @param deviceId 设备ID
     * @return
     */
    public Call<HomePluginQuantityEntry> getHomePluginQuantityInfo(String homeId, String deviceId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
            jsonObject.put("deviceid", deviceId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getPluginQuantityInfo(Api.getApi().getUrl(PanelUrls.URL_PLUGIN_QUANTITY_INFO), getGM(map));
    }

    /**
     * 获取首页配件信息
     *
     * @param deviceId 设备ID
     * @return 首页配件数据
     */
    public Call<ResponseBody> getHomePluginInfo(String deviceId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
            jsonObject.put("device_id", deviceId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getPluginInfo(Api.getApi().getUrl(PanelUrls.URL_HOME_PLUGIN_INFO), getGM(map));
    }

    /**
     * 获取配件状态
     *
     * @param sTypes "1C,38,32",  // 指定需要查询的类型
     * @param type   1:ready to arm; 2:power; 3:都要
     */
    public Call<StringResponseEntry> getPluginStatus(String messageId, String devToken, String userId,
                                                     ArrayList<String> sTypes, int type) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            StringBuilder sb = new StringBuilder();
            if (null != sTypes) {
                for (int i = 0; i < sTypes.size(); i++) {
                    sb.append(sTypes.get(i));
                    if (sTypes.size() - 1 != i) {
                        sb.append(",");
                    }
                }
            }

            jsonObject.put("messageid", messageId);
            jsonObject.put("devtoken", devToken);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.TASK_PLUGIN_STATUS);
            jsonObject.put("userid", userId);
            jsonObject.put("stype", sb.toString());
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), getGM(map));
    }

    /**
     * 获取SmartButton列表数据
     *
     * @param deviceid 主机ID
     * @return smartButton列表
     */
    public Call<ResponseBody> getSmartButtonListCall(String deviceid) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceid);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getSmartButtonListData(Api.getApi().getUrl(PanelUrls.URL_LIST_SMART_BUTTON), getGM(map));
    }

    /**
     * 获取指定类型的配件列表
     *
     * @param deviceId   主机ID
     * @param categoryId 配件类型
     * @return 配件列表
     */
    public Call<ResponseBody> getSpecifyPlugin(String deviceId, int categoryId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceId);
            jsonObject.put("category", categoryId);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getSpecifyPlugin(Api.getApi().getUrl(PanelUrls.URL_LIST_SPECIFY_PLUGIN), map);
    }

    /**
     * 获取继电器配件列表
     *
     * @param deviceid 主机ID
     * @return 继电器列表
     */
    public Call<ResponseBody> getRelayListCall(String deviceid) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceid);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());

        return services.getRelayListData(Api.getApi().getUrl(PanelUrls.URL_LIST_RELAY), getGM(map));
    }

    /**
     * 获取安防配件列表
     */
    public Call<ResponseBody> getCategoryPlugsCallV4(String deviceId, int categoryId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceId);
            jsonObject.put("category", categoryId);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getCategoryPlugsCallV4(Api.getApi().getUrl(PanelUrls.URL_LIST_ACCESSORY_V2), map);
    }

    /**
     * 获取门窗探测器列表
     */
    public Call<ResponseBody> getDoorSensorListData(String deviceId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceId);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());

        return services.getDoorSensorListData(Api.getApi().getUrl(PanelUrls.URL_LIST_DOOR_SENSOR), getGM(map));
    }

    /**
     * 获取CustomizeHomeArm可设置的配件列表
     */
    public Call<HomeArmStatueEntry> getHomeArmStatusCall(String deviceId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceId);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getHomeArmStatusCall(Api.getApi().getUrl(PanelUrls.URL_LIST_CUSTOMIZE_HOME_ARM), map);
    }

    /**
     * 获取延时报警可设置的配件列表
     */
    public Call<EntryDelayModel> getEntryDelayCall(String deviceId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceId);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getEntryDelayCall(Api.getApi().getUrl(PanelUrls.URL_LIST_ENTRY_DELAY), map);
    }

    /**
     * 设置定制在家布防或延时报警
     */
    public Call<StringResponseEntry> getChangeEntryDelayCall(String uid, String messageid, String deviceToken,
                                                             String type, int time, String data, String thirdParty,
                                                             String askData, boolean entrydelaysound) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", type);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());

            if (time >= 0) {
                jsonObject.put("time", time);
            }

            if (data != null) {
                jsonObject.put("datas", data);
            }

            if (thirdParty != null) {
                jsonObject.put("thirdpartydatas", thirdParty);
            }

            if (askData != null) {
                jsonObject.put("newaskdatas", askData);
            }

            jsonObject.put("entrydelaysound", entrydelaysound);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    /**
     * 安全设置-消息设置-获取当前设置的通知语言
     */
    public Call<AppMessageEntry> getDeviceTextCall(String deviceId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceId);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceTextCall(Api.getApi().getUrl(PanelUrls.URL_GET_DEVICE_TEXT), map);
    }

    /**
     * 安全设置-消息设置-设置推送语言
     */
    public Call<StringResponseEntry> getNewPushMessageCall(String userid, String devicetoken,
                                                           String messageid, String lang) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", userid);
            jsonObject.put("devtoken", devicetoken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("lang", lang);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_DEVICE_TEXT_NEW);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    /**
     * 安全设置-消息设置-设置推送语言
     */
    public Call<StringResponseEntry> getSaveMessage(String uid, String deviceToken, String messageid,
                                                    String deviceText, String lang) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_DEVICE_TEXT);
            jsonObject.put("device_text", deviceText);
            jsonObject.put("lang", lang);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    /**
     * 安全设置-胁迫报警设置-获取胁迫报警开关状态
     */
    public Call<SosMessageEntry> getSosMessageCall(String deviceId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceId);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getSOSMessageCall(Api.getApi().getUrl(PanelUrls.URL_GET_INTIMIDATION_ALARM), map);
    }

    /**
     * 安全设置-胁迫报警设置-第一次打开取胁迫报警
     */
    public Call<StringResponseEntry> getFirstChangeSos(String uid, String messageid, String devtoken,
                                                       String pushliterary, String password) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("messageid", messageid);
            jsonObject.put("devtoken", devtoken);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_INTIMIDATIONALARM_DATA_ON);
            jsonObject.put("pushliterary", pushliterary);
            jsonObject.put("password", password);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    /**
     * 安全设置-胁迫报警设置-修改胁迫报警开关转态（非第一次打开）
     */
    public Call<StringResponseEntry> getChangeSosEnable(String uid, String messageid, String devtoken, boolean enable) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("messageid", messageid);
            jsonObject.put("devtoken", devtoken);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_INTIMIDATIONALARM_ENABLE);
            jsonObject.put("enable", enable);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    /**
     * 安全设置-胁迫报警设置-修改胁迫报警密码
     */
    public Call<StringResponseEntry> getChangeSosPassword(String uid, String messageid, String devtoken, String password) {

        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("messageid", messageid);
            jsonObject.put("devtoken", devtoken);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_INTIMIDATIONALARM_PASSWORD);
            jsonObject.put("password", password);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    /**
     * 安全设置-胁迫报警设置-修改胁迫报警报警信息
     */
    public Call<StringResponseEntry> getChangeSosMessage(String uid, String messageid, String devtoken, String pushliterary) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("messageid", messageid);
            jsonObject.put("devtoken", devtoken);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_INTIMIDATIONALARM_PUSH_TXT);
            jsonObject.put("pushliterary", pushliterary);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    /**
     * 安全设置-设置CID
     */
    public Call<ContactIdResponseEntry> getContactIdCall(String deviceid) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceid);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getContactId(Api.getApi().getUrl(PanelUrls.URL_GET_CID_DATA), map);
    }

    /**
     * 消息设置-修改CID设置
     */
    public Call<StringResponseEntry> getUpdateContactIdCall(String uid, String deviceToken, String messageid,
                                                            boolean enable, String contactidcode, String countrycode, String phone) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_CONTACTID);
            jsonObject.put("enable", enable);
            jsonObject.put("contactidcode", contactidcode);
            jsonObject.put("countrycode", countrycode);
            jsonObject.put("phone", phone);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    /**
     * 获取CMS配置信息
     *
     * @return CMS配置信息
     */
    public Call<CmsProtocolEntry> getCmsData(String deviceId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceId);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());

        return services.getCmsData(Api.getApi().getUrl(PanelUrls.URL_GET_CMS_DATA), getGM(map));
    }

    /**
     * 修改CMS配置信息
     */
    public Call<StringResponseEntry> modifyCms(String messageId, String devToken,
                                               CmsProtocolModel protocolModel, String userId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        JSONObject info = new JSONObject();
        try {
            jsonObject.put("messageid", messageId);
            jsonObject.put("devtoken", devToken);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_CMS_INFO);
            jsonObject.put("protocol_name", protocolModel.getProtocolName());
            jsonObject.put("userid", userId);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());

            if (!TextUtils.isEmpty(protocolModel.getInfoStr())) {
                jsonObject.put("info", new JSONObject(protocolModel.getInfoStr()));
            } else {
                info.put("primary_ip", protocolModel.getInfo().getPrimary_ip());
                info.put("primary_port", protocolModel.getInfo().getPrimary_port());
                info.put("secondary_ip", protocolModel.getInfo().getSecondary_ip());
                info.put("secondary_port", protocolModel.getInfo().getSecondary_port());
                info.put("account_number", protocolModel.getInfo().getAccount_number());
                info.put("encryption", protocolModel.getInfo().isEncryption());
                info.put("encryption_key", protocolModel.getInfo().getEncryption_key());
                info.put("network", protocolModel.getInfo().getNetwork());

                jsonObject.put("info", info);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), getGM(map));
    }

    /**
     * 获取ReadyToArm的开关状态
     */
    public Call<ReadyToArmSwitchStatusEntry> getReadyToArmSwitchStatus(String deviceId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceId);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("token", UserManager.getInstance().getToken());
        map.put("json", getGMTime(jsonObject));

        return services.getReadyToArmSwitchStatus(Api.getApi().getUrl(PanelUrls.URL_GET_READY_TO_ARM_STATE), getGM(map));
    }

    /**
     * 设置ReadyToArm的开关状态
     */
    public Call<StringResponseEntry> ChangeReadyToArmSwitchCall(String uid, String deviceToken, String messageid, boolean enable) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_READYTOARM);
            jsonObject.put("enable", enable);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), getGM(map));
    }

    /**
     * 高级设置-获取之前设置的延时布防时间
     */
    public Call<TimePickerEntry> getDelayTimeCall(String deviceId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceId);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getExitDelayTimeCall(Api.getApi().getUrl(PanelUrls.URL_GET_EXIT_DELAY_TIME), map);
    }

    /**
     * 高级设置-设置的延时布防时间
     * <p>
     * 2.1.6 增加exitdelaysound设置
     */
    public Call<StringResponseEntry> getChangeExitDelayTimeCall(String uid, String deviceToken, String messageid,
                                                                int time, boolean exitdelaysound) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_EXITDELAY);
            jsonObject.put("time", time);
            jsonObject.put("exitdelaysound", exitdelaysound);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    /**
     * 高级设置-获取之前设置的主机报警鸣响时长
     */
    public Call<TimePickerEntry> getSirenTimeCall(String deviceId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceId);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getSirenTimeCall(Api.getApi().getUrl(PanelUrls.URL_GET_SIREN_TIME), map);
    }

    /**
     * 高级设置-设置的主机报警鸣响时长
     */
    public Call<StringResponseEntry> getChangeSirenTimeCall(String uid, String deviceToken, String messageid, int time) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SIRENTIME_KEY);
            jsonObject.put("time", time);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }


    public Call<StringResponseEntry> sendUpdateTuyaPluginNameCmd(String messageId, String deviceToken,
                                                                 String userId, String pluginId, String newName) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("messageid", messageId);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("userid", userId);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.UPDATE_TUYA_NAME);

            jsonObject.put("pluginid", pluginId);
            jsonObject.put("name", newName);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    /**
     * 高级设置-获取之前设置的值
     */
    public Call<GetAdvancedSettingResult> getAdvancedSetting(String deviceId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceId);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());

        return services.getAdvancedSetting(Api.getApi().getUrl(PanelUrls.URL_GET_ADVANCED_SETTING), getGM(map));
    }

    /**
     * 高级设置-获取主机布撤防提示音
     */
    public Call<GetPlaySoundSettingResult> getPlaySoundSetting(String deviceId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceId);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());

        return services.getPlaySoundSetting(Api.getApi().getUrl(PanelUrls.URL_GET_PLAY_SOUND_ARM_DISARM), getGM(map));
    }

    /**
     * 高级设置-设置主机布撤防提示音
     */
    public Call<StringResponseEntry> setPlaySoundSettingCmd(String uid, String messageid,
                                                            String deviceToken, boolean isOn) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_PLAY_SOUND);
            jsonObject.put("ison", isOn);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), getGM(map));
    }

    /**
     * 高级设置-获取设置的时区
     */
    public Call<TimeZoneResponseEntry> getTimeZoneSettingCall(String deviceid) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceid);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getTimeZoneSettingCall(Api.getApi().getUrl(PanelUrls.URL_GET_TIMEZONE), map);
    }

    /**
     * 高级设置-设置的时区
     */
    public Call<StringResponseEntry> getSetTimeZoneCall(String uid, String deviceToken, String messageid, String timezone) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_DEVICE_TIMEZONE);
            jsonObject.put("timezone", timezone);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    /**
     * 高级设置-获取4G配置
     */
    public Call<FourGInfoEntry> get4GInfo(String deviceid) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceid);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.get4GInfo(Api.getApi().getUrl(PanelUrls.URL_GET_4G_SETTING), getGM(map));
    }

    /**
     * 高级设置-配置4G设置
     */
    public Call<StringResponseEntry> set4GInfo(String uid, String deviceToken, String messageid, FourGInfoEntry.ResultBean bean) {
        Map<String, Object> map = new HashMap<>();

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            //将数据作为字符串传，跟主机联调过
            jsonObject.put("info", new Gson().toJson(bean));
            jsonObject.put("cmd", PanelOperatorConstant.CMD.CMD_SET_4G);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    /**
     * 请求主机打开蓝牙
     */
    public Call<StringResponseEntry> callDeviceOpenBle(String uid, String deviceToken, String messageid, boolean isOpen) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.CMD_SET_BT);
            jsonObject.put("bt", isOpen);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), getGM(map));
    }

    /**
     * 高级设置-修改主机密码
     */
    public Call<StringResponseEntry> getChangeDevicePasswordCall(String uid, String messageid, String deviceToken, String old_password, String password) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_PASSWORD);
            jsonObject.put("old_password", old_password);
            jsonObject.put("password", password);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    /**
     * 高级设置-重置主机
     */
    public Call<StringResponseEntry> getResetDeviceCall(String uid, String deviceToken, String messageid,
                                                        String password, boolean retainplugins) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.RESET_DEVICE);
            jsonObject.put("password", password);
            jsonObject.put("retainplugins", retainplugins);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    /**
     * 获取主机sim卡信息
     */
    public Call<SimDataEntry> getSimData(String deviceid) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceid);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getSimData(Api.getApi().getUrl(PanelUrls.URL_GET_SIM_DATA), getGM(map));
    }

    /**
     * 高级设置-设置是否打开限制模式
     */
    public Call<StringResponseEntry> setRestrictModeSmsCmd(String uid, String deviceToken, String messageid, boolean isOn) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_RESTRICT_MODE_SMS);
            jsonObject.put("offline_sms", isOn);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), getGM(map));
    }

    /**
     * 重命名主机
     */
    public Call<StringResponseEntry> getChangeDeviceNameCall(String deviceId, String name) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceId);
            jsonObject.put("name", name);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getChangeDeviceNameCall(Api.getApi().getUrl(PanelUrls.URL_RENAME_DEVICE), map);
    }

    /**
     * 获取EventList的数据
     */
    public Call<EventListEntry> getEventListData(String deviceid, int limit, long timestamp, String filters) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            JSONArray f;
            try {
                f = new JSONArray(filters);
            } catch (Exception e) {
                f = new JSONArray();
                e.printStackTrace();
            }
            jsonObject.put("deviceid", deviceid);
            jsonObject.put("limit", limit);
            jsonObject.put("timestamp", timestamp);
            jsonObject.put("filters", f);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getEventList(Api.getApi().getUrl(PanelUrls.URL_GET_EVENT_LIST), map);
    }

    /**
     * 获取eventlist的设置
     */
    public Call<EventListSettingEntry> getEventListSetting(String deviceId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("device_id", deviceId);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getEventListSetting(Api.getApi().getUrl(PanelUrls.URL_GET_EVENT_LIST_SETTING), getGM(map));
    }

    /**
     * 设置EventList的记录情况
     *
     * @param dwLog     disarm时是否记录门磁的开/合事件
     * @param tamperLog disarm时是否记录主机防拆事件
     */
    public Call<StringResponseEntry> updateEventListSetting(String uid, String deviceToken, String messageid,
                                                            boolean dwLog, boolean tamperLog) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.UPDATE_EVENTLIST_SETTING);

            jsonObject.put("dw_event_log", dwLog);
            jsonObject.put("tamper_event_log", tamperLog);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), getGM(map));
    }

    /**
     * 获取主机的报警状态
     */
    public Call<SosStatusEntry> getSOSStatusCall(String deviceid) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceid);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getSosStatusCall(Api.getApi().getUrl(PanelUrls.URL_GET_SOS_STATE), map);
    }

    /**
     * 其他类型配件-OtherPluginListFragment
     */
    public Call<CategoryPlugsEntry> getCategoryPlugsCallV3(String deviceId, int categoryId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceId);
            jsonObject.put("category", categoryId);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getCategoryPlugsCallV3(Api.getApi().getUrl(PanelUrls.URL_LIST_SPECIFY_PLUGIN_V3), map);
    }

    /**
     * 删除其他类型配件
     */
    public Call<StringResponseEntry> getDeleteOtherPlugsCmdCall(String uid, String deviceToken, String messageid, String plugId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.DELETE_PLUGIN);

            jsonObject.put("category", PanelConstant.Category.OTHER_PLUGIN);
            jsonObject.put("pluginid", plugId);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), getGM(map));
    }

    /**
     * 添加配件
     */
    public Call<StringResponseEntry> getAddPlugsCmdCall(AddPlugsBuilder builder) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", builder.getUid());
            jsonObject.put("pluginid", builder.getPlugid());
            jsonObject.put("devtoken", builder.getDeviceToken());
            jsonObject.put("messageid", builder.getMessageid());
            jsonObject.put("decodeid", builder.getDecodeid());
            jsonObject.put("cmd", PanelOperatorConstant.CMD.ADD_PLUGIN);
            jsonObject.put("siren_setting", builder.getSirenSetting());

            String decodeId = "";
            if (!TextUtils.isEmpty(builder.getDecodeid())) {
                decodeId = builder.getDecodeid();
            } else {
                decodeId = Dinsafe.str64ToHexStr(builder.getPlugid());
            }

            final String dTypeString = decodeId.substring(0, 1);
            if (TextUtils.isEmpty(dTypeString)) {
                return null;
            }

//            改变配件ID
            int dType = Integer.parseInt(dTypeString);

            //检查小Type
            String sTypeID = decodeId.substring(1, 3);
            final String sType = PluginTypeHelper.getInstance().getSType(sTypeID);
            if (TextUtils.isEmpty(sType)) {
                return null;
            }

            if (dType == 5 && "30".equals(sTypeID)) {
                sTypeID = "05";
            } else if (dType == 6 && "30".equals(sTypeID)) {
                sTypeID = "05";
            } else if (dType == 8 && "30".equals(sTypeID)) {
                sTypeID = "07";
            } else if (dType == 7) {
                sTypeID = "1B";
            }

            if (dType == 5 || dType == 6 || dType == 8 || dType == 9) {
                dType = 0;
            } else if (dType == 7) {
                dType = 1;
            }
            jsonObject.put("category", dType);
            jsonObject.put("subcategory", sTypeID);
            jsonObject.put("pluginname", builder.getPlugName());
            if (!TextUtils.isEmpty(builder.getIpcData())) {
                jsonObject.put("ipcdata", PanelSecretUtil.getSC(builder.getIpcData()));
            }
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    /**
     * 添加非官方配件
     */
    public Call<StringResponseEntry> getAddNotOfficalPlugsCmdCall(AddPlugsBuilder builder) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", builder.getUid());
            jsonObject.put("pluginid", builder.getPlugid());
            jsonObject.put("devtoken", builder.getDeviceToken());
            jsonObject.put("messageid", builder.getMessageid());
            jsonObject.put("decodeid", builder.getDecodeid());
            jsonObject.put("cmd", PanelOperatorConstant.CMD.ADD_PLUGIN);
            jsonObject.put("siren_setting", builder.getSirenSetting());

            jsonObject.put("category", 20);
            jsonObject.put("subcategory", builder.getsType());
            jsonObject.put("pluginname", builder.getPlugName());
            if (!TextUtils.isEmpty(builder.getIpcData())) {
                jsonObject.put("ipcdata", PanelSecretUtil.getSC(builder.getIpcData()));
            }
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    public Call<StringResponseEntry> getDeletePlugsCmdCall(String uid, String deviceToken, String messageid, String plugId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.DELETE_PLUGIN);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());

            String decodeId = Dinsafe.str64ToHexStr(plugId);

            final String dTypeString = decodeId.substring(0, 1);
            if (TextUtils.isEmpty(dTypeString)) {
                return null;
            }

            int dType = Integer.parseInt(dTypeString);
            if (dType == 5 || dType == 6 || dType == 8 || dType == 9) {
                dType = 0;
            } else if (dType == 7) {
                dType = 1;
            }

            jsonObject.put("category", dType);
            jsonObject.put("pluginid", plugId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    public Call<StringResponseEntry> getDeletePlugsCmdCall(String uid, String deviceToken, String messageid,
                                                           String qrcode, String plugId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.DELETE_PLUGIN);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());

            String decodeId = Dinsafe.str64ToHexStr(plugId);

            final String dTypeString = decodeId.substring(0, 1);
            if (TextUtils.isEmpty(dTypeString)) {
                return null;
            }

            int dType = Integer.parseInt(dTypeString);
            if (dType == 5 || dType == 6 || dType == 8 || dType == 9) {
                dType = 0;
            } else if (dType == 7) {
                dType = 1;
            }

            jsonObject.put("category", dType);
            jsonObject.put("pluginid", qrcode);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    public Call<StringResponseEntry> getDeleteASKPlugsCmdCall(String uid, String deviceToken,
                                                              String messageid, String sendid,
                                                              String stype, String id) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.DELETE_NEWASKPLUGIN);
            jsonObject.put("sendid", sendid);
            jsonObject.put("stype", stype);
            jsonObject.put("id", id);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    public Call<StringResponseEntry> getChangePlugNameCall(String uid, String deviceToken, String messageid,
                                                           String qrcode, String pluginId, String plugName) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_PLUGINDATA);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());

            String decodeId = Dinsafe.str64ToHexStr(pluginId);

            final String dTypeString = decodeId.substring(0, 1);
            if (TextUtils.isEmpty(dTypeString)) {
                return null;
            }

            int dType = Integer.parseInt(dTypeString);
            if (dType == 5 || dType == 6 || dType == 8 || dType == 9) {
                dType = 0;
            } else if (dType == 7) {
                dType = 1;
            }

            jsonObject.put("category", dType);
            jsonObject.put("plugin_item_name", plugName);
            jsonObject.put("pluginid", qrcode);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    public Call<StringResponseEntry> getChangeOtherPlugNameCall(String uid, String deviceToken,
                                                                String messageid, String plugId, String plugName) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_PLUGINDATA);

            String decodeId = Dinsafe.str64ToHexStr(plugId);

            jsonObject.put("category", PanelConstant.Category.OTHER_PLUGIN);
            jsonObject.put("plugin_item_name", plugName);
            jsonObject.put("pluginid", plugId);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    /**
     * @param isNewAskPlug 是否新型的ASK智能插座
     * @param sendid       新型插座需要传，旧插座不传
     * @param stype        新型插座需要传，旧插座不传
     * @return
     */
    public Call<StringResponseEntry> getSmartPlugsStatusChangeCall(String uid, String deviceToken,
                                                                   String plugId, int isOn, boolean isNewAskPlug,
                                                                   String sendid, String stype) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", uid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", RandomStringUtils.getMessageId());
            jsonObject.put("cmd", isNewAskPlug ? PanelOperatorConstant.CMD.SET_NEW_SMART_PLUG_ON
                    : PanelOperatorConstant.CMD.SET_SMART_PLUG_ENABLE);
            jsonObject.put("pluginid", plugId);
            jsonObject.put("plugin_item_smart_plug_enable", isOn);

            if (isNewAskPlug) {
                // 新ASK智能插座新增字段
                jsonObject.put("sendid", sendid);
                jsonObject.put("stype", stype);
                jsonObject.put("dtype", 10);
            }
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    public Call<StringResponseEntry> getAskSirenSettingCall(String userid, String deviceToken, String messageid,
                                                            String sendid, String stype, String advancesetting) {

        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("messageid", messageid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_NEWASKSIRENDATA);
            jsonObject.put("sendid", sendid);
            jsonObject.put("stype", stype);
            jsonObject.put("advancesetting", advancesetting);
            jsonObject.put("userid", userid);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    public Call<StringResponseEntry> getSirenSettingCall(String userid, String deviceToken, String messageid,
                                                         String pluginid, String sirensetting) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", userid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_WIRELESS_SIREN_ADVANCED_SETTING);
            jsonObject.put("pluginid", pluginid);
            jsonObject.put("siren_setting", sirensetting);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }


    public Call<StringResponseEntry> getASKPluginModifyCall(String userId, String deviceToken,
                                                            String messageid, String datas, String name) {
        Map<String, Object> map = new HashMap<>();
        try {
            JSONObject data = new JSONObject(datas);
            data.put("messageid", messageid);
            data.put("devtoken", deviceToken);
            data.put("name", name);
            data.put("userid", userId);
            data.put("cmd", PanelOperatorConstant.CMD.SET_NEWASKPLUGINDATA);
            data.put("home_id", PanelManager.getInstance().getCurrentHomeId());
            map.put("json", data);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    public Call<ResponseBody> getListDoorBellCall(String deviceid) {
        Map<String, Object> map = new HashMap<>();
        try {
            JSONObject data = new JSONObject();
            data.put("deviceid", deviceid);
            data.put("home_id", PanelManager.getInstance().getCurrentHomeId());
            map.put("json", data);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("token", UserManager.getInstance().getToken());

        return services.getListDoorBellCall(Api.getApi().getUrl(PanelUrls.URL_LIST_DOORBELL_DATA), map);
    }

    public Call<StringResponseEntry> getModifyDoorBellCall(String deviceToken, JSONObject data) {
        Map<String, Object> map = new HashMap<>();
        try {
            data.put("devtoken", deviceToken);
            data.put("home_id", PanelManager.getInstance().getCurrentHomeId());
            map.put("json", data);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("token", UserManager.getInstance().getToken());

        return services.getModifyDoorBellCall(Api.getApi().getUrl(PanelUrls.URL_MODIFY_DOORBELL), map);
    }

    public Call<StringResponseEntry> getAddDoorBellCall(JSONObject data) {
        Map<String, Object> map = new HashMap<>();
        try {
            data.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (Exception e) {
            e.printStackTrace();
        }
        map.put("json", data);
        map.put("token", UserManager.getInstance().getToken());
        return services.getAddDoorBellCall(Api.getApi().getUrl(PanelUrls.URL_ADD_DOORBELL), map);
    }

    public Call<StringResponseEntry> getDeleteDoorBellCall(String deviceToken, String id) {
        Map<String, Object> map = new HashMap<>();
        try {
            JSONObject data = new JSONObject();
            data.put("home_id", PanelManager.getInstance().getCurrentHomeId());
            data.put("devtoken", deviceToken);
            data.put("id", id);
            map.put("json", data);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeleteDoorBellCall(Api.getApi().getUrl(PanelUrls.URL_DELETE_DOORBELL), map);
    }

    public Call<DoorBell> getDoorBellCapCall(String id, long timestamp) {
        Map<String, Object> map = new HashMap<>();
        try {
            JSONObject data = new JSONObject();
            data.put("deviceid", id);
            data.put("limit", 20);
            data.put("timestamp", timestamp);
            data.put("home_id", PanelManager.getInstance().getCurrentHomeId());
            map.put("json", data);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("token", UserManager.getInstance().getToken());

        return services.getDoorBellCapCall(Api.getApi().getUrl(PanelUrls.URL_LIST_DOORBELL_CAPTURE), map);
    }

    public Call<StringResponseEntry> getDeleteDoorBellCapCall(String id, String deviceToken) {
        Map<String, Object> map = new HashMap<>();
        try {
            JSONObject data = new JSONObject();
            data.put("id", id);
            data.put("devtoken", deviceToken);
            data.put("home_id", PanelManager.getInstance().getCurrentHomeId());
            map.put("json", data);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeleteDoorBellCapCall(Api.getApi().getUrl(PanelUrls.URL_DELETE_DOORBELL_CAPTURE), map);
    }

    public Call<StringResponseEntry> getTestSirenCall(String userid, String devicetoken, String sendid,
                                                      String stype, int music, int volume) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", userid);
            jsonObject.put("devtoken", devicetoken);
            jsonObject.put("messageid", RandomStringUtils.getMessageId());
            jsonObject.put("sendid", sendid);
            if (!TextUtils.isEmpty(stype)) {
                jsonObject.put("stype", stype);
            }
            if (-1 != volume && -1 != music) {
                jsonObject.put("volume", volume);
                jsonObject.put("music", music);
            }
            jsonObject.put("cmd", PanelOperatorConstant.CMD.TEST_SIREN);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    public Call<StringResponseEntry> getNotClosedDoorListDataCall(String userId, String deviceToken,
                                                                  String messageId, String task) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", userId);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("messageid", messageId);
            jsonObject.put("task", task);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.TASK_DS_STATUS_OP);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), getGM(map));
    }

    public Call<ResponseBody> getNewAskPlugInfo(String deviceid, String stype, String sendid) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceid);
            jsonObject.put("stype", stype);
            jsonObject.put("sendid", sendid);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (Exception e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        map = getGM(map);

        JSONObject jsonObject1 = new JSONObject(map);
        DDLog.d("Tri", jsonObject1.toString());
        return services.getNewAskPlugInfo(Api.getApi().getUrl(PanelUrls.URL_GET_NEW_ASK_DATA), map);
    }

    /**
     * 获取自定义五键遥控器控制的智能插座配件列表
     */
    public Call<ResponseBody> getCustomizeSmartPlugs(String deviceId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceId);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getCustomizeSmartPlugs(Api.getApi().getUrl(PanelUrls.URL_LIST_CUSTOMIZE_SMART_PLUGS), getGM(map));
    }

    /**
     * @param deviceToken
     * @param userId
     * @param messageId
     * @param pluginId    配件id
     *                    dtype       如果是新插座，则dtype为10；旧插座，则dtype为3
     *                    stype       旧插座时，sendid为空字符串,目前新插座传3E
     * @param sendid      旧插座时，stype为空字符串
     * @return
     */
    public Call<StringResponseEntry> setCustomizeSmartPlugs(String userId, String deviceToken, String messageId,
                                                            String pluginId, String sendid) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("messageid", messageId);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("userid", userId);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.CUSTOMIZE_REMOTE_CONTROL);
            jsonObject.put("pluginid", pluginId);
            jsonObject.put("dtype", TextUtils.isEmpty(sendid) ? 3 : 10);
            jsonObject.put("sendid", TextUtils.isEmpty(sendid) ? "" : sendid);
            jsonObject.put("stype", TextUtils.isEmpty(sendid) ? "" : "3E");
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), getGM(map));
    }

    /**
     * 获取SmartButton的配置信息
     *
     * @param deviceid
     * @param sendid
     * @param stype
     * @return
     */
    public Call<ResponseBody> getSmartButtonConfig(String deviceid, String sendid, String stype) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("deviceid", deviceid);
            jsonObject.put("sendid", sendid);
            jsonObject.put("stype", stype);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getSmartButtonConfig(Api.getApi().getUrl(PanelUrls.URL_GET_SMART_BUTTON_CONF), getGM(map));
    }

    /**
     * 修改或保存SmartButton的控制对象配置信息
     *
     * @param messageId
     * @param deviceToken
     * @param userId
     * @param smartButtonSendId SmartButton的sendid
     * @param smartButtonStype  SmartButton的stype
     * @param actionConfig      SmartButton执行的指令信息
     * @return
     */
    public Call<StringResponseEntry> updateAndSaveSmartButtonConfig(String userId, String deviceToken,
                                                                    String messageId, String smartButtonSendId,
                                                                    String smartButtonStype,
                                                                    ArrayList<JSONObject> actionConfig) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("messageid", messageId);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("userid", userId);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.UPDATE_PLUGIN_CONF);

            jsonObject.put("sendid", smartButtonSendId);
            jsonObject.put("stype", smartButtonStype);

            JSONArray jsonArray = new JSONArray();
            for (JSONObject configJson :
                    actionConfig) {
                if (null != configJson) {
                    jsonArray.put(configJson);
                }
            }
            jsonObject.put("datas", jsonArray);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        DDLog.d("SMARTBUTTON", jsonObject.toString());

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), getGM(map));
    }

    /**
     * 修改配件的block模式
     *
     * @param messageId
     * @param deviceToken
     * @param userId
     * @param pluginId    涂鸦配件ID
     * @param sendid
     * @param stype
     * @param block       // int，配件屏蔽设置，0：不屏蔽；1、屏蔽tamper alarm；2：屏蔽整个配件；3：chime
     * @return
     */
    public Call<StringResponseEntry> getModifyPluginBlockCall(String userId, String deviceToken, String messageId,
                                                              String pluginId, String sendid, String stype, int block) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("messageid", messageId);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("userid", userId);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_PLUGIN_BLOCK);
            jsonObject.put("plugin_id", pluginId);
            jsonObject.put("sendid", sendid);
            jsonObject.put("stype", stype);
            jsonObject.put("block", block);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), getGM(map));
    }

    /**
     * 获取caremode数据
     */
    public Call<ResponseBody> getCareModeData(String deviceid) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("device_id", deviceid);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());

        return services.getCareModeData(Api.getApi().getUrl(PanelUrls.URL_GET_CARE_MODE_DATA), getGM(map));
    }

    /**
     * 修改care mode模式开关
     *
     * @param isOn 是否打开care mode
     */
    public Call<StringResponseEntry> getModifyCareModeCall(String userId, String deviceToken, String messageId, boolean isOn) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("messageid", messageId);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("userid", userId);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_CAREMODE_DATA);
            jsonObject.put("care_mode", isOn);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), getGM(map));
    }

    /**
     * 修改care mode模式开关
     *
     * @param noActionTime 没有响应时间
     */
    public Call<StringResponseEntry> getModifyCareModeNoActionTimeCall(String userId, String deviceToken, String messageId, int noActionTime) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("messageid", messageId);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("userid", userId);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_CAREMODE_DATA);
            jsonObject.put("no_action_time", noActionTime);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), getGM(map));
    }

    /**
     * 修改care mode模式开关
     */
    public Call<StringResponseEntry> getModifyCareModeAlarmTimeCall(String userId, String deviceToken, String messageId, int alarmTime) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("messageid", messageId);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("userid", userId);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_CAREMODE_DATA);
            jsonObject.put("alarm_delay_time", alarmTime);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), getGM(map));
    }

    /**
     * 修改care mode模式开关
     */
    public Call<StringResponseEntry> getModifyCareModePluginCall(String userId, String deviceToken, String messageId, String data) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("messageid", messageId);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("userid", userId);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_CAREMODE_PLUGIN);
            jsonObject.put("datas", data);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), getGM(map));
    }

    /**
     * 取消老人模式倒计时
     */
    public Call<StringResponseEntry> getCancelCareModeNoActionCall(String userId, String deviceToken, String messageId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("messageid", messageId);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("userid", userId);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.CANCEL_NO_ACTION_SOS);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), getGM(map));
    }

    /**
     * 取消老人模式倒计时
     */
    public Call<StringResponseEntry> getCareModeNoActionSosCall(String userId, String deviceToken, String messageId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("messageid", messageId);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("userid", userId);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.NO_ACTION_SOS);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), getGM(map));
    }

    /**
     * 获取设备转态详情页的异常配件信息指令，结果通过websocket返回
     */
    public Call<StringResponseEntry> getHomeExceptionAccessoryInfo(String messageId, String devToken, String userId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("messageid", messageId);
            jsonObject.put("devtoken", devToken);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.GET_HOME_EXCEPTION_ACCESSORY);
            jsonObject.put("userid", userId);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), getGM(map));
    }

    public Call<StringResponseEntry> getRelayControlCall(String userid, String devicetoken,
                                                         String messageid, String action, String sendid) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("devtoken", devicetoken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("action", action);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.RELAY_ACTION);
            jsonObject.put("userid", userid);
            jsonObject.put("sendid", sendid);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), getGM(map));
    }

    /**
     * 获取首页配件具体信息-用于跳转改名页
     * 目前请求时根据是否有id来判断新旧配件，id为空为新配件
     *
     * @param deviceId 设备ID
     * @param category 新旧配件都需要
     * @param sendid   新配件要
     * @param stype    新配件要
     * @param id       旧配件要
     * @return
     */
    public Call<ResponseBody> getHomePluginDetails(String deviceId, int category,
                                                   String stype, String sendid,
                                                   String id) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
            jsonObject.put("device_id", deviceId);
            jsonObject.put("category", category);
            if (TextUtils.isEmpty(id)) {
                // 新配件
                jsonObject.put("stype", stype);
                jsonObject.put("sendid", sendid);
            } else {
                // 旧配件
                jsonObject.put("id", id);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getPluginDetails(Api.getApi().getUrl(PanelUrls.URL_GET_PLUGIN_DETAILS), getGM(map));
    }

    public Call<StringResponseEntry> deletePanel(String deviceId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
            jsonObject.put("deviceid", deviceId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getDeleteDeviceCall(Api.getApi().getUrl(PanelUrls.URL_DELETE_DEVICE), getGM(map));
    }

    public Call<StringResponseEntry> getAddNewASKPluginCall(String userid, String devicetoken,
                                                            String messageid, String plugin) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", userid);
            jsonObject.put("devtoken", devicetoken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("plugin", plugin);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.ADD_NEWASKPLUGIN);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    /**
     * 请求升级主机
     */
    public Call<StringResponseEntry> updatePanelFirmware(String userid, String devicetoken,
                                                         String messageid) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("userid", userid);
            jsonObject.put("devtoken", devicetoken);
            jsonObject.put("messageid", messageid);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SYSTEM_UPDATERESET);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    public Call<StringResponseEntry> setDoorWindowPushStatus(String userid, String deviceToken, String messageid,
                                                             String pluginId, String sendid, String stype, boolean pushStatus) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("messageid", messageid);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_DOOR_WINDOW_PUSH_STATUS);
            jsonObject.put("sendid", sendid);
            jsonObject.put("stype", stype);
            jsonObject.put("plugin_id", pluginId);
            jsonObject.put("push_status", pushStatus);
            jsonObject.put("userid", userid);
            jsonObject.put("home_id", PanelManager.getInstance().getCurrentHomeId());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDeviceCmdCall(Api.getApi().getUrl(Urls.URL_PANEL_CMD), map);
    }

    public Call<GetArmRulesResponse> getArmRules(String homeId, String deviceId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
            jsonObject.put("device_id", deviceId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getArmRules(Api.getApi().getUrl(PanelUrls.URL_GET_ARM_RULES), getGM(map));
    }

    public Call<GetCareModeStatusResponse> getCareModeStatus(String homeId, String deviceId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
            jsonObject.put("device_id", deviceId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getCareModeStatus(Api.getApi().getUrl(PanelUrls.URL_GET_CARE_MODE_STATUS), getGM(map));
    }

    public Call<GetVoicePromptResponse> getVoicePrompt(String homeId, String deviceId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
            jsonObject.put("device_id", deviceId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.getVoicePrompt(Api.getApi().getUrl(PanelUrls.URL_GET_VOICE_PROMPT), getGM(map));
    }

    public Call<ListAccessoriesResponse> listAccessories(String homeId, String deviceId, long addTime, List<String> sTypeList, boolean orderDesc) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            JSONArray stype = new JSONArray();
            if (null != sTypeList && sTypeList.size() > 0) {
                for (String s : sTypeList) {
                    stype.put(s);
                }
            }

            jsonObject.put("home_id", homeId);
            jsonObject.put("device_id", deviceId);
            jsonObject.put("addtime", addTime);
            jsonObject.put("stypes", stype);
            jsonObject.put("order", orderDesc ? ORDER_DESC : ORDER_ASC);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.listAccessories(Api.getApi().getUrl(PanelUrls.URL_LIST_ACCESSORIES), getGM(map));
    }

    public Call<SearchAccessoriesResponse> searchAccessories(String homeId, String deviceId,
                                                             List<AccessoriesInfoParams> accessories,
                                                             long addTime, boolean orderDesc) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            final JSONArray accessoriesList = new JSONArray();
            if (null != accessories && accessories.size() > 0) {
                for (AccessoriesInfoParams accessory : accessories) {
                    final String sType = accessory.getsType();
                    final String sendId = accessory.getSendId();
                    if (!TextUtils.isEmpty(sendId) && !TextUtils.isEmpty(sType)) {
                        JSONObject obj = new JSONObject();
                        obj.put("stype", sType);
                        obj.put("sendid", sendId);
                        accessoriesList.put(obj);
                    }
                }
            }

            jsonObject.put("home_id", homeId);
            jsonObject.put("device_id", deviceId);
            jsonObject.put("add_time", addTime);
            jsonObject.put("accessories", accessoriesList);
            jsonObject.put("order", orderDesc ? ORDER_DESC : ORDER_ASC);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.searchAccessories(Api.getApi().getUrl(PanelUrls.URL_SEARCH_ACCESSORIES), getGM(map));
    }

    public Call<SearchPanelsResponse> searchPanels(String homeId, @NotNull List<String> panelIds) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            JSONArray deviceIdList = new JSONArray();
            if (null != panelIds && panelIds.size() > 0) {
                for (String panelId : panelIds) {
                    deviceIdList.put(panelId);
                }
            }

            jsonObject.put("home_id", homeId);
            jsonObject.put("device_ids", deviceIdList);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.searchPanels(Api.getApi().getUrl(PanelUrls.URL_SEARCH_PANELS), getGM(map));
    }

    public Call<ListPanelTokensResponse> listPanelTokens(String homeID, long addTime, boolean orderDesc, int pageSize) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("addtime", addTime);
            jsonObject.put("order", orderDesc ? "desc" : "asc");
            jsonObject.put("page_size", pageSize <= 0 ? 20 : pageSize);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.listPanelTokens(Api.getApi().getUrl(PanelUrls.URL_LIST_PANEL_TOKENS), map);
    }

    // 临时屏蔽红外触发
    public Call<PirSettingEnabledStatueEntry> setPirSettingEnabledStatue(String deviceToken, String homeId
            , String messageId, String pluginId, String sendId, String sType, String userId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();

        try {
            jsonObject.put("cmd", PanelOperatorConstant.CMD.BYPASS_PLUGIN_5_MIN);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("home_id", homeId);
            jsonObject.put("messageid", messageId);
            jsonObject.put("plugin_id", pluginId);
            jsonObject.put("sendid", sendId);
            jsonObject.put("stype", sType);
            jsonObject.put("userid", userId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.setPirSettingEnabledStatue(Api.getApi().getUrl(PanelUrls.URL_SEND_CMD), getGM(map));
    }

    // 设置红外灵敏度设置模式
    public Call<PirSensitivityResponse> setPirSensitivity(String deviceToken, String homeId
            , String messageId, String pluginId, String sendId, String sType, String userId, int sensitivity) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();

        try {
            jsonObject.put("cmd", PanelOperatorConstant.CMD.SET_SENSITIVITY);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("home_id", homeId);
            jsonObject.put("messageid", messageId);
            jsonObject.put("plugin_id", pluginId);
            jsonObject.put("sendid", sendId);
            jsonObject.put("sensitivity", sensitivity);
            jsonObject.put("stype", sType);
            jsonObject.put("userid", userId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.setPirSensitivity(Api.getApi().getUrl(PanelUrls.URL_SEND_CMD), getGM(map));
    }

    // 退出红外设置模式
    public Call<PirSensitivityEntry> exitPirSettingMode(String deviceToken, String homeId
            , String messageId, String pluginId, String sendId, String sType, String userId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();

        try {
            jsonObject.put("cmd", PanelOperatorConstant.CMD.EXIT_PIR_SETTING_MODE);
            jsonObject.put("devtoken", deviceToken);
            jsonObject.put("home_id", homeId);
            jsonObject.put("messageid", messageId);
            jsonObject.put("plugin_id", pluginId);
            jsonObject.put("sendid", sendId);
            jsonObject.put("stype", sType);
            jsonObject.put("userid", userId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", getGMTime(jsonObject));
        map.put("token", UserManager.getInstance().getToken());
        return services.exitPirSettingMode(Api.getApi().getUrl(PanelUrls.URL_SEND_CMD), getGM(map));
    }
}
