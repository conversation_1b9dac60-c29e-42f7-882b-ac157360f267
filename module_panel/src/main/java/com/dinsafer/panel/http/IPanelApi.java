package com.dinsafer.panel.http;


import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.panel.operate.bean.AppMessageEntry;
import com.dinsafer.panel.operate.bean.CategoryPlugsEntry;
import com.dinsafer.panel.operate.bean.CmsProtocolEntry;
import com.dinsafer.panel.operate.bean.ContactIdResponseEntry;
import com.dinsafer.panel.operate.bean.DoorBell;
import com.dinsafer.panel.operate.bean.EntryDelayModel;
import com.dinsafer.panel.operate.bean.EventListEntry;
import com.dinsafer.panel.operate.bean.EventListSettingEntry;
import com.dinsafer.panel.operate.bean.FourGInfoEntry;
import com.dinsafer.panel.operate.bean.GetAdvancedSettingResult;
import com.dinsafer.panel.operate.bean.GetArmRulesResponse;
import com.dinsafer.panel.operate.bean.GetCareModeStatusResponse;
import com.dinsafer.panel.operate.bean.GetPlaySoundSettingResult;
import com.dinsafer.panel.operate.bean.GetVoicePromptResponse;
import com.dinsafer.panel.operate.bean.HomeArmStatueEntry;
import com.dinsafer.panel.operate.bean.HomeDeviceInfoEntry;
import com.dinsafer.panel.operate.bean.HomePluginQuantityEntry;
import com.dinsafer.panel.operate.bean.ListAccessoriesResponse;
import com.dinsafer.panel.operate.bean.ListPanelTokensResponse;
import com.dinsafer.panel.operate.bean.PirSensitivityEntry;
import com.dinsafer.panel.operate.bean.PirSensitivityResponse;
import com.dinsafer.panel.operate.bean.PirSettingEnabledStatueEntry;
import com.dinsafer.panel.operate.bean.ReadyToArmSwitchStatusEntry;
import com.dinsafer.panel.operate.bean.SearchAccessoriesResponse;
import com.dinsafer.panel.operate.bean.SearchPanelsResponse;
import com.dinsafer.panel.operate.bean.SimDataEntry;
import com.dinsafer.panel.operate.bean.SosMessageEntry;
import com.dinsafer.panel.operate.bean.SosStatusEntry;
import com.dinsafer.panel.operate.bean.TimePickerEntry;
import com.dinsafer.panel.operate.bean.TimeZoneResponseEntry;

import java.util.Map;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;
import retrofit2.http.Url;

/**
 * 主机相关网络请求接口
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/12 2:56 PM
 */
interface IPanelApi {
    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> getDeviceCmdCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<HomeDeviceInfoEntry> getDeviceInfo(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<HomePluginQuantityEntry> getPluginQuantityInfo(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<ResponseBody> getPluginInfo(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<ResponseBody> getSmartButtonListData(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<ResponseBody> getSpecifyPlugin(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<ResponseBody> getRelayListData(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<ResponseBody> getCategoryPlugsCallV4(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<ResponseBody> getDoorSensorListData(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<HomeArmStatueEntry> getHomeArmStatusCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<EntryDelayModel> getEntryDelayCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<AppMessageEntry> getDeviceTextCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<SosMessageEntry> getSOSMessageCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<ContactIdResponseEntry> getContactId(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<CmsProtocolEntry> getCmsData(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<ReadyToArmSwitchStatusEntry> getReadyToArmSwitchStatus(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<TimePickerEntry> getExitDelayTimeCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<TimePickerEntry> getSirenTimeCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<GetAdvancedSettingResult> getAdvancedSetting(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<GetPlaySoundSettingResult> getPlaySoundSetting(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<TimeZoneResponseEntry> getTimeZoneSettingCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<FourGInfoEntry> get4GInfo(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<SimDataEntry> getSimData(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> getDeleteDeviceCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> getChangeDeviceNameCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<EventListEntry> getEventList(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<SosStatusEntry> getSosStatusCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<CategoryPlugsEntry> getCategoryPlugsCallV3(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<EventListSettingEntry> getEventListSetting(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<ResponseBody> getListDoorBellCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> getModifyDoorBellCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> getAddDoorBellCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> getDeleteDoorBellCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<DoorBell> getDoorBellCapCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> getDeleteDoorBellCapCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<ResponseBody> getNewAskPlugInfo(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<ResponseBody> getCustomizeSmartPlugs(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<ResponseBody> getSmartButtonConfig(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<ResponseBody> getCareModeData(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<ResponseBody> getPluginDetails(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<GetArmRulesResponse> getArmRules(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<GetCareModeStatusResponse> getCareModeStatus(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<GetVoicePromptResponse> getVoicePrompt(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<ListAccessoriesResponse> listAccessories(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<SearchAccessoriesResponse> searchAccessories(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<SearchPanelsResponse> searchPanels(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<ListPanelTokensResponse> listPanelTokens(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<PirSettingEnabledStatueEntry> setPirSettingEnabledStatue(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<PirSensitivityResponse> setPirSensitivity(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<PirSensitivityEntry> exitPirSettingMode(@Url String url, @FieldMap Map<String, Object> map);

}
