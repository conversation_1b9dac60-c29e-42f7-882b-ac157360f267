package com.dinsafer.panel.bean.device;

import androidx.annotation.Keep;
import android.text.TextUtils;

import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.common.NetKeyConstants;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelResultBuilder;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.panel.operate.PanelOperator;
import com.dinsafer.panel.operate.PanelOperatorConstant;
import com.dinsafer.panel.operate.bean.event.DeviceResultEvent;
import com.dinsafer.panel.operate.callback.PanelCallback;

import org.json.JSONObject;

import java.util.Map;

/**
 * SmartPlug
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/14 4:24 PM
 */
@Keep
public class SmartPlugDevice extends BasePluginDevice<SmartPlugDevice> {
    public SmartPlugDevice(String id, int category, String subCategory, String fatherId) {
        super(id, category, subCategory, null, fatherId);
    }

    public SmartPlugDevice(String id, String fatherId) {
        // TODO 新Ask SmartPlug category/subcategory初始化
        super(id, 0, null, null, fatherId);
    }

    @Override
    public void submit(Map arg) {
        DDLog.i(TAG, "submit param: " + arg);
        if (null == arg || 0 >= arg.size()) {
            throw new IllegalArgumentException("submit param can not be null");
        }
        try {
            String cmd = (String) arg.get(PanelDataKey.CMD);
            if (TextUtils.isEmpty(cmd)) {
                DDLog.e(TAG, "Empty cmd");
                return;
            }
            if (!isCanOperate()) {
                if (submitOnDeviceDelete(cmd, arg)) {
                    return;
                }

                DDLog.e(TAG, this + "是已经被删除的状态，不支持CMD: " + cmd);
                dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                        + ErrorCode.DEVICE_NOT_BELONE_USER + ", errorMsg: " + "主机是已经被删除的状态，不支持CMD: " + cmd).createResult());
                return;
            }
            switch (cmd) {
                case PluginCmd.PLUGIN_DELETE:
                    if (null == DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA)) {
                        deletePlugin(arg);
                    } else {
                        deleteAskPlugin(arg);
                    }
                    break;
                case PluginCmd.PLUGIN_SETNAME:
                    if (null == DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA)) {
                        modifyPlugin(arg);
                    } else {
                        modifyAskPlugin(arg);
                    }
                    break;
                case PluginCmd.PLUG_CHANGE_ON:
                    boolean on = (boolean) arg.get(PanelDataKey.SmartPlug.ON);
                    JSONObject askData = DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA);
                    boolean isAsk = false;
                    String sendId = null, sType = null;
                    if (null != askData) {
                        isAsk = true;
                        sendId = DDJSONUtil.getString(askData, NetKeyConstants.NET_KEY_SEND_ID);
                        sType = DDJSONUtil.getString(askData, NetKeyConstants.NET_KEY_S_TYPE);
                    }
                    if (DeviceHelper.getBoolean(getInfo(), PanelDataKey.SmartPlug.IS_ASK_SMART_PLUG, false)) {
                        // 首页配件
                        isAsk = true;
                        sendId = DeviceHelper.getString(getInfo(), PanelDataKey.SEND_ID, "");
                        sType = DeviceHelper.getString(getInfo(), PanelDataKey.S_TYPE, "");
                    }
                    PanelManager.getInstance().getNetworkRequestManager().requestSmartPlugsStatusChange(
                            getId(), PanelManager.getInstance().getCurrentPanelToken(), on ? 1 : 0,
                            isAsk, sendId, sType, new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PluginCmd.GET_PLUGIN_DETAIL:
                    getPluginDetailInfo(cmd, arg);
                    break;
                default:
                    DDLog.e(TAG, "Not support cmd: " + cmd);
                    break;
            }
        } catch (Exception e) {
            DDLog.i(TAG, "Error on submit");
            e.printStackTrace();
        }
    }

    @Override
    protected void onDeviceResultEvent(DeviceResultEvent deviceResultEvent) {
        try {
            final String operateCmd = deviceResultEvent.getCmdType();
            final String cmd = PanelOperator.transformFromPanelOperatorCmd(operateCmd);
            final String messageId = deviceResultEvent.getMessageId();

            switch (operateCmd) {
                case PanelOperatorConstant.CMD.SET_SMART_PLUG_ENABLE:
                case PanelOperatorConstant.CMD.SET_NEW_SMART_PLUG_ON:
                    JSONObject jsonObject = new JSONObject(deviceResultEvent.getReslut());
                    String pluginId = DDJSONUtil.getString(jsonObject, "pluginid");
                    if (!getId().equals(pluginId)) {
                        return;
                    }
                    DDLog.i(TAG, "deviceResultEvent: " + deviceResultEvent.toString());
                    boolean isSelf = mMessageIdMap.containsKey(messageId);
                    mMessageIdMap.remove(messageId);
                    dispatchDeviceResult(cmd, deviceResultEvent.getStatus(), isSelf, 1, deviceResultEvent.getReslut());
                    break;
                default:
                    if (mMessageIdMap.containsKey(messageId)) {
                        // 如果是自己操作的，直接返回
                        dispatchDeviceResult(cmd, deviceResultEvent.getStatus(), true, 1, deviceResultEvent.getReslut());
                        mMessageIdMap.remove(messageId);
                    }
                    break;
            }
        } catch (Exception e) {
            DDLog.e(TAG, "Error onDeviceResultEvent");
            e.printStackTrace();
        }
    }
}
