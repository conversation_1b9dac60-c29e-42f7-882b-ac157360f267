package com.dinsafer.panel.bean.device;

import androidx.annotation.Keep;
import android.text.TextUtils;

import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.utils.RandomStringUtils;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelResultBuilder;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.panel.operate.callback.PanelCallback;

import java.util.HashMap;
import java.util.Map;

/**
 * 不确定具体类型的配件数据
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/17 6:14 PM
 */
@Keep
public class OtherDevice extends BasePluginDevice {
    private static final String TAG = OtherDevice.class.getSimpleName();
    protected final Map<String, String> mMessageIdMap = new HashMap<>();

    public OtherDevice(String id, int category, String subCategory, String fatherId) {
        // TODO 初始化category subcategory
        super(id, category, subCategory, null, fatherId);
    }

    @Override
    public void submit(Map arg) {
        DDLog.i(TAG, "submit param: " + arg);
        if (null == arg || 0 >= arg.size()) {
            throw new IllegalArgumentException("submit param can not be null");
        }
        final String messageId;
        try {
            String cmd = (String) arg.get(PanelDataKey.CMD);
            if (TextUtils.isEmpty(cmd)) {
                DDLog.e(TAG, "Empty cmd");
                return;
            }
            if (!isCanOperate()) {
                if (submitOnDeviceDelete(cmd, arg)) {
                    return;
                }

                DDLog.e(TAG, this + "是已经被删除的状态，不支持CMD: " + cmd);
                dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                        + ErrorCode.DEVICE_NOT_BELONE_USER + ", errorMsg: " + "主机是已经被删除的状态，不支持CMD: " + cmd).createResult());
                return;
            }
            switch (cmd) {
                case PluginCmd.PLUGIN_DELETE:
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    PanelManager.getInstance().getNetworkRequestManager().requestDeleteOtherPlugin(
                            PanelManager.getInstance().getCurrentPanelToken(), messageId, getId(),
                            new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PluginCmd.PLUGIN_SETNAME:
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    String newName = (String) arg.get(PanelDataKey.NAME);
                    PanelManager.getInstance().getNetworkRequestManager().requestChangeOtherPlugName(
                            PanelManager.getInstance().getCurrentPanelToken(), messageId, getId(), newName,
                            new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                default:
                    DDLog.e(TAG, "Not support cmd: " + cmd);
                    break;
            }
        } catch (Exception e) {
            DDLog.i(TAG, "Error on submit");
            e.printStackTrace();
        }
    }
}
