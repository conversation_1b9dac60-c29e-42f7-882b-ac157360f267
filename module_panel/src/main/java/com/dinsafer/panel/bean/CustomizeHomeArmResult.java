package com.dinsafer.panel.bean;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Customize Home Arm 配件列表数据
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/17 6:16 PM
 */
public class CustomizeHomeArmResult extends SimplePluginResult2<Map<String, Object>> {
    private static final String KEY_THIRD_PART_PLUGIN = "third_path_plugin";
    private static final String KEY_OFFICIAL_PLUGIN = "official_plugin";
    private static final String KEY_ASK_PLUGIN = "ask_plugin";
    private int time;
    private List<Integer> options;
    private boolean entrydelaysound;

    public CustomizeHomeArmResult() {
        pluginMap.put(KEY_THIRD_PART_PLUGIN, new ArrayList<>());
        pluginMap.put(KEY_OFFICIAL_PLUGIN, new ArrayList<>());
        pluginMap.put(KEY_ASK_PLUGIN, new ArrayList<>());
    }

    /**
     * 获取第三方配件列表
     */
    public ArrayList<Map<String, Object>> getThirdPartPlugin() {
        return pluginMap.get(KEY_THIRD_PART_PLUGIN);
    }

    /**
     * 获取官方配件列表
     */
    public ArrayList<Map<String, Object>> getOfficialPlugin() {
        return pluginMap.get(KEY_OFFICIAL_PLUGIN);
    }

    /**
     * 获取ASK配件列表
     */
    public ArrayList<Map<String, Object>> getAskPlugin() {
        return pluginMap.get(KEY_ASK_PLUGIN);
    }

    public int getTime() {
        return time;
    }

    public void setTime(int time) {
        this.time = time;
    }

    public List<Integer> getOptions() {
        return options;
    }

    public void setOptions(List<Integer> options) {
        this.options = options;
    }

    public boolean isEntrydelaysound() {
        return entrydelaysound;
    }

    public void setEntrydelaysound(boolean entrydelaysound) {
        this.entrydelaysound = entrydelaysound;
    }
}
