package com.dinsafer.panel.bean.device;

import androidx.annotation.Keep;

import android.text.TextUtils;

import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.common.NetKeyConstants;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dincore.utils.RandomStringUtils;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelResultBuilder;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.panel.operate.PanelOperator;
import com.dinsafer.panel.operate.PanelOperatorConstant;
import com.dinsafer.panel.operate.bean.event.DeviceResultEvent;
import com.dinsafer.panel.operate.callback.PanelCallback;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Map;

/**
 * DoorSensor数据
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/17 3:29 PM
 */
@Keep
public class DoorSensorDevice extends BasePluginDevice<DoorSensorDevice> {
    public DoorSensorDevice(String id, int category, String subCategory, String fatherId) {
        // TODO 初始化category
        super(id, category, subCategory, null, fatherId);
    }

    @Override
    public void submit(Map arg) {
        DDLog.i(TAG, "submit param: " + arg);
        if (null == arg || 0 >= arg.size()) {
            throw new IllegalArgumentException("submit param can not be null");
        }
        try {
            String cmd = (String) arg.get(PanelDataKey.CMD);
            if (TextUtils.isEmpty(cmd)) {
                DDLog.e(TAG, "Empty cmd");
                return;
            }
            if (!isCanOperate()) {
                if (submitOnDeviceDelete(cmd, arg)) {
                    return;
                }

                DDLog.e(TAG, this + "是已经被删除的状态，不支持CMD: " + cmd);
                dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                        + ErrorCode.DEVICE_NOT_BELONE_USER + ", errorMsg: " + "主机是已经被删除的状态，不支持CMD: " + cmd).createResult());
                return;
            }
            switch (cmd) {
                case PluginCmd.PLUGIN_DELETE:
                    if (null == DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA)) {
                        deletePlugin(arg);
                    } else {
                        deleteAskPlugin(arg);
                    }
                    break;
                case PluginCmd.PLUGIN_SETNAME:
                    if (null == DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA)) {
                        modifyPlugin(arg);
                    } else {
                        modifyAskPlugin(arg);
                    }
                    break;
                case PluginCmd.PLUGIN_CONFIG_BLOCK:
                    configPluginBlock(cmd, arg);
                    break;
                case PluginCmd.UPDATE_PLUGIN_CONFIG:
                    updateSmartButtonConfig(cmd, arg);
                    break;
                case PluginCmd.TEST_SIREN:
                    testScrien(cmd, arg);
                    break;
                case PluginCmd.GET_TARGET_LIST:
                    getPluginTargetList(cmd, arg);
                    break;
                case PluginCmd.GET_PLUGIN_DETAIL:
                    getPluginDetailInfo(cmd, arg);
                    break;
                case PluginCmd.SET_DOOR_WINDOW_PUSH_STATUS:
                    setDoorWindowPushStatus(cmd, arg);
                    break;
                default:
                    DDLog.e(TAG, "Not support cmd: " + cmd);
                    break;
            }
        } catch (Exception e) {
            DDLog.i(TAG, "Error on submit");
            e.printStackTrace();
        }
    }

    private void setDoorWindowPushStatus(String cmd, Map<String, Object> arg) {
        DDLog.i(TAG, "setDoorWindowPushStatus");
        JSONObject askData = DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA);
        String sendId = DDJSONUtil.getString(askData, NetKeyConstants.NET_KEY_SEND_ID);
        String sType = DDJSONUtil.getString(askData, NetKeyConstants.NET_KEY_S_TYPE);
        boolean opened = DeviceHelper.getBoolean(arg, PanelDataKey.DoorSensor.PUSH_STATUS, false);
        messageId = RandomStringUtils.getMessageId();

        PanelManager.getInstance().getNetworkRequestManager().requestSetDoorWindowPushStatus(
                PanelManager.getInstance().getCurrentPanelToken(), messageId, getId(), sendId, sType, opened,
                new PanelCallback.NetworkResult<String>() {
                    @Override
                    public void onSuccess(String result) {
                    }

                    @Override
                    public void onError(int errorCode, String errorMsg) {
                        dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                + errorCode + ", errorMsg: " + errorMsg).createResult());
                    }
                }
        );
    }

    @Override
    protected void onDeviceResultEvent(DeviceResultEvent deviceResultEvent) {
        final String operatorCmd = deviceResultEvent.getCmdType();
        final String cmd = PanelOperator.transformFromPanelOperatorCmd(operatorCmd);
        DDLog.i(TAG, "onEvent, DeviceResultEvent: " + deviceResultEvent + ", operatorCmd: " + operatorCmd);

        switch (operatorCmd) {
            case PanelOperatorConstant.CMD.SET_DOOR_WINDOW_PUSH_STATUS:
                try {
                    JSONObject jsonObject = new JSONObject(deviceResultEvent.getReslut());
                    String pluginId = jsonObject.getString("plugin_id");
                    if (!getId().equals(pluginId)) {
                        return;
                    }
                    jsonObject.put(PanelDataKey.CmdResult.OPERATION_CMD, operatorCmd);
                    boolean push_status = jsonObject.getBoolean("push_status");
                    if (null != getInfo()) {
                        getInfo().put(PanelDataKey.DoorSensor.PUSH_STATUS, push_status);
                    }
                    DDLog.i(TAG, "update sire data: " + deviceResultEvent.toString());
                    dispatchDeviceResult(cmd, deviceResultEvent.getStatus(), false, 1, jsonObject.toString());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                break;
            default:
                if (mMessageIdMap.containsKey(messageId)) {
                    // 如果是自己操作的，直接返回
                    dispatchDeviceResult(cmd, deviceResultEvent.getStatus(), true, 1, deviceResultEvent.getReslut());
                    mMessageIdMap.remove(messageId);
                }
                break;
        }
    }
}
