package com.dinsafer.panel.bean;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

/**
 * 包含轮询sType配件列表结果-Map
 * <p>
 * 如果没有指定类型的配件，返回一个长度为0的列表
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/17 4:37 PM
 */
public class SimplePluginResult2<T> {
    protected final Map<String, ArrayList<T>> pluginMap; // 指定类型的配件列表
    protected final Set<String> sTypeSet; // 需要轮询配件状态的sType Set

    public SimplePluginResult2() {
        pluginMap = new LinkedHashMap<>();
        sTypeSet = new HashSet<>();
    }

    public Map<String, ArrayList<T>> getPluginMap() {
        return pluginMap;
    }

    public Set<String> getsTypeSet() {
        return sTypeSet;
    }
}
