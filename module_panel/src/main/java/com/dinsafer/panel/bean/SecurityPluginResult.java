package com.dinsafer.panel.bean;

import com.dinsafer.panel.bean.device.SecurityDevice;
import com.dinsafer.panel.common.PanelConstant;

import java.util.ArrayList;

/**
 * 获取安防配件数据结果
 * <p>
 * 有序包含红外、普通门磁、水感、震动门磁、烟感、紧急按钮
 * 如果没有指定类型的配件，返回一个长度为0的列表
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/17 2:38 PM
 */
public class SecurityPluginResult extends SimplePluginResult2<SecurityDevice> {

    public SecurityPluginResult() {
        // 初始相关的化配件列表
        pluginMap.put(PanelConstant.Plugin.OLD_PIR_SENSOR_09, new ArrayList<>());
        pluginMap.put(PanelConstant.Plugin.OLD_DOOR_WINDOW_SENSOR_0B, new ArrayList<>());
        pluginMap.put(PanelConstant.Plugin.OLD_LIQUID_SENSOR_0E, new ArrayList<>());
        pluginMap.put(PanelConstant.Plugin.OLD_VIBRATION_SENSOR_06, new ArrayList<>());
        pluginMap.put(PanelConstant.Plugin.OLD_SMOKE_SENSOR_05, new ArrayList<>());
        pluginMap.put(PanelConstant.Plugin.OLD_PANIC_BUTTON_07, new ArrayList<>());
        pluginMap.put(PanelConstant.Plugin.WIRED_BRIDGE, new ArrayList<>());
    }

    public ArrayList<SecurityDevice> getPirSensors() {
        return pluginMap.get(PanelConstant.Plugin.OLD_PIR_SENSOR_09);
    }

    public ArrayList<SecurityDevice> getDoorWindowSensors() {
        return pluginMap.get(PanelConstant.Plugin.OLD_DOOR_WINDOW_SENSOR_0B);
    }

    public ArrayList<SecurityDevice> getLiquidSensors() {
        return pluginMap.get(PanelConstant.Plugin.OLD_LIQUID_SENSOR_0E);
    }

    public ArrayList<SecurityDevice> getVibrationSensors() {
        return pluginMap.get(PanelConstant.Plugin.OLD_VIBRATION_SENSOR_06);
    }

    public ArrayList<SecurityDevice> getSmokeSensors() {
        return pluginMap.get(PanelConstant.Plugin.OLD_SMOKE_SENSOR_05);
    }

    public ArrayList<SecurityDevice> getPanicButtons() {
        return pluginMap.get(PanelConstant.Plugin.OLD_PANIC_BUTTON_07);
    }

    public ArrayList<SecurityDevice> getWiredBridges() {
        return pluginMap.get(PanelConstant.Plugin.WIRED_BRIDGE);
    }
}
