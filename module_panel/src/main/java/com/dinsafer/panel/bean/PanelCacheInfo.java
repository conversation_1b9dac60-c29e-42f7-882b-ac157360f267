package com.dinsafer.panel.bean;

import androidx.annotation.Keep;
import androidx.annotation.Nullable;

import android.text.TextUtils;

import com.dinsafer.dincore.db.cache.ICacheInfo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/11/29 14:55
 */
@Keep
public class PanelCacheInfo implements ICacheInfo {

    private static final long serialVersionUID = 60071196409430316L;
    private String panelId;
    private long updateTime;
    private final List<PluginCache> pluginList;

    public PanelCacheInfo() {
        pluginList = new ArrayList<>();
    }

    public PanelCacheInfo(String panelId) {
        this.panelId = panelId;
        this.pluginList = new ArrayList<>();
    }

    public PanelCacheInfo(PanelCacheInfo cacheInfo) {
        this.pluginList = new ArrayList<>();
        this.updateFrom(cacheInfo);
    }

    public boolean addPlugin(final String id, final String sType, final String sendId, final long addTime, final String name) {
        return addPlugin(new AskPluginCache(id, sType, sendId, addTime, name));
    }

    public boolean addPlugin(final AskPluginCache askPlugin) {
        if (null == askPlugin || TextUtils.isEmpty(askPlugin.getId())
                || TextUtils.isEmpty(askPlugin.getSendId()) || TextUtils.isEmpty(askPlugin.getsType())
                || pluginList.contains(askPlugin)) {
            return false;
        }
        pluginList.add(askPlugin);
        return true;
    }

    public boolean containId(final String pluginId) {
        if (TextUtils.isEmpty(pluginId)) {
            return false;
        }

        for (PluginCache pluginCache : pluginList) {
            if (pluginId.equals(pluginCache.getId())) {
                return true;
            }
        }
        return false;
    }

    public boolean containPlugin(final String id, final String sType, final String sendId) {
        return containPlugin(new AskPluginCache(id, sType, sendId, 0));
    }

    public boolean containPlugin(final AskPluginCache plugin) {
        return null != plugin && pluginList.contains(plugin);
    }

    public boolean removePlugin(final String id, final String sType, final String sendId) {
        return removePlugin(new AskPluginCache(id, sType, sendId, 0));
    }

    public boolean removePlugin(final AskPluginCache askPlugin) {
        if (null == askPlugin) {
            return false;
        }
        return pluginList.remove(askPlugin);
    }

    public String getPanelId() {
        return panelId;
    }

    public void setPanelId(String panelId) {
        this.panelId = panelId;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

    public List<PluginCache> getPluginList() {
        return pluginList;
    }

    public void updateFrom(@Nullable PanelCacheInfo src) {
        pluginList.clear();
        if (null == src) {
            panelId = null;
            updateTime = 0;
            return;
        }

        updateTime = src.updateTime;
        panelId = src.panelId;
        if (null != src.pluginList && src.pluginList.size() > 0) {
            this.pluginList.addAll(src.pluginList);
        }

        if (null == this.pluginList || pluginList.size() == 0) {
            updateTime = 1;
        }
    }

    public boolean isNeedSaveCache() {
        return true;
    }

    @Override
    public String toString() {
        return "PanelCacheInfo{" +
                "panelId='" + panelId + '\'' +
                ", updateTime=" + updateTime +
                ", pluginList=" + pluginList +
                '}';
    }

    @Keep
    public static class AskPluginCache implements PluginCache, Serializable {
        private static final long serialVersionUID = 3080213317760900008L;
        private String id;
        private String sType;
        private String sendId;
        private long addTime;
        private String name;

        public AskPluginCache() {
        }

        public AskPluginCache(String id, String sType, String sendId, long addTime) {
            this.id = id;
            this.sType = sType;
            this.sendId = sendId;
            this.addTime = addTime;
        }

        public AskPluginCache(String id, String sType, String sendId, long addTime, String name) {
            this.id = id;
            this.sType = sType;
            this.sendId = sendId;
            this.addTime = addTime;
            this.name = name;
        }

        @Override
        public String getId() {
            return id;
        }

        @Override
        public void setId(String id) {
            this.id = id;
        }

        public String getsType() {
            return sType;
        }

        public void setsType(String sType) {
            this.sType = sType;
        }

        public String getSendId() {
            return sendId;
        }

        public void setSendId(String sendId) {
            this.sendId = sendId;
        }

        @Override
        public long getAddTime() {
            return addTime;
        }

        @Override
        public void setAddTime(long addTime) {
            this.addTime = addTime;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            AskPluginCache that = (AskPluginCache) o;
            return Objects.equals(id, that.id) && Objects.equals(sType, that.sType) && Objects.equals(sendId, that.sendId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(id, sType, sendId);
        }

        @Override
        public String toString() {
            return "AskPluginCache{" +
                    "id='" + id + '\'' +
                    ", sType='" + sType + '\'' +
                    ", sendId='" + sendId + '\'' +
                    ", addTime=" + addTime +
                    ", name='" + name + '\'' +
                    '}';
        }

        @Override
        public boolean isAskPlugin() {
            return true;
        }
    }

    /**
     * 配件缓存信息接口
     */
    @Keep
    public interface PluginCache {

        void setId(String id);

        String getId();

        void setAddTime(long addTime);

        long getAddTime();

        boolean isAskPlugin();
    }
}
