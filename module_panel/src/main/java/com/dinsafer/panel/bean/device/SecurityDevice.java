package com.dinsafer.panel.bean.device;

import android.text.TextUtils;

import androidx.annotation.Keep;

import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelResultBuilder;
import com.dinsafer.panel.common.PluginCmd;

import java.util.Map;

/**
 * 安防配件信息
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/17 11:07 AM
 */
@Keep
public class SecurityDevice extends BasePluginDevice {

    public SecurityDevice(String id, int category, String subCategory, String fatherId) {
        // TODO 初始化category
        super(id, category, subCategory, null, fatherId);
    }

    @Override
    public void submit(Map arg) {
        DDLog.i(TAG, "submit param: " + arg);
        if (null == arg || 0 >= arg.size()) {
            throw new IllegalArgumentException("submit param can not be null");
        }
        try {
            String cmd = (String) arg.get(PanelDataKey.CMD);
            if (TextUtils.isEmpty(cmd)) {
                DDLog.e(TAG, "Empty cmd");
                return;
            }
            if (!isCanOperate()) {
                if (submitOnDeviceDelete(cmd, arg)) {
                    return;
                }

                DDLog.e(TAG, this + "是已经被删除的状态，不支持CMD: " + cmd);
                dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                        + ErrorCode.DEVICE_NOT_BELONE_USER + ", errorMsg: " + "主机是已经被删除的状态，不支持CMD: " + cmd).createResult());
                return;
            }
            switch (cmd) {
                case PluginCmd.PLUGIN_DELETE:
                    if (null == DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA)) {
                        deletePlugin(arg);
                    } else {
                        deleteAskPlugin(arg);
                    }
                    break;
                case PluginCmd.PLUGIN_SETNAME:
                    if (null == DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA)) {
                        modifyPlugin(arg);
                    } else {
                        modifyAskPlugin(arg);
                    }
                    break;
                case PluginCmd.PLUGIN_CONFIG_BLOCK:
                    configPluginBlock(cmd, arg);
                    break;
                case PluginCmd.UPDATE_PLUGIN_CONFIG:
                    updateSmartButtonConfig(cmd, arg);
                    break;
                case PluginCmd.TEST_SIREN:
                    testScrien(cmd, arg);
                    break;
                case PluginCmd.GET_TARGET_LIST:
                    getPluginTargetList(cmd, arg);
                    break;
                case PluginCmd.BYPASS_PLUGIN_5_MIN:
                    setPirSettingEnabledStatue(cmd, arg);
                    break;
                case PluginCmd.SET_SENSITIVITY:
                    setPirSensitivity(cmd, arg);
                    break;
                case PluginCmd.EXIT_PIR_SETTING_MODE:
                    exitPirSettingMode(cmd, arg);
                    break;
                default:
                    DDLog.e(TAG, "Not support cmd: " + cmd);
                    break;
            }
        } catch (Exception e) {
            DDLog.i(TAG, "Error on submit");
            e.printStackTrace();
        }
    }
}
