package com.dinsafer.panel.bean.device;

import androidx.annotation.Keep;
import android.text.TextUtils;

import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.common.NetKeyConstants;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dincore.utils.RandomStringUtils;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelResultBuilder;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.panel.operate.callback.PanelCallback;

import java.util.Map;

/**
 * 继电器/Relay
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/14 4:24 PM
 */
@Keep
public class RelayDevice extends BasePluginDevice {

    public RelayDevice(String id, String fatherId) {
        // TODO category/subcategory初始化
        super(id, 0, null, null, fatherId);
    }

    @Override
    public void submit(Map arg) {
        DDLog.i(TAG, "submit param: " + arg);
        if (null == arg || 0 >= arg.size()) {
            throw new IllegalArgumentException("submit param can not be null");
        }
        try {
            String cmd = (String) arg.get(PanelDataKey.CMD);
            if (TextUtils.isEmpty(cmd)) {
                DDLog.e(TAG, "Empty cmd");
                return;
            }
            if (!isCanOperate()) {
                if (submitOnDeviceDelete(cmd, arg)) {
                    return;
                }

                DDLog.e(TAG, this + "是已经被删除的状态，不支持CMD: " + cmd);
                dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                        + ErrorCode.DEVICE_NOT_BELONE_USER + ", errorMsg: " + "主机是已经被删除的状态，不支持CMD: " + cmd).createResult());
                return;
            }
            switch (cmd) {
                case PluginCmd.PLUGIN_DELETE:
                    if (null == DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA)) {
                        deletePlugin(arg);
                    } else {
                        deleteAskPlugin(arg);
                    }
                    break;
                case PluginCmd.PLUGIN_SETNAME:
                    if (null == DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA)) {
                        modifyPlugin(arg);
                    } else {
                        modifyAskPlugin(arg);
                    }
                    break;
                case PluginCmd.CONTROL_RELY_ACTION:
                    String action = (String) arg.get(PanelDataKey.Relay.ACTION);
                    String sendId = DDJSONUtil.getString(DeviceHelper.getJsonObject(getInfo(),
                            PanelDataKey.ASK_DATA), NetKeyConstants.NET_KEY_SEND_ID);
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    PanelManager.getInstance().getNetworkRequestManager().requestControlRelay(
                            PanelManager.getInstance().getCurrentPanelToken(), messageId,
                            action, sendId, new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                default:
                    DDLog.e(TAG, "Not support cmd: " + cmd);
                    break;
            }
        } catch (Exception e) {
            DDLog.i(TAG, "Error on submit");
            e.printStackTrace();
        }
    }
}
