package com.dinsafer.panel.bean.device;

import androidx.annotation.Keep;
import android.text.TextUtils;

import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.common.NetKeyConstants;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelResultBuilder;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.panel.operate.callback.PanelCallback;

import org.json.JSONObject;

import java.util.Map;

/**
 * SmartButton
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/14 3:16 PM
 */
@Keep
public class SmartButtonDevice extends BasePluginDevice {
    public SmartButtonDevice(String id, int category, String subCategory,
                             Map<String, Object> info, String fatherId) {
        super(id, category, subCategory, info, fatherId);
    }

    public SmartButtonDevice(String id, String fatherId, String subcategory) {
        // TODO category/subCategory
        super(id, 0, subcategory, null, fatherId);
    }

    @Override
    public void submit(Map arg) {
        DDLog.i(TAG, "submit param: " + arg);
        if (null == arg || 0 >= arg.size()) {
            throw new IllegalArgumentException("submit param can not be null");
        }
        try {
            String cmd = (String) arg.get(PanelDataKey.CMD);
            if (TextUtils.isEmpty(cmd)) {
                DDLog.e(TAG, "Empty cmd");
                return;
            }
            if (!isCanOperate()) {
                if (submitOnDeviceDelete(cmd, arg)) {
                    return;
                }

                DDLog.e(TAG, this + "是已经被删除的状态，不支持CMD: " + cmd);
                dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                        + ErrorCode.DEVICE_NOT_BELONE_USER + ", errorMsg: " + "主机是已经被删除的状态，不支持CMD: " + cmd).createResult());
                return;
            }
            switch (cmd) {
                case PluginCmd.PLUGIN_DELETE:
                    if (null == DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA)) {
                        deletePlugin(arg);
                    } else {
                        deleteAskPlugin(arg);
                    }
                    break;
                case PluginCmd.PLUGIN_SETNAME:
                    if (null == DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA)) {
                        modifyPlugin(arg);
                    } else {
                        modifyAskPlugin(arg);
                    }
                    break;
                case PluginCmd.GET_SMART_BUTTON_CONFIG:
                    JSONObject askData = DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA);
                    String sendId = DDJSONUtil.getString(askData, NetKeyConstants.NET_KEY_SEND_ID);
                    String sType = DDJSONUtil.getString(askData, NetKeyConstants.NET_KEY_S_TYPE);

                    PanelManager.getInstance().getNetworkRequestManager().requestSmartButtonConfig(getFatherId(),
                            PanelManager.getInstance().getCurrentPanelToken(), sendId, sType,
                            new PanelCallback.NetworkResult<String>() {
                                @Override
                                public void onSuccess(String result) {
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, result).createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PluginCmd.UPDATE_PLUGIN_CONFIG:
                    updateSmartButtonConfig(cmd, arg);
                    break;
                case PluginCmd.TEST_SIREN:
                    testScrien(cmd, arg);
                    break;
                case PluginCmd.GET_TARGET_LIST:
                    getPluginTargetList(cmd, arg);
                    break;
                default:
                    DDLog.e(TAG, "Not support cmd: " + cmd);
                    break;
            }
        } catch (Exception e) {
            DDLog.i(TAG, "Error on submit");
            e.printStackTrace();
        }
    }
}
