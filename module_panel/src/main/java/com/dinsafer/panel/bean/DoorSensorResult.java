package com.dinsafer.panel.bean;

import com.dinsafer.panel.bean.device.DoorSensorDevice;

import java.util.ArrayList;

/**
 * 获取门窗探测器配件数据结果
 * <p>
 * 有序包含新、旧门窗探测器
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/17 2:38 PM
 */
public class DoorSensorResult extends SimplePluginResult2<DoorSensorDevice> {

    private static final String OLD_DOOR_SENSOR = "Common window/door sensor";
    private static final String NEW_DOOR_SENSOR = "Smart window/door sensor";

    public DoorSensorResult() {
        // 初始相关的化配件列表
        pluginMap.put(NEW_DOOR_SENSOR, new ArrayList<>());
        pluginMap.put(OLD_DOOR_SENSOR, new ArrayList<>());
    }

    public ArrayList<DoorSensorDevice> getOldDoorSensors() {
        return pluginMap.get(OLD_DOOR_SENSOR);
    }

    public ArrayList<DoorSensorDevice> getNewDoorSensors() {
        return pluginMap.get(NEW_DOOR_SENSOR);
    }
}
