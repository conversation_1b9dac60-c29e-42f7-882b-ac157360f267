package com.dinsafer.panel.bean;

import com.dinsafer.dincore.common.NetKeyConstants;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dssupport.plugin.PluginConstants;
import com.dinsafer.dssupport.plugin.PluginTypeHelper;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.bean.device.DoorSensorDevice;
import com.dinsafer.panel.bean.device.SmartPlugDevice;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.common.PanelDataKey;

import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 首页配件信息
 *
 * <AUTHOR>
 * @date 2021/6/4 14:39
 */
public class HomePluginResult {
    private static final String TAG = HomePluginResult.class.getSimpleName();

    private List<SmartPlugDevice> smartPlugList; // 插座
    private List<DoorSensorDevice> doorSensorList; // 门磁
    private List<DoorSensorDevice> vibrateSensorList; // 震动门磁


    public List<SmartPlugDevice> getSmartPlugList() {
        return smartPlugList;
    }

    public void setSmartPlugList(List<SmartPlugDevice> smartPlugList) {
        this.smartPlugList = smartPlugList;
    }

    public List<DoorSensorDevice> getDoorSensorList() {
        return doorSensorList;
    }

    public void setDoorSensorList(List<DoorSensorDevice> doorSensorList) {
        this.doorSensorList = doorSensorList;
    }

    public List<DoorSensorDevice> getVibrateSensorList() {
        return vibrateSensorList;
    }

    public void setVibrateSensorList(List<DoorSensorDevice> vibrateSensorList) {
        this.vibrateSensorList = vibrateSensorList;
    }

    public static HomePluginResult parseFromHomePluginEntry(final String panelId, final JSONObject jsonObject) {
        List<SmartPlugDevice> smartPlugDevices = new ArrayList<>();
        List<DoorSensorDevice> doorSensorDevices = new ArrayList<>();
        List<DoorSensorDevice> vibrateSensorDevices = new ArrayList<>();

        JSONObject pluginInfo = DDJSONUtil.getJSONObject(jsonObject, NetKeyConstants.NET_KEY_PLUGIN__INFO);

        // 1、自家插座
        JSONArray jsonArray = DDJSONUtil.getJSONarray(pluginInfo, NetKeyConstants.NET_KEY_SMART__PLUG);
        JSONObject pluginItemJson;
        Map<String, Object> pluginParams;
        if (null != jsonArray && 0 < jsonArray.length()) {
            SmartPlugDevice plugDevice;
            for (int i = 0; i < jsonArray.length(); i++) {
                try {
                    pluginItemJson = (JSONObject) jsonArray.get(i);
                    pluginParams = new HashMap<>();
                    int category = DDJSONUtil.getInt(pluginItemJson, NetKeyConstants.NET_KEY_CATEGORY);
                    if (PluginConstants.CATEGORY_10 == category) {
                        // TODO subcategory
                        // 新插座
                        plugDevice = new SmartPlugDevice(DDJSONUtil.getString(pluginItemJson, NetKeyConstants.NET_KEY_ID),
                                category, null, panelId);
                        parseNewPlugFromJson(pluginItemJson, pluginParams);
                        pluginParams.put(PanelDataKey.LOADING_STATE, PanelConstant.PluginLoadingState.SUCCESS);
                        pluginParams.put(PanelDataKey.SmartPlug.IS_ASK_SMART_PLUG,
                                PluginTypeHelper.getInstance().isSmartPlugAsk(DDJSONUtil.getString(pluginItemJson,
                                        NetKeyConstants.NET_KEY_S_TYPE)));
                        pluginParams.put(PanelDataKey.HAVE_LOADING_STATE, true);
                        pluginParams.put(PanelDataKey.HAVE_ONLINE_STATE, true);
                    } else {
                        // 旧插座
                        plugDevice = new SmartPlugDevice(DDJSONUtil.getString(pluginItemJson, NetKeyConstants.NET_KEY_ID),
                                category, DDJSONUtil.getString(pluginItemJson, NetKeyConstants.NET_KEY_SUB__CATEGORY), panelId);
                        parseOldPlugFromJson(pluginItemJson, pluginParams);
                        pluginParams.put(PanelDataKey.HAVE_LOADING_STATE, true);
                        pluginParams.put(PanelDataKey.LOADING_STATE, PanelConstant.PluginLoadingState.SUCCESS);
                        pluginParams.put(PanelDataKey.SmartPlug.IS_ASK_SMART_PLUG, false);
                        pluginParams.put(PanelDataKey.HAVE_ONLINE_STATE, false);
                    }

                    boolean isOn = DDJSONUtil.getBoolean(pluginItemJson, NetKeyConstants.NET_KEY_ENABLE);
                    pluginParams.put(PanelDataKey.PLUGIN_SWITCH_STATE, isOn
                            ? PanelConstant.PluginSwitchState.OPENED
                            : PanelConstant.PluginSwitchState.CLOSED);
                    plugDevice.setInfo(pluginParams);
                    smartPlugDevices.add(plugDevice);
                } catch (Exception e) {
                    DDLog.d(TAG, "Error on parse plug item, i: " + i);
                    e.printStackTrace();
                }
            }
        }

        // 2、doorSensor
        jsonArray = DDJSONUtil.getJSONarray(pluginInfo, NetKeyConstants.NET_KEY_DOOR__WINDOW);
        DoorSensorDevice doorSensorDevice;
        if (null != jsonArray && 0 < jsonArray.length()) {
            for (int i = 0; i < jsonArray.length(); i++) {
                try {
                    pluginParams = new HashMap<>();
                    pluginItemJson = (JSONObject) jsonArray.get(i);
                    int category = DDJSONUtil.getInt(pluginItemJson, NetKeyConstants.NET_KEY_CATEGORY);
                    if (PluginConstants.CATEGORY_10 == category) {
                        // 新门磁
                        // TODO 更新subcategory
                        doorSensorDevice = new DoorSensorDevice(DDJSONUtil.getString(pluginItemJson, NetKeyConstants.NET_KEY_ID),
                                category, null, panelId);
                        parseNewPlugFromJson(pluginItemJson, pluginParams);
                        pluginParams.put(PanelDataKey.BLOCK, DDJSONUtil.getInt(pluginItemJson, NetKeyConstants.NET_KEY_BLOCK));
                        pluginParams.put(PanelDataKey.LOADING_STATE, PanelConstant.PluginLoadingState.SUCCESS);
                        pluginParams.put(PanelDataKey.HAVE_LOADING_STATE, false);
                        pluginParams.put(PanelDataKey.HAVE_ONLINE_STATE, true);
                    } else {
                        // 旧门磁
                        doorSensorDevice = new DoorSensorDevice(DDJSONUtil.getString(pluginItemJson, NetKeyConstants.NET_KEY_ID),
                                category, DDJSONUtil.getString(pluginItemJson, NetKeyConstants.NET_KEY_SUB__CATEGORY), panelId);
                        parseOldPlugFromJson(pluginItemJson, pluginParams);
                        pluginParams.put(PanelDataKey.LOADING_STATE, PanelConstant.PluginLoadingState.SUCCESS);
                        pluginParams.put(PanelDataKey.HAVE_LOADING_STATE, false);
                        pluginParams.put(PanelDataKey.HAVE_ONLINE_STATE, false);
                        pluginParams.put(PanelDataKey.IS_ONLINE, true);
                    }

                    doorSensorDevice.setInfo(pluginParams);
                    doorSensorDevices.add(doorSensorDevice);
                } catch (Exception e) {
                    DDLog.d(TAG, "Error on parse door sensor item, i: " + i);
                    e.printStackTrace();
                }
            }
        }

        // 3、VibrationSensor
        jsonArray = DDJSONUtil.getJSONarray(pluginInfo, NetKeyConstants.NET_KEY_VIBRATION);
        if (null != jsonArray && 0 < jsonArray.length()) {
            for (int i = 0; i < jsonArray.length(); i++) {
                try {
                    pluginParams = new HashMap<>();
                    pluginItemJson = (JSONObject) jsonArray.get(i);
                    int category = DDJSONUtil.getInt(pluginItemJson, NetKeyConstants.NET_KEY_CATEGORY);
                    if (PluginConstants.CATEGORY_10 == category) {
                        // 新震动门磁
                        // TODO 更新subcategory
                        doorSensorDevice = new DoorSensorDevice(DDJSONUtil.getString(pluginItemJson, NetKeyConstants.NET_KEY_ID),
                                category, null, panelId);
                        parseNewPlugFromJson(pluginItemJson, pluginParams);
                        pluginParams.put(PanelDataKey.BLOCK, DDJSONUtil.getInt(pluginItemJson, NetKeyConstants.NET_KEY_BLOCK));
                        pluginParams.put(PanelDataKey.LOADING_STATE, PanelConstant.PluginLoadingState.SUCCESS);
                        pluginParams.put(PanelDataKey.HAVE_LOADING_STATE, false);
                        pluginParams.put(PanelDataKey.HAVE_ONLINE_STATE, true);
                    } else {
                        // 旧震动门磁
                        doorSensorDevice = new DoorSensorDevice(DDJSONUtil.getString(pluginItemJson, NetKeyConstants.NET_KEY_ID),
                                category, DDJSONUtil.getString(pluginItemJson, NetKeyConstants.NET_KEY_SUB__CATEGORY), panelId);
                        parseOldPlugFromJson(pluginItemJson, pluginParams);
                        pluginParams.put(PanelDataKey.LOADING_STATE, PanelConstant.PluginLoadingState.SUCCESS);
                        pluginParams.put(PanelDataKey.HAVE_LOADING_STATE, false);
                        pluginParams.put(PanelDataKey.HAVE_ONLINE_STATE, false);
                        pluginParams.put(PanelDataKey.IS_ONLINE, true);
                    }

                    doorSensorDevice.setInfo(pluginParams);
                    doorSensorDevices.add(doorSensorDevice);
                } catch (Exception e) {
                    DDLog.d(TAG, "Error on parse vibration door sensor item, i: " + i);
                    e.printStackTrace();
                }
            }
        }

        HomePluginResult homePluginResult = new HomePluginResult();
        homePluginResult.setSmartPlugList(smartPlugDevices);
        homePluginResult.setDoorSensorList(doorSensorDevices);
        homePluginResult.setVibrateSensorList(vibrateSensorDevices);
        return homePluginResult;
    }


    /**
     * 解析并创建新类型的配件
     * id
     * name
     * block
     * category
     * keeplive
     * sendid
     * stype
     *
     * @param jsonObject
     */
    private static void parseNewPlugFromJson(JSONObject jsonObject, @NotNull Map<String, Object> params) {
        try {
            params.put(PanelDataKey.NAME, DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_NAME));
            params.put(PanelDataKey.CATEGORY, DDJSONUtil.getInt(jsonObject, NetKeyConstants.NET_KEY_CATEGORY));
            params.put(PanelDataKey.BLOCK, DDJSONUtil.getInt(jsonObject, NetKeyConstants.NET_KEY_BLOCK));
            params.put(PanelDataKey.SEND_ID, DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_SEND_ID));
            params.put(PanelDataKey.S_TYPE, DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_S_TYPE));
            params.put(PanelDataKey.IS_ONLINE, DDJSONUtil.getBoolean(jsonObject, NetKeyConstants.NET_KEY_KEEP_LIVE));
        } catch (Exception e) {
            DDLog.e(TAG, "Error on parseNewPlugFromJson");
            e.printStackTrace();
        }
    }

    /**
     * 解析并创建旧类型的配件
     * block
     * category
     * decodeid
     * sub_category
     * keeplive
     *
     * @param jsonObject
     */
    private static void parseOldPlugFromJson(JSONObject jsonObject, @NotNull Map<String, Object> params) {
        try {
            params.put(PanelDataKey.NAME, DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_NAME));
            params.put(PanelDataKey.BLOCK, DDJSONUtil.getInt(jsonObject, NetKeyConstants.NET_KEY_BLOCK));
            params.put(PanelDataKey.CATEGORY, DDJSONUtil.getInt(jsonObject, NetKeyConstants.NET_KEY_CATEGORY));
            params.put(PanelDataKey.DECODE_ID, DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_DECODE_ID));
            params.put(PanelDataKey.SUBCATEGORY, DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_SUB__CATEGORY));
            params.put(PanelDataKey.IS_ONLINE, DDJSONUtil.getBoolean(jsonObject, NetKeyConstants.NET_KEY_KEEP_LIVE));
        } catch (Exception e) {
            DDLog.e(TAG, "Error on parseOldPlugFromJson");
            e.printStackTrace();
        }
    }

}
