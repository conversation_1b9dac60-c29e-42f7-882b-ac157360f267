package com.dinsafer.panel.bean.device;

import android.text.TextUtils;

import androidx.annotation.IntRange;
import androidx.annotation.RestrictTo;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.common.NetKeyConstants;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dincore.utils.RandomStringUtils;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelResultBuilder;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.panel.http.PanelApi;
import com.dinsafer.panel.operate.PanelOperator;
import com.dinsafer.panel.operate.PanelOperatorConstant;
import com.dinsafer.panel.operate.bean.PirSensitivityEntry;
import com.dinsafer.panel.operate.bean.PirSensitivityResponse;
import com.dinsafer.panel.operate.bean.PirSettingEnabledStatueEntry;
import com.dinsafer.panel.operate.bean.event.DeviceResetOrDeletedEvent;
import com.dinsafer.panel.operate.bean.event.DeviceResultEvent;
import com.dinsafer.panel.operate.callback.PanelCallback;
import com.dinsafer.panel.util.PanelSecretUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 配件
 *
 * <AUTHOR>
 * @date 2021/6/1 19:11
 */
public abstract class BasePluginDevice<T extends Device> extends Device {
    protected final String TAG = this.getClass().getSimpleName();
    protected final Map<String, String> mMessageIdMap = new HashMap<>();
    protected String messageId;

    /**
     * 标记为未查询到配件基本信息的状态
     */
    public void setNeedLoadInfoAgain() {
        setFlagLoading(false);
        setFlagLoaded(false);
    }

    public void markFlagLoading() {
        setFlagLoading(true);
    }

    public void markFlagLoaded() {
        setFlagLoaded(true);
    }

    public void updateInfo(T device) {
        if (null == device || null == device.getInfo()) {
            return;
        }

        if (null == getInfo()) {
            setInfo(device.getInfo());
            return;
        }

        getInfo().putAll(device.getInfo());
    }

    public void updateInfo(Map<String, Object> src) {
        if (null == src) {
            return;
        }

        if (null == getInfo()) {
            setInfo(new HashMap<>());
            return;
        }

        getInfo().putAll(src);
    }

    public void setLoaded(boolean loaded) {
        setFlagLoaded(loaded);
    }

    public BasePluginDevice() {
        initFlagOnNetwork();
        EventBus.getDefault().register(this);
    }

    public BasePluginDevice(String id, int category, String subCategory, Map<String, Object> info) {
        super(id, category, subCategory, info);
        initFlagOnNetwork();
        EventBus.getDefault().register(this);
    }

    public BasePluginDevice(String id, int category, String subCategory, Map<String, Object> info, String fatherId) {
        super(id, category, subCategory, info, fatherId);
        initFlagOnNetwork();
        EventBus.getDefault().register(this);
    }

    protected boolean isCanOperate() {
        return !getFlagDeleted();
    }

    /**
     * 处理主机被删除后可以选择进行操作的CMD
     *
     * @return true 表示已经处理了该cmd
     */
    protected boolean submitOnDeviceDelete(@NotNull String cmd, Map arg) {
        if (PluginCmd.PLUGIN_DELETE.equals(cmd)) {
            final JSONObject result = new JSONObject();
            try {
                result.put(PanelDataKey.CmdResult.CMD, cmd);
                result.put(PanelDataKey.CmdResult.OPERATION_CMD, PanelOperatorConstant.CMD.DELETE_PLUGIN);
                result.put(NetKeyConstants.NET_KEY_PLUGIN_ID, getId());
            } catch (Exception e) {
                e.printStackTrace();
            }
            dispatchDeviceResult(cmd, 1, true, 1, result.toString());
            remove();
            PanelManager.getInstance().removeCache(getFatherId(), getId());
            return true;
        }
        return false;
    }

    protected void deletePlugin(Map<String, Object> args) {
        DDLog.i(TAG, "deletePlugin");
        final String cmd = (String) args.get(PanelDataKey.CMD);
        final String qrCode = getId();
        final String decodeId = DeviceHelper.getString(getInfo(), PanelDataKey.DECODE_ID, "");
        String pluginId = getId();
        if (!TextUtils.isEmpty(decodeId)) {
            pluginId = PanelSecretUtil.hexStrToStr64(decodeId);
        }
        messageId = RandomStringUtils.getMessageId();
        mMessageIdMap.put(messageId, cmd);
        PanelManager.getInstance().getNetworkRequestManager().requestDeletePlugs(
                PanelManager.getInstance().getCurrentPanelToken(), messageId, qrCode, pluginId,
                new PanelCallback.NetworkResult<StringResponseEntry>() {
                    @Override
                    public void onSuccess(StringResponseEntry result) {
                    }

                    @Override
                    public void onError(int errorCode, String errorMsg) {
                        dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                + errorCode + ", errorMsg: " + errorMsg).createResult());
                    }
                }
        );
    }

    protected void deleteAskPlugin(Map<String, Object> args) {
        DDLog.i(TAG, "deleteAskPlugin");
        JSONObject jsonObject = DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA);
        final String cmd = (String) args.get(PanelDataKey.CMD);
        final String sendId = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_SEND_ID);
        final String stype = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_S_TYPE);
        messageId = RandomStringUtils.getMessageId();
        mMessageIdMap.put(messageId, cmd);
        PanelManager.getInstance().getNetworkRequestManager().requestDeleteASKPlugs(
                PanelManager.getInstance().getCurrentPanelToken(), messageId, sendId, stype, getId(),
                new PanelCallback.NetworkResult<StringResponseEntry>() {
                    @Override
                    public void onSuccess(StringResponseEntry result) {
                    }

                    @Override
                    public void onError(int errorCode, String errorMsg) {
                        dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                + errorCode + ", errorMsg: " + errorMsg).createResult());
                    }
                }
        );
    }

    protected void modifyPlugin(Map<String, Object> args) {
        DDLog.i(TAG, "modifyPlugin");
        final String cmd = (String) args.get(PanelDataKey.CMD);
        final String qrCode = getId();
        final String decodeId = DeviceHelper.getString(getInfo(), PanelDataKey.DECODE_ID, "");
        String pluginId = getId();
        if (!TextUtils.isEmpty(decodeId)) {
            pluginId = PanelSecretUtil.hexStrToStr64(decodeId);
        }
        String newName = (String) args.get(PanelDataKey.NAME);
        messageId = RandomStringUtils.getMessageId();
        mMessageIdMap.put(messageId, cmd);
        PanelManager.getInstance().getNetworkRequestManager().requestChangePlugName(
                PanelManager.getInstance().getCurrentPanelToken(), messageId, qrCode, pluginId, newName,
                new PanelCallback.NetworkResult<StringResponseEntry>() {
                    @Override
                    public void onSuccess(StringResponseEntry result) {
                    }

                    @Override
                    public void onError(int errorCode, String errorMsg) {
                        dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                + errorCode + ", errorMsg: " + errorMsg).createResult());
                    }
                }
        );
    }

    protected void modifyAskPlugin(Map<String, Object> args) {
        DDLog.i(TAG, "modifyAskPlugin");
        final String cmd = (String) args.get(PanelDataKey.CMD);
        String newName = (String) args.get(PanelDataKey.NAME);
        String datas = "";
        if (null != DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA)) {
            datas = DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA).toString();
        }
        messageId = RandomStringUtils.getMessageId();
        mMessageIdMap.put(messageId, cmd);
        PanelManager.getInstance().getNetworkRequestManager().requestASKPluginModify(
                PanelManager.getInstance().getCurrentPanelToken(), messageId, datas, newName,
                new PanelCallback.NetworkResult<StringResponseEntry>() {
                    @Override
                    public void onSuccess(StringResponseEntry result) {
                    }

                    @Override
                    public void onError(int errorCode, String errorMsg) {
                        dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                + errorCode + ", errorMsg: " + errorMsg).createResult());
                    }
                }
        );
    }

    /**
     * 修改屏蔽配件类型
     */
    protected void configPluginBlock(String cmd, Map<String, Object> arg) {
        DDLog.i(TAG, "configPluginBlock");
        messageId = RandomStringUtils.getMessageId();
        mMessageIdMap.put(messageId, cmd);
        int block = (int) arg.get(PanelDataKey.DoorSensor.BLOCK_TYPE);
        JSONObject askData = DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA);
        String sType = DDJSONUtil.getString(askData, "stype");
        String sendID = DDJSONUtil.getString(askData, "sendid");
        if (TextUtils.isEmpty(sendID)
                || TextUtils.isEmpty(sType)) {
            dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                    + ErrorCode.PARAM_ERROR + ", errorMsg: " + "配件没有stype或sendid").createResult());
            return;
        }

        PanelManager.getInstance().getNetworkRequestManager().requestModifyPluginBlock(
                PanelManager.getInstance().getCurrentPanelToken(), messageId, getId(),
                sendID, sType, block, new PanelCallback.NetworkResult<StringResponseEntry>() {
                    @Override
                    public void onSuccess(StringResponseEntry result) {
                    }

                    @Override
                    public void onError(int errorCode, String errorMsg) {
                        dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                + errorCode + ", errorMsg: " + errorMsg).createResult());
                    }
                }
        );
    }

    /**
     * 主机屏蔽红外触发（5min）
     */
    protected void setPirSettingEnabledStatue(String cmd, Map<String, Object> arg) {
        DDLog.i(TAG, "setPirSettingEnabledStatue");
        messageId = RandomStringUtils.getMessageId();
        mMessageIdMap.put(messageId, cmd);
        JSONObject askData = DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA);
        String sType = DDJSONUtil.getString(askData, "stype");
        String sendId = DDJSONUtil.getString(askData, "sendid");
        if (TextUtils.isEmpty(sendId)
                || TextUtils.isEmpty(sType)) {
            dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                    + ErrorCode.PARAM_ERROR + ", errorMsg: " + "配件没有stype或sendid").createResult());
            return;
        }

        PanelManager.getInstance().getNetworkRequestManager().setPirSettingEnabledStatue(
                PanelManager.getInstance().getCurrentPanelToken(), PanelManager.getInstance().getCurrentHomeId()
                , messageId, getId(), sendId, sType, new PanelCallback.NetworkResult<PirSettingEnabledStatueEntry>() {
                    @Override
                    public void onSuccess(PirSettingEnabledStatueEntry entry) {
                    }

                    @Override
                    public void onError(int errorCode, String errorMsg) {
                        dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                + errorCode + ", errorMsg: " + errorMsg).createResult());
                    }
                });

    }

    /**
     * 设置红外灵敏度设置模式
     */
    protected void setPirSensitivity(String cmd, Map<String, Object> arg) {
        DDLog.i(TAG, "setPirSensitivity");
        messageId = RandomStringUtils.getMessageId();
        mMessageIdMap.put(messageId, cmd);
        JSONObject askData = DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA);
        String sType = DDJSONUtil.getString(askData, "stype");
        String sendId = DDJSONUtil.getString(askData, "sendid");
        int sensitivity = (int) arg.get(PanelDataKey.PirSensitivityInfo.SENSITIVITY);
        if (TextUtils.isEmpty(sendId)
                || TextUtils.isEmpty(sType)) {
            dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                    + ErrorCode.PARAM_ERROR + ", errorMsg: " + "配件没有stype或sendid").createResult());
            return;
        }

        PanelManager.getInstance().getNetworkRequestManager().setPirSensitivity(
                PanelManager.getInstance().getCurrentPanelToken(), PanelManager.getInstance().getCurrentHomeId()
                , messageId, getId(), sendId, sType, sensitivity, new PanelCallback.NetworkResult<PirSensitivityResponse>() {
                    @Override
                    public void onSuccess(PirSensitivityResponse response) {
                    }

                    @Override
                    public void onError(int errorCode, String errorMsg) {
                        dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                + errorCode + ", errorMsg: " + errorMsg).createResult());
                    }
                }
        );
    }

    /**
     * 退出红外设置模式
     */
    protected void exitPirSettingMode(String cmd, Map<String, Object> map) {
        DDLog.i(TAG, "exitPirSettingMode");
        messageId = RandomStringUtils.getMessageId();
        mMessageIdMap.put(messageId, cmd);
        JSONObject askData = DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA);
        String sType = DDJSONUtil.getString(askData, "stype");
        String sendId = DDJSONUtil.getString(askData, "sendid");
        if (TextUtils.isEmpty(sendId)
                || TextUtils.isEmpty(sType)) {
            dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                    + ErrorCode.PARAM_ERROR + ", errorMsg: " + "配件没有stype或sendid").createResult());
            return;
        }

        PanelManager.getInstance().getNetworkRequestManager().exitPirSettingMode(PanelManager.getInstance().getCurrentPanelToken()
                , PanelManager.getInstance().getCurrentHomeId(), messageId, getId(), sendId, sType
                , new PanelCallback.NetworkResult<PirSensitivityEntry>() {
                    @Override
                    public void onSuccess(PirSensitivityEntry entry) {
                    }

                    @Override
                    public void onError(int errorCode, String errorMsg) {
                        dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                + errorCode + ", errorMsg: " + errorMsg).createResult());
                    }
                });


    }

    /**
     * 测试警笛
     */
    protected void testScrien(String cmd, Map<String, Object> arg) {
        DDLog.i(TAG, "testScrien");
        String sendId = (String) arg.get(PanelDataKey.SEND_ID);
        String sType = (String) arg.get(PanelDataKey.S_TYPE);
        if (TextUtils.isEmpty(sendId)) {
            sendId = DeviceHelper.getString(getInfo(), PanelDataKey.SEND_ID, "");
        }
        if (TextUtils.isEmpty(sType)) {
            sType = DeviceHelper.getString(getInfo(), PanelDataKey.S_TYPE, "");
        }
        int music = (int) arg.get(PanelDataKey.WirelessSiren.MUSIC);
        int volume = (int) arg.get(PanelDataKey.WirelessSiren.VOLUME);
        PanelManager.getInstance().getNetworkRequestManager().requestTestSiren(
                PanelManager.getInstance().getCurrentPanelToken(), sendId, sType, music, volume,
                new PanelCallback.NetworkResult<StringResponseEntry>() {
                    @Override
                    public void onSuccess(StringResponseEntry result) {
                        if (1 == result.getStatus()) {
                            dispatchResult(cmd, PanelResultBuilder.success(cmd, true, result.getResult()).createResult());
                        } else {
                            dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                    + result.getStatus() + ", errorMsg: " + result.getErrorMessage()).createResult());
                        }
                    }

                    @Override
                    public void onError(int errorCode, String errorMsg) {
                        dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                + errorCode + ", errorMsg: " + errorMsg).createResult());
                    }
                }
        );
    }

    /**
     * 获取可以在SmartButton控制的配件列表
     */
    protected void getPluginTargetList(String cmd, Map<String, Object> arg) {
        DDLog.i(TAG, "getPluginTargetList");
        int category = (int) arg.get(PanelDataKey.CATEGORY);
        PanelManager.getInstance().getNetworkRequestManager().requestSmartButtonTargetPluginList(
                getFatherId(), category, new PanelCallback.NetworkResult<String>() {
                    @Override
                    public void onSuccess(String result) {
                        dispatchResult(cmd, PanelResultBuilder.success(cmd, true, result).createResult());
                    }

                    @Override
                    public void onError(int errorCode, String errorMsg) {
                        dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                + errorCode + ", errorMsg: " + errorMsg).createResult());
                    }
                }
        );
    }

    /**
     * 修改smartButton的控制信息
     */
    protected void updateSmartButtonConfig(String cmd, Map<String, Object> arg) {
        JSONObject askData = DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA);
        String sendId = DDJSONUtil.getString(askData, NetKeyConstants.NET_KEY_SEND_ID);
        String sType = DDJSONUtil.getString(askData, NetKeyConstants.NET_KEY_S_TYPE);
        messageId = RandomStringUtils.getMessageId();
        mMessageIdMap.put(messageId, cmd);
        ArrayList<JSONObject> actionConfigs = (ArrayList<JSONObject>) arg.get(PanelDataKey.SmartButton.ACTION_CONFIG);
        PanelManager.getInstance().getNetworkRequestManager().requestUpdateAndSaveSmartButtonConfig(
                PanelManager.getInstance().getCurrentPanelToken(), messageId, sendId, sType,
                new PanelCallback.NetworkResult<StringResponseEntry>() {
                    @Override
                    public void onSuccess(StringResponseEntry result) {
                    }

                    @Override
                    public void onError(int errorCode, String errorMsg) {
                        dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                + errorCode + ", errorMsg: " + errorMsg).createResult());
                    }
                }, actionConfigs
        );
    }

    @Override
    public void destory() {
        // super.destory();
        EventBus.getDefault().unregister(this);
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY)
    @Override
    public void setFlagDeleted(boolean isDeleted) {
        super.setFlagDeleted(isDeleted);
        if (!isDeleted) {
            synchronized (this) {
                try {
                    EventBus.getDefault().register(this);
                } catch (Exception e) {
                    if (MsctLog.isEnableLog) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(final DeviceResetOrDeletedEvent deviceDeletedEvent) {
        DDLog.i(TAG, "onEvent, DeviceDeletedEvent id: " + deviceDeletedEvent.getId());
        if (!TextUtils.isEmpty(deviceDeletedEvent.getId()) && getFatherId().equals(deviceDeletedEvent.getId())) {
            PanelManager.getInstance().removeCache(getFatherId(), getId());
            remove();
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(final DeviceResultEvent deviceResultEvent) {
        DDLog.i(TAG, "onEvent, DeviceResultEvent");
        if (null == deviceResultEvent) {
            return;
        }
        try {
            final String operateCmd = deviceResultEvent.getCmdType();
            final String cmd = PanelOperator.transformFromPanelOperatorCmd(operateCmd);
            final String messageId = deviceResultEvent.getMessageId();
            if (TextUtils.isEmpty(operateCmd)
                    || TextUtils.isEmpty(messageId)
                    || PanelOperatorConstant.CMD.TASK_PLUGIN_STATUS.equals(operateCmd)) {
                return;
            }

            if (1 != deviceResultEvent.getStatus() && mMessageIdMap.containsKey(messageId)) {
                dispatchDeviceResult(cmd, deviceResultEvent.getStatus(), true, 1, deviceResultEvent.getReslut());
                mMessageIdMap.remove(messageId);
                return;
            }

            switch (operateCmd) {
                case PanelOperatorConstant.CMD.SET_NEWASKPLUGINDATA:
                    JSONObject jsonObject = new JSONObject(deviceResultEvent.getReslut());
                    jsonObject.put(PanelDataKey.CmdResult.OPERATION_CMD, operateCmd);
                    String pluginId = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_PLUGIN__ID);
                    String name = DDJSONUtil.getString(jsonObject, "name");
                    if (!getId().equals(pluginId)) {
                        return;
                    }
                    DDLog.i(TAG, "deviceResultEvent: " + deviceResultEvent.toString());
                    if (!TextUtils.isEmpty(name) && null != getInfo()) {
                        getInfo().put(PanelDataKey.NAME, name);
                    }
                    boolean isSelf = mMessageIdMap.containsKey(messageId);
                    mMessageIdMap.remove(messageId);
                    dispatchDeviceResult(cmd, deviceResultEvent.getStatus(), isSelf, 1, jsonObject.toString());
                    break;
                case PanelOperatorConstant.CMD.SET_PLUGINDATA:
                    jsonObject = new JSONObject(deviceResultEvent.getReslut());
                    jsonObject.put(PanelDataKey.CmdResult.OPERATION_CMD, operateCmd);
                    pluginId = DDJSONUtil.getString(jsonObject, "pluginid");
                    name = DDJSONUtil.getString(jsonObject, "plugin_item_name");
                    if (!getId().equals(pluginId)) {
                        return;
                    }
                    DDLog.i(TAG, "deviceResultEvent: " + deviceResultEvent.toString());
                    if (!TextUtils.isEmpty(name) && null != getInfo()) {
                        getInfo().put(PanelDataKey.NAME, name);
                    }
                    isSelf = mMessageIdMap.containsKey(messageId);
                    mMessageIdMap.remove(messageId);
                    dispatchDeviceResult(cmd, deviceResultEvent.getStatus(), isSelf, 1, jsonObject.toString());
                    break;
                case PanelOperatorConstant.CMD.DELETE_PLUGIN:
                    jsonObject = new JSONObject(deviceResultEvent.getReslut());
                    jsonObject.put(PanelDataKey.CmdResult.OPERATION_CMD, operateCmd);
                    pluginId = DDJSONUtil.getString(jsonObject, "pluginid");
                    if (!getId().equals(pluginId)) {
                        return;
                    }
                    DDLog.i(TAG, "deviceResultEvent: " + deviceResultEvent.toString());
                    isSelf = mMessageIdMap.containsKey(messageId);
                    mMessageIdMap.remove(messageId);
                    PanelManager.getInstance().removeCache(getFatherId(), getId());
                    dispatchDeviceResult(cmd, deviceResultEvent.getStatus(), isSelf, 1, jsonObject.toString());
                    remove();
                    break;
                case PanelOperatorConstant.CMD.DELETE_NEWASKPLUGIN:
                    jsonObject = new JSONObject(deviceResultEvent.getReslut());
                    jsonObject.put(PanelDataKey.CmdResult.OPERATION_CMD, operateCmd);
                    pluginId = DDJSONUtil.getString(jsonObject, "plugin_id");
                    if (!getId().equals(pluginId)) {
                        return;
                    }
                    DDLog.i(TAG, "deviceResultEvent: " + deviceResultEvent.toString());
                    isSelf = mMessageIdMap.containsKey(messageId);
                    mMessageIdMap.remove(messageId);
                    PanelManager.getInstance().removeCache(getFatherId(), getId());
                    dispatchDeviceResult(cmd, deviceResultEvent.getStatus(), isSelf, 1, jsonObject.toString());
                    remove();
                    break;
                case PanelOperatorConstant.CMD.PLUGIN_OFFLINE:
                case PanelOperatorConstant.CMD.PLUGIN_ONLINE:
                    jsonObject = new JSONObject(deviceResultEvent.getReslut());
                    jsonObject.put(PanelDataKey.CmdResult.OPERATION_CMD, operateCmd);
                    String sendid = DDJSONUtil.getString(jsonObject, "sendid");
                    JSONObject askData = DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA);
                    if (TextUtils.isEmpty(sendid) || null == askData
                            || !sendid.equals(DDJSONUtil.getString(askData, NetKeyConstants.NET_KEY_SEND_ID))) {
                        return;
                    }
                    DDLog.i(TAG, "deviceResultEvent: " + deviceResultEvent.toString());
                    dispatchDeviceResult(cmd, deviceResultEvent.getStatus(), false, 1, jsonObject.toString());
                    break;
                case PanelOperatorConstant.CMD.EVENT_LOWERPOWER:
                case PanelOperatorConstant.CMD.EVENT_FULLPOWER:
                    jsonObject = new JSONObject(deviceResultEvent.getReslut());
                    jsonObject.put(PanelDataKey.CmdResult.OPERATION_CMD, operateCmd);
                    pluginId = DDJSONUtil.getString(jsonObject, "pluginid");
                    if (!getId().equals(pluginId)) {
                        return;
                    }
                    DDLog.i(TAG, "deviceResultEvent: " + deviceResultEvent.toString());
                    dispatchDeviceResult(cmd, deviceResultEvent.getStatus(), false, 1, jsonObject.toString());
                    break;
                case PanelOperatorConstant.CMD.SET_PLUGIN_BLOCK:
                    jsonObject = new JSONObject(deviceResultEvent.getReslut());
                    pluginId = DDJSONUtil.getString(jsonObject, "plugin_id");
                    if (!getId().equals(pluginId)) {
                        return;
                    }
                    DDLog.i(TAG, "deviceResultEvent: " + deviceResultEvent.toString());
                    isSelf = mMessageIdMap.containsKey(messageId);
                    mMessageIdMap.remove(messageId);
                    dispatchDeviceResult(cmd, deviceResultEvent.getStatus(), isSelf, 1, deviceResultEvent.getReslut());
                    break;
                case PanelOperatorConstant.CMD.BYPASS_PLUGIN_5_MIN:
                case PanelOperatorConstant.CMD.SET_SENSITIVITY:
                    jsonObject = new JSONObject(deviceResultEvent.getReslut());
                    pluginId = DDJSONUtil.getString(jsonObject, "plugin_id");
                    if (!getId().equals(pluginId)) {
                        return;
                    }
                    DDLog.i(TAG, "deviceResultEvent: " + deviceResultEvent.toString());
                    isSelf = mMessageIdMap.containsKey(messageId);
                    mMessageIdMap.remove(messageId);
                    dispatchDeviceResult(cmd, deviceResultEvent.getStatus(), isSelf, 1, deviceResultEvent.getReslut());
                    break;
                case PanelOperatorConstant.CMD.EXIT_PIR_SETTING_MODE:
                    DDLog.i(TAG, "deviceResultEvent: " + deviceResultEvent.toString());
                    isSelf = mMessageIdMap.containsKey(messageId);
                    mMessageIdMap.remove(messageId);
                    dispatchDeviceResult(cmd, deviceResultEvent.getStatus(), isSelf, 1, deviceResultEvent.getReslut());
                    break;
                default:
                    onDeviceResultEvent(deviceResultEvent);
                    break;
            }
        } catch (Exception e) {
            DDLog.i(TAG, "Error on parse cmd result.");
            e.printStackTrace();
        }
    }

    protected void onDeviceResultEvent(final DeviceResultEvent deviceResultEvent) {
        DDLog.i(TAG, "onEvent, DeviceResultEvent: " + deviceResultEvent);
        final String operatorCmd = deviceResultEvent.getCmdType();
        final String messageId = deviceResultEvent.getMessageId();

        if (TextUtils.isEmpty(operatorCmd)
                || TextUtils.isEmpty(messageId)) {
            return;
        }

        final String cmd = PanelOperator.transformFromPanelOperatorCmd(operatorCmd);
        if (mMessageIdMap.containsKey(messageId)) {
            // 如果是自己操作的，直接返回
            dispatchDeviceResult(cmd, deviceResultEvent.getStatus(), true, 1, deviceResultEvent.getReslut());
            mMessageIdMap.remove(messageId);
        }
    }

    protected void dispatchDeviceResult(String cmd, int status, boolean operateSelf,
                                        @IntRange(from = 0, to = 1) int resultType, String result) {
        if (1 == status) {
            dispatchResult(cmd, PanelResultBuilder
                    .success(cmd, operateSelf, resultType, result)
                    .createResult());
        } else {
            dispatchResult(cmd, PanelResultBuilder
                    .error(cmd, operateSelf, resultType, result)
                    .createResult());
        }
    }

    /**
     * 获取首页的配件详情数据
     */
    protected void getPluginDetailInfo(String cmd, Map<String, Object> arg) {
        String sendId = DeviceHelper.getString(getInfo(), PanelDataKey.SEND_ID, null);
        String stype = DeviceHelper.getString(getInfo(), PanelDataKey.S_TYPE, null);

        if (TextUtils.isEmpty(sendId) || TextUtils.isEmpty(stype)) {
            dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                    + ErrorCode.PARAM_ERROR + ", errorMsg: " + "没有sendid或stype").createResult());
            return;
        }

        PanelManager.getInstance().getNetworkRequestManager().requestHomePluginDetails(getFatherId(),
                getCategory(), stype, sendId, null,
                new PanelCallback.NetworkResult<String>() {
                    @Override
                    public void onSuccess(String result) {
                        if (!TextUtils.isEmpty(result)) {
                            try {
                                JSONObject askData = new JSONObject(result);
                                if (null == getInfo()) {
                                    setInfo(new HashMap());
                                }
                                getInfo().put(PanelDataKey.ASK_DATA, askData);
                            } catch (Exception e) {
                                DDLog.e(TAG, "Error on set ASK DATA");
                                e.printStackTrace();
                            }
                            dispatchResult(cmd, PanelResultBuilder.success(cmd, true, result).createResult());
                        } else {
                            dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                    + ErrorCode.DEFAULT + ", errorMsg: " + "没有数据").createResult());
                        }
                    }

                    @Override
                    public void onError(int errorCode, String errorMsg) {
                        dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                + errorCode + ", errorMsg: " + errorMsg).createResult());
                    }
                });
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY)
    @Override
    public void setInfo(Map info) {
        super.setInfo(info);
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY)
    @Override
    public void setFlagCache(boolean isCache) {
        super.setFlagCache(isCache);
    }
}
