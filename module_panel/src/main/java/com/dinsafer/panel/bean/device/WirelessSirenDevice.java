package com.dinsafer.panel.bean.device;

import androidx.annotation.Keep;
import android.text.TextUtils;

import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.common.NetKeyConstants;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dincore.utils.RandomStringUtils;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelResultBuilder;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.panel.operate.PanelOperator;
import com.dinsafer.panel.operate.PanelOperatorConstant;
import com.dinsafer.panel.operate.bean.event.DeviceResultEvent;
import com.dinsafer.panel.operate.callback.PanelCallback;
import com.dinsafer.panel.util.PanelSecretUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Map;

/**
 * WirelessSiren/无线警笛数据
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/17 4:29 PM
 */
@Keep
public class WirelessSirenDevice extends BasePluginDevice {

    public WirelessSirenDevice(String id, int category, String subCategory, String fatherId) {
        //  TODO category/subcategory
        super(id, category, subCategory, null, fatherId);
    }

    @Override
    public void submit(Map arg) {
        DDLog.i(TAG, "submit param: " + arg);
        if (null == arg || 0 >= arg.size()) {
            throw new IllegalArgumentException("submit param can not be null");
        }
        try {
            String cmd = (String) arg.get(PanelDataKey.CMD);
            if (TextUtils.isEmpty(cmd)) {
                DDLog.e(TAG, "Empty cmd");
                return;
            }
            if (!isCanOperate()) {
                if (submitOnDeviceDelete(cmd, arg)) {
                    return;
                }

                DDLog.e(TAG, this + "是已经被删除的状态，不支持CMD: " + cmd);
                dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                        + ErrorCode.DEVICE_NOT_BELONE_USER + ", errorMsg: " + "主机是已经被删除的状态，不支持CMD: " + cmd).createResult());
                return;
            }
            switch (cmd) {
                case PluginCmd.PLUGIN_DELETE:
                    if (null == DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA)) {
                        deletePlugin(arg);
                    } else {
                        deleteAskPlugin(arg);
                    }
                    break;
                case PluginCmd.PLUGIN_SETNAME:
                    if (null == DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA)) {
                        modifyPlugin(arg);
                    } else {
                        modifyAskPlugin(arg);
                    }
                    break;
                case PluginCmd.TEST_SIREN:
                    testScrien(cmd, arg);
                    break;
                case PluginCmd.CHANGE_SIREN_SETTING:
                    changeSirenSetting(cmd, arg);
                    break;
                default:
                    DDLog.e(TAG, "Not support cmd: " + cmd);
                    break;
            }
        } catch (Exception e) {
            DDLog.i(TAG, "Error on submit");
            e.printStackTrace();
        }
    }

    /**
     * 修改警笛的设置
     */
    private void changeSirenSetting(String cmd, Map<String, Object> arg) {
        DDLog.i(TAG, "changeSirenSetting");
        messageId = RandomStringUtils.getMessageId();
        String sirenSetting = (String) arg.get(PanelDataKey.WirelessSiren.SIREN_DATA);
        JSONObject askData = DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA);
        if (null == askData) {
            // 旧的警笛
            mMessageIdMap.put(messageId, cmd);
            PanelManager.getInstance().getNetworkRequestManager().requestSirenSetting(
                    PanelManager.getInstance().getCurrentPanelToken(), messageId, getId(),
                    sirenSetting, new PanelCallback.NetworkResult<StringResponseEntry>() {
                        @Override
                        public void onSuccess(StringResponseEntry result) {

                        }

                        @Override
                        public void onError(int errorCode, String errorMsg) {
                            dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                    + errorCode + ", errorMsg: " + errorMsg).createResult());
                        }
                    }
            );
        } else {
            // 新的警笛
            String sType = DDJSONUtil.getString(askData, NetKeyConstants.NET_KEY_S_TYPE);
            String sendId = getId();
            if (TextUtils.isEmpty(sType)) {
                dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                        + ErrorCode.PARAM_ERROR + ", errorMsg: " + "配件缺少stype或sendid").createResult());
            } else {
                String advanceSetting = sirenSetting;
                if ("21".equals(sType)
                        || "22".equals(sType)
                        || "34".equals(sType)
                        || "35".equals(sType)) {
                    String[] tempData = sirenSetting.toString().split(",");
                    //                    第一项
                    String sosTimeString = Integer.toBinaryString(Integer.parseInt(tempData[3]));
                    if (sosTimeString.length() < 5) {
                        int length = 5 - sosTimeString.length();
                        for (int i = 0; i < length; i++) {
                            sosTimeString = "0" + sosTimeString;
                        }
                    }

                    String firstByte = Integer.toBinaryString(Integer.parseInt(tempData[0])) +
                            Integer.toBinaryString(Integer.parseInt(tempData[1])) +
                            Integer.toBinaryString(Integer.parseInt(tempData[2])) +
                            sosTimeString;

                    String firstHex = Integer.toHexString(Integer.valueOf(firstByte, 2));
                    if (firstHex.length() < 2) {
                        //    前面补0
                        firstHex = "0" + firstHex;
                    }

                    String twoByte = PanelSecretUtil.intToByte(Integer.parseInt(tempData[4])) +
                            PanelSecretUtil.intToByte(Integer.parseInt(tempData[5])) +
                            PanelSecretUtil.intToByte(Integer.parseInt(tempData[6])) +
                            PanelSecretUtil.intToByte(Integer.parseInt(tempData[7]));

                    String twoHex = Integer.toHexString(Integer.valueOf(twoByte, 2));
                    if (twoHex.length() < 2) {
                        // 前面补0
                        twoHex = "0" + twoHex;
                    }

                    int promptVolume = getIndexWithValue(tempData[8]);
                    int alarmVolume = getIndexWithValue(tempData[9]);

                    // 4位0为扩展位
                    String threeByte = PanelSecretUtil.intToByte(promptVolume) +
                            PanelSecretUtil.intToByte(alarmVolume) + "0000";

                    String threeHex = Integer.toHexString(Integer.valueOf(threeByte, 2));
                    if (threeHex.length() < 2) {
                        // 前面补0
                        threeHex = "0" + threeHex;
                    }
                    advanceSetting = firstHex + "," + twoHex + "," + threeHex;
                    sendId = DDJSONUtil.getString(askData, NetKeyConstants.NET_KEY_SEND_ID);
                }
                mMessageIdMap.put(messageId, cmd);
                PanelManager.getInstance().getNetworkRequestManager().requestAskSirenSetting(
                        PanelManager.getInstance().getCurrentPanelToken(), messageId, sendId,
                        sType, advanceSetting, new PanelCallback.NetworkResult<StringResponseEntry>() {
                            @Override
                            public void onSuccess(StringResponseEntry result) {

                            }

                            @Override
                            public void onError(int errorCode, String errorMsg) {
                                dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                        + errorCode + ", errorMsg: " + errorMsg).createResult());
                            }
                        }
                );
            }
        }
    }

    private int getIndexWithValue(String value) {
        int volString = 0;
        if ("0".equals(value))
            volString = 0;

        if ("1".equals(value))
            volString = 1;

        if ("5".equals(value)) {
            volString = 2;
        }

        if ("10".equals(value)) {
            volString = 3;
        }
        return volString;
    }

    @Override
    protected void onDeviceResultEvent(DeviceResultEvent deviceResultEvent) {
        final String operatorCmd = deviceResultEvent.getCmdType();
        final String cmd = PanelOperator.transformFromPanelOperatorCmd(operatorCmd);

        switch (operatorCmd) {
            case PanelOperatorConstant.CMD.SET_WIRELESS_SIREN_ADVANCED_SETTING:
                try {
                    JSONObject jsonObject = new JSONObject(deviceResultEvent.getReslut());
                    String pluginId = jsonObject.getString("pluginid");
                    if (!getId().equals(pluginId)) {
                        return;
                    }
                    jsonObject.put(PanelDataKey.CmdResult.OPERATION_CMD, operatorCmd);
                    String sirenData = jsonObject.getString("plugin_item_wireless_siren_advanced_setting");
                    if (null != getInfo()) {
                        getInfo().put(PanelDataKey.WirelessSiren.SIREN_DATA, sirenData);
                    }
                    DDLog.i(TAG, "update sire data: " + deviceResultEvent.toString());
                    dispatchDeviceResult(cmd, deviceResultEvent.getStatus(), false, 1, jsonObject.toString());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                break;
            case PanelOperatorConstant.CMD.SET_NEWASKSIRENDATA:
                try {
                    JSONObject jsonObject = new JSONObject(deviceResultEvent.getReslut());
                    String sendId = jsonObject.getString("sendid");
                    String mSendId = DDJSONUtil.getString(DeviceHelper.getJsonObject(getInfo(),
                            PanelDataKey.ASK_DATA), "sendid");
                    if (TextUtils.isEmpty(sendId) || !sendId.equals(mSendId)) {
                        return;
                    }
                    jsonObject.put(PanelDataKey.CmdResult.OPERATION_CMD, operatorCmd);
                    JSONObject askData = DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA);
                    if (null != askData) {
                        askData.put("advancesetting", DDJSONUtil.getString(jsonObject, "advancesetting"));
                    }
                    DDLog.i(TAG, "update ask sire data: " + deviceResultEvent.toString());
                    dispatchDeviceResult(cmd, deviceResultEvent.getStatus(), false, 1, jsonObject.toString());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                break;
            default:
                if (mMessageIdMap.containsKey(messageId)) {
                    // 如果是自己操作的，直接返回
                    dispatchDeviceResult(cmd, deviceResultEvent.getStatus(), true, 1, deviceResultEvent.getReslut());
                    mMessageIdMap.remove(messageId);
                }
                break;
        }
    }
}
