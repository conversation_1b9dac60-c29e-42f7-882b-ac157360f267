package com.dinsafer.panel.bean.device;

import androidx.annotation.Keep;
import android.text.TextUtils;

import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelResultBuilder;
import com.dinsafer.panel.common.PluginCmd;
import com.dinsafer.panel.operate.callback.PanelCallback;

import org.json.JSONObject;

import java.util.Map;

/**
 * DoorBell/门铃
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/20 11:00 AM
 */
@Keep
public class DoorBellDevice extends BasePluginDevice {
    private static final String TAG = DoorBellDevice.class.getSimpleName();

    // TODO 初始化category/subcategory
    public DoorBellDevice(String id, int category, String subCategory, String fatherId) {
        super(id, category, subCategory, null, fatherId);
    }

    @Override
    public void submit(Map arg) {
        DDLog.i(TAG, "submit param: " + arg);
        if (null == arg || 0 >= arg.size()) {
            throw new IllegalArgumentException("submit param can not be null");
        }
        try {
            String cmd = (String) arg.get(PanelDataKey.CMD);
            if (TextUtils.isEmpty(cmd)) {
                DDLog.e(TAG, "Empty cmd");
                return;
            }
            if (!isCanOperate()) {
                if (submitOnDeviceDelete(cmd, arg)) {
                    return;
                }

                DDLog.e(TAG, this + "是已经被删除的状态，不支持CMD: " + cmd);
                dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                        + ErrorCode.DEVICE_NOT_BELONE_USER + ", errorMsg: " + "主机是已经被删除的状态，不支持CMD: " + cmd).createResult());
                return;
            }
            switch (cmd) {
                case PluginCmd.PLUGIN_DELETE:
                    PanelManager.getInstance().getNetworkRequestManager().requestDeleteDoorBell(
                            PanelManager.getInstance().getCurrentPanelToken(), getId(),
                            new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                    PanelManager.getInstance().removeCache(getFatherId(), getId());
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, result.getResult()).createResult());
                                    remove();
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PluginCmd.PLUGIN_SETNAME:
                    String newName = (String) arg.get(PanelDataKey.NAME);
                    JSONObject data = DeviceHelper.getJsonObject(getInfo(), PanelDataKey.ASK_DATA);
                    if (null != data) {
                        data.put("name", newName);
                    }
                    PanelManager.getInstance().getNetworkRequestManager().requestModifyDoorBell(
                            PanelManager.getInstance().getCurrentPanelToken(), data,
                            new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, result.getResult()).createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                default:
                    DDLog.e(TAG, "Not support cmd: " + cmd);
                    break;
            }
        } catch (Exception e) {
            DDLog.i(TAG, "Error on submit");
            e.printStackTrace();
        }
    }
}
