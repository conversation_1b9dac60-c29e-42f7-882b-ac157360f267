package com.dinsafer.panel.bean.device;

import static com.dinsafer.panel.PanelManager.HOME_STYPE_ALL;

import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.RestrictTo;

import com.dinsafer.dincore.common.DeivceChangeEvent;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.common.NetKeyConstants;
import com.dinsafer.dincore.db.DBKey;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dincore.utils.RandomStringUtils;
import com.dinsafer.dssupport.msctlib.db.KV;
import com.dinsafer.dssupport.plugin.PluginConstants;
import com.dinsafer.dssupport.plugin.PluginTypeHelper;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.bean.CustomizeHomeArmResult;
import com.dinsafer.panel.bean.DoorSensorResult;
import com.dinsafer.panel.bean.HomePluginResult;
import com.dinsafer.panel.bean.PanelCacheInfo;
import com.dinsafer.panel.bean.SecurityPluginResult;
import com.dinsafer.panel.bean.SimplePluginResult;
import com.dinsafer.panel.common.PanelCmd;
import com.dinsafer.panel.common.PanelConstant;
import com.dinsafer.panel.common.PanelDataKey;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.panel.common.PanelResultBuilder;
import com.dinsafer.panel.operate.PanelOperator;
import com.dinsafer.panel.operate.PanelOperatorConstant;
import com.dinsafer.panel.operate.bean.AppMessageEntry;
import com.dinsafer.panel.operate.bean.CmsProtocolModel;
import com.dinsafer.panel.operate.bean.CommonAccessoriesBean;
import com.dinsafer.panel.operate.bean.ContactIdResponseEntry;
import com.dinsafer.panel.operate.bean.EventListSettingEntry;
import com.dinsafer.panel.operate.bean.FourGInfoEntry;
import com.dinsafer.panel.operate.bean.GetAdvancedSettingResult;
import com.dinsafer.panel.operate.bean.GetCareModeStatusResponse;
import com.dinsafer.panel.operate.bean.HomePluginEntry;
import com.dinsafer.panel.operate.bean.HomePluginQuantityEntry;
import com.dinsafer.panel.operate.bean.PanelInfoNew;
import com.dinsafer.panel.operate.bean.ReadyToArmSwitchStatusEntry;
import com.dinsafer.panel.operate.bean.SimDataEntry;
import com.dinsafer.panel.operate.bean.SosMessageEntry;
import com.dinsafer.panel.operate.bean.SosStatusEntry;
import com.dinsafer.panel.operate.bean.TimePickerEntry;
import com.dinsafer.panel.operate.bean.TimeZoneResponseEntry;
import com.dinsafer.panel.operate.bean.TuyaItemPlus;
import com.dinsafer.panel.operate.bean.UnCloseDoorEntry;
import com.dinsafer.panel.operate.bean.event.DeviceCmdAckEvent;
import com.dinsafer.panel.operate.bean.event.DeviceEventListEvent;
import com.dinsafer.panel.operate.bean.event.DeviceResetOrDeletedEvent;
import com.dinsafer.panel.operate.bean.event.DeviceResultEvent;
import com.dinsafer.panel.operate.bean.event.DeviceSimStatueEvent;
import com.dinsafer.panel.operate.bean.event.EventListDataFixTime;
import com.dinsafer.panel.operate.bean.event.EventListTimeOutCheck;
import com.dinsafer.panel.operate.bean.event.OfflineEvent;
import com.dinsafer.panel.operate.bean.event.PingUpdataEvent;
import com.dinsafer.panel.operate.bean.event.ShowBlockToastEvent;
import com.dinsafer.panel.operate.bean.event.UserNetworkEvent;
import com.dinsafer.panel.operate.bean.event.WebSocketEvent;
import com.dinsafer.panel.operate.bean.param.CustomizeHomeArmParams;
import com.dinsafer.panel.operate.callback.PanelCallback;
import com.dinsafer.panel.operate.task.CheckTimeOutTask;
import com.dinsafer.panel.operate.task.DeviceWorkQueue;
import com.dinsafer.panel.util.PanelPluginUtil;
import com.dinsafer.panel.util.PluginInfoQueryHelper;
import com.dinsafer.panel.util.PluginStateRoundRobinUtil;
import com.google.gson.Gson;

import org.greenrobot.eventbus.EventBus;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 主机信息
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/14 2:13 PM
 */
@Keep
public class PanelDevice extends Device {
    private static final String TAG = "PanelDevice";

    private String panelToken; // 主机Token
    private PanelInfoNew mCurrentPanel; // 当前主机信息
    private HomePluginQuantityEntry.PluginBean mPluginQuantityInfo; // 主机配件数量信息
    private HomePluginEntry mHomePluginInfo; // 主机首页配件信息
    private final Map<String, Device> mPluginDeviceMap = new HashMap<>();
    private final Set<String> mSmartButtonIds = new HashSet<>();
    private final Set<String> mSmartPlugIds = new HashSet<>();
    private final Set<String> mSignalRepeaterPlugIds = new HashSet<>();
    private final Set<String> mRelayIds = new HashSet<>();
    private final Set<String> mSecurityIds = new HashSet<>();
    private final Set<String> mDoorSensorIds = new HashSet<>();
    private final Set<String> mWirelessSirenIds = new HashSet<>();
    private final Set<String> mKeypadIds = new HashSet<>();
    private final Set<String> mRemoteControlIds = new HashSet<>();
    private final Set<String> mDoorbellIds = new HashSet<>();
    private final Set<String> mOtherPluginIds = new HashSet<>();
    private final Set<String> mHomePluginIds = new HashSet<>();
    private final Map<String, String> mMessageIdMap = new HashMap<>();
    private PluginStateRoundRobinUtil mPluginStateQueryUtil;
    private PluginInfoQueryHelper mPluginInfoQueryHelper;

    private int lastArmState = PanelOperatorConstant.KEY.DISARM_STATUS;
    private long mLastCmdTime;

    public PanelDevice(String id, final String token, PanelInfoNew panelInfo, final boolean isCache) {
        super(id, 0, null, null);
        initFlagOnNetwork();
        setFlagCache(isCache);
        setFlagLoaded(true);
        this.panelToken = token;
        this.mCurrentPanel = panelInfo;
        mPluginStateQueryUtil = new PluginStateRoundRobinUtil(token,
                UserManager.getInstance().getUser().getUid());
        mPluginInfoQueryHelper = new PluginInfoQueryHelper(this);
        updateInfoMap(true);
    }

    public PanelDevice(PanelCacheInfo cacheInfo) {
        super(cacheInfo.getPanelId(), 0, null, null);
        initFlagOnReadCache();
        setFlagDeleted(true);
        setFlagLoaded(true);
        mPluginInfoQueryHelper = new PluginInfoQueryHelper(this);
    }

    public static PanelDevice createFromDeletedInfo(PanelCacheInfo cacheInfo) {
        return new PanelDevice(cacheInfo);
    }

    public static PanelDevice createFromPanelInfo(String panelToken, PanelInfoNew panelInfo,
                                                  final boolean isCache) {
        if (null == panelInfo
                || TextUtils.isEmpty(panelInfo.getDevice_id())) {
            return null;
        }
        return new PanelDevice(panelInfo.getDevice_id(), panelToken, panelInfo, isCache);
    }

    public void setPanelToken(String panelToken) {
        this.panelToken = panelToken;
    }

    @Override
    public void destory() {
        if (null != mPluginStateQueryUtil && isCanOperate())
            mPluginStateQueryUtil.stopQueryPluginStatusRoundRobin();
        for (Device device : mPluginDeviceMap.values()) {
            device.destory();
        }
        // super.destory();
    }

    @Override
    public void submit(Map arg) {
        DDLog.i(TAG, "submit param: " + arg);
        if (null == arg || 0 >= arg.size()) {
            throw new IllegalArgumentException("submit param can not be null");
        }
        final String messageId;
        try {
            String cmd = (String) arg.get(PanelDataKey.CMD);
            if (TextUtils.isEmpty(cmd)) {
                DDLog.e(TAG, "Empty cmd");
                dispatchResult("EMPTY_CMD", PanelResultBuilder.error("EMPTY_CMD", true, "code: "
                        + ErrorCode.PARAM_ERROR + ", errorMsg: " + "空的CMD").createResult());
                return;
            }
            if (!isCanOperate()) {
                if (submitOnDeviceDelete(cmd, arg)) {
                    return;
                }

                DDLog.e(TAG, "主机是已经被删除的状态，不支持CMD: " + cmd);
                dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                        + ErrorCode.DEVICE_NOT_BELONE_USER + ", errorMsg: " + "主机是已经被删除的状态，不支持CMD: " + cmd).createResult());
                return;
            }

            switch (cmd) {
                case PanelCmd.OPERATION_ARM:
                    int armState = (int) arg.get(PanelDataKey.ARM_STATUS);
                    boolean force = (boolean) arg.get(PanelDataKey.FORCE);
                    if (PanelParamsHelper.OPERATE_ARM_ARM == armState) {
                        messageId = PanelManager.getInstance().getPanelOperator().sendCmdArm(force);
                    } else if (PanelParamsHelper.OPERATE_ARM_DISARM == armState) {
                        messageId = PanelManager.getInstance().getPanelOperator().sendCmdDisArm(force);
                    } else if (PanelParamsHelper.OPERATE_ARM_HOME_ARM == armState) {
                        messageId = PanelManager.getInstance().getPanelOperator().sendCmdHomeArm(force);
                    } else {
                        messageId = null;
                        DDLog.e(TAG, "Not support yet.");
                    }
                    if (null != messageId) {
                        mMessageIdMap.put(messageId, cmd);
                    }
                    break;
                case PanelCmd.OPERATION_SOS:
                    messageId = PanelManager.getInstance().getPanelOperator().sendCmdSos();
                    mMessageIdMap.put(messageId, cmd);
                    break;
                case PanelCmd.GET_SIM_CARD_INFO:
                    PanelManager.getInstance().getNetworkRequestManager().requestGetSimData(getId(),
                            new PanelCallback.NetworkResult<SimDataEntry>() {
                                @Override
                                public void onSuccess(SimDataEntry result) {
                                    Map<String, Object> resultMap = new HashMap<>();
                                    resultMap.put(PanelDataKey.SimInfo.D_PHONE, result.getResult().getD_phone());
                                    resultMap.put(PanelDataKey.SimInfo.SIM, result.getResult().getSim());
                                    resultMap.put(PanelDataKey.SimInfo.IMEI, result.getResult().getImei());
                                    resultMap.put(PanelDataKey.SimInfo.IMSI, result.getResult().getImsi());
                                    resultMap.put(PanelDataKey.SimInfo.CSQ, result.getResult().getCsq());
                                    resultMap.put(PanelDataKey.SimInfo.PIN, result.getResult().getPin());
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, resultMap).createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            });
                    break;
                case PanelCmd.RESTRICT_SMS_STRING:
                    int armType = (int) arg.get(PanelDataKey.ARM_TYPE);
                    // TODO RESTRICT_SMS_STRING
                    break;
                case PanelCmd.GET_EXITDELAY:
                    PanelManager.getInstance().getNetworkRequestManager().requestGetExitDelayTime(getId(),
                            new PanelCallback.NetworkResult<TimePickerEntry>() {
                                @Override
                                public void onSuccess(TimePickerEntry result) {
                                    Map<String, Object> resultMap = new HashMap<>();
                                    resultMap.put(PanelDataKey.TIME_COMMON, result.getResult().getTime());
                                    resultMap.put(PanelDataKey.EXIT_DELAY_SOUND, result.getResult().isExitdelaysound());
                                    resultMap.put(PanelDataKey.OPTIONS, result.getResult().getOptions());
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, resultMap).createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            });
                    break;
                case PanelCmd.SET_EXITDELAY:
                    int time = (int) arg.get(PanelDataKey.TIME_COMMON);
                    boolean soundEnable = (boolean) arg.get(PanelDataKey.SOUND_ENABLE_EXIT_DELAY);
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    PanelManager.getInstance().getNetworkRequestManager().requestChangeExitDelayTime(panelToken,
                            messageId, time, soundEnable, new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                    // WebSocket返回
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    mMessageIdMap.remove(messageId);
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.GET_ENTRYDELAY:
                    PanelManager.getInstance().getNetworkRequestManager().requestEntryDelayResult(getId(),
                            new PanelCallback.NetworkResult<CustomizeHomeArmResult>() {
                                @Override
                                public void onSuccess(CustomizeHomeArmResult result) {
                                    Map<String, Object> resultMap = new HashMap<>();
                                    resultMap.put(PanelDataKey.TIME_COMMON, result.getTime());
                                    resultMap.put(PanelDataKey.OPTIONS, result.getOptions());
                                    resultMap.put(PanelDataKey.ENTRY_DELAY_SOUND, result.isEntrydelaysound());
                                    resultMap.put(PanelDataKey.PLUGINS, result.getOfficialPlugin());
                                    resultMap.put(PanelDataKey.THIRD_PARTY_PLUGINS, result.getThirdPartPlugin());
                                    resultMap.put(PanelDataKey.NEW_ASK_PLUGIN, result.getAskPlugin());
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, resultMap).createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            });
                    break;
                case PanelCmd.SET_ENTRYDELAY:
                    time = (int) arg.get(PanelDataKey.TIME_COMMON);
                    boolean entryDelaySound = (boolean) arg.get(PanelDataKey.ENTRY_DELAY_SOUND);
                    String plugins = (String) arg.get(PanelDataKey.DATAS_ENTRY_DELAY);
                    String thirdPartPlugins = (String) arg.get(PanelDataKey.THIRD_PARTY_DATAS_ENTRY_DELAY);
                    String askPlugs = (String) arg.get(PanelDataKey.NEW_ASK_DATAS_ENTRY_DELAY);
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);

                    PanelManager.getInstance().getNetworkRequestManager().requestConfirmCustomizeHomeArm(getId(), panelToken,
                            new CustomizeHomeArmParams(messageId, plugins, askPlugs, thirdPartPlugins, time, entryDelaySound),
                            new PanelCallback.NetworkResult<String>() {
                                @Override
                                public void onSuccess(String result) {
                                    // webSocket返回
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            });
                    break;
                case PanelCmd.GET_SIRENTIME:
                    PanelManager.getInstance().getNetworkRequestManager().requestGetSirenTime(getId(),
                            new PanelCallback.NetworkResult<TimePickerEntry>() {
                                @Override
                                public void onSuccess(TimePickerEntry result) {
                                    Map<String, Object> resultMap = new HashMap<>();
                                    resultMap.put(PanelDataKey.TIME_COMMON, result.getResult().getTime());
                                    resultMap.put(PanelDataKey.EXIT_DELAY_SOUND, result.getResult().isExitdelaysound());
                                    resultMap.put(PanelDataKey.OPTIONS, result.getResult().getOptions());
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, resultMap).createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            });
                    break;
                case PanelCmd.SET_SIRENTIME:
                    time = (int) arg.get(PanelDataKey.TIME_COMMON);
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    PanelManager.getInstance().getNetworkRequestManager().requestChangeSirenTime(panelToken,
                            messageId, time, new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                    // WebSocket返回
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    mMessageIdMap.remove(messageId);
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.GET_HOMEARM_INFO:
                    PanelManager.getInstance().getNetworkRequestManager().requestCustomizeHomeArmResult(getId(),
                            new PanelCallback.NetworkResult<CustomizeHomeArmResult>() {
                                @Override
                                public void onSuccess(CustomizeHomeArmResult result) {
                                    Map<String, Object> resultMap = new HashMap<>();
                                    resultMap.put(PanelDataKey.TIME_COMMON, result.getTime());
                                    resultMap.put(PanelDataKey.OPTIONS, result.getOptions());
                                    resultMap.put(PanelDataKey.PLUGINS, result.getOfficialPlugin());
                                    resultMap.put(PanelDataKey.THIRD_PARTY_PLUGINS, result.getThirdPartPlugin());
                                    resultMap.put(PanelDataKey.NEW_ASK_PLUGIN, result.getAskPlugin());
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, resultMap).createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            });
                    break;
                case PanelCmd.SET_HOMEARM_INFO:
                    plugins = (String) arg.get(PanelDataKey.DATAS_ENTRY_DELAY);
                    thirdPartPlugins = (String) arg.get(PanelDataKey.THIRD_PARTY_DATAS_ENTRY_DELAY);
                    askPlugs = (String) arg.get(PanelDataKey.NEW_ASK_DATAS_ENTRY_DELAY);
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);

                    PanelManager.getInstance().getNetworkRequestManager().requestConfirmCustomizeHomeArm(getId(),
                            panelToken,
                            new CustomizeHomeArmParams(messageId, plugins, askPlugs, thirdPartPlugins),
                            new PanelCallback.NetworkResult<String>() {
                                @Override
                                public void onSuccess(String result) {
                                    // webSocket返回
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            });
                    break;
                case PanelCmd.GET_DURESS_INFO:
                    PanelManager.getInstance().getNetworkRequestManager().requestCurrentPanelIntimidateSosState(
                            getId(), panelToken,
                            new PanelCallback.NetworkResult<SosMessageEntry>() {
                                @Override
                                public void onSuccess(SosMessageEntry result) {
                                    Map<String, Object> resultMap = new HashMap<>();
                                    resultMap.put(PanelDataKey.IntimidateSos.ENABLE, result.getResult().isEnable());
                                    resultMap.put(PanelDataKey.IntimidateSos.HAD_SET_PASSWORD, result.getResult().isPassword());
                                    resultMap.put(PanelDataKey.IntimidateSos.SMS, result.getResult().getSms());
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, resultMap).createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.INIT_DURESS_INFO:
                    String sms = (String) arg.get(PanelDataKey.IntimidateSos.SMS);
                    String password = (String) arg.get(PanelDataKey.IntimidateSos.PASSWORD);
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    PanelManager.getInstance().getNetworkRequestManager().requestOpenPanelIntimidateSosFirst(
                            panelToken, messageId, sms, password, new PanelCallback.NetworkResult<String>() {
                                @Override
                                public void onSuccess(String result) {
                                    // webSocket返回
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }

                    );
                    break;
                case PanelCmd.ENABLE_DURESS:
                    boolean enable = (Boolean) arg.get(PanelDataKey.IntimidateSos.ENABLE);
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    PanelManager.getInstance().getNetworkRequestManager().requestChangePanelIntimidateSosState(
                            panelToken, messageId, enable, new PanelCallback.NetworkResult<String>() {
                                @Override
                                public void onSuccess(String result) {
                                    // webSocket返回
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }

                    );
                    break;
                case PanelCmd.SET_DURESS_PASSWORD:
                    password = (String) arg.get(PanelDataKey.IntimidateSos.PASSWORD);
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    PanelManager.getInstance().getNetworkRequestManager().requestChangePanelIntimidateSosPassword(
                            panelToken, messageId, password, new PanelCallback.NetworkResult<String>() {
                                @Override
                                public void onSuccess(String result) {
                                    // webSocket返回
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }

                    );
                    break;
                case PanelCmd.SET_DURESS_SMS:
                    sms = (String) arg.get(PanelDataKey.IntimidateSos.SMS);
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    PanelManager.getInstance().getNetworkRequestManager().requestChangePanelIntimidateSosMessage(
                            panelToken, messageId, sms, new PanelCallback.NetworkResult<String>() {
                                @Override
                                public void onSuccess(String result) {
                                    // webSocket返回
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }

                    );
                    break;
                case PanelCmd.DELETE_PANEL:
                    PanelManager.getInstance().getNetworkRequestManager().requestDeletePanel(
                            getId(), new PanelCallback.NetworkResult<String>() {
                                @Override
                                public void onSuccess(String result) {
                                    DDLog.i(TAG, "成功删除离线主机");
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, "").createResult());
                                    KV.remove(DBKey.CURRENT_DEVICE_ID);
                                    EventBus.getDefault().post(new DeviceResetOrDeletedEvent(getId()));
                                    PanelManager.getInstance().getPanelOperator().toCloseWs();
                                    PanelManager.getInstance().releaseAllDevices();
                                    PanelManager.getInstance().resetDeviceInfo();
                                    PanelManager.getInstance().clearDeviceCache();
                                    remove();
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.RENAME_PANEL:
                    String panelName = (String) arg.get(PanelDataKey.Panel.NAME);
                    PanelManager.getInstance().getNetworkRequestManager().requestChangePanelName(
                            getId(), panelName, new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                    if (null != getInfo()) {
                                        getInfo().put(PanelDataKey.Panel.NAME, panelName);
                                    }
                                    PanelManager.getInstance().setCurrentPanelName(panelName);
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, "").createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.RESET_PANEL:
                    password = (String) arg.get(PanelDataKey.Panel.PASSWORD);
                    boolean retainPlugins = (boolean) arg.get(PanelDataKey.Panel.RETAIN_PLUGINS);
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    PanelManager.getInstance().getNetworkRequestManager().requestResetPanel(
                            panelToken, messageId, password, retainPlugins,
                            new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.GET_PANEL_EVENTLIST:
                    long timestamp = (Long) arg.get(PanelDataKey.TIMESTAMP);
                    String filters = (String) arg.get(PanelDataKey.FILTERS);
                    PanelManager.getInstance().getNetworkRequestManager().requestGetEventList(
                            getId(), panelToken, 20, timestamp, filters,
                            new PanelCallback.NetworkResult<List<Map<String, Object>>>() {
                                @Override
                                public void onSuccess(List<Map<String, Object>> result) {
                                    Map<String, Object> resultMap = new HashMap<>();
                                    resultMap.put(PanelDataKey.EventList.EVENT_LIST, result);
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, resultMap).createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.GET_EVENTLIST_SETTING:
                    PanelManager.getInstance().getNetworkRequestManager().requestGetEventListSetting(
                            getId(), new PanelCallback.NetworkResult<EventListSettingEntry>() {
                                @Override
                                public void onSuccess(EventListSettingEntry result) {
                                    Map<String, Object> temp = new HashMap<>();
                                    temp.put(PanelDataKey.EventList.dw_event_log, result.getResult().isDw_event_log());
                                    temp.put(PanelDataKey.EventList.TAMPER_EVENT_LOG, result.getResult().isTamper_event_log());
                                    Map<String, Object> resultMap = new HashMap<>();
                                    resultMap.put(PanelDataKey.EventList.EVENT_LIST_SETTING, temp);
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, resultMap).createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.SET_EVENTLIST_SETTING:
                    boolean dwLog = (boolean) arg.get(PanelDataKey.EventList.DOOR_WINDOW);
                    boolean tamperLog = (boolean) arg.get(PanelDataKey.EventList.TAMPER);
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    PanelManager.getInstance().getNetworkRequestManager().requestUpdateEventListSetting(
                            panelToken, messageId, dwLog, tamperLog,
                            new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.GET_PANEL_SOSINFO:
                    PanelManager.getInstance().getNetworkRequestManager().requestGetSosStatus(getId(),
                            new PanelCallback.NetworkResult<SosStatusEntry>() {
                                @Override
                                public void onSuccess(SosStatusEntry result) {
                                    Map<String, Object> resultMap = new HashMap<>();
                                    resultMap.put(PanelDataKey.SosInfo.SOS_ALARM, result.getResult().isSosalarm());
                                    resultMap.put(PanelDataKey.SosInfo.IS_DEVICE, result.getResult().isdevice());
                                    resultMap.put(PanelDataKey.SosInfo.TIME, result.getResult().getTime());
                                    resultMap.put(PanelDataKey.SosInfo.UID, result.getResult().getUid());
                                    resultMap.put(PanelDataKey.SosInfo.PHOTO, result.getResult().getPhoto());
                                    resultMap.put(PanelDataKey.SosInfo.PLUGIN_ID, result.getResult().getPluginid());
                                    resultMap.put(PanelDataKey.SosInfo.PLUGIN_NAME, result.getResult().getPluginname());
                                    resultMap.put(PanelDataKey.SosInfo.INTIMIDATION_MESSAGE, result.getResult().getIntimidationmessage());
                                    resultMap.put(PanelDataKey.SosInfo.SUBCATEGORY, result.getResult().getSubcategory());
                                    resultMap.put(PanelDataKey.SosInfo.CATEGORY, result.getResult().getCategory());
                                    resultMap.put(PanelDataKey.SosInfo.SOS_TYPE, result.getResult().getSostype());
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, resultMap).createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.GET_PANEL_CIDDATA:
                    PanelManager.getInstance().getNetworkRequestManager().requestGetContactId(getId(),
                            new PanelCallback.NetworkResult<ContactIdResponseEntry>() {
                                @Override
                                public void onSuccess(ContactIdResponseEntry result) {
                                    Map<String, Object> resultMap = new HashMap<>();
                                    resultMap.put(PanelDataKey.Cid.ENABLE, result.getResult().isEnable());
                                    resultMap.put(PanelDataKey.Cid.CONTACT_ID_CODE, result.getResult().getContactidcode());
                                    resultMap.put(PanelDataKey.Cid.COUNTRY_CODE, result.getResult().getCountrycode());
                                    resultMap.put(PanelDataKey.Cid.PHONE, result.getResult().getPhone());
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, resultMap).createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.SET_PANEL_CIDDATA:
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    enable = (boolean) arg.get(PanelDataKey.Cid.ENABLE);
                    String contactIdCode = (String) arg.get(PanelDataKey.Cid.CONTACT_ID_CODE);
                    String countryCode = (String) arg.get(PanelDataKey.Cid.COUNTRY_CODE);
                    String phone = (String) arg.get(PanelDataKey.Cid.PHONE);
                    PanelManager.getInstance().getNetworkRequestManager().requestChangeContactIdCall(
                            panelToken, messageId, enable, contactIdCode, countryCode, phone,
                            new PanelCallback.NetworkResult<String>() {
                                @Override
                                public void onSuccess(String result) {
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.GET_TIMEZONE:
                    PanelManager.getInstance().getNetworkRequestManager().requestGetPanelTimeZone(getId(),
                            new PanelCallback.NetworkResult<TimeZoneResponseEntry>() {
                                @Override
                                public void onSuccess(TimeZoneResponseEntry result) {
                                    Map<String, Object> resultMap = new HashMap<>();
                                    resultMap.put(PanelDataKey.Timezone.TIMEZONE, result.getResult().getTimezone());
                                    resultMap.put(PanelDataKey.Timezone.TIMEZONE_LIST, result.getResult().getTimezonelist());
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, resultMap).createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.SET_TIMEZONE:
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    String setTimeZone = (String) arg.get(PanelDataKey.Timezone.TIMEZONE);
                    PanelManager.getInstance().getTimeoutChecker().addTask(new CheckTimeOutTask(cmd, messageId));
                    PanelManager.getInstance().getNetworkRequestManager().requestChangePanelTimeZone(
                            panelToken, messageId, setTimeZone,
                            new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                    // webSocket返回
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    PanelManager.getInstance().getTimeoutChecker().removeTaskById(messageId);
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.GET_READYTOARM_STATUS:
                    PanelManager.getInstance().getNetworkRequestManager().requestCurrentReadyToArmStatus(
                            getId(), new PanelCallback.NetworkResult<ReadyToArmSwitchStatusEntry>() {
                                @Override
                                public void onSuccess(ReadyToArmSwitchStatusEntry result) {
                                    Map<String, Object> resultMap = new HashMap<>();
                                    resultMap.put(PanelDataKey.ReadyToArm.URL, result.getResult().getUrl());
                                    resultMap.put(PanelDataKey.ReadyToArm.COUNT, result.getResult().getCount());
                                    resultMap.put(PanelDataKey.ReadyToArm.ENABLE, result.getResult().isEnable());
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, resultMap).createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.SET_READYTOARM_STATUS:
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    enable = (boolean) arg.get(PanelDataKey.ReadyToArm.ENABLE);
                    PanelManager.getInstance().getNetworkRequestManager().requestChangeReadyToArmState(
                            panelToken, messageId, enable,
                            new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                    // webSocket返回
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.GET_ADVANCED_SETTING:
                    PanelManager.getInstance().getNetworkRequestManager().requestPanelAdvancedSetting(
                            getId(), new PanelCallback.NetworkResult<GetAdvancedSettingResult>() {
                                @Override
                                public void onSuccess(GetAdvancedSettingResult result) {
                                    Map<String, Object> resultMap = new HashMap<>();
                                    resultMap.put(PanelDataKey.AdvancedSetting.IS_ON, result.getResult().isIson());
                                    resultMap.put(PanelDataKey.AdvancedSetting.OFFLINE_SMS, result.getResult().isOffline_sms());
                                    resultMap.put(PanelDataKey.AdvancedSetting.PANEL_NAME, result.getResult().getName());
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, resultMap).createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.SET_ARM_SOUND:
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    boolean on = (boolean) arg.get(PanelDataKey.ArmSoundSetting.ON);
                    PanelManager.getInstance().getNetworkRequestManager().requestChangePlaySoundSetting(
                            panelToken, messageId, on,
                            new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                    // webSocket返回
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.SET_RESTRICT_MODE:
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    on = (boolean) arg.get(PanelDataKey.RestrictMode.ON);
                    PanelManager.getInstance().getNetworkRequestManager().requestChangeRestrictModeState(
                            panelToken, messageId, on,
                            new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                    // webSocket返回
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.OPEN_PANEL_BLUETOOTH:
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    on = (boolean) arg.get(PanelDataKey.ON);
                    PanelManager.getInstance().getNetworkRequestManager().requestCallDeviceOpenBle(
                            panelToken, messageId, on,
                            new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, "").createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.SET_PANEL_PASSWORD:
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    password = (String) arg.get(PanelDataKey.Panel.PASSWORD);
                    String old_password = (String) arg.get(PanelDataKey.Panel.OLD_PASSWORD);
                    PanelManager.getInstance().getNetworkRequestManager().requestChangePanelPasswordCall(
                            panelToken, messageId, old_password, password,
                            new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                    // webSocket返回
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.GET_4G_INFO:
                    PanelManager.getInstance().getNetworkRequestManager().requestGet4GInfo(
                            getId(), new PanelCallback.NetworkResult<FourGInfoEntry>() {
                                @Override
                                public void onSuccess(FourGInfoEntry result) {
                                    Map<String, Object> resultMap = new HashMap<>();
                                    resultMap.put(PanelDataKey.Panel4GInfo.AUTOMATIC, result.getResult().isAutomatic());
                                    resultMap.put(PanelDataKey.Panel4GInfo.NODE_NAME, result.getResult().getNodeName());
                                    resultMap.put(PanelDataKey.Panel4GInfo.PASSWORD, result.getResult().getPassword());
                                    resultMap.put(PanelDataKey.Panel4GInfo.USER_NAME, result.getResult().getUserName());
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, resultMap).createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.SET_4G_INFO:
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    boolean automatic = (boolean) arg.get(PanelDataKey.Panel4GInfo.AUTOMATIC);
                    String node_name = (String) arg.get(PanelDataKey.Panel4GInfo.NODE_NAME);
                    password = (String) arg.get(PanelDataKey.Panel4GInfo.PASSWORD);
                    String userName = (String) arg.get(PanelDataKey.Panel4GInfo.USER_NAME);
                    FourGInfoEntry.ResultBean params = new FourGInfoEntry.ResultBean(automatic, node_name, userName, password);
                    PanelManager.getInstance().getNetworkRequestManager().requestSet4GInfo(
                            panelToken, messageId, params,
                            new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.GET_CMS_INFO:
                    PanelManager.getInstance().getNetworkRequestManager().requestGetCmsData(
                            getId(), new PanelCallback.NetworkResult<List<Map<String, Object>>>() {
                                @Override
                                public void onSuccess(List<Map<String, Object>> result) {
                                    Map<String, Object> resultMap = new HashMap<>();
                                    resultMap.put(PanelDataKey.Cms.DATAS, result);
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, resultMap).createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.SET_CMS_INFO:
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    String protocolName = (String) arg.get(PanelDataKey.Cms.PROTOCOL_NAME);
                    String info = (String) arg.get(PanelDataKey.Cms.INFO);
                    CmsProtocolModel protocolModel = new CmsProtocolModel(protocolName, info);
                    PanelManager.getInstance().getNetworkRequestManager().requestModifyCms(
                            panelToken, messageId, protocolModel,
                            new PanelCallback.NetworkResult<String>() {
                                @Override
                                public void onSuccess(String result) {
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.GET_CAREMODE:
                    PanelManager.getInstance().getNetworkRequestManager().requestCareModeData(
                            getId(), panelToken, new PanelCallback.NetworkResult<String>() {
                                @Override
                                public void onSuccess(String result) {
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, result).createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.SET_CAREMODE:
                    messageId = RandomStringUtils.getMessageId();
                    if (arg.containsKey(PanelDataKey.ON)) {
                        on = (boolean) arg.get(PanelDataKey.ON);
                        mMessageIdMap.put(messageId, cmd);
                        PanelManager.getInstance().getNetworkRequestManager().requestModifyCareMode(
                                panelToken, messageId, on, new PanelCallback.NetworkResult<StringResponseEntry>() {
                                    @Override
                                    public void onSuccess(StringResponseEntry result) {

                                    }

                                    @Override
                                    public void onError(int errorCode, String errorMsg) {
                                        dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                                + errorCode + ", errorMsg: " + errorMsg).createResult());
                                    }
                                }
                        );
                    } else if (arg.containsKey(PanelDataKey.CareMode.NO_ACTION_TIME)) {
                        int noActionTime = (int) arg.get(PanelDataKey.CareMode.NO_ACTION_TIME);
                        mMessageIdMap.put(messageId, cmd);
                        PanelManager.getInstance().getNetworkRequestManager().requestModifyCareModeNoActionTime(
                                panelToken, messageId, noActionTime,
                                new PanelCallback.NetworkResult<StringResponseEntry>() {
                                    @Override
                                    public void onSuccess(StringResponseEntry result) {
                                    }

                                    @Override
                                    public void onError(int errorCode, String errorMsg) {
                                        dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                                + errorCode + ", errorMsg: " + errorMsg).createResult());
                                    }
                                }
                        );
                    } else if (arg.containsKey(PanelDataKey.CareMode.ALARM_DELAY_TIME)) {
                        int alarmDelayTime = (int) arg.get(PanelDataKey.CareMode.ALARM_DELAY_TIME);
                        mMessageIdMap.put(messageId, cmd);
                        PanelManager.getInstance().getNetworkRequestManager().requestModifyCareModeAlarmTime(
                                panelToken, messageId, alarmDelayTime,
                                new PanelCallback.NetworkResult<StringResponseEntry>() {
                                    @Override
                                    public void onSuccess(StringResponseEntry result) {
                                    }

                                    @Override
                                    public void onError(int errorCode, String errorMsg) {
                                        dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                                + errorCode + ", errorMsg: " + errorMsg).createResult());
                                    }
                                }
                        );
                    }
                    break;
                case PanelCmd.SET_CAREMODE_PLUGINS:
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    plugins = (String) arg.get(PanelDataKey.CareMode.PLUGINS);
                    PanelManager.getInstance().getNetworkRequestManager().requestModifyCareModePlugin(
                            panelToken, messageId, plugins, new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.CARE_MODE_CANCEL_SOS:
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    PanelManager.getInstance().getNetworkRequestManager().requestCancelCareModeNoAction(
                            panelToken, messageId,
                            new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.CAREMODE_NOACTION_SOS:
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    PanelManager.getInstance().getNetworkRequestManager().requestCareModeNoActionSos(
                            panelToken, messageId,
                            new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.GET_UNCLOSE_PLUGS:
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    String taskType = (String) arg.get(PanelDataKey.ReadyToArm.TASK);
                    PanelManager.getInstance().getNetworkRequestManager().requestNotClosedDoorListData(
                            panelToken, messageId, taskType, new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.GET_EXCEPTION_PLUGS:
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    PanelManager.getInstance().getNetworkRequestManager().requestHomeExceptionAccessoryInfo(
                            panelToken, messageId, new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.GET_PANEL_MESSAGE:
                    PanelManager.getInstance().getNetworkRequestManager().requestPanelNotificationLanguage(
                            getId(), new PanelCallback.NetworkResult<AppMessageEntry>() {
                                @Override
                                public void onSuccess(AppMessageEntry result) {
                                    Map<String, Object> resultMap = new HashMap<>();
                                    resultMap.put(PanelDataKey.PushLanguage.DEVICE_TEXT, result.getResult().getDevice_text());
                                    resultMap.put(PanelDataKey.PushLanguage.LANG, result.getResult().getLang());
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true, resultMap).createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.SET_MESSAGE_LANGUAGE:
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    String langId = (String) arg.get(PanelDataKey.PushLanguage.LANG_ID);
                    PanelManager.getInstance().getNetworkRequestManager().requestChangePanelNotificationLanguage(
                            panelToken, null, messageId, langId,
                            new PanelCallback.NetworkResult<String>() {
                                @Override
                                public void onSuccess(String result) {
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.SET_MESSAGE_TEMPLATE:
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    langId = (String) arg.get(PanelDataKey.PushLanguage.LANG_ID);
                    String message = (String) arg.get(PanelDataKey.PushLanguage.MESSAGE);
                    PanelManager.getInstance().getNetworkRequestManager().requestChangePanelNotificationLanguage(
                            panelToken, message, messageId, langId,
                            new PanelCallback.NetworkResult<String>() {
                                @Override
                                public void onSuccess(String result) {
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.GET_PLUGSANDMEMBERS:
                    PanelManager.getInstance().getNetworkRequestManager().requestGetPluginQuantityInfo(
                            PanelManager.getInstance().getCurrentHomeId(), getId(),
                            new PanelCallback.NetworkResult<HomePluginQuantityEntry.PluginBean>() {
                                @Override
                                public void onSuccess(HomePluginQuantityEntry.PluginBean result) {
                                    mPluginQuantityInfo = result;
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true,
                                            parsePluginQuantityEntryToMap(result)).createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true,
                                            parsePluginQuantityEntryToMap(mPluginQuantityInfo)).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.GET_PLUGS_INFO:
                    PanelManager.getInstance().getNetworkRequestManager().requestUpdatePluginInfo(
                            PanelManager.getInstance().getCurrentPanelId(), new PanelCallback.NetworkResult<String>() {
                                @Override
                                public void onSuccess(String result) {
                                    try {
                                        JSONObject resultJson = new JSONObject(result);
                                        mHomePluginInfo = HomePluginEntry.parseFromJson(resultJson);
                                        dispatchResult(cmd, PanelResultBuilder.success(cmd, true,
                                                result).createResult());
                                    } catch (JSONException e) {
                                        e.printStackTrace();
                                    }

                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            });
                    break;
                case PanelCmd.START_ROUND_ROBIN_PLUGIN_STATE:
                    if (null != mPluginStateQueryUtil && isCanOperate())
                        mPluginStateQueryUtil.startQueryPluginStatusRoundRobin();
                    break;
                case PanelCmd.STOP_ROUND_ROBIN_PLUGIN_STATE:
                    if (null != mPluginStateQueryUtil && isCanOperate())
                        mPluginStateQueryUtil.stopQueryPluginStatusRoundRobin();
                    break;
                case PanelCmd.GET_CUSTOMIZE_SMART_PLUGS:
                    PanelManager.getInstance().getNetworkRequestManager().requestCustomizeSmartPlugs(
                            getId(), panelToken, new PanelCallback.NetworkResult<String>() {
                                @Override
                                public void onSuccess(String result) {
                                    dispatchResult(cmd, PanelResultBuilder.success(cmd, true,
                                            result).createResult());
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.SAVE_CUSTOMIZE_SMART_PLUGS:
                    messageId = RandomStringUtils.getMessageId();
                    mMessageIdMap.put(messageId, cmd);
                    String pluginId = (String) arg.get(PanelDataKey.ID);
                    String sendId = (String) arg.get(PanelDataKey.SEND_ID);
                    PanelManager.getInstance().getNetworkRequestManager().requestSetCustomizeSmartPlugs(
                            panelToken, messageId, pluginId, sendId,
                            new PanelCallback.NetworkResult<StringResponseEntry>() {
                                @Override
                                public void onSuccess(StringResponseEntry result) {
                                }

                                @Override
                                public void onError(int errorCode, String errorMsg) {
                                    dispatchResult(cmd, PanelResultBuilder.error(cmd, true, "code: "
                                            + errorCode + ", errorMsg: " + errorMsg).createResult());
                                }
                            }
                    );
                    break;
                case PanelCmd.ROLLBACK_ARM_STATE:
                    mCurrentPanel.setArm_state(lastArmState);
                    getInfo().put(PanelDataKey.Panel.ARM_STATUS, lastArmState);
                    dispatchResult(PanelCmd.ON_ARM_STATE_ROLLBACK,
                            PanelResultBuilder.success(PanelCmd.ON_ARM_STATE_ROLLBACK, true,
                                    "").createResult());
                    break;
                case PanelCmd.OPERATION_WS_CONNECTION:
                    int connect = (int) arg.get(PanelDataKey.CONNECTION);
                    if (PanelParamsHelper.WEB_SOCKET_CONNECT == connect) {
                        PanelManager.getInstance().requestGetPanelInfo(getId());
                    } else if (PanelParamsHelper.WEB_SOCKET_DISCONNECT == connect) {
                        PanelManager.getInstance().getPanelOperator().toCloseWs();
                    }
                    break;
                case PanelCmd.LOAD_PLUGIN_INFO:
                    List<String> pluginIds = (List<String>) arg.get(PanelDataKey.PLUGINS);
                    mPluginInfoQueryHelper.getPluginInfo(pluginIds);
                    break;
                default:
                    DDLog.e(TAG, "Not support cmd: " + cmd);
                    break;
            }
        } catch (Exception e) {
            DDLog.i(TAG, "Error on submit");
            e.printStackTrace();
        }
    }

    @Nullable
    public PluginStateRoundRobinUtil getPluginStateQueryUtil() {
        return mPluginStateQueryUtil;
    }

    /**
     * 处理主机被删除后可以选择进行操作的CMD
     *
     * @return true 表示已经处理了该cmd
     */
    private boolean submitOnDeviceDelete(@NotNull String cmd, Map arg) {
        if (PanelCmd.DELETE_PANEL.equals(cmd)) {
            if (getFlagDeleted()) {
                DDLog.i(TAG, "成功删除离线主机-之前已经被删除了，仅清空缓存");
                dispatchResult(cmd, PanelResultBuilder.success(cmd, true, "").createResult());
                KV.remove(DBKey.CURRENT_DEVICE_ID);
                EventBus.getDefault().post(new DeviceResetOrDeletedEvent(getId()));
                PanelManager.getInstance().getPanelOperator().toCloseWs();
                PanelManager.getInstance().releaseAllDevices();
                PanelManager.getInstance().resetDeviceInfo();
                PanelManager.getInstance().clearDeviceCache();
                remove();
                return true;
            }
        }
        return false;
    }

    private Map<String, Object> parsePluginQuantityEntryToMap
            (HomePluginQuantityEntry.PluginBean bean) {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> pluginMap = new HashMap<>();
        if (null != bean) {
            pluginMap.put(PanelDataKey.PanelPluginQuantity.DOOR_BELL_COUNT, bean.getDoor_bell());
            pluginMap.put(PanelDataKey.PanelPluginQuantity.THIRD_PARTY_ACCESSORY_COUNT, bean.getThird_party_accessory());
            pluginMap.put(PanelDataKey.PanelPluginQuantity.IP_CAMERA_COUNT, bean.getIpc());
            pluginMap.put(PanelDataKey.PanelPluginQuantity.DOOR_SENSOR_COUNT, bean.getDoor_window());
            pluginMap.put(PanelDataKey.PanelPluginQuantity.KEYPAD_ACCESSORY_COUNT, bean.getKeypad());
            pluginMap.put(PanelDataKey.PanelPluginQuantity.RELAY_ACCESSORY_COUNT, bean.getRoller_shutter());
            pluginMap.put(PanelDataKey.PanelPluginQuantity.REMOTE_CONTROL_COUNT, bean.getRemote_controller());
            pluginMap.put(PanelDataKey.PanelPluginQuantity.SECURITY_ACCESSORY_COUNT, bean.getSecurity_accessories());
            pluginMap.put(PanelDataKey.PanelPluginQuantity.SIREN_COUNT, bean.getSiren());
            pluginMap.put(PanelDataKey.PanelPluginQuantity.SMART_BUTTON_COUNT, bean.getSmart_button());
            pluginMap.put(PanelDataKey.PanelPluginQuantity.SMART_PLUG_COUNT, bean.getSmart_plug());
            pluginMap.put(PanelDataKey.PanelPluginQuantity.SIGNAL_REPEATER_PLUG_COUNT, bean.getSignal_repeater_plug());
            if (null != getInfo()) {
                getInfo().putAll(pluginMap);
            }
        }

        result.put(PanelDataKey.PanelPluginQuantity.QUANTITY_INFO, pluginMap);
        return result;
    }

    /**
     * 标记为未查询到主机基本信息的状态
     */
    public void setNeedLoadInfoAgain() {
        setFlagLoaded(false);
        setFlagLoading(false);
        ArrayList<Device> devices = new ArrayList<>(mPluginDeviceMap.values());
        for (Device device : devices) {
            if (device instanceof BasePluginDevice) {
                ((BasePluginDevice<?>) device).setNeedLoadInfoAgain();
            }
        }
    }

    public void markFlagLoading() {
        setFlagLoaded(true);
    }

    public void markFlagLoaded() {
        setFlagLoaded(true);
    }

    /**
     * 根据DeviceId获取当前主机的配件Device
     *
     * @param deviceId 配件DeviceId
     * @return 配件Device
     */
    public Device getDeviceById(@NotNull String deviceId) {
        if (0 >= mPluginDeviceMap.size()) {
            return null;
        }
        return mPluginDeviceMap.get(deviceId);
    }

    public void removeDeviceById(@NotNull String deviceId) {
        Device device = getDeviceById(deviceId);
        if (null != device) {
            mPluginDeviceMap.remove(deviceId);
        }

        if (mHomePluginIds.contains(deviceId)) {
            mHomePluginIds.remove(deviceId);
        }

        if (mDoorSensorIds.contains(deviceId)) {
            mDoorSensorIds.remove(deviceId);
        }

        if (mSmartPlugIds.contains(deviceId)) {
            mSmartPlugIds.remove(deviceId);
        }

        if (mSignalRepeaterPlugIds.contains(deviceId)) {
            mSignalRepeaterPlugIds.remove(deviceId);
        }
    }

    /**
     * 更新Map中的Info信息
     */
    public void updateInfoMap(boolean needCheckUpgrade) {
        Map<String, Object> panelParams = getInfo();
        if (null == panelParams) {
            panelParams = new HashMap<>();
            setInfo(panelParams);
        }
        lastArmState = mCurrentPanel.getArm_state();
        panelParams.put(PanelDataKey.Panel.IS_ONLINE, mCurrentPanel.isOnline());
        panelParams.put(PanelDataKey.Panel.ARM_STATUS, mCurrentPanel.getArm_state());
        panelParams.put(PanelDataKey.Panel.SOS, mCurrentPanel.isSos());
        panelParams.put(PanelDataKey.Panel.EXIT_DELAY, mCurrentPanel.getExitdelay());
        panelParams.put(PanelDataKey.Panel.ENTRY_DELAY, mCurrentPanel.getEntrydelay());
        // panelParams.put(PanelDataKey.Panel.ROLE, mCurrentPanel.getPermission());
        panelParams.put(PanelDataKey.Panel.IS_MESSAGE_SET, mCurrentPanel.isHas_device_text_set());
        panelParams.put(PanelDataKey.Panel.TIMEZONE, mCurrentPanel.getTimezone());
        panelParams.put(PanelDataKey.Panel.IS_CHARGE, mCurrentPanel.isCharge());
        panelParams.put(PanelDataKey.Panel.BATTERY_LEVEL, mCurrentPanel.getBattery_level());
        panelParams.put(PanelDataKey.Panel.NET_TYPE, mCurrentPanel.getNetwork());
        panelParams.put(PanelDataKey.Panel.LAN_IP, mCurrentPanel.getIp());
        panelParams.put(PanelDataKey.Panel.SIM_STATUS, mCurrentPanel.getSim());
        panelParams.put(PanelDataKey.Panel.UPGRADING, mCurrentPanel.isUpdate());
        panelParams.put(PanelDataKey.Panel.DEVICE_TOKEN, panelToken);
        panelParams.put(PanelDataKey.Panel.FIRMWARE_VERSION, mCurrentPanel.getFirmware_version());
        panelParams.put(PanelDataKey.Panel.SSID, mCurrentPanel.getSsid());
        panelParams.put(PanelDataKey.Panel.WIFI_MAC_ADDRESS, mCurrentPanel.getWifi_mac_addr());
        panelParams.put(PanelDataKey.Panel.WIFI_RSSI, mCurrentPanel.getWifi_rssi());
        panelParams.put(PanelDataKey.Panel.LAST_NO_ACTION_TIME, mCurrentPanel.getLast_no_action_time());
        panelParams.put(PanelDataKey.Panel.ALARM_DELAY_TIME, mCurrentPanel.getAlarm_delay_time());
        panelParams.put(PanelDataKey.Panel.NO_ACTION_TIME, mCurrentPanel.getNo_action_time());

        if (needCheckUpgrade
                && checkIfNeedUpgradeFirmware(mCurrentPanel.isUpdate(), mCurrentPanel.getFirmware_version())) {
            DDLog.i(TAG, "发送升级指令了");
            PanelManager.getInstance().getNetworkRequestManager().requestUpdatePanelFirmware(
                    panelToken, RandomStringUtils.getMessageId(),
                    new PanelCallback.NetworkResult<String>() {
                        @Override
                        public void onSuccess(String result) {
                        }

                        @Override
                        public void onError(int errorCode, String errorMsg) {

                        }
                    }
            );
            panelParams.put(PanelDataKey.Panel.UPGRADING, true);
        }
    }

    /**
     * 检查是否需要发送升级主机的指令
     *
     * @return true 需要升级
     */
    private boolean checkIfNeedUpgradeFirmware(boolean upgrading, String firmwareVersion) {
        if (!upgrading && !TextUtils.isEmpty(firmwareVersion)) {
            try {
                String[] currentVersions = firmwareVersion.split("/");
                String[] versionCode = currentVersions[0].split("\\.");

                if (3 > versionCode.length) {
                    return true;
                }
                // 校验固件版本号是否小于等于1.0.0
                // 校验第一位
                int code;
                code = Integer.parseInt(versionCode[0]);
                if (code > 1) {
                    return false;
                } else if (code < 1) {
                    return true;
                }

                // 第一位为1，校验第二位
                code = Integer.parseInt(versionCode[1]);
                if (code > 0) {
                    return false;
                }

                // 第二位为0，校验第三位
                code = Integer.parseInt(versionCode[2]);
                return code <= 0;

            } catch (Exception e) {
                DDLog.e(TAG, "Error on check if need upgrade panel.");
                e.printStackTrace();
            }
        }
        return false;
    }

    public void updateHomePluginInfo(HomePluginEntry homePluginEntry) {

    }

    /**
     * 更新Map中的Info信息
     */
    public void updateInfoMap(PanelInfoNew panelInfo, boolean needCheckUpgrade) {
        if (null == panelInfo || !getId().equals(panelInfo.getDevice_id())) {
            return;
        }
        this.mCurrentPanel = panelInfo;
        updateInfoMap(needCheckUpgrade);
    }

    /**
     * 获取当前主机的信息
     *
     * @return 主机信息
     */
    public PanelInfoNew getPanelInfo() {
        return mCurrentPanel;
    }

    /**
     * 通知主机在线状态
     *
     * @param operateSelf 是否自己触发的
     * @param isOnline    主机是否在线 true 在线
     */
    private void notifyPanelOnlineState(boolean operateSelf, boolean isOnline) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.IS_ONLINE, isOnline);
        dispatchResult(PanelCmd.PANEL_OFFLINE,
                PanelResultBuilder.success(PanelCmd.PANEL_OFFLINE, operateSelf, result).createResult());
    }

    /**
     * 通知主机连接WebSocket的状态
     *
     * @param operateSelf 是否自己触发的
     * @param connection  0:连接中, 1:在线, -1:离线
     */
    private void notifyPanelWsConnectState(boolean operateSelf,
                                           @PanelParamsHelper.WebSocketConnectOperate int connection) {
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CONNECTION, connection);
        dispatchResult(PanelCmd.OPERATION_WS_CONNECTION,
                PanelResultBuilder.success(PanelCmd.OPERATION_WS_CONNECTION, operateSelf, result).createResult());
    }

    /**
     * 通知请求服务器刷新主机
     */
    private void notifyPanelRenew(boolean operateSelf) {
        dispatchResult(PanelCmd.PANEL_RENEW,
                PanelResultBuilder.success(PanelCmd.PANEL_RENEW, operateSelf, "").createResult());
    }

    public void onEventListTimeOutCheckEvent(final EventListTimeOutCheck checkTimeEvent) {
        DDLog.i(TAG, "onEvent, EventListTimeOutCheck");
        mCurrentPanel.setArm_state(lastArmState);
        getInfo().put(PanelDataKey.Panel.ARM_STATUS, lastArmState);
        dispatchResult(PanelCmd.ON_ARM_STATE_ROLLBACK,
                PanelResultBuilder.success(PanelCmd.ON_ARM_STATE_ROLLBACK, true,
                        "").createResult());

        // 超时，重连WebSocket
        PanelManager.getInstance().getTimeoutChecker().cleanAllTask();
        PanelManager.getInstance().getPanelOperator().connectWs();
    }

    public void onEventListDataFixTimeEvent(final EventListDataFixTime fixTimeEvent) {
        DDLog.i(TAG, "onEvent, EventListDataFixTime");
        // TODO
    }

    public void onDeviceEventListEvent(final DeviceEventListEvent eventListEvent) {
        DDLog.i(TAG, "onEvent, DeviceEventListEvent");
        // TODO
    }

    public void onDeviceSimStatueEvent(final DeviceSimStatueEvent simStatueEvent) {
        DDLog.i(TAG, "onEvent, DeviceSimStatueEvent");
        // TODO
    }

    public void onShowBlockToastEvent(final ShowBlockToastEvent ev) {
        DDLog.i(TAG, "onEvent, ShowBlockToastEvent");
        Map<String, Object> result = new HashMap<>();
        result.put(PanelDataKey.CmdResult.OPERATION_CMD, ev.getCmdType());
        result.put(PanelDataKey.PLUGIN, ev.getPlugin());
        dispatchResult(PanelCmd.SHOW_BLOCK_TOAST, PanelResultBuilder
                .success(PanelCmd.SHOW_BLOCK_TOAST, true, result)
                .createResult());
    }

    private boolean isCMDArmDisarmHomeArm(String type) {
        return PanelOperatorConstant.CMD.ARM_KEY.equals(type)
                || PanelOperatorConstant.CMD.DISARM_KEY.equals(type)
                || PanelOperatorConstant.CMD.HOMEARM_KEY.equals(type);
    }

    public void onDeviceCmdAckEvent(final DeviceCmdAckEvent deviceCmdAckEvent) {
        DDLog.i(TAG, "onEvent, deviceCmdAckEvent");
        if (!isCMDArmDisarmHomeArm(deviceCmdAckEvent.getCmdType())) {
            return;
        }

        if (DeviceWorkQueue.getInstance().getQueueBackUpSize() == 0) {
            if (deviceCmdAckEvent.getCmdType().equals(PanelOperatorConstant.CMD.ARM_KEY)) {
                //  队列里面没有任务，并且是arm的操作。其他操作不处理。
                if (mCurrentPanel.getExitdelay() > 0) {
                    mCurrentPanel.setArm_state(PanelOperatorConstant.KEY.ARM_STATUS);
                    getInfo().put(PanelDataKey.Panel.ARM_STATUS, PanelOperatorConstant.KEY.ARM_STATUS);
                }
            }
        } else if (DeviceWorkQueue.getInstance().getQueueBackUpSize() == 1) {
            // 有一个任务在执行，
            if (DeviceWorkQueue.getInstance().getQueueBackUp().get(0).taskId.equals(deviceCmdAckEvent.getMessageId())) {
                // 是本人的操作，不去除arm任务。
                if (deviceCmdAckEvent.getCmdType().equals(PanelOperatorConstant.CMD.ARM_KEY)) {
                    mCurrentPanel.setArm_state(PanelOperatorConstant.KEY.ARM_STATUS);
                    getInfo().put(PanelDataKey.Panel.ARM_STATUS, PanelOperatorConstant.KEY.ARM_STATUS);
                    DeviceWorkQueue.getInstance().setAckTaskById(deviceCmdAckEvent.getMessageId());
                }
            } else {
                // 不同，证明此操作不是我做的。需要去除arm的任务并且停止倒数
                if (DeviceWorkQueue.getInstance().getQueueBackUp().get(0).type.equals(PanelOperatorConstant.CMD.ARM_KEY)) {
                    DeviceWorkQueue.getInstance().removeTaskByIndex(0);
                    // 去除了后，如果没有任务在做，则直接使用状态
                    if (DeviceWorkQueue.getInstance().getQueueBackUpSize() == 0) {
                        if (PanelOperatorConstant.CMD.ARM_KEY.equals(deviceCmdAckEvent.getCmdType())) {
                            mCurrentPanel.setArm_state(PanelOperatorConstant.KEY.ARM_STATUS);
                            getInfo().put(PanelDataKey.Panel.ARM_STATUS, PanelOperatorConstant.KEY.ARM_STATUS);
                        } else if (PanelOperatorConstant.CMD.DISARM_KEY.equals(deviceCmdAckEvent.getCmdType())) {
                            mCurrentPanel.setArm_state(PanelOperatorConstant.KEY.DISARM_STATUS);
                            getInfo().put(PanelDataKey.Panel.ARM_STATUS, PanelOperatorConstant.KEY.DISARM_STATUS);
                        } else if (PanelOperatorConstant.CMD.HOMEARM_KEY.equals(deviceCmdAckEvent.getCmdType())) {
                            mCurrentPanel.setArm_state(PanelOperatorConstant.KEY.HOME_STATUS);
                            getInfo().put(PanelDataKey.Panel.ARM_STATUS, PanelOperatorConstant.KEY.HOME_STATUS);
                        }
                    }
                }
            }
        } else {
            //   任务队列大于1个。
            if (deviceCmdAckEvent.getCmdType().equals(PanelOperatorConstant.CMD.DISARM_KEY)
                    || deviceCmdAckEvent.getCmdType().equals(PanelOperatorConstant.CMD.HOMEARM_KEY)) {
                for (int i = 0; i < DeviceWorkQueue.getInstance().getQueueBackUpSize(); i++) {
                    if (DeviceWorkQueue.getInstance().getQueueBackUp().get(i).type
                            .equals(PanelOperatorConstant.CMD.ARM_KEY)) {
                        DeviceWorkQueue.getInstance().removeTaskByIndex(i);
                    }
                }
            } else if (deviceCmdAckEvent.getCmdType().equals(PanelOperatorConstant.CMD.ARM_KEY)) {
                for (int i = 0; i < DeviceWorkQueue.getInstance().getQueueBackUpSize() - 1; i++) {
                    if (DeviceWorkQueue.getInstance().getQueueBackUp().get(i).type.equals(PanelOperatorConstant.CMD.ARM_KEY)) {
                        DeviceWorkQueue.getInstance().removeTaskByIndex(i);
                    }
                }
                if (DeviceWorkQueue.getInstance().getQueueBackUp().getLast().type
                        .equals(PanelOperatorConstant.CMD.ARM_KEY)) {
                    mCurrentPanel.setArm_state(PanelOperatorConstant.KEY.ARM_STATUS);
                    getInfo().put(PanelDataKey.Panel.ARM_STATUS, PanelOperatorConstant.KEY.ARM_STATUS);
                    DeviceWorkQueue.getInstance().setAckTaskById(deviceCmdAckEvent.getMessageId());
                }
            }
        }

        if (deviceCmdAckEvent.getCmdType().equals(PanelOperatorConstant.CMD.ARM_KEY)) {
            for (int i = 0; i < DeviceWorkQueue.getInstance().getQueueBackUpSize() - 1; i++) {
                if (DeviceWorkQueue.getInstance().getQueueBackUp().get(i).type
                        .equals(PanelOperatorConstant.CMD.ARM_KEY)) {
                    DeviceWorkQueue.getInstance().removeTaskByIndex(i);
                }
            }

            if (DeviceWorkQueue.getInstance().getQueueBackUpSize() > 1) {
                //  为什么要大于1个任务才删除?不然当你延迟布放,这里会立刻删除掉任务队列,导致无法去除掉eventlist的arm事件
                DeviceWorkQueue.getInstance().removeTaskById(deviceCmdAckEvent.getMessageId());
            }
            mCurrentPanel.setArm_state(PanelOperatorConstant.KEY.ARM_STATUS);
            getInfo().put(PanelDataKey.Panel.ARM_STATUS, PanelOperatorConstant.KEY.ARM_STATUS);
            DeviceWorkQueue.getInstance().setAckTaskById(deviceCmdAckEvent.getMessageId());
        } else if (deviceCmdAckEvent.getCmdType().equals(PanelOperatorConstant.CMD.DISARM_KEY)
                || deviceCmdAckEvent.getCmdType().equals(PanelOperatorConstant.CMD.HOMEARM_KEY)) {
            for (int i = 0; i < DeviceWorkQueue.getInstance().getQueueBackUpSize(); i++) {
                if (DeviceWorkQueue.getInstance().getQueueBackUp().get(i).type
                        .equals(PanelOperatorConstant.CMD.ARM_KEY)) {
                    DeviceWorkQueue.getInstance().removeTaskByIndex(i);
                }
            }

            if (PanelOperatorConstant.CMD.DISARM_KEY.equals(deviceCmdAckEvent.getCmdType())) {
                mCurrentPanel.setArm_state(PanelOperatorConstant.KEY.DISARM_STATUS);
                getInfo().put(PanelDataKey.Panel.ARM_STATUS, PanelOperatorConstant.KEY.DISARM_STATUS);
            } else if (PanelOperatorConstant.CMD.HOMEARM_KEY.equals(deviceCmdAckEvent.getCmdType())) {
                mCurrentPanel.setArm_state(PanelOperatorConstant.KEY.HOME_STATUS);
                getInfo().put(PanelDataKey.Panel.ARM_STATUS, PanelOperatorConstant.KEY.HOME_STATUS);
            }
        }

        String result = deviceCmdAckEvent.getReslut();
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(PanelDataKey.CmdResult.OPERATION_CMD, deviceCmdAckEvent.getCmdType());
            if (PanelOperatorConstant.CMD.ARM_KEY.equals(deviceCmdAckEvent.getCmdType())) {
                jsonObject.put(PanelDataKey.Panel.EXIT_DELAY, mCurrentPanel.getExitdelay());
            }
            result = jsonObject.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        onDeviceResultOrAckEvent(true, deviceCmdAckEvent.getCmdType(),
                deviceCmdAckEvent.getStatus(), deviceCmdAckEvent.getMessageId(), result);
    }

    public void onDeviceResultEvent(final DeviceResultEvent deviceResultEvent) {
        PanelManager.getInstance().getTimeoutChecker().removeTaskById(deviceResultEvent.getMessageId());
        boolean isSelfCmd = DeviceWorkQueue.getInstance().removeTaskById(deviceResultEvent.getMessageId());
        int operateQueueSize = DeviceWorkQueue.getInstance().getQueueBackUpSize();
        DDLog.i(TAG, "onEvent, DeviceResultEvent: " + deviceResultEvent + ", queueBackupSize: " + operateQueueSize);

        String result = deviceResultEvent.getReslut();
        JSONObject jsonObject;
        try {
            final String operateCmd = deviceResultEvent.getCmdType();
            if (!TextUtils.isEmpty(operateCmd)) {
                switch (operateCmd) {
                    case PanelOperatorConstant.CMD.SET_EXITDELAY:
                        try {
                            jsonObject = new JSONObject(deviceResultEvent.getReslut());
                            int time = DDJSONUtil.getInt(jsonObject, "time");
                            boolean sound = DDJSONUtil.getBoolean(jsonObject, "sound");
                            mCurrentPanel.setExitdelay(time);
                            getInfo().put(PanelDataKey.Panel.EXIT_DELAY, time);
                        } catch (Exception e) {
                            DDLog.e(TAG, "Error on parse result for cmd: SET_EXITDELAY_2");
                            e.printStackTrace();
                        }
                        break;
                    case PanelOperatorConstant.CMD.SET_ENTRYDELAY:
                        try {
                            jsonObject = new JSONObject(deviceResultEvent.getReslut());
                            int time = DDJSONUtil.getInt(jsonObject, "time");
                            boolean sound = DDJSONUtil.getBoolean(jsonObject, "sound");
                            mCurrentPanel.setEntrydelay(time);
                            getInfo().put(PanelDataKey.Panel.ENTRY_DELAY, time);
                        } catch (Exception e) {
                            DDLog.e(TAG, "Error on parse result.");
                            e.printStackTrace();
                        }
                        break;
                    case PanelOperatorConstant.CMD.TASK_SOS:
                    case PanelOperatorConstant.CMD.TASK_INTIMIDATIONALARM_SOS:
                    case PanelOperatorConstant.CMD.TASK_ANTIINTERFER_SOS:
                    case PanelOperatorConstant.CMD.TASK_FC_SOS:
                    case PanelOperatorConstant.CMD.TASK_FC_SOS_PANEL:
                    case PanelOperatorConstant.CMD.NO_ACTION_SOS:
                        // 收到SOS信号时，需要判断取消当前正在执行的延时布防
                        if (DeviceWorkQueue.getInstance().getQueueBackUpSize() == 1
                                && DeviceWorkQueue.getInstance().getQueueBackUp().get(0).type
                                .equals(PanelOperatorConstant.CMD.ARM_KEY)) {
                            // 移除本地任务并清除列表的布防事件
                            DeviceWorkQueue.getInstance().removeTaskByIndex(0);
                        }
                        // 更新SOS状态
                        boolean sos = !PanelOperatorConstant.CMD.TASK_INTIMIDATIONALARM_SOS.equals(operateCmd);
                        mCurrentPanel.setSos(sos);
                        getInfo().put(PanelDataKey.Panel.SOS, sos);

                        jsonObject = new JSONObject(result);
                        jsonObject.put(PanelDataKey.CmdResult.OPERATION_CMD, operateCmd);
                        result = jsonObject.toString();
                        break;
                    case PanelOperatorConstant.CMD.ARM_KEY:
                        boolean isLatestCmd = newArmCallBackAction(deviceResultEvent);
                        jsonObject = new JSONObject(result);
                        jsonObject.put(PanelDataKey.CmdResult.OPERATION_CMD, operateCmd);
                        jsonObject.put(PanelDataKey.Panel.OPERATE_QUEUE_SIZE, operateQueueSize);
                        jsonObject.put(PanelDataKey.Panel.IS_LATEST_CMD, isLatestCmd);
                        result = jsonObject.toString();
                        break;
                    case PanelOperatorConstant.CMD.DISARM_KEY:
                        isLatestCmd = DisArmCallBackAction(deviceResultEvent, DeviceWorkQueue.getInstance().getQueueBackUpSize());
                        jsonObject = new JSONObject(result);
                        jsonObject.put(PanelDataKey.CmdResult.OPERATION_CMD, operateCmd);
                        jsonObject.put(PanelDataKey.Panel.OPERATE_QUEUE_SIZE, operateQueueSize);
                        jsonObject.put(PanelDataKey.Panel.IS_LATEST_CMD, isLatestCmd);
                        result = jsonObject.toString();
                        break;
                    case PanelOperatorConstant.CMD.HOMEARM_KEY:
                        isLatestCmd = newHomeArmCallBackAction(deviceResultEvent);
                        jsonObject = new JSONObject(result);
                        jsonObject.put(PanelDataKey.CmdResult.OPERATION_CMD, operateCmd);
                        jsonObject.put(PanelDataKey.Panel.OPERATE_QUEUE_SIZE, operateQueueSize);
                        jsonObject.put(PanelDataKey.Panel.OPERATE_QUEUE_SIZE, operateQueueSize);
                        jsonObject.put(PanelDataKey.Panel.IS_LATEST_CMD, isLatestCmd);
                        result = jsonObject.toString();
                        break;
                    case PanelOperatorConstant.CMD.ADD_NEWASKPLUGIN:
                        addAskPlugin(deviceResultEvent);
                        toChangePlugsNumber(deviceResultEvent);
                        break;
                    case PanelOperatorConstant.CMD.ADD_PLUGIN:
                        // addOldPlugin(deviceResultEvent);
                        toChangePlugsNumber(deviceResultEvent);
                        break;
                    case PanelOperatorConstant.CMD.DELETE_NEWASKPLUGIN:
                    case PanelOperatorConstant.CMD.DELETE_PLUGIN:
                        toChangePlugsNumber(deviceResultEvent);
                        break;
                    case PanelOperatorConstant.CMD.SET_DEVICE_TIMEZONE:
                        try {
                            jsonObject = new JSONObject(deviceResultEvent.getReslut());
                            String timezone = DDJSONUtil.getString(jsonObject, "timezone");
                            long offset = DDJSONUtil.getLong(jsonObject, "offset");
                            if (!TextUtils.isEmpty(timezone)) {
                                mCurrentPanel.setTimezone(timezone);
                                getInfo().put(PanelDataKey.Panel.TIMEZONE, timezone);
                            }
                        } catch (Exception e) {
                            DDLog.e(TAG, "Error on parse result.");
                            e.printStackTrace();
                        }
                        break;
                    case PanelOperatorConstant.CMD.UPDATE_EVENTLIST_SETTING:
                        try {
                            jsonObject = new JSONObject(deviceResultEvent.getReslut());
                            boolean dw_event_log = DDJSONUtil.getBoolean(jsonObject, "dw_event_log");
                            boolean tamper_event_log = DDJSONUtil.getBoolean(jsonObject, "tamper_event_log");
                        } catch (Exception e) {
                            DDLog.e(TAG, "Error on parse result.");
                            e.printStackTrace();
                        }
                        break;
                    case PanelOperatorConstant.CMD.UPDATEING_SYSTEM:
                    case PanelOperatorConstant.CMD.SYSTEM_UPDATERESET:
                        getInfo().put(PanelDataKey.Panel.UPGRADING, true);
                        break;
                    case PanelOperatorConstant.CMD.UP_POWER:
                        try {
                            jsonObject = new JSONObject(deviceResultEvent.getReslut());
                            if (null != getInfo()) {
                                getInfo().put(PanelDataKey.Panel.IS_CHARGE, DDJSONUtil.getBoolean(jsonObject, "ischarge"));
                            }
                        } catch (Exception e) {
                            DDLog.e(TAG, "Error on parse result.");
                            e.printStackTrace();
                        }
                        break;
                    case PanelOperatorConstant.CMD.EVENT_LOWERPOWER:
                    case PanelOperatorConstant.CMD.EVENT_FULLPOWER:
                        jsonObject = new JSONObject(deviceResultEvent.getReslut());
                        jsonObject.put(PanelDataKey.CmdResult.OPERATION_CMD, operateCmd);
                        result = jsonObject.toString();
                        break;
                    case PanelOperatorConstant.CMD.SET_NEWASKPLUGINDATA:
                        toRenamePlugs(deviceResultEvent);
                        break;
                }
            }
        } catch (Exception e) {
            DDLog.e(TAG, "Update panel info error on cmd result.");
            e.printStackTrace();
        }

        onDeviceResultOrAckEvent(false, deviceResultEvent.getCmdType(),
                deviceResultEvent.getStatus(), deviceResultEvent.getMessageId(), result);
        mMessageIdMap.remove(deviceResultEvent.getMessageId());
    }

    /**
     * ack或Result WebSocket结果分发
     *
     * @param isAck true: ack; false: result
     */
    private void onDeviceResultOrAckEvent(boolean isAck, String operatorCmd, int status,
                                          String messageId, String result) {
        final String cmd = PanelOperator.transformFromPanelOperatorCmd(operatorCmd);
        boolean owner = false;
        if (!TextUtils.isEmpty(messageId)
                && mMessageIdMap.containsKey(messageId)) {
            owner = true;
        }
        if (TextUtils.isEmpty(cmd)) {
            DDLog.e(TAG, "Empty cmd type.");
            return;
        }
        if (1 == status) {
            dispatchResult(cmd, PanelResultBuilder
                    .success(cmd, owner, isAck ? 0 : 1, result)
                    .withOriginStatus(status)
                    .createResult());
        } else {
            dispatchResult(cmd, PanelResultBuilder
                    .error(cmd, owner, isAck ? 0 : 1, result)
                    .withOriginStatus(status)
                    .createResult());
        }
        if (!isAck && 1 == status) {
            if (PanelCmd.RESET_PANEL.equals(cmd)
                    || PanelCmd.DELETE_PANEL.equals(cmd)) {
                DDLog.i(TAG, "删除或重置主机");
                KV.remove(DBKey.CURRENT_DEVICE_ID);
                // 删除主机下的配件的缓存信息
                EventBus.getDefault().post(new DeviceResetOrDeletedEvent(getId()));
                PanelManager.getInstance().getPanelOperator().toCloseWs();
                PanelManager.getInstance().releaseAllDevices();
                PanelManager.getInstance().resetDeviceInfo();
                PanelManager.getInstance().clearDeviceCache();
                remove();
            }
        }
    }

    public void setDeleteByOther(final boolean deleted) {
        setFlagDeleted(deleted);
        ArrayList<Device> devices = new ArrayList<>(mPluginDeviceMap.values());
        for (Device device : devices) {
            ((BasePluginDevice<?>) device).setFlagDeleted(deleted);
        }
    }

    public void onOfflineEvent(OfflineEvent ev) {
        DDLog.i(TAG, "onEvent, OfflineEvent");
        if (null != getInfo()) {
            getInfo().put(PanelDataKey.Panel.IS_ONLINE, false);
        }
        if (null != mCurrentPanel) {
            mCurrentPanel.setOnline(false);
        }
        notifyPanelOnlineState(true, false);
    }

    public void onUserNetworkEvent(UserNetworkEvent ev) {
        DDLog.i(TAG, "onEvent, UserNetworkEvent");
        Map<String, Object> result = new HashMap<>();
        dispatchResult(PanelCmd.USER_NETWORK_ERROR,
                PanelResultBuilder.success(PanelCmd.USER_NETWORK_ERROR, true, result).createResult());
    }

    public void onWebSocketEvent(WebSocketEvent webSocketEvent) {
        DDLog.i(TAG, "onEvent, onWebSocketEvent");
        notifyPanelWsConnectState(true,
                WebSocketEvent.CONNET_SUCCESS == webSocketEvent.getType()
                        ? PanelParamsHelper.WEB_SOCKET_CONNECT
                        : PanelParamsHelper.WEB_SOCKET_DISCONNECT);
    }

    public void onPingUpdataEvent(PingUpdataEvent pingUpdataEvent) {
        DDLog.i(TAG, "onEvent, PingUpdataEvent");
        mCurrentPanel.setBattery_level(pingUpdataEvent.getBatteryLevel());
        mCurrentPanel.setCharge(pingUpdataEvent.isCharging());
        mCurrentPanel.setNetwork(pingUpdataEvent.getNetType());
        mCurrentPanel.setIp(pingUpdataEvent.getIpAddress());
        mCurrentPanel.setWifi_rssi(pingUpdataEvent.getWifi_rssi());

        if (null != getInfo()) {
            getInfo().put(PanelDataKey.Panel.BATTERY_LEVEL, mCurrentPanel.getBattery_level());
            getInfo().put(PanelDataKey.Panel.NET_TYPE, mCurrentPanel.getNetwork());
            getInfo().put(PanelDataKey.Panel.LAN_IP, mCurrentPanel.getIp());
            getInfo().put(PanelDataKey.Panel.IS_CHARGE, mCurrentPanel.isCharge());
            getInfo().put(PanelDataKey.Panel.WIFI_RSSI, mCurrentPanel.getWifi_rssi());
        }

        dispatchResult(PanelCmd.PANEL_PING_INFO,
                PanelResultBuilder
                        .success(PanelCmd.PANEL_PING_INFO, false, pingUpdataEvent.getOriginalResult())
                        .createResult());
    }

    public void onDeivceChangeEvent(DeivceChangeEvent event) {
        DDLog.i(TAG, "onEvent, onDeivceChangeEvent");
        if (event.isRemove() && null != event.getDevice()) {
            final String deviceId = event.getDevice().getId();
            Device remove = mPluginDeviceMap.remove(deviceId);
            if (null != remove) {
                remove.destory();
            }
        }
    }

    /**
     * 判断plugins的值有没有数据（是否有门窗没关）
     * 如果没有-->表示流程执行完毕
     * 如果有-->显示ready to arm弹窗
     * 判断force值————true为显示执行界面 (前提是同一个messageid)
     * false为显示知道了界面
     */
    private boolean newArmCallBackAction(DeviceResultEvent ev) {
        DDLog.i(TAG, "newArmCallBackAction");
        Gson gson = new Gson();
        try {
            JSONObject jsonObject = new JSONObject(ev.getReslut());
            final long time = DDJSONUtil.getLong(jsonObject, "gmtime");
            boolean isLatestCmd = time > mLastCmdTime;
            if (!TextUtils.isEmpty(DDJSONUtil.getString(jsonObject, "plugins"))) {
                UnCloseDoorEntry.ResultBean unCloseDoorEntry = gson.fromJson(ev.getReslut(), UnCloseDoorEntry.ResultBean.class);
                if (unCloseDoorEntry.getPlugins().size() > 0) {
                    /**
                     * plugin 有数据，触发ready to arm
                     */
                    if (!unCloseDoorEntry.isForce()) {
                        if (time >= mLastCmdTime) {
                            mLastCmdTime = time;
                            mCurrentPanel.setArm_state(PanelOperatorConstant.KEY.ARM_STATUS);
                            getInfo().put(PanelDataKey.Panel.ARM_STATUS, PanelOperatorConstant.KEY.ARM_STATUS);
                        }
                        lastArmState = mCurrentPanel.getArm_state();
                    } else {
                        if (!mMessageIdMap.containsKey(ev.getMessageId())) {
                            //如果不是自己的，就回到上一个状态
                            mCurrentPanel.setArm_state(lastArmState);
                            getInfo().put(PanelDataKey.Panel.ARM_STATUS, lastArmState);
                        }
                    }

                } else {
                    //无数据，跟ready to arm 没关。或没有门是开着的。或者提示框点击确认执行
                    if (time >= mLastCmdTime) {
                        mLastCmdTime = time;
                        mCurrentPanel.setArm_state(PanelOperatorConstant.KEY.ARM_STATUS);
                        getInfo().put(PanelDataKey.Panel.ARM_STATUS, PanelOperatorConstant.KEY.ARM_STATUS);
                    }
                    lastArmState = mCurrentPanel.getArm_state();
                }
            } else {
                if (time >= mLastCmdTime) {
                    mLastCmdTime = time;
                    mCurrentPanel.setArm_state(PanelOperatorConstant.KEY.ARM_STATUS);
                    getInfo().put(PanelDataKey.Panel.ARM_STATUS, PanelOperatorConstant.KEY.ARM_STATUS);
                }
                lastArmState = mCurrentPanel.getArm_state();
            }
            return isLatestCmd;
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return false;
    }

    private boolean DisArmCallBackAction(DeviceResultEvent ev, int size) {
        DDLog.i(TAG, "DisArmCallBackAction");
        try {
            JSONObject jsonObject = new JSONObject(ev.getReslut());
            long time = DDJSONUtil.getLong(jsonObject, "gmtime");
            boolean isLatestCmd = time > mLastCmdTime;
            DDLog.i(TAG, "dis arm:" + time);
            if (time >= mLastCmdTime) {
                mLastCmdTime = time;
//                    延迟布放，遥控撤防，会出现状态问题，如果没有这个event
                DeviceCmdAckEvent event = new DeviceCmdAckEvent(ev.getCmdType(), ev.getStatus(), ev.getMessageId());
                EventBus.getDefault().post(event);
                if (size == 0) {
                    mCurrentPanel.setSos(false);
                    getInfo().put(PanelDataKey.Panel.SOS, false);
                }

                mCurrentPanel.setArm_state(PanelOperatorConstant.KEY.DISARM_STATUS);
                getInfo().put(PanelDataKey.Panel.ARM_STATUS, PanelOperatorConstant.KEY.DISARM_STATUS);

                lastArmState = mCurrentPanel.getArm_state();
            } else {
                DDLog.i(TAG, "ignore arm");
            }
            return isLatestCmd;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return false;
    }

    private boolean isCanOperate() {
        return !getFlagDeleted();
    }

    private boolean newHomeArmCallBackAction(DeviceResultEvent ev) {
        try {
            JSONObject jsonObject = new JSONObject(ev.getReslut());
            long time = DDJSONUtil.getLong(jsonObject, "gmtime");
            boolean isLatestCmd = time > mLastCmdTime;
            if (!TextUtils.isEmpty(DDJSONUtil.getString(jsonObject, "plugins"))) {
                Gson gson = new Gson();
                UnCloseDoorEntry.ResultBean unCloseDoorEntry = gson.fromJson(ev.getReslut(), UnCloseDoorEntry.ResultBean.class);

                if (unCloseDoorEntry.getPlugins().size() > 0) {
                    /**
                     * plugin 有数据，触发ready to arm
                     */

                    if (!unCloseDoorEntry.isForce()) {
                        HomeArmCallBackAction(ev);
                        lastArmState = mCurrentPanel.getArm_state();
                    } else {
                        /**
                         * 这里是手机触发的ready to arm界面
                         * 先行删除eventlist数据。因为这次的eventlist是无效的。
                         */
                        if (!mMessageIdMap.containsKey(ev.getMessageId())) {
                            //如果不是自己的，就回到上一个状态
                            mCurrentPanel.setArm_state(lastArmState);
                            getInfo().put(PanelDataKey.Panel.ARM_STATUS, lastArmState);
                        }
                    }
                } else {
                    //无数据，跟ready to arm 没关。或没有门是开着的。或者提示框点击确认执行
                    HomeArmCallBackAction(ev);
                    lastArmState = mCurrentPanel.getArm_state();
                }
            } else {
                HomeArmCallBackAction(ev);
                lastArmState = mCurrentPanel.getArm_state();
            }
            return isLatestCmd;
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return false;
    }


    private boolean HomeArmCallBackAction(DeviceResultEvent ev) {
        try {
            JSONObject jsonObject = new JSONObject(ev.getReslut());
            long time = DDJSONUtil.getLong(jsonObject, "gmtime");
            DDLog.i(TAG, "home arm:" + time);
            if (time >= mLastCmdTime) {
                mLastCmdTime = time;
                //                    延迟布放，遥控撤防，会出现状态问题，如果没有这个event
                DeviceCmdAckEvent event = new DeviceCmdAckEvent(ev.getCmdType(), ev.getStatus(), ev.getMessageId());
                EventBus.getDefault().post(event);

                mCurrentPanel.setArm_state(PanelOperatorConstant.KEY.HOME_STATUS);
                getInfo().put(PanelDataKey.Panel.ARM_STATUS, PanelOperatorConstant.KEY.HOME_STATUS);
            } else {
                DDLog.i(TAG, "ignore arm");
            }
            return true;
        } catch (JSONException e) {
            e.printStackTrace();
            return true;
        }
    }

    /**
     * 更新配件信息
     */
    private void toRenamePlugs(DeviceResultEvent ev) {
        DDLog.i(TAG, "toRenamePlugs");
        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject(ev.getReslut());
            String id = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_PLUGIN__ID);
            String name = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_NAME);
            PanelManager.getInstance().updateAskPluginCacheName(getId(), id, name);
        } catch (JSONException e) {
            e.printStackTrace();
        }

    }

    /**
     * 添加或删除配件时，更新配件数量
     */
    private void toChangePlugsNumber(DeviceResultEvent ev) {
        DDLog.i(TAG, "toChangePlugsNumber");
        boolean isAdd;
        if (PanelOperatorConstant.CMD.ADD_PLUGIN.equals(ev.getCmdType())
                || PanelOperatorConstant.CMD.ADD_NEWASKPLUGIN.equals(ev.getCmdType())) {
            isAdd = true;
        } else if (PanelOperatorConstant.CMD.DELETE_PLUGIN.equals(ev.getCmdType())
                || PanelOperatorConstant.CMD.DELETE_NEWASKPLUGIN.equals(ev.getCmdType())) {
            isAdd = false;
        } else {
            return;
        }

        if (ev.getStatus() != 1) {
            DDLog.e(TAG, "添加配件或删除配件失败");
            return;
        }

        try {
            JSONObject result = new JSONObject(ev.getReslut());
            int category = -1;
            String stype = DDJSONUtil.getString(result, NetKeyConstants.NET_KEY_S_TYPE);
            if (PanelOperatorConstant.CMD.DELETE_NEWASKPLUGIN.equals(ev.getCmdType())
                    || PanelOperatorConstant.CMD.ADD_NEWASKPLUGIN.equals(ev.getCmdType())) {
                if (PluginConstants.TYPE_21.equals(stype)
                        || PluginConstants.TYPE_22.equals(stype)
                        || PluginConstants.TYPE_34.equals(stype)
                        || PluginConstants.TYPE_35.equals(stype)) {
                    category = PanelConstant.Category.WIRELESS;
                } else if (PluginConstants.TYPE_11.equals(stype)
                        || PluginConstants.TYPE_16.equals(stype)
                        || PluginConstants.TYPE_19.equals(stype)
                        || PluginConstants.TYPE_1C.equals(stype)
                        || PluginConstants.TYPE_25.equals(stype)
                        || PluginConstants.TYPE_2C.equals(stype)
                        || PluginConstants.TYPE_38.equals(stype)
                        || PluginConstants.TYPE_3D.equals(stype)) {
                    category = PanelConstant.Category.DOOR_SENSOR;
                } else if (PluginConstants.TYPE_12.equals(stype)) {
                    category = PanelConstant.Category.RELAY;
                } else if (PluginConstants.TYPE_1E.equals(stype)
                        || PluginConstants.TYPE_3A.equals(stype)) {
                    category = PanelConstant.Category.RC_KEY;
                } else if (PluginConstants.TYPE_2F.equals(stype)) {
                    category = PanelConstant.Category.KEYPAD_KEY;
                } else if (PluginConstants.TYPE_3E.equals(stype)) {
                    category = PanelConstant.Category.SMART_PLUGS;
                } else if (PluginConstants.TYPE_4E.equals(stype)) {
                    category = PanelConstant.Category.SIGNAL_REPEATER_PLUG;
                } else if (PluginConstants.TYPE_3B.equals(stype)) {
                    category = PanelConstant.Category.SMART_BUTTON;
                } else {
                    category = PanelConstant.Category.SECURITY;
                }
            } else {
                category = result.getInt(NetKeyConstants.NET_KEY_CATEGORY);
                if (DDJSONUtil.has(result, NetKeyConstants.NET_KEY_DECODE_ID)) {
                    String id = PluginTypeHelper.getInstance().getSTypeByDecodeid(
                            result.getString(NetKeyConstants.NET_KEY_DECODE_ID));
                    if (PluginConstants.NAME_VIBRATION_SENSOR.equals(id)
                            || PluginConstants.NAME_DOOR_WINDOW_SENSOR.equals(id)) {
                        category = PanelConstant.Category.DOOR_SENSOR;
                    } else if (PluginConstants.NAME_WIRELESS_KEYPAD.equals(id)
                            || PluginConstants.NAME_RFID_TAG.equals(id)) {
                        // 之前键盘和遥控器是在一组的，后面分开两组了
                        category = PanelConstant.Category.KEYPAD_KEY;
                    }
                } else {
                    String id = PluginTypeHelper.getInstance().getSTypeByID(
                            result.getString(NetKeyConstants.NET_KEY_PLUGIN_ID));
                    if (PluginConstants.NAME_VIBRATION_SENSOR.equals(id)
                            || PluginConstants.NAME_DOOR_WINDOW_SENSOR.equals(id)) {
                        category = PanelConstant.Category.DOOR_SENSOR;
                    } else if (PluginConstants.NAME_WIRELESS_KEYPAD.equals(id)
                            || PluginConstants.NAME_RFID_TAG.equals(id)) {
                        // 之前键盘和遥控器是在一组的，后面分开两组了
                        category = PanelConstant.Category.KEYPAD_KEY;
                    }
                }
            }
            if (category == PanelConstant.Category.SECURITY) {
                int count = DeviceHelper.getInt(getInfo(), PanelDataKey.PanelPluginQuantity.SECURITY_ACCESSORY_COUNT, 0);
                if (isAdd) {
                    count = count + 1;
                } else {
                    count = count - 1;
                }
                if (null != getInfo()) {
                    getInfo().put(PanelDataKey.PanelPluginQuantity.SECURITY_ACCESSORY_COUNT, Math.max(count, 0));
                }
            } else if (category == PanelConstant.Category.KEYPAD_KEY) {
                int count = DeviceHelper.getInt(getInfo(), PanelDataKey.PanelPluginQuantity.KEYPAD_ACCESSORY_COUNT, 0);
                if (isAdd) {
                    count = count + 1;
                } else {
                    count = count - 1;
                }
                if (null != getInfo()) {
                    getInfo().put(PanelDataKey.PanelPluginQuantity.KEYPAD_ACCESSORY_COUNT, Math.max(count, 0));
                }
            } else if (category == PanelConstant.Category.RC_KEY) {
                int count = DeviceHelper.getInt(getInfo(), PanelDataKey.PanelPluginQuantity.REMOTE_CONTROL_COUNT, 0);
                if (isAdd) {
                    count = count + 1;
                } else {
                    count = count - 1;
                }
                if (null != getInfo()) {
                    getInfo().put(PanelDataKey.PanelPluginQuantity.REMOTE_CONTROL_COUNT, Math.max(count, 0));
                }
            } else if (category == PanelConstant.Category.SIGNAL_REPEATER_PLUG) {
                int count = DeviceHelper.getInt(getInfo(), PanelDataKey.PanelPluginQuantity.SIGNAL_REPEATER_PLUG_COUNT, 0);
                if (isAdd) {
                    count = count + 1;
                } else {
                    count = count - 1;
                }
                if (null != getInfo()) {
                    getInfo().put(PanelDataKey.PanelPluginQuantity.SIGNAL_REPEATER_PLUG_COUNT, Math.max(count, 0));
                }
            } else if (category == PanelConstant.Category.SMART_PLUGS) {
                int count = DeviceHelper.getInt(getInfo(), PanelDataKey.PanelPluginQuantity.SMART_PLUG_COUNT, 0);
                if (isAdd) {
                    count = count + 1;
                } else {
                    count = count - 1;
                }
                if (null != getInfo()) {
                    getInfo().put(PanelDataKey.PanelPluginQuantity.SMART_PLUG_COUNT, Math.max(count, 0));
                }
            } else if (category == PanelConstant.Category.SMART_BUTTON) {
                int count = DeviceHelper.getInt(getInfo(), PanelDataKey.PanelPluginQuantity.SMART_BUTTON_COUNT, 0);
                if (isAdd) {
                    count = count + 1;
                } else {
                    count = count - 1;
                }
                if (null != getInfo()) {
                    getInfo().put(PanelDataKey.PanelPluginQuantity.SMART_BUTTON_COUNT, Math.max(count, 0));
                }
            } else if (category == PanelConstant.Category.WIRELESS) {
                int count = DeviceHelper.getInt(getInfo(), PanelDataKey.PanelPluginQuantity.SIREN_COUNT, 0);
                if (isAdd) {
                    count = count + 1;
                } else {
                    count = count - 1;
                }
                if (null != getInfo()) {
                    getInfo().put(PanelDataKey.PanelPluginQuantity.SIREN_COUNT, Math.max(count, 0));
                }
            } else if (category == PanelConstant.Category.OTHER_PLUGIN) {
                int count = DeviceHelper.getInt(getInfo(), PanelDataKey.PanelPluginQuantity.THIRD_PARTY_ACCESSORY_COUNT, 0);
                if (isAdd) {
                    count = count + 1;
                } else {
                    count = count - 1;
                }
                if (null != getInfo()) {
                    getInfo().put(PanelDataKey.PanelPluginQuantity.THIRD_PARTY_ACCESSORY_COUNT, Math.max(count, 0));
                }
            } else if (category == PanelConstant.Category.DOOR_BELL) {
                int count = DeviceHelper.getInt(getInfo(), PanelDataKey.PanelPluginQuantity.DOOR_BELL_COUNT, 0);
                if (isAdd) {
                    count = count + 1;
                } else {
                    count = count - 1;
                }
                if (null != getInfo()) {
                    getInfo().put(PanelDataKey.PanelPluginQuantity.DOOR_BELL_COUNT, Math.max(count, 0));
                }
            } else if (category == PanelConstant.Category.DOOR_SENSOR) {
                int count = DeviceHelper.getInt(getInfo(), PanelDataKey.PanelPluginQuantity.DOOR_SENSOR_COUNT, 0);
                if (isAdd) {
                    count = count + 1;
                } else {
                    count = count - 1;
                }
                if (null != getInfo()) {
                    getInfo().put(PanelDataKey.PanelPluginQuantity.DOOR_SENSOR_COUNT, Math.max(count, 0));
                }
            } else if (category == PanelConstant.Category.RELAY) {
                int count = DeviceHelper.getInt(getInfo(), PanelDataKey.PanelPluginQuantity.RELAY_ACCESSORY_COUNT, 0);
                if (isAdd) {
                    count = count + 1;
                } else {
                    count = count - 1;
                }
                if (null != getInfo()) {
                    getInfo().put(PanelDataKey.PanelPluginQuantity.RELAY_ACCESSORY_COUNT, Math.max(count, 0));
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 添加旧配件-需要在首页显示的配件
     */
    private void addOldPlugin(DeviceResultEvent ev) {
        DDLog.i(TAG, "addOldPlugin");
        try {
            JSONObject jsonObject = new JSONObject(ev.getReslut());
            int category = PanelPluginUtil.getOldPluginCategory(jsonObject);
            if (PanelConstant.Category.SMART_PLUGS == category) {
                // 旧插座
                String id = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_PLUGIN_ID);
                String name = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_PLUGIN_NAME);
                String decodeid = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_DECODE_ID);
                String subcategory = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_SUB__CATEGORY);

                SmartPlugDevice smartPlugDevice = new SmartPlugDevice(id, category, subcategory, getId());
                Map<String, Object> pluginParams = new HashMap<>();
                pluginParams.put(PanelDataKey.NAME, name);
                pluginParams.put(PanelDataKey.CATEGORY, category);
                pluginParams.put(PanelDataKey.SUBCATEGORY, subcategory);
                pluginParams.put(PanelDataKey.DECODE_ID, decodeid);

                pluginParams.put(PanelDataKey.HAVE_LOADING_STATE, true);
                pluginParams.put(PanelDataKey.LOADING_STATE, PanelConstant.PluginLoadingState.SUCCESS);
                pluginParams.put(PanelDataKey.SmartPlug.IS_ASK_SMART_PLUG, false);
                pluginParams.put(PanelDataKey.HAVE_ONLINE_STATE, false);
                smartPlugDevice.setInfo(pluginParams);

                mPluginDeviceMap.put(id, smartPlugDevice);
                mHomePluginIds.add(id);
                DeivceChangeEvent deivceChangeEvent = new DeivceChangeEvent(smartPlugDevice);
                deivceChangeEvent.setAdd(true);
                EventBus.getDefault().post(deivceChangeEvent);
            } else if (PanelConstant.Category.DOOR_SENSOR == category) {
                // 旧门磁
                String id = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_PLUGIN_ID);
                String name = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_PLUGIN_NAME);
                String decodeid = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_DECODE_ID);
                String subcategory = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_SUB__CATEGORY);

                DoorSensorDevice doorSensorDevice = new DoorSensorDevice(id, category, subcategory, getId());
                Map<String, Object> pluginParams = new HashMap<>();
                pluginParams.put(PanelDataKey.NAME, name);
                pluginParams.put(PanelDataKey.CATEGORY, category);
                pluginParams.put(PanelDataKey.SUBCATEGORY, subcategory);
                pluginParams.put(PanelDataKey.DECODE_ID, decodeid);

                pluginParams.put(PanelDataKey.LOADING_STATE, PanelConstant.PluginLoadingState.SUCCESS);
                pluginParams.put(PanelDataKey.HAVE_LOADING_STATE, false);
                pluginParams.put(PanelDataKey.HAVE_ONLINE_STATE, false);
                pluginParams.put(PanelDataKey.IS_ONLINE, true);
                doorSensorDevice.setInfo(pluginParams);

                mPluginDeviceMap.put(id, doorSensorDevice);
                mHomePluginIds.add(id);
                DeivceChangeEvent deivceChangeEvent = new DeivceChangeEvent(doorSensorDevice);
                deivceChangeEvent.setAdd(true);
                EventBus.getDefault().post(deivceChangeEvent);
            }
        } catch (Exception e) {
            DDLog.e(TAG, "addOldPlugin-ERROR!!!!!!!!!!");
            e.printStackTrace();
        }
    }

    /**
     * 添加新Ask配件-需要在首页显示的配件
     */
    private void addAskPlugin(DeviceResultEvent ev) {
        DDLog.i(TAG, "addAskPlugin");
        try {
            JSONObject jsonObject = new JSONObject(ev.getReslut());
            String stype = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_S_TYPE);
            String id = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_PLUGIN__ID);
            String name = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_PLUGIN_NAME);
            if (TextUtils.isEmpty(name))
                name = DDJSONUtil.getString(jsonObject, "pluginName");
            String sendId = DDJSONUtil.getString(jsonObject, NetKeyConstants.NET_KEY_ID);
            int category = PanelPluginUtil.getAskPluginType(stype);

            boolean supportHome = false;
            for (String supportSTyoe : HOME_STYPE_ALL) {
                if (supportSTyoe.equals(stype)) {
                    supportHome = true;
                    break;
                }
            }
            if (!supportHome) {
                DDLog.i(TAG, "不需要添加在首页的配件类型");
                return;
            }

            if (PanelConstant.Category.SMART_PLUGS == category
                    || PanelConstant.Category.SIGNAL_REPEATER_PLUG == category) {
                // 添加新插座
                // TODO 初始化subcategory
                Map<String, Object> pluginParams = new HashMap<>();
                pluginParams.put(PanelDataKey.NAME, name);
                pluginParams.put(PanelDataKey.CATEGORY, PluginConstants.CATEGORY_10);
                pluginParams.put(PanelDataKey.SEND_ID, sendId);
                pluginParams.put(PanelDataKey.S_TYPE, stype);

                pluginParams.put(PanelDataKey.LOADING_STATE, PanelConstant.PluginLoadingState.SUCCESS);
                pluginParams.put(PanelDataKey.SmartPlug.IS_ASK_SMART_PLUG, true);
                pluginParams.put(PanelDataKey.HAVE_LOADING_STATE, true);
                pluginParams.put(PanelDataKey.HAVE_ONLINE_STATE, true);
                pluginParams.put(PanelDataKey.IS_ONLINE, true);

                // 更新缓存中的Device
                Device cacheDevice = mPluginDeviceMap.get(id);
                if (cacheDevice instanceof BasePluginDevice) {
                    BasePluginDevice<?> cachePluginDevice = (BasePluginDevice<?>) cacheDevice;
                    cachePluginDevice.updateInfo(pluginParams);
                    cachePluginDevice.setLoaded(true);
                    cachePluginDevice.setFlagDeleted(false);
                    PanelManager.getInstance().updateAskPluginCacheName(getId(), id, name);
                    PanelManager.getInstance().addPluginCache(getId(),
                            new PanelCacheInfo.AskPluginCache(id, stype, sendId, 0L));
                    return;
                }

                // 新创建
                SmartPlugDevice plugDevice = new SmartPlugDevice(id, PluginConstants.CATEGORY_10, stype, getId());
                plugDevice.setInfo(pluginParams);

                mPluginDeviceMap.put(id, plugDevice);
                mHomePluginIds.add(id);
                PanelManager.getInstance().addPluginCache(getId(),
                        new PanelCacheInfo.AskPluginCache(id, stype, sendId, 0L));
                DeivceChangeEvent deivceChangeEvent = new DeivceChangeEvent(plugDevice);
                deivceChangeEvent.setAdd(true);
                EventBus.getDefault().post(deivceChangeEvent);
            } else if (PanelConstant.Category.DOOR_SENSOR == category) {
                // 新添加新门磁
                Map<String, Object> pluginParams = new HashMap<>();
                pluginParams.put(PanelDataKey.NAME, name);
                pluginParams.put(PanelDataKey.CATEGORY, PluginConstants.CATEGORY_10);
                pluginParams.put(PanelDataKey.SEND_ID, sendId);
                pluginParams.put(PanelDataKey.S_TYPE, stype);

                pluginParams.put(PanelDataKey.LOADING_STATE, PanelConstant.PluginLoadingState.SUCCESS);
                pluginParams.put(PanelDataKey.HAVE_LOADING_STATE, false);
                pluginParams.put(PanelDataKey.HAVE_ONLINE_STATE, true);
                pluginParams.put(PanelDataKey.IS_ONLINE, true);

                // 更新缓存中的Device
                Device cacheDevice = mPluginDeviceMap.get(id);
                if (cacheDevice instanceof BasePluginDevice) {
                    BasePluginDevice<?> cachePluginDevice = (BasePluginDevice<?>) cacheDevice;
                    cachePluginDevice.updateInfo(pluginParams);
                    cachePluginDevice.setLoaded(true);
                    cachePluginDevice.setFlagDeleted(false);
                    PanelManager.getInstance().updateAskPluginCacheName(getId(), id, name);
                    PanelManager.getInstance().addPluginCache(getId(),
                            new PanelCacheInfo.AskPluginCache(id, stype, sendId, 0L));
                    return;
                }

                // 新创建
                DoorSensorDevice doorSensorDevice = new DoorSensorDevice(id, PluginConstants.CATEGORY_10, stype, getId());
                doorSensorDevice.setInfo(pluginParams);

                mPluginDeviceMap.put(id, doorSensorDevice);
                mHomePluginIds.add(id);
                PanelManager.getInstance().addPluginCache(getId(),
                        new PanelCacheInfo.AskPluginCache(id, stype, sendId, 0L));
                DeivceChangeEvent deivceChangeEvent = new DeivceChangeEvent(doorSensorDevice);
                deivceChangeEvent.setAdd(true);
                EventBus.getDefault().post(deivceChangeEvent);
            }
        } catch (Exception e) {
            DDLog.e(TAG, "addAskPlugin-ERROR!!!!!!!!!");
            e.printStackTrace();
        }
    }

    /**
     * 请求CareMore的数据
     */
    public void requestCareModeInfo() {
        PanelManager.getInstance().getNetworkRequestManager().getCareModeStatus(
                PanelManager.getInstance().getCurrentHomeId(), getId(), new PanelCallback.NetworkResult<GetCareModeStatusResponse.ResultBean>() {
                    @Override
                    public void onSuccess(GetCareModeStatusResponse.ResultBean result) {
                        DDLog.d(TAG, "requestCareModeInfo SUCCESS");
                        if (null != mCurrentPanel) {
                            mCurrentPanel.setLast_no_action_time(result.getLast_no_action_time());
                            GetCareModeStatusResponse.ResultBean.SettingBean setting = result.getSetting();
                            if (null != setting) {
                                mCurrentPanel.setNo_action_time(setting.getNo_action_time());
                                mCurrentPanel.setAlarm_delay_time(setting.getAlarm_delay_time());
                            }
                            updateInfoMap(false);
                        }
                    }

                    @Override
                    public void onError(int errorCode, String errorMsg) {
                        DDLog.d(TAG, "requestCareModeInfo FAILED, code: " + errorCode + ", msg: " + errorMsg);
                    }
                }
        );
    }

    // ************************** 获取配件列表-开始************************* //
    public ArrayList<SmartButtonDevice> requestSmartButtonListSync() {
        final String panelId = getId();
        if (TextUtils.isEmpty(panelId) || !isCanOperate()) {
            return new ArrayList<>();
        }

        ArrayList<SmartButtonDevice> smartButtonDevices = PanelManager.getInstance().getNetworkRequestManager()
                .requestSmartButtonListSync(panelId);
        for (SmartButtonDevice smartButtonDevice : smartButtonDevices) {
            mSmartButtonIds.add(smartButtonDevice.getId());
            mPluginDeviceMap.put(smartButtonDevice.getId(), smartButtonDevice);
        }
        return smartButtonDevices;
    }

    public ArrayList<SmartPlugDevice> requestSignalRepeaterPlugList() {
        final String panelId = getId();
        if (TextUtils.isEmpty(panelId) || !isCanOperate()) {
            return new ArrayList<>();
        }

        ArrayList<SmartPlugDevice> signalRepeaterPlugDevices = PanelManager.getInstance().getNetworkRequestManager()
                .requestSignalRepeaterPlugListSync(panelId);
        ArrayList<SmartPlugDevice> resultList = new ArrayList<>();
        Device oldDevice;
        SmartPlugDevice oldPlugDevice;
        for (SmartPlugDevice device : signalRepeaterPlugDevices) {
            mSignalRepeaterPlugIds.add(device.getId());
            oldDevice = mPluginDeviceMap.get(device.getId());
            if (oldDevice instanceof SmartPlugDevice) {
                DDLog.i(TAG, "更新SignalRepeaterPlug信息");
                oldPlugDevice = (SmartPlugDevice) oldDevice;
                oldPlugDevice.updateInfo(device);
                resultList.add(oldPlugDevice);
                device.destory();
            } else {
                mPluginDeviceMap.put(device.getId(), device);
                resultList.add(device);
            }
        }

        return resultList;
    }

    public ArrayList<SmartPlugDevice> requestSmartPlugListSync() {
        final String panelId = getId();
        if (TextUtils.isEmpty(panelId) || !isCanOperate()) {
            return new ArrayList<>();
        }

        ArrayList<SmartPlugDevice> smartPlugDevices = PanelManager.getInstance().getNetworkRequestManager()
                .requestSmartPlugListSync(panelId);
        ArrayList<SmartPlugDevice> resultList = new ArrayList<>();
        Device oldDevice;
        SmartPlugDevice oldPlugDevice;
        for (SmartPlugDevice device : smartPlugDevices) {
            mSmartPlugIds.add(device.getId());
            oldDevice = mPluginDeviceMap.get(device.getId());
            if (oldDevice instanceof SmartPlugDevice) {
                DDLog.i(TAG, "更新SmartPlug信息");
                oldPlugDevice = (SmartPlugDevice) oldDevice;
                oldPlugDevice.updateInfo(device);
                resultList.add(oldPlugDevice);
                device.destory();
            } else {
                mPluginDeviceMap.put(device.getId(), device);
                resultList.add(device);
            }
        }

        return resultList;
    }

    public ArrayList<RelayDevice> requestRollerShutterListSync() {
        final String panelId = getId();
        if (TextUtils.isEmpty(panelId) || !isCanOperate()) {
            return new ArrayList<>();
        }

        ArrayList<RelayDevice> devices = PanelManager.getInstance().getNetworkRequestManager()
                .requestRollerShutterListSync(panelId);
        for (RelayDevice device : devices) {
            mRelayIds.add(device.getId());
            mPluginDeviceMap.put(device.getId(), device);
        }

        return devices;
    }

    public ArrayList<SecurityDevice> requestSecurityAccessoryResultSync() {
        if (null != mPluginStateQueryUtil)
            mPluginStateQueryUtil.cleanStype();
        final String panelId = getId();
        if (TextUtils.isEmpty(panelId) || !isCanOperate()) {
            return new ArrayList<>();
        }

        SecurityPluginResult devices = PanelManager.getInstance().getNetworkRequestManager()
                .requestSecurityAccessoryResultSync(panelId);
        if (null != devices && null != devices.getsTypeSet() && 0 < devices.getsTypeSet().size()
                && null != mPluginStateQueryUtil) {
            for (String stype : devices.getsTypeSet()) {
                mPluginStateQueryUtil.addStypeWithFilter(stype);
            }
        }
        ArrayList<SecurityDevice> resultList = new ArrayList<>();
        if (null != devices) {
            if (null != devices.getPirSensors() && 0 < devices.getPirSensors().size()) {
                for (SecurityDevice device : devices.getPirSensors()) {
                    mSecurityIds.add(device.getId());
                    mPluginDeviceMap.put(device.getId(), device);
                    resultList.add(device);
                }
            }
            if (null != devices.getDoorWindowSensors() && 0 < devices.getDoorWindowSensors().size()) {
                for (SecurityDevice device : devices.getDoorWindowSensors()) {
                    mSecurityIds.add(device.getId());
                    mPluginDeviceMap.put(device.getId(), device);
                    resultList.add(device);
                }
            }
            if (null != devices.getLiquidSensors() && 0 < devices.getLiquidSensors().size()) {
                for (SecurityDevice device : devices.getLiquidSensors()) {
                    mSecurityIds.add(device.getId());
                    mPluginDeviceMap.put(device.getId(), device);
                    resultList.add(device);
                }
            }
            if (null != devices.getSmokeSensors() && 0 < devices.getSmokeSensors().size()) {
                for (SecurityDevice device : devices.getSmokeSensors()) {
                    mSecurityIds.add(device.getId());
                    mPluginDeviceMap.put(device.getId(), device);
                    resultList.add(device);
                }
            }
            if (null != devices.getPanicButtons() && 0 < devices.getPanicButtons().size()) {
                for (SecurityDevice device : devices.getPanicButtons()) {
                    mSecurityIds.add(device.getId());
                    mPluginDeviceMap.put(device.getId(), device);
                    resultList.add(device);
                }
            }
            if (null != devices.getWiredBridges() && 0 < devices.getWiredBridges().size()) {
                for (SecurityDevice device : devices.getWiredBridges()) {
                    mSecurityIds.add(device.getId());
                    mPluginDeviceMap.put(device.getId(), device);
                    resultList.add(device);
                }
            }
        }

        return resultList;
    }

    public ArrayList<DoorSensorDevice> requestDoorSensorResultSync() {
        if (null != mPluginStateQueryUtil)
            mPluginStateQueryUtil.cleanStype();
        final String panelId = getId();
        if (TextUtils.isEmpty(panelId) || !isCanOperate()) {
            return new ArrayList<>();
        }

        DoorSensorResult result = PanelManager.getInstance().getNetworkRequestManager()
                .requestDoorSensorResultSync(panelId);
        if (null != result && null != result.getsTypeSet() && 0 < result.getsTypeSet().size()
                && null != mPluginStateQueryUtil) {
            for (String stype : result.getsTypeSet()) {
                mPluginStateQueryUtil.addStypeWithFilter(stype);
            }
        }
        ArrayList<DoorSensorDevice> resultList = new ArrayList<>();
        if (null != result) {
            Device oldDevice;
            DoorSensorDevice sensorDevice;
            if (null != result.getOldDoorSensors() && 0 < result.getOldDoorSensors().size()) {
                for (DoorSensorDevice device : result.getOldDoorSensors()) {
                    mDoorSensorIds.add(device.getId());
                    oldDevice = mPluginDeviceMap.get(device.getId());
                    if (oldDevice instanceof DoorSensorDevice) {
                        DDLog.i(TAG, "更新Doorsensor信息");
                        sensorDevice = (DoorSensorDevice) oldDevice;
                        sensorDevice.updateInfo(device);
                        resultList.add(sensorDevice);
                        device.destory();
                    } else {
                        mPluginDeviceMap.put(device.getId(), device);
                        resultList.add(device);
                    }
                }
            }
            if (null != result.getNewDoorSensors() && 0 < result.getNewDoorSensors().size()) {
                for (DoorSensorDevice device : result.getNewDoorSensors()) {
                    mDoorSensorIds.add(device.getId());
                    oldDevice = mPluginDeviceMap.get(device.getId());
                    if (oldDevice instanceof DoorSensorDevice) {
                        DDLog.i(TAG, "更新Doorsensor信息");
                        sensorDevice = (DoorSensorDevice) oldDevice;
                        sensorDevice.updateInfo(device);
                        resultList.add(sensorDevice);
                        device.destory();
                    } else {
                        mPluginDeviceMap.put(device.getId(), device);
                        resultList.add(device);
                    }
                }
            }
        }
        return resultList;
    }

    public ArrayList<WirelessSirenDevice> requestWirelessSirenResultSync() {
        if (null != mPluginStateQueryUtil)
            mPluginStateQueryUtil.cleanStype();
        final String panelId = getId();
        if (TextUtils.isEmpty(panelId) || !isCanOperate()) {
            return new ArrayList<>();
        }

        SimplePluginResult<WirelessSirenDevice> result = PanelManager.getInstance().getNetworkRequestManager()
                .requestWirelessSirenResultSync(panelId);
        if (null != result && null != result.getsTypeSet() && 0 < result.getsTypeSet().size()
                && null != mPluginStateQueryUtil) {
            for (String stype : result.getsTypeSet()) {
                mPluginStateQueryUtil.addStypeWithFilter(stype);
            }
        }
        if (null != result && null != result.getPluginList()) {
            for (WirelessSirenDevice device : result.getPluginList()) {
                mWirelessSirenIds.add(device.getId());
                mPluginDeviceMap.put(device.getId(), device);
            }
            return result.getPluginList();
        } else {
            return new ArrayList<>();
        }
    }

    public ArrayList<KeypadDevice> requestKeypadDeviceListSync() {
        if (null != mPluginStateQueryUtil)
            mPluginStateQueryUtil.cleanStype();
        final String panelId = getId();
        if (TextUtils.isEmpty(panelId) || !isCanOperate()) {
            return new ArrayList<>();
        }

        SimplePluginResult<KeypadDevice> result = PanelManager.getInstance().getNetworkRequestManager()
                .requestKeypadResultSync(panelId);
        if (null != result && null != result.getsTypeSet() && 0 < result.getsTypeSet().size()
                && null != mPluginStateQueryUtil) {
            for (String stype : result.getsTypeSet()) {
                mPluginStateQueryUtil.addStypeWithFilter(stype);
            }
        }
        if (null != result && null != result.getPluginList()) {
            for (KeypadDevice device : result.getPluginList()) {
                mPluginDeviceMap.put(device.getId(), device);
                mKeypadIds.add(device.getId());
            }
            return result.getPluginList();
        } else {
            return new ArrayList<>();
        }
    }

    public ArrayList<RemoteControlDevice> requestRemoteControlResultSync() {
        if (null != mPluginStateQueryUtil)
            mPluginStateQueryUtil.cleanStype();
        final String panelId = getId();
        if (TextUtils.isEmpty(panelId) || !isCanOperate()) {
            return new ArrayList<>();
        }

        SimplePluginResult<RemoteControlDevice> result = PanelManager.getInstance().getNetworkRequestManager()
                .requestRemoteControlResultSync(panelId);
        if (null != result && null != result.getsTypeSet() && 0 < result.getsTypeSet().size()
                && null != mPluginStateQueryUtil) {
            for (String stype : result.getsTypeSet()) {
                mPluginStateQueryUtil.addStypeWithFilter(stype);
            }
        }
        if (null != result && null != result.getPluginList()) {
            for (RemoteControlDevice device : result.getPluginList()) {
                mPluginDeviceMap.put(device.getId(), device);
                mRemoteControlIds.add(device.getId());
            }
            return result.getPluginList();
        } else {
            return new ArrayList<>();
        }
    }

    public ArrayList<DoorBellDevice> requestListDoorBellSync() {
        final String panelId = getId();
        if (TextUtils.isEmpty(panelId) || !isCanOperate()) {
            return new ArrayList<>();
        }

        ArrayList<DoorBellDevice> doorbellDeviceList = PanelManager.getInstance().getNetworkRequestManager()
                .requestListDoorBellSync(panelId, panelToken);
        for (DoorBellDevice device : doorbellDeviceList) {
            mDoorbellIds.add(device.getId());
            mPluginDeviceMap.put(device.getId(), device);
        }

        return doorbellDeviceList;
    }

    public ArrayList<OtherDevice> requestOtherPluginListSync() {
        final String panelId = getId();
        if (TextUtils.isEmpty(panelId) || !isCanOperate()) {
            return new ArrayList<>();
        }

        ArrayList<OtherDevice> otherDevices = PanelManager.getInstance().getNetworkRequestManager()
                .requestOtherPluginListSync(panelId);
        for (OtherDevice device : otherDevices) {
            mDoorbellIds.add(device.getId());
            mPluginDeviceMap.put(device.getId(), device);
        }

        return otherDevices;
    }


    @NonNull
    public List<Device> requestCacheHomePlugin(final PanelCacheInfo cacheInfo) {
        return createHomePluginDeviceFromCache(cacheInfo);
    }

    @NonNull
    public List<Device> requestLocalAndNewHomePlugin(final PanelCacheInfo cacheInfo) {
        return requestHomePluginListSyncNew(cacheInfo);
    }

    @NonNull
    public List<Device> requestAllHomePlugin(final PanelCacheInfo cacheInfo) {
        ArrayList<Device> cacheDevice = createHomePluginDeviceFromCache(cacheInfo);
        // 加载全部网络数据
        final ArrayList<Device> netDevices = createHomePluginDeviceFromNetwork(cacheInfo, 1);
        final ArrayList<Device> devices = new ArrayList<>(netDevices);
        //devices.addAll(netDevices);
        if (cacheDevice != null && cacheDevice.size() > 0) {
            for (Device device : cacheDevice) {
                if (!contain(devices, device)) {
                    devices.add(device);
                } else {
                    device.destory();
                }
            }
        }
        DDLog.i(TAG, getId() + " 仅返缓存+网络, size=" + devices.size());
        return devices;
    }

    public ArrayList<Device> requestHomePluginListSyncNew(final PanelCacheInfo cacheInfo,
                                                          final boolean cacheFirst) {
        // cacheFirst并且有缓存，仅返回缓存
        final ArrayList<Device> cacheDevice = createHomePluginDeviceFromCache(cacheInfo);
        if (cacheFirst && cacheDevice.size() > 0) {
            DDLog.i(TAG, getId() + "仅返回缓存, size=" + cacheDevice.size());
            return cacheDevice;
        }
        return requestHomePluginListSyncNew(cacheInfo);
    }

    public ArrayList<Device> requestHomePluginListSyncNew(final PanelCacheInfo cacheInfo) {
        ArrayList<Device> cacheDevice = createHomePluginDeviceFromCache(cacheInfo);
        // 从上一次加载的时间开始加载
        final String panelId = getId();
        final boolean isMyCache = null != cacheInfo && panelId.equals(cacheInfo.getPanelId());
        final long newestAddTime = isMyCache ? cacheInfo.getUpdateTime() + 1 : 1;
        final ArrayList<Device> netDevices = createHomePluginDeviceFromNetwork(cacheInfo, newestAddTime);
        final ArrayList<Device> devices = new ArrayList<>(netDevices);
        //devices.addAll(netDevices);
        if (cacheDevice != null && cacheDevice.size() > 0) {
            for (Device device : cacheDevice) {
                if (!contain(devices, device)) {
                    devices.add(device);
                } else {
                    device.destory();
                }
            }
        }
        DDLog.i(TAG, getId() + " 仅返缓存+网络, size=" + devices.size());
        return devices;
    }

    private boolean contain(List<Device> origin, Device device) {
        boolean contain = false;
        for (Device device1 : origin) {
            if (device1.getId().equals(device.getId())) {
                contain = true;
                break;
            }
        }
        return contain;
    }

    /**
     * 从缓存创建Device
     *
     * @return
     */
    private ArrayList<Device> createHomePluginDeviceFromNetwork(@Nullable PanelCacheInfo
                                                                        cacheInfo, long newestAddTime) {
        final ArrayList<Device> devices = new ArrayList<>();
        // 1. 已经被删除的主机，不能从网络请求数据了
        final String panelId = getId();
        if (!isCanOperate() || TextUtils.isEmpty(panelId)) {
            return devices;
        }

        // 从上一次加载的时间开始加载
        final boolean isMyCache = null != cacheInfo && panelId.equals(cacheInfo.getPanelId());

        // 没有分页的，都是获取到最新的数据
        List<String> stypeList = Arrays.asList(HOME_STYPE_ALL);
        List<CommonAccessoriesBean> accessoriesBeanList = PanelManager.getInstance().getNetworkRequestManager()
                .listAccessoriesSync(PanelManager.getInstance().getCurrentHomeId(), getId(), newestAddTime, stypeList, false);
        boolean needSave = false;
        if (null != accessoriesBeanList && accessoriesBeanList.size() > 0) {
            for (CommonAccessoriesBean commonAccessoriesBean : accessoriesBeanList) {
                String pluginId = commonAccessoriesBean.getId();
                String stype = commonAccessoriesBean.getStype();
                String sendId = commonAccessoriesBean.getSendid();
                int category = PanelPluginUtil.getAskPluginType(stype);
                boolean isOn = PanelConstant.STATUS_OPENED_ASK_PLUG == commonAccessoriesBean.getEnable();

                // 缓存仅支持部分门磁和部分插座
                if (PanelConstant.Category.SMART_PLUGS != category
                        && PanelConstant.Category.SIGNAL_REPEATER_PLUG != category
                        && PanelConstant.Category.DOOR_SENSOR != category) {
                    continue;
                }

                BasePluginDevice<?> newPlugin;
                final Map<String, Object> pluginParams = new HashMap<>();
                if (PanelConstant.Category.SMART_PLUGS == category
                        || PanelConstant.Category.SIGNAL_REPEATER_PLUG == category) {
                    newPlugin = new SmartPlugDevice(pluginId, PluginConstants.CATEGORY_10, stype, getId());
                    pluginParams.put(PanelDataKey.SmartPlug.IS_ASK_SMART_PLUG, true);
                    pluginParams.put(PanelDataKey.PLUGIN_SWITCH_STATE, isOn
                            ? PanelConstant.PluginSwitchState.OPENED
                            : PanelConstant.PluginSwitchState.CLOSED);
                    pluginParams.put(PanelDataKey.HAVE_LOADING_STATE, true);
                } else {
                    newPlugin = new DoorSensorDevice(pluginId, PluginConstants.CATEGORY_10, stype, getId());
                    pluginParams.put(PanelDataKey.HAVE_LOADING_STATE, false);
                }

                pluginParams.put(PanelDataKey.CATEGORY, PluginConstants.CATEGORY_10);
                pluginParams.put(PanelDataKey.NAME, commonAccessoriesBean.getName());
                pluginParams.put(PanelDataKey.SEND_ID, sendId);
                pluginParams.put(PanelDataKey.S_TYPE, stype);

                pluginParams.put(PanelDataKey.LOADING_STATE, PanelConstant.PluginLoadingState.SUCCESS);
                pluginParams.put(PanelDataKey.HAVE_ONLINE_STATE, true);
                pluginParams.put(PanelDataKey.IS_ONLINE, true);

                newPlugin.setInfo(pluginParams);
                newPlugin.setFlagCache(isMyCache && cacheInfo.containId(pluginId));
                newPlugin.setLoaded(true);

                mPluginDeviceMap.put(pluginId, newPlugin);
                mHomePluginIds.add(pluginId);
                devices.add(newPlugin);

                if (isMyCache) {
                    cacheInfo.addPlugin(pluginId, stype, sendId, commonAccessoriesBean.getAdd_time(), commonAccessoriesBean.getName());
                    needSave = true;
                    if (commonAccessoriesBean.getAdd_time() > newestAddTime) {
                        newestAddTime = commonAccessoriesBean.getAdd_time();
                    }
                }
            }
        }
        if (needSave) {
            cacheInfo.setUpdateTime(newestAddTime);
            PanelManager.getInstance().saveDeviceCache();
        }
        DDLog.i(TAG, getId() + ": 读取到首页网络配件列表size= " + devices.size());
        return devices;
    }

    /**
     * 从缓存创建Device
     *
     * @return
     */
    @NonNull
    private ArrayList<Device> createHomePluginDeviceFromCache(PanelCacheInfo cacheInfo) {
        final ArrayList<Device> devices = new ArrayList<>();
        final String panelId = getId();
        if (!TextUtils.isEmpty(panelId) && null != cacheInfo && panelId.equals(cacheInfo.getPanelId())) {
            List<PanelCacheInfo.PluginCache> pluginList = cacheInfo.getPluginList();
            if (null != pluginList && pluginList.size() > 0) {
                for (PanelCacheInfo.PluginCache pluginCache : pluginList) {
                    // 1. 因为首页只有ASK配件，如果缓存中的配件不是ASK配件，无需处理
                    if (!(pluginCache instanceof PanelCacheInfo.AskPluginCache)) {
                        continue;
                    }
                    PanelCacheInfo.AskPluginCache askPluginCache = (PanelCacheInfo.AskPluginCache) pluginCache;
                    String pluginId = askPluginCache.getId();

                    // 如果缓存的Device已经在内存中
                    Device device = mPluginDeviceMap.get(pluginId);
                    if (device instanceof BasePluginDevice) {
                        BasePluginDevice pluginDevice = (BasePluginDevice) device;
                        pluginDevice.setFlagCache(true);
                        if (!isCanOperate())
                            pluginDevice.setFlagDeleted(true);
                    }

                    String stype = askPluginCache.getsType();
                    String sendId = askPluginCache.getSendId();
                    int category = PanelPluginUtil.getAskPluginType(stype);
                    // 缓存仅支持部分门磁和部分插座
                    if (PanelConstant.Category.SMART_PLUGS != category
                            && PanelConstant.Category.DOOR_SENSOR != category
                            && PanelConstant.Category.SIGNAL_REPEATER_PLUG != category) {
                        continue;
                    }

                    // 如果内存中的Device不是配件Device，先移除，一般不会出现这个情况
                    if (null != device) {
                        device.destory();
                        mPluginDeviceMap.remove(pluginId);
                    }

                    final Map<String, Object> pluginParams = new HashMap<>();
                    BasePluginDevice<?> cachePlugin;
                    // 缓存仅支持部分门磁和部分插座
                    if (PanelConstant.Category.SMART_PLUGS == category
                            || PanelConstant.Category.SIGNAL_REPEATER_PLUG == category) {
                        cachePlugin = new SmartPlugDevice(pluginId, PluginConstants.CATEGORY_10, stype, getId());
                        pluginParams.put(PanelDataKey.SmartPlug.IS_ASK_SMART_PLUG, true);

                    } else {
                        cachePlugin = new DoorSensorDevice(pluginId, PluginConstants.CATEGORY_10, stype, getId());
                        pluginParams.put(PanelDataKey.HAVE_LOADING_STATE, false);
                    }

                    pluginParams.put(PanelDataKey.CATEGORY, PluginConstants.CATEGORY_10);
                    pluginParams.put(PanelDataKey.SEND_ID, sendId);
                    pluginParams.put(PanelDataKey.S_TYPE, stype);
                    pluginParams.put(PanelDataKey.NAME, askPluginCache.getName());

                    pluginParams.put(PanelDataKey.LOADING_STATE, PanelConstant.PluginLoadingState.SUCCESS);
                    pluginParams.put(PanelDataKey.HAVE_LOADING_STATE, true);
                    pluginParams.put(PanelDataKey.HAVE_ONLINE_STATE, true);
                    pluginParams.put(PanelDataKey.IS_ONLINE, false);
                    cachePlugin.setInfo(pluginParams);

                    cachePlugin.setFlagCache(true);
                    cachePlugin.setLoaded(false);
                    if (!isCanOperate()) {
                        cachePlugin.setFlagDeleted(true);
                    }

                    mPluginDeviceMap.put(pluginId, cachePlugin);
                    mHomePluginIds.add(pluginId);
                    devices.add(cachePlugin);
                }
            }
        }
        DDLog.i(TAG, getId() + ": 读取到首页缓存配件列表size= " + devices.size() + ", isDelete: " + getFlagDeleted());
        return devices;
    }

    public ArrayList<Device> requestHomePluginListSync() {
        final String panelId = getId();
        if (TextUtils.isEmpty(panelId) || !isCanOperate()) {
            return new ArrayList<>();
        }

        ArrayList<Device> homePlugins = new ArrayList<>();
        HomePluginResult homePluginResult = PanelManager.getInstance().getNetworkRequestManager()
                .requestGetPluginInfoSync(getId());

        Device oldDevice;
        SmartPlugDevice oldPlugDevice;
        for (SmartPlugDevice device : homePluginResult.getSmartPlugList()) {
            oldDevice = mPluginDeviceMap.get(device.getId());
            if (oldDevice instanceof SmartPlugDevice) {
                DDLog.i(TAG, "更新SmartPlug信息");
                oldPlugDevice = (SmartPlugDevice) oldDevice;
                oldPlugDevice.updateInfo(device);
                homePlugins.add(oldPlugDevice);
                device.destory();
            } else {
                mPluginDeviceMap.put(device.getId(), device);
                homePlugins.add(device);
            }

            mSmartPlugIds.add(device.getId());
            mHomePluginIds.add(device.getId());
        }
        DoorSensorDevice doorSensorDevice;
        for (DoorSensorDevice device : homePluginResult.getDoorSensorList()) {
            oldDevice = mPluginDeviceMap.get(device.getId());
            if (oldDevice instanceof DoorSensorDevice) {
                DDLog.i(TAG, "更新Doorsensor信息");
                doorSensorDevice = (DoorSensorDevice) oldDevice;
                doorSensorDevice.updateInfo(device);
                homePlugins.add(doorSensorDevice);
                device.destory();
            } else {
                mPluginDeviceMap.put(device.getId(), device);
                homePlugins.add(device);
            }

            mDoorSensorIds.add(device.getId());
            mHomePluginIds.add(device.getId());
        }
        for (DoorSensorDevice device : homePluginResult.getVibrateSensorList()) {
            oldDevice = mPluginDeviceMap.get(device.getId());
            if (oldDevice instanceof DoorSensorDevice) {
                DDLog.i(TAG, "更新Doorsensor信息");
                doorSensorDevice = (DoorSensorDevice) oldDevice;
                doorSensorDevice.updateInfo(device);
                homePlugins.add(doorSensorDevice);
                device.destory();
            } else {
                mPluginDeviceMap.put(device.getId(), device);
                homePlugins.add(device);
            }

            mDoorSensorIds.add(device.getId());
            mHomePluginIds.add(device.getId());
        }
        return homePlugins;
    }

    public boolean releasePluginDeviceByType(String sub) {
        if (null != mPluginStateQueryUtil)
            mPluginStateQueryUtil.stopQueryPluginStatusRoundRobin();
        if (TextUtils.isEmpty(sub)) {
            return false;
        }
        DDLog.i(TAG, "releasePluginDeviceByType, type: " + sub);
        boolean released = true;
        Set<String> targetPluginIds;
        switch (sub) {
            case PanelConstant.DeviceType.SMART_BUTTON:
                targetPluginIds = mSmartButtonIds;
                break;
            case PanelConstant.DeviceType.SIGNAL_REPEATER_PLUG:
                targetPluginIds = mSignalRepeaterPlugIds;
                break;
            case PanelConstant.DeviceType.SMART_PLUG:
                targetPluginIds = mSmartPlugIds;
                break;
            case PanelConstant.DeviceType.ROLLER_SHUTTER:
                targetPluginIds = mRelayIds;
                break;
            case PanelConstant.DeviceType.SECURITY_ACCESSORY:
                targetPluginIds = mSecurityIds;
                break;
            case PanelConstant.DeviceType.DOOR_WINDOW_SENSOR:
                targetPluginIds = mDoorSensorIds;
                break;
            case PanelConstant.DeviceType.WIRELESS_SIREN:
                targetPluginIds = mWirelessSirenIds;
                break;
            case PanelConstant.DeviceType.REMOTE_CONTROL:
                targetPluginIds = mRemoteControlIds;
                break;
            case PanelConstant.DeviceType.KEYBOARD_KEY_TAGS:
                targetPluginIds = mKeypadIds;
                break;
            case PanelConstant.DeviceType.DOORBELL:
                targetPluginIds = mDoorbellIds;
                break;
            case PanelConstant.DeviceType.OTHER_PLUGIN:
                targetPluginIds = mOtherPluginIds;
                break;
            case PanelConstant.DeviceType.HOME_PLUGIN:
                targetPluginIds = mHomePluginIds;
                break;
            default:
                targetPluginIds = null;
                released = false;
                break;
        }
        if (null != targetPluginIds && 0 < targetPluginIds.size()) {
            Device device;
            if (targetPluginIds == mHomePluginIds) {
                // 释放首页的配件-需要移除对于配件列表中该配件的id
                for (String deviceId : targetPluginIds) {
                    if (mDoorSensorIds.contains(deviceId)) {
                        mDoorSensorIds.remove(deviceId);
                    }
                    if (mSmartPlugIds.contains(deviceId)) {
                        mSmartPlugIds.remove(deviceId);
                    }
                    if (mSignalRepeaterPlugIds.contains(deviceId)) {
                        mSignalRepeaterPlugIds.remove(deviceId);
                    }

                    device = mPluginDeviceMap.remove(deviceId);
                    if (null != device) {
                        device.destory();
                    }
                }
                targetPluginIds.clear();
            } else {
                // 释放非首页的配件-配件在首页显示，不能释放
                ArrayList<String> needReleaseIds = new ArrayList<>();
                for (String deviceId : targetPluginIds) {
                    if (mHomePluginIds.contains(deviceId)) {
                        continue;
                    }
                    device = mPluginDeviceMap.remove(deviceId);
                    if (null != device) {
                        device.destory();
                    }
                    needReleaseIds.add(deviceId);
                }
                if (0 < needReleaseIds.size()) {
                    targetPluginIds.removeAll(needReleaseIds);
                }
            }
        }
        return released;
    }

    // ************************** 获取配件列表-结束************************* //

    @RestrictTo(RestrictTo.Scope.LIBRARY)
    @Override
    public void dispatchResult(String cmd, Map result) {
        super.dispatchResult(cmd, result);
    }

    @Override
    @RestrictTo(RestrictTo.Scope.LIBRARY)
    public void setFlagDeleted(boolean isDeleted) {
        super.setFlagDeleted(isDeleted);
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY)
    @Override
    public void setFlagCache(boolean isCache) {
        super.setFlagCache(isCache);
    }
}
