package com.dinsafer.panel.bean;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;

/**
 * 包含轮询sType配件列表结果-List
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/17 2:38 PM
 */
public class SimplePluginResult<T> {
    private final ArrayList<T> pluginList; // WirelessSiren
    private final Set<String> sTypeSet; // 需要轮询配件状态的sType Set

    public SimplePluginResult() {
        pluginList = new ArrayList<>();
        sTypeSet = new HashSet<>();
    }

    public ArrayList<T> getPluginList() {
        return pluginList;
    }

    public Set<String> getsTypeSet() {
        return sTypeSet;
    }
}
