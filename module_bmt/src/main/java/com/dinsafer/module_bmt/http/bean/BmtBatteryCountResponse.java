package com.dinsafer.module_bmt.http.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;
import java.util.List;

@Keep
public class BmtBatteryCountResponse extends BaseHttpEntry implements Serializable {


    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean implements Serializable{
            private List<BatteryCountBean> models;

        public List<BatteryCountBean> getModels() {
            return models;
        }

        public void setModels(List<BatteryCountBean> models) {
            this.models = models;
        }
    }

    @Keep
    public static class BatteryCountBean implements Serializable {

    }
}
