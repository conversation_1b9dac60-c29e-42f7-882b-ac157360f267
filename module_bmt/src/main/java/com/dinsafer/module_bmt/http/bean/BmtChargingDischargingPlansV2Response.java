package com.dinsafer.module_bmt.http.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;
import java.util.List;

@Keep
public class BmtChargingDischargingPlansV2Response extends BaseHttpEntry implements Serializable {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        Result = result;
    }

    @Keep
    public static class ResultBean implements Serializable {
        private int smart;
        private int emergency;
        private List<Integer> hope_charge_discharges; // 每小时的期望充放电量，单位：百分比
        private List<Float> relative_price_norms; // 归一化后每小时的相对电价
        private List<Float> user_prices;
        private List<Float> market_prices;    // 每小时的分时批发价
        private List<Integer> plans;  // 每小时的充放电量计划。0：A计划，1：B计划，2：C计划
        private List<Integer> forecast_solars;    // 每小时的预测PV，单位：wh
        private List<Integer> conditions; // 每小时的天气编码。0：部分多云，1：晴天，2：阴天，3：雾，4：下雨，5：下雪，6：雨夹雪
        private List<Long> sunrises;
        private List<Long> sunsets;
        private Float abs_zero_ec_prices;
        private int c1;
        private int c2;
        private int c3;
        private long gmtime;
        private int s1;
        private int s2;
        private long start_time;
        private String timezone;
        private String unit_price;

        public Float getAbs_zero_ec_prices() {
            return abs_zero_ec_prices;
        }

        public void setAbs_zero_ec_prices(Float abs_zero_ec_prices) {
            this.abs_zero_ec_prices = abs_zero_ec_prices;
        }

        public int getC1() {
            return c1;
        }

        public void setC1(int c1) {
            this.c1 = c1;
        }

        public int getC2() {
            return c2;
        }

        public void setC2(int c2) {
            this.c2 = c2;
        }

        public int getC3() {
            return c3;
        }

        public void setC3(int c3) {
            this.c3 = c3;
        }

        public List<Integer> getConditions() {
            return conditions;
        }

        public void setConditions(List<Integer> conditions) {
            this.conditions = conditions;
        }

        public int getEmergency() {
            return emergency;
        }

        public void setEmergency(int emergency) {
            this.emergency = emergency;
        }

        public List<Integer> getForecast_solars() {
            return forecast_solars;
        }

        public void setForecast_solars(List<Integer> forecast_solars) {
            this.forecast_solars = forecast_solars;
        }

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        public List<Integer> getHope_charge_discharges() {
            return hope_charge_discharges;
        }

        public void setHope_charge_discharges(List<Integer> hope_charge_discharges) {
            this.hope_charge_discharges = hope_charge_discharges;
        }

        public List<Float> getMarket_prices() {
            return market_prices;
        }

        public void setMarket_prices(List<Float> market_prices) {
            this.market_prices = market_prices;
        }

        public List<Integer> getPlans() {
            return plans;
        }

        public void setPlans(List<Integer> plans) {
            this.plans = plans;
        }

        public List<Float> getRelative_price_norms() {
            return relative_price_norms;
        }

        public void setRelative_price_norms(List<Float> relative_price_norms) {
            this.relative_price_norms = relative_price_norms;
        }

        public int getS1() {
            return s1;
        }

        public void setS1(int s1) {
            this.s1 = s1;
        }

        public int getS2() {
            return s2;
        }

        public void setS2(int s2) {
            this.s2 = s2;
        }

        public int getSmart() {
            return smart;
        }

        public void setSmart(int smart) {
            this.smart = smart;
        }

        public long getStart_time() {
            return start_time;
        }

        public void setStart_time(long start_time) {
            this.start_time = start_time;
        }

        public List<Long> getSunrises() {
            return sunrises;
        }

        public void setSunrises(List<Long> sunrises) {
            this.sunrises = sunrises;
        }

        public List<Long> getSunsets() {
            return sunsets;
        }

        public void setSunsets(List<Long> sunsets) {
            this.sunsets = sunsets;
        }

        public String getTimezone() {
            return timezone;
        }

        public void setTimezone(String timezone) {
            this.timezone = timezone;
        }

        public String getUnit_price() {
            return unit_price;
        }

        public void setUnit_price(String unit_price) {
            this.unit_price = unit_price;
        }

        public List<Float> getUser_prices() {
            return user_prices;
        }

        public void setUser_prices(List<Float> user_prices) {
            this.user_prices = user_prices;
        }
    }
}
