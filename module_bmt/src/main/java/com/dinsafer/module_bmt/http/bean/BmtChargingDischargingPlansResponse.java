package com.dinsafer.module_bmt.http.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;
import java.util.List;

@Keep
public class BmtChargingDischargingPlansResponse extends BaseHttpEntry implements Serializable {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        Result = result;
    }

    @Keep
    public static class ResultBean implements Serializable {
        private int c1;
        private int c2;
        private int c3;
        private List<List<Float>> electricity_price_percents; // 每小时的相对电价百分比
        private int emergency;
        private List<List<Integer>> forecast_solars;  // 每小时的预测PV
        private long gmtime;
        private List<List<Integer>> hope_charge_discharges; // 每小时的期望充放电量，单位：百分比
        private List<List<Integer>> plans;  // 每小时的充放电量计划。0：A计划，1：B计划，2：C计划
        private int s1;
        private int s2;
        private int smart;
        private long start_time;  // 数据起始时间，UTC 时间，单位 秒
        private String timezone;
        private List<Integer> weather_conditions;

        public int getC1() {
            return c1;
        }

        public void setC1(int c1) {
            this.c1 = c1;
        }

        public int getC2() {
            return c2;
        }

        public void setC2(int c2) {
            this.c2 = c2;
        }

        public int getC3() {
            return c3;
        }

        public void setC3(int c3) {
            this.c3 = c3;
        }

        public List<List<Float>> getElectricity_price_percents() {
            return electricity_price_percents;
        }

        public void setElectricity_price_percents(List<List<Float>> electricity_price_percents) {
            this.electricity_price_percents = electricity_price_percents;
        }

        public int getEmergency() {
            return emergency;
        }

        public void setEmergency(int emergency) {
            this.emergency = emergency;
        }

        public List<List<Integer>> getForecast_solars() {
            return forecast_solars;
        }

        public void setForecast_solars(List<List<Integer>> forecast_solars) {
            this.forecast_solars = forecast_solars;
        }

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        public List<List<Integer>> getHope_charge_discharges() {
            return hope_charge_discharges;
        }

        public void setHope_charge_discharges(List<List<Integer>> hope_charge_discharges) {
            this.hope_charge_discharges = hope_charge_discharges;
        }

        public List<List<Integer>> getPlans() {
            return plans;
        }

        public void setPlans(List<List<Integer>> plans) {
            this.plans = plans;
        }

        public int getS1() {
            return s1;
        }

        public void setS1(int s1) {
            this.s1 = s1;
        }

        public int getS2() {
            return s2;
        }

        public void setS2(int s2) {
            this.s2 = s2;
        }

        public int getSmart() {
            return smart;
        }

        public void setSmart(int smart) {
            this.smart = smart;
        }

        public long getStart_time() {
            return start_time;
        }

        public void setStart_time(long start_time) {
            this.start_time = start_time;
        }

        public String getTimezone() {
            return timezone;
        }

        public void setTimezone(String timezone) {
            this.timezone = timezone;
        }

        public List<Integer> getWeather_conditions() {
            return weather_conditions;
        }

        public void setWeather_conditions(List<Integer> weather_conditions) {
            this.weather_conditions = weather_conditions;
        }
    }
}
