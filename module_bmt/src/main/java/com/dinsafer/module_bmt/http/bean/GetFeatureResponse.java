package com.dinsafer.module_bmt.http.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;
import java.util.List;

@Keep
public class GetFeatureResponse extends BaseHttpEntry implements Serializable  {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        Result = result;
    }

    @Keep
    public static class ResultBean implements Serializable {
        private List<String> citys;
        private String country_code;
        private String country_name;
        private boolean elec_support;
        private boolean grid_conn_support;
        private boolean grid_to_battery;
        private String timezone;

        public List<String> getCitys() {
            return citys;
        }

        public void setCitys(List<String> citys) {
            this.citys = citys;
        }

        public String getCountry_code() {
            return country_code;
        }

        public void setCountry_code(String country_code) {
            this.country_code = country_code;
        }

        public String getCountry_name() {
            return country_name;
        }

        public void setCountry_name(String country_name) {
            this.country_name = country_name;
        }

        public boolean isElec_support() {
            return elec_support;
        }

        public void setElec_support(boolean elec_support) {
            this.elec_support = elec_support;
        }

        public boolean isGrid_conn_support() {
            return grid_conn_support;
        }

        public void setGrid_conn_support(boolean grid_conn_support) {
            this.grid_conn_support = grid_conn_support;
        }

        public boolean isGrid_to_battery() {
            return grid_to_battery;
        }

        public void setGrid_to_battery(boolean grid_to_battery) {
            this.grid_to_battery = grid_to_battery;
        }

        public String getTimezone() {
            return timezone;
        }

        public void setTimezone(String timezone) {
            this.timezone = timezone;
        }
    }
}
