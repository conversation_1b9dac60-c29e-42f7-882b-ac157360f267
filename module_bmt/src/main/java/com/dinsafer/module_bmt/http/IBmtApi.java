package com.dinsafer.module_bmt.http;

import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.module_bmt.http.bean.BmtBSensorResponse;
import com.dinsafer.module_bmt.http.bean.BmtBatteryCountResponse;
import com.dinsafer.module_bmt.http.bean.BmtChargingDischargingPlansResponse;
import com.dinsafer.module_bmt.http.bean.BmtChargingDischargingPlansV2Response;
import com.dinsafer.module_bmt.http.bean.BmtE2ELoginResponse;
import com.dinsafer.module_bmt.http.bean.BmtListResponse;
import com.dinsafer.module_bmt.http.bean.BmtLocationResponse;
import com.dinsafer.module_bmt.http.bean.BmtStatEcoResponse;
import com.dinsafer.module_bmt.http.bean.BmtStatsResponse;
import com.dinsafer.module_bmt.http.bean.ElecPriceInfoResponse;
import com.dinsafer.module_bmt.http.bean.GetCityListResponse;
import com.dinsafer.module_bmt.http.bean.GetCountryListResponse;
import com.dinsafer.module_bmt.http.bean.GetDefaultPricePercentResponse;
import com.dinsafer.module_bmt.http.bean.GetFeatureResponse;
import com.dinsafer.module_bmt.http.bean.GetFusePowerCapsResponse;
import com.dinsafer.module_bmt.http.bean.GetIsDualPowerOpenResponse;
import com.dinsafer.module_bmt.http.bean.GetRegionResponse;
import com.dinsafer.module_bmt.http.bean.UpdateRegionResponse;

import java.util.Map;

import retrofit2.Call;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;
import retrofit2.http.Url;

public interface IBmtApi {
    @POST
    @FormUrlEncoded
    Call<BmtListResponse> fetchBmtList(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<BmtE2ELoginResponse> e2elogin(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> stringCall(@Url String url, @FieldMap Map<String, Object> map);


    @POST
    @FormUrlEncoded
    Call<BmtListResponse> searchBmt(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<BmtBatteryCountResponse> getBmtBatteryCount(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<GetCityListResponse> getCityList(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<GetCountryListResponse> getCountyList(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<UpdateRegionResponse> updateRegion(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<GetRegionResponse> getRegion(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<BmtStatsResponse> getBmtStats(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<BmtStatEcoResponse> getBmtEco(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<BmtBSensorResponse> getBmtBSensorStatus(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> bindInverter(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<ElecPriceInfoResponse> getElecPriceInfo(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<GetFusePowerCapsResponse> getFusePowerCaps(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<GetFeatureResponse> getFeature(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<GetIsDualPowerOpenResponse> getIsDualPowerOpen(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<BmtChargingDischargingPlansResponse> getChargingDischargingPlans(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<BmtChargingDischargingPlansV2Response> getChargingDischargingPlansV2(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<GetDefaultPricePercentResponse> getDefaultPricePercent(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<BmtLocationResponse> getBmtLocation(@Url String url, @FieldMap Map<String, Object> map);
}
