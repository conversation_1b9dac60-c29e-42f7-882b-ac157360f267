package com.dinsafer.module_bmt.http.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;

@Keep
public class GetDefaultPricePercentResponse  extends BaseHttpEntry implements Serializable {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        Result = result;
    }

    @Keep
    public static class ResultBean implements Serializable {
        private int c1;
        private int c2;
        private int c3;
        private int emergency_reserve;
        private int s1;
        private int s2;
        private int smart_reserve;

        public int getC1() {
            return c1;
        }

        public void setC1(int c1) {
            this.c1 = c1;
        }

        public int getC2() {
            return c2;
        }

        public void setC2(int c2) {
            this.c2 = c2;
        }

        public int getC3() {
            return c3;
        }

        public void setC3(int c3) {
            this.c3 = c3;
        }

        public int getEmergency_reserve() {
            return emergency_reserve;
        }

        public void setEmergency_reserve(int emergency_reserve) {
            this.emergency_reserve = emergency_reserve;
        }

        public int getS1() {
            return s1;
        }

        public void setS1(int s1) {
            this.s1 = s1;
        }

        public int getS2() {
            return s2;
        }

        public void setS2(int s2) {
            this.s2 = s2;
        }

        public int getSmart_reserve() {
            return smart_reserve;
        }

        public void setSmart_reserve(int smart_reserve) {
            this.smart_reserve = smart_reserve;
        }
    }
}
