package com.dinsafer.module_bmt.http.bean;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;

import androidx.annotation.Keep;

@Keep
public class UpdateRegionResponse extends BaseHttpEntry implements Serializable {


    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean implements Serializable {
        private String band;
        private boolean ignore;

        public String getBand() {
            return band;
        }

        public void setBand(String band) {
            this.band = band;
        }

        public boolean isIgnore() {
            return ignore;
        }

        public void setIgnore(boolean ignore) {
            this.ignore = ignore;
        }
    }

}
