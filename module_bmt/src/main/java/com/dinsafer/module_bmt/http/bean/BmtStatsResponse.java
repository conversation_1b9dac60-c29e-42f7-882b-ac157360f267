package com.dinsafer.module_bmt.http.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;
import java.util.List;

@Keep
public class BmtStatsResponse extends BaseHttpEntry implements Serializable {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean implements Serializable{
        private Long gmtime;
        private List<List<Float>> data;
        private Integer interval;
        private Long start_time;
        private String timezone;
        private Boolean b_sensor_installed;

        public Long getGmtime() {
            return gmtime;
        }

        public void setGmtime(Long gmtime) {
            this.gmtime = gmtime;
        }

        public List<List<Float>> getData() {
            return data;
        }

        public void setData(List<List<Float>> data) {
            this.data = data;
        }

        public Integer getInterval() {
            return interval;
        }

        public void setInterval(Integer interval) {
            this.interval = interval;
        }

        public Long getStart_time() {
            return start_time;
        }

        public void setStart_time(Long start_time) {
            this.start_time = start_time;
        }

        public String getTimezone() {
            return timezone;
        }

        public void setTimezone(String timezone) {
            this.timezone = timezone;
        }

        public Boolean getB_sensor_installed() {
            return b_sensor_installed;
        }

        public void setB_sensor_installed(Boolean b_sensor_installed) {
            this.b_sensor_installed = b_sensor_installed;
        }
    }
}
