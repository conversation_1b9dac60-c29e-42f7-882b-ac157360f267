package com.dinsafer.module_bmt.http.bean;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;

import androidx.annotation.Keep;

@Keep
public class GetRegionResponse extends BaseHttpEntry implements Serializable {


    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean implements Serializable {
        private String region;
        private String delivery_area;
        private boolean grid_conn_support;
        private boolean smart_tariff_tracking;

        public String getRegion() {
            return region;
        }

        public void setRegion(String region) {
            this.region = region;
        }

        public String getDelivery_area() {
            return delivery_area;
        }

        public void setDelivery_area(String delivery_area) {
            this.delivery_area = delivery_area;
        }

        public boolean isGrid_conn_support() {
            return grid_conn_support;
        }

        public void setGrid_conn_support(boolean grid_conn_support) {
            this.grid_conn_support = grid_conn_support;
        }

        public boolean isSmart_tariff_tracking() {
            return smart_tariff_tracking;
        }

        public void setSmart_tariff_tracking(boolean smart_tariff_tracking) {
            this.smart_tariff_tracking = smart_tariff_tracking;
        }
    }

}
