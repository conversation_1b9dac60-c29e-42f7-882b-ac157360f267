package com.dinsafer.module_bmt.http;

import androidx.annotation.Nullable;

import com.dinsafer.module_bmt.http.bean.BmtBatteryCountResponse;
import com.dinsafer.module_bmt.http.bean.BmtListResponse;

import java.util.ArrayList;

import retrofit2.Callback;
import retrofit2.Response;

public class BmtRepo {

    public BmtRepo() {
    }

    public void fetchBmt(String homeID, int pageSize, long addTime, boolean orderDesc, Callback<BmtListResponse> callback) {
        BmtApi.getInstance().fetchBmt(homeID, pageSize, addTime, orderDesc).enqueue(callback);
    }

    @Nullable
    public Response<BmtListResponse> fetchBmtSync(String homeID, int pageSize, long addTime, boolean orderDesc) {
        Response<BmtListResponse> response = null;
        try {
            response = BmtApi.getInstance().fetchBmt(homeID, pageSize, addTime, orderDesc).execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return response;
    }

    public void getBmtBatteryCount(String homeID, ArrayList<String> models, Callback<BmtBatteryCountResponse> callback) {
        BmtApi.getInstance().getBmtBatteryCount(homeID,models).enqueue(callback);
    }
}
