package com.dinsafer.module_bmt.http;

import android.text.TextUtils;

import com.dinsafer.dincore.http.Api;
import com.dinsafer.dincore.http.BmtStatsApi;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.dincore.utils.QiNiuUploadManager;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.bean.BmtConst;
import com.dinsafer.module_bmt.http.bean.BmtBSensorResponse;
import com.dinsafer.module_bmt.http.bean.BmtBatteryCountResponse;
import com.dinsafer.module_bmt.http.bean.BmtChargingDischargingPlansResponse;
import com.dinsafer.module_bmt.http.bean.BmtChargingDischargingPlansV2Response;
import com.dinsafer.module_bmt.http.bean.BmtE2ELoginResponse;
import com.dinsafer.module_bmt.http.bean.BmtListResponse;
import com.dinsafer.module_bmt.http.bean.BmtLocationResponse;
import com.dinsafer.module_bmt.http.bean.BmtStatEcoResponse;
import com.dinsafer.module_bmt.http.bean.BmtStatsResponse;
import com.dinsafer.module_bmt.http.bean.ElecPriceInfoResponse;
import com.dinsafer.module_bmt.http.bean.GetCityListResponse;
import com.dinsafer.module_bmt.http.bean.GetCountryListResponse;
import com.dinsafer.module_bmt.http.bean.GetDefaultPricePercentResponse;
import com.dinsafer.module_bmt.http.bean.GetFeatureResponse;
import com.dinsafer.module_bmt.http.bean.GetFusePowerCapsResponse;
import com.dinsafer.module_bmt.http.bean.GetIsDualPowerOpenResponse;
import com.dinsafer.module_bmt.http.bean.GetRegionResponse;
import com.dinsafer.module_bmt.http.bean.SearchBmtParams;
import com.dinsafer.module_bmt.http.bean.UpdateRegionResponse;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;

import retrofit2.Call;

public class BmtApi {
    private final IBmtApi services;
    private final IBmtApi servicesStats;
    public static final String GM = "gm";
    public static final String GMTIME = "gmtime";

    private BmtApi() {
        services = Api.getApi().getRetrofit().create(IBmtApi.class);
        servicesStats = BmtStatsApi.getApi().getRetrofit().create(IBmtApi.class);
    }


    private static class Holder {
        private static final BmtApi INSTANT = new BmtApi();
    }

    public static BmtApi getInstance() {
        return Holder.INSTANT;
    }

    public Call<BmtListResponse> fetchBmt(String homeID, int pageSize, long addTime, boolean orderDesc) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("models", new JSONArray(BmtConst.ALL_PROVIDER));
            jsonObject.put("page_size", pageSize);
            jsonObject.put("addtime", addTime);
            jsonObject.put("order", orderDesc ? "desc" : "asc");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.fetchBmtList(Api.getApi()
                .getUrl(BmtUrls.URL_FETCH_BMT_LIST), map);
    }

    public Call<BmtE2ELoginResponse> login(String homeID, String pid, String provider) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("models", new JSONArray().put(provider));
            jsonObject.put("page_size", 0);
            if (UserManager.getInstance().isThirdPartyUser()) {
                jsonObject.put("user_id", UserManager.getInstance().getUser().getThirdPartyUID());
            }
            JSONObject id = new JSONObject();
            id.put("id", pid);
            id.put("model", provider);
            jsonObject.put("ids", new JSONArray().put(id));
            jsonObject.put("addtime", 0);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.e2elogin(Api.getApi()
                .getUrl(BmtUrls.URL_BMT_E2E_LOGIN), map);
    }

    public Call<StringResponseEntry> setName(String homeID, String pid, String provider, String name) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("id", pid);
            jsonObject.put("model", provider);
            jsonObject.put("name", name);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.stringCall(Api.getApi()
                .getUrl(BmtUrls.URL_BMT_SETNAME), map);
    }


    public Call<StringResponseEntry> deleteBmt(String homeID, String pid, String provider) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("model", provider);
            jsonObject.put("id", pid);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.stringCall(Api.getApi()
                .getUrl(BmtUrls.URL_BMT_DELETE), map);
    }

    /**
     * 查询指定 pid 的自研 bmt
     */
    public Call<BmtListResponse> searchBmt(String homeID, @NonNull List<SearchBmtParams> bmts) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            final JSONArray pidList = new JSONArray();
            if (null != bmts && bmts.size() > 0) {
                for (SearchBmtParams bmt : bmts) {
                    final String pid = bmt.getPid();
                    final String provider = bmt.getProvider();
                    if (!TextUtils.isEmpty(pid) && !TextUtils.isEmpty(provider)) {
                        final JSONObject obj = new JSONObject();
                        obj.put("id", pid);
                        obj.put("model", provider);
                        pidList.put(obj);
                    }
                }
            }
            jsonObject.put("home_id", homeID);
            jsonObject.put("ids", pidList);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.searchBmt(Api.getApi()
                .getUrl(BmtUrls.URL_SEARCH_BMT), map);
    }

    /**
     * 获取制定型号的电池数量
     *
     * @param homeID
     * @param models
     * @return
     */
    public Call<BmtBatteryCountResponse> getBmtBatteryCount(String homeID, ArrayList<String> models) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("model", new JSONArray(models));
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getBmtBatteryCount(Api.getApi()
                .getUrl(BmtUrls.URL_GET_BMT_BATTERY_COUNT), map);
    }

    /**
     * 获取城市列表
     *
     * @param
     * @return
     */
    public Call<GetCityListResponse> getCityList() {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getCityList(Api.getApi()
                .getUrl(BmtUrls.URL_GET_CITY_LIST), map);
    }

    public Call<GetCountryListResponse> getCountryList() {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getCountyList(Api.getApi()
                .getUrl(BmtUrls.URL_GET_COUNTY_LIST), map);
    }

    /**
     * 保存国家/城市
     *
     * @param
     * @return
     */
    public Call<UpdateRegionResponse> updateRegion(String home_id, String id, String model, String country_code, String country_name, String city_name, String timezone) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
            jsonObject.put("id", id);
            jsonObject.put("model", model);
            jsonObject.put("country_code", country_code);
            jsonObject.put("region", country_name);
            jsonObject.put("timezone", timezone);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.updateRegion(Api.getApi()
                .getUrl(BmtUrls.URL_UPDATE_REGION), map);
    }


    /**
     * 获取区域
     *
     * @param
     * @return
     */
    public Call<GetRegionResponse> getRegion(String home_id, String id, String model) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
            jsonObject.put("id", id);
            jsonObject.put("model", model);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getRegion(Api.getApi()
                .getUrl(BmtUrls.URL_GET_REGION), map);
    }

    /**
     * 获取设备负载数据
     *
     * @param home_id
     * @param id
     * @param model
     * @param offset  // int - 天/周/月/12年 的偏移量（0 表示当天的数据，-n 表示前 n 天/周/月/12年 的数据）
     * @return
     */
    public Call<BmtStatsResponse> getBmtStats(String home_id, String id, String model, int offset, String cmd, String interval) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
            jsonObject.put("id", id);
            jsonObject.put("model", model);
            jsonObject.put("offset", offset);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        String url = getBmtStatsUrl(cmd, interval);

        return servicesStats.getBmtStats(BmtStatsApi.getApi()
                .getUrl(url), map);
    }

    public Call<BmtStatsResponse> getBmtGridStats(String home_id, String id, String model, int offset, String cmd, String interval,int queryInterval,boolean getReal) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
            jsonObject.put("id", id);
            jsonObject.put("model", model);
            jsonObject.put("offset", offset);
            jsonObject.put("get_real", getReal);
            jsonObject.put("query_interval", queryInterval);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        String url = getBmtStatsUrl(cmd, interval);

        return servicesStats.getBmtStats(BmtStatsApi.getApi()
                .getUrl(url), map);
    }

    private String getBmtStatsUrl(String cmd, String interval) {
        if (!TextUtils.isEmpty(cmd) && cmd.equals(BmtCmd.GET_STATS_BATTERY_POWERLEVEL)) {
            return BmtUrls.URL_BMT_STATS_BATTERY_POWER_LEVEL;
        }
        String url = "";
        switch (cmd) {
            case BmtCmd.GET_STATS_LOADUSAGE:
                url = BmtUrls.URL_BMT_STATS_USAGE;
                break;
            case BmtCmd.GET_STATS_BATTERY:
                url = BmtUrls.URL_BMT_STATS_BATTERY;
                break;
            case BmtCmd.GET_STATS_GRID:
                url = BmtUrls.URL_BMT_STATS_GRID;
                break;
            case BmtCmd.GET_STATS_MPPT:
                url = BmtUrls.URL_BMT_STATS_MPPT;
                break;

            case BmtCmd.GET_STATS_REVENUE:
                url = BmtUrls.URL_BMT_STATS_REVENUE;
                break;

            case BmtCmd.GET_STATS_LOADUSAGE_V2:
                url = BmtUrls.URL_BMT_STATS_USAGE_V2;
                break;

            case BmtCmd.GET_STATS_BATTERY_V2:
                url = BmtUrls.URL_BMT_STATS_BATTERY_V2;
                break;

            case BmtCmd.GET_STATS_MPPT_V2:
                url = BmtUrls.URL_BMT_STATS_MPPT_V2;
                break;

            case BmtCmd.GET_STATS_REVENUE_V2:
                url = BmtUrls.URL_BMT_STATS_REVENUE_V2;
                break;
        }
        if (!TextUtils.isEmpty(interval)) {
            url = url + interval + "/";
        }
        return url;
    }

    /**
     * 获取环保评级
     *
     * @param home_id
     * @param id
     * @param model
     * @param offset
     * @param interval
     * @return
     */
    public Call<BmtStatEcoResponse> getBmtStatEco(String home_id, String id, String model, int offset, String interval, int version) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
            jsonObject.put("id", id);
            jsonObject.put("model", model);
            jsonObject.put("offset", offset);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        String url = BmtUrls.URL_BMT_STATS_ECO;
        if (version == 2) {
            url = BmtUrls.URL_BMT_STATS_ECO_V2;
        }
        return servicesStats.getBmtEco(BmtStatsApi.getApi()
                .getUrl(url + interval + "/"), map);
    }

    /**
     * 获取设备 b sensor 状态
     *
     * @param home_id
     * @param id
     * @param model
     * @return
     */
    public Call<BmtBSensorResponse> getBmtBSensorStatus(String home_id, String id, String model) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
            jsonObject.put("id", id);
            jsonObject.put("model", model);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return servicesStats.getBmtBSensorStatus(BmtStatsApi.getApi()
                .getUrl(BmtUrls.URL_BMT_BSENSOR), map);
    }

    /**
     * 绑定逆变器 id
     *
     * @param home_id
     * @param bmt_id
     * @param model
     * @param inverter_id
     * @param mcu_id
     * @return
     */
    public Call<StringResponseEntry> bindInverter(String home_id, String bmt_id, String model, String inverter_id, String mcu_id) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("bmt_id", bmt_id);
            jsonObject.put("home_id", home_id);
            jsonObject.put("inverter_id", inverter_id);
            jsonObject.put("mcu_id", mcu_id);
            jsonObject.put("model", model);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.bindInverter(Api.getApi()
                .getUrl(BmtUrls.URL_BIND_INVERTER), map);
    }

    public Call<ElecPriceInfoResponse> getElecPriceInfo(String home_id, String id, String model, int offset) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
            jsonObject.put("bmt_id", id);
            jsonObject.put("model", model);
            jsonObject.put("offset", offset);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getElecPriceInfo(Api.getApi()
                .getUrl(BmtUrls.URL_ELEC_PRICE_INFO), map);
    }

    /**
     * 获取保险丝功率上限列表
     *
     * @param
     * @return
     */
    public Call<GetFusePowerCapsResponse> getFusePowerCaps() {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getFusePowerCaps(Api.getApi()
                .getUrl(BmtUrls.URL_GET_FUSE_POWER_CAPS_V2), map);
    }

    /**
     * 获取设备功能开关
     *
     * @param home_id
     * @param id
     * @param model
     * @return
     */
    public Call<GetFeatureResponse> getFeature(String home_id, String id, String model) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
            jsonObject.put("id", id);
            jsonObject.put("model", model);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getFeature(Api.getApi()
                .getUrl(BmtUrls.URL_GET_FEATURE), map);
    }

    /**
     * 获取指定家庭是否曾经接入第三方PV
     *
     * @param
     * @return
     */
    public Call<GetIsDualPowerOpenResponse> getIsDualPowerOpen(String home_id, String id) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
            jsonObject.put("bmt_id", id);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getIsDualPowerOpen(Api.getApi()
                .getUrl(BmtUrls.URL_IS_DUAL_POWER_OPEN), map);
    }

    public Call<BmtChargingDischargingPlansResponse> getChargingDischargingPlans(String home_id, String id,
                                                                                 String model, int offset) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
            jsonObject.put("id", id);
            jsonObject.put("model", model);
            jsonObject.put("offset", offset);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return servicesStats.getChargingDischargingPlans(BmtStatsApi.getApi()
                .getUrl(BmtUrls.URL_BMT_STATS_GET_CHARGING_DISCHARGING_PLANS), map);
    }

    public Call<BmtChargingDischargingPlansV2Response> getChargingDischargingPlansV2(String home_id, String id, String model) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
            jsonObject.put("id", id);
            jsonObject.put("model", model);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return servicesStats.getChargingDischargingPlansV2(BmtStatsApi.getApi()
                .getUrl(BmtUrls.URL_BMT_STATS_GET_CHARGING_DISCHARGING_PLANS_V2), map);
    }

    public Call<GetDefaultPricePercentResponse> getDefaultPricePercent(String home_id, String id,
                                                                       String model) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
            jsonObject.put("id", id);
            jsonObject.put("model", model);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getDefaultPricePercent(BmtStatsApi.getApi()
                .getUrl(BmtUrls.URL_BMT_DEFAULT_PRICE_PERCENT), map);
    }

    public Call<BmtLocationResponse> getBmtLocation(String home_id, String id,
                                                    String model) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
            jsonObject.put("bmt_id", id);
            jsonObject.put("model", model);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getBmtLocation(BmtStatsApi.getApi()
                .getUrl(BmtUrls.URL_BMT_GET_BMT_LOCATION), map);
    }
}
