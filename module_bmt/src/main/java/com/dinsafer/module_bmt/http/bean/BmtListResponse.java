package com.dinsafer.module_bmt.http.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;
import java.util.List;

@Keep
public class BmtListResponse extends BaseHttpEntry implements Serializable {


    /**
     * Cmd :
     * Result : [{"name":"","pid":"","group_id":"","end_id":"","addtime":1621580076757000000}]
     */

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean implements Serializable{
            private List<BmtBeen> bmts;

        public List<BmtBeen> getBmts() {
            return bmts;
        }

        public void setBmts(List<BmtBeen> bmts) {
            this.bmts = bmts;
        }
    }

    @Keep
    public static class BmtBeen implements Serializable {
        /**
         * name :
         * pid :
         * group_id :
         * end_id :
         * addtime : 1621580076757000000
         */

        private String id;
        private String name;
        private String group_id;
        private String end_id;
        private String addr;
        private Long addtime;
        private String model;
        private String country_code;
        private String delivery_area;


        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPid() {
            return id;
        }

        public void setPid(String pid) {
            this.id = pid;
        }

        public String getGroup_id() {
            return group_id;
        }

        public void setGroup_id(String group_id) {
            this.group_id = group_id;
        }

        public String getEnd_id() {
            return end_id;
        }

        public void setEnd_id(String end_id) {
            this.end_id = end_id;
        }

        public Long getAddtime() {
            return addtime;
        }

        public void setAddtime(Long addtime) {
            this.addtime = addtime;
        }

        public String getAddr() {
            return addr;
        }

        public void setAddr(String addr) {
            this.addr = addr;
        }

        public String getProvider() {
            return model;
        }

        public void setProvider(String provider) {
            this.model = provider;
        }

        public String getCountry_code() {
            return country_code;
        }

        public void setCountry_code(String country_code) {
            this.country_code = country_code;
        }

        public String getDelivery_area() {
            return delivery_area;
        }

        public void setDelivery_area(String delivery_area) {
            this.delivery_area = delivery_area;
        }

        public boolean hasCountryCode() {
            return country_code != null;
        }
        public boolean hasDeliveryArea() {
            return delivery_area != null;
        }
    }
}
