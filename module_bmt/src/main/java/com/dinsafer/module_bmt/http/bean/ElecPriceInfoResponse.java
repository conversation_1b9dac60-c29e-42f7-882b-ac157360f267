package com.dinsafer.module_bmt.http.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;
import java.util.List;

@Keep
public class ElecPriceInfoResponse extends BaseHttpEntry implements Serializable {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean implements Serializable{
        private List<List<Float>> data;
        private Long start_time;
        private String timezone;
        private String unit;
        private String unit_app;

        public List<List<Float>> getData() {
            return data;
        }

        public void setData(List<List<Float>> data) {
            this.data = data;
        }

        public Long getStart_time() {
            return start_time;
        }

        public void setStart_time(Long start_time) {
            this.start_time = start_time;
        }

        public String getTimezone() {
            return timezone;
        }

        public void setTimezone(String timezone) {
            this.timezone = timezone;
        }

        public String getUnit() {
            return unit;
        }

        public void setUnit(String unit) {
            this.unit = unit;
        }

        public String getUnit_app() {
            return unit_app;
        }

        public void setUnit_app(String unit_app) {
            this.unit_app = unit_app;
        }
    }
}
