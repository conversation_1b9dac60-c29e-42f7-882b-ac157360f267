package com.dinsafer.module_bmt.http.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;

@Keep
public class BmtBSensorResponse extends BaseHttpEntry implements Serializable {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        Result = result;
    }

    @Keep
    public static class ResultBean {

        private Boolean b_sensor_installed;
        private Long gmtime;

        public Boolean getB_sensor_installed() {
            return b_sensor_installed;
        }

        public void setB_sensor_installed(Boolean b_sensor_installed) {
            this.b_sensor_installed = b_sensor_installed;
        }

        public Long getGmtime() {
            return gmtime;
        }

        public void setGmtime(Long gmtime) {
            this.gmtime = gmtime;
        }
    }
}
