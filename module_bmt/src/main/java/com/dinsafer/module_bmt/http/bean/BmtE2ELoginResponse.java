package com.dinsafer.module_bmt.http.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;
import java.util.List;

@Keep
public class BmtE2ELoginResponse extends BaseHttpEntry implements Serializable {


    /**
     * Cmd :
     * Result : [{"pid":"","group_id":"","end_id":"","end_secret":"","chat_secret":"","addtime":1621580076757000000}]
     */

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        private List<E2ELoginBean> e2es;

        public List<E2ELoginBean> getE2es() {
            return e2es;
        }

        public void setE2es(List<E2ELoginBean> e2es) {
            this.e2es = e2es;
        }
    }

    @Keep
    public static class E2ELoginBean implements Serializable {
        /**
         * pid :
         * group_id :
         * end_id :
         * end_secret :
         * chat_secret :
         * addtime : 1621580076757000000
         */

        private String id;
        private String group_id;
        private String end_id;
        private String end_secret;
        private String chat_secret;
        private String host;
        private String util_host;
        private Long addtime;

        public String getPid() {
            return id;
        }

        public void setPid(String pid) {
            this.id = pid;
        }

        public String getGroup_id() {
            return group_id;
        }

        public void setGroup_id(String group_id) {
            this.group_id = group_id;
        }

        public String getEnd_id() {
            return end_id;
        }

        public void setEnd_id(String end_id) {
            this.end_id = end_id;
        }

        public String getEnd_secret() {
            return end_secret;
        }

        public void setEnd_secret(String end_secret) {
            this.end_secret = end_secret;
        }

        public String getChat_secret() {
            return chat_secret;
        }

        public void setChat_secret(String chat_secret) {
            this.chat_secret = chat_secret;
        }

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public String getUtil_host() {
            return util_host;
        }

        public void setUtil_host(String util_host) {
            this.util_host = util_host;
        }

        public Long getAddtime() {
            return addtime;
        }

        public void setAddtime(Long addtime) {
            this.addtime = addtime;
        }
    }
}
