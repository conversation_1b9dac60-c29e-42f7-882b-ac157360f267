package com.dinsafer.module_bmt.http.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.util.List;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/11/23 16:38
 */
@Keep
public class SearchBmtResponse extends BaseHttpEntry {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        private int count;
        private long gmtime;
        private List<ListBean> list;

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        public List<ListBean> getList() {
            return list;
        }

        public void setList(List<ListBean> list) {
            this.list = list;
        }

        @Keep
        public static class ListBean {
            private long addtime;
            private String end_id;
            private String group_id;
            private String name;
            private String pid;
            private String provider;

            public long getAddtime() {
                return addtime;
            }

            public void setAddtime(long addtime) {
                this.addtime = addtime;
            }

            public String getEnd_id() {
                return end_id;
            }

            public void setEnd_id(String end_id) {
                this.end_id = end_id;
            }

            public String getGroup_id() {
                return group_id;
            }

            public void setGroup_id(String group_id) {
                this.group_id = group_id;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getPid() {
                return pid;
            }

            public void setPid(String pid) {
                this.pid = pid;
            }

            public String getProvider() {
                return provider;
            }

            public void setProvider(String provider) {
                this.provider = provider;
            }
        }
    }
}
