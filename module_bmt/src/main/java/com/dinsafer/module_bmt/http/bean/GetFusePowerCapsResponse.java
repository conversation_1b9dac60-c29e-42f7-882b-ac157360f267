package com.dinsafer.module_bmt.http.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;
import java.util.List;

@Keep
public class GetFusePowerCapsResponse extends BaseHttpEntry implements Serializable {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean implements Serializable {

        private int count;
        private List<FusePowerCapBean> higher;
        private List<FusePowerCapBean> lower;
        private List<FusePowerCapBean> medium;
        private long gmtime;

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }

        public List<FusePowerCapBean> getHigher() {
            return higher;
        }

        public void setHigher(List<FusePowerCapBean> higher) {
            this.higher = higher;
        }

        public List<FusePowerCapBean> getLower() {
            return lower;
        }

        public void setLower(List<FusePowerCapBean> lower) {
            this.lower = lower;
        }

        public List<FusePowerCapBean> getMedium() {
            return medium;
        }

        public void setMedium(List<FusePowerCapBean> medium) {
            this.medium = medium;
        }

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        @Keep
        public static class FusePowerCapBean {
            private Double power_cap;
            private String spec;

            public Double getPower_cap() {
                return power_cap;
            }

            public void setPower_cap(Double power_cap) {
                this.power_cap = power_cap;
            }

            public String getSpec() {
                return spec;
            }

            public void setSpec(String spec) {
                this.spec = spec;
            }
        }
    }
}
