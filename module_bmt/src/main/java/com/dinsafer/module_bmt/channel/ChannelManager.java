package com.dinsafer.module_bmt.channel;

import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.msctlib.kcp.IKcpCallBack;
import com.dinsafer.dssupport.msctlib.kcp.IKcpCreateCallBack;
import com.dinsafer.dssupport.msctlib.netty.ISenderCallBack;
import com.dinsafer.module_bmt.bean.BmtDevice;

import java.util.HashMap;
import java.util.Map;

public class ChannelManager {

    private static final String TAG = ChannelManager.class.getSimpleName();

    BmtDevice bmtDevice;

    private Map<Integer, Channel> channelList = new HashMap<>();

    private IChannelCallBack channelCallBack;

    public ChannelManager(BmtDevice bmtDevice,
                          String homeID,
                          IKcpCallBack kcpCallBack,
                          ISenderCallBack msctCallBack) {
        this.bmtDevice = bmtDevice;
//        Channel lanChannel = new LanChannel(this, this.bmtDevice, kcpCallBack);
//
//        lanChannel.setChannelCallBack(new IChannelCallBack() {
//            @Override
//            public void onConnect() {
//                checkConnect();
//                MsctLog.i(TAG, "bmtDevice:" + bmtDevice.getId() + " lan channel connect");
//            }
//
//            @Override
//            public void onConnecting() {
//                checkConnectting();
//                MsctLog.i(TAG, "bmtDevice:" + bmtDevice.getId() + " lan channel connectting");
//            }
//
//            @Override
//            public void onDisconnect(int code, String msg) {
//                checkDisconnect(code, msg);
//                MsctLog.i(TAG, "bmtDevice:" + bmtDevice.getId() + " lan channel disconnect:" +
//                        code + " msg:" + msg);
//            }
//        });
//        channelList.put(Channel.LAN, lanChannel);

        Channel proxyChannel = new ProxyChannel(this, this.bmtDevice, homeID, kcpCallBack, msctCallBack);
        proxyChannel.setChannelCallBack(new IChannelCallBack() {
            @Override
            public void onConnect() {
                checkConnect();
//                代理通了，那么尝试连局域网
//                lanChannel.connect();
                MsctLog.i(TAG, "bmt:" + bmtDevice.getId() + " proxy channel connect");
            }

            @Override
            public void onConnecting() {
                checkConnectting();
                MsctLog.i(TAG, "bmt:" + bmtDevice.getId() + " proxy channel connectting");
            }

            @Override
            public void onDisconnect(int code, String msg) {
                checkDisconnect(code, msg);
                MsctLog.i(TAG, "bmt:" + bmtDevice.getId() + " proxy channel disconnect:" +
                        code + " msg:" + msg);
            }
        });
        channelList.put(Channel.PROXY, proxyChannel);
//        Channel p2pChannel = new P2pChannel(this, this.bmtDevice, kcpCallBack);
//
//        p2pChannel.setChannelCallBack(new IChannelCallBack() {
//            @Override
//            public void onConnect() {
//                checkConnect();
//
//                MsctLog.i(TAG, "bmtDevice:" + bmtDevice.getId() + " p2p channel connect");
//            }
//
//            @Override
//            public void onConnecting() {
//                checkConnectting();
//                MsctLog.i(TAG, "bmtDevice:" + bmtDevice.getId() + " p2p channel connectting");
//            }
//
//            @Override
//            public void onDisconnect(int code, String msg) {
//                checkDisconnect(code, msg);
//                MsctLog.i(TAG, "bmtDevice:" + bmtDevice.getId() + " p2p channel disconnect:" +
//                        code + " msg:" + msg);
//            }
//        });
//        channelList.put(Channel.P2P, p2pChannel);


    }

    private void checkConnect() {
        if (channelCallBack == null) {
            return;
        }

        if (isConnect()) {
            channelCallBack.onConnect();
        }
    }

    private void checkConnectting() {
        if (channelCallBack == null) {
            return;
        }

        if (isConnectting()) {
            channelCallBack.onConnecting();
        }
    }

    private void checkDisconnect(int code, String msg) {
        if (channelCallBack == null) {
            return;
        }

        if (isDisconnect()) {
            channelCallBack.onDisconnect(code, msg);
        }
    }


    public boolean isConnect() {
//        TODO 代理模式不同下，lan通了，最上层也是离线，需要处理
        boolean isConnect = false;
        for (Channel value : channelList.values()) {
            if (value.isConnect()) {
                isConnect = true;
                break;
            }
        }

        return isConnect;
    }

    private boolean isConnectting() {
        boolean isConnectting = false;
        for (Channel value : channelList.values()) {
            if (value.isConnect()) {
                return isConnectting;
            }
        }

        for (Channel value : channelList.values()) {
            if (value.isConnectting()) {
                isConnectting = true;
                return isConnectting;
            }
        }

        return isConnectting;
    }

    private boolean isDisconnect() {
        boolean isHasNoDisconnect = false;
        for (Channel value : channelList.values()) {
            if (!value.isDisconnect()) {
                isHasNoDisconnect = true;
                break;
            }
        }

        return !isHasNoDisconnect;
    }

    public IChannelCallBack getChannelCallBack() {
        return channelCallBack;
    }

    public void setChannelCallBack(IChannelCallBack channelCallBack) {
        this.channelCallBack = channelCallBack;
    }

    public Channel getChannel(int type) {
        return channelList.get(type);
    }

    public Channel getChannel() {
        if (getChannel(Channel.LAN) != null && getChannel(Channel.LAN).isConnect()) {
            MsctLog.i(TAG, "get lan channel");
            return getChannel(Channel.LAN);
        }
        if (getChannel(Channel.P2P) != null && getChannel(Channel.P2P).isConnect()) {
            MsctLog.i(TAG, "get P2P channel");
            return getChannel(Channel.P2P);
        }
        if (getChannel(Channel.PROXY) != null && getChannel(Channel.PROXY).isConnect()) {
            MsctLog.i(TAG, "get proxy channel");
            return getChannel(Channel.PROXY);
        }
        MsctLog.i(TAG, "get null channel");
        return null;
    }

    public void createKcp(int type, int sessionID, IKcpCreateCallBack kcpCreateCallBack) {
        Channel channel = getChannel();
        if (channel == null) {
            if (kcpCreateCallBack != null) {
                kcpCreateCallBack.onError(ErrorCode.DEFAULT, "bmt is no connect");
            }
            return;
        }
        channel.createKcp(type, sessionID, kcpCreateCallBack);
    }

    public void removeKcp(int conv) {
        for (Channel value : channelList.values()) {
            value.removeKcp(conv);
        }
    }

    public void disconnect() {
        for (Channel value : channelList.values()) {
            value.disconnect();
        }
    }

    public void destory() {
        for (Channel value : channelList.values()) {
            value.destory();
        }
    }
}
