package com.dinsafer.module_bmt;

import android.app.Application;
import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.common.CommonCmdEvent;
import com.dinsafer.dincore.common.DeivceChangeEvent;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IService;
import com.dinsafer.dincore.db.cache.DeviceCacheHelper;
import com.dinsafer.dincore.user.bean.DinUser;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dincore.utils.MapUtils;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.msctlib.netty.IMultipleSender;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_bmt.add.BmtHP5000Binder;
import com.dinsafer.module_bmt.add.BmtHP5000NetworkManager;
import com.dinsafer.module_bmt.add.BmtHP5001Binder;
import com.dinsafer.module_bmt.add.BmtHP5001NetworkManager;
import com.dinsafer.module_bmt.add.BmtPowerCore20Binder;
import com.dinsafer.module_bmt.add.BmtPowerCore20NetworkManager;
import com.dinsafer.module_bmt.add.BmtPowerCore30Binder;
import com.dinsafer.module_bmt.add.BmtPowerCore30NetworkManager;
import com.dinsafer.module_bmt.add.BmtPowerPulseBinder;
import com.dinsafer.module_bmt.add.BmtPowerPulseNetworkManager;
import com.dinsafer.module_bmt.add.BmtPowerStoreBinder;
import com.dinsafer.module_bmt.add.BmtPowerStoreNetworkManager;
import com.dinsafer.module_bmt.bean.BmtCacheInfo;
import com.dinsafer.module_bmt.bean.BmtConst;
import com.dinsafer.module_bmt.bean.BmtDevice;
import com.dinsafer.module_bmt.bean.HP5000;
import com.dinsafer.module_bmt.bean.HP5001;
import com.dinsafer.module_bmt.bean.PowerCore20;
import com.dinsafer.module_bmt.bean.PowerCore30;
import com.dinsafer.module_bmt.bean.PowerPulse;
import com.dinsafer.module_bmt.bean.PowerStore;
import com.dinsafer.module_bmt.event.BmtChipsStatusEvent;
import com.dinsafer.module_bmt.event.BmtCountryCodeUpdateEvent;
import com.dinsafer.module_bmt.event.BmtDeviceNameUpdateEvent;
import com.dinsafer.module_bmt.event.BmtExceptionEvent;
import com.dinsafer.module_bmt.event.BmtThirdPartyPVOnEvent;
import com.dinsafer.module_bmt.event.BmtVertStateEvent;
import com.dinsafer.module_bmt.event.EmergencySmartReserveEvent;
import com.dinsafer.module_bmt.event.GridStatusEvent;
import com.dinsafer.module_bmt.event.IotVersionEvent;
import com.dinsafer.module_bmt.event.PvSupplyCustomizationSupportedEvent;
import com.dinsafer.module_bmt.event.RegulateFrequencyStateEvent;
import com.dinsafer.module_bmt.http.BmtRepo;
import com.dinsafer.module_bmt.http.bean.BmtListResponse;
import com.dinsafer.module_bmt.utils.SearchBmtHelper;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.netty.util.internal.StringUtil;
import retrofit2.Response;

public class BmtService implements IService {
    private static final String BINDER_KEY_BMT_HP5000 = "bmt_hp5000_binder";
    private static final String BINDER_KEY_BMT_HP5000_NETWORK_MANAGER = "bmt_hp5000_network_binder";
    private static final String BINDER_KEY_BMT_HP5001 = "bmt_hp5001_binder";
    private static final String BINDER_KEY_BMT_HP5001_NETWORK_MANAGER = "bmt_hp5001_network_binder";
    private static final String BINDER_KEY_BMT_PC1BAK15HS10 = "bmt_pc1bak1hs10_binder";
    private static final String BINDER_KEY_BMT_PC1BAK15HS10_NETWORK_MANAGER = "bmt_pc1bak15hs10_network_binder";
    private static final String BINDER_KEY_BMT_PS1BAK10HS10 = "bmt_ps1bak10hs10_binder";
    private static final String BINDER_KEY_BMT_PS1BAK10HS10_NETWORK_MANAGER = "bmt_ps1bak10hs10_network_binder";
    private static final String BINDER_KEY_BMT_VB1BAK5HS10 = "bmt_vb1bak5hs10_binder";
    private static final String BINDER_KEY_BMT_VB1BAK5HS10_NETWORK_MANAGER = "bmt_vb1bak5hs10_network_binder";

    private static final String BINDER_KEY_BMT_PC3 = "bmt_pc3_binder";
    private static final String BINDER_KEY_BMT_PC3_NETWORK_MANAGER = "bmt_pc3_network_binder";
    private static final int PAGE_SIZE_DEFAULT = 30; // 加载bmt列表一页的数量
    private static final String CACHE_IDENTIFY = "BmtService"; // 缓存key后缀
    private static final String TAG = BmtService.class.getSimpleName();

    //    list的操作要加锁，否则在遍历的时候，去remove，就会奔溃
    private final List<Device> mBmtList1 = new ArrayList<>();
    private String currentHomeID;
    private IMultipleSender multipleSender;

    private Map<String, Object> configMap;
    private final static byte[] fetchDeviceLock = new byte[0];
    private BmtRepo bmtRepo = new BmtRepo();
    //    支持的配件类型
    private List<String> supportDeivceType = new ArrayList<>();
    private final byte[] listLock = new byte[0];
    private final BmtCacheInfo cacheInfo = new BmtCacheInfo();
    private int e2ePort;
    private String e2eDomain;

    @Keep
    public BmtService(Application application) {
    }

    @Override
    @Keep
    public void load() {
        EventBus.getDefault().register(this);
        supportDeivceType.addAll(BmtConst.ALL_PROVIDER);
    }

    @Override
    @Keep
    public void unLoad() {
        EventBus.getDefault().unregister(this);
    }

    @Override
    @Keep
    public void config(Map<String, Object> configArg) {
        configMap = configArg;
        final String lastHomeId = currentHomeID;
        currentHomeID = (String) MapUtils.get(configArg, "homeID", "");
        SearchBmtHelper.get().setCurrentHomeId(currentHomeID);
        if (configArg.containsKey("multipleSender")) {
            Object temp = configArg.get("multipleSender");
            if (temp != null) {
                multipleSender = (IMultipleSender) temp;
            }
        }

        if (!TextUtils.isEmpty(lastHomeId) && !lastHomeId.equals(currentHomeID)) {
            // 切换了家庭
            cacheInfo.updateFrom(null);

            for (Device device : mBmtList1) {
                device.destory();
            }
            mBmtList1.clear();
        } else {
            // 没有切换家庭
            synchronized (listLock) {
                if (null != multipleSender && mBmtList1.size() > 0) {
                    for (Device device : mBmtList1) {
                        BmtDevice bmtDevice = (BmtDevice) device;
                        bmtDevice.updateSender(multipleSender);
                    }
                }
            }
        }
    }

    /**
     * 循环获取到最新时间的device
     */
    @Override
    @Keep
    public List<Device> fetchDevices() {
        synchronized (fetchDeviceLock) {
            createDeviceFromCache();
            markDevicesFromCache();
            requestDeviceCirculate(cacheInfo.getNewestAddTime());
            DDLog.i(TAG, "fetch bmt Devices finish");
        }
        return new ArrayList<>(mBmtList1);
    }

    private void requestDeviceCirculate(final long startTimeStamp) {
        synchronized (fetchDeviceLock) {
            final String loadingHomeId = currentHomeID;
            long addTime = startTimeStamp;
            DDLog.i(TAG, "get bmt device start...,timeStamp=" + addTime);
            // !!!! 循环获取配件
            final List<BmtListResponse.BmtBeen> netDeviceList = new ArrayList<>();
            while (true) {
                if (!TextUtils.isEmpty(loadingHomeId) && !loadingHomeId.equals(currentHomeID)) {
                    break;
                }

                final List<BmtListResponse.BmtBeen> result = requestIpcOnPageSync(addTime, false);
                if (null == result || result.size() == 0) {
                    break;
                }

                netDeviceList.addAll(result);
                for (BmtListResponse.BmtBeen bean : result) {
                    if (null != bean.getAddtime() && bean.getAddtime() > addTime) {
                        addTime = bean.getAddtime();
                    }
                }
                if (result.size() < PAGE_SIZE_DEFAULT) {
                    break;
                }
            }
            // 切换了房间，返回空数据
            if (!TextUtils.isEmpty(loadingHomeId) && !loadingHomeId.equals(currentHomeID)) {
                return;
            }
            if (netDeviceList.size() > 0) {
                final List<Device> netDevices = createDeviceFromNet(netDeviceList);
                if (netDevices.size() > 0) {
                    saveDeviceCache();
                }
            }
        }
    }

    @Nullable
    private List<BmtListResponse.BmtBeen> requestIpcOnPageSync(final long addTime, final boolean orderDesc) {
        DDLog.d(TAG, "requestIpcOnPageSync, addTime: " + (addTime + 1));
        final Response<BmtListResponse> response = bmtRepo.fetchBmtSync(currentHomeID, PAGE_SIZE_DEFAULT, addTime + 1, orderDesc);
        if (null == response || !response.isSuccessful()) {
            return null;
        }
        final BmtListResponse body = response.body();
        if (null == body) {
            return null;
        }

        if (response.body().getResult() == null) {
            return null;
        }

        final List<BmtListResponse.BmtBeen> result = body.getResult().getBmts();
        if (null == result || result.size() == 0) {
            return null;
        }

        return result;
    }

    @Override
    @Keep
    public Device getDevice(String id) {
        // 只有id无法确定同id不同provider的设备
        return null;
    }

    @Keep
    public Device getDevice(String id, String sub) {
        for (Device device : mBmtList1) {
            if (device.getId().equals(id) && device.getSubCategory().equals(sub)) {
                return device;
            }
        }
        return null;
    }

    @Override
    @Keep
    public List<Device> getDeviceByType(String sub) {
        if (isSupportedDeviceType(sub)) {
            return fetchDevices();
        }
        return null;
    }

    @Override
    public List<Device> getDeviceByType(String sub, boolean cacheFirst) {
        if (!isSupportedDeviceType(sub)) {
            return null;
        }

        synchronized (fetchDeviceLock) {
            if (cacheFirst) {
                // 1. 该模式下，有缓存返回缓存，没有直接加载全部
                final List<Device> deviceFromCache = createDeviceFromCache();
                if (deviceFromCache.size() > 0) {
                    return deviceFromCache;
                } else {
                    return fetchDevices();
                }
            }

            // 2. 该模式下，仅加载下一页
            markDevicesFromCache();

            final String loadingHomeId = currentHomeID;
            final long addTime = cacheInfo.getNewestAddTime();
            final List<BmtListResponse.BmtBeen> netDeviceList = requestIpcOnPageSync(addTime, false);
            if (null != netDeviceList && netDeviceList.size() > 0) {
                long maxAddTime = addTime;
                for (BmtListResponse.BmtBeen bean : netDeviceList) {
                    if (null != bean.getAddtime() && bean.getAddtime() > maxAddTime) {
                        maxAddTime = bean.getAddtime();
                    }
                }

                final List<Device> netDevices = createDeviceFromNet(netDeviceList);

                // 切换了房间，返回空数据
                if (!TextUtils.isEmpty(loadingHomeId) && !loadingHomeId.equals(currentHomeID)) {
                    return null;
                }

                if (0 < netDevices.size()) {
                    saveDeviceCache();
                }
            }
        }

        return new ArrayList<>(mBmtList1);
    }

    @Nullable
    @Override
    public List<Device> getCacheDeviceByType(String type) {
        if (!isSupportedDeviceType(type)) {
            return null;
        }
        final List<Device> result = new ArrayList<>();
        final List<Device> cacheDevices = createDeviceFromCache();
        if (cacheDevices.size() > 0) {
            markDevicesFromCache();
            result.addAll(cacheDevices);
        }
        return result;
    }

    @Nullable
    @Override
    public List<Device> getLocalAndNewDeviceByType(String type) {
        if (!isSupportedDeviceType(type)) {
            return null;
        }
        return fetchDevices();
    }

    @Nullable
    @Override
    public List<Device> getAllDeviceByType(String type) {
        if (!isSupportedDeviceType(type)) {
            return null;
        }
        synchronized (fetchDeviceLock) {
            createDeviceFromCache();
            markDevicesFromCache();
            requestDeviceCirculate(0);
            DDLog.i(TAG, "getAllDeviceByType finish: bmt");
        }
        return new ArrayList<>(mBmtList1);
    }

    @Override
    public boolean isSupportedDeviceType(String type) {
        return !TextUtils.isEmpty(type) && supportDeivceType.contains(type);
    }

    @Override
    public boolean removeDeviceCacheById(String s) {
        if (TextUtils.isEmpty(s)) {
            return false;
        }

        synchronized (listLock) {
            boolean remove = false;
            for (Device device : mBmtList1) {
                if (s.equals(device.getId())) {
                    cacheInfo.removeDevice(s, null);
                    saveDeviceCache();
                    mBmtList1.remove(device);
                    remove = true;
                    break;
                }
            }
            DDLog.i(TAG, "removeDeviceCacheById. " + s + " remove succeed ？" + remove);
            return remove;
        }
    }

    @Override
    public boolean removeDeviceCacheByIdAndSub(String id, String sub) {
        if (TextUtils.isEmpty(sub) || TextUtils.isEmpty(id)) {
            return false;
        }

        synchronized (listLock) {
            boolean remove = false;
            for (Device device : mBmtList1) {
                if (id.equals(device.getId()) && sub.equals(device.getSubCategory())) {
                    cacheInfo.removeDevice(id, sub);
                    saveDeviceCache();
                    mBmtList1.remove(device);
                    remove = true;
                    break;
                }
            }
            DDLog.i(TAG, "removeDeviceCacheByIdAndSub. " + sub + " remove succeed ？" + remove);
            return remove;
        }
    }

    @Override
    public boolean removeDeviceCacheByType(String sub) {
        if (isSupportedDeviceType(sub)) {
            clearDeviceCache();
            return true;
        }
        return false;
    }

    @Override
    @Keep
    public boolean releaseDeviceByType(String sub) {
        if (BmtConst.ALL_PROVIDER.contains(sub)) {
            for (Device device : mBmtList1) {
                if (device.getSubCategory().equals(sub)) {
                    BmtDevice bmtDevice = (BmtDevice) device;
                    bmtDevice.disconnect();
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public Device acquireTemporaryDevices(String id, String model) {
        synchronized (fetchDeviceLock) {
            Device result = SearchBmtHelper.get().searchTemporaryBmtNoCache(currentHomeID, id, model, multipleSender);
            return result;
        }
    }

    @Override
    public BasePluginBinder createPluginBinder(Context context, @NonNull String type) {
        switch (type) {
            case BINDER_KEY_BMT_HP5000:
                return new BmtHP5000Binder(context);
            case BINDER_KEY_BMT_HP5001:
                return new BmtHP5001Binder(context);
            case BINDER_KEY_BMT_PC1BAK15HS10:
                return new BmtPowerCore20Binder(context);
            case BINDER_KEY_BMT_PS1BAK10HS10:
                return new BmtPowerStoreBinder(context);
            case BINDER_KEY_BMT_VB1BAK5HS10:
                return new BmtPowerPulseBinder(context);
            case BINDER_KEY_BMT_PC3:
                return new BmtPowerCore30Binder(context);
            case BINDER_KEY_BMT_HP5000_NETWORK_MANAGER:
                return new BmtHP5000NetworkManager(context);
            case BINDER_KEY_BMT_HP5001_NETWORK_MANAGER:
                return new BmtHP5001NetworkManager(context);
            case BINDER_KEY_BMT_PC1BAK15HS10_NETWORK_MANAGER:
                return new BmtPowerCore20NetworkManager(context);
            case BINDER_KEY_BMT_PS1BAK10HS10_NETWORK_MANAGER:
                return new BmtPowerStoreNetworkManager(context);
            case BINDER_KEY_BMT_VB1BAK5HS10_NETWORK_MANAGER:
                return new BmtPowerPulseNetworkManager(context);
            case BINDER_KEY_BMT_PC3_NETWORK_MANAGER:
                return new BmtPowerCore30NetworkManager(context);
            default:
                return null;
        }
    }

    @Subscribe
    public void onEvent(DeivceChangeEvent event) {
        if (event.isAdd()) {

        } else if (event.isRemove()) {
            synchronized (listLock) {
                for (Device device : mBmtList1) {
                    if (device.getId().equals(event.getDevice().getId())
                            && device.getSubCategory().equals(event.getDevice().getSubCategory())) {
                        cacheInfo.removeDevice(device.getId(), device.getSubCategory());
                        saveDeviceCache();
                        mBmtList1.remove(device);
                        DDLog.i(TAG, " DeivceChangeEvent: 删除bmt：" + mBmtList1.size());
                        break;
                    }
                }
            }
        }
    }

    @Subscribe
    public void onEvent(CommonCmdEvent commonCmdEvent) {
        synchronized (listLock) {
            DDLog.i(TAG, "on Event: commonCmdEvent");
            if ((CommonCmdEvent.CMD.UPDATE_REGION.equals(commonCmdEvent.getCmd()))) {
                try {
                    JSONObject object = new JSONObject(commonCmdEvent.getExtra());
                    String model = DDJSONUtil.getString(object, "model");
                    if (!BmtConst.ALL_PROVIDER.contains(model)) {
                        return;
                    }
                    String pid = DDJSONUtil.getString(object, "id");
                    for (Device device : mBmtList1) {
                        if (pid.equals(device.getId()) && model.equals(device.getSubCategory())) {
                            BmtDevice bmtDevice = (BmtDevice) device;
                            String countryCode = DDJSONUtil.getString(object, "new_country_code");
                            String deliveryArea = DDJSONUtil.getString(object, "new_delivery_area");
                            bmtDevice.setCountryCodeAndDeliveryArea(countryCode, deliveryArea);
                            EventBus.getDefault().post(new BmtCountryCodeUpdateEvent(pid, countryCode, deliveryArea));
                            break;
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            } else if ((CommonCmdEvent.CMD.BMT_ADD.equals(commonCmdEvent.getCmd()))
                    && !TextUtils.isEmpty(commonCmdEvent.getExtra())) {
//             手动插入device
                try {
                    JSONObject object = new JSONObject(commonCmdEvent.getExtra());
                    String model = DDJSONUtil.getString(object, "model");
                    if (!BmtConst.ALL_PROVIDER.contains(model)) {
                        return;
                    }
                    String pid = DDJSONUtil.getString(object, "id");
                    for (Device device : mBmtList1) {
                        if (pid.equals(device.getId()) && model.equals(device.getSubCategory())) {
                            BmtDevice bmtDevice = (BmtDevice) device;
                            bmtDevice.setFlagDeleted(false);
                            bmtDevice.needConnectAgain();
                            DDLog.i(TAG, "on Event: 重新连接，添加bmt 但bmt已经存在了：" + mBmtList1.size());
                            bmtDevice.disconnect();
                            HashMap<String, Object> par = new HashMap<>();
                            par.put("cmd", BmtCmd.CONNECT);
                            par.put("discardCache", true);
                            device.submit(par);
                            return;
                        }
                    }

                    BmtDevice bmt = null;
                    BmtCacheInfo.CacheInfo cache = null;
                    if (BmtConst.PROVIDER_BMT_HP5000.equalsIgnoreCase(model)) {
                        bmt = new HP5000(multipleSender,
                                DDJSONUtil.getString(object, "id"),
                                DDJSONUtil.getString(object, "homeID"),
                                DDJSONUtil.getString(object, "end_id"),
                                DDJSONUtil.getString(object, "group_id"),
                                System.currentTimeMillis());
                        bmt.needConnectAgain();
                        cache = new BmtCacheInfo.CacheInfo(DDJSONUtil.getString(object, "id"), BmtConst.PROVIDER_BMT_HP5000);
                    } else if (BmtConst.PROVIDER_BMT_HP5001.equalsIgnoreCase(model)) {
                        bmt = new HP5001(multipleSender,
                                DDJSONUtil.getString(object, "id"),
                                DDJSONUtil.getString(object, "homeID"),
                                DDJSONUtil.getString(object, "end_id"),
                                DDJSONUtil.getString(object, "group_id"),
                                System.currentTimeMillis());
                        bmt.needConnectAgain();
                        cache = new BmtCacheInfo.CacheInfo(DDJSONUtil.getString(object, "id"), BmtConst.PROVIDER_BMT_HP5001);
                    } else if (BmtConst.PROVIDER_PC1_BAK15_HS10.equalsIgnoreCase(model)) {
                        bmt = new PowerCore20(multipleSender,
                                DDJSONUtil.getString(object, "id"),
                                DDJSONUtil.getString(object, "homeID"),
                                DDJSONUtil.getString(object, "end_id"),
                                DDJSONUtil.getString(object, "group_id"),
                                System.currentTimeMillis());
                        bmt.needConnectAgain();
                        cache = new BmtCacheInfo.CacheInfo(DDJSONUtil.getString(object, "id"), BmtConst.PROVIDER_PC1_BAK15_HS10);
                    } else if (BmtConst.PROVIDER_PS1_BAK10_HS10.equalsIgnoreCase(model)) {
                        bmt = new PowerStore(multipleSender,
                                DDJSONUtil.getString(object, "id"),
                                DDJSONUtil.getString(object, "homeID"),
                                DDJSONUtil.getString(object, "end_id"),
                                DDJSONUtil.getString(object, "group_id"),
                                System.currentTimeMillis());
                        bmt.needConnectAgain();
                        cache = new BmtCacheInfo.CacheInfo(DDJSONUtil.getString(object, "id"), BmtConst.PROVIDER_PS1_BAK10_HS10);
                    } else if (BmtConst.PROVIDER_VB1_BAK5_HS10.equals(model)) {
                        bmt = new PowerPulse(multipleSender,
                                DDJSONUtil.getString(object, "id"),
                                DDJSONUtil.getString(object, "homeID"),
                                DDJSONUtil.getString(object, "end_id"),
                                DDJSONUtil.getString(object, "group_id"),
                                System.currentTimeMillis());
                        bmt.needConnectAgain();
                        cache = new BmtCacheInfo.CacheInfo(DDJSONUtil.getString(object, "id"), BmtConst.PROVIDER_VB1_BAK5_HS10);
                    } else if (BmtConst.PROVIDER_PC3.equals(model)) {
                        bmt = new PowerCore30(multipleSender,
                                DDJSONUtil.getString(object, "id"),
                                DDJSONUtil.getString(object, "homeID"),
                                DDJSONUtil.getString(object, "end_id"),
                                DDJSONUtil.getString(object, "group_id"),
                                System.currentTimeMillis());
                        bmt.needConnectAgain();
                        cache = new BmtCacheInfo.CacheInfo(DDJSONUtil.getString(object, "id"), BmtConst.PROVIDER_PC3);
                    }
                    if (bmt == null) {
                        return;
                    }
                    DDLog.i(TAG, "on Event: 添加bmt：" + mBmtList1.size());
                    addDeviceIfNotExit(bmt);
                    cacheInfo.addDevice(cache);
                    saveDeviceCache();

                    DeivceChangeEvent event = new DeivceChangeEvent(bmt);
                    event.setAdd(true);
                    EventBus.getDefault().post(event);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            } else if ((CommonCmdEvent.CMD.BMT_DELETE.equals(commonCmdEvent.getCmd()))
                    && !TextUtils.isEmpty(commonCmdEvent.getExtra())) {
                try {
                    JSONObject object = new JSONObject(commonCmdEvent.getExtra());
                    String model = DDJSONUtil.getString(object, "model");
                    if (!BmtConst.ALL_PROVIDER.contains(model)) {
                        return;
                    }
                    String pid = DDJSONUtil.getString(object, "id");
                    for (Device device : mBmtList1) {
                        if (pid.equals(device.getId()) && model.equals(device.getSubCategory())) {
                            BmtDevice bmtDevice = (BmtDevice) device;
                            bmtDevice.deleteDirect(true);
                            DDLog.i(TAG, "on Event: 删除bmt：" + mBmtList1.size());
                            break;
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            } else if ((CommonCmdEvent.CMD.BMT_RENAME.equals(commonCmdEvent.getCmd()))
                    && !TextUtils.isEmpty(commonCmdEvent.getExtra())) {
                try {
                    JSONObject object = new JSONObject(commonCmdEvent.getExtra());
                    String model = DDJSONUtil.getString(object, "model");
                    if (!BmtConst.ALL_PROVIDER.contains(model)) {
                        return;
                    }
                    String pid = DDJSONUtil.getString(object, "id");
                    for (Device device : mBmtList1) {
                        if (pid.equals(device.getId()) && model.equals(device.getSubCategory())) {
                            BmtDevice bmtDevice = (BmtDevice) device;
                            bmtDevice.setNameDirect(DDJSONUtil.getString(object, "name"));
                            break;
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            } else if (CommonCmdEvent.CMD.REMOTE_ADDRESS_UPDATED.equals(commonCmdEvent.getCmd())
                    && !TextUtils.isEmpty(commonCmdEvent.getExtra())) {
                try {
                    JSONObject object = new JSONObject(commonCmdEvent.getExtra());
                    final String localHomeId = DDJSONUtil.getString(object, "home_id");
                    final int e2ePort = DDJSONUtil.getInt(object, "e2e_port");
                    final String e2eDomain = DDJSONUtil.getString(object, "e2e_domain");
                    if (!TextUtils.isEmpty(localHomeId) && localHomeId.equals(currentHomeID)
                            && (0 == e2ePort || e2ePort != this.e2ePort)
                            && !TextUtils.isEmpty(e2eDomain) && !e2eDomain.equals(this.e2eDomain)) {
                        this.e2ePort = e2ePort;
                        this.e2eDomain = e2eDomain;
                        notifyRemoteAddressUpdated();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else if (CommonCmdEvent.CMD.ON_BEFORE_HOME_DISCONNECT.equals(commonCmdEvent.getCmd())) {
                final List<Device> localList = mBmtList1;
                if (null != localList && localList.size() > 0) {
                    for (Device device : localList) {
                        BmtDevice bmtDevice = (BmtDevice) device;
                        bmtDevice.needConnectAgain();
                    }
                }
            } else if (CommonCmdEvent.CMD.ON_BEFORE_HOME_SWITCH.equals(commonCmdEvent.getCmd())) {
                SearchBmtHelper.get().release();
            } else if (CommonCmdEvent.CMD.LOGIN_SUCCESS.equals(commonCmdEvent.getCmd())) {
                cacheInfo.updateFrom(null);
                for (Device device : mBmtList1) {
                    device.destory();
                }
                mBmtList1.clear();
            }
        }
    }

    /**
     * 连接的服务器节点变更，所有设备需要重连
     */
    private void notifyRemoteAddressUpdated() {
        BmtDevice bmtDevice;
        for (Device device : mBmtList1) {
            if (device instanceof BmtDevice) {
                bmtDevice = (BmtDevice) device;
                bmtDevice.onRemoteAddressUpdated();
            }
        }
    }

    private void markDevicesFromCache() {
        synchronized (listLock) {
            if (mBmtList1.size() > 0) {
                for (Device device : mBmtList1) {
                    ((BmtDevice)device).setFlagCache(true);
                }
            }
        }
    }

    @NonNull
    private List<Device> createDeviceFromNet(@NonNull List<BmtListResponse.BmtBeen> deviceBeanList) {
        List<Device> netDevices = new ArrayList<>();
        for (BmtListResponse.BmtBeen listBean : deviceBeanList) {
            // 已经在缓存中，修改状态
            final Device cacheDevice = getDevice(listBean.getPid(), listBean.getProvider());
            if (null != cacheDevice) {
                final BmtDevice d = (BmtDevice) cacheDevice;
                d.setFlagCache(true);
                d.setFlagDeleted(false);
                d.updateSender(multipleSender);
                continue;
            }

            // 不在缓存中，创建并缓存
            BmtDevice device;
            BmtCacheInfo.CacheInfo cache = null;
            boolean added;
            if (BmtConst.PROVIDER_BMT_HP5000.equals(listBean.getProvider())) {
                device = new HP5000(multipleSender, currentHomeID, listBean);
                added = addDeviceIfNotExit(device);
                if (added) {
                    netDevices.add(device);
                    cache = new BmtCacheInfo.CacheInfo(listBean.getPid(), BmtConst.PROVIDER_BMT_HP5000);
                }
            } else if (BmtConst.PROVIDER_BMT_HP5001.equals(listBean.getProvider())) {
                device = new HP5001(multipleSender, currentHomeID, listBean);
                added = addDeviceIfNotExit(device);
                if (added) {
                    netDevices.add(device);
                    cache = new BmtCacheInfo.CacheInfo(listBean.getPid(), BmtConst.PROVIDER_BMT_HP5001);
                }
            } else if (BmtConst.PROVIDER_PC1_BAK15_HS10.equals(listBean.getProvider())) {
                device = new PowerCore20(multipleSender, currentHomeID, listBean);
                added = addDeviceIfNotExit(device);
                if (added) {
                    netDevices.add(device);
                    cache = new BmtCacheInfo.CacheInfo(listBean.getPid(), BmtConst.PROVIDER_PC1_BAK15_HS10);
                }
            } else if (BmtConst.PROVIDER_PS1_BAK10_HS10.equals(listBean.getProvider())) {
                device = new PowerStore(multipleSender, currentHomeID, listBean);
                added = addDeviceIfNotExit(device);
                if (added) {
                    netDevices.add(device);
                    cache = new BmtCacheInfo.CacheInfo(listBean.getPid(), BmtConst.PROVIDER_PS1_BAK10_HS10);
                }
            } else if (BmtConst.PROVIDER_VB1_BAK5_HS10.equals(listBean.getProvider())) {
                device = new PowerPulse(multipleSender, currentHomeID, listBean);
                added = addDeviceIfNotExit(device);
                if (added) {
                    netDevices.add(device);
                    cache = new BmtCacheInfo.CacheInfo(listBean.getPid(), BmtConst.PROVIDER_VB1_BAK5_HS10);
                }
            } else if (BmtConst.PROVIDER_PC3.equals(listBean.getProvider())) {
                device = new PowerCore30(multipleSender, currentHomeID, listBean);
                added = addDeviceIfNotExit(device);
                if (added) {
                    netDevices.add(device);
                    cache = new BmtCacheInfo.CacheInfo(listBean.getPid(), BmtConst.PROVIDER_PC3);
                }
            }

            if (null != cache) {
                final Long addTime = listBean.getAddtime();
                if (null != addTime) {
                    cache.setAddTime(addTime);
                }
                cache.setCountryCode(listBean.hasCountryCode() ? listBean.getCountry_code() : "");
                cache.setDeliveryArea(listBean.hasDeliveryArea() ? listBean.getDelivery_area() : "");
                cacheInfo.addDevice(cache);
            }
        }
        DDLog.i(TAG, CACHE_IDENTIFY + ":cache----netdevice: " + netDevices.size());
        return netDevices;
    }

    @NonNull
    private List<Device> createDeviceFromCache() {
        if (mBmtList1.size() > 0) {
            DDLog.d(TAG, CACHE_IDENTIFY + ":cache--------不从缓存创建");
            return new ArrayList<>(mBmtList1);
        }

        final String homeId = currentHomeID;
        DinUser user = DinCore.getUserInstance().getUser();
        final String userId = null != user ? user.getUser_id() : null;
        final BmtCacheInfo cache = DeviceCacheHelper.getCache(homeId, userId, CACHE_IDENTIFY, BmtCacheInfo.class);
        cacheInfo.updateFrom(cache);
        DDLog.d(TAG, CACHE_IDENTIFY + ":cache--------reedCache: " + cacheInfo);

        List<Device> cacheDevices = new ArrayList<>();
        if (!cacheInfo.isCacheEmpty()) {
            final List<BmtCacheInfo.CacheInfo> cacheList = cacheInfo.getCacheList();
            Device device = null;
            for (BmtCacheInfo.CacheInfo info : cacheList) {
                final String pid = info.getPid();
                final String provider = info.getProvider();
                if (TextUtils.isEmpty(pid) || TextUtils.isEmpty(provider)) {
                    continue;
                }

                final Device cacheDevice = getDevice(pid, provider);
                if (null != cacheDevice) {
                    ((BmtDevice) cacheDevice).setFlagCache(true);
                    cacheDevices.add(cacheDevice);
                    continue;
                }

                if (BmtConst.PROVIDER_BMT_HP5000.equalsIgnoreCase(provider)) {
                    device = new HP5000(multipleSender, currentHomeID, pid, info);
                } else if (BmtConst.PROVIDER_BMT_HP5001.equalsIgnoreCase(provider)) {
                    device = new HP5001(multipleSender, currentHomeID, pid, info);
                } else if (BmtConst.PROVIDER_PC1_BAK15_HS10.equalsIgnoreCase(provider)) {
                    device = new PowerCore20(multipleSender, currentHomeID, pid, info);
                } else if (BmtConst.PROVIDER_PS1_BAK10_HS10.equalsIgnoreCase(provider)) {
                    device = new PowerStore(multipleSender, currentHomeID, pid, info);
                } else if (BmtConst.PROVIDER_VB1_BAK5_HS10.equalsIgnoreCase(provider)) {
                    device = new PowerPulse(multipleSender, currentHomeID, pid, info);
                } else if (BmtConst.PROVIDER_PC3.equalsIgnoreCase(provider)) {
                    device = new PowerCore30(multipleSender, currentHomeID, pid, info);
                }

                if (device != null) {
                    addDeviceIfNotExit(device);
                    cacheDevices.add(device);
                }
            }
        }
        DDLog.i(TAG, CACHE_IDENTIFY + ":cache----缓存device: " + cacheDevices.size());
        return cacheDevices;
    }

    private boolean addDeviceIfNotExit(Device device) {
        if (null != device) {
            if (!mBmtList1.contains(device)) {
                DDLog.d(TAG, CACHE_IDENTIFY + ":添加Device到列表");
                mBmtList1.add(device);
                return true;
            } else {
                BmtDevice d = (BmtDevice) device;
                d.setFlagDeleted(false);
                d.updateSender(multipleSender);
                return false;
            }
        }
        return false;
    }

    private void saveDeviceCache() {
        final String homeId = currentHomeID;
        DinUser user = DinCore.getUserInstance().getUser();
        final String userId = null != user ? user.getUser_id() : null;
        DeviceCacheHelper.saveCacheAsync(homeId, userId, CACHE_IDENTIFY, cacheInfo);
        DDLog.d(TAG, CACHE_IDENTIFY + ":cache--------savecache: " + cacheInfo);
    }

    private void clearDeviceCache() {
        final String homeId = currentHomeID;
        DinUser user = DinCore.getUserInstance().getUser();
        final String userId = null != user ? user.getUser_id() : null;
        DeviceCacheHelper.removeCacheASync(homeId, userId, CACHE_IDENTIFY);
    }

    @Subscribe
    public void onEvent(BmtExceptionEvent event) {
        String deviceId = event.getDeviceId();
        String deviceSub = event.getSubCategory();
        if (StringUtil.isNullOrEmpty(deviceId)) return;
        final List<BmtCacheInfo.CacheInfo> cacheList = cacheInfo.getCacheList();
        if (cacheList == null || cacheList.size() == 0) return;
        for (BmtCacheInfo.CacheInfo bmtCacheInfo : cacheList) {
            if (deviceId.equals(bmtCacheInfo.getPid()) && deviceSub.equals(bmtCacheInfo.getProvider())) {
                if (event.getVertBatteryExceptions() != null) {
                    bmtCacheInfo.setVertBatteryExceptions(event.getVertBatteryExceptions());
                }
                if (event.getVertExceptions() != null) {
                    bmtCacheInfo.setVertExceptions(event.getVertExceptions());
                }
                if (event.getVertGridExceptions() != null) {
                    bmtCacheInfo.setVertGridExceptions(event.getVertGridExceptions());
                }
                if (event.getVertSystemExceptions() != null) {
                    bmtCacheInfo.setVertSystemExceptions(event.getVertSystemExceptions());
                }
                if (event.getVertmpptExceptions() != null) {
                    bmtCacheInfo.setVertmpptExceptions(event.getVertmpptExceptions());
                }
                if (event.getVertPresentExceptions() != null) {
                    bmtCacheInfo.setVertPresentExceptions(event.getVertPresentExceptions());
                }
                if (event.getVertDCExceptions() != null) {
                    bmtCacheInfo.setVertDCExceptions(event.getVertDCExceptions());
                }
                if (event.getEvExceptions() != null) {
                    bmtCacheInfo.setEvExceptions(event.getEvExceptions());
                }
                if (event.getMpptExceptions() != null) {
                    bmtCacheInfo.setMpptExceptions(event.getMpptExceptions());
                }
                if (event.getCabinetExceptions() != null) {
                    bmtCacheInfo.setCabinetExceptions(event.getCabinetExceptions());
                }
                if (event.getBatteryExceptions() != null) {
                    bmtCacheInfo.setBatteryExceptions(event.getBatteryExceptions());
                }
                if (event.getSystemExceptions() != null) {
                    bmtCacheInfo.setSystemExceptions(event.getSystemExceptions());
                }
                if (event.getCommunicationExceptions() != null) {
                    bmtCacheInfo.setCommunicationExceptions(event.getCommunicationExceptions());
                }
                saveDeviceCache();
                break;
            }
        }
    }

    @Subscribe
    public void onEvent(BmtDeviceNameUpdateEvent event) {
        MsctLog.i(TAG, "BmtDeviceNameUpdateEvent, " + event.toString());
        updateDsDeviceNameOnCache(event.getPid(), event.getSubCategory(), event.getName());
    }

    private void updateDsDeviceNameOnCache(final String pid, final String sub, final String name) {
        if (TextUtils.isEmpty(pid)
                || TextUtils.isEmpty(sub)
                || TextUtils.isEmpty(name)) {
            MsctLog.w(TAG, "updateBmtDeviceNameOnCache, failed, pid or name is null! pid=" + pid + ", sub=" + sub + ", name=" + name);
            return;
        }

        final List<BmtCacheInfo.CacheInfo> cacheList = cacheInfo.getCacheList();
        boolean updated = false;
        if (null != cacheList && cacheList.size() > 0) {
            BmtCacheInfo.CacheInfo cache;
            for (int i = 0; i < cacheList.size(); i++) {
                cache = cacheList.get(i);
                if (pid.equals(cache.getPid()) && sub.equals(cache.getProvider())) {
                    if (!name.equals(cache.getName())) {
                        cache.setName(name);
                        updated = true;
                    }
                    break;
                }
            }
        }

        if (updated) {
            saveDeviceCache();
        }
    }

    @Subscribe
    public void onEvent(RegulateFrequencyStateEvent event) {
        String id = event.getDeviceId();
        String provider = event.getProvider();
        int regulateFrequencyState = event.getRegulateFrequencyState();
        if (TextUtils.isEmpty(id)) {
            MsctLog.w(TAG, "updateRegulateFrequencyStateOnCache, failed, devId is null! pid=" + id);
            return;
        }

        final List<BmtCacheInfo.CacheInfo> cacheList = cacheInfo.getCacheList();
        boolean updated = false;
        if (null != cacheList && cacheList.size() > 0) {
            BmtCacheInfo.CacheInfo cache;
            for (int i = 0; i < cacheList.size(); i++) {
                cache = cacheList.get(i);
                if (id.equals(cache.getPid()) && provider.equals(cache.getProvider())) {
                    if (regulateFrequencyState != cache.getRegulateFrequencyState()) {
                        cache.setRegulateFrequencyState(regulateFrequencyState);
                        updated = true;
                    }
                    break;
                }
            }
        }

        if (updated) {
            saveDeviceCache();
        }
    }

    @Subscribe
    public void onEvent(BmtChipsStatusEvent event) {
        String devId = event.getDeviceId();
        String provider = event.getProvider();
        int chipsStatus = event.getChipsStatus();
        if (TextUtils.isEmpty(devId)) {
            MsctLog.w(TAG, "updateBmtChipsStatusOnCache, failed, devId is null! pid=" + devId);
            return;
        }

        final List<BmtCacheInfo.CacheInfo> cacheList = cacheInfo.getCacheList();
        boolean updated = false;
        if (null != cacheList && cacheList.size() > 0) {
            BmtCacheInfo.CacheInfo cache;
            for (int i = 0; i < cacheList.size(); i++) {
                cache = cacheList.get(i);
                if (devId.equals(cache.getPid()) && provider.equals(cache.getProvider())) {
                    if (chipsStatus != cache.getChipsStatus()) {
                        cache.setChipsStatus(chipsStatus);
                        updated = true;
                    }
                    break;
                }
            }
        }

        if (updated) {
            saveDeviceCache();
        }
    }

    @Subscribe
    public void onEvent(BmtThirdPartyPVOnEvent event) {
        String devId = event.getDeviceId();
        String provider = event.getProvider();
        boolean thirdPartyPVOn = event.isThirdpartyPVOn();
        if (TextUtils.isEmpty(devId)) {
            MsctLog.w(TAG, "updateBmtChipsStatusOnCache, failed, devId is null! pid=" + devId);
            return;
        }

        final List<BmtCacheInfo.CacheInfo> cacheList = cacheInfo.getCacheList();
        boolean updated = false;
        if (null != cacheList && cacheList.size() > 0) {
            BmtCacheInfo.CacheInfo cache;
            for (int i = 0; i < cacheList.size(); i++) {
                cache = cacheList.get(i);
                if (devId.equals(cache.getPid()) && provider.equals(cache.getProvider())) {
                    if (thirdPartyPVOn != cache.isThirdpartyPVOn()) {
                        cache.setThirdpartyPVOn(thirdPartyPVOn);
                        updated = true;
                    }
                    break;
                }
            }
        }

        if (updated) {
            saveDeviceCache();
        }
    }

    @Subscribe
    public void onEvent(BmtVertStateEvent event) {
        String devId = event.getDeviceId();
        String provider = event.getProvider();
        List vertStates = event.getVertStates();
        if (TextUtils.isEmpty(devId)) {
            MsctLog.w(TAG, "updateBmtChipsStatusOnCache, failed, devId is null! pid=" + devId);
            return;
        }

        final List<BmtCacheInfo.CacheInfo> cacheList = cacheInfo.getCacheList();
        if (null != cacheList && cacheList.size() > 0) {
            BmtCacheInfo.CacheInfo cache;
            for (int i = 0; i < cacheList.size(); i++) {
                cache = cacheList.get(i);
                if (devId.equals(cache.getPid()) && provider.equals(cache.getProvider())) {
                    cache.setVertStates(vertStates);
                    saveDeviceCache();
                    break;
                }
            }
        }

    }

    @Subscribe
    public void onEvent(IotVersionEvent event) {
        String devId = event.getDeviceId();
        String provider = event.getProvider();
        String iotVersion = event.getIotVersion();
        if (TextUtils.isEmpty(devId)) {
            MsctLog.w(TAG, "IotVersionEvent, failed, devId is null! pid=" + devId);
            return;
        }

        final List<BmtCacheInfo.CacheInfo> cacheList = cacheInfo.getCacheList();
        boolean updated = false;
        if (null != cacheList && cacheList.size() > 0) {
            BmtCacheInfo.CacheInfo cache;
            for (int i = 0; i < cacheList.size(); i++) {
                cache = cacheList.get(i);
                if (devId.equals(cache.getPid()) && provider.equals(cache.getProvider())) {
                    String cacheIotVersion = cache.getIotVersion();
                    if (TextUtils.isEmpty(cacheIotVersion) || (!TextUtils.isEmpty(iotVersion) &&
                            !cacheIotVersion.equals(iotVersion))) {
                        cache.setIotVersion(iotVersion);
                        updated = true;
                    }
                    break;
                }
            }
        }
        if (updated) {
            saveDeviceCache();
        }
    }

    @Subscribe
    public void onEvent(BmtCountryCodeUpdateEvent event) {
        String pid = event.getPid();
        String countryCode = event.getCountryCode();
        String deliveryArea = event.getDeliveryArea();
        if (TextUtils.isEmpty(pid) || (TextUtils.isEmpty(countryCode) && TextUtils.isEmpty(deliveryArea))) {
            return;
        }

        final List<BmtCacheInfo.CacheInfo> cacheList = cacheInfo.getCacheList();
        boolean updated = false;
        if (null != cacheList && cacheList.size() > 0) {
            BmtCacheInfo.CacheInfo cache;
            for (int i = 0; i < cacheList.size(); i++) {
                cache = cacheList.get(i);
                if (pid.equals(cache.getPid())) {
                    if (!TextUtils.isEmpty(countryCode) && !countryCode.equals(cache.getCountryCode())) {
                        cache.setCountryCode(countryCode);
                        updated = true;
                    }

                    if (!TextUtils.isEmpty(deliveryArea) && !deliveryArea.equals(cache.getDeliveryArea())) {
                        cache.setDeliveryArea(deliveryArea);
                        updated = true;
                    }
                    break;
                }
            }
        }

        if (updated) {
            saveDeviceCache();
        }
    }

    @Subscribe
    public void onEvent(GridStatusEvent event) {
        String devId = event.getDeviceId();
        String provider = event.getProvider();
        int gridStatus = event.getGridStatus();
        if (TextUtils.isEmpty(devId)) {
            MsctLog.w(TAG, "IotVersionEvent, failed, devId is null! pid=" + devId);
            return;
        }

        final List<BmtCacheInfo.CacheInfo> cacheList = cacheInfo.getCacheList();
        boolean updated = false;
        if (null != cacheList && cacheList.size() > 0) {
            BmtCacheInfo.CacheInfo cache;
            for (int i = 0; i < cacheList.size(); i++) {
                cache = cacheList.get(i);
                if (devId.equals(cache.getPid()) && provider.equals(cache.getProvider())) {
                    if (gridStatus != cache.getGridStatus()) {
                        cache.setGridStatus(gridStatus);
                        updated = true;
                    }
                    break;
                }
            }
        }
        if (updated) {
            saveDeviceCache();
        }
    }

    @Subscribe
    public void onEvent(EmergencySmartReserveEvent event) {
        String devId = event.getDeviceId();
        String provider = event.getProvider();
        int emergencyReserve = event.getEmergency_reserve();
        int smartReserve = event.getSmart_reserve();
        if (TextUtils.isEmpty(devId) || TextUtils.isEmpty(provider)) {
            MsctLog.w(TAG, "EmergencySmartReserveEvent, failed, devId or provider is null! pid=" + devId);
            return;
        }

        final List<BmtCacheInfo.CacheInfo> cacheList = cacheInfo.getCacheList();
        boolean updated = false;
        if (null != cacheList && cacheList.size() > 0) {
            BmtCacheInfo.CacheInfo cache;
            for (int i = 0; i < cacheList.size(); i++) {
                cache = cacheList.get(i);
                if (devId.equals(cache.getPid()) && provider.equals(cache.getProvider())) {
                    if (emergencyReserve != cache.getEmergency_reserve()) {
                        cache.setEmergency_reserve(emergencyReserve);
                        updated = true;
                    }
                    if (smartReserve != cache.getSmart_reserve()) {
                        cache.setSmart_reserve(smartReserve);
                        updated = true;
                    }
                    break;
                }
            }
        }
        if (updated) {
            saveDeviceCache();
        }
    }

    @Subscribe
    public void onEvent(PvSupplyCustomizationSupportedEvent event) {
        String devId = event.getDeviceId();
        String provider = event.getProvider();
        boolean pvSupplyCustomizationSupported = event.isPvSupplyCustomizationSupported();
        if (TextUtils.isEmpty(devId) || TextUtils.isEmpty(provider)) {
            MsctLog.w(TAG, "updateBmtChipsStatusOnCache, failed, devId is null! pid=" + devId);
            return;
        }

        final List<BmtCacheInfo.CacheInfo> cacheList = cacheInfo.getCacheList();
        boolean updated = false;
        if (null != cacheList && cacheList.size() > 0) {
            BmtCacheInfo.CacheInfo cache;
            for (int i = 0; i < cacheList.size(); i++) {
                cache = cacheList.get(i);
                if (devId.equals(cache.getPid()) && provider.equals(cache.getProvider())) {
                    if (pvSupplyCustomizationSupported != cache.isPvSupplyCustomizationSupported()) {
                        cache.setPvSupplyCustomizationSupported(pvSupplyCustomizationSupported);
                        updated = true;
                    }
                    break;
                }
            }
        }

        if (updated) {
            saveDeviceCache();
        }
    }
}
