package com.dinsafer.module_bmt.utils;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.msctlib.netty.IMultipleSender;
import com.dinsafer.module_bmt.bean.BmtConst;
import com.dinsafer.module_bmt.bean.BmtDevice;
import com.dinsafer.module_bmt.bean.HP5000;
import com.dinsafer.module_bmt.bean.HP5001;
import com.dinsafer.module_bmt.bean.PowerCore20;
import com.dinsafer.module_bmt.bean.PowerCore30;
import com.dinsafer.module_bmt.bean.PowerPulse;
import com.dinsafer.module_bmt.bean.PowerStore;
import com.dinsafer.module_bmt.http.BmtApi;
import com.dinsafer.module_bmt.http.bean.BmtListResponse;
import com.dinsafer.module_bmt.http.bean.SearchBmtParams;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2023/1/4 3:03 下午
 */
public class SearchBmtHelper {

    private final static String TAG = "SearchBmtHelper";
    private final static long LOAD_INTERVAL = 2L * 1000; // 加载时间间隔

    private String currentHomeId;
    private Call<BmtListResponse> mSearchIpcCall;

    private final ConcurrentHashMap<String, BmtDevice> loadingMap = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, BmtDevice> needLoadMap = new ConcurrentHashMap<>();

    private volatile boolean scheduled = false;
    private final Handler mHandler = new Handler(Looper.getMainLooper());
    private final Runnable searchTask = () -> {
        cancel(true);

        synchronized (SearchBmtHelper.this) {
            if (needLoadMap.size() > 0) {
                final List<SearchBmtParams> params = new ArrayList<>(needLoadMap.size());
                for (Map.Entry<String, BmtDevice> entry : needLoadMap.entrySet()) {
                    params.add(new SearchBmtParams(entry.getKey(), entry.getValue().getSubCategory()));
                }
                loadingMap.putAll(needLoadMap);
                needLoadMap.clear();
                startSearchIpc(currentHomeId, params);
            }
        }
        scheduled = false;
    };

    /**
     * 添加到查询BMT信息队列，等时间到时统一请求
     *
     * @param homeId
     * @param bmtDevice
     */
    public void addTask(final String homeId, final BmtDevice bmtDevice) {
        MsctLog.v(TAG, "addTask, homeId: " + homeId + ", deviceId: " + bmtDevice.getId());
        if (TextUtils.isEmpty(currentHomeId) || !currentHomeId.equals(homeId)) {
            MsctLog.w(TAG, "addTask, currentHomeId is not equal to device's homeId, ignore!");
            return;
        }

        final String deviceId = bmtDevice.getId();
        if (TextUtils.isEmpty(deviceId)) {
            MsctLog.w(TAG, "addTask, empty device's id!");
            return;
        }

        synchronized (this) {
            if (loadingMap.containsKey(deviceId)
                    || needLoadMap.containsKey(deviceId)) {
                MsctLog.w(TAG, "addTask, device's id is loading or needLoad!");
                return;
            }

            needLoadMap.put(deviceId, bmtDevice);

            schedule(false);
        }
    }

    private void schedule(final boolean now) {
        synchronized (this) {
            if (needLoadMap.size() > 0 && !scheduled) {
                scheduled = true;
                mHandler.postDelayed(searchTask, now ? 0 : LOAD_INTERVAL);
            }
        }
    }

    public void setCurrentHomeId(final String homeId) {
        final String lastHomeId = currentHomeId;
        this.currentHomeId = homeId;
        if (!TextUtils.isEmpty(lastHomeId) && !lastHomeId.equals(currentHomeId)) {
            release();
        }
    }

    private void startSearchIpc(String homeID, @NonNull List<SearchBmtParams> params) {
        mSearchIpcCall = BmtApi.getInstance().searchBmt(homeID, params);
        mSearchIpcCall.enqueue(new Callback<BmtListResponse>() {
            @Override
            public void onResponse(Call<BmtListResponse> call, Response<BmtListResponse> response) {
                BmtListResponse body = response.body();
                if (response.isSuccessful() && null != body && body.getResult() != null) {
                    BmtListResponse.ResultBean result = body.getResult();
                    synchronized (SearchBmtHelper.this) {
                        if (loadingMap.size() == 0) {
                            return;
                        }
                        final ConcurrentHashMap<String, BmtDevice> devices = new ConcurrentHashMap<>(loadingMap);
                        loadingMap.clear();
                        notifyResult(devices, result.getBmts(), true);
                    }
                } else {
                    synchronized (SearchBmtHelper.this) {
                        if (loadingMap.size() == 0) {
                            return;
                        }
                        final ConcurrentHashMap<String, BmtDevice> devices = new ConcurrentHashMap<>(loadingMap);
                        loadingMap.clear();
                        notifyResult(devices, null, false);
                    }
                }
                mSearchIpcCall = null;
                schedule(true);
            }

            @Override
            public void onFailure(Call<BmtListResponse> call, Throwable t) {
                t.printStackTrace();
                synchronized (SearchBmtHelper.this) {
                    if (loadingMap.size() > 0) {
                        final ConcurrentHashMap<String, BmtDevice> devices = new ConcurrentHashMap<>(loadingMap);
                        loadingMap.clear();
                        notifyResult(devices, null, false);
                    }
                }
                mSearchIpcCall = null;
                schedule(true);
            }
        });
    }

    /**
     * 直接查询指定的Bmt信息
     *
     * @param homeID
     * @param bmtDevice
     */
    public void startSearchBmtSingle(String homeID, @NonNull final BmtDevice bmtDevice) {
        final String deviceId = bmtDevice.getId();
        final String provider = bmtDevice.getSubCategory();
        if (TextUtils.isEmpty(homeID) || TextUtils.isEmpty(deviceId) || TextUtils.isEmpty(provider)) {
            MsctLog.e(TAG, "startSearchBmtSingle homeId or empty deviceId or empty provider");
            return;
        }
        final SearchBmtParams params = new SearchBmtParams(deviceId, provider);
        MsctLog.i("test_time", "dincore-startSearchBmtSingle");
        Call<BmtListResponse> BmtListResponseCall = BmtApi.getInstance().searchBmt(homeID, Collections.singletonList(params));
        BmtListResponseCall.enqueue(new Callback<BmtListResponse>() {
            @Override
            public void onResponse(Call<BmtListResponse> call, Response<BmtListResponse> response) {
                MsctLog.i("test_time", "dincore-startSearchBmtSingleSuccess");
                BmtListResponse body = response.body();
                if (response.isSuccessful() && null != body && body.getResult() != null) {
                    BmtListResponse.ResultBean result = body.getResult();
                    List<BmtListResponse.BmtBeen> list = result.getBmts();
                    BmtListResponse.BmtBeen info = null;
                    if (null != list && list.size() > 0
                            && !TextUtils.isEmpty(deviceId)
                            && !TextUtils.isEmpty(provider)) {
                        for (int i = 0; i < list.size(); i++) {
                            BmtListResponse.BmtBeen listBean = list.get(i);
                            if (deviceId.equals(listBean.getPid()) && provider.equals(listBean.getProvider())) {
                                info = listBean;
                                break;
                            }
                        }
                    }
                    bmtDevice.onSearchBmtInfo(info, true);
                } else {
                    bmtDevice.onSearchBmtInfo(null, true);
                }
            }

            @Override
            public void onFailure(Call<BmtListResponse> call, Throwable t) {
                t.printStackTrace();
                bmtDevice.onSearchBmtInfo(null, false);
            }
        });
    }

    /**
     * 查询指定的Bmt信息，且不更新缓存的相关信息
     */
    public BmtDevice searchTemporaryBmtNoCache(String homeID, String deviceId, String provider, IMultipleSender sender) {
        if (TextUtils.isEmpty(homeID)
                || TextUtils.isEmpty(deviceId)
                || TextUtils.isEmpty(provider)
                || null == sender) {
            MsctLog.e(TAG, "searchTemporaryBmtNoCache homeId or empty deviceId or empty provider");
            return null;
        }
        final SearchBmtParams params = new SearchBmtParams(deviceId, provider);
        MsctLog.i("test_time", "dincore-searchTemporaryBmtNoCache");
        BmtDevice resultDevice = null;

        Call<BmtListResponse> bmtListResponseCall = BmtApi.getInstance().searchBmt(homeID, Collections.singletonList(params));
        try {
            Response<BmtListResponse> response = bmtListResponseCall.execute();
            MsctLog.i("test_time", "dincore-searchTemporaryBmtNoCache");
            BmtListResponse body = response.body();
            if (response.isSuccessful() && null != body && body.getResult() != null) {
                BmtListResponse.ResultBean result = body.getResult();
                List<BmtListResponse.BmtBeen> list = result.getBmts();
                if (null != list && list.size() > 0) {
                    for (int i = 0; i < list.size(); i++) {
                        BmtListResponse.BmtBeen listBean = list.get(i);
                        if (deviceId.equals(listBean.getPid())) {
                            if (BmtConst.PROVIDER_BMT_HP5000.equalsIgnoreCase(listBean.getProvider())) {
                                resultDevice = new HP5000(sender, homeID, listBean);
                                resultDevice.needConnectAgain();
                            } else if (BmtConst.PROVIDER_BMT_HP5001.equalsIgnoreCase(listBean.getProvider())) {
                                resultDevice = new HP5001(sender, homeID, listBean);
                                resultDevice.needConnectAgain();
                            } else if (BmtConst.PROVIDER_PC1_BAK15_HS10.equalsIgnoreCase(listBean.getProvider())) {
                                resultDevice = new PowerCore20(sender, homeID, listBean);
                                resultDevice.needConnectAgain();
                            } else if (BmtConst.PROVIDER_PS1_BAK10_HS10.equalsIgnoreCase(listBean.getProvider())) {
                                resultDevice = new PowerStore(sender, homeID, listBean);
                                resultDevice.needConnectAgain();
                            } else if (BmtConst.PROVIDER_VB1_BAK5_HS10.equalsIgnoreCase(listBean.getProvider())) {
                                resultDevice = new PowerPulse(sender, homeID, listBean);
                                resultDevice.needConnectAgain();
                            } else if (BmtConst.PROVIDER_PC3.equalsIgnoreCase(listBean.getProvider())) {
                                resultDevice = new PowerCore30(sender, homeID, listBean);
                                resultDevice.needConnectAgain();
                            }
                            break;
                        }
                    }
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return resultDevice;

    }

    private void notifyResult(
            final @NonNull ConcurrentHashMap<String, BmtDevice> dsDevices,
            final @Nullable List<BmtListResponse.BmtBeen> list, boolean success) {
        if (dsDevices.size() == 0) {
            return;
        }

        for (BmtDevice dsDevice : dsDevices.values()) {
            BmtListResponse.BmtBeen info = null;
            final String deviceId = dsDevice.getId();
            final String subCategory = dsDevice.getSubCategory();
            if (null != list && list.size() > 0
                    && !TextUtils.isEmpty(deviceId)
                    && !TextUtils.isEmpty(subCategory)) {
                for (int i = 0; i < list.size(); i++) {
                    BmtListResponse.BmtBeen listBean = list.get(i);
                    if (deviceId.equals(listBean.getPid()) && subCategory.equals(listBean.getProvider())) {
                        info = listBean;
                        break;
                    }
                }
            }
            dsDevice.onSearchBmtInfo(info, success);
        }
    }

    public void release() {
        mHandler.removeCallbacksAndMessages(null);
        synchronized (SearchBmtHelper.this) {
            if (loadingMap.size() == 0 && needLoadMap.size() == 0) {
                return;
            }
            final ConcurrentHashMap<String, BmtDevice> devices = new ConcurrentHashMap<>(loadingMap);
            devices.putAll(needLoadMap);
            notifyResult(devices, null, false);

            cancel(false);

            needLoadMap.clear();
            loadingMap.clear();
        }
    }

    /**
     * @param reload 是否需要重新加入等待队列
     */
    private void cancel(final boolean reload) {
        if (null != mSearchIpcCall && !mSearchIpcCall.isCanceled()) {
            if (reload) {
                synchronized (this) {
                    needLoadMap.putAll(loadingMap);
                    loadingMap.clear();
                }
            }

            mSearchIpcCall.cancel();
            mSearchIpcCall = null;
        }
    }

    private SearchBmtHelper() {
    }

    private final static class Holder {
        private static final SearchBmtHelper INSTANCE = new SearchBmtHelper();
    }

    public static SearchBmtHelper get() {
        return Holder.INSTANCE;
    }
}
