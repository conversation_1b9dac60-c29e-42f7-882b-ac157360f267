package com.dinsafer.module_bmt.bean;

import com.dinsafer.dssupport.msctlib.netty.IMultipleSender;
import com.dinsafer.module_bmt.http.bean.BmtListResponse;

/**
 * 三相
 * Author: MiraclesHed
 * Date: 2022/12/7
 */
public class HP5000 extends BmtDevice{
    public HP5000(IMultipleSender sender, String homeID, String pid, BmtCacheInfo.CacheInfo info) {
        super(sender, homeID, pid, BmtConst.PROVIDER_BMT_HP5000, info);
    }

    public HP5000(IMultipleSender sender, String pid, String homeID, String endId, String group_id, Long addtime) {
        super(sender, pid, homeID, endId, group_id, addtime, BmtConst.PROVIDER_BMT_HP5000);
    }

    public HP5000(IMultipleSender sender, String homeID, BmtListResponse.BmtBeen deviceBean) {
        super(sender, homeID, deviceBean, BmtConst.PROVIDER_BMT_HP5000);
    }
}
