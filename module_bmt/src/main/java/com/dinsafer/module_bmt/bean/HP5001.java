package com.dinsafer.module_bmt.bean;

import com.dinsafer.dssupport.msctlib.netty.IMultipleSender;
import com.dinsafer.module_bmt.http.bean.BmtListResponse;

/**
 * 单相
 * Author: MiraclesHed
 * Date: 2022/12/7
 */
public class HP5001 extends BmtDevice {
    public HP5001(IMultipleSender sender, String homeID, String pid, BmtCacheInfo.CacheInfo info) {
        super(sender, homeID, pid, BmtConst.PROVIDER_BMT_HP5001, info);
    }

    public HP5001(IMultipleSender sender, String pid, String homeID, String endId, String group_id, Long addtime) {
        super(sender, pid, homeID, endId, group_id, addtime, BmtConst.PROVIDER_BMT_HP5001);
    }

    public HP5001(IMultipleSender sender, String homeID, BmtListResponse.BmtBeen deviceBean) {
        super(sender, homeID, deviceBean, BmtConst.PROVIDER_BMT_HP5001);
    }
}
