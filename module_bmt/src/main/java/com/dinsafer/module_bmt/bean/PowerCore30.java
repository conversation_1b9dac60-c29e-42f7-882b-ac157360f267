package com.dinsafer.module_bmt.bean;

import com.dinsafer.dssupport.msctlib.netty.IMultipleSender;
import com.dinsafer.module_bmt.http.bean.BmtListResponse;

/**
 * @describe：对应型号 PC3
 * @date：2024/12/5
 * @author: create by Sydnee
 */
public class PowerCore30 extends BmtDevice{
    public PowerCore30(IMultipleSender sender, String homeID, String pid, BmtCacheInfo.CacheInfo bmtInfo) {
        super(sender, homeID, pid, BmtConst.PROVIDER_PC3, bmtInfo);
    }

    public PowerCore30(IMultipleSender sender, String pid, String homeID, String endId, String group_id, Long addtime) {
        super(sender, pid, homeID, endId, group_id, addtime, BmtConst.PROVIDER_PC3);
    }

    public PowerCore30(IMultipleSender sender, String homeID, BmtListResponse.BmtBeen deviceBean) {
        super(sender, homeID, deviceBean, BmtConst.PROVIDER_PC3);
    }
}
