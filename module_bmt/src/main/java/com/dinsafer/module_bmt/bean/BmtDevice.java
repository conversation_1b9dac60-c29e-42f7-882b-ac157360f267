package com.dinsafer.module_bmt.bean;

import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;

import com.dinsafer.dincore.common.Cmd;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.http.NetWorkException;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.utils.MapUtils;
import com.dinsafer.dincore.utils.MyMessageIdHolder;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.msctlib.kcp.IKcpCallBack;
import com.dinsafer.dssupport.msctlib.msct.MsctDataFactory;
import com.dinsafer.dssupport.msctlib.msct.MsctResponse;
import com.dinsafer.dssupport.msctlib.netty.IMultipleSender;
import com.dinsafer.dssupport.msctlib.netty.ISenderCallBack;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.module_bmt.channel.Channel;
import com.dinsafer.module_bmt.channel.ChannelManager;
import com.dinsafer.module_bmt.channel.IChannelCallBack;
import com.dinsafer.module_bmt.channel.ProxyChannel;
import com.dinsafer.module_bmt.cmd.ICmdConverter;
import com.dinsafer.module_bmt.cmd.Mcu;
import com.dinsafer.module_bmt.cmd.MsctCmdReponseConverter;
import com.dinsafer.module_bmt.cmd.MsctCmdRequestConverter;
import com.dinsafer.module_bmt.event.BmtChipsStatusEvent;
import com.dinsafer.module_bmt.event.BmtCountryCodeUpdateEvent;
import com.dinsafer.module_bmt.event.BmtDeviceNameUpdateEvent;
import com.dinsafer.module_bmt.event.BmtExceptionEvent;
import com.dinsafer.module_bmt.event.BmtThirdPartyPVOnEvent;
import com.dinsafer.module_bmt.event.BmtVertStateEvent;
import com.dinsafer.module_bmt.event.EmergencySmartReserveEvent;
import com.dinsafer.module_bmt.event.PvSupplyCustomizationSupportedEvent;
import com.dinsafer.module_bmt.event.RegulateFrequencyStateEvent;
import com.dinsafer.module_bmt.event.GridStatusEvent;
import com.dinsafer.module_bmt.event.IotVersionEvent;
import com.dinsafer.module_bmt.http.BmtApi;
import com.dinsafer.module_bmt.http.bean.BmtBSensorResponse;
import com.dinsafer.module_bmt.http.bean.BmtChargingDischargingPlansResponse;
import com.dinsafer.module_bmt.http.bean.BmtChargingDischargingPlansV2Response;
import com.dinsafer.module_bmt.http.bean.BmtListResponse;
import com.dinsafer.module_bmt.http.bean.BmtLocationResponse;
import com.dinsafer.module_bmt.http.bean.BmtStatEcoResponse;
import com.dinsafer.module_bmt.http.bean.BmtStatsResponse;
import com.dinsafer.module_bmt.http.bean.ElecPriceInfoResponse;
import com.dinsafer.module_bmt.http.bean.GetCityListResponse;
import com.dinsafer.module_bmt.http.bean.GetCountryListResponse;
import com.dinsafer.module_bmt.http.bean.GetDefaultPricePercentResponse;
import com.dinsafer.module_bmt.http.bean.GetFeatureResponse;
import com.dinsafer.module_bmt.http.bean.GetFusePowerCapsResponse;
import com.dinsafer.module_bmt.http.bean.GetIsDualPowerOpenResponse;
import com.dinsafer.module_bmt.http.bean.GetRegionResponse;
import com.dinsafer.module_bmt.http.bean.UpdateRegionResponse;
import com.dinsafer.module_bmt.utils.SearchBmtHelper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.greenrobot.eventbus.EventBus;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * BMT设备类
 */
public abstract class BmtDevice extends Device {
    protected final String TAG = "BmtDevice";

    private boolean isInit;
    //    bmt 的end_id
    protected String endId;
    protected String group_id;
    protected IMultipleSender sender;
    //bmt目前没有用到kcp
    protected IKcpCallBack kcpCallBack;
    protected ISenderCallBack msctCallBack;


    //   bmt属性
    protected String name;
    protected long addtime;
    protected int connectStatus = -1;
    protected String homeID;
    protected int chipsStatus;
    protected List vertBatteryExceptions;
    protected List vertExceptions;
    protected List vertGridExceptions;
    protected List vertSystemExceptions;
    protected List vertmpptExceptions;
    protected List vertPresentExceptions;
    protected List vertDCExceptions;
    protected List evExceptions;
    protected List mpptExceptions;
    protected List cabinetExceptions;
    protected List batteryExceptions;
    protected List systemExceptions;
    protected List communicationExceptions;
    protected boolean thirdpartyPVOn;
    protected List vertStates;
    protected String iotVersion;
    protected String countryCode;
    protected String deliveryArea;
    // 0.未知 1.离网 2.并网
    protected int gridStatus;
    // 调频状态
    protected int regulateFrequencyState;
    // 紧急充电阈值
    protected int emergency_reserve = -1;
    // 智能充电阈值
    protected int smart_reserve = -1;
    // 是否支持自定义PV供应等级
    protected boolean pvSupplyCustomizationSupported;

    //    表示对象是否已经被删除，删除的话，不要回调连接状态变化，重复了
    protected boolean isDelete = false;
//    ipc 属性结束

    protected ChannelManager channelManager;

    private final MyMessageIdHolder messageIdHolder = new MyMessageIdHolder();

    private ICmdConverter<Map, MsctDataFactory> cmdRequestConverter = new MsctCmdRequestConverter(this);
    private ICmdConverter<MsctResponse, Map> cmdResponseConverter = new MsctCmdReponseConverter(this);

    protected ExecutorService executorService = Executors.newCachedThreadPool();

    // 上一次状态, 不直接从info拿是因为中间如果收到异常推送, discardCache为true的话, 拿到的状态会更新为-1
    // 所以定义多个变量来标识
    protected int previousStatus = -1;

    protected IChannelCallBack channelCallBack = new IChannelCallBack() {
        @Override
        public void onConnect() {
            MsctLog.i("test_time", "dincore-connectSuccess");
            if (isDelete) {
                return;
            }
            if (connectStatus == 1) {
                return;
            }
            connectStatus = 1;
            convertToInfo();

            Map result = Cmd.getDefaultResultMap(true, BmtCmd.CONNECT);
            result.put("connect_status", 1);
            dispatchResult(BmtCmd.CONNECT, result);

            Map statusResult = Cmd.getDefaultResultMap(true, BmtCmd.CONNECT_STATUS_CHANGED);
            statusResult.put("connect_status", 1);
            statusResult.put(BmtDataKey.PREVIOUS_STATUS, previousStatus);
            dispatchResult(BmtCmd.CONNECT_STATUS_CHANGED, statusResult);
            dispatchOnline();
            previousStatus = 1;
        }

        @Override
        public void onConnecting() {
            if (isDelete) {
                return;
            }
            if (connectStatus == 0) {
                return;
            }
            Map result = Cmd.getDefaultResultMap(false, BmtCmd.CONNECT);
            result.put("connect_status", 0);
            connectStatus = 0;
            convertToInfo();
            result.put(BmtDataKey.PREVIOUS_STATUS, previousStatus);
            dispatchResult(BmtCmd.CONNECT, result);
            previousStatus = 0;
        }

        @Override
        public void onDisconnect(int code, String msg) {
            if (isDelete) {
                return;
            }
            final int lastConnectStatus = connectStatus;
            connectStatus = -1;
            convertToInfo();
            if (code == ErrorCode.ERROR_CONNECT) {
                Map result = Cmd.getDefaultResultMap(false, BmtCmd.CONNECT);
                result.put("errorMessage", msg);
                result.put("connect_status", -1);
                result.put(BmtDataKey.PREVIOUS_STATUS, previousStatus);
                dispatchResult(BmtCmd.CONNECT, result);
            } else {
                if (lastConnectStatus == -1) {
                    previousStatus = -1;
                    return;
                }
                Map result = Cmd.getDefaultResultMap(true, BmtCmd.CONNECT_STATUS_CHANGED);
                result.put("connect_status", -1);
                result.put(BmtDataKey.PREVIOUS_STATUS, previousStatus);
                dispatchResult(BmtCmd.CONNECT_STATUS_CHANGED, result);
                dispatchOffline(msg);
            }
            previousStatus = -1;

        }
    };

    public BmtDevice(IMultipleSender sender, String homeID, String pid, String subCategory, BmtCacheInfo.CacheInfo bmtInfo) {
        super();
        initFlagOnReadCache();
        this.sender = sender;
        this.setId(pid);
        this.setCategory(3);
        this.setSubCategory(subCategory);
        this.homeID = homeID;
        this.vertBatteryExceptions = bmtInfo.getVertBatteryExceptions();
        this.vertExceptions = bmtInfo.getVertExceptions();
        this.vertGridExceptions = bmtInfo.getVertGridExceptions();
        this.vertSystemExceptions = bmtInfo.getVertSystemExceptions();
        this.vertmpptExceptions = bmtInfo.getVertmpptExceptions();
        this.vertPresentExceptions = bmtInfo.getVertPresentExceptions();
        this.vertDCExceptions = bmtInfo.getVertDCExceptions();
        this.evExceptions = bmtInfo.getEvExceptions();
        this.mpptExceptions = bmtInfo.getMpptExceptions();
        this.cabinetExceptions = bmtInfo.getCabinetExceptions();
        this.batteryExceptions = bmtInfo.getBatteryExceptions();
        this.systemExceptions = bmtInfo.getSystemExceptions();
        this.communicationExceptions = bmtInfo.getCommunicationExceptions();
        this.name = bmtInfo.getName();
        this.chipsStatus = bmtInfo.getChipsStatus();
        this.thirdpartyPVOn = bmtInfo.isThirdpartyPVOn();
        this.vertStates = bmtInfo.getVertStates();
        this.iotVersion = bmtInfo.getIotVersion();
        this.countryCode = bmtInfo.getCountryCode();
        this.deliveryArea = bmtInfo.getDeliveryArea();
        this.gridStatus = bmtInfo.getGridStatus();
        this.regulateFrequencyState = bmtInfo.getRegulateFrequencyState();
        this.emergency_reserve = bmtInfo.getEmergency_reserve();
        this.smart_reserve = bmtInfo.getSmart_reserve();
        this.pvSupplyCustomizationSupported = bmtInfo.isPvSupplyCustomizationSupported();
        if (TextUtils.isEmpty(name)) {
            name = "bmt";
        }
        if (endId == null) {
            endId = "";
        }
        if (group_id == null) {
            group_id = "";
        }
        if (this.homeID == null) {
            this.homeID = "";
        }
        if (this.countryCode == null) {
            this.countryCode = "";
        }
        if (this.deliveryArea == null) {
            this.deliveryArea = "";
        }
        convertToInfo();
        kcpCallBack = createKcpCallBack();
        msctCallBack = createMsctCallBack();
        channelManager = new ChannelManager(this, homeID, kcpCallBack, msctCallBack);
        channelManager.setChannelCallBack(channelCallBack);
    }

    public BmtDevice(IMultipleSender sender, String pid, String homeID, String endId, String group_id, Long addtime, String subCategory) {
        super();
        initFlagOnNetwork();
        this.endId = endId;
        this.group_id = group_id;
        this.addtime = addtime;
        this.homeID = homeID;
        this.sender = sender;
        this.setId(pid);
        this.setCategory(3);
        this.setSubCategory(subCategory);
        if (TextUtils.isEmpty(name)) {
            name = "bmt";
        }
        if (endId == null) {
            endId = "";
        }
        if (group_id == null) {
            group_id = "";
        }
        if (this.homeID == null) {
            this.homeID = "";
        }
        if (this.countryCode == null) {
            this.countryCode = "";
        }
        if (this.deliveryArea == null) {
            this.deliveryArea = "";
        }
        convertToInfo();
        kcpCallBack = createKcpCallBack();
        msctCallBack = createMsctCallBack();
        channelManager = new ChannelManager(this, homeID, kcpCallBack, msctCallBack);
        channelManager.setChannelCallBack(channelCallBack);
    }

    public BmtDevice(IMultipleSender sender, String homeID, BmtListResponse.BmtBeen deviceBean, String subCategory) {
        super();
        initFlagOnNetwork();
        this.sender = sender;
        this.setId(deviceBean.getPid());
        this.setCategory(3);
        this.setSubCategory(subCategory);
        this.name = deviceBean.getName();
        this.endId = deviceBean.getEnd_id();
        this.group_id = deviceBean.getGroup_id();
        this.addtime = deviceBean.getAddtime();
        this.countryCode = deviceBean.hasCountryCode() ? deviceBean.getCountry_code() : "";
        this.deliveryArea = deviceBean.hasDeliveryArea() ? deviceBean.getDelivery_area() : "";
        this.homeID = homeID;
        if (TextUtils.isEmpty(name)) {
            name = "bmt";
        }
        if (endId == null) {
            endId = "";
        }
        if (group_id == null) {
            group_id = "";
        }
        if (this.homeID == null) {
            this.homeID = "";
        }
        if (this.countryCode == null) {
            this.countryCode = "";
        }
        if (this.deliveryArea == null) {
            this.deliveryArea = "";
        }
        convertToInfo();
        kcpCallBack = createKcpCallBack();
        msctCallBack = createMsctCallBack();
        channelManager = new ChannelManager(this, homeID, kcpCallBack, msctCallBack);
        channelManager.setChannelCallBack(channelCallBack);
        postNameUpdateEvent();
    }

    private IKcpCallBack createKcpCallBack() {
        return new IKcpCallBack() {
            @Override
            public void onMessage(int conv, byte[] bytes) {
//                byte[] msgb = bytes;
//                try {
//                    String j = new String(msgb);
//                    MsctLog.v(TAG, "receive decode:" + j);
//                    JSONObject jsonObject = new JSONObject(j);
//                    handlerKcpResponse(jsonObject);
//                } catch (JSONException e) {
//                    e.printStackTrace();
//                }

            }

            @Override
            public void onException(String s) {

            }

            @Override
            public void onClose() {
//                MsctLog.i(TAG, "onClose");
//                disconnect();
            }
        };
    }

    private ISenderCallBack createMsctCallBack() {
        return new ISenderCallBack() {
            @Override
            public void onReceive(MsctResponse msctResponse) {
                handMsctResponse(msctResponse);
            }

            @Override
            public void onDisconnect(String s) {

            }

            @Override
            public void onConnenct() {

            }

            @Override
            public void onReconnect() {

            }
        };
    }

    protected void convertToInfo() {
        Map<String, Object> jsonObject = this.getInfo();
        jsonObject.put("name", name);
        jsonObject.put("networkState", connectStatus);
        jsonObject.put("addtime", this.addtime);
        jsonObject.put("homeId", this.homeID);
        jsonObject.put("chipsStatus", this.chipsStatus);
        jsonObject.put("countryCode", this.countryCode);
        jsonObject.put("deliveryArea", this.deliveryArea);
        if (this.vertBatteryExceptions != null) {
            jsonObject.put(BmtDataKey.VERT_BATTERY_EXCEPTIONS, this.vertBatteryExceptions);
        }
        if (this.vertExceptions != null) {
            jsonObject.put(BmtDataKey.VERT_EXCEPTIONS, this.vertExceptions);
        }
        if (this.vertGridExceptions != null) {
            jsonObject.put(BmtDataKey.VERT_GRID_EXCEPTIONS, this.vertGridExceptions);
        }
        if (this.vertSystemExceptions != null) {
            jsonObject.put(BmtDataKey.VERT_SYSTEM_EXCEPTIONS, this.vertSystemExceptions);
        }
        if (this.vertmpptExceptions != null) {
            jsonObject.put(BmtDataKey.VERT_MPPT_EXCEPTIONS, this.vertmpptExceptions);
        }
        if (this.vertPresentExceptions != null) {
            jsonObject.put(BmtDataKey.VERT_PRESENT_EXCEPTIONS, this.vertPresentExceptions);
        }
        if (this.vertDCExceptions != null) {
            jsonObject.put(BmtDataKey.VERT_DC_EXCEPTIONS, this.vertDCExceptions);
        }
        if (this.evExceptions != null) {
            jsonObject.put("evExceptions", this.evExceptions);
        }
        if (this.mpptExceptions != null) {
            jsonObject.put("mpptExceptions", this.mpptExceptions);
        }
        if (this.cabinetExceptions != null) {
            jsonObject.put("cabinetExceptions", this.cabinetExceptions);
        }
        if (this.batteryExceptions != null) {
            jsonObject.put("batteryExceptions", this.batteryExceptions);
        }
        if (this.systemExceptions != null) {
            jsonObject.put("systemExceptions", this.systemExceptions);
        }
        if (this.communicationExceptions != null) {
            jsonObject.put("communicationExceptions", this.communicationExceptions);
        }
        jsonObject.put(BmtDataKey.THIRD_PARTY_PV_ON, this.thirdpartyPVOn);
        if (this.vertStates != null) {
            jsonObject.put(BmtDataKey.VERT_STATES, this.vertStates);
        }
        if (iotVersion != null) {
            jsonObject.put(BmtDataKey.IOTVERSION, this.iotVersion);
        }
        jsonObject.put(BmtDataKey.GRID_STATUS, this.gridStatus);
        jsonObject.put(BmtDataKey.FREQUENCY_MODULATION_STATUS, this.regulateFrequencyState);
        jsonObject.put(BmtDataKey.EMERGENCY_RESERVE, this.emergency_reserve);
        jsonObject.put(BmtDataKey.SMART_RESERVE, this.smart_reserve);
        jsonObject.put(BmtDataKey.PV_SUPPLY_CUSTOMIZATION_SUPPORTED, this.pvSupplyCustomizationSupported);
    }

    public void setChipsStatus(int chipsStatus) {
        this.chipsStatus = chipsStatus;
        convertToInfo();
        EventBus.getDefault().post(new BmtChipsStatusEvent(getId(), getSubCategory(), chipsStatus));
    }

    public void setInverterExceptions(List<Integer> vertBatteryExceptions, List<Integer> vertExceptions,
                                      List<Integer> vertGridExceptions, List<Integer> vertSystemExceptions,
                                      List<Integer> vertmpptExceptions, List<Integer> vertPresentExceptions, List<Integer> vertDCExceptions) {
        this.vertBatteryExceptions = vertBatteryExceptions;
        this.vertExceptions = vertExceptions;
        this.vertGridExceptions = vertGridExceptions;
        this.vertSystemExceptions = vertSystemExceptions;
        this.vertmpptExceptions = vertmpptExceptions;
        this.vertPresentExceptions = vertPresentExceptions;
        this.vertDCExceptions = vertDCExceptions;
        convertToInfo();
        EventBus.getDefault().post(new BmtExceptionEvent(getId(), getSubCategory(), vertBatteryExceptions, vertExceptions, vertGridExceptions, vertSystemExceptions,
                vertmpptExceptions, vertPresentExceptions, vertDCExceptions));
    }

    public void setEvExceptions(List evExceptions) {
        this.evExceptions = evExceptions;
        convertToInfo();
        EventBus.getDefault().post(new BmtExceptionEvent(getId(), getSubCategory(), 2, evExceptions));
    }

    public void setMpptExceptions(List mpptExceptions) {
        this.mpptExceptions = mpptExceptions;
        convertToInfo();
        EventBus.getDefault().post(new BmtExceptionEvent(getId(), getSubCategory(), 3, mpptExceptions));
    }

    public void setCabinetExceptions(List cabinetExceptions) {
        this.cabinetExceptions = cabinetExceptions;
        convertToInfo();
        EventBus.getDefault().post(new BmtExceptionEvent(getId(), getSubCategory(), 4, cabinetExceptions));
    }

    public void setBatteryExceptions(List batteryExceptions) {
        this.batteryExceptions = batteryExceptions;
        convertToInfo();
        EventBus.getDefault().post(new BmtExceptionEvent(getId(), getSubCategory(), 5, batteryExceptions));
    }

    public void setSystemExceptions(List systemExceptions) {
        this.systemExceptions = systemExceptions;
        convertToInfo();
        EventBus.getDefault().post(new BmtExceptionEvent(getId(), getSubCategory(), 6, systemExceptions));
    }

    public void setCommunicationExceptions(List communicationExceptions) {
        this.communicationExceptions = communicationExceptions;
        convertToInfo();
        EventBus.getDefault().post(new BmtExceptionEvent(getId(), getSubCategory(), 7, communicationExceptions));
    }

    public void setExceptions(List<Integer> vertBatteryExceptions, List<Integer> vertExceptions,
                              List<Integer> vertGridExceptions, List<Integer> vertSystemExceptions,
                              List<Integer> vertmpptExceptions, List<Integer> vertPresentExceptions, List<Integer> vertDCExceptions,
                              List evExceptions, List mpptExceptions, List cabinetExceptions, List batteryExceptions, List systemExceptions, List communicationExceptions) {
        this.vertBatteryExceptions = vertBatteryExceptions;
        this.vertExceptions = vertExceptions;
        this.vertGridExceptions = vertGridExceptions;
        this.vertSystemExceptions = vertSystemExceptions;
        this.vertmpptExceptions = vertmpptExceptions;
        this.vertPresentExceptions = vertPresentExceptions;
        this.vertDCExceptions = vertDCExceptions;
        this.evExceptions = evExceptions;
        this.mpptExceptions = mpptExceptions;
        this.cabinetExceptions = cabinetExceptions;
        this.batteryExceptions = batteryExceptions;
        this.systemExceptions = systemExceptions;
        this.communicationExceptions = communicationExceptions;
        convertToInfo();
        EventBus.getDefault().post(new BmtExceptionEvent(getId(), getSubCategory(), vertBatteryExceptions, vertExceptions, vertGridExceptions, vertSystemExceptions,
                vertmpptExceptions, vertPresentExceptions, vertDCExceptions, evExceptions,
                mpptExceptions, cabinetExceptions, batteryExceptions, systemExceptions, communicationExceptions));
    }

    public void setThirdpartyPVOn(boolean thirdpartyPVOn) {
        this.thirdpartyPVOn = thirdpartyPVOn;
        convertToInfo();
        EventBus.getDefault().post(new BmtThirdPartyPVOnEvent(getId(), getSubCategory(), thirdpartyPVOn));
    }

    public synchronized void setVertState(int index, int vertState) {
        if (vertStates == null) {
            vertStates = new ArrayList();
            for (int i = 0; i < 3; i++) {
                vertStates.add(Mcu.Inverter.InverterState.Working.getCode());
            }
        }
        vertStates.set(index, vertState);
        convertToInfo();
        EventBus.getDefault().post(new BmtVertStateEvent(getId(), getSubCategory(), vertStates));
    }

    public void setIotVersion(String iotVersion) {
        this.iotVersion = iotVersion;
        convertToInfo();
        EventBus.getDefault().post(new IotVersionEvent(getId(), getSubCategory(), iotVersion));
    }

    public void setGridStatus(int gridStatus) {
        this.gridStatus = gridStatus;
        convertToInfo();
        EventBus.getDefault().post(new GridStatusEvent(getId(), getSubCategory(), gridStatus));
    }

    public void setRegulateFrequencyState(int regulateFrequencyState) {
        this.regulateFrequencyState = regulateFrequencyState;
        convertToInfo();
        EventBus.getDefault().post(new RegulateFrequencyStateEvent(getId(), getSubCategory(), regulateFrequencyState));
    }

    public void setPvSupplyCustomizationSupported (boolean pvSupplyCustomizationSupported) {
        this.pvSupplyCustomizationSupported = pvSupplyCustomizationSupported;
        convertToInfo();
        EventBus.getDefault().post(new PvSupplyCustomizationSupportedEvent(getId(), getSubCategory(), pvSupplyCustomizationSupported));
    }

    @Override
    public void submit(Map arg) {
        String cmd = (String) arg.get("cmd");
        switch (cmd) {
            case BmtCmd.SET_NAME:
                setName((String) arg.get("name"));
                break;
            case BmtCmd.DELETE:
                delete();
                break;
            case BmtCmd.CONNECT:
                setGridStatus(0);
                final boolean discardCache = DeviceHelper.getBoolean(arg, "discardCache", true);
                MsctLog.i("test_time", "dincore-BmtCmd.CONNECT");
                connect(discardCache);
                break;
            case BmtCmd.DISCONNECT:
                disconnect();
                break;
            case BmtCmd.GET_REGION_LIST:
                getRegionList(arg);
                break;
            case BmtCmd.UPDATE_REGION:
                updateRegion(arg);
                break;
            case BmtCmd.GET_REGION:
                getRegion(arg);
                break;
            case BmtCmd.GET_REGION_COUNTRIES:
                getRegionCountriesList();
                break;

            case BmtCmd.GET_STATS_LOADUSAGE:
            case BmtCmd.GET_STATS_BATTERY:
            case BmtCmd.GET_STATS_MPPT:
            case BmtCmd.GET_STATS_BATTERY_POWERLEVEL:
            case BmtCmd.GET_STATS_REVENUE:
            case BmtCmd.GET_STATS_LOADUSAGE_V2:
            case BmtCmd.GET_STATS_BATTERY_V2:
            case BmtCmd.GET_STATS_MPPT_V2:
            case BmtCmd.GET_STATS_REVENUE_V2:
                getBmtStats(cmd, arg);
                break;

            case BmtCmd.GET_STATS_GRID:
                getBmtGridStats(cmd,arg);
                break;

            case BmtCmd.GET_ELEC_PRICE_INFO:
                getElectPriceInfo(cmd, arg);
                break;

            case BmtCmd.GET_STATS_ECO:
                getBmtStatsEco(arg, 1);
                break;

            case BmtCmd.GET_BSENSOR_STATUS:
                getBmtBSenSorStatus();
                break;
            case BmtCmd.BIND_INVERTER:
                bindInverter(arg);
                break;

            case BmtCmd.GET_FUSE_POWER_CAPS:
                getFusePowerCaps(arg);
                break;

            case BmtCmd.GET_FEATURE:
                getFeature();
                break;

            case BmtCmd.GET_DUALPOWER_OPEN:
                getDualPowerOpen();
                break;

            case BmtCmd.GET_STATS_ECO_V2:
                getBmtStatsEco(arg, 2);
                break;

            case BmtCmd.GET_CHARGING_DISCHARGING_PLANS:
                getChargingDischargingPlans(arg);
                break;

            case BmtCmd.GET_CHARGING_DISCHARGING_PLANS_V2:
                getChargingDischargingPlansV2();
                break;

            case BmtCmd.GET_AIMODE_SETTINGS:
                getAIModeSettings();
                break;

            case BmtCmd.GET_LOCATION:
                getBmtLocation();
                break;

            default: {
                MsctDataFactory convert = cmdRequestConverter.convert(arg);
                if (convert != null) {
                    sendMsct(convert);
                } else {
                    Map result;
                    if (cmd.equals(BmtCmd.GET_CHIPS_STATUS) || cmd.equals(BmtCmd.UPDATE_CHIPS) || cmd.equals(BmtCmd.GET_CHIPS_UPDATE_PROGRESS)) {
                        result = Cmd.getOfflineFailResultMap(cmd);
                    } else {
                        result = Cmd.getDefaultResultMap(false, null);
                    }
                    result.put("errorMessage", "cmd:" + cmd + " not support");
                    dispatchResult(cmd, result);
                }
            }

        }
    }

    private void getBmtLocation() {
        BmtApi.getInstance().getBmtLocation(homeID, getId(), getSubCategory())
                .enqueue(new Callback<BmtLocationResponse>() {
                    @Override
                    public void onResponse(Call<BmtLocationResponse> call, Response<BmtLocationResponse> response) {
                        BmtLocationResponse body = response.body();
                        Map resultMap;
                        if (body != null && body.getResult() != null) {
                            BmtLocationResponse.ResultBean resultBean = body.getResult();
                            if (resultBean != null) {
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_LOCATION);
                                Cmd.putResultValue(resultMap, BmtDataKey.LATITUDE, resultBean.getLatitude());
                                Cmd.putResultValue(resultMap, BmtDataKey.LONGITUDE, resultBean.getLongitude());
                            } else {
                                resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_LOCATION);
                                resultMap.put("errorMessage", "errorMessage, response null");
                            }
                        } else {
                            resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_LOCATION);
                            resultMap.put("errorMessage", "errorMessage, response null");
                        }
                        dispatchResult(BmtCmd.GET_LOCATION, resultMap);
                    }

                    @Override
                    public void onFailure(Call<BmtLocationResponse> call, Throwable t) {
                        t.printStackTrace();
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_LOCATION);
                        resultMap.put("errorMessage", t.getMessage());
                        dispatchResult(BmtCmd.GET_LOCATION, resultMap);
                    }
                });
    }

    /**
     * 获取电价默认值/阈值（AI模式）
     */
    private void getAIModeSettings() {
        BmtApi.getInstance().getDefaultPricePercent(homeID, getId(), getSubCategory())
                .enqueue(new Callback<GetDefaultPricePercentResponse>() {
                    @Override
                    public void onResponse(Call<GetDefaultPricePercentResponse> call, Response<GetDefaultPricePercentResponse> response) {
                        GetDefaultPricePercentResponse body = response.body();
                        Map resultMap;
                        if (body != null && body.getResult() != null) {
                            GetDefaultPricePercentResponse.ResultBean resultBean = body.getResult();
                            resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_AIMODE_SETTINGS);
                            Cmd.putResultValue(resultMap, BmtDataKey.C_1, resultBean.getC1());
                            Cmd.putResultValue(resultMap, BmtDataKey.C_2, resultBean.getC2());
                            Cmd.putResultValue(resultMap, BmtDataKey.C_3, resultBean.getC3());
                            Cmd.putResultValue(resultMap, BmtDataKey.S_1, resultBean.getS1());
                            Cmd.putResultValue(resultMap, BmtDataKey.S_2, resultBean.getS2());
                            Cmd.putResultValue(resultMap, BmtDataKey.EMERGENCY_RESERVE, resultBean.getSmart_reserve());
                            Cmd.putResultValue(resultMap, BmtDataKey.SMART_RESERVE, resultBean.getSmart_reserve());
                            emergency_reserve = resultBean.getEmergency_reserve();
                            smart_reserve = resultBean.getSmart_reserve();
                            convertToInfo();
                            EventBus.getDefault().post(new EmergencySmartReserveEvent(getId(), getSubCategory(),
                                    emergency_reserve, smart_reserve));
                        } else {
                            resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_AIMODE_SETTINGS);
                            resultMap.put("errorMessage", "errorMessage, response null");
                        }
                        dispatchResult(BmtCmd.GET_AIMODE_SETTINGS, resultMap);
                    }

                    @Override
                    public void onFailure(Call<GetDefaultPricePercentResponse> call, Throwable t) {
                        t.printStackTrace();
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_AIMODE_SETTINGS);
                        resultMap.put("errorMessage", t.getMessage());
                        dispatchResult(BmtCmd.GET_AIMODE_SETTINGS, resultMap);
                    }
                });
    }

    /**
     * 获取电池充放电策略
     *
     * @param arg
     */
    private void getChargingDischargingPlans(Map arg) {
        int offset = DeviceHelper.getInt(arg, BmtDataKey.OFFSET, 0);
        BmtApi.getInstance().getChargingDischargingPlans(homeID, getId(), getSubCategory(), offset)
                .enqueue(new Callback<BmtChargingDischargingPlansResponse>() {
                    @Override
                    public void onResponse(Call<BmtChargingDischargingPlansResponse> call, Response<BmtChargingDischargingPlansResponse> response) {
                        BmtChargingDischargingPlansResponse body = response.body();
                        Map resultMap;
                        if (body != null && body.getResult() != null) {
                            resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_CHARGING_DISCHARGING_PLANS);
                            BmtChargingDischargingPlansResponse.ResultBean resultBean = body.getResult();
                            Cmd.putResultValue(resultMap, BmtDataKey.C_1, resultBean.getC1());
                            Cmd.putResultValue(resultMap, BmtDataKey.C_2, resultBean.getC2());
                            Cmd.putResultValue(resultMap, BmtDataKey.C_3, resultBean.getC3());
                            Cmd.putResultValue(resultMap, BmtDataKey.S_1, resultBean.getS1());
                            Cmd.putResultValue(resultMap, BmtDataKey.S_2, resultBean.getS2());
                            Cmd.putResultValue(resultMap, BmtDataKey.SMART, resultBean.getSmart());
                            Cmd.putResultValue(resultMap, BmtDataKey.EMERGENCY, resultBean.getEmergency());
                            Cmd.putResultValue(resultMap, BmtDataKey.ELECTRICITY_PRICE_PERCENTS, resultBean.getElectricity_price_percents());
                            Cmd.putResultValue(resultMap, BmtDataKey.FORECAST_SOLARS, resultBean.getForecast_solars());
                            Cmd.putResultValue(resultMap, BmtDataKey.HOPE_CHARGE_DISCHARGES, resultBean.getHope_charge_discharges());
                            Cmd.putResultValue(resultMap, BmtDataKey.PLANS, resultBean.getPlans());
                            Cmd.putResultValue(resultMap, BmtDataKey.WEATHER_CONDITIONS, resultBean.getWeather_conditions());
                            Cmd.putResultValue(resultMap, BmtDataKey.GMTTIME, resultBean.getGmtime());
                            Cmd.putResultValue(resultMap, BmtDataKey.START_TIME, resultBean.getStart_time());
                            Cmd.putResultValue(resultMap, BmtDataKey.TIMEZONE, resultBean.getTimezone());
                        } else {
                            resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CHARGING_DISCHARGING_PLANS);
                            resultMap.put("errorMessage", "errorMessage, response null");
                        }
                        dispatchResult(BmtCmd.GET_CHARGING_DISCHARGING_PLANS, resultMap);
                    }

                    @Override
                    public void onFailure(Call<BmtChargingDischargingPlansResponse> call, Throwable t) {
                        t.printStackTrace();
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CHARGING_DISCHARGING_PLANS);
                        resultMap.put("errorMessage", t.getMessage());
                        dispatchResult(BmtCmd.GET_CHARGING_DISCHARGING_PLANS, resultMap);
                    }
                });
    }

    private void getChargingDischargingPlansV2() {
        BmtApi.getInstance().getChargingDischargingPlansV2(homeID, getId(), getSubCategory())
                .enqueue(new Callback<BmtChargingDischargingPlansV2Response>() {
                    @Override
                    public void onResponse(Call<BmtChargingDischargingPlansV2Response> call, Response<BmtChargingDischargingPlansV2Response> response) {
                        BmtChargingDischargingPlansV2Response body = response.body();
                        Map resultMap;
                        if (body != null && body.getResult() != null) {
                            resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_CHARGING_DISCHARGING_PLANS_V2);
                            BmtChargingDischargingPlansV2Response.ResultBean resultBean = body.getResult();
                            Cmd.putResultValue(resultMap, BmtDataKey.ABS_ZERO_EC_PRICES, resultBean.getAbs_zero_ec_prices());
                            Cmd.putResultValue(resultMap, BmtDataKey.C_1, resultBean.getC1());
                            Cmd.putResultValue(resultMap, BmtDataKey.C_2, resultBean.getC2());
                            Cmd.putResultValue(resultMap, BmtDataKey.C_3, resultBean.getC3());
                            Cmd.putResultValue(resultMap, BmtDataKey.CONDITIONS, resultBean.getConditions());
                            Cmd.putResultValue(resultMap, BmtDataKey.EMERGENCY, resultBean.getEmergency());
                            Cmd.putResultValue(resultMap, BmtDataKey.FORECAST_SOLARS, resultBean.getForecast_solars());
                            Cmd.putResultValue(resultMap, BmtDataKey.GMTTIME, resultBean.getGmtime());
                            Cmd.putResultValue(resultMap, BmtDataKey.HOPE_CHARGE_DISCHARGES, resultBean.getHope_charge_discharges());
                            Cmd.putResultValue(resultMap, BmtDataKey.MARKET_PRICES, resultBean.getMarket_prices());
                            Cmd.putResultValue(resultMap, BmtDataKey.PLANS, resultBean.getPlans());
                            Cmd.putResultValue(resultMap, BmtDataKey.RELATIVE_PRICE_NORMS, resultBean.getRelative_price_norms());
                            Cmd.putResultValue(resultMap, BmtDataKey.S_1, resultBean.getS1());
                            Cmd.putResultValue(resultMap, BmtDataKey.S_2, resultBean.getS2());
                            Cmd.putResultValue(resultMap, BmtDataKey.SMART, resultBean.getSmart());
                            Cmd.putResultValue(resultMap, BmtDataKey.START_TIME, resultBean.getStart_time());
                            Cmd.putResultValue(resultMap, BmtDataKey.SUNRISES, resultBean.getSunrises());
                            Cmd.putResultValue(resultMap, BmtDataKey.SUNSETS, resultBean.getSunsets());
                            Cmd.putResultValue(resultMap, BmtDataKey.TIMEZONE, resultBean.getTimezone());
                            Cmd.putResultValue(resultMap, BmtDataKey.UNIT_PRICE, resultBean.getUnit_price());
                            Cmd.putResultValue(resultMap, BmtDataKey.USER_PRICES, resultBean.getUser_prices());
                        } else {
                            resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CHARGING_DISCHARGING_PLANS_V2);
                            resultMap.put("errorMessage", "errorMessage, response null");
                        }
                        dispatchResult(BmtCmd.GET_CHARGING_DISCHARGING_PLANS_V2, resultMap);
                    }

                    @Override
                    public void onFailure(Call<BmtChargingDischargingPlansV2Response> call, Throwable t) {
                        t.printStackTrace();
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CHARGING_DISCHARGING_PLANS_V2);
                        resultMap.put("errorMessage", t.getMessage());
                        dispatchResult(BmtCmd.GET_CHARGING_DISCHARGING_PLANS_V2, resultMap);
                    }
                });
    }

    private void getDualPowerOpen() {
        BmtApi.getInstance().getIsDualPowerOpen(homeID, getId())
                .enqueue(new Callback<GetIsDualPowerOpenResponse>() {
                    @Override
                    public void onResponse(Call<GetIsDualPowerOpenResponse> call, Response<GetIsDualPowerOpenResponse> response) {
                        GetIsDualPowerOpenResponse body = response.body();
                        Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_DUALPOWER_OPEN);
                        if (body != null && body.getResult() != null) {
                            Cmd.putResultValue(resultMap, BmtDataKey.IS_OPEN, body.getResult().isIs_open());
                        } else {
                            resultMap.put("errorMessage", "response null");
                        }
                        dispatchResult(BmtCmd.GET_DUALPOWER_OPEN, resultMap);
                    }

                    @Override
                    public void onFailure(Call<GetIsDualPowerOpenResponse> call, Throwable t) {
                        t.printStackTrace();
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_DUALPOWER_OPEN);
                        resultMap.put("errorMessage", t.getMessage());
                        dispatchResult(BmtCmd.GET_DUALPOWER_OPEN, resultMap);
                    }
                });
    }


    public void onReceiveResetAck() {
        reboot();
//        deleteDirect(false);
        delete();
    }

    private void reboot() {
        final Map<String, Object> data = new HashMap<>();
        data.put("cmd", BmtCmd.RESET_DEVICE_DATA);
        submit(data);
    }

    /**
     * 获取设备功能开关
     */
    private void getFeature() {
        BmtApi.getInstance().getFeature(homeID, getId(), getSubCategory())
                .enqueue(new Callback<GetFeatureResponse>() {
                    @Override
                    public void onResponse(Call<GetFeatureResponse> call, Response<GetFeatureResponse> response) {
                        GetFeatureResponse body = response.body();
                        Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_FEATURE);
                        if (body != null && body.getResult() != null) {
                            Cmd.putResultValue(resultMap, BmtDataKey.CITYS, body.getResult().getCitys());
                            Cmd.putResultValue(resultMap, BmtDataKey.COUNTRY_CODE, body.getResult().getCountry_code());
                            Cmd.putResultValue(resultMap, BmtDataKey.COUNTRY_NAME, body.getResult().getCountry_name());
                            Cmd.putResultValue(resultMap, BmtDataKey.ELEC_SUPPORT, body.getResult().isElec_support());
                            Cmd.putResultValue(resultMap, BmtDataKey.GRID_CONN_SUPPORT, body.getResult().isGrid_conn_support());
                            Cmd.putResultValue(resultMap, BmtDataKey.GRID_TO_BATTERY, body.getResult().isGrid_to_battery());
                            Cmd.putResultValue(resultMap, BmtDataKey.TIMEZONE, body.getResult().getTimezone());
                        } else {
                            resultMap.put("errorMessage", "response null");
                        }
                        dispatchResult(BmtCmd.GET_FEATURE, resultMap);
                    }

                    @Override
                    public void onFailure(Call<GetFeatureResponse> call, Throwable t) {
                        t.printStackTrace();
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_FEATURE);
                        resultMap.put("errorMessage", t.getMessage());
                        dispatchResult(BmtCmd.GET_FEATURE, resultMap);
                    }
                });
    }

    private void getBmtBSenSorStatus() {
        BmtApi.getInstance().getBmtBSensorStatus(homeID, getId(), getSubCategory())
                .enqueue(new Callback<BmtBSensorResponse>() {
                    @Override
                    public void onResponse(Call<BmtBSensorResponse> call, Response<BmtBSensorResponse> response) {
                        BmtBSensorResponse body = response.body();
                        Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_BSENSOR_STATUS);
                        if (body != null && body.getResult() != null) {
                            Cmd.putResultValue(resultMap, BmtDataKey.B_SENSOR_INSTALLED, body.getResult().getB_sensor_installed());
                        } else {
                            resultMap.put("errorMessage", "response null");
                        }
                        dispatchResult(BmtCmd.GET_BSENSOR_STATUS, resultMap);
                    }

                    @Override
                    public void onFailure(Call<BmtBSensorResponse> call, Throwable t) {
                        t.printStackTrace();
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_BSENSOR_STATUS);
                        resultMap.put("errorMessage", t.getMessage());
                        dispatchResult(BmtCmd.GET_BSENSOR_STATUS, resultMap);
                    }
                });
    }

    /**
     * 获取环保评级
     *
     * @param arg
     */
    private void getBmtStatsEco(Map arg, int version) {
        String interval = (String) MapUtils.get(arg, BmtDataKey.INTERVAL, "");
        int offset = (int) MapUtils.get(arg, BmtDataKey.OFFSET, 0);
        BmtApi.getInstance().getBmtStatEco(homeID, getId(), getSubCategory(), offset, interval, version)
                .enqueue(new Callback<BmtStatEcoResponse>() {
                    @Override
                    public void onResponse(Call<BmtStatEcoResponse> call, Response<BmtStatEcoResponse> response) {
                        BmtStatEcoResponse body = response.body();
                        Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_STATS_ECO);
                        if (body != null && body.getResult() != null) {
                            Cmd.putResultValue(resultMap, BmtDataKey.DATA, body.getResult().getData());
                            Cmd.putResultValue(resultMap, BmtDataKey.GMTTIME, body.getResult().getGmtime());
                            Cmd.putResultValue(resultMap, BmtDataKey.INTERVAL, body.getResult().getInterval());
                            Cmd.putResultValue(resultMap, BmtDataKey.START_TIME, body.getResult().getStart_time());
                            Cmd.putResultValue(resultMap, BmtDataKey.TIMEZONE, body.getResult().getTimezone());
                            Cmd.putResultValue(resultMap, BmtDataKey.B_SENSOR_INSTALLED, body.getResult().getB_sensor_installed());
                        } else {
                            resultMap.put("errorMessage", "response null");
                        }
                        dispatchResult(BmtCmd.GET_STATS_ECO, resultMap);
                    }

                    @Override
                    public void onFailure(Call<BmtStatEcoResponse> call, Throwable t) {
                        t.printStackTrace();
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_STATS_ECO);
                        resultMap.put("errorMessage", t.getMessage());
                        dispatchResult(BmtCmd.GET_STATS_ECO, resultMap);
                    }
                });
    }

    /**
     * bmt 统计数据
     *
     * @param cmd
     * @param arg
     */
    private void getBmtStats(String cmd, Map arg) {
        String interval = (String) MapUtils.get(arg, BmtDataKey.INTERVAL, "");
        int offset = (int) MapUtils.get(arg, BmtDataKey.OFFSET, 0);
        BmtApi.getInstance().getBmtStats(homeID, getId(), getSubCategory(), offset, cmd, interval)
                .enqueue(new Callback<BmtStatsResponse>() {
                    @Override
                    public void onResponse(Call<BmtStatsResponse> call, Response<BmtStatsResponse> response) {
                        BmtStatsResponse body = response.body();
                        Map resultMap = Cmd.getDefaultResultMap(true, cmd);
                        if (body != null && body.getResult() != null) {
                            Cmd.putResultValue(resultMap, BmtDataKey.OFFSET, offset);
                            Cmd.putResultValue(resultMap, BmtDataKey.DATA, body.getResult().getData());
                            Cmd.putResultValue(resultMap, BmtDataKey.GMTTIME, body.getResult().getGmtime());
                            Cmd.putResultValue(resultMap, BmtDataKey.INTERVAL, body.getResult().getInterval());
                            Cmd.putResultValue(resultMap, BmtDataKey.START_TIME, body.getResult().getStart_time());
                            Cmd.putResultValue(resultMap, BmtDataKey.TIMEZONE, body.getResult().getTimezone());
                            Cmd.putResultValue(resultMap, BmtDataKey.B_SENSOR_INSTALLED, body.getResult().getB_sensor_installed());
                            Cmd.putResultValue(resultMap, BmtDataKey.CYCLE_TYPE, interval);
                        } else {
                            resultMap.put("errorMessage", "response null");
                        }
                        dispatchResult(cmd, resultMap);
                    }

                    @Override
                    public void onFailure(Call<BmtStatsResponse> call, Throwable t) {
                        t.printStackTrace();
                        Map resultMap = Cmd.getDefaultResultMap(false, cmd);
                        resultMap.put("errorMessage", t.getMessage());
                        dispatchResult(cmd, resultMap);
                    }
                });
    }

    private void getBmtGridStats(String cmd, Map arg) {
        String interval = (String) MapUtils.get(arg, BmtDataKey.INTERVAL, "");
        int queryInterval = (int) MapUtils.get(arg, BmtDataKey.QUERY_INTERVAL, 0);
        boolean getReal = (boolean) MapUtils.get(arg, BmtDataKey.GET_REAL, false);
        int offset = (int) MapUtils.get(arg, BmtDataKey.OFFSET, 0);
        BmtApi.getInstance().getBmtGridStats(homeID, getId(), getSubCategory(), offset, cmd, interval,queryInterval,getReal)
                .enqueue(new Callback<BmtStatsResponse>() {
                    @Override
                    public void onResponse(Call<BmtStatsResponse> call, Response<BmtStatsResponse> response) {
                        BmtStatsResponse body = response.body();
                        Map resultMap = Cmd.getDefaultResultMap(true, cmd);
                        if (body != null && body.getResult() != null) {
                            Cmd.putResultValue(resultMap, BmtDataKey.OFFSET, offset);
                            Cmd.putResultValue(resultMap, BmtDataKey.DATA, body.getResult().getData());
                            Cmd.putResultValue(resultMap, BmtDataKey.GMTTIME, body.getResult().getGmtime());
                            Cmd.putResultValue(resultMap, BmtDataKey.INTERVAL, body.getResult().getInterval());
                            Cmd.putResultValue(resultMap, BmtDataKey.START_TIME, body.getResult().getStart_time());
                            Cmd.putResultValue(resultMap, BmtDataKey.TIMEZONE, body.getResult().getTimezone());
                            Cmd.putResultValue(resultMap, BmtDataKey.B_SENSOR_INSTALLED, body.getResult().getB_sensor_installed());
                            Cmd.putResultValue(resultMap, BmtDataKey.CYCLE_TYPE, interval);
                        } else {
                            resultMap.put("errorMessage", "response null");
                        }
                        dispatchResult(cmd, resultMap);
                    }

                    @Override
                    public void onFailure(Call<BmtStatsResponse> call, Throwable t) {
                        t.printStackTrace();
                        Map resultMap = Cmd.getDefaultResultMap(false, cmd);
                        resultMap.put("errorMessage", t.getMessage());
                        dispatchResult(cmd, resultMap);
                    }
                });
    }

    /**
     * [APP] 获取电价信息
     *
     * @param cmd
     * @param arg
     */
    private void getElectPriceInfo(String cmd, Map arg) {
        int offset = (int) MapUtils.get(arg, BmtDataKey.OFFSET, 0);
        BmtApi.getInstance().getElecPriceInfo(homeID, getId(), getSubCategory(), offset)
                .enqueue(new Callback<ElecPriceInfoResponse>() {
                    @Override
                    public void onResponse(Call<ElecPriceInfoResponse> call, Response<ElecPriceInfoResponse> response) {
                        ElecPriceInfoResponse body = response.body();
                        Map resultMap = Cmd.getDefaultResultMap(true, cmd);
                        if (body != null && body.getResult() != null) {
                            Cmd.putResultValue(resultMap, BmtDataKey.OFFSET, offset);
                            Cmd.putResultValue(resultMap, BmtDataKey.DATA, body.getResult().getData());
                            Cmd.putResultValue(resultMap, BmtDataKey.START_TIME, body.getResult().getStart_time());
                            Cmd.putResultValue(resultMap, BmtDataKey.TIMEZONE, body.getResult().getTimezone());
                            Cmd.putResultValue(resultMap, BmtDataKey.UNIT, body.getResult().getUnit());
                            Cmd.putResultValue(resultMap, BmtDataKey.UNIT_APP, body.getResult().getUnit_app());
                        } else {
                            resultMap.put("errorMessage", "response null");
                        }
                        dispatchResult(cmd, resultMap);
                    }

                    @Override
                    public void onFailure(Call<ElecPriceInfoResponse> call, Throwable t) {
                        t.printStackTrace();
                        Map resultMap = Cmd.getDefaultResultMap(false, cmd);
                        resultMap.put("errorMessage", t.getMessage());
                        dispatchResult(cmd, resultMap);
                    }
                });
    }


    private void getRegion(Map arg) {
        BmtApi.getInstance().getRegion(homeID, getId(), getSubCategory())
                .enqueue(new Callback<GetRegionResponse>() {
                    @Override
                    public void onResponse(Call<GetRegionResponse> call, Response<GetRegionResponse> response) {
                        GetRegionResponse body = response.body();
                        if (body != null && body.getResult() != null) {
                            Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_REGION);
                            Cmd.putResultValue(resultMap, "region", body.getResult().getRegion());
                            Cmd.putResultValue(resultMap, BmtDataKey.DELIVERY_AREAS, body.getResult().getDelivery_area());
                            Cmd.putResultValue(resultMap, BmtDataKey.GRID_CONN_SUPPORT, body.getResult().isGrid_conn_support());
                            Cmd.putResultValue(resultMap, "smart_tariff_tracking", body.getResult().isSmart_tariff_tracking());
                            dispatchResult(BmtCmd.GET_REGION, resultMap);
                        } else {
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_REGION);
                            resultMap.put("errorMessage", "response null");
                            dispatchResult(BmtCmd.GET_REGION, resultMap);
                        }
                    }

                    @Override
                    public void onFailure(Call<GetRegionResponse> call, Throwable t) {
                        t.printStackTrace();
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_REGION);
                        resultMap.put("errorMessage", t.getMessage());
                        dispatchResult(BmtCmd.GET_REGION, resultMap);
                    }
                });

    }

    private void updateRegion(Map arg) {
        String country_code = (String) MapUtils.get(arg, "country_code", "");
        String country_name = (String) MapUtils.get(arg, "country_name", "");
        String city_name = (String) MapUtils.get(arg, "city_name", "");
        String timezone = (String) MapUtils.get(arg, "timezone", "");
        if (TextUtils.isEmpty(country_code) || TextUtils.isEmpty(country_name) || TextUtils.isEmpty(city_name) || TextUtils.isEmpty(timezone)) {
            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.UPDATE_REGION);
            resultMap.put("errorMessage", "parameter [country_code/country_name/city_name/timezone] is empty");
            dispatchResult(BmtCmd.UPDATE_REGION, resultMap);
            return;
        }

        BmtApi.getInstance().updateRegion(homeID, getId(), getSubCategory(), country_code, country_name, city_name, timezone)
                .enqueue(new Callback<UpdateRegionResponse>() {
                    @Override
                    public void onResponse(Call<UpdateRegionResponse> call, Response<UpdateRegionResponse> response) {
                        UpdateRegionResponse body = response.body();
                        if (body != null && body.getResult() != null) {
                            Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.UPDATE_REGION);
                            Cmd.putResultValue(resultMap, "band", body.getResult().getBand());
                            Cmd.putResultValue(resultMap, "ignore", body.getResult().isIgnore());
                            dispatchResult(BmtCmd.UPDATE_REGION, resultMap);
                        } else {
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.UPDATE_REGION);
                            resultMap.put("errorMessage", "response null");
                            dispatchResult(BmtCmd.UPDATE_REGION, resultMap);
                        }
                    }

                    @Override
                    public void onFailure(Call<UpdateRegionResponse> call, Throwable t) {
                        t.printStackTrace();
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.UPDATE_REGION);
                        resultMap.put("errorMessage", t.getMessage());
                        dispatchResult(BmtCmd.UPDATE_REGION, resultMap);
                    }
                });

    }

    private void getFusePowerCaps(Map arg) {
        BmtApi.getInstance().getFusePowerCaps().enqueue(new Callback<GetFusePowerCapsResponse>() {
            @Override
            public void onResponse(Call<GetFusePowerCapsResponse> call, Response<GetFusePowerCapsResponse> response) {
                GetFusePowerCapsResponse body = response.body();
                Map resultMap;
                if (body != null && body.getResult() != null) {
                    resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_FUSE_POWER_CAPS);
                    Gson gson = new Gson();
                    Type typeToken = new TypeToken<Map<String, Object>>() {
                    }.getType();
                    Map result = gson.fromJson(gson.toJson(body.getResult()), typeToken);
                    resultMap.put("result", result);
                } else {
                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_FUSE_POWER_CAPS);
                    resultMap.put("errorMessage", "response null");
                }
                dispatchResult(BmtCmd.GET_FUSE_POWER_CAPS, resultMap);
            }

            @Override
            public void onFailure(Call<GetFusePowerCapsResponse> call, Throwable t) {
                t.printStackTrace();
                Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_FUSE_POWER_CAPS);
                resultMap.put("errorMessage", t.getMessage());
                dispatchResult(BmtCmd.GET_FUSE_POWER_CAPS, resultMap);
            }
        });
    }

    private void getRegionList(Map arg) {
        BmtApi.getInstance().getCityList().enqueue(new Callback<GetCityListResponse>() {
            @Override
            public void onResponse(Call<GetCityListResponse> call, Response<GetCityListResponse> response) {
                GetCityListResponse body = response.body();
                if (body != null && body.getResult() != null && body.getResult().getList() != null) {
                    Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_REGION_LIST);
                    Gson gson = new Gson();
                    Type typeToken = new TypeToken<Map<String, Object>>() {
                    }.getType();
                    Map result = gson.fromJson(gson.toJson(body.getResult()), typeToken);
                    resultMap.put("result", result);
                    dispatchResult(BmtCmd.GET_REGION_LIST, resultMap);
                } else {
                    Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_REGION_LIST);
                    resultMap.put("errorMessage", "response null");
                    dispatchResult(BmtCmd.GET_REGION_LIST, resultMap);
                }
            }

            @Override
            public void onFailure(Call<GetCityListResponse> call, Throwable t) {
                t.printStackTrace();
                Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_REGION_LIST);
                resultMap.put("errorMessage", t.getMessage());
                dispatchResult(BmtCmd.GET_REGION_LIST, resultMap);
            }
        });
    }

    private void getRegionCountriesList() {
        BmtApi.getInstance().getCountryList().enqueue(new Callback<GetCountryListResponse>() {
            public void onResponse(Call<GetCountryListResponse> call, Response<GetCountryListResponse> response) {
                GetCountryListResponse body = response.body();
                if (body != null && body.getResult() != null) {
                    Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_REGION_COUNTRIES);
                    Gson gson = new Gson();
                    Type typeToken = new TypeToken<Map<String, Object>>() {
                    }.getType();
                    Map list = new HashMap();
                    list.put("list", body.getResult());
                    Map result = gson.fromJson(gson.toJson(list), typeToken);
                    resultMap.put("result", result);
                    dispatchResult(BmtCmd.GET_REGION_COUNTRIES, resultMap);
                } else {
                    Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_REGION_COUNTRIES);
                    resultMap.put("errorMessage", "response null");
                    dispatchResult(BmtCmd.GET_REGION_COUNTRIES, resultMap);
                }
            }

            @Override
            public void onFailure(Call<GetCountryListResponse> call, Throwable t) {
                t.printStackTrace();
                Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_REGION_COUNTRIES);
                resultMap.put("errorMessage", t.getMessage());
                dispatchResult(BmtCmd.GET_REGION_COUNTRIES, resultMap);
            }
        });
    }

    private void bindInverter(Map arg) {
        BmtApi.getInstance().bindInverter(
                        homeID,
                        getId(),
                        getSubCategory(),
                        (String) MapUtils.get(arg, "inverter_id", ""),
                        (String) MapUtils.get(arg, "mcu_id", ""))
                .enqueue(new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                        StringResponseEntry body = response.body();
                        if (body != null && body.getResult() != null) {
                            Map<String, Object> resultMap = Cmd.getDefaultResultMap(true, BmtCmd.BIND_INVERTER);
                            dispatchResult(BmtCmd.BIND_INVERTER, resultMap);
                        } else {
                            Map<String, Object> resultMap = Cmd.getDefaultResultMap(false, BmtCmd.BIND_INVERTER);
                            resultMap.put("errorMessage", "response null");
                            dispatchResult(BmtCmd.BIND_INVERTER, resultMap);
                        }
                    }

                    @Override
                    public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                        Map<String, Object> resultMap = Cmd.getDefaultResultMap(false, BmtCmd.BIND_INVERTER);
                        if (t instanceof NetWorkException) {
                            resultMap.put("errorMessage", ((NetWorkException) t).getMsgDes());
                            Cmd.putResultValue(resultMap, "originStatus", ((NetWorkException) t).getStatus());
                        }
                        dispatchResult(BmtCmd.BIND_INVERTER, resultMap);
                    }
                });
    }

    private void handMsctResponse(MsctResponse msctResponse) {
        Map result = cmdResponseConverter.convert(msctResponse);
        if (result != null) {
            dispatchResult((String) result.get("cmd"), result);
        }
    }

    public void disconnect() {
        channelManager.disconnect();
    }

    private void delete() {
        if (getFlagDeleted()) {
            MsctLog.i(TAG, "删除BMT成功");
            Map result = Cmd.getDefaultResultMap(true, BmtCmd.DELETE);
            dispatchResult(BmtCmd.DELETE, result);
            isDelete = true;
            disconnect();
            remove();
            return;
        }

        BmtApi.getInstance().deleteBmt(homeID, getId(), getSubCategory().toUpperCase()).enqueue(new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                MsctLog.i(TAG, "删除BMT成功");
                Map result = Cmd.getDefaultResultMap(true, BmtCmd.DELETE);
                dispatchResult(BmtCmd.DELETE, result);
                isDelete = true;
                disconnect();
                destory();
                remove();
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                MsctLog.i(TAG, "删除BMT失败：" + t.getCause());
                Map result = Cmd.getDefaultResultMap(false, BmtCmd.DELETE);
                if (t instanceof NetWorkException) {
                    result.put("errorMessage", ((NetWorkException) t).getMsgDes());
                    Cmd.putResultValue(result, "originStatus", ((NetWorkException) t).getStatus());
                } else {
                    result.put("errorMessage", "mes:" + t.getMessage());
                }
                dispatchResult(BmtCmd.DELETE, result);
            }
        });
    }

    public void deleteDirect(final boolean notify) {
        isDelete = true;
        disconnect();
        destory();
        if (notify) {
            remove();
        }
    }


    private void setName(String newname) {
        BmtApi.getInstance().setName(homeID, getId(), getSubCategory().toUpperCase(), newname).enqueue(new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                updateNameSync(newname);
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                Map result = Cmd.getDefaultResultMap(false, BmtCmd.SET_NAME);
                result.put("errorMessage", "sender is null");
                dispatchResult(BmtCmd.SET_NAME, result);
            }
        });
    }

    public void setNameDirect(String newname) {
        updateNameSync(newname);
    }

    private long lastUpdateTimeMillis = 0;

    /**
     * 更新名字
     * 自己修改名字时，会有请求的回调和home的通知两次重复回调给外面.
     * 由于无法区分，只能加个时间戳了
     *
     * @param newName 新的名字
     */
    private synchronized void updateNameSync(String newName) {
        final long currentTime = System.currentTimeMillis();
        final boolean notify;
        notify = null == this.name || !this.name.equals(newName) || (currentTime - lastUpdateTimeMillis > 1000);
        lastUpdateTimeMillis = currentTime;
        this.name = newName;
        convertToInfo();
        if (notify) {
            Map result = Cmd.getDefaultResultMap(true, BmtCmd.SET_NAME);
            dispatchResult(BmtCmd.SET_NAME, result);
        }
        postNameUpdateEvent();
    }

    protected void sendByte(byte[] msg) {
        Channel channel = channelManager.getChannel();
        if (channel != null) {
            channel.kcpCmdSend(msg);
        } else {
            MsctLog.e(TAG, "want to send something but no channel connect");
        }
    }

    public MsctDataFactory.Builder createMsctBuilder() {
        Channel channel = channelManager.getChannel();
        if (channel != null) {
            return channel.createMsctBuilder();
        } else {
            MsctLog.e(TAG, "create msct builder but no channel connect");
            return null;
        }
    }

    protected void sendMsct(MsctDataFactory msg) {
        Channel channel = channelManager.getChannel();
        if (channel != null) {
            Log.d(TAG, "sendMsct: " + msg.toString());
            channel.msctCmdSend(msg);
        } else {
            MsctLog.e(TAG, "want to send something but no channel connect");
        }
    }

    public void onSearchBmtInfo(@Nullable BmtListResponse.BmtBeen info, final boolean success) {
        MsctLog.i(TAG, getId() + ": onSearchBmtInfo, success: " + success);
        if (!success) {
            setFlagLoading(false);
            setFlagLoaded(false);
            realConnect();
            return;
        }

        setFlagLoading(false);
        final String currentId = getId();
        final String curSubCategory = getSubCategory();
        if (null != info && currentId.equals(info.getPid()) && curSubCategory.equals(info.getProvider())) {
            addtime = info.getAddtime();
            endId = info.getEnd_id();
            group_id = info.getGroup_id();
            name = info.getName();
            countryCode = info.hasCountryCode() ? info.getCountry_code() : "";
            deliveryArea = info.hasDeliveryArea() ? info.getDelivery_area() : "";
            convertToInfo();
            setFlagLoaded(true);
            realConnect();
            if (!TextUtils.isEmpty(countryCode) || !TextUtils.isEmpty(deliveryArea)) {
                EventBus.getDefault().post(new BmtCountryCodeUpdateEvent(getId(), countryCode, deliveryArea));
            }
            postNameUpdateEvent();
        } else {
            setFlagDeleted(true);
        }

        // 这个要放在最后，否则外面会短时间切换为离线状态
        setFlagLoaded(true);
    }

    private void postNameUpdateEvent() {
        if (!TextUtils.isEmpty(name)) {
            EventBus.getDefault().post(new BmtDeviceNameUpdateEvent(getId(), getSubCategory(), name));
        }
    }

    public void needConnectAgain() {
        setFlagLoaded(false);
        setFlagLoading(false);
    }

    private void connect(final boolean discardCache) {
        if (discardCache) {
            MsctLog.i(TAG, getId() + ": connect- discardCache = true");
            connectStatus = -1;
            setFlagLoaded(false);
            setFlagLoading(true);
            SearchBmtHelper.get().startSearchBmtSingle(homeID, this);
            return;
        }

        if (channelManager.isConnect()) {
            MsctLog.i(TAG, getId() + ": connect- is connected");
            return;
        }

        if (getFlagLoading()) {
            MsctLog.i(TAG, getId() + ": connect- loading flag is truee");
            return;
        }

        if (getFlagLoaded()) {
            realConnect();
            return;
        }

        setFlagLoading(true);
        SearchBmtHelper.get().addTask(homeID, this);
    }

    private void realConnect() {
        MsctLog.i(TAG, "bmt connect groupID:" + group_id);
        MsctLog.i("test_time", "dincore-realConnect");
        Channel channel = channelManager.getChannel(Channel.PROXY);
        ProxyChannel proxyChannel = (ProxyChannel) channel;
        proxyChannel.setGroup_id(group_id);
        proxyChannel.setReceiveID(endId);
        channel.connect();
//        p2p通道得等proxy通道通了才可以
//        Channel p2pChannel = channelManager.getChannel(Channel.P2P);
//        p2pChannel.connect();
    }

//    public void createKcp(int type, int sessionID, IKcpCreateCallBack kcpCreateCallBack) {
//        channelManager.createKcp(type, sessionID, kcpCreateCallBack);
//    }
//
//    public void removeKcp(int conv) {
//        channelManager.removeKcp(conv);
//    }


    public IMultipleSender getMultiSender() {
        return this.sender;
    }

    @Override
    public void destory() {
        super.destory();
        channelManager.destory();
        messageIdHolder.cleanAllMessageId();
    }

    public void onRemoteAddressUpdated() {
        if (isInit) {
            MsctLog.i("RemoteAddressUpdated", getId() + "onRemoteAddressUpdated");
            // 断开并释放切换节点之前的连接
            disconnect();
            // 使用新节点进行连接
            connect(true);
        } else {
            isInit = true;
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BmtDevice cacheInfo = (BmtDevice) o;
        return Objects.equals(getId(), cacheInfo.getId())
                && Objects.equals(homeID, cacheInfo.homeID);
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId(), homeID);
    }

    public void updateSender(IMultipleSender multipleSender) {
        if (null != multipleSender && this.sender != multipleSender) {
            DDLog.i(TAG, "updateSender");
            final boolean needAutoConnect = 1 == connectStatus || 0 == connectStatus;
            if (null != channelManager) {
                channelManager.destory();
                channelManager.setChannelCallBack(null);
            }

            this.sender = multipleSender;
            channelManager = new ChannelManager(this, homeID, kcpCallBack, msctCallBack);
            channelManager.setChannelCallBack(channelCallBack);
            if (needAutoConnect) {
                DDLog.i(TAG, "auto connect on updateSender");
                connect(true);
            }
        }
    }

    private boolean hasException() {
        boolean hasException = isListNotEmpty(this.vertBatteryExceptions)
                || isListNotEmpty(this.vertExceptions)
                || isListNotEmpty(this.vertGridExceptions)
                || isListNotEmpty(this.vertSystemExceptions)
                || isListNotEmpty(this.vertmpptExceptions)
                || isListNotEmpty(this.vertPresentExceptions)
                || isListNotEmpty(this.vertDCExceptions)
                || isListNotEmpty(this.evExceptions)
                || isListNotEmpty(this.mpptExceptions)
                || isListNotEmpty(this.cabinetExceptions)
                || isListNotEmpty(this.batteryExceptions)
                || isListNotEmpty(this.systemExceptions)
                || isListNotEmpty(this.communicationExceptions);
        return hasException;
    }

    private boolean isListNotEmpty(List list) {
        return list != null && list.size() > 0;
    }

    public void setCountryCodeAndDeliveryArea(String countryCode, String deliveryArea) {
        if (countryCode != null) {
            this.countryCode = countryCode;
        }
        if (deliveryArea != null) {
            this.deliveryArea = deliveryArea;
        }
        convertToInfo();
    }

    @Override
    @RestrictTo(RestrictTo.Scope.LIBRARY)
    public void dispatchResult(String cmd, Map result) {
        super.dispatchResult(cmd, result);
    }

    @Override
    @RestrictTo(RestrictTo.Scope.LIBRARY)
    public void setFlagDeleted(boolean isDeleted) {
        super.setFlagDeleted(isDeleted);
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY)
    @Override
    public void setFlagCache(boolean isCache) {
        super.setFlagCache(isCache);
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY)
    @Override
    protected void setFlagLoading(boolean isLoading) {
        super.setFlagLoading(isLoading);
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY)
    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String toString() {
        return "BmtDevice{" +
                "TAG='" + TAG + '\'' +
                ", name='" + name + '\'' +
                ", endId='" + endId + '\'' +
                ", group_id='" + group_id + '\'' +
                ", addtime=" + addtime +
                ", homeID='" + homeID + '\'' +
                ", connectStatus=" + connectStatus + '\'' +
                ", isDelete=" + isDelete + '\'' +
                ", countryCode=" + countryCode + '\'' +
                ", deliveryArea=" + deliveryArea + '\'' +
                "} " + super.toString();
    }
}
