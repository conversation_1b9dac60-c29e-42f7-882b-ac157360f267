package com.dinsafer.module_bmt.bean;

import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dincore.db.cache.ICacheInfo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/11/26 16:50
 */
@Keep
public class BmtCacheInfo implements ICacheInfo {
    private static final long serialVersionUID = 1283737693712032442L;

    private long bmtAddTime;

    private List<CacheInfo> cacheList;

    public BmtCacheInfo() {
        this.cacheList = new ArrayList<>();
    }

    public boolean isCacheReady() {
        return bmtAddTime > 0;
    }

    public long getCommonAddTime() {
        return bmtAddTime;
    }

    public void setCommonAddTime(long commonAddTime) {
        this.bmtAddTime = commonAddTime;
    }

    public long getNewestAddTime() {
        long newestAddTime = 0L;
        if (0 < cacheList.size()) {
            for (CacheInfo cacheInfo : cacheList) {
                long addTime = cacheInfo.getAddTime();
                if (addTime > newestAddTime) {
                    newestAddTime = addTime;
                }
            }
        }
        return newestAddTime;
    }

    public long getBmtAddTime() {
        return bmtAddTime;
    }

    public void setBmtAddTime(long bmtAddTime) {
        this.bmtAddTime = bmtAddTime;
    }

    public boolean addDevice(final String pid, String provider) {
        final CacheInfo newCache = new CacheInfo(pid, provider);
        return addDevice(newCache);
    }

    public boolean addDevice(CacheInfo cacheInfo) {
        if (null != cacheInfo && !TextUtils.isEmpty(cacheInfo.getPid()) && !cacheList.contains(cacheInfo)) {
            cacheList.add(cacheInfo);
            return true;
        }
        return false;
    }

    public boolean removeDevice(final String pid, @Nullable final String provider) {
        CacheInfo newCache = new CacheInfo(pid, provider);
        if (!TextUtils.isEmpty(pid) && cacheList.contains(newCache)) {
            cacheList.remove(newCache);
            return true;
        }
        return false;
    }

    public void updateFrom(@Nullable final BmtCacheInfo src) {
        this.cacheList.clear();
        if (null == src) {
            bmtAddTime = 0;
            return;
        }

        bmtAddTime = src.bmtAddTime;
        if (null != src.cacheList) {
            this.cacheList.addAll(src.cacheList);
        }
        if (null == this.cacheList || cacheList.size() == 0) {
            bmtAddTime = 1;
        }
    }

    public List<CacheInfo> getCacheList() {
        return cacheList;
    }

    public boolean isCacheEmpty() {
        return null == cacheList || cacheList.size() == 0;
    }

    @Override
    public String toString() {
        return "BmtCacheInfo{" +
                "bmtAddTime=" + bmtAddTime +
                ", cacheList=" + cacheList +
                '}';
    }

    @Override
    public boolean isNeedSaveCache() {
        return true;
    }

    /**
     * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
     * @version 1.0
     * @since 2022/11/26 19:31
     */
    @Keep
    public static final class CacheInfo implements Serializable {
        private static final long serialVersionUID = 1284814048931686523L;
        private String pid;
        private String provider;
        protected List vertBatteryExceptions;
        protected List vertExceptions;
        protected List vertGridExceptions;
        protected List vertSystemExceptions;
        protected List vertmpptExceptions;
        protected List vertPresentExceptions;
        protected List vertDCExceptions;
        private List evExceptions;
        private List mpptExceptions;
        private List cabinetExceptions;
        private List batteryExceptions;
        private List systemExceptions;
        private List communicationExceptions;
        private String name;
        protected int chipsStatus;

        private long addTime;

        protected boolean thirdpartyPVOn;
        protected List vertStates;

        protected String iotVersion;

        private String countryCode;
        private String deliveryArea;
        private int gridStatus;
        private int regulateFrequencyState;

        private int emergency_reserve = -1;
        private int smart_reserve = -1;

        private boolean pvSupplyCustomizationSupported;

        public CacheInfo() {
        }

        public CacheInfo(String pid, String provider) {
            this.pid = pid;
            this.provider = provider;
        }

        public CacheInfo(String pid, String provider, String name) {
            this.pid = pid;
            this.provider = provider;
            this.name = name;
        }

        public CacheInfo(String pid, String provider, String name, long addTime) {
            this.pid = pid;
            this.provider = provider;
            this.name = name;
            this.addTime = addTime;
        }

        public static CacheInfo createFrom(@NonNull CacheInfo info) {
            final CacheInfo newInfo = new CacheInfo(info.pid, info.provider);
            newInfo.setName(info.name);
            return newInfo;
        }

        public String getPid() {
            return pid;
        }

        public void setPid(String pid) {
            this.pid = pid;
        }

        public String getProvider() {
            return provider;
        }

        public void setProvider(String provider) {
            this.provider = provider;
        }

        public List getVertBatteryExceptions() {
            return vertBatteryExceptions;
        }

        public void setVertBatteryExceptions(List vertBatteryExceptions) {
            this.vertBatteryExceptions = vertBatteryExceptions;
        }

        public List getVertExceptions() {
            return vertExceptions;
        }

        public void setVertExceptions(List vertExceptions) {
            this.vertExceptions = vertExceptions;
        }

        public List getVertGridExceptions() {
            return vertGridExceptions;
        }

        public void setVertGridExceptions(List vertGridExceptions) {
            this.vertGridExceptions = vertGridExceptions;
        }

        public List getVertSystemExceptions() {
            return vertSystemExceptions;
        }

        public void setVertSystemExceptions(List vertSystemExceptions) {
            this.vertSystemExceptions = vertSystemExceptions;
        }

        public List getVertmpptExceptions() {
            return vertmpptExceptions;
        }

        public void setVertmpptExceptions(List vertmpptExceptions) {
            this.vertmpptExceptions = vertmpptExceptions;
        }

        public List getVertPresentExceptions() {
            return vertPresentExceptions;
        }

        public void setVertPresentExceptions(List vertPresentExceptions) {
            this.vertPresentExceptions = vertPresentExceptions;
        }

        public List getVertDCExceptions() {
            return vertDCExceptions;
        }

        public void setVertDCExceptions(List vertDCExceptions) {
            this.vertDCExceptions = vertDCExceptions;
        }

        public List getEvExceptions() {
            return evExceptions;
        }

        public void setEvExceptions(List evExceptions) {
            this.evExceptions = evExceptions;
        }

        public List getMpptExceptions() {
            return mpptExceptions;
        }

        public void setMpptExceptions(List mpptExceptions) {
            this.mpptExceptions = mpptExceptions;
        }

        public List getCabinetExceptions() {
            return cabinetExceptions;
        }

        public void setCabinetExceptions(List cabinetExceptions) {
            this.cabinetExceptions = cabinetExceptions;
        }

        public List getBatteryExceptions() {
            return batteryExceptions;
        }

        public void setBatteryExceptions(List batteryExceptions) {
            this.batteryExceptions = batteryExceptions;
        }

        public List getSystemExceptions() {
            return systemExceptions;
        }

        public void setSystemExceptions(List systemExceptions) {
            this.systemExceptions = systemExceptions;
        }

        public List getCommunicationExceptions() {
            return communicationExceptions;
        }

        public void setCommunicationExceptions(List communicationExceptions) {
            this.communicationExceptions = communicationExceptions;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getChipsStatus() {
            return chipsStatus;
        }

        public void setChipsStatus(int chipsStatus) {
            this.chipsStatus = chipsStatus;
        }

        public long getAddTime() {
            return addTime;
        }

        public void setAddTime(long addTime) {
            this.addTime = addTime;
        }

        public boolean isThirdpartyPVOn() {
            return thirdpartyPVOn;
        }

        public void setThirdpartyPVOn(boolean thirdpartyPVOn) {
            this.thirdpartyPVOn = thirdpartyPVOn;
        }

        public List getVertStates() {
            return vertStates;
        }

        public void setVertStates(List vertStates) {
            this.vertStates = vertStates;
        }

        public String getIotVersion() {
            return iotVersion;
        }

        public void setIotVersion(String iotVersion) {
            this.iotVersion = iotVersion;
        }

        public String getCountryCode() {
            return countryCode;
        }

        public void setCountryCode(String countryCode) {
            this.countryCode = countryCode;
        }

        public String getDeliveryArea() {
            return deliveryArea;
        }

        public void setDeliveryArea(String deliveryArea) {
            this.deliveryArea = deliveryArea;
        }

        public int getGridStatus() {
            return gridStatus;
        }

        public void setGridStatus(int gridStatus) {
            this.gridStatus = gridStatus;
        }

        public int getRegulateFrequencyState() {
            return regulateFrequencyState;
        }

        public void setRegulateFrequencyState(int regulateFrequencyState) {
            this.regulateFrequencyState = regulateFrequencyState;
        }

        public int getEmergency_reserve() {
            return emergency_reserve;
        }

        public void setEmergency_reserve(int emergency_reserve) {
            this.emergency_reserve = emergency_reserve;
        }

        public int getSmart_reserve() {
            return smart_reserve;
        }

        public void setSmart_reserve(int smart_reserve) {
            this.smart_reserve = smart_reserve;
        }

        public boolean isPvSupplyCustomizationSupported() {
            return pvSupplyCustomizationSupported;
        }

        public void setPvSupplyCustomizationSupported(boolean pvSupplyCustomizationSupported) {
            this.pvSupplyCustomizationSupported = pvSupplyCustomizationSupported;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            CacheInfo cacheInfo = (CacheInfo) o;
            return Objects.equals(pid, cacheInfo.pid) && Objects.equals(provider, cacheInfo.provider);
        }

        @Override
        public int hashCode() {
            return Objects.hash(pid, provider);
        }

        @Override
        public String toString() {
            return "CacheInfo{" +
                    "pid='" + pid + '\'' +
                    ", provider='" + provider + '\'' +
                    ", vertBatteryExceptions=" + vertBatteryExceptions +
                    ", vertExceptions=" + vertExceptions +
                    ", vertGridExceptions=" + vertGridExceptions +
                    ", vertSystemExceptions=" + vertSystemExceptions +
                    ", vertmpptExceptions=" + vertmpptExceptions +
                    ", vertPresentExceptions=" + vertPresentExceptions +
                    ", vertDCExceptions=" + vertDCExceptions +
                    ", evExceptions=" + evExceptions +
                    ", mpptExceptions=" + mpptExceptions +
                    ", cabinetExceptions=" + cabinetExceptions +
                    ", batteryExceptions=" + batteryExceptions +
                    ", systemExceptions=" + systemExceptions +
                    ", communicationExceptions=" + communicationExceptions +
                    ", name='" + name + '\'' +
                    ", chipsStatus=" + chipsStatus +
                    ", addTime=" + addTime +
                    ", thirdpartyPVOn=" + thirdpartyPVOn +
                    ", vertStates=" + vertStates +
                    ", iotVersion='" + iotVersion + '\'' +
                    ", countryCode='" + countryCode + '\'' +
                    ", deliveryArea='" + deliveryArea + '\'' +
                    ", gridStatus=" + gridStatus +
                    ", regulateFrequencyState=" + regulateFrequencyState +
                    ", emergency_reserve=" + emergency_reserve +
                    ", smart_reserve=" + smart_reserve +
                    ", pvSupplyCustomizationSupported=" + pvSupplyCustomizationSupported +
                    '}';
        }
    }
}
