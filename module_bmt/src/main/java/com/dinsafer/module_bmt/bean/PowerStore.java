package com.dinsafer.module_bmt.bean;

import com.dinsafer.dssupport.msctlib.netty.IMultipleSender;
import com.dinsafer.module_bmt.http.bean.BmtListResponse;

/**
 * 对应型号 PS1-BAK10-HS10
 */
public class PowerStore extends BmtDevice{
    public PowerStore(IMultipleSender sender, String homeID, String pid, BmtCacheInfo.CacheInfo bmtInfo) {
        super(sender, homeID, pid, BmtConst.PROVIDER_PS1_BAK10_HS10, bmtInfo);
    }

    public PowerStore(IMultipleSender sender, String pid, String homeID, String endId, String group_id, Long addtime) {
        super(sender, pid, homeID, endId, group_id, addtime, BmtConst.PROVIDER_PS1_BAK10_HS10);
    }

    public PowerStore(IMultipleSender sender, String homeID, BmtListResponse.BmtBeen deviceBean) {
        super(sender, homeID, deviceBean, BmtConst.PROVIDER_PS1_BAK10_HS10);
    }
}
