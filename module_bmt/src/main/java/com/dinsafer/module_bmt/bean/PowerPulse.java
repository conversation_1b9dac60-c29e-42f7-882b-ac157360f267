package com.dinsafer.module_bmt.bean;

import com.dinsafer.dssupport.msctlib.netty.IMultipleSender;
import com.dinsafer.module_bmt.http.bean.BmtListResponse;

public class PowerPulse extends BmtDevice{
    public PowerPulse(IMultipleSender sender, String homeID, String pid, BmtCacheInfo.CacheInfo bmtInfo) {
        super(sender, homeID, pid, BmtConst.PROVIDER_VB1_BAK5_HS10, bmtInfo);
    }

    public PowerPulse(IMultipleSender sender, String pid, String homeID, String endId, String group_id, Long addtime) {
        super(sender, pid, homeID, endId, group_id, addtime, BmtConst.PROVIDER_VB1_BAK5_HS10);
    }

    public PowerPulse(IMultipleSender sender, String homeID, BmtListResponse.BmtBeen deviceBean) {
        super(sender, homeID, deviceBean, BmtConst.PROVIDER_VB1_BAK5_HS10);
    }
}
