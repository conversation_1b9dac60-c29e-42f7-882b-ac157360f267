package com.dinsafer.module_bmt.bean;

import com.dinsafer.dssupport.msctlib.netty.IMultipleSender;
import com.dinsafer.module_bmt.http.bean.BmtListResponse;

/**
 * 对应型号 PC1-BAK15-HS10
 */
public class PowerCore20 extends BmtDevice{
    public PowerCore20(IMultipleSender sender, String homeID, String pid, BmtCacheInfo.CacheInfo bmtInfo) {
        super(sender, homeID, pid, BmtConst.PROVIDER_PC1_BAK15_HS10, bmtInfo);
    }

    public PowerCore20(IMultipleSender sender, String pid, String homeID, String endId, String group_id, Long addtime) {
        super(sender, pid, homeID, endId, group_id, addtime, BmtConst.PROVIDER_PC1_BAK15_HS10);
    }

    public PowerCore20(IMultipleSender sender, String homeID, BmtListResponse.BmtBeen deviceBean) {
        super(sender, homeID, deviceBean, BmtConst.PROVIDER_PC1_BAK15_HS10);
    }
}
