package com.dinsafer.module_bmt.bean;

import androidx.annotation.Keep;

/**
 * Bmt扫描到的WIFI信息
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2023/3/7 16:57
 */
@Keep
public class BmtBleWifiInfo {
    private final String ssid;
    private final int rssi;
    private boolean auth = false;

    public BmtBleWifiInfo(String ssid, int rssi) {
        this.ssid = ssid;
        this.rssi = rssi;
    }

    public void setAuth(boolean auth) {
        this.auth = auth;
    }

    public String getSsid() {
        return ssid;
    }

    public int getRssi() {
        return rssi;
    }

    public boolean isAuth() {
        return auth;
    }

    @Override
    public String toString() {
        return "BmtBleWifiInfo{" +
                "ssid='" + ssid + '\'' +
                ", rssi=" + rssi +
                ", auth=" + auth +
                '}';
    }
}
