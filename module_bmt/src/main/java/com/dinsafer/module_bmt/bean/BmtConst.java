package com.dinsafer.module_bmt.bean;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Keep;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/5/25 6:05 下午
 */
@Keep
public class BmtConst {
    // 代表旧版本 1.0 或 1.5 硬件，新版 IoT 不再使用；
    public static final String OLD_UUID = "70D1EA0C-4503-496A-B67D-6E48F11C43EB";
    // 代表逆变器 1.0 硬件，支持 HP5000；
    public static final String ONE_POINT_ZERO_UUID = "85c12883-d0c8-40ab-8e51-c28339bafbf0";
    // 代表逆变器 1.5 硬件，支持 PS1, PS2, VB1；
    public static final String ONE_POINT_FIVE_UUID = "6e0655ab-3eff-4f46-bab1-45d4bf2c0374";
    // 代表逆变器 2.0 硬件，支持 PC1；
    public static final String TWO_POINT_ZERO_UUID = "bcf25cee-fbad-4f85-a41c-8da2bd9ed1ac";
    // 代表逆变器 3.0 硬件，支持 PC3；
    public static final String THREE_POINT_ZERO_UUID = "d977e25f-8679-4d93-8613-243f0e67fee6";

    // Power CORE 1.0 支持 的 UUID
    public static final String POWER_CORE_1_UUID = OLD_UUID + "," + ONE_POINT_ZERO_UUID;
    // Power CORE 2.0 支持 的 UUID
    public static final String POWER_CORE_2_UUID = TWO_POINT_ZERO_UUID + "," + THREE_POINT_ZERO_UUID ;
    // Power CORE 3.0 支持 的 UUID
    public static final String POWER_CORE_3_UUID = THREE_POINT_ZERO_UUID;

    // Power Store 支持 的 UUID
    public static final String POWER_STORE_UUID = OLD_UUID + "," + ONE_POINT_FIVE_UUID;
    // Power Pulse 支持 的 UUID
    public static final String POWER_PULSE_UUID = ONE_POINT_FIVE_UUID;

    //三相
    public static final String PROVIDER_BMT_HP5000 = "HP5000";
    //单相
    public static final String PROVIDER_BMT_HP5001 = "HP5001";

    // Power Core 2.0
    // 家庭储能电源2.0(三相)
    public static final String PROVIDER_PC1_BAK15_HS10 = "PC1-BAK15-HS10";
    // Power Store
    public static final String PROVIDER_PS1_BAK10_HS10 = "PS1-BAK10-HS10";

    // Power Pulse
    public static final String PROVIDER_VB1_BAK5_HS10 = "VB1-BAK5-HS10";
    // Power Core 3.0
    public static final String PROVIDER_PC3 = "PC3";


    public static List<String> ALL_PROVIDER;
    static {
        ALL_PROVIDER = new ArrayList<>();
        ALL_PROVIDER.add(PROVIDER_BMT_HP5000);
        ALL_PROVIDER.add(PROVIDER_BMT_HP5001);
        ALL_PROVIDER.add(PROVIDER_PC1_BAK15_HS10);
        ALL_PROVIDER.add(PROVIDER_PS1_BAK10_HS10);
        ALL_PROVIDER.add(PROVIDER_VB1_BAK5_HS10);
        ALL_PROVIDER.add(PROVIDER_PC3);
    }

}
