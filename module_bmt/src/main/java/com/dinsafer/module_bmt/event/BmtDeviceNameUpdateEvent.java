package com.dinsafer.module_bmt.event;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2023/1/10 5:19 下午
 */
@Keep
public class BmtDeviceNameUpdateEvent {
    @NonNull
    private final String pid;
    @NonNull
    private final String subCategory;
    @Nullable
    private final String name;

    public BmtDeviceNameUpdateEvent(@NonNull String pid, @Nullable String subCategory, @Nullable String name) {
        this.pid = pid;
        this.subCategory = subCategory;
        this.name = name;
    }

    @NonNull
    public String getSubCategory() {
        return subCategory;
    }

    @NonNull
    public String getPid() {
        return pid;
    }

    @Nullable
    public String getName() {
        return name;
    }

    @Override
    public String toString() {
        return "BmtDeviceNameUpdateEvent{" +
                "pid='" + pid + '\'' +
                ", subCategory='" + subCategory + '\'' +
                ", name='" + name + '\'' +
                '}';
    }
}
