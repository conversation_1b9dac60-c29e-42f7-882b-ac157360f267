package com.dinsafer.module_bmt.event;

public class BmtThirdPartyPVOnEvent {
    private String deviceId;
    private String provider;
    private boolean thirdpartyPVOn;

    public BmtThirdPartyPVOnEvent(String deviceId, String provider, boolean thirdpartyPVOn) {
        this.deviceId = deviceId;
        this.provider = provider;
        this.thirdpartyPVOn = thirdpartyPVOn;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public boolean isThirdpartyPVOn() {
        return thirdpartyPVOn;
    }

    public void setThirdpartyPVOn(boolean thirdpartyPVOn) {
        this.thirdpartyPVOn = thirdpartyPVOn;
    }
}
