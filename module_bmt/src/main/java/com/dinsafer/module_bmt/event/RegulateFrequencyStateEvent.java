package com.dinsafer.module_bmt.event;

/**
 * @describe：
 * @date：2024/11/22
 * @author: create by Sydnee
 */
public class RegulateFrequencyStateEvent {
    private String deviceId;
    private String provider;
    private int regulateFrequencyState;

    public RegulateFrequencyStateEvent(String deviceId, String provider, int regulateFrequencyState) {
        this.deviceId = deviceId;
        this.provider = provider;
        this.regulateFrequencyState = regulateFrequencyState;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public String getProvider() {
        return provider;
    }

    public int getRegulateFrequencyState() {
        return regulateFrequencyState;
    }
}

