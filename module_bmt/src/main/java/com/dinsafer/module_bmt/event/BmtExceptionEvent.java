package com.dinsafer.module_bmt.event;

import androidx.annotation.Keep;

import java.util.List;

@Keep
public class BmtExceptionEvent {
    private String deviceId;
    private String subCategory;
    protected List vertBatteryExceptions;
    protected List vertExceptions;
    protected List vertGridExceptions;
    protected List vertSystemExceptions;
    protected List vertmpptExceptions;
    protected List vertPresentExceptions;
    protected List vertDCExceptions;
    private List evExceptions;
    private List mpptExceptions;
    private List cabinetExceptions;
    private List batteryExceptions;
    private List systemExceptions;
    private List communicationExceptions;


    public BmtExceptionEvent(String deviceId, String subCategory) {
        this.deviceId = deviceId;
        this.subCategory = subCategory;
    }

    public BmtExceptionEvent(String deviceId, String subCategory, List<Integer> vertBatteryExceptions,List<Integer> vertExceptions,
                             List<Integer> vertGridExceptions,List<Integer> vertSystemExceptions,
                             List<Integer> vertmpptExceptions,List<Integer> vertPresentExceptions,List<Integer> vertDCExceptions,
                             List evExceptions, List mpptExceptions, List cabinetExceptions, List batteryExceptions, List systemExceptions, List communicationExceptions) {
        this.deviceId = deviceId;
        this.subCategory = subCategory;
        this.vertBatteryExceptions = vertBatteryExceptions;
        this.vertExceptions = vertExceptions;
        this.vertGridExceptions = vertGridExceptions;
        this.vertSystemExceptions = vertSystemExceptions;
        this.vertmpptExceptions = vertmpptExceptions;
        this.vertPresentExceptions = vertPresentExceptions;
        this.vertDCExceptions = vertDCExceptions;
        this.evExceptions = evExceptions;
        this.mpptExceptions = mpptExceptions;
        this.cabinetExceptions = cabinetExceptions;
        this.batteryExceptions = batteryExceptions;
        this.systemExceptions = systemExceptions;
        this.communicationExceptions = communicationExceptions;
    }

    public BmtExceptionEvent(String deviceId, String subCategory, List<Integer> vertBatteryExceptions,List<Integer> vertExceptions,
                             List<Integer> vertGridExceptions,List<Integer> vertSystemExceptions,
                             List<Integer> vertmpptExceptions,List<Integer> vertPresentExceptions,List<Integer> vertDCExceptions) {
        this.deviceId = deviceId;
        this.subCategory = subCategory;
        this.vertBatteryExceptions = vertBatteryExceptions;
        this.vertExceptions = vertExceptions;
        this.vertGridExceptions = vertGridExceptions;
        this.vertSystemExceptions = vertSystemExceptions;
        this.vertmpptExceptions = vertmpptExceptions;
        this.vertPresentExceptions = vertPresentExceptions;
        this.vertDCExceptions = vertDCExceptions;
    }

    public BmtExceptionEvent(String deviceId, String subCategory, int type, List exceptions) {
        this.deviceId = deviceId;
        this.subCategory = subCategory;
        switch (type) {
            case 2:
                this.evExceptions = exceptions;
                break;

            case 3:
                this.mpptExceptions = exceptions;
                break;

            case 4:
                this.cabinetExceptions = exceptions;
                break;

            case 5:
                this.batteryExceptions = exceptions;
                break;

            case 6:
                this.systemExceptions = exceptions;
                break;

            case 7:
                this.communicationExceptions = exceptions;
                break;
        }
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }

    public List getVertBatteryExceptions() {
        return vertBatteryExceptions;
    }

    public void setVertBatteryExceptions(List vertBatteryExceptions) {
        this.vertBatteryExceptions = vertBatteryExceptions;
    }

    public List getVertExceptions() {
        return vertExceptions;
    }

    public void setVertExceptions(List vertExceptions) {
        this.vertExceptions = vertExceptions;
    }

    public List getVertGridExceptions() {
        return vertGridExceptions;
    }

    public void setVertGridExceptions(List vertGridExceptions) {
        this.vertGridExceptions = vertGridExceptions;
    }

    public List getVertSystemExceptions() {
        return vertSystemExceptions;
    }

    public void setVertSystemExceptions(List vertSystemExceptions) {
        this.vertSystemExceptions = vertSystemExceptions;
    }

    public List getVertmpptExceptions() {
        return vertmpptExceptions;
    }

    public void setVertmpptExceptions(List vertmpptExceptions) {
        this.vertmpptExceptions = vertmpptExceptions;
    }

    public List getVertPresentExceptions() {
        return vertPresentExceptions;
    }

    public void setVertPresentExceptions(List vertPresentExceptions) {
        this.vertPresentExceptions = vertPresentExceptions;
    }

    public List getVertDCExceptions() {
        return vertDCExceptions;
    }

    public void setVertDCExceptions(List vertDCExceptions) {
        this.vertDCExceptions = vertDCExceptions;
    }

    public List getEvExceptions() {
        return evExceptions;
    }

    public void setEvExceptions(List evExceptions) {
        this.evExceptions = evExceptions;
    }

    public List getMpptExceptions() {
        return mpptExceptions;
    }

    public void setMpptExceptions(List mpptExceptions) {
        this.mpptExceptions = mpptExceptions;
    }

    public List getCabinetExceptions() {
        return cabinetExceptions;
    }

    public void setCabinetExceptions(List cabinetExceptions) {
        this.cabinetExceptions = cabinetExceptions;
    }

    public List getBatteryExceptions() {
        return batteryExceptions;
    }

    public void setBatteryExceptions(List batteryExceptions) {
        this.batteryExceptions = batteryExceptions;
    }

    public List getSystemExceptions() {
        return systemExceptions;
    }

    public void setSystemExceptions(List systemExceptions) {
        this.systemExceptions = systemExceptions;
    }

    public List getCommunicationExceptions() {
        return communicationExceptions;
    }

    public void setCommunicationExceptions(List communicationExceptions) {
        this.communicationExceptions = communicationExceptions;
    }
}
