package com.dinsafer.module_bmt.event;

public class IotVersionEvent {

    private String deviceId;
    private String provider;

    private String iotVersion;

    public IotVersionEvent(String deviceId, String provider, String iotVersion) {
        this.deviceId = deviceId;
        this.provider = provider;
        this.iotVersion = iotVersion;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getIotVersion() {
        return iotVersion;
    }

    public void setIotVersion(String iotVersion) {
        this.iotVersion = iotVersion;
    }
}
