package com.dinsafer.module_bmt;

import androidx.annotation.Keep;

import com.dinsafer.dincore.common.Cmd;

@Keep
public class BmtCmd extends Cmd {
    public static final String CONNECT = "connect";
    public static final String DISCONNECT = "disconnect";
    public static final String CONNECT_STATUS_CHANGED = "connect_status_changed";

    //*************************************逆变器相关*************************************************
    //=获取逆变器的信息(0x1004)
    public static final String GET_INVERTER_INFO = "get_inverter_info";
    //=获取逆变器的输入信息(0x1002)
    public static final String GET_INVERTER_INPUT_INFO = "get_inverter_input_info";
    //=逆变器的控制(0x0001)
    public static final String SET_INVERTER_OPEN = "set_inverter_open";
    //=获取逆变器的输出信息(0x1001)
    public static final String GET_INVERTER_OUTPUT_INFO = "get_inverter_output_info";
    //=逆变器异常推送(0x2002)
    public static final String INVERTER_EXCEPTION = "inverter_exception";
    //=获取全局电流流向负载信息(0xa030)
    public static final String GET_GLOBAL_CURRENT_FLOW_INFO = "get_global_currentflow_info";
    //绑定逆变器 id(服务器接口)
    public static final String BIND_INVERTER = "bind_inverter";
    //=重启逆变器(0xa032)
    public static final String RESET_INVERTER = "reset_inverter";

    //*************************************电池包相关*************************************************
    //=获取电池包总信息(0x1005)
    public static final String GET_BATTERY_ALLINFO = "get_battery_allinfo";
    //=设置电池包总“OFF”(0x0002)
    public static final String SET_BATTERY_ALLOFF = "set_battery_alloff";
    //=电池包单个引索变化通知(0x2001)
    public static final String BATTERY_INDEX_CHANGED = "battery_index_changed";
    //=获取电池包单个信息(0x1006)
    public static final String GET_BATTERY_INFO = "get_battery_info";
    //=通过索引读电池包配件状态(0x100c)
    public static final String GET_BATTERY_ACCESSORYSTATE = "get_battery_accessorystate";
    //=电池包配件状态变化事件(0x2006)
    public static final String BATTERY_ACCESSORYSTATE_CHANGED = "battery_accessorystate_changed";
    //=电池包异常推送(0x2003)
    public static final String BATTERY_EXCEPTION = "battery_exception";
    //=电池包状态信息通知(0xa014)
    public static final String BATTERY_STATUSINFO_NOTIFY = "battery_statusinfo_notify";
    //=电池包配件状态变化事件自定义版本(0xa015)
    public static final String BATTERY_ACCESSORYSTATE_CHANGED_CUSTOM = "battery_accessorystate_changed_custom";

    //*************************************全局******************************************************
    //=读全局负载状态(0x1007)
    public static final String GET_GLOBALLOAD_STATE = "get_global_loadstate";
    //=读全局异常(0x1000)
    public static final String GET_GLOBAL_EXCEPTIONS = "get_global_exceptions";
    //=系统过功率保护事件(0x200a)
    public static final String SYSTEM_EXCEPTION = "system_exception";
    //=通信故障事件(0x200b)
    public static final String COMMUNICATION_EXCEPTION = "communication_exception";
    // 设置忽略异常状态(0xa028)
    public static final String SET_EXCEPTION_IGNORE = "set_exception_ignore";
    //=读32大佬的全局异常(0xa033)
    public static final String GET_VIEW_EXCEPTIONS = "get_view_exceptions";
    //=读设备固件版本(0xa037)
    public static final String GET_FIRMWARES = "get_firmwares";
    //=获取保险丝功率上限列表
    public static final String GET_FUSE_POWER_CAPS = "get_fuse_power_caps";
    //=设置主保险丝规格(0xa038)
    public static final String SET_FUSE_SPECS = "set_fuse_specs";
    //=获取主保险丝规格(0xa039)
    public static final String GET_FUSE_SPECS = "get_fuse_specs";
    //=通知IoT同步需要的数据(0xa044)
    public static final String SYNC_CONF = "sync_conf";
    //=获取所有 feature 支持(0xa052)
    public static final String GET_ALL_SUPPORT_FEATURES = "get_all_support_features";

    //*************************************MCU******************************************************
    //=读MCU信息(0x1003)
    public static final String GET_MCU_INFO = "get_mcu_info";

    //*************************************机柜******************************************************
    //=通过索引读机柜配件状态(0x100d)
    public static final String GET_CABINET_STATE = "get_cabinet_state";
    //=机柜配件状态变化事件(0x2007)
    public static final String CABINET_STATE_CHANGED = "cabinet_state_changed";
    //=读机柜信息(0x100e)
    public static final String GET_CABINET_ALLINFO = "get_cabinet_allinfo";
    //=机柜引索变化通知(0x2008)
    public static final String CABINET_INDEX_CHANGED = "cabinet_index_changed";
    //=机柜异常事件(0x2009)
    public static final String CABINET_EXCEPTION = "cabinet_exception";

    //*************************************MPPT*****************************************************
    //=读MPPT状态(0x1008)
    public static final String GET_MPPT_STATE = "get_mppt_state";
    //=MPPT异常推送(0x2004)
    public static final String MPPT_EXCEPTION = "mppt_exception";
    //=获取第三方PV配置(0xa040)
    public static final String GET_THIRDPARTYPV_INFO = "get_thirdpartypv_info";
    //=设置第三方pv开关(0xa041)
    public static final String SET_THIRD_PARTYPV_ON = "set_thirdpartypv_on";

    //*************************************EV*******************************************************
    //=读EV状态(0x100a)
    public static final String GET_EV_STATE = "get_ev_state";
    //=EV异常推送(0x2005)
    public static final String EV_EXCEPTION = "ev_exception";
    // 获取EV充电模式(0xa020)
    public static final String GET_CURRENT_EV_CHARGING_MODE = "get_current_evchargingmode";
    // 获取EV充电模式-预设时间设置(0xa021)
    public static final String GET_EV_CHARGING_MODE_SCHEDULE = "get_evchargingmode_schedule";
    // 设置EV充电模式(0xa022)
    public static final String SET_EV_CHARGING_MODE = "set_evchargingmode";
    //=获取EV充电高级状态(0xa025)
    public static final String GET_CURRENT_EVADVANCESTATUS = "get_current_evadvancestatus";
    //=EV充电高级状态改变推送(0xa026)
    public static final String EV_ADVANCESTATUS_CHANGED = "ev_advancestatus_changed";
    //=获取EV充电状态信息(0xa027)
    public static final String GET_EVCHARGING_INFO = "get_evcharging_info";
    //=退出EV即时充电模式(0xa029)
    public static final String SET_EVCHARGINGMODE_INSTANTCHARGE = "set_evchargingmode_instantcharge";
    //=设置EV充电模式配置（即时充电下）(0xa031)
    public static final String SET_EVCHARGINGMODE_INSTANT = "set_evchargingmode_instant";

    // 获取智能 EV 调控状态(0xa050)
    public static final String GET_SMART_EV_STATUS = "get_smart_ev_status";
    // 设置智能 EV 调控状态(0xa051)
    public static final String SET_SMART_EV_STATUS = "set_smart_ev_status";
    //*************************************策略*******************************************************
    //=紧急充电设置(0xa001)
    public static final String SET_EMERGENCY_CHARGE = "set_emergency_charge";
    //=获取紧急充电设置(0xa002)
    public static final String GET_EMERGENCY_CHARGE = "get_emergency_charge";
    //=充电策略设置(0xa003)
    public static final String SET_CHARGE_STRATEGIES = "set_charge_strategies";
    //=获取充电策略设置(0xa004)
    public static final String GET_CHARGE_STRATEGIES = "get_charge_strategies";

    // 打开虚拟电厂(0xa005)
    public static final String SET_VIRTUAL_POWER_PLANT = "set_virtualpowerplant";
    // 获取虚拟电厂设置(0xa006)
    public static final String GET_VIRTUAL_POWER_PLANT = "get_virtualpowerplant";
    // 获取通讯信号强度(0xa007)
    public static final String GET_COMMUNICATE_SIGNAL = "get_communicate_signal";
    // 获取 PV 供应优先级(0xa047)
    public static final String GET_PV_DIST = "get_pv_dist";
    // 设置 PV 供应优先级(0xa048)
    public static final String SET_PV_DIST = "set_pv_dist";
    // 获取设备定位信息
    public static final String GET_LOCATION = "get_location";


    //*************************************BMT所在的城市**********************************************
    //获取城市列表
    public static final String GET_REGION_LIST = "get_region_list";
    //获取城市列表
    public static final String UPDATE_REGION = "update_region";
    //获取区域
    public static final String GET_REGION = "get_region";
    //获取国家列表
    public static final String GET_REGION_COUNTRIES = "get_region_countries";
    /**
     * 通过MSCT设置国家/城市(0xa024)
     */
    public static final String SET_REGION = "set_region";

    // 请求重置设备(0xa008)
    public static final String RESET = "reset";
    // 重置设备(0xa009)
    public static final String RESET_DEVICE_DATA = "reset_devicedata";
    // 删除设备
    public static final String DELETE = "delete";
    // 获取IOT信息(0xa010)
    public static final String GET_ADVANCE_INFO = "get_advance_info";
    // 重新启动逆变器(0xa011)
    public static final String REBOOT_INVERTER = "reboot_inverter";
    // 获取当前模式(0x1009)
    public static final String GET_MODE = "get_mode";
    // 获取当前模式(0xa043)(新接口)
    public static final String GET_MODE_V2 = "get_mode_v2";

    //************************************* 升级 **********************************************
    // 获取IOT管理的芯片升级状态(0xa011)
    public static final String GET_CHIPS_STATUS = "get_chips_status";
    // 获取IOT管理的芯片升级进度(0xa012)
    public static final String GET_CHIPS_UPDATE_PROGRESS = "get_chips_update_progress";
    // 通知IOT升级芯片(0xa013)
    public static final String UPDATE_CHIPS = "update_chips";

    //*************************************BMT数据统计**********************************************
    //获取「负载」数据
    public static final String GET_STATS_LOADUSAGE = "get_stats_loadusage";

    //获取「电池」功耗/能量
    public static final String GET_STATS_BATTERY = "get_stats_battery";

    //获取「电网」功耗/能量
    public static final String GET_STATS_GRID = "get_stats_grid";

    //获取「mppt」功耗/能量
    public static final String GET_STATS_MPPT = "get_stats_mppt";

    //获取「电池百分比」数据
    public static final String GET_STATS_BATTERY_POWERLEVEL = "get_stats_battery_powerlevel";

    // 获取「收益」功耗/能量
    public static final String GET_STATS_REVENUE = "get_stats_revenue";

    // 获取环保评级
    public static final String GET_STATS_ECO = "get_stats_eco";

    // 获取设备 b sensor 状态
    public static final String GET_BSENSOR_STATUS = "get_bsensor_status";

    // [APP] 获取电价信息
    public static final String GET_ELEC_PRICE_INFO = "get_elec_price_info";

    // 获取指定家庭是否曾经接入第三方PV
    public static final String GET_DUALPOWER_OPEN = "get_dualpower_open";
    // 获取「负载」数据 V2
    public static final String GET_STATS_LOADUSAGE_V2 = "get_stats_loadusage_v2";
    // 获取「电池」功耗/能量 V2
    public static final String GET_STATS_BATTERY_V2 = "get_stats_battery_v2";
    // 获取「mppt」功耗/能量 V2
    public static final String GET_STATS_MPPT_V2 = "get_stats_mppt_v2";
    // 获取「收益」功耗/能量 V2
    public static final String GET_STATS_REVENUE_V2 = "get_stats_revenue_v2";
    // 获取环保评级 V2
    public static final String GET_STATS_ECO_V2 = "get_stats_eco_v2";

    //*************************************储能模式相关**********************************************
    // 获取当前储能模式(0xa016)
    public static final String GET_CURRENT_RESERVE_MODE = "get_current_reservemode";
    // 获取储能模式-价格跟踪设置(0xa017)
    public static final String GET_PRICE_TRACK_RESERVE_MODE = "get_pricetrack_reservemode";
    // 获取储能模式-定时设置(0xa018)
    public static final String GET_SCHEDULE_RESERVE_MODE = "get_schedule_reservemode";
    // 设置储能模式(0xa019)
    public static final String SET_RESERVE_MODE = "set_reservemode";
    // 获取电池充放电策略
    public static final String GET_CHARGING_DISCHARGING_PLANS = "get_charging_discharging_plans";
    // 获取电池充放电策略V2
    public static final String GET_CHARGING_DISCHARGING_PLANS_V2 = "get_charging_discharging_plans_v2";
    // 获取储能模式-覆盖AI(0xa046)
    public static final String GET_CUSTOM_SCHEDULEMODE = "get_custom_schedulemode";
    // 获取电价默认值/阈值（AI模式）
    public static final String GET_AIMODE_SETTINGS = "get_aimode_settings";

    //*************************************电网并网配置**********************************************
    // 获取电网并网配置(0xa034)
    public static final String GET_GRIDCONNECTION_CONFIG = "get_gridconnection_config";
    // 设置电网并网配置(0xa035)
    public static final String UPDATE_GRIDCONNECTION_CONFIG = "update_gridconnection_config";
    // 恢复默认电网并网配置(0xa036)
    public static final String RESUME_GRIDCONNECTION_CONFIG = "resume_gridconnection_config";

    //*************************************市电充电控制开关**********************************************
    public static final String GET_FEATURE = "get_feature";

    // 通过引索获取电池包单个信息(0xa044)
    public static final String GET_REGULATE_FREQUENCY_STATE = "get_regulatefrequency_state";
}
