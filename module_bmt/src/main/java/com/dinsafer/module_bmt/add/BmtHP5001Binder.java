package com.dinsafer.module_bmt.add;

import android.content.Context;

import com.dinsafer.dincore.common.CommonCmdEvent;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.module_bmt.bean.BmtConst;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

/**
 * HP5001添加器
 */
@Keep
public class BmtHP5001Binder extends BaseBmtBinder {

    public BmtHP5001Binder(@NonNull Context mContext) {
        super(mContext);
        UUID = "23593c00-69b8-419b-84f3-a3fe7a354cdb";
        WRITE_UUID = "23593c01-69b8-419b-84f3-a3fe7a354cdb";
        READ_UUID = "23593c02-69b8-419b-84f3-a3fe7a354cdb";
    }

    @Override
    protected void handleRegisterSuccess(JSONObject data, String model) throws JSONException {
        data.put("homeID", mHomeID);
        data.put("model", BmtConst.PROVIDER_BMT_HP5001);
        data.put("id", DDJSONUtil.getString(data, "pid"));
        MsctLog.i(TAG, data.toString());
        CommonCmdEvent commonCmdEvent = new CommonCmdEvent(CommonCmdEvent.CMD.BMT_ADD);
        commonCmdEvent.setExtra(data.toString());
        EventBus.getDefault().post(commonCmdEvent);
    }
}
