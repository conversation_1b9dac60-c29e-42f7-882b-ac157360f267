package com.dinsafer.module_bmt.add;

import com.dinsafer.dssupport.crypt.Encryption;
import com.dinsafer.dssupport.msctlib.msct.IConvert;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.dssupport.utils.HexUtil;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2022/12/12
 */
class BmtBleConvert implements IConvert {
    private static final String TAG = BmtBleConvert.class.getSimpleName();

    private String iv = "dfcf28d0734569a6";
    private String key = "fbade9e36a3f36d3d676c1b808451dd7";

    @Override
    public byte[] decode(byte[] msg) {
        if (msg == null) {
            return new byte[0];
        }
        DDLog.d(TAG, "-------------------------解密前--------------------------");
        DDLog.d(TAG, "| " + new String(msg));
        DDLog.d(TAG, "-------------------------解密后Hex--------------------------");
        try {
            byte[] result = Encryption.decryptAes(iv, key, msg);
            DDLog.d(TAG, "| " + HexUtil.bytesToHexString(result));
            DDLog.d(TAG, "--------------------------------------------------------");
            return result;
        } catch (Exception e) {
            DDLog.d(TAG, "| 加密失败");
            DDLog.d(TAG, "--------------------------------------------------------");
            return msg;
        }
    }

    @Override
    public byte[] encode(byte[] msg) {
        if (msg == null) {
            return new byte[0];
        }
        DDLog.d(TAG, "-------------------------加密前--------------------------");
        DDLog.d(TAG, "| " + new String(msg));
        DDLog.d(TAG, "-------------------------加密后Hex--------------------------");
        try {
            byte[] result = Encryption.encryptAes(iv, key, msg);
            DDLog.d(TAG, "| " + HexUtil.bytesToHexString(result));
            DDLog.d(TAG, "--------------------------------------------------------");
            return result;
        } catch (Exception e) {
            DDLog.d(TAG, "| 加密失败");
            DDLog.d(TAG, "--------------------------------------------------------");
            return msg;
        }
    }
}
