package com.dinsafer.module_bmt.add;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.dinsafer.dincore.common.CommonCmdEvent;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.module_bmt.bean.BmtConst;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * PS1-BAK10-HS10添加器
 */
@Keep
public class BmtPowerStoreBinder extends BaseBmtBinder {

    public BmtPowerStoreBinder(@NonNull Context mContext) {
        super(mContext);
        UUID = BmtConst.POWER_STORE_UUID;
        WRITE_UUID = "156DF971-1DD5-44D6-A381-F271821F6804";
        READ_UUID = "1C7D885F-E617-49E9-8E5D-6AC6D9ED8765";
    }

    @Override
    protected void handleRegisterSuccess(JSONObject data, String model) throws JSONException {
        data.put("homeID", mHomeID);
        data.put("model", TextUtils.isEmpty(model) ? BmtConst.PROVIDER_BMT_HP5000 : model);
        data.put("id", DDJSONUtil.getString(data, "pid"));
        MsctLog.i(TAG, data.toString());
        CommonCmdEvent commonCmdEvent = new CommonCmdEvent(CommonCmdEvent.CMD.BMT_ADD);
        commonCmdEvent.setExtra(data.toString());
        EventBus.getDefault().post(commonCmdEvent);
    }
}
