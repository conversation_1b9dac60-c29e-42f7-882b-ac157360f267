package com.dinsafer.module_bmt.add;

import android.bluetooth.le.<PERSON>an<PERSON>ord;
import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.clj.fastble.callback.BleScanCallback;
import com.clj.fastble.data.BleDevice;
import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.activtor.bean.Plugin;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.utils.BleHelper;
import com.dinsafer.dincore.utils.VersionCompare;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.msctlib.msct.IConvert;
import com.dinsafer.dssupport.msctlib.utils.MsctJSONUtil;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_bmt.bean.BmtBleWifiInfo;
import com.dinsafer.module_bmt.bean.BmtConst;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Method;
import java.math.BigInteger;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;

/**
 * Bmt添加器基类,蓝牙
 */
public abstract class BaseBmtBinder extends BasePluginBinder {

    private final String CHECK_VERSION = "v2.0.0-beta.12";
    private final int ERROR_CODE_ETHERNET = 4;
    protected String mSsid, mSsidPassword, mHomeID, mDataHost, mModel;
    protected boolean mAuth;

    private boolean isTimeOutToFindDevice = false;

    protected String UUID;
    protected String WRITE_UUID;
    protected String READ_UUID;
    private boolean isNeedRecovery = false;
    protected IConvert bmtBleConvert = new BmtBleConvert();

    private BleHelper.IMessageCallback callback = new BleHelper.IMessageCallback() {
        @Override
        public void onMessage(byte[] notifyData) {
            handlerMessage(notifyData);
        }
    };

    private IWifiListCallBack wifiListCallBack;
    private IModelCallback modelCallback;
    private IVersionCallback versionCallback;
    private String mVersion = "";


    @Keep
    public BaseBmtBinder(@NonNull Context mContext) {
        super(mContext);
    }


    @Override
    @Keep
    public void bindDevice(Plugin plugin) {
        //
        if (TextUtils.isEmpty(mSsid)) {
            callBackBindResult(ErrorCode.DEFAULT, "ssid is null");
            return;
        }

        if (mAuth && TextUtils.isEmpty(mSsidPassword)) {
            callBackBindResult(ErrorCode.DEFAULT, "password is null");
            return;
        }

        if (TextUtils.isEmpty(mHomeID)) {
            callBackBindResult(ErrorCode.DEFAULT, "homeID is null");
            return;
        }

        if (TextUtils.isEmpty(mModel)) {
            callBackBindResult(ErrorCode.DEFAULT, "mode is null");
            return;
        }

        if (isNeedRecovery) {
            setAppID();
        } else {
            setSsid();
        }
    }

    @Keep
    public void setSsid(String mSsid) {
        this.mSsid = mSsid;
    }

    @Keep
    public void setSsidPassword(String mSsidPassword) {
        this.mSsidPassword = mSsidPassword;
    }

    @Keep
    public void setAuth(boolean mAuth) {
        this.mAuth = mAuth;
    }

    @Keep
    public void setBindHomeID(String mHomeID) {
        this.mHomeID = mHomeID;
    }

    @Keep
    public void setDataHost(String dataHost) {
        this.mDataHost = dataHost;
    }

    @Keep
    public void setModel(String model) {
        this.mModel = model;
    }

    private void startConfigWifi() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", BmtBleCmd.SET_NET);
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void bindBmtDevice() {
        setAppID();
    }

    @Keep
    public void discoveryDevice(long timeout, BleScanCallback bleScanCallback) {
        BleHelper.getInstance().setScanRuleWithUUID(timeout, UUID, WRITE_UUID, READ_UUID);
        BleHelper.getInstance().scanDevice(bleScanCallback);
    }

    @Keep
    public void stopDiscoveryDevice() {
        BleHelper.getInstance().cancelScan();
    }

    @Keep
    public void connectDevice(BleDevice bmtDevice,
                              BleHelper.ConnectCallback connectCallback) {
        mVersion = "";
        isNeedRecovery = isNeedRecoveryDevice(bmtDevice);
        DDLog.d(TAG, "connectDevice-->isNeedRecovery:" + isNeedRecovery);
        BleHelper.getInstance().release();
        BleHelper.getInstance().addConnectCallBack(connectCallback);
        BleHelper.getInstance().addMessageCallBack(callback);
        BleHelper.getInstance().connected(bmtDevice, bmtBleConvert);
    }

    @Keep
    public void getWifiList() {
        wifiList.clear();
        supportPwdFreeNetwork = false;
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", "get_wifi_list");
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Keep
    public IWifiListCallBack getWifiListCallBack() {
        return wifiListCallBack;
    }

    @Keep
    public void setWifiListCallBack(IWifiListCallBack wifiListCallBack) {
        this.wifiListCallBack = wifiListCallBack;
    }

    @Keep
    public IModelCallback getModelCallback() {
        return modelCallback;
    }

    @Keep
    public void setModelCallback(IModelCallback modelCallback) {
        this.modelCallback = modelCallback;
    }

    @Keep
    public IVersionCallback getVersionCallback() {
        return versionCallback;
    }

    @Keep
    public void setVersionCallback(IVersionCallback versionCallback) {
        this.versionCallback = versionCallback;
    }


    @Keep
    public String getConnectedUUID() {
        return BleHelper.getInstance().getConnectedUUID();
    }

    @Keep
    public boolean isOldUUID() {
        String connectedUUID = getConnectedUUID();
        return !TextUtils.isEmpty(connectedUUID)
                && connectedUUID.equalsIgnoreCase(BmtConst.OLD_UUID);
    }

    @Keep
    public boolean isThreePointZero() {
        String connectedUUID = getConnectedUUID();
        return !TextUtils.isEmpty(connectedUUID)
                && connectedUUID.equalsIgnoreCase(BmtConst.THREE_POINT_ZERO_UUID);
    }

    @Keep
    public int compareVersion() {
        VersionCompare versionCompare = new VersionCompare(mVersion);
        VersionCompare checkVersion = new VersionCompare(CHECK_VERSION);
        return versionCompare.compareTo(checkVersion);
    }

    @Keep
    public boolean isOnePointZero() {
        String connectedUUID = getConnectedUUID();
        boolean isOnePointZero = !TextUtils.isEmpty(connectedUUID)
                && connectedUUID.equalsIgnoreCase(BmtConst.ONE_POINT_ZERO_UUID);
        if (isOnePointZero) {
            return true;
        } else {
            return !TextUtils.isEmpty(mVersion) && compareVersion() < 0;
        }
    }

    @Keep
    public boolean isOnePointFive() {
        String connectedUUID = getConnectedUUID();
        boolean isOnePointFive = !TextUtils.isEmpty(connectedUUID)
                && connectedUUID.equalsIgnoreCase(BmtConst.ONE_POINT_FIVE_UUID);
        if (isOnePointFive) {
            return true;
        } else {
            return !TextUtils.isEmpty(mVersion) && compareVersion() >= 0;
        }
    }

    @Keep
    public boolean isTwoPointZero() {
        String connectedUUID = getConnectedUUID();
        return !TextUtils.isEmpty(connectedUUID)
                && connectedUUID.equalsIgnoreCase(BmtConst.TWO_POINT_ZERO_UUID);
    }

    private List<BmtBleWifiInfo> wifiList = new ArrayList<>();
    private boolean supportPwdFreeNetwork;

    private void handlerMessage(byte[] notifyData) {
        String js = new String(notifyData);
        MsctLog.i(TAG, "handler rece:" + js);
        try {
            JSONObject jsonObject = new JSONObject(js);
            String cmd = MsctJSONUtil.getString(jsonObject, "cmd");
            int status = 0;
            switch (cmd) {
                case BmtBleCmd.GET_VERSION:
                    status = MsctJSONUtil.getInt(jsonObject, "status");
                    mVersion = MsctJSONUtil.getString(jsonObject, "result");
                    if (versionCallback != null) {
                        versionCallback.onVersion(BmtBleCmd.GET_VERSION, status, mVersion);
                    }
                    break;

                case BmtBleCmd.GET_VALID_MODELS:
                    status = MsctJSONUtil.getInt(jsonObject, "status");
                    String result = MsctJSONUtil.getString(jsonObject, "result");
                    if (modelCallback != null) {
                        modelCallback.onModelCallback(BmtBleCmd.GET_VALID_MODELS, status, result);
                    }
                    break;

                case BmtBleCmd.GET_MODEL:
                    status = MsctJSONUtil.getInt(jsonObject, "status");
                    if (modelCallback != null) {
                        modelCallback.onModelCallback(BmtBleCmd.GET_MODEL, status, MsctJSONUtil.getString(jsonObject, "result"));
                    }
                    break;

                case BmtBleCmd.GET_ETHERNET_STATE:
                    status = MsctJSONUtil.getInt(jsonObject, "status");
                    callBackEthernetResult(status, MsctJSONUtil.getInt(jsonObject, "result"));
                    break;

                case BmtBleCmd.GET_WIFI_LIST:
                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
                        if (wifiListCallBack != null) {
                            wifiListCallBack.onWifiListCallBack(wifiList, supportPwdFreeNetwork);
                        }
                    } else {
                        final int rssi = MsctJSONUtil.getInt(jsonObject, "rssi");
                        final String ssid = MsctJSONUtil.getString(jsonObject, "result");
                        BmtBleWifiInfo info = new BmtBleWifiInfo(ssid, rssi);
                        if (MsctJSONUtil.has(jsonObject, "auth")) {
                            final boolean auth = MsctJSONUtil.getBoolean(jsonObject, "auth");
                            info.setAuth(auth);
                            supportPwdFreeNetwork = true;
                        }
                        wifiList.add(info);
                    }
                    break;
                case BmtBleCmd.SET_WIFI_NAME:
                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
                        setSsidPW();
                    } else {
                        callBackBindResult(ErrorCode.DEFAULT, "wifi ssid error");
                    }
                    break;
                case BmtBleCmd.SET_WIFI_PASSWORD:
                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
                        startConfigWifi();
                    } else {
                        callBackBindResult(ErrorCode.DEFAULT, "wifi ssid error");
                    }
                    break;
                case BmtBleCmd.SET_NET:
                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
                        if (isNeedRecovery) {
                            callBackBindResult(3, null);
                        } else {
                            bindBmtDevice();
                        }
                    } else {
                        callBackBindResult(ErrorCode.DEFAULT, "wifi connect error");
                    }
                    break;
                case BmtBleCmd.SET_APP_ID:
                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
                        setAppSecret();
                    } else {
                        callBackBindResult(ErrorCode.DEFAULT, "wifi appid error");
                    }
                    break;
                case BmtBleCmd.SET_APP_SECRET:
                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
                        setHTTPHost();
                    } else {
                        callBackBindResult(ErrorCode.DEFAULT, "wifi sec error");
                    }
                    break;
                case BmtBleCmd.SET_HTTP_HOST:
                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
                        setDataHost();
                    } else {
                        callBackBindResult(ErrorCode.DEFAULT, "wifi udp error");
                    }
                    break;
                case BmtBleCmd.SET_DATA_HOST:
                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
                        if (isNeedRecovery) {
                            setSsid();
                        } else {
                            setHomeID();
                        }
                    } else {
                        callBackBindResult(ErrorCode.DEFAULT, "wifi udp error");
                    }
                    break;
                case BmtBleCmd.SET_HOME:
                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
                        if (isOldUUID() && isOnePointZero()) {
                            register();
                        } else {
                            setModel();
                        }
                    } else {
                        callBackBindResult(ErrorCode.DEFAULT, "wifi homeid error");
                    }
                    break;
                case BmtBleCmd.SET_MODEL:
                    status = MsctJSONUtil.getInt(jsonObject, "status");
                    if (status == 1) {
                        register();
                    }
                    if (modelCallback != null) {
                        modelCallback.onModelCallback(BmtBleCmd.SET_MODEL, status, "");
                    }
                    break;
                case BmtBleCmd.REGISTER:
                    if (MsctJSONUtil.getInt(jsonObject, "status") == 1) {
                        handleRegisterSuccess(jsonObject, mModel);
                        callBackBindResult(1, MsctJSONUtil.getString(jsonObject, "pid"));
                    } else {
                        callBackBindResult(MsctJSONUtil.getInt(jsonObject, "status"), "bind ipc error");
                    }
                    mVersion = "";
                    break;
                case BmtBleCmd.SET_TIMEZONE:
                    callBackBindResult(21, "set timezone success");
                    updateConfig();
                    break;
                case BmtBleCmd.UPDATE_CONFIG:
                    callBackBindResult(22, "update config success");
                    break;
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    protected abstract void handleRegisterSuccess(JSONObject data, String model) throws JSONException;

    private void setSsid() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", BmtBleCmd.SET_WIFI_NAME);
            jsonObject.put("data", mSsid);
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void setSsidPW() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", BmtBleCmd.SET_WIFI_PASSWORD);
            jsonObject.put("data", mSsidPassword);
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void setAppID() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", BmtBleCmd.SET_APP_ID);
            jsonObject.put("data", DinCore.getInstance().getAppID());
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void setAppSecret() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", BmtBleCmd.SET_APP_SECRET);
            jsonObject.put("data", DinCore.getInstance().getAppSecret());
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void setHTTPHost() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", BmtBleCmd.SET_HTTP_HOST);
            jsonObject.put("data", DinCore.getInstance().getDomain());
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void setDataHost() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", BmtBleCmd.SET_DATA_HOST);
            jsonObject.put("data", mDataHost);
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void setHomeID() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", BmtBleCmd.SET_HOME);
            jsonObject.put("data", mHomeID);
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void setModel() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", BmtBleCmd.SET_MODEL);
            jsonObject.put("data", mModel);
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void register() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", BmtBleCmd.REGISTER);
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void setTimeZone() {
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("GMT"),
                Locale.getDefault());
        Date currentLocalTime = calendar.getTime();

        DateFormat date = new SimpleDateFormat("ZZZZZ", Locale.getDefault());
        String localTime = date.format(currentLocalTime);
        localTime = "tzn" + localTime + ":00";
        DDLog.i("timezone", localTime);

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", BmtBleCmd.SET_TIMEZONE);
            jsonObject.put("tz", localTime);
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }


    private void updateConfig() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", BmtBleCmd.UPDATE_CONFIG);
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Keep
    public void getVersion() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", BmtBleCmd.GET_VERSION);
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Keep
    public void getModel() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", BmtBleCmd.GET_MODEL);
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Keep
    public void getEthernetState() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", BmtBleCmd.GET_ETHERNET_STATE);
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Keep
    public void ethernetConnect() {
        bindBmtDevice();
    }

    @Keep
    public void getValidModels() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("cmd", BmtBleCmd.GET_VALID_MODELS);
            BleHelper.getInstance().write(jsonObject, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Keep
    public float getHardwareVersion() {
        if (isOnePointZero()) {
            return 1.0f;
        } else if (isOnePointFive()) {
            return 1.5f;
        } else if (isTwoPointZero()) {
            return 2.0f;
        } else if (isThreePointZero()) {
            return 3.0f;
        } else {
            return 0;
        }
    }

    @Keep
    public void syncConfig() {
        setTimeZone();
    }

    /**
     * 是否是变砖的IPC
     *
     * @param bleDevice
     * @return
     */
    private boolean isNeedRecoveryDevice(BleDevice bleDevice) {
        int dataByIndex = getDataByIndex(bleDevice, 1);
        return dataByIndex == 1;
    }

    /**
     * 通过ScanRecord,获取到他的ServiceData，获取到前两位
     * 第一个 1代表新的未被添加过或已被Reset的IPC  0代表旧的已被添加或配网的IPC
     * 第二位 1代表变砖 0代表正常
     */
    private int getDataByIndex(final BleDevice bleDevice, final int index) {
        int result = -1;
        try {
            ScanRecord scanRecord = parseScanRecordFromBytes(bleDevice.getScanRecord());
            if (scanRecord.getServiceData().size() <= 0) {
                return result;
            }
            byte[] data = scanRecord.getServiceData().entrySet().iterator().next().getValue();
            char[] dataChar = String.format("%08d", Integer.valueOf(new BigInteger(1, data).toString(2))).toCharArray();
            DDLog.d(TAG, "service data is" + Arrays.toString(dataChar));
            result = Character.getNumericValue(dataChar[index]);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on get panel data by index: " + index);
            e.printStackTrace();
        }
        return result;
    }

    @Keep
    private ScanRecord parseScanRecordFromBytes(byte[] bytes) {
        try {
            Method parseFromBytes = ScanRecord.class.getMethod("parseFromBytes", byte[].class);
            return (ScanRecord) parseFromBytes.invoke(null, (Object) bytes);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void stop() {
        super.stop();
        stopDiscoveryDevice();
        BleHelper.getInstance().disconnectAllDevice();
    }

    @Override
    @Keep
    public void destroyBinder() {
        super.destroyBinder();
        wifiListCallBack = null;
        BleHelper.getInstance().release();
        BleHelper.getInstance().disconnectAllDevice();
    }

    @Keep
    public interface IWifiListCallBack {
        void onWifiListCallBack(List<BmtBleWifiInfo> wifis, boolean supportPwdFreeNetwork);
    }

    @Keep
    public interface IModelCallback {
        void onModelCallback(String cmd, int status, String result);
    }

    @Keep
    public interface IVersionCallback {
        void onVersion(String cmd, int status, String version);
    }

    @Keep
    public interface IEthernetCallback {
        void onEthernetCallback(String cmd, int status, int ethernetState);
    }
}
