package com.dinsafer.module_bmt;

import androidx.annotation.Keep;

@Keep
public class BmtDataKey {
    public final static String CMD = "cmd";
    public final static String ERROR_MESSAGE = "errorMessage";
    public final static String INTERVAL = "interval";
    public final static String OFFSET = "offset";
    public final static String GET_REAL = "get_real";
    public final static String QUERY_INTERVAL = "query_interval";
    public final static String START_TIME = "start_time";
    public final static String SUNRISES = "sunrises";
    public final static String SUNSETS = "sunsets";
    public final static String TIMEZONE = "timezone";
    public final static String UNIT_PRICE = "unit_price";
    public final static String USER_PRICES = "user_prices";
    public final static String DATA = "data";
    public final static String GMTTIME = "gmtime";
    public final static String B_SENSOR_INSTALLED = "b_sensor_installed";
    public final static String VERT_BATTERY_EXCEPTIONS = "vertBatteryExceptions";
    public final static String VERT_EXCEPTIONS = "vertExceptions";
    public final static String VERT_GRID_EXCEPTIONS = "vertGridExceptions";
    public final static String VERT_SYSTEM_EXCEPTIONS = "vertSystemExceptions";
    public final static String VERT_MPPT_EXCEPTIONS = "vertmpptExceptions";
    public final static String VERT_PRESENT_EXCEPTIONS = "vertPresentExceptions";
    public final static String VERT_DC_EXCEPTIONS = "vertDCExceptions";
    public final static String FAN_STATE = "fanState";
    public final static String ID_INFO = "idInfo";
    public final static String VERSION = "version";
    public final static String INDEX = "index";
    public final static String INDEX_S = "indexs";
    public final static String STATE = "state";
    public final static String EXCEPTIONS = "exceptions";
    public final static String WIFI = "wifi";
    public final static String CELLULAR = "cellular";
    public final static String EV = "ev";
    public final static String MPPT = "mppt";
    public final static String CABINET = "cabinet";
    public final static String BATTERY = "battery";
    public final static String SYSTEM = "system";
    public final static String COMMUNICATION = "communication";
    public final static String HEATING = "heating";
    public final static String HEAT_AVAILABLE = "heatAvailable";
    public final static String VALUE_0 = "0";
    public final static String VALUE_1 = "1";
    public final static String VALUE_2 = "2";
    public final static String VALUE_3 = "3";
    public final static String CITY_SOURCE_ON = "citySourceOn";
    public final static String CITY_SOURCE = "citySource";
    public final static String BATTERY_ON = "batteryOn";
    public final static String MPPT_ON = "mpptOn";
    public final static String EV_ON = "evOn";
    public final static String FUSE_POWER_CAPS = "fuse_power_caps";
    public final static String B_SENSOR_INPUT_ON = "BSensorInputOn";
    public final static String B_SENSOR_INPUT = "BSensorInput";
    public final static String COUNT = "count";
    public final static String HIGH_VOLT = "highVolt";
    public final static String LOW_VOLT = "lowVolt";
    public final static String TOTAL_VOLT = "totalVolt";
    public final static String BMS_LOW_TEMP = "bmsLowTemp";
    public final static String BMS_HIGH_TEMP = "bmsHighTemp";
    public final static String ELECTRODE_LOW_TEMP = "electrodeLowTemp";
    public final static String ELECTRODE_HIGH_TEMP = "electrodeHighTemp";
    public final static String SOC = "soc";
    public final static String AMPERE = "ampere";
    public final static String CUR_POWER = "curPower";
    public final static String FULL_POWER = "fullPower";
    public final static String REMAIN_TIME = "remainTime";
    public final static String CHARGE_TIME = "chargeTime";
    public final static String ON = "on";
    public final static String B_SENSOR_OUTPUT_ON = "BSensorOutputOn";
    public final static String B_SENSOR_OUTPUT = "BSensorOutput";
    public final static String INVERTER_IN_ON = "inverterInOn";
    public final static String INVERTER_IN = "inverterIn";
    public final static String OTHER_OUT_ON = "otherOutOn";
    public final static String OTHER_OUT = "otherOut";
    public final static String MODE = "mode";
    public final static String WIFI_NAME = "wifiName";
    public final static String IP = "ip";
    public final static String MAC = "mac";
    public final static String STRATEGY_TYPE = "strategyType";
    public final static String SMARTRESERVE = "smartReserve";
    public final static String EMERGENCYRESERVE = "emergencyReserve";
    public final static String GOOD_PRICE_PERCENTAGE = "goodPricePercentage";
    public final static String ACCEPTABLE_PRICE_PERCENTAGE = "acceptablePricePercentage";
    public final static String OTHER_ON = "otherOn";
    public final static String DISCHARGE_SWITCH_ON = "dischargeSwitchOn";
    public final static String CHARGE_SWITCH_ON = "chargeSwitchOn";
    public final static String BMS_TEMP = "bmsTemp";
    public final static String ELECTRODE_A_TEMP = "electrodeATemp";
    public final static String ELECTRODE_B_TEMP = "electrodeBTemp";
    public final static String VOLTAGE = "voltage";
    public final static String RELEASE_TIMES = "releaseTimes";
    public final static String HEALTHY = "healthy";
    public final static String BARCODE = "barcode";
    public final static String CABINET_INDEX = "cabinetIndex";
    public final static String CABINET_POSITION_INDEX = "cabinetPositionIndex";
    public final static String CAPACITY = "capacity";
    public final static String WATER_STATE = "waterState";
    public final static String SMOKE_STATE = "smokeState";
    public final static String IS_ADD = "isAdd";
    public final static String LIGHT_STATE = "lightState";
    public final static String VALUE_0_ON = "0On";
    public final static String VALUE_1_ON = "1On";
    public final static String VALUE_2_ON = "2On";
    public final static String PROGRESS = "progress";
    public final static String FAILED = "failed";
    public final static String ERROR = "error";
    public final static String STATUS = "status";
    public final static String CYCLE_TYPE = "cycleType";
    public final static String SOC_STATE = "socState";
    public final static String RESERVE_MODE = "reserveMode";
    public final static String SMART = "smart";
    public final static String EMERGENCY = "emergency";
    public final static String LOW_POWER_ALERT = "lowpowerAlert";
    public final static String BATTERY_PROTECT = "batteryProtect";
    public final static String ABS_ZERO_EC_PRICES = "abs_zero_ec_prices";
    public final static String C_1 = "c1";
    public final static String C_2 = "c2";
    public final static String C_3 = "c3";
    public final static String CONDITIONS = "conditions";
    public final static String S_1 = "s1";
    public final static String S_2 = "s2";
    public final static String WEEKDAYS = "weekdays";
    public final static String WEEKEND = "weekend";
    public final static String DETAIL_STATE = "detailState";
    public final static String EV_CHARGING_MODE = "evChargingMode";
    public final static String ADVANCE_STATUS = "advanceStatus";
    public final static String BATTERY_CHARGED = "batteryCharged";
    public final static String IGNORE_EXCEPTION = "ignoreException";
    public final static String IS_PRICE_TRACKING_SUPPORTED = "isPriceTrackingSupported";
    public final static String COUNTRY_CODE = "country_code";
    public final static String COUNTRY_NAME = "country_name";
    public final static String CITY_NAME = "city_name";
    public final static String BATTERY_WAT = "batteryWat";
    public final static String SOLAR_WAT = "solarWat";
    public final static String GRID_WAT = "gridWat";
    public final static String VECHI_WAT = "vechiWat";
    public final static String OTHER_LOAD_WAT = "otherLoadWat";
    public final static String ADDITIONAL_LOAD_WAT = "additionLoadWat";
    public final static String SYNC = "sync";
    public final static String WIF_RSSI = "wifiRSSI";
    public final static String CELLULAR_RSSI = "cellularRSSI";

    public final static String ETHERNET = "ethernet";
    public final static String IP2_WAT = "ip2Wat";
    public final static String OP2_WAT = "op2Wat";
    public final static String OPEN = "open";
    public final static String FIXED = "fixed";
    public final static String FIXED_FULL = "fixedFull";
    public final static String DELAY = "delay";
    public final static String PRICE_PERCENT = "pricePercent";
    public final static String DATA_MODE = "dataMode";

    public final static String POWER_PERCENT = "powerPercent";
    public final static String APP_FAST_POWER_DOWN = "appFastPowerDown";
    public final static String QU_MODE = "quMode";
    public final static String COS_MODE = "cosMode";
    public final static String ANTI_ISLANDING_MODE = "antiislandingMode";
    public final static String HIGH_LOW_MODE = "highlowMode";
    public final static String ACTIVE_SOFT_START_RATE = "activeSoftStartRate";
    public final static String OVER_FREQUENCY_LOAD_SHEDDING = "overfrequencyLoadShedding";
    public final static String OVER_FREQUENCY_LOAD_SHEDDING_SLOPE = "overfrequencyLoadSheddingSlope";
    public final static String UNDER_FREQUENCY_LOAD_SHEDDING = "underfrequencyLoadShedding";
    public final static String UNDER_FREQUENCY_LOAD_SHEDDING_SLOPE = "underfrequencyLoadSheddingSlope";
    public final static String POWER_FACTOR = "powerFactor";
    public final static String RECONNECT_TIME = "reconnectTime";
    public final static String GRID_OVER_VOLTAGE_1 = "gridOvervoltage1";
    public final static String GRID_OVER_VOLTAGE_1_PROTECT_TIME = "gridOvervoltage1ProtectTime";
    public final static String GRID_OVER_VOLTAGE_2 = "gridOvervoltage2";
    public final static String GRID_OVER_VOLTAGE_2_PROTECT_TIME = "gridOvervoltage2ProtectTime";
    public final static String GRID_UNDER_VOLTAGE_1 = "gridUndervoltage1";
    public final static String GRID_UNDER_VOLTAGE_1_PROTECT_TIME = "gridUndervoltage1ProtectTime";
    public final static String GRID_UNDER_VOLTAGE_2 = "gridUndervoltage2";
    public final static String GRID_UNDER_VOLTAGE_2_PROTECT_TIME = "gridUndervoltage2ProtectTime";
    public final static String GRID_OVER_FREQUENCY_1 = "gridOverfrequency1";
    public final static String GRID_OVER_FREQUENCY_2 = "gridOverfrequency2";
    public final static String GRID_OVER_FREQUENCY_PROTECT_TIME = "gridOverfrequencyProtectTime";
    public final static String GRID_UNDER_FREQUENCY_1 = "gridUnderfrequency1";
    public final static String GRID_UNDER_FREQUENCY_2 = "gridUnderfrequency2";
    public final static String GRID_UNDER_FREQUENCY_PROTECT_TIME = "gridUnderfrequencyProtectTime";
    public final static String RECONNECT_SLOPE = "reconnectSlope";
    public final static String ELEC_SUPPORT = "elec_support";
    public final static String GRID_CONN_SUPPORT = "grid_conn_support";
    public final static String REBOOT_GRID_0VER_VOLTAGE = "rebootGridOvervoltage";
    public final static String REBOOT_GRID_UNDER_VOLTAGE = "rebootGridUndervoltage";
    public final static String REBOOT_GRID_OVER_FREQUENCY = "rebootGridOverfrequency";
    public final static String REBOOT_GRID_UNDER_FREQUENCY = "rebootGridUnderfrequency";
    public final static String GRID_VALID = "gridValid";
    public final static String BSENSOR_VALID = "bsensorValid";
    public final static String TYPE = "type";
    public final static String UNIT = "unit";
    public final static String UNIT_APP = "unit_app";
    public final static String SOLAR_EFFICIENCY = "solarEfficiency";
    public final static String FUSE_POWER_HIGHER = "higher";
    public final static String FUSE_POWER_LOWER = "lower";
    public final static String FUSE_POWER_MEDIUM = "medium";
    public final static String POWER_CAP = "power_cap";
    public final static String SPEC = "spec";
    public final static String CITYS = "citys";
    public final static String GRID_TO_BATTERY = "grid_to_battery";
    public final static String THIRD_PARTY_PV_ON = "thirdpartyPVOn";
    public final static String DUAL_POWER_WAT = "dualPowerWat";
    public final static String VERT_STATES = "vertStates";
    public final static String POLICY = "policy";
    public final static String FLAG = "flag";
    public final static String IS_OPEN = "isOpen";
    public final static String PREVIOUS_STATUS = "previous_status";
    public final static String IOTVERSION = "iotVersion";

    public final static String DELIVERY_AREAS = "delivery_areas";
    public final static String GRID_STATUS = "gridStatus";
    public final static String FREQUENCY_MODULATION_STATUS = "frequencyModulationStatus";
    public final static String ELECTRICITY_PRICE_PERCENTS = "electricity_price_percents";
    public final static String FORECAST_SOLARS = "forecast_solars";
    public final static String HOPE_CHARGE_DISCHARGES = "hope_charge_discharges";
    public final static String MARKET_PRICES = "market_prices";
    public final static String PLANS = "plans";
    public final static String RELATIVE_PRICE_NORMS = "relative_price_norms";
    public final static String WEATHER_CONDITIONS = "weather_conditions";

    public final static String SMART_RESERVE = "smart_reserve";
    public final static String EMERGENCY_RESERVE = "emergency_reserve";
    public final static String AI = "ai";
    public final static String SCHEDULED = "scheduled";

    public final static String LATITUDE = "latitude";
    public final static String LONGITUDE = "longitude";

    public final static String HARDWARE_VERSION = "hardware_version";
    public final static String ETHERNET_IP = "ethernet_ip";
    public final static String ETHERNET_MAC = "ethernet_mac";

    public final static String PV_SUPPLY_CUSTOMIZATION_SUPPORTED = "pvSupplyCustomizationSupported";

    public final static String TOGGLE_STATUS = "toggleStatus";
    public final static String REQUEST_RESTART = "requestRestart";
    public final static String SHOULD_RESTART = "shouldRestart";
}
