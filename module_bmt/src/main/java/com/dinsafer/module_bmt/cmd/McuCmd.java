package com.dinsafer.module_bmt.cmd;

public class McuCmd {

    //*************************************逆变器相关*************************************************
    //=读逆变器信息(0x1004)
    public static final short GET_INVERTER_INFO = 0x1004;
    //=获取逆变器的输入信息(0x1002)
    public static final short GET_INVERTER_INPUT_INFO = 0x1002;
    //=逆变器的控制(0x0001)
    public static final short SET_INVERTER_OPEN = 0x0001;
    //=获取逆变器的输出信息(0x1001)
    public static final short GET_INVERTER_OUTPUT_INFO = 0x1001;
    //逆变器异常推送(0x2002)
    public static final short INVERTER_EXCEPTION = 0x2002;
    //=获取全局电流流向负载信息(0xa030)
    public static final short GET_GLOBAL_CURRENT_FLOW_INFO = (short) 0xa030;
    //重启逆变器(0xa032)
    public static final short RESET_INVERTER = (short) 0xa032;

    //*************************************电池包相关*************************************************
    //=获取电池包总信息(0x1005)
    public static final short GET_BATTERY_ALLINFO = 0x1005;
    //=设置电池包总“OFF”(0x0002)
    public static final short SET_BATTERY_ALLOFF = 0x0002;
    //=电池包单个引索变化通知(0x2001)
    public static final short BATTERY_INDEX_CHANGED = 0x2001;
    //=获取电池包单个信息(0x1006)
    public static final short GET_BATTERY_INFO = 0x1006;
    //通过索引读电池包配件状态(0x100c)
    public static final short GET_BATTERY_ACCESSORYSTATE = 0x100c;
    //=电池包配件状态变化事件(0x2006)
    public static final short BATTERY_ACCESSORYSTATE_CHANGED = 0x2006;
    //=电池包异常推送(0x2003)
    public static final short BATTERY_EXCEPTION = 0x2003;

    //=电池包状态信息通知
    public static final short BATTERY_STATUSINFO_NOTIFY = (short) 0xa014;
    //=电池包配件状态变化事件自定义版本
    public static final short BATTERY_ACCESSORYSTATE_CHANGED_CUSTOM = (short) 0xa015;

    //*************************************全局******************************************************
    //=读全局负载状态(0x1007)
    public static final short GET_GLOBALLOAD_STATE = 0x1007;
    //=读全局异常(0x1000)
    public static final short GET_GLOBAL_EXCEPTIONS = 0x1000;
    //=系统过功率保护事件(0x200a)
    public static final short SYSTEM_EXCEPTION = 0x200a;
    //=通信故障事件(0x200b)
    public static final short COMMUNICATION_EXCEPTION = 0x200b;
    // 设置忽略异常状态(0xa028)
    public static final short SET_EXCEPTION_IGNORE = (short) 0xa028;
    //=读32大佬的全局异常(0xa033)
    public static final short GET_VIEW_EXCEPTIONS = (short) 0xa033;
    //=读设备固件版本(0xa037)
    public static final short GET_FIRMWARES = (short) 0xa037;
    //=设置主保险丝规格(0xa038)
    public static final short SET_FUSE_SPECS = (short) 0xa038;
    //=获取主保险丝规格(0xa039)
    public static final short GET_FUSE_SPECS = (short) 0xa039;
    //=通知IoT同步需要的数据(0xa044)
    public static final short SYNC_CONF = (short) 0xa044;
    //=获取所有 feature 支持(0xa052)
    public static final short GET_ALL_SUPPORT_FEATURES = (short) 0xa052;

    /**
     * 通过MSCT设置国家/城市(0xa024)
     */
    public static final short SET_REGION = (short) 0xa024;

    //*************************************MCU******************************************************
    //=读MCU信息(0x1003)
    public static final short GET_MCU_INFO = 0x1003;

    //*************************************机柜******************************************************
    //=通过索引读机柜配件状态(0x100d)
    public static final short GET_CABINET_STATE = 0x100d;
    //=机柜配件状态变化事件(0x2007)
    public static final short CABINET_STATE_CHANGED = 0x2007;
    //=读机柜信息(0x100e)
    public static final short GET_CABINET_ALLINFO = 0x100e;
    //=机柜引索变化通知(0x2008)
    public static final short CABINET_INDEX_CHANGED = 0x2008;
    //=机柜异常事件(0x2009)
    public static final short CABINET_EXCEPTION = 0x2009;

    //*************************************MPPT*****************************************************
    //=读MPPT状态(0x1008)
    public static final short GET_MPPT_STATE = 0x1008;
    //=MPPT异常推送(0x2004)
    public static final short MPPT_EXCEPTION = 0x2004;
    //=获取第三方PV配置(0xa040)
    public static final short GET_THIRDPARTYPV_INFO = (short) 0xa040;
    //=设置第三方pv开关(0xa041)
    public static final short SET_THIRD_PARTYPV_ON = (short) 0xa041;

    //*************************************EV*******************************************************
    //=读EV状态(0x100a)
    public static final short GET_EV_STATE = 0x100a;
    //=EV异常推送(0x2005)
    public static final short EV_EXCEPTION = 0x2005;
    // 获取EV充电模式(0xa020)
    public static final short GET_CURRENT_EV_CHARGING_MODE = (short) 0xa020;
    // 获取EV充电模式-预设时间设置(0xa021)
    public static final short GET_EV_CHARGING_MODE_SCHEDULE = (short) 0xa021;
    // 设置EV充电模式(0xa022)
    public static final short SET_EV_CHARGING_MODE = (short) 0xa022;
    //=获取EV充电高级状态(0xa025)
    public static final short GET_CURRENT_EVADVANCESTATUS = (short) 0xa025;
    //=EV充电高级状态改变推送(0xa026)
    public static final short EV_ADVANCESTATUS_CHANGED = (short) 0xa026;
    //=获取EV充电状态信息(0xa027)
    public static final short GET_EVCHARGING_INFO = (short) 0xa027;
    //=退出EV即时充电模式(0xa029)
    public static final short SET_EVCHARGINGMODE_INSTANTCHARGE = (short) 0xa029;
    //=设置EV充电模式配置（即时充电下）(0xa031)
    public static final short SET_EVCHARGINGMODE_INSTANT = (short) 0xa031;
    // 获取智能 EV 调控状态(0xa050)
    public static final short GET_SMART_EV_STATUS = (short) 0xa050;
    // 设置智能 EV 调控状态(0xa051)
    public static final short SET_SMART_EV_STATUS = (short) 0xa051;

    //*************************************策略*******************************************************
    //=紧急充电设置(0xa001)
    public static final short SET_EMERGENCY_CHARGE = (short) 0xa001;
    //=获取紧急充电设置(0xa002)
    public static final short GET_EMERGENCY_CHARGE = (short) 0xa002;
    //=充电策略设置(0xa003)
    public static final short SET_CHARGE_STRATEGIES = (short) 0xa003;
    //=获取充电策略设置(0xa004)
    public static final short GET_CHARGE_STRATEGIES = (short) 0xa004;

    // 打开虚拟电厂(0xa005)
    public static final short SET_VIRTUAL_POWER_PLANT = (short) 0xa005;
    // 获取虚拟电厂设置(0xa006)
    public static final short GET_VIRTUAL_POWER_PLANT = (short) 0xa006;
    // 获取通讯信号强度(0xa007)
    public static final short GET_COMMUNICATE_SIGNAL = (short) 0xa007;

    // 请求重置设备(0xa008)
    public static final short RESET = (short) 0xa008;
    // 重置设备(0xa009)
    public static final short RESET_DEVICE_DATA = (short) 0xa009;
    // 获取IOT信息(0xa010)
    public static final short GET_ADVANCE_INFO = (short) 0xa010;
    // 重新启动逆变器(0xa011)
    public static final short REBOOT_INVERTER = (short) 0xa011;
    // 获取当前模式(0x1009)
    public static final short GET_MODE = (short) 0x1009;
    // 获取当前模式(0xa043)
    public static final short GET_MODE_V2 = (short) 0xa043;
    // 获取 PV 供应优先级(0xa047)
    public static final short GET_PV_DIST = (short) 0xa047;
    // 设置 PV 供应优先级(0xa048)
    public static final short SET_PV_DIST = (short) 0xa048;

    //************************************* 升级 **********************************************
    // 获取IOT管理的芯片升级状态(0xa011)
    public static final short GET_CHIPS_STATUS = (short) 0xa011;
    // 获取IOT管理的芯片升级进度(0xa012)
    public static final short GET_CHIPS_UPDATE_PROGRESS = (short) 0xa012;
    // 通知IOT升级芯片(0xa013)
    public static final short UPDATE_CHIPS = (short) 0xa013;

    //*************************************储能模式相关**********************************************
    // 获取当前储能模式(0xa016)
    public static final short GET_CURRENT_RESERVE_MODE = (short) 0xa016;
    // 获取储能模式-价格跟踪设置(0xa017)
    public static final short GET_PRICE_TRACK_RESERVE_MODE = (short) 0xa017;
    // 获取储能模式-定时设置(0xa018)
    public static final short GET_SCHEDULE_RESERVE_MODE = (short) 0xa018;
    // 设置储能模式(0xa019)
    public static final short SET_RESERVE_MODE = (short) 0xa019;
    // 获取储能模式-覆盖AI(0xa046)
    public static final short GET_CUSTOM_SCHEDULEMODE = (short) 0xa046;

    //*************************************电网并网配置**********************************************
    // 获取电网并网配置(0xa034)
    public static final short GET_GRIDCONNECTION_CONFIG = (short) 0xa034;
    // 设置电网并网配置(0xa035)
    public static final short UPDATE_GRIDCONNECTION_CONFIG = (short) 0xa035;
    // 恢复默认电网并网配置(0xa036)
    public static final short RESUME_GRIDCONNECTION_CONFIG = (short) 0xa036;

    // 获取当前调频状态(0xa045)
    public static final short GET_REGULATE_FREQUENCY_STATE = (short) 0xa045;
}
