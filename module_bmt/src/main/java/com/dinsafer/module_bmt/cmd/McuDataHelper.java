package com.dinsafer.module_bmt.cmd;

import com.dinsafer.dincore.utils.BinaryUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2022/12/7
 */
class McuDataHelper {
    static final int STATUS_ERROR_DEF = -1;

    /**
     * 解析逆变器的状态
     *
     * @param
     * @return {{@link #STATUS_ERROR_DEF}} 表示错误
     */
    public static int parseInverterState(byte state) {
        Mcu.Inverter.InverterState resultState = Mcu.Inverter.InverterState.findByState(state);
        if (null == resultState) {
            return STATUS_ERROR_DEF;
        }
        return resultState.getCode();
    }

    /**
     * 解析逆变器的风扇状态
     *
     * @return {{@link #STATUS_ERROR_DEF}} 表示错误
     */
    public static int parseInverterFanState(byte state) {
        Mcu.Inverter.InverterFanState resultState = Mcu.Inverter.InverterFanState.findByState(state);
        if (null == resultState) {
            return STATUS_ERROR_DEF;
        }
        return resultState.getCode();
    }

    /**
     * 解析逆变电池端故障码
     *
     * @param module
     * @return
     */
    public static List<Integer> parseInverterBatteryException(int module) {
        final List<Integer> exceptionList = new ArrayList<>();
        for (int i = 0; i < Mcu.Inverter.InverterBatteryException.values().length; i++) {
            if (BinaryUtils.getBitValue(module, i) == 1) {
                Mcu.Inverter.InverterBatteryException exception = Mcu.Inverter.InverterBatteryException.findByIndex(i);
                if (null != exception) {
                    exceptionList.add(exception.getCode());
                }
            }
        }
        return exceptionList;
    }

    /**
     * 解析逆变器异常数据
     *
     * @param module
     * @return
     */
    public static List<Integer> parseInverterException(int module) {
        final List<Integer> exceptionList = new ArrayList<>();
        for (int i = 0; i < Mcu.Inverter.InverterException.values().length; i++) {
            if (BinaryUtils.getBitValue(module, i) == 1) {
                Mcu.Inverter.InverterException exception = Mcu.Inverter.InverterException.findByIndex(i);
                if (null != exception) {
                    exceptionList.add(exception.getCode());
                }
            }
        }
        return exceptionList;
    }

    /**
     * 逆变电网端故障
     *
     * @param module
     * @return
     */
    public static List<Integer> parseInverterGridException(int module) {
        final List<Integer> exceptionList = new ArrayList<>();
        for (int i = 0; i < Mcu.Inverter.InverterGridException.values().length; i++) {
            if (BinaryUtils.getBitValue(module, i) == 1) {
                Mcu.Inverter.InverterGridException exception = Mcu.Inverter.InverterGridException.findByIndex(i);
                if (null != exception) {
                    exceptionList.add(exception.getCode());
                }
            }
        }
        return exceptionList;
    }

    /**
     * 逆变系统故障
     *
     * @param module
     * @return
     */
    public static List<Integer> parseInverterSystemException(int module) {
        final List<Integer> exceptionList = new ArrayList<>();
        for (int i = 0; i < Mcu.Inverter.InverterSystemException.values().length; i++) {
            if (BinaryUtils.getBitValue(module, i) == 1) {
                Mcu.Inverter.InverterSystemException exception = Mcu.Inverter.InverterSystemException.findByIndex(i);
                if (null != exception) {
                    exceptionList.add(exception.getCode());
                }
            }
        }
        return exceptionList;
    }

    /**
     * 逆变光伏故障
     *
     * @param module
     * @return
     */
    public static List<Integer> parseInverterMPPTException(int module) {
        final List<Integer> exceptionList = new ArrayList<>();
        for (int i = 0; i < Mcu.Inverter.InverterMPPTException.values().length; i++) {
            if (BinaryUtils.getBitValue(module, i) == 1) {
                Mcu.Inverter.InverterMPPTException exception = Mcu.Inverter.InverterMPPTException.findByIndex(i);
                if (null != exception) {
                    exceptionList.add(exception.getCode());
                }
            }
        }
        return exceptionList;
    }

    /**
     * 逆变显示故障
     *
     * @param module
     * @return
     */
    public static List<Integer> parseInverterPresentException(int module) {
        final List<Integer> exceptionList = new ArrayList<>();
        for (int i = 0; i < Mcu.Inverter.InverterPresentException.values().length; i++) {
            if (BinaryUtils.getBitValue(module, i) == 1) {
                Mcu.Inverter.InverterPresentException exception = Mcu.Inverter.InverterPresentException.findByIndex(i);
                if (null != exception) {
                    exceptionList.add(exception.getCode());
                }
            }
        }
        return exceptionList;
    }

    /**
     * 逆变DC主动故障
     *
     * @param module
     * @return
     */
    public static List<Integer> parseInverterDCException(int module) {
        final List<Integer> exceptionList = new ArrayList<>();
        for (int i = 0; i < Mcu.Inverter.InverterDCException.values().length; i++) {
            if (BinaryUtils.getBitValue(module, i) == 1) {
                Mcu.Inverter.InverterDCException exception = Mcu.Inverter.InverterDCException.findByIndex(i);
                if (null != exception) {
                    exceptionList.add(exception.getCode());
                }
            }
        }
        return exceptionList;
    }

    /**
     * 解析电池包异常数据
     *
     * @param module
     * @return
     */
    public static List<Integer> parseBatteryException(int module) {
        final List<Integer> exceptionList = new ArrayList<>();
        for (int i = 0; i < Mcu.Battery.BatteryException.values().length; i++) {
            if (BinaryUtils.getBitValue(module, i) == 1) {
                Mcu.Battery.BatteryException exception = Mcu.Battery.BatteryException.findByIndex(i);
                if (null != exception) {
                    exceptionList.add(exception.getCode());
                }
            }
        }

        return exceptionList;
    }

    /**
     * 解析MPPT异常数据
     *
     * @param module
     * @return
     */
    public static List<Integer> parseMPPTException(int module) {
        final List<Integer> exceptionList = new ArrayList<>();
        for (int i = 0; i < Mcu.MPPT.MPPTException.values().length; i++) {
            if (BinaryUtils.getBitValue(module, i) == 1) {
                Mcu.MPPT.MPPTException exception = Mcu.MPPT.MPPTException.findByIndex(i);
                if (null != exception) {
                    exceptionList.add(exception.getCode());
                }
            }
        }

        return exceptionList;
    }

    /**
     * 解析EV异常数据
     *
     * @param module
     * @return
     */
    public static List<Integer> parseEVException(int module) {
        final List<Integer> exceptionList = new ArrayList<>();
        for (int i = 0; i < Mcu.EV.EVException.values().length; i++) {
            if (BinaryUtils.getBitValue(module, i) == 1) {
                Mcu.EV.EVException exception = Mcu.EV.EVException.findByIndex(i);
                if (null != exception) {
                    exceptionList.add(exception.getCode());
                }
            }
        }

        return exceptionList;
    }

    /**
     * 解析EV状态标识
     *
     * @param
     * @return
     */
    public static int parseEVDetailState(int state) {
        Mcu.EV.EVDetailState detailState = Mcu.EV.EVDetailState.valueOf(state);
        if (null == detailState) {
            return STATUS_ERROR_DEF;
        }
        return detailState.getCode();
    }

    /**
     * 解析EV储能模式
     *
     * @param value
     * @return
     */
    public static int parseEVChargingMode(int value) {
        Mcu.EV.EVChargingMode evChargingMode = Mcu.EV.EVChargingMode.valueOf(value + 1);
        if (null == evChargingMode) {
            return STATUS_ERROR_DEF;
        }
        return evChargingMode.getCode();
    }

    /**
     * 解析机柜异常数据
     *
     * @param module
     * @return
     */
    public static List<Integer> parseCabinetException(int module) {
        final List<Integer> exceptionList = new ArrayList<>();
        for (int i = 0; i < Mcu.Cabinet.CabinetException.values().length; i++) {
            if (BinaryUtils.getBitValue(module, i) == 1) {
                Mcu.Cabinet.CabinetException exception = Mcu.Cabinet.CabinetException.findByIndex(i);
                if (null != exception) {
                    exceptionList.add(exception.getCode());
                }
            }
        }

        return exceptionList;
    }

    /**
     * 解析系统异常数据
     *
     * @param module
     * @return
     */
    public static List<Integer> parseSystemException(int module) {
        final List<Integer> exceptionList = new ArrayList<>();
        for (int i = 0; i < Mcu.System.SystemException.values().length; i++) {
            if (BinaryUtils.getBitValue(module, i) == 1) {
                Mcu.System.SystemException exception = Mcu.System.SystemException.findByIndex(i);
                if (null != exception) {
                    exceptionList.add(exception.getCode());
                }
            }
        }

        return exceptionList;
    }

    /**
     * 解析通讯异常数据
     *
     * @param module
     * @return
     */
    public static List<Integer> parseCommunicationException(int module) {
        final List<Integer> exceptionList = new ArrayList<>();
        for (int i = 0; i < Mcu.System.CommunicationException.values().length; i++) {
            if (BinaryUtils.getBitValue(module, i) == 1) {
                Mcu.System.CommunicationException exception = Mcu.System.CommunicationException.findByIndex(i);
                if (null != exception) {
                    exceptionList.add(exception.getCode());
                }
            }
        }

        return exceptionList;
    }

    /**
     * 解析机柜水感配件状态
     *
     * @param
     * @return
     */
    public static int parseCabinetWaterState(int state) {
        Mcu.Cabinet.CabinetWaterState waterState = Mcu.Cabinet.CabinetWaterState.findByState(state);
        if (null == waterState) {
            return STATUS_ERROR_DEF;
        }
        return waterState.getCode();
    }

    /**
     * 解析机柜烟感配件状态
     *
     * @param
     * @return
     */
    public static int parseCabinetSmokeState(int state) {
        Mcu.Cabinet.CabinetSmokeState smokeState = Mcu.Cabinet.CabinetSmokeState.findByState(state);
        if (null == smokeState) {
            return STATUS_ERROR_DEF;
        }
        return smokeState.getCode();
    }

    /**
     * 解析机柜风扇配件状态
     *
     * @param
     * @return
     */
    public static int parseCabinetFanState(int state) {
        Mcu.Cabinet.CabinetFanState fanState = Mcu.Cabinet.CabinetFanState.findByState(state);
        if (null == fanState) {
            return STATUS_ERROR_DEF;
        }
        return fanState.getCode();
    }

    /**
     * 解析Ev状态
     *
     * @param
     * @return
     */
    public static int parseEVState(int state) {
        Mcu.EV.EVState evState = Mcu.EV.EVState.findByState(state);
        if (null == evState) {
            return STATUS_ERROR_DEF;
        }
        return evState.getCode();
    }

    /**
     * 解析Ev灯光配件状态
     *
     * @param
     * @return
     */
    public static int parseEVLightState(int state) {
        Mcu.EV.EVLightState lightState = Mcu.EV.EVLightState.findByState(state);
        if (null == lightState) {
            return STATUS_ERROR_DEF;
        }
        return lightState.getCode();
    }

    /**
     * 解析通讯信号强度
     *
     * @param
     * @return
     */
    public static int parseWifiSignal(int state) {
        Mcu.CommunicateSignal.WIFISignal wifiSignal = Mcu.CommunicateSignal.WIFISignal.findByValue(state);
        if (null == wifiSignal) {
            return STATUS_ERROR_DEF;
        }
        return wifiSignal.getCode();
    }

    /**
     * Cellular信号强度
     *
     * @param
     * @return
     */
    public static int parseCellularSignal(int state) {
        Mcu.CommunicateSignal.CellularSignal wifiSignal = Mcu.CommunicateSignal.CellularSignal.findByState(state);
        if (null == wifiSignal) {
            return STATUS_ERROR_DEF;
        }
        return wifiSignal.getCode();
    }

    public static int parseEthernetSignal(int state) {
        Mcu.CommunicateSignal.EthernetSignal ethernetSignal = Mcu.CommunicateSignal.EthernetSignal.findByState(state);
        if (null == ethernetSignal) {
            return STATUS_ERROR_DEF;
        }
        return ethernetSignal.getCode();
    }

    /**
     * 解析当前模式
     *
     * @param
     * @return
     */
    public static int parseChargeMode(int state) {
        Mcu.Mode.ChargeMode chargeMode = Mcu.Mode.ChargeMode.findByValue(state);
        if (null == chargeMode) {
            return STATUS_ERROR_DEF;
        }
        return chargeMode.getCode();
    }

    /**
     * BMT当前的策略
     * @param policy
     * @return
     */
    public static int parseChargeModePolicy(int policy) {
        Mcu.Mode.ChargeModePolicy modePolicy = Mcu.Mode.ChargeModePolicy.findByValue(policy);
        if (null == modePolicy) {
            return STATUS_ERROR_DEF;
        }
        return modePolicy.getCode();
    }

    /**
     * 模式标志
     * @param flag
     * @return
     */
    public static int parseChargeModeFlag(int flag) {
        Mcu.Mode.ChargeModeFlag modeFlag = Mcu.Mode.ChargeModeFlag.findByValue(flag);
        if (null == modeFlag) {
            return STATUS_ERROR_DEF;
        }
        return modeFlag.getCode();
    }

    /**
     * 温度转换
     *
     * @param tempInK MSCT过来的原始温度
     * @return 开尔文温度
     */
    public static double convertTempK(int tempInK) {
        return ((1.0 * tempInK) / 10);
    }

    /**
     * 解析当前升级状态
     *
     * @param
     * @return
     */
    public static int parseChipsUpdateState(int state) {
        Mcu.Chips.ChipsUpdateState updateState = Mcu.Chips.ChipsUpdateState.valueOf(state);
        if (null == updateState) {
            return STATUS_ERROR_DEF;
        }
        return updateState.getCode();
    }

    /**
     * 解析当前升级指令执行状态
     *
     * @param
     * @return
     */
    public static int parseChipsUpdateErrorState(int state) {
        Mcu.Chips.ChipsUpdateError updateState = Mcu.Chips.ChipsUpdateError.valueOf(state);
        if (null == updateState) {
            return STATUS_ERROR_DEF;
        }
        return updateState.getCode();
    }

    /**
     * 解析当前储能模式
     *
     * @param
     * @return
     */
    public static int parseReserveMode(int state) {
        Mcu.Reserve.ReserveMode reserveMode = Mcu.Reserve.ReserveMode.valueOf(state + 1);
        if (null == reserveMode) {
            return STATUS_ERROR_DEF;
        }
        return reserveMode.getCode();
    }

    public static int parseEVAdvanceStatus(int status) {
        Mcu.EV.EVAdvanceStatus evAdvanceStatus = Mcu.EV.EVAdvanceStatus.valueOf(status + 1);
        if (null == evAdvanceStatus) {
            return STATUS_ERROR_DEF;
        }
        return evAdvanceStatus.getEvAdvanceStatus();
    }

    public static List<Integer> parseUpgradeError(int error) {
        final List<Integer> errorList = new ArrayList<>();
        if (BinaryUtils.getBitValue(error, 0) == 1) {
            Mcu.Upgrade.UpgradeError firstError = Mcu.Upgrade.UpgradeError.findByIndex(0);
            if (null != firstError) {
                errorList.add(firstError.getError());
            }
        }
        if (BinaryUtils.getBitValue(error, 15) == 1) {
            Mcu.Upgrade.UpgradeError lastError = Mcu.Upgrade.UpgradeError.findByIndex(Mcu.Upgrade.UpgradeError.values().length - 1);
            if (null != lastError) {
                errorList.add(lastError.getError());
            }
        }
        return errorList;
    }

    public static int parseSolarEfficiencyType(int type) {
        Mcu.SolarEfficiency.SolarEfficiencyType solarEfficiencyType = Mcu.SolarEfficiency.SolarEfficiencyType.findByIndex(type);
        return solarEfficiencyType.getSolarEfficiencyType();
    }

    /**
     * 解析ThirdPartyPVActiveError数据
     *
     * @param module
     * @return
     */
    public static List<Integer> parseThirdPartyPVActiveError(int module) {
        final List<Integer> errorList = new ArrayList<>();
        for (int i = 0; i < Mcu.ThirdPartyPVActiveError.ThirdPartyPVActiveErrorType.values().length; i++) {
            if (BinaryUtils.getBitValue(module, i) == 1) {
                Mcu.ThirdPartyPVActiveError.ThirdPartyPVActiveErrorType error = Mcu.ThirdPartyPVActiveError.ThirdPartyPVActiveErrorType.findByIndex(i);
                if (null != error) {
                    errorList.add(error.getThirdPartyPVActiveErrorType());
                }
            }
        }
        return errorList;
    }

    public static int parseRegulateFrequencyStateType(int type) {
        Mcu.RegulateFrequencyState.RegulateFrequencyStateType  regulateFrequencyStateType = Mcu.RegulateFrequencyState.RegulateFrequencyStateType.findByIndex(type);
        return regulateFrequencyStateType.getRegulateFrequencyStateType();
    }

    public static int parsePVPreferenceType(int type) {
        Mcu.PVPreference.PVPreferenceType  pvPreferenceType = Mcu.PVPreference.PVPreferenceType.findByIndex(type);
        return pvPreferenceType.getPreference();
    }

    public static int parseSmartEVToggleStatusType(int type) {
        Mcu.SmartEVToggleStatus.SmartEVToggleStatusType smartEVToggleStatus =  Mcu.SmartEVToggleStatus.SmartEVToggleStatusType.findByIndex(type);
        return smartEVToggleStatus.getStatus();
    }
}
