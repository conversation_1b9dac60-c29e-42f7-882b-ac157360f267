package com.dinsafer.module_bmt.cmd;

import com.dinsafer.dincore.common.Cmd;
import com.dinsafer.dincore.utils.BinaryUtils;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.msctlib.msct.ContentType;
import com.dinsafer.dssupport.msctlib.msct.Exoption;
import com.dinsafer.dssupport.msctlib.msct.MsctContext;
import com.dinsafer.dssupport.msctlib.msct.MsctResponse;
import com.dinsafer.dssupport.msctlib.msct.Utils;
import com.dinsafer.dssupport.utils.HexUtil;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.module_bmt.bean.BmtDevice;

import java.util.List;
import java.util.Map;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2022/12/5
 */
public class MsctCmdReponseConverter implements ICmdConverter<MsctResponse, Map> {
    private String TAG = getClass().getSimpleName();
    private BmtDevice device;
    private final int STATUS_SUCCESS = 0;

    public MsctCmdReponseConverter(BmtDevice device) {
        this.device = device;
    }

    @Override
    public Map convert(MsctResponse response) {
        if (!ContentType.rawByte.equals(response.getContentType())) {
            return null;
        }
        MsctContext msctContext = response.getMsctContext();
        short method = BinaryUtils.byteToShortLittle(Utils.unSignByteToByte(msctContext.ShouldGetOptionHeader(Exoption.OPTION_METHOD)));
        int status = response.getStatus();
        byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
        MsctLog.i(TAG, String.format("convert method:%04x,status:%d,payload:%s", method, status, HexUtil.bytesToHexString(payload)));
        switch (method) {
            case McuCmd.BATTERY_ACCESSORYSTATE_CHANGED_CUSTOM:
                handleBatteryAccessorystateChangedCustom(status, payload);
                break;
            case McuCmd.BATTERY_STATUSINFO_NOTIFY:
                return handleBatteryStatusInfoNotify(status, payload);
            case McuCmd.BATTERY_INDEX_CHANGED:
                return handleBatteryIndexChanged(status, payload);
            case McuCmd.BATTERY_ACCESSORYSTATE_CHANGED:
                return handleBatteryAccessoryStateChanged(status, payload);
            case McuCmd.INVERTER_EXCEPTION:
                return handleInverterException(status, payload);
            case McuCmd.BATTERY_EXCEPTION:
                return handleBatteryException(status, payload);
            case McuCmd.MPPT_EXCEPTION:
                return handleMPPTException(status, payload);
            case McuCmd.EV_EXCEPTION:
                return handleEVException(status, payload);
            case McuCmd.CABINET_STATE_CHANGED:
                return handleCabinetStateChanged(status, payload);
            case McuCmd.CABINET_INDEX_CHANGED:
                return handleCabinetIndexChanged(status, payload);
            case McuCmd.SYSTEM_EXCEPTION:
                return handleSystemException(status, payload);
            case McuCmd.COMMUNICATION_EXCEPTION:
                return handleCommunicationException(status, payload);
            case McuCmd.CABINET_EXCEPTION:
                return handleCabinetException(status, payload);
            case McuCmd.EV_ADVANCESTATUS_CHANGED:
                return handleEVAdvanceStatusChange(status, payload);
            default:
                break;
        }

        return null;
    }

    private Map handleEVAdvanceStatusChange(int status, byte[] payload) {
        if (payload == null || payload.length < 1) {
            return Cmd.getDefaultResultMap(false, BmtCmd.EV_ADVANCESTATUS_CHANGED);
        }
        Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.EV_ADVANCESTATUS_CHANGED);
        Cmd.putResultValue(resultMap, BmtDataKey.ADVANCE_STATUS, McuDataHelper.parseEVAdvanceStatus(Utils.byte2Int(payload[0])));
        return resultMap;
    }

    private Map handleCabinetException(int status, byte[] payload) {
        if (payload == null || payload.length < 2) {
            return Cmd.getDefaultResultMap(false, BmtCmd.CABINET_EXCEPTION);
        }
        List exceptions = McuDataHelper.parseCabinetException(BinaryUtils.bytes2IntLittle(new byte[]{payload[1], payload[2], 0, 0}));
        Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.CABINET_EXCEPTION);
        Cmd.putResultValue(resultMap, BmtDataKey.INDEX, Utils.byte2Int(payload[0]));
        Cmd.putResultValue(resultMap, BmtDataKey.EXCEPTIONS, exceptions);
        if (status == STATUS_SUCCESS) {
            device.setCabinetExceptions(exceptions);
        }
        return resultMap;
    }

    private Map handleCommunicationException(int status, byte[] payload) {
        if (payload == null || payload.length < 2) {
            return Cmd.getDefaultResultMap(false, BmtCmd.COMMUNICATION_EXCEPTION);
        }
        List exceptions = McuDataHelper.parseCommunicationException(BinaryUtils.bytes2IntLittle(new byte[]{payload[0], payload[1], 0, 0}));
        Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.COMMUNICATION_EXCEPTION);
        Cmd.putResultValue(resultMap, BmtDataKey.EXCEPTIONS, exceptions);
        if (status == STATUS_SUCCESS) {
            device.setCommunicationExceptions(exceptions);
        }
        return resultMap;
    }

    private Map handleSystemException(int status, byte[] payload) {
        if (payload == null || payload.length < 2) {
            return Cmd.getDefaultResultMap(false, BmtCmd.SYSTEM_EXCEPTION);
        }
        List exceptions = McuDataHelper.parseSystemException(BinaryUtils.bytes2IntLittle(new byte[]{payload[0], payload[1], 0, 0}));
        Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.SYSTEM_EXCEPTION);
        Cmd.putResultValue(resultMap, BmtDataKey.EXCEPTIONS, exceptions);
        if (status == STATUS_SUCCESS) {
            device.setSystemExceptions(exceptions);
        }
        return resultMap;
    }

    private Map handleCabinetIndexChanged(int status, byte[] payload) {
        if (payload == null || payload.length < 2) {
            return Cmd.getDefaultResultMap(false, BmtCmd.CABINET_INDEX_CHANGED);
        }

        Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.CABINET_INDEX_CHANGED);
        Cmd.putResultValue(resultMap, BmtDataKey.IS_ADD, 0x01 == Utils.byte2Int(payload[0]));
        Cmd.putResultValue(resultMap, BmtDataKey.CABINET_INDEX, Utils.byte2Int(payload[1]));
        return resultMap;
    }

    private Map handleCabinetStateChanged(int status, byte[] payload) {
        if (payload == null || payload.length < 4) {
            return Cmd.getDefaultResultMap(false, BmtCmd.CABINET_STATE_CHANGED);
        }

        Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.CABINET_STATE_CHANGED);
        Cmd.putResultValue(resultMap, BmtDataKey.INDEX, Utils.byte2Int(payload[0]));
        Cmd.putResultValue(resultMap, BmtDataKey.WATER_STATE, McuDataHelper.parseCabinetWaterState(payload[1]));
        Cmd.putResultValue(resultMap, BmtDataKey.SMOKE_STATE, McuDataHelper.parseCabinetSmokeState(payload[2]));
        Cmd.putResultValue(resultMap, BmtDataKey.FAN_STATE, McuDataHelper.parseCabinetFanState(payload[3]));
        return resultMap;
    }

    private Map handleEVException(int status, byte[] payload) {
        if (payload == null || payload.length < 3) {
            return Cmd.getDefaultResultMap(false, BmtCmd.EV_EXCEPTION);
        }

        Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.EV_EXCEPTION);
        List exceptions = McuDataHelper.parseEVException(BinaryUtils.bytes2IntLittle(new byte[]{payload[0], payload[1], 0, 0}));
        int detailState = McuDataHelper.parseEVDetailState(BinaryUtils.bytes2IntLittle(new byte[] {payload[2], 0, 0,0}));

        Cmd.putResultValue(resultMap, BmtDataKey.EXCEPTIONS, exceptions);
        Cmd.putResultValue(resultMap, BmtDataKey.DETAIL_STATE, detailState);
        if (status == STATUS_SUCCESS) {
            device.setEvExceptions(exceptions);
        }
        return resultMap;
    }

    private Map handleMPPTException(int status, byte[] payload) {
        if (payload == null || payload.length < 3) {
            return Cmd.getDefaultResultMap(false, BmtCmd.MPPT_EXCEPTION);
        }

        int index = BinaryUtils.bytes2IntLittle(new byte[]{payload[0], 0, 0, 0});
        List exceptions = McuDataHelper.parseMPPTException(BinaryUtils.bytes2IntLittle(new byte[]{payload[1], payload[2], 0, 0}));
        Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.MPPT_EXCEPTION);
        Cmd.putResultValue(resultMap, BmtDataKey.INDEX, index);
        Cmd.putResultValue(resultMap, BmtDataKey.EXCEPTIONS, exceptions);
        if (status == STATUS_SUCCESS) {
            device.setMpptExceptions(exceptions);
        }
        return resultMap;
    }


    private Map handleBatteryException(int status, byte[] payload) {
        if (payload == null || payload.length < 5) {
            return Cmd.getDefaultResultMap(false, BmtCmd.BATTERY_EXCEPTION);
        }

        int index = BinaryUtils.bytes2IntLittle(new byte[]{payload[0], 0, 0, 0});
        int module = BinaryUtils.bytes2IntLittle(new byte[]{payload[1], payload[2], 0, 0});
        List exceptions = McuDataHelper.parseBatteryException(module >> 2);
        int cabinetIndex = payload[3];
        int cabinetPositionIndex = payload[4];

        Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.BATTERY_EXCEPTION);
        Cmd.putResultValue(resultMap, BmtDataKey.INDEX, index);
        Cmd.putResultValue(resultMap, BmtDataKey.CABINET_INDEX, cabinetIndex);
        Cmd.putResultValue(resultMap, BmtDataKey.CABINET_POSITION_INDEX, cabinetPositionIndex);
        Cmd.putResultValue(resultMap, BmtDataKey.EXCEPTIONS, exceptions);
        if (status == STATUS_SUCCESS) {
            device.setBatteryExceptions(exceptions);
        }
        return resultMap;
    }

    private Map handleInverterException(int status, byte[] payload) {
        if (payload == null || payload.length < 15) {
            return Cmd.getDefaultResultMap(false, BmtCmd.INVERTER_EXCEPTION);
        }
        int index = BinaryUtils.bytes2IntLittle(new byte[]{payload[0], 0, 0, 0});

        // 解析异常数据
        int flag;
        flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[1], payload[2], 0, 0});
        List<Integer> vertBatteryExceptions = McuDataHelper.parseInverterBatteryException(flag);
        flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[3], payload[4], 0, 0});
        List<Integer> vertExceptions = McuDataHelper.parseInverterException(flag);
        flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[5], payload[6], 0, 0});
        List<Integer> vertGridExceptions = McuDataHelper.parseInverterGridException(flag);
        flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[7], payload[8], 0, 0});
        List<Integer> vertSystemExceptions = McuDataHelper.parseInverterSystemException(flag);
        flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[9], payload[10], 0, 0});
        List<Integer> vertmpptExceptions = McuDataHelper.parseInverterMPPTException(flag);
        flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[11], payload[12], 0, 0});
        List<Integer> vertPresentExceptions = McuDataHelper.parseInverterPresentException(flag);
        flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[13], payload[14], 0, 0});
        List<Integer> vertDCExceptions = McuDataHelper.parseInverterDCException(flag);

        Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.INVERTER_EXCEPTION);
        Cmd.putResultValue(resultMap, BmtDataKey.INDEX, index);
        Cmd.putResultValue(resultMap, BmtDataKey.VERT_BATTERY_EXCEPTIONS, vertBatteryExceptions);
        Cmd.putResultValue(resultMap, BmtDataKey.VERT_EXCEPTIONS, vertExceptions);
        Cmd.putResultValue(resultMap, BmtDataKey.VERT_GRID_EXCEPTIONS, vertGridExceptions);
        Cmd.putResultValue(resultMap, BmtDataKey.VERT_SYSTEM_EXCEPTIONS, vertSystemExceptions);
        Cmd.putResultValue(resultMap, BmtDataKey.VERT_MPPT_EXCEPTIONS, vertmpptExceptions);
        Cmd.putResultValue(resultMap, BmtDataKey.VERT_PRESENT_EXCEPTIONS, vertPresentExceptions);
        Cmd.putResultValue(resultMap, BmtDataKey.VERT_DC_EXCEPTIONS, vertDCExceptions);
        if (status == STATUS_SUCCESS) {
            device.setInverterExceptions(vertBatteryExceptions,vertExceptions, vertGridExceptions,vertSystemExceptions,
                    vertmpptExceptions,vertPresentExceptions,vertDCExceptions);
        }
        return resultMap;
    }

    private Map handleBatteryAccessoryStateChanged(int status, byte[] payload) {
        if (payload == null || payload.length < 2) {
            return Cmd.getDefaultResultMap(false, BmtCmd.BATTERY_ACCESSORYSTATE_CHANGED);
        }

        int index = BinaryUtils.bytes2IntLittle(new byte[]{payload[0], 0, 0, 0});
        int state = BinaryUtils.bytes2IntLittle(new byte[]{payload[1], 0, 0, 0});
        boolean heating = false;
        boolean heatAvailable = false;
        if (Mcu.Battery.STATE_HEATING_FILM_HEATING_OPEN == state) {
            heatAvailable = true;
            heating = true;
        } else if (Mcu.Battery.STATE_HEATING_FILM_HEATING_CLOSE == state) {
            heatAvailable = true;
            heating = false;
        } else {
            heatAvailable = false;
            heating = false;
        }

        Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.BATTERY_ACCESSORYSTATE_CHANGED);
        Cmd.putResultValue(resultMap, BmtDataKey.INDEX, index);
        Cmd.putResultValue(resultMap, BmtDataKey.HEATING, heating);
        Cmd.putResultValue(resultMap, BmtDataKey.HEAT_AVAILABLE, heatAvailable);
        return resultMap;
    }

    private Map handleBatteryIndexChanged(int status, byte[] payload) {
        if (payload == null || payload.length < 4) {
            return Cmd.getDefaultResultMap(false, BmtCmd.BATTERY_INDEX_CHANGED);
        }

        boolean isAdd = payload[0] == 1;
        int index = payload[1] & 0xff;
        int cabinetIndex = payload[2] & 0xff;
        int cabinetPositionIndex = payload[3] & 0xff;

        Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.BATTERY_INDEX_CHANGED);
        Cmd.putResultValue(resultMap, BmtDataKey.INDEX, index);
        Cmd.putResultValue(resultMap, BmtDataKey.IS_ADD, isAdd);
        Cmd.putResultValue(resultMap, BmtDataKey.CABINET_INDEX, cabinetIndex);
        Cmd.putResultValue(resultMap, BmtDataKey.CABINET_POSITION_INDEX, cabinetPositionIndex);
        return resultMap;
    }


    private Map handleBatteryStatusInfoNotify(int status, byte[] payload) {
        if (payload == null || payload.length < 2) {
            return Cmd.getDefaultResultMap(false, BmtCmd.BATTERY_STATUSINFO_NOTIFY);
        }

        int socState = payload[0] & 0xff;
        Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.BATTERY_STATUSINFO_NOTIFY);
        Cmd.putResultValue(resultMap, BmtDataKey.SOC_STATE, socState);
        return resultMap;
    }

    private Map handleBatteryAccessorystateChangedCustom(int status, byte[] payload) {
        if (payload == null || payload.length < 4) {
            return Cmd.getDefaultResultMap(false, BmtCmd.BATTERY_ACCESSORYSTATE_CHANGED_CUSTOM);
        }
        int index = BinaryUtils.bytes2IntLittle(new byte[]{payload[0], 0, 0, 0});
        int state = BinaryUtils.bytes2IntLittle(new byte[]{payload[1], 0, 0, 0});
        boolean heating = false;
        boolean heatAvailable = false;
        if (Mcu.Battery.STATE_HEATING_FILM_HEATING_OPEN == state) {
            heatAvailable = true;
            heating = true;
        } else if (Mcu.Battery.STATE_HEATING_FILM_HEATING_CLOSE == state) {
            heatAvailable = true;
            heating = false;
        } else {
            heatAvailable = false;
            heating = false;
        }
        int cabinetIndex = BinaryUtils.bytes2IntLittle(new byte[]{payload[2], 0, 0, 0});
        int cabinetPositionIndex = BinaryUtils.bytes2IntLittle(new byte[]{payload[3], 0, 0, 0});
        Map resultMap = Cmd.getDefaultResultMap(true, BmtCmd.BATTERY_ACCESSORYSTATE_CHANGED_CUSTOM);
        Cmd.putResultValue(resultMap, BmtDataKey.INDEX, index);
        Cmd.putResultValue(resultMap, BmtDataKey.HEATING, heating);
        Cmd.putResultValue(resultMap, BmtDataKey.HEAT_AVAILABLE, heatAvailable);
        Cmd.putResultValue(resultMap, BmtDataKey.CABINET_INDEX, cabinetIndex);
        Cmd.putResultValue(resultMap, BmtDataKey.CABINET_POSITION_INDEX, cabinetPositionIndex);
        return resultMap;
    }
}
