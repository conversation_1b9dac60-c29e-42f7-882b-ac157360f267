package com.dinsafer.module_bmt.cmd;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dincore.common.Cmd;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.dincore.utils.BinaryUtils;
import com.dinsafer.dincore.utils.MapUtils;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.msctlib.msct.ContentType;
import com.dinsafer.dssupport.msctlib.msct.Exoption;
import com.dinsafer.dssupport.msctlib.msct.IMsg;
import com.dinsafer.dssupport.msctlib.msct.MsctContext;
import com.dinsafer.dssupport.msctlib.msct.MsctDataFactory;
import com.dinsafer.dssupport.msctlib.msct.MsctResponse;
import com.dinsafer.dssupport.msctlib.msct.Utils;
import com.dinsafer.dssupport.msctlib.queue.IRequestCallBack;
import com.dinsafer.dssupport.msctlib.utils.HexUtil;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_bmt.BmtCmd;
import com.dinsafer.module_bmt.BmtDataKey;
import com.dinsafer.module_bmt.bean.BmtDevice;
import com.dinsafer.module_bmt.event.BmtCountryCodeUpdateEvent;

import org.greenrobot.eventbus.EventBus;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2022/12/5
 */
public class MsctCmdRequestConverter implements ICmdConverter<Map, MsctDataFactory> {
    private String TAG = getClass().getSimpleName();
    private BmtDevice device;

    public MsctCmdRequestConverter(BmtDevice device) {
        this.device = device;
    }

    @Override
    public MsctDataFactory convert(Map arg) {
        String cmd = (String) arg.get(BmtDataKey.CMD);
        switch (cmd) {
            case BmtCmd.GET_INVERTER_INFO:
                return getInverterInfo(arg);
            case BmtCmd.GET_INVERTER_INPUT_INFO:
                return getInverterInputInfo();
            case BmtCmd.GET_BATTERY_ALLINFO:
                return getBatteryAllInfo();
            case BmtCmd.SET_INVERTER_OPEN:
                return setInverterOpen(arg);
            case BmtCmd.GET_INVERTER_OUTPUT_INFO:
                return getInverterOutputInfo(arg);
            case BmtCmd.SET_BATTERY_ALLOFF:
                return setBatteryAllOff(arg);
            case BmtCmd.GET_BATTERY_INFO:
                return getBatteryInfo(arg);
            case BmtCmd.GET_BATTERY_ACCESSORYSTATE:
                return getBatteryAccessoryState(arg);
            case BmtCmd.GET_GLOBAL_EXCEPTIONS:
                return getGlobalExceptions(arg);
            case BmtCmd.GET_GLOBALLOAD_STATE:
                return getGlobalLoadState(arg);
            case BmtCmd.GET_MCU_INFO:
                return getMcuInfo(arg);
            case BmtCmd.GET_CABINET_STATE:
                return getCabinetState(arg);
            case BmtCmd.GET_MPPT_STATE:
                return getMPPTState(arg);
            case BmtCmd.GET_EV_STATE:
                return getEvState(arg);
            case BmtCmd.GET_CABINET_ALLINFO:
                return getCabinetAllInfo(arg);
            case BmtCmd.SET_EMERGENCY_CHARGE:
                return setEmergencyCharge(arg);
            case BmtCmd.GET_EMERGENCY_CHARGE:
                return getEmergencyCharge(arg);
            case BmtCmd.SET_CHARGE_STRATEGIES:
                return setChargeStrategies(arg);
            case BmtCmd.GET_CHARGE_STRATEGIES:
                return getChargeStrategies(arg);
            case BmtCmd.SET_VIRTUAL_POWER_PLANT:
                return setVirtualPowerPlant(arg);
            case BmtCmd.GET_VIRTUAL_POWER_PLANT:
                return getVirtualPowerPlant(arg);
            case BmtCmd.GET_COMMUNICATE_SIGNAL:
                return getCommunicationSignal(arg);
            case BmtCmd.RESET:
                return reset(arg);
            case BmtCmd.RESET_DEVICE_DATA:
                return resetDeviceData(arg);
            case BmtCmd.GET_ADVANCE_INFO:
                return getAdvanceInfo(arg);
            case BmtCmd.REBOOT_INVERTER:
                return rebootInverter(arg);
            case BmtCmd.GET_MODE:
                return getMode(arg);
            case BmtCmd.GET_CHIPS_STATUS:
                return getChipsStatus(arg);
            case BmtCmd.GET_CHIPS_UPDATE_PROGRESS:
                return getChipsUpdateProgress(arg);
            case BmtCmd.UPDATE_CHIPS:
                return updateChips(arg);
            case BmtCmd.GET_CURRENT_RESERVE_MODE:
                return getCurrentReserveMode(arg);
            case BmtCmd.GET_PRICE_TRACK_RESERVE_MODE:
                return getPriceTrackReserveMode(arg);
            case BmtCmd.GET_SCHEDULE_RESERVE_MODE:
                return getScheduleReserveMode(arg);
            case BmtCmd.SET_RESERVE_MODE:
                return setReserveMode(arg);
            case BmtCmd.GET_CURRENT_EV_CHARGING_MODE:
                return getCurrentEVChargingMode(arg);
            case BmtCmd.GET_EV_CHARGING_MODE_SCHEDULE:
                return getEVChargingModeSchedule(arg);
            case BmtCmd.SET_EV_CHARGING_MODE:
                return setEVChargingMode(arg);
            case BmtCmd.GET_CURRENT_EVADVANCESTATUS:
                return getCurrentEVAdvanceStatus(arg);
            case BmtCmd.GET_EVCHARGING_INFO:
                return getEVChargingInfo(arg);
            case BmtCmd.SET_REGION:
                return setRegion(cmd, arg);
            case BmtCmd.SET_EXCEPTION_IGNORE:
                return setExceptionIgnore(arg);
            case BmtCmd.SET_EVCHARGINGMODE_INSTANTCHARGE:
                return setEVCMInstanceCharge(arg);
            case BmtCmd.GET_GLOBAL_CURRENT_FLOW_INFO:
                return getGlobalCurrentFlowInfo(arg);
            case BmtCmd.SET_EVCHARGINGMODE_INSTANT:
                return setEvchargingmodeInstant(arg);
            case BmtCmd.RESET_INVERTER:
                return resetInverter(arg);
            case BmtCmd.GET_VIEW_EXCEPTIONS:
                return getViewExceptions(arg);
            case BmtCmd.GET_GRIDCONNECTION_CONFIG:
                return getGridConnectionConfig(arg);
            case BmtCmd.UPDATE_GRIDCONNECTION_CONFIG:
                return updateGridConnectionConfig(arg);
            case BmtCmd.RESUME_GRIDCONNECTION_CONFIG:
                return resumeGridConnectionConfig();
            case BmtCmd.GET_FIRMWARES:
                return getFirmwares(arg);
            case BmtCmd.SET_FUSE_SPECS:
                return setFuseSpecs(cmd, arg);
            case BmtCmd.GET_FUSE_SPECS:
                return getFuseSpecs(arg);
            case BmtCmd.GET_THIRDPARTYPV_INFO:
                return getThirdPartyPVInfo(arg);
            case BmtCmd.SET_THIRD_PARTYPV_ON:
                return setThirdPartyPVOn(arg);
            case BmtCmd.GET_MODE_V2:
                return getModeV2(arg);
            case BmtCmd.SYNC_CONF:
                return syncConf();
            case BmtCmd.GET_REGULATE_FREQUENCY_STATE:
                return getRegulateFrequencyState();
            case BmtCmd.GET_CUSTOM_SCHEDULEMODE:
                return getCustomScheduleMode();
            case BmtCmd.GET_PV_DIST:
                return getPVDist();
            case BmtCmd.SET_PV_DIST:
                return setPVDist(arg);
            case BmtCmd.GET_SMART_EV_STATUS:
                return getSmartEvStatus();
            case BmtCmd.SET_SMART_EV_STATUS:
                return setSmartEvStatus(arg);
            case BmtCmd.GET_ALL_SUPPORT_FEATURES:
                return getAllSupportFeatures();
        }
        return null;
    }

    private MsctDataFactory getAllSupportFeatures() {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_ALL_SUPPORT_FEATURES)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse msctResponse = (MsctResponse) iMsg;
                                if (!msctResponse.isSuccess()) {
                                    notifyError(BmtCmd.GET_ALL_SUPPORT_FEATURES, msctResponse.getErrorMsg());
                                    return;
                                }
                                printResponse(msctResponse);
                                byte[] payload = (byte[]) msctResponse.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 1) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_ALL_SUPPORT_FEATURES);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_ALL_SUPPORT_FEATURES);
                                boolean pvSupplyCustomizationSupported = payload[0] == 0x01;
                                device.setPvSupplyCustomizationSupported(pvSupplyCustomizationSupported);
                                Cmd.putResultValue(resultMap, BmtDataKey.PV_SUPPLY_CUSTOMIZATION_SUPPORTED, pvSupplyCustomizationSupported);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }

                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_ALL_SUPPORT_FEATURES);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_ALL_SUPPORT_FEATURES);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_ALL_SUPPORT_FEATURES);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                }).build();
        return msctBuilder.build();
    }

    private MsctDataFactory setSmartEvStatus(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        int toggleStatus = DeviceHelper.getInt(arg, BmtDataKey.TOGGLE_STATUS, 0);
        boolean requestRestart = DeviceHelper.getBoolean(arg, BmtDataKey.REQUEST_RESTART, false);
        byte[] params = new byte[2];
        params[0] = (byte) toggleStatus;
        params[1] = (byte) ((byte) (requestRestart ? 0x01 : 0x00));
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.SET_SMART_EV_STATUS)
                .setIsNeedResult(false)
                .setPayload(params)
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (response.isSuccess()) {
                                    printResponse(response);
                                }
                                Map resultMap = Cmd.getDefaultResultMap(response.isSuccess(), BmtCmd.SET_SMART_EV_STATUS);
                                resultMap.put(BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_SMART_EV_STATUS);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_SMART_EV_STATUS);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_SMART_EV_STATUS);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                });
        return msctBuilder.build();
    }

    private MsctDataFactory getSmartEvStatus() {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_SMART_EV_STATUS)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse msctResponse = (MsctResponse) iMsg;
                                if (!msctResponse.isSuccess()) {
                                    notifyError(BmtCmd.GET_SMART_EV_STATUS, msctResponse.getErrorMsg());
                                    return;
                                }
                                printResponse(msctResponse);
                                byte[] payload = (byte[]) msctResponse.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 2) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_SMART_EV_STATUS);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_SMART_EV_STATUS);
                                Cmd.putResultValue(resultMap, BmtDataKey.TOGGLE_STATUS, McuDataHelper.parseSmartEVToggleStatusType(payload[0] + 1));
                                Cmd.putResultValue(resultMap, BmtDataKey.SHOULD_RESTART, payload[1] == 0x01);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }

                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_SMART_EV_STATUS);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_SMART_EV_STATUS);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_SMART_EV_STATUS);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                }).build();
        return msctBuilder.build();
    }

    private MsctDataFactory setPVDist(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        int ai = DeviceHelper.getInt(arg, BmtDataKey.AI, 0);
        int scheduled = DeviceHelper.getInt(arg, BmtDataKey.SCHEDULED, 0);
        byte[] params = new byte[2];
        params[0] = (byte) ai;
        params[1] = (byte) scheduled;
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.SET_PV_DIST)
                .setIsNeedResult(false)
                .setPayload(params)
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (response.isSuccess()) {
                                    printResponse(response);
                                }
                                Map resultMap = Cmd.getDefaultResultMap(response.isSuccess(), BmtCmd.SET_PV_DIST);
                                resultMap.put(BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                Cmd.putResultValue(resultMap, BmtDataKey.AI, ai);
                                Cmd.putResultValue(resultMap, BmtDataKey.SCHEDULED, scheduled);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_PV_DIST);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_PV_DIST);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_PV_DIST);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                });
        return msctBuilder.build();
    }

    private MsctDataFactory getPVDist() {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_PV_DIST)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse msctResponse = (MsctResponse) iMsg;
                                if (!msctResponse.isSuccess()) {
                                    notifyError(BmtCmd.GET_PV_DIST, msctResponse.getErrorMsg());
                                    return;
                                }
                                printResponse(msctResponse);
                                byte[] payload = (byte[]) msctResponse.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 2) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_PV_DIST);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_PV_DIST);
                                Cmd.putResultValue(resultMap, BmtDataKey.AI, McuDataHelper.parsePVPreferenceType(payload[0]));
                                Cmd.putResultValue(resultMap, BmtDataKey.SCHEDULED, McuDataHelper.parsePVPreferenceType(payload[1]));
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }

                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_PV_DIST);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_PV_DIST);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_PV_DIST);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                }).build();
        return msctBuilder.build();
    }

    private MsctDataFactory getCustomScheduleMode() {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }

        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_CUSTOM_SCHEDULEMODE)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse msctResponse = (MsctResponse) iMsg;
                                if (!msctResponse.isSuccess()) {
                                    notifyError(BmtCmd.GET_CUSTOM_SCHEDULEMODE, msctResponse.getErrorMsg());
                                    return;
                                }
                                printResponse(msctResponse);
                                byte[] payload = (byte[]) msctResponse.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 53) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CUSTOM_SCHEDULEMODE);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_CUSTOM_SCHEDULEMODE);
                                final int smart = BinaryUtils.bytes2IntLittle(new byte[]{payload[0], 0, 0, 0});
                                final int emergency = BinaryUtils.bytes2IntLittle(new byte[]{payload[1], 0, 0, 0});
                                final int lowPowerAlert = BinaryUtils.bytes2IntLittle(new byte[]{payload[2], 0, 0, 0});
                                final int batteryProtect = BinaryUtils.bytes2IntLittle(new byte[]{payload[3], 0, 0, 0});
                                final Integer[] weekdays = bytesToArray(payload, 4, 27);
                                final Integer[] weekend = bytesToArray(payload, 28, 51);
                                Cmd.putResultValue(resultMap, BmtDataKey.SMART, smart);
                                Cmd.putResultValue(resultMap, BmtDataKey.EMERGENCY, emergency);
                                Cmd.putResultValue(resultMap, BmtDataKey.LOW_POWER_ALERT, lowPowerAlert);
                                Cmd.putResultValue(resultMap, BmtDataKey.BATTERY_PROTECT, batteryProtect);
                                Cmd.putResultValue(resultMap, BmtDataKey.WEEKDAYS, weekdays);
                                Cmd.putResultValue(resultMap, BmtDataKey.WEEKEND, weekend);
                                Cmd.putResultValue(resultMap, BmtDataKey.SYNC, payload[52] == 0x01);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }

                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CUSTOM_SCHEDULEMODE);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CUSTOM_SCHEDULEMODE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CUSTOM_SCHEDULEMODE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                }).build();
        return msctBuilder.build();
    }

    private MsctDataFactory getRegulateFrequencyState() {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_REGULATE_FREQUENCY_STATE)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_REGULATE_FREQUENCY_STATE, response.getErrorMsg());
                                    return;
                                }

                                printResponse(response);
                                byte[] payload = (byte[]) ((MsctResponse) iMsg).getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 1) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_REGULATE_FREQUENCY_STATE);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_REGULATE_FREQUENCY_STATE);
                                Cmd.putResultValue(resultMap, BmtDataKey.STATE, McuDataHelper.parseRegulateFrequencyStateType(payload[0]));
                                int state = payload[0];
                                device.setRegulateFrequencyState(state);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }

                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_REGULATE_FREQUENCY_STATE);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_REGULATE_FREQUENCY_STATE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_REGULATE_FREQUENCY_STATE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                }).build();
        return msctBuilder.build();
    }


    private MsctDataFactory syncConf() {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.SYNC_CONF)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.SYNC_CONF, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                Map<String, Object> resultMap = Cmd.getDefaultResultMap(true, BmtCmd.SYNC_CONF);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            notifyError(BmtCmd.SYNC_CONF, e.getMessage());
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        notifyError(BmtCmd.SYNC_CONF, "Fail");
                    }

                    @Override
                    public void onTimeOut() {
                        notifyError(BmtCmd.SYNC_CONF, "Time out");
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getModeV2(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_MODE_V2)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_MODE_V2, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 3) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_MODE_V2);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_MODE_V2);
                                Cmd.putResultValue(resultMap, BmtDataKey.MODE, McuDataHelper.parseChargeMode(payload[0]));
                                Cmd.putResultValue(resultMap, BmtDataKey.POLICY, McuDataHelper.parseChargeModePolicy(payload[1]));
                                Cmd.putResultValue(resultMap, BmtDataKey.FLAG, McuDataHelper.parseChargeModeFlag(payload[2]));
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_MODE_V2);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_MODE_V2);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_MODE_V2);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory setThirdPartyPVOn(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }

        final boolean on = DeviceHelper.getBoolean(arg, BmtDataKey.ON, false);
        final byte[] params = new byte[1];
        params[0] = on ? (byte) 0x01 : (byte) 0x00;
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.SET_THIRD_PARTYPV_ON)
                .setIsNeedResult(false)
                .setPayload(params)
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.SET_THIRD_PARTYPV_ON, response.getErrorMsg());
                                    return;
                                }
                                Map<String, Object> resultMap;
                                byte[] payload = (byte[]) ((MsctResponse) iMsg).getMsctContext().getDecodedPayload();
                                if (payload == null || payload.length < 3) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_THIRD_PARTYPV_ON);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.SET_THIRD_PARTYPV_ON);
                                Cmd.putResultValue(resultMap, BmtDataKey.ON, payload[0] == 0x01);
                                List thirdPartyPVActiveErrors = McuDataHelper.parseThirdPartyPVActiveError(BinaryUtils.bytes2IntLittle(new byte[]{payload[1], payload[2], 0, 0}));
                                Cmd.putResultValue(resultMap, BmtDataKey.ERROR, thirdPartyPVActiveErrors);
                                device.setThirdpartyPVOn(on);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_THIRD_PARTYPV_ON);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_THIRD_PARTYPV_ON);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_THIRD_PARTYPV_ON);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getThirdPartyPVInfo(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_THIRDPARTYPV_INFO)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_THIRDPARTYPV_INFO, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) ((MsctResponse) iMsg).getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 1) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_THIRDPARTYPV_INFO);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_THIRDPARTYPV_INFO);
                                Cmd.putResultValue(resultMap, BmtDataKey.ON, payload[0] == 0x01);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);

                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_THIRDPARTYPV_INFO);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_THIRDPARTYPV_INFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_THIRDPARTYPV_INFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                });
        return msctBuilder.build();
    }

    private MsctDataFactory getFuseSpecs(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_FUSE_SPECS)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_FUSE_SPECS, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) ((MsctResponse) iMsg).getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 1) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_FUSE_SPECS);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                int specLen = payload[0];
                                if (specLen > 0) {
                                    if (payload.length - 1 < specLen) {
                                        resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_FUSE_SPECS);
                                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                        return;
                                    }
                                    String spec = "";
                                    byte[] specArray = new byte[specLen];
                                    System.arraycopy(payload, 1, specArray, 0, specLen);
                                    spec = new String(specArray);
                                    int powerCapLen = payload.length - specLen - 1;
                                    if (powerCapLen < 1) {
                                        resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_FUSE_SPECS);
                                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                        return;
                                    }
                                    byte[] powerCapArray = new byte[powerCapLen];
                                    System.arraycopy(payload, 1 + specLen, powerCapArray, 0, powerCapLen);
                                    int powerCap = BinaryUtils.byteToShortLittle(powerCapArray);
                                    resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_FUSE_SPECS);
                                    Cmd.putResultValue(resultMap, BmtDataKey.SPEC, spec);
                                    Cmd.putResultValue(resultMap, BmtDataKey.POWER_CAP, powerCap);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                } else {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_FUSE_SPECS);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                }

                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_FUSE_SPECS);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_FUSE_SPECS);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_FUSE_SPECS);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                });
        return msctBuilder.build();
    }

    private MsctDataFactory setFuseSpecs(@NonNull final String cmd, Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        final String spec = DeviceHelper.getString(arg, BmtDataKey.SPEC, "");
        final int powerCap = DeviceHelper.getInt(arg, BmtDataKey.POWER_CAP, -1);
        if (TextUtils.isEmpty(spec) || powerCap < 0) {
            return null;
        }
        final byte[] specPayloads = getStrParamByteArr(spec);
        int specLen = specPayloads.length;
        byte[] powerCapPayloads = BinaryUtils.intToByteLittle(powerCap);
        int powerCapLen = powerCapPayloads.length;

        final byte[] payloads = new byte[specLen + powerCapLen];
        System.arraycopy(specPayloads, 0, payloads, 0, specLen);
        System.arraycopy(powerCapPayloads, 0, payloads, specLen, powerCapLen);
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.SET_FUSE_SPECS)
                .setIsNeedResult(false)
                .setPayload(payloads)
                .setContentType(ContentType.rawByte)
                .setCallBack(new BaseMsctRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                Map<String, Object> resultMap;
                                if (!response.isSuccess()) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_FUSE_SPECS);
                                    Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                printResponse(response);
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.SET_FUSE_SPECS);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            notifyError(cmd, e.getMessage());
                        }
                    }

                    @Override
                    public void onFail() {
                        notifyError(cmd, null);
                    }

                    @Override
                    public void onTimeOut() {
                        notifyError(cmd, "Timeout");
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getFirmwares(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        int type = DeviceHelper.getInt(arg, BmtDataKey.TYPE, -1);
        byte[] params = new byte[1];
        params[0] = (byte) type;
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_FIRMWARES)
                .setIsNeedResult(false)
                .setPayload(params)
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_FIRMWARES, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) ((MsctResponse) iMsg).getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 1) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_FIRMWARES);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                int responseType = payload[0];
                                byte versionInfoLength = payload[1];
                                String version = "";
                                if (versionInfoLength > 0) {
                                    byte[] versioninfoArray = new byte[versionInfoLength];
                                    System.arraycopy(payload, 2, versioninfoArray, 0, versionInfoLength);
                                    version = new String(versioninfoArray);
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_FIRMWARES);
                                Cmd.putResultValue(resultMap, BmtDataKey.TYPE, responseType);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERSION, version);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_FIRMWARES);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_FIRMWARES);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_FIRMWARES);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                });
        return msctBuilder.build();
    }

    private MsctDataFactory resumeGridConnectionConfig() {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.RESUME_GRIDCONNECTION_CONFIG)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                Map<String, Object> resultMap;
                                if (!response.isSuccess()) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.RESUME_GRIDCONNECTION_CONFIG);
                                    Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                printResponse(response);
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.RESUME_GRIDCONNECTION_CONFIG);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.RESUME_GRIDCONNECTION_CONFIG);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.RESUME_GRIDCONNECTION_CONFIG);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.RESUME_GRIDCONNECTION_CONFIG);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory updateGridConnectionConfig(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        float powerPercent = DeviceHelper.getFloat(arg, BmtDataKey.POWER_PERCENT, 0f);
        boolean appFastPowerDown = DeviceHelper.getBoolean(arg, BmtDataKey.APP_FAST_POWER_DOWN, false);
        boolean quMode = DeviceHelper.getBoolean(arg, BmtDataKey.QU_MODE, false);
        boolean cosMode = DeviceHelper.getBoolean(arg, BmtDataKey.COS_MODE, false);
        boolean antiislandingMode = DeviceHelper.getBoolean(arg, BmtDataKey.ANTI_ISLANDING_MODE, false);
        boolean highlowMode = DeviceHelper.getBoolean(arg, BmtDataKey.HIGH_LOW_MODE, false);
        float activeSoftStartRate = DeviceHelper.getFloat(arg, BmtDataKey.ACTIVE_SOFT_START_RATE, 0f);
        float overfrequencyLoadShedding = DeviceHelper.getFloat(arg, BmtDataKey.OVER_FREQUENCY_LOAD_SHEDDING, 0f);
        float overfrequencyLoadSheddingSlope = DeviceHelper.getFloat(arg, BmtDataKey.OVER_FREQUENCY_LOAD_SHEDDING_SLOPE, 0f);
        float underfrequencyLoadShedding = DeviceHelper.getFloat(arg, BmtDataKey.UNDER_FREQUENCY_LOAD_SHEDDING, 0f);
        float underfrequencyLoadSheddingSlope = DeviceHelper.getFloat(arg, BmtDataKey.UNDER_FREQUENCY_LOAD_SHEDDING_SLOPE, 0f);
        float powerFactor = DeviceHelper.getFloat(arg, BmtDataKey.POWER_FACTOR, 0f);
        float reconnectTime = DeviceHelper.getFloat(arg, BmtDataKey.RECONNECT_TIME, 0);
        float gridOvervoltage1 = DeviceHelper.getFloat(arg, BmtDataKey.GRID_OVER_VOLTAGE_1, 0f);
        float gridOvervoltage1ProtectTime = DeviceHelper.getFloat(arg, BmtDataKey.GRID_OVER_VOLTAGE_1_PROTECT_TIME, 0f);
        float gridOvervoltage2 = DeviceHelper.getFloat(arg, BmtDataKey.GRID_OVER_VOLTAGE_2, 0f);
        float gridOvervoltage2ProtectTime = DeviceHelper.getFloat(arg, BmtDataKey.GRID_OVER_VOLTAGE_2_PROTECT_TIME, 0f);
        float gridUndervoltage1 = DeviceHelper.getFloat(arg, BmtDataKey.GRID_UNDER_VOLTAGE_1, 0f);
        float gridUndervoltage1ProtectTime = DeviceHelper.getFloat(arg, BmtDataKey.GRID_UNDER_VOLTAGE_1_PROTECT_TIME, 0f);
        float gridUndervoltage2 = DeviceHelper.getFloat(arg, BmtDataKey.GRID_UNDER_VOLTAGE_2, 0f);
        float gridUndervoltage2ProtectTime = DeviceHelper.getFloat(arg, BmtDataKey.GRID_UNDER_VOLTAGE_2_PROTECT_TIME, 0f);
        float gridOverfrequency1 = DeviceHelper.getFloat(arg, BmtDataKey.GRID_OVER_FREQUENCY_1, 0f);
        int gridOverfrequencyProtectTime = DeviceHelper.getInt(arg, BmtDataKey.GRID_OVER_FREQUENCY_PROTECT_TIME, 0);
        float gridOverfrequency2 = DeviceHelper.getFloat(arg, BmtDataKey.GRID_OVER_FREQUENCY_2, 0f);
        float gridUnderfrequency1 = DeviceHelper.getFloat(arg, BmtDataKey.GRID_UNDER_FREQUENCY_1, 0f);
        int gridUnderfrequencyProtectTime = DeviceHelper.getInt(arg, BmtDataKey.GRID_UNDER_FREQUENCY_PROTECT_TIME, 0);
        float gridUnderfrequency2 = DeviceHelper.getFloat(arg, BmtDataKey.GRID_UNDER_FREQUENCY_2, 0f);
        float reconnectSlope = DeviceHelper.getFloat(arg, BmtDataKey.RECONNECT_SLOPE, 0f);
        float rebootGridOvervoltage = DeviceHelper.getFloat(arg, BmtDataKey.REBOOT_GRID_0VER_VOLTAGE, 0f);
        float rebootGridUndervoltage = DeviceHelper.getFloat(arg, BmtDataKey.REBOOT_GRID_UNDER_VOLTAGE, 0f);
        float rebootGridOverfrequency = DeviceHelper.getFloat(arg, BmtDataKey.REBOOT_GRID_OVER_FREQUENCY, 0f);
        float rebootGridUnderfrequency = DeviceHelper.getFloat(arg, BmtDataKey.REBOOT_GRID_UNDER_FREQUENCY, 0f);


//        float activeSoftStartRate = DeviceHelper.getFloat(arg, BmtDataKey.ACTIVE_SOFT_START_RATE, 0f);
//        boolean appFastPowerDown = DeviceHelper.getBoolean(arg, BmtDataKey.APP_FAST_POWER_DOWN, false);
//        float overfrequencyLoadShedding = DeviceHelper.getFloat(arg, BmtDataKey.OVER_FREQUENCY_LOAD_SHEDDING, 0f);
//        float overfrequencyLoadSheddingSlope = DeviceHelper.getFloat(arg, BmtDataKey.OVER_FREQUENCY_LOAD_SHEDDING_SLOPE, 0f);
//        float underfrequencyLoadShedding = DeviceHelper.getFloat(arg, BmtDataKey.UNDER_FREQUENCY_LOAD_SHEDDING, 0f);
//        boolean quMode = DeviceHelper.getBoolean(arg, BmtDataKey.QU_MODE, false);
//        boolean cosMode = DeviceHelper.getBoolean(arg, BmtDataKey.COS_MODE, false);
//        boolean antiislandingMode = DeviceHelper.getBoolean(arg, BmtDataKey.ANTI_ISLANDING_MODE, false);
//        boolean highlowMode = DeviceHelper.getBoolean(arg, BmtDataKey.HIGH_LOW_MODE, false);
//        float powerFactor = DeviceHelper.getFloat(arg, BmtDataKey.POWER_FACTOR, 0f);
//        int reconnectTime = DeviceHelper.getInt(arg, BmtDataKey.RECONNECT_TIME, 0);
//        float reconnectSlope = DeviceHelper.getFloat(arg, BmtDataKey.RECONNECT_SLOPE, 0f);
//        float gridOvervoltage1 = DeviceHelper.getFloat(arg, BmtDataKey.GRID_OVER_VOLTAGE_1, 0f);
//        float gridOvervoltage2 = DeviceHelper.getFloat(arg, BmtDataKey.GRID_OVER_VOLTAGE_2, 0f);
//        float gridUndervoltage1 = DeviceHelper.getFloat(arg, BmtDataKey.GRID_UNDER_VOLTAGE_1, 0f);
//        float gridUndervoltage2 = DeviceHelper.getFloat(arg, BmtDataKey.GRID_UNDER_VOLTAGE_2, 0f);
//        float gridOverfrequency1 = DeviceHelper.getFloat(arg, BmtDataKey.GRID_OVER_FREQUENCY_1, 0f);
//        float gridOverfrequency2 = DeviceHelper.getFloat(arg, BmtDataKey.GRID_OVER_FREQUENCY_2, 0f);
//        float gridUnderfrequency1 = DeviceHelper.getFloat(arg, BmtDataKey.GRID_UNDER_FREQUENCY_1, 0f);
//        float gridUnderfrequency2 = DeviceHelper.getFloat(arg, BmtDataKey.GRID_UNDER_FREQUENCY_2, 0f);

//        int activeSoftStartRateInt = (int) (activeSoftStartRate * 100);
//        int overfrequencyLoadSheddingInt = (int) (overfrequencyLoadShedding * 100);
//        int overfrequencyLoadSheddingSlopeInt = (int) (overfrequencyLoadSheddingSlope * 100);
//        int underfrequencyLoadSheddingInt = (int) (underfrequencyLoadShedding * 100);
//        int powerFactorInt = (int) (powerFactor * 100);
//        int reconnectSlopeInt = (int) (reconnectSlope * 100);
//        int gridOvervoltage1Int = (int) (gridOvervoltage1 * 10);
//        int gridOvervoltage2Int = (int) (gridOvervoltage2 * 10);
//        int gridUndervoltage1Int = (int) (gridUndervoltage1 * 10);
//        int gridUndervoltage2Int = (int) (gridUndervoltage2 * 10);
//        int gridOverfrequency1Int = (int) (gridOverfrequency1 * 100);
//        int gridOverfrequency2Int = (int) (gridOverfrequency2 * 100);
//        int gridUnderfrequency1Int = (int) (gridUnderfrequency1 * 100);
//        int gridUnderfrequency2Int = (int) (gridUnderfrequency2 * 100);


//        byte[] params = new byte[40];
//        System.arraycopy(BinaryUtils.intToByteLittle(activeSoftStartRateInt), 0, params, 0, 2);
//        System.arraycopy(BinaryUtils.intToByteLittle(appFastPowerDown ? 1 : 0), 0, params, 2, 2);
//        System.arraycopy(BinaryUtils.intToByteLittle(overfrequencyLoadSheddingInt), 0, params, 4, 2);
//        System.arraycopy(BinaryUtils.intToByteLittle(overfrequencyLoadSheddingSlopeInt), 0, params, 6, 2);
//        System.arraycopy(BinaryUtils.intToByteLittle(underfrequencyLoadSheddingInt), 0, params, 8, 2);
//        System.arraycopy(BinaryUtils.intToByteLittle(quMode ? 1 : 0), 0, params, 10, 2);
//        System.arraycopy(BinaryUtils.intToByteLittle(cosMode ? 1 : 0), 0, params, 12, 2);
//        System.arraycopy(BinaryUtils.intToByteLittle(antiislandingMode ? 1 : 0), 0, params, 14, 2);
//        System.arraycopy(BinaryUtils.intToByteLittle(highlowMode ? 1 : 0), 0, params, 16, 2);
//        System.arraycopy(BinaryUtils.intToByteLittle(powerFactorInt), 0, params, 18, 2);
//        System.arraycopy(BinaryUtils.intToByteLittle(reconnectTime), 0, params, 20, 2);
//        System.arraycopy(BinaryUtils.intToByteLittle(reconnectSlopeInt), 0, params, 22, 2);
//        System.arraycopy(BinaryUtils.intToByteLittle(gridOvervoltage1Int), 0, params, 24, 2);
//        System.arraycopy(BinaryUtils.intToByteLittle(gridOvervoltage2Int), 0, params, 26, 2);
//        System.arraycopy(BinaryUtils.intToByteLittle(gridUndervoltage1Int), 0, params, 28, 2);
//        System.arraycopy(BinaryUtils.intToByteLittle(gridUndervoltage2Int), 0, params, 30, 2);
//        System.arraycopy(BinaryUtils.intToByteLittle(gridOverfrequency1Int), 0, params, 32, 2);
//        System.arraycopy(BinaryUtils.intToByteLittle(gridOverfrequency2Int), 0, params, 34, 2);
//        System.arraycopy(BinaryUtils.intToByteLittle(gridUnderfrequency1Int), 0, params, 36, 2);
//        System.arraycopy(BinaryUtils.intToByteLittle(gridUnderfrequency2Int), 0, params, 38, 2);

        byte[] params = new byte[64];
        int gridOvervoltage1ProtectTimeInt = (int) (gridOvervoltage1ProtectTime * 10);
        System.arraycopy(BinaryUtils.intToByteLittle(gridOvervoltage1ProtectTimeInt), 0, params, 0, 2);

        int gridOvervoltage2ProtectTimeInt = (int) (gridOvervoltage2ProtectTime * 10);
        System.arraycopy(BinaryUtils.intToByteLittle(gridOvervoltage2ProtectTimeInt), 0, params, 2, 2);

        int gridUndervoltage1ProtectTimeInt = (int) (gridUndervoltage1ProtectTime * 10);
        System.arraycopy(BinaryUtils.intToByteLittle(gridUndervoltage1ProtectTimeInt), 0, params, 4, 2);

        int gridUndervoltage2ProtectTimeInt = (int) (gridUndervoltage2ProtectTime * 10);
        System.arraycopy(BinaryUtils.intToByteLittle(gridUndervoltage2ProtectTimeInt), 0, params, 6, 2);

        int activeSoftStartRateInt = (int) (activeSoftStartRate * 100);
        System.arraycopy(BinaryUtils.intToByteLittle(activeSoftStartRateInt), 0, params, 8, 2);

        System.arraycopy(BinaryUtils.intToByteLittle(appFastPowerDown ? 1 : 0), 0, params, 10, 2);

        int overfrequencyLoadSheddingInt = (int) (overfrequencyLoadShedding * 100);
        System.arraycopy(BinaryUtils.intToByteLittle(overfrequencyLoadSheddingInt), 0, params, 12, 2);

        int overfrequencyLoadSheddingSlopeInt = (int) (overfrequencyLoadSheddingSlope * 100);
        System.arraycopy(BinaryUtils.intToByteLittle(overfrequencyLoadSheddingSlopeInt), 0, params, 14, 2);

        int underfrequencyLoadSheddingInt = (int) (underfrequencyLoadShedding * 100);
        System.arraycopy(BinaryUtils.intToByteLittle(underfrequencyLoadSheddingInt), 0, params, 16, 2);

        System.arraycopy(BinaryUtils.intToByteLittle(quMode ? 1 : 0), 0, params, 18, 2);
        System.arraycopy(BinaryUtils.intToByteLittle(cosMode ? 1 : 0), 0, params, 20, 2);
        System.arraycopy(BinaryUtils.intToByteLittle(antiislandingMode ? 1 : 0), 0, params, 22, 2);
        System.arraycopy(BinaryUtils.intToByteLittle(highlowMode ? 1 : 0), 0, params, 24, 2);

        int powerFactorInt = (int) (powerFactor * 100);
        System.arraycopy(BinaryUtils.intToByteLittle(powerFactorInt), 0, params, 26, 2);

        int reconnectTimeInt = (int) (reconnectTime * 10);
        System.arraycopy(BinaryUtils.intToByteLittle(reconnectTimeInt), 0, params, 28, 2);

        int reconnectSlopeInt = (int) (reconnectSlope * 100);
        System.arraycopy(BinaryUtils.intToByteLittle(reconnectSlopeInt), 0, params, 30, 2);

        int gridOvervoltage1Int = (int) (gridOvervoltage1 * 10);
        System.arraycopy(BinaryUtils.intToByteLittle(gridOvervoltage1Int), 0, params, 32, 2);

        int gridOvervoltage2Int = (int) (gridOvervoltage2 * 10);
        System.arraycopy(BinaryUtils.intToByteLittle(gridOvervoltage2Int), 0, params, 34, 2);

        int gridUndervoltage1Int = (int) (gridUndervoltage1 * 10);
        System.arraycopy(BinaryUtils.intToByteLittle(gridUndervoltage1Int), 0, params, 36, 2);

        int gridUndervoltage2Int = (int) (gridUndervoltage2 * 10);
        System.arraycopy(BinaryUtils.intToByteLittle(gridUndervoltage2Int), 0, params, 38, 2);

        int gridOverfrequency1Int = (int) (gridOverfrequency1 * 100);
        System.arraycopy(BinaryUtils.intToByteLittle(gridOverfrequency1Int), 0, params, 40, 2);

        int gridOverfrequency2Int = (int) (gridOverfrequency2 * 100);
        System.arraycopy(BinaryUtils.intToByteLittle(gridOverfrequency2Int), 0, params, 42, 2);

        int gridUnderfrequency1Int = (int) (gridUnderfrequency1 * 100);
        System.arraycopy(BinaryUtils.intToByteLittle(gridUnderfrequency1Int), 0, params, 44, 2);

        int gridUnderfrequency2Int = (int) (gridUnderfrequency2 * 100);
        System.arraycopy(BinaryUtils.intToByteLittle(gridUnderfrequency2Int), 0, params, 46, 2);

        int powerPercentInt = (int) (powerPercent * 100);
        System.arraycopy(BinaryUtils.intToByteLittle(powerPercentInt), 0, params, 48, 2);

        System.arraycopy(BinaryUtils.intToByteLittle(gridOverfrequencyProtectTime), 0, params, 50, 2);
        System.arraycopy(BinaryUtils.intToByteLittle(gridUnderfrequencyProtectTime), 0, params, 52, 2);

        int underfrequencyLoadSheddingSlopeInt = (int) (underfrequencyLoadSheddingSlope * 1000);
        System.arraycopy(BinaryUtils.intToByteLittle(underfrequencyLoadSheddingSlopeInt), 0, params, 54, 2);

        int rebootGridUnderfrequencyInt = (int) (rebootGridUnderfrequency * 100);
        System.arraycopy(BinaryUtils.intToByteLittle(rebootGridUnderfrequencyInt), 0, params, 56, 2);

        int rebootGridOverfrequencyInt = (int) (rebootGridOverfrequency * 100);
        System.arraycopy(BinaryUtils.intToByteLittle(rebootGridOverfrequencyInt), 0, params, 58, 2);

        int rebootGridUndervoltageInt = (int) (rebootGridUndervoltage * 10);
        System.arraycopy(BinaryUtils.intToByteLittle(rebootGridUndervoltageInt), 0, params, 60, 2);

        int rebootGridOvervoltageInt = (int) (rebootGridOvervoltage * 10);
        System.arraycopy(BinaryUtils.intToByteLittle(rebootGridOvervoltageInt), 0, params, 62, 2);

        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.UPDATE_GRIDCONNECTION_CONFIG)
                .setIsNeedResult(false)
                .setPayload(params)
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (response.isSuccess()) {
                                    printResponse(response);
                                }
                                Map resultMap = Cmd.getDefaultResultMap(response.isSuccess(), BmtCmd.UPDATE_GRIDCONNECTION_CONFIG);
                                resultMap.put(BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.UPDATE_GRIDCONNECTION_CONFIG);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.UPDATE_GRIDCONNECTION_CONFIG);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.UPDATE_GRIDCONNECTION_CONFIG);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                });
        return msctBuilder.build();
    }

    private MsctDataFactory getGridConnectionConfig(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_GRIDCONNECTION_CONFIG)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_GRIDCONNECTION_CONFIG, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) ((MsctResponse) iMsg).getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 64) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_GRIDCONNECTION_CONFIG);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_GRIDCONNECTION_CONFIG);
                                float gridOvervoltage1ProtectTime = BinaryUtils.bytes2IntLittle(new byte[]{payload[0], payload[1], 0, 0}) / 10f;
                                float gridOvervoltage2ProtectTime = BinaryUtils.bytes2IntLittle(new byte[]{payload[2], payload[3], 0, 0}) / 10f;
                                float gridUndervoltage1ProtectTime = BinaryUtils.bytes2IntLittle(new byte[]{payload[4], payload[5], 0, 0}) / 10f;
                                float gridUndervoltage2ProtectTime = BinaryUtils.bytes2IntLittle(new byte[]{payload[6], payload[7], 0, 0}) / 10f;
                                float activeSoftStartRate = BinaryUtils.bytes2IntLittle(new byte[]{payload[8], payload[9], 0, 0}) / 100f;
                                boolean appFastPowerDown = BinaryUtils.bytes2IntLittle(new byte[]{payload[10], payload[11], 0, 0}) == 1;
                                float overfrequencyLoadShedding = BinaryUtils.bytes2IntLittle(new byte[]{payload[12], payload[13], 0, 0}) / 100f;
                                float overfrequencyLoadSheddingSlope = BinaryUtils.bytes2IntLittle(new byte[]{payload[14], payload[15], 0, 0}) / 100f;
                                float underfrequencyLoadShedding = BinaryUtils.bytes2IntLittle(new byte[]{payload[16], payload[17], 0, 0}) / 100f;
                                boolean quMode = BinaryUtils.bytes2IntLittle(new byte[]{payload[18], payload[19], 0, 0}) == 1;
                                boolean cosMode = BinaryUtils.bytes2IntLittle(new byte[]{payload[20], payload[21], 0, 0}) == 1;
                                boolean antiislandingMode = BinaryUtils.bytes2IntLittle(new byte[]{payload[22], payload[23], 0, 0}) == 1;
                                boolean highlowMode = BinaryUtils.bytes2IntLittle(new byte[]{payload[24], payload[25], 0, 0}) == 1;
                                float powerFactor = BinaryUtils.bytes2IntLittle(new byte[]{payload[26], payload[27], 0, 0}) / 100f;
                                float reconnectTime = BinaryUtils.bytes2IntLittle(new byte[]{payload[28], payload[29], 0, 0}) / 10f;
                                float reconnectSlope = BinaryUtils.bytes2IntLittle(new byte[]{payload[30], payload[31], 0, 0}) / 100f;
                                float gridOvervoltage1 = BinaryUtils.bytes2IntLittle(new byte[]{payload[32], payload[33], 0, 0}) / 10f;
                                float gridOvervoltage2 = BinaryUtils.bytes2IntLittle(new byte[]{payload[34], payload[35], 0, 0}) / 10f;
                                float gridUndervoltage1 = BinaryUtils.bytes2IntLittle(new byte[]{payload[36], payload[37], 0, 0}) / 10f;
                                float gridUndervoltage2 = BinaryUtils.bytes2IntLittle(new byte[]{payload[38], payload[39], 0, 0}) / 10f;
                                float gridOverfrequency1 = BinaryUtils.bytes2IntLittle(new byte[]{payload[40], payload[41], 0, 0}) / 100f;
                                float gridOverfrequency2 = BinaryUtils.bytes2IntLittle(new byte[]{payload[42], payload[43], 0, 0}) / 100f;
                                float gridUnderfrequency1 = BinaryUtils.bytes2IntLittle(new byte[]{payload[44], payload[45], 0, 0}) / 100f;
                                float gridUnderfrequency2 = BinaryUtils.bytes2IntLittle(new byte[]{payload[46], payload[47], 0, 0}) / 100f;
                                float powerPercent = BinaryUtils.bytes2IntLittle(new byte[]{payload[48], payload[49], 0, 0}) / 100f;
                                int gridOverfrequencyProtectTime = BinaryUtils.bytes2IntLittle(new byte[]{payload[50], payload[51], 0, 0});
                                int gridUnderfrequencyProtectTime = BinaryUtils.bytes2IntLittle(new byte[]{payload[52], payload[53], 0, 0});
                                float underfrequencyLoadSheddingSlope = BinaryUtils.bytes2IntLittle(new byte[]{payload[54], payload[55], 0, 0}) / 1000f;
                                float rebootGridUnderfrequency = BinaryUtils.bytes2IntLittle(new byte[]{payload[56], payload[57], 0, 0}) / 100f;
                                float rebootGridOverfrequency = BinaryUtils.bytes2IntLittle(new byte[]{payload[58], payload[59], 0, 0}) / 100f;
                                float rebootGridUndervoltage = BinaryUtils.bytes2IntLittle(new byte[]{payload[60], payload[61], 0, 0}) / 10f;
                                float rebootGridOvervoltage = BinaryUtils.bytes2IntLittle(new byte[]{payload[62], payload[63], 0, 0}) / 10f;

//                                float activeSoftStartRate =BinaryUtils.bytes2IntLittle(new byte[]{payload[0], payload[1], 0, 0}) / 100f;
//                                boolean appFastPowerDown = BinaryUtils.bytes2IntLittle(new byte[]{payload[2], payload[3], 0, 0}) == 1;
//                                float overfrequencyLoadShedding = BinaryUtils.bytes2IntLittle(new byte[]{payload[4], payload[5], 0, 0}) / 100f;
//                                float overfrequencyLoadSheddingSlope = BinaryUtils.bytes2IntLittle(new byte[]{payload[6], payload[7], 0, 0}) / 100f;
//                                float underfrequencyLoadShedding = BinaryUtils.bytes2IntLittle(new byte[]{payload[8], payload[9], 0, 0}) / 100f;
//                                boolean quMode = BinaryUtils.bytes2IntLittle(new byte[]{payload[10], payload[11], 0, 0}) == 1;
//                                boolean cosMode = BinaryUtils.bytes2IntLittle(new byte[]{payload[12], payload[13], 0, 0}) == 1;
//                                boolean antiislandingMode = BinaryUtils.bytes2IntLittle(new byte[]{payload[14], payload[15], 0, 0}) == 1;
//                                boolean highlowMode = BinaryUtils.bytes2IntLittle(new byte[]{payload[16], payload[17], 0, 0}) == 1;
//                                float powerFactor = BinaryUtils.bytes2IntLittle(new byte[]{payload[18], payload[19], 0, 0}) / 100f;
//                                int reconnectTime = BinaryUtils.bytes2IntLittle(new byte[]{payload[20], payload[21], 0, 0});
//                                float reconnectSlope = BinaryUtils.bytes2IntLittle(new byte[]{payload[22], payload[23], 0, 0}) / 100f;
//                                float gridOvervoltage1 = BinaryUtils.bytes2IntLittle(new byte[]{payload[24], payload[25], 0, 0}) / 10f;
//                                float gridOvervoltage2 = BinaryUtils.bytes2IntLittle(new byte[]{payload[26], payload[27], 0, 0}) / 10f;
//                                float gridUndervoltage1 = BinaryUtils.bytes2IntLittle(new byte[]{payload[28], payload[29], 0, 0}) / 10f;
//                                float gridUndervoltage2 = BinaryUtils.bytes2IntLittle(new byte[]{payload[30], payload[31], 0, 0}) / 10f;
//                                float gridOverfrequency1 = BinaryUtils.bytes2IntLittle(new byte[]{payload[32], payload[33], 0, 0}) / 100f;
//                                float gridOverfrequency2 = BinaryUtils.bytes2IntLittle(new byte[]{payload[34], payload[35], 0, 0}) / 100f;
//                                float gridUnderfrequency1 = BinaryUtils.bytes2IntLittle(new byte[]{payload[36], payload[37], 0, 0}) / 100f;
//                                float gridUnderfrequency2 = BinaryUtils.bytes2IntLittle(new byte[]{payload[38], payload[39], 0, 0}) / 100f;
                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_OVER_VOLTAGE_1_PROTECT_TIME, gridOvervoltage1ProtectTime);
                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_OVER_VOLTAGE_2_PROTECT_TIME, gridOvervoltage2ProtectTime);
                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_UNDER_VOLTAGE_1_PROTECT_TIME, gridUndervoltage1ProtectTime);
                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_UNDER_VOLTAGE_2_PROTECT_TIME, gridUndervoltage2ProtectTime);
                                Cmd.putResultValue(resultMap, BmtDataKey.ACTIVE_SOFT_START_RATE, activeSoftStartRate);
                                Cmd.putResultValue(resultMap, BmtDataKey.APP_FAST_POWER_DOWN, appFastPowerDown);
                                Cmd.putResultValue(resultMap, BmtDataKey.OVER_FREQUENCY_LOAD_SHEDDING, overfrequencyLoadShedding);
                                Cmd.putResultValue(resultMap, BmtDataKey.OVER_FREQUENCY_LOAD_SHEDDING_SLOPE, overfrequencyLoadSheddingSlope);
                                Cmd.putResultValue(resultMap, BmtDataKey.UNDER_FREQUENCY_LOAD_SHEDDING, underfrequencyLoadShedding);
                                Cmd.putResultValue(resultMap, BmtDataKey.QU_MODE, quMode);
                                Cmd.putResultValue(resultMap, BmtDataKey.COS_MODE, cosMode);
                                Cmd.putResultValue(resultMap, BmtDataKey.ANTI_ISLANDING_MODE, antiislandingMode);
                                Cmd.putResultValue(resultMap, BmtDataKey.HIGH_LOW_MODE, highlowMode);
                                Cmd.putResultValue(resultMap, BmtDataKey.POWER_FACTOR, powerFactor);
                                Cmd.putResultValue(resultMap, BmtDataKey.RECONNECT_TIME, reconnectTime);
                                Cmd.putResultValue(resultMap, BmtDataKey.RECONNECT_SLOPE, reconnectSlope);
                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_OVER_VOLTAGE_1, gridOvervoltage1);
                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_OVER_VOLTAGE_2, gridOvervoltage2);
                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_UNDER_VOLTAGE_1, gridUndervoltage1);
                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_UNDER_VOLTAGE_2, gridUndervoltage2);
                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_OVER_FREQUENCY_1, gridOverfrequency1);
                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_OVER_FREQUENCY_2, gridOverfrequency2);
                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_UNDER_FREQUENCY_1, gridUnderfrequency1);
                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_UNDER_FREQUENCY_2, gridUnderfrequency2);
                                Cmd.putResultValue(resultMap, BmtDataKey.POWER_PERCENT, powerPercent);
                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_OVER_FREQUENCY_PROTECT_TIME, gridOverfrequencyProtectTime);
                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_UNDER_FREQUENCY_PROTECT_TIME, gridUnderfrequencyProtectTime);
                                Cmd.putResultValue(resultMap, BmtDataKey.UNDER_FREQUENCY_LOAD_SHEDDING_SLOPE, underfrequencyLoadSheddingSlope);
                                Cmd.putResultValue(resultMap, BmtDataKey.REBOOT_GRID_UNDER_FREQUENCY, rebootGridUnderfrequency);
                                Cmd.putResultValue(resultMap, BmtDataKey.REBOOT_GRID_OVER_FREQUENCY, rebootGridOverfrequency);
                                Cmd.putResultValue(resultMap, BmtDataKey.REBOOT_GRID_UNDER_VOLTAGE, rebootGridUndervoltage);
                                Cmd.putResultValue(resultMap, BmtDataKey.REBOOT_GRID_OVER_FREQUENCY, rebootGridOvervoltage);


//                                Cmd.putResultValue(resultMap, BmtDataKey.APP_FAST_POWER_DOWN, appFastPowerDown);
//                                Cmd.putResultValue(resultMap, BmtDataKey.QU_MODE, quMode);
//                                Cmd.putResultValue(resultMap, BmtDataKey.COS_MODE, cosMode);
//                                Cmd.putResultValue(resultMap, BmtDataKey.ANTI_ISLANDING_MODE, antiislandingMode);
//                                Cmd.putResultValue(resultMap, BmtDataKey.HIGH_LOW_MODE, highlowMode);
//                                Cmd.putResultValue(resultMap, BmtDataKey.ACTIVE_SOFT_START_RATE, activeSoftStartRate);
//                                Cmd.putResultValue(resultMap, BmtDataKey.OVER_FREQUENCY_LOAD_SHEDDING, overfrequencyLoadShedding);
//                                Cmd.putResultValue(resultMap, BmtDataKey.OVER_FREQUENCY_LOAD_SHEDDING_SLOPE, overfrequencyLoadSheddingSlope);
//                                Cmd.putResultValue(resultMap, BmtDataKey.UNDER_FREQUENCY_LOAD_SHEDDING, underfrequencyLoadShedding);
//                                Cmd.putResultValue(resultMap, BmtDataKey.POWER_FACTOR, powerFactor);
//                                Cmd.putResultValue(resultMap, BmtDataKey.RECONNECT_TIME, reconnectTime);
//                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_OVER_VOLTAGE_1, gridOvervoltage1);
//                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_OVER_VOLTAGE_2, gridOvervoltage2);
//                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_UNDER_VOLTAGE_1, gridUndervoltage1);
//                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_UNDER_VOLTAGE_2, gridUndervoltage2);
//                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_OVER_FREQUENCY_1, gridOverfrequency1);
//                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_OVER_FREQUENCY_2, gridOverfrequency2);
//                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_UNDER_FREQUENCY_1, gridUnderfrequency1);
//                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_UNDER_FREQUENCY_2, gridUnderfrequency2);
//                                Cmd.putResultValue(resultMap, BmtDataKey.RECONNECT_SLOPE, reconnectSlope);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_GRIDCONNECTION_CONFIG);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_GRIDCONNECTION_CONFIG);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getTimeoutResultMap(BmtCmd.GET_GRIDCONNECTION_CONFIG);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getViewExceptions(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_VIEW_EXCEPTIONS)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_VIEW_EXCEPTIONS, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) ((MsctResponse) iMsg).getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 14) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_VIEW_EXCEPTIONS);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_VIEW_EXCEPTIONS);

                                int flag;
                                flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[0], payload[1], 0, 0});
                                List<Integer> vertBatteryExceptions = McuDataHelper.parseInverterBatteryException(flag);
                                flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[2], payload[3], 0, 0});
                                List<Integer> vertExceptions = McuDataHelper.parseInverterException(flag);
                                flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[4], payload[5], 0, 0});
                                List<Integer> vertGridExceptions = McuDataHelper.parseInverterGridException(flag);
                                flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[6], payload[7], 0, 0});
                                List<Integer> vertSystemExceptions = McuDataHelper.parseInverterSystemException(flag);
                                flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[8], payload[9], 0, 0});
                                List<Integer> vertmpptExceptions = McuDataHelper.parseInverterMPPTException(flag);
                                flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[10], payload[11], 0, 0});
                                List<Integer> vertPresentExceptions = McuDataHelper.parseInverterPresentException(flag);
                                flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[12], payload[13], 0, 0});
                                List<Integer> vertDCExceptions = McuDataHelper.parseInverterDCException(flag);
                                List evException = McuDataHelper.parseEVException(BinaryUtils.bytes2IntLittle(new byte[]{payload[14], payload[15], 0, 0}));
                                List mpptException = McuDataHelper.parseMPPTException(BinaryUtils.bytes2IntLittle(new byte[]{payload[16], payload[17], 0, 0}));
                                List cabinetException = McuDataHelper.parseCabinetException(BinaryUtils.bytes2IntLittle(new byte[]{payload[18], payload[19], 0, 0}));
                                List batteryException = McuDataHelper.parseBatteryException(BinaryUtils.bytes2IntLittle(new byte[]{payload[20], payload[21], 0, 0}) >> 2);
                                List systemException = McuDataHelper.parseSystemException(BinaryUtils.bytes2IntLittle(new byte[]{payload[22], payload[23], 0, 0}));
                                List communicationException = McuDataHelper.parseCommunicationException(BinaryUtils.bytes2IntLittle(new byte[]{payload[24], payload[25], 0, 0}));

                                Cmd.putResultValue(resultMap, BmtDataKey.VERT_BATTERY_EXCEPTIONS, vertBatteryExceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERT_EXCEPTIONS, vertExceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERT_GRID_EXCEPTIONS, vertGridExceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERT_SYSTEM_EXCEPTIONS, vertSystemExceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERT_MPPT_EXCEPTIONS, vertmpptExceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERT_PRESENT_EXCEPTIONS, vertPresentExceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERT_DC_EXCEPTIONS, vertDCExceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.EV, evException);
                                Cmd.putResultValue(resultMap, BmtDataKey.MPPT, mpptException);
                                Cmd.putResultValue(resultMap, BmtDataKey.CABINET, cabinetException);
                                Cmd.putResultValue(resultMap, BmtDataKey.BATTERY, batteryException);
                                Cmd.putResultValue(resultMap, BmtDataKey.SYSTEM, systemException);
                                Cmd.putResultValue(resultMap, BmtDataKey.COMMUNICATION, communicationException);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_VIEW_EXCEPTIONS);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_VIEW_EXCEPTIONS);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_VIEW_EXCEPTIONS);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory resetInverter(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.RESET_INVERTER)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.RESET_INVERTER, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 1) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.RESET_INVERTER);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.RESET_INVERTER);
                                int delay = payload[0];
                                Cmd.putResultValue(resultMap, BmtDataKey.DELAY, delay);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map<String, Object> resultMap = Cmd.getDefaultResultMap(false, BmtCmd.RESET_INVERTER);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.RESET_INVERTER);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.RESET_INVERTER);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory setEvchargingmodeInstant(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        int evChargingMode = DeviceHelper.getInt(arg, BmtDataKey.EV_CHARGING_MODE, 1);
        int fixed = DeviceHelper.getInt(arg, BmtDataKey.FIXED, 0);
        byte[] params = new byte[4];
        params[0] = (byte) (evChargingMode - 1);
        params[1] = (byte) (evChargingMode == 5 ? 0x00 : 0x01);
        System.arraycopy(BinaryUtils.intToByteLittle(fixed), 0, params, 2, 2);
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.SET_EVCHARGINGMODE_INSTANT)
                .setIsNeedResult(false)
                .setPayload(params)
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (response.isSuccess()) {
                                    printResponse(response);
                                }
                                Map resultMap = Cmd.getDefaultResultMap(response.isSuccess(), BmtCmd.SET_EVCHARGINGMODE_INSTANT);
                                resultMap.put(BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                Cmd.putResultValue(resultMap, BmtDataKey.EV_CHARGING_MODE, evChargingMode);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_EVCHARGINGMODE_INSTANT);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_EVCHARGINGMODE_INSTANT);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_EVCHARGINGMODE_INSTANT);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                });
        return msctBuilder.build();
    }


    private MsctDataFactory getGlobalCurrentFlowInfo(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        int dataMode = DeviceHelper.getInt(arg, BmtDataKey.DATA_MODE, 1);
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_GLOBAL_CURRENT_FLOW_INFO)
                .setIsNeedResult(false)
                .setPayload(new byte[]{(byte) dataMode})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_GLOBAL_CURRENT_FLOW_INFO, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 16) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_GLOBAL_CURRENT_FLOW_INFO);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                int payloadLen = payload.length;
                                int newLen = Math.max(payloadLen, 22);
                                byte[] newPayload = new byte[newLen];
                                System.arraycopy(payload, 0, newPayload, 0, payloadLen);
                                if (payloadLen < newLen) {
                                    for (int k = 0; k < (newLen - payloadLen); k++) {
                                        int index = payloadLen + k;
                                        if (index == 16 || index == 17) {
                                            newPayload[payloadLen + k] = 1;
                                        } else {
                                            newPayload[payloadLen + k] = 0;
                                        }
                                    }
                                }
                                int batteryWat = BinaryUtils.byteToShortLittle(new byte[]{payload[0], payload[1]});
                                int solarWat = BinaryUtils.byteToShortLittle(new byte[]{payload[2], payload[3]});
                                int gridWat = BinaryUtils.byteToShortLittle(new byte[]{payload[4], payload[5]});
                                int additionLoadWat = BinaryUtils.byteToShortLittle(new byte[]{payload[6], payload[7]});
                                int otherLoadWat = BinaryUtils.byteToShortLittle(new byte[]{payload[8], payload[9]});
                                int vechiWat = BinaryUtils.byteToShortLittle(new byte[]{payload[10], payload[11]});
                                int ip2Wat = BinaryUtils.bytes2IntLittle(new byte[]{payload[12], payload[13], 0, 0});
                                int op2Wat = BinaryUtils.bytes2IntLittle(new byte[]{payload[14], payload[15], 0, 0});
                                boolean gridValid = newPayload[16] == 1;
                                boolean bsensorValid = newPayload[17] == 1;
                                int solarEfficiency = McuDataHelper.parseSolarEfficiencyType(newPayload[18]);
                                boolean thirdpartyPVOn = newPayload[19] == 1;
                                int dualPowerWat = BinaryUtils.byteToShortLittle(new byte[]{newPayload[20], newPayload[21], 0, 0});
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_GLOBAL_CURRENT_FLOW_INFO);
                                Cmd.putResultValue(resultMap, BmtDataKey.BATTERY_WAT, batteryWat);
                                Cmd.putResultValue(resultMap, BmtDataKey.SOLAR_WAT, solarWat);
                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_WAT, gridWat);
                                Cmd.putResultValue(resultMap, BmtDataKey.ADDITIONAL_LOAD_WAT, additionLoadWat);
                                Cmd.putResultValue(resultMap, BmtDataKey.OTHER_LOAD_WAT, otherLoadWat);
                                Cmd.putResultValue(resultMap, BmtDataKey.VECHI_WAT, vechiWat);
                                Cmd.putResultValue(resultMap, BmtDataKey.IP2_WAT, ip2Wat);
                                Cmd.putResultValue(resultMap, BmtDataKey.OP2_WAT, op2Wat);
                                Cmd.putResultValue(resultMap, BmtDataKey.GRID_VALID, gridValid);
                                Cmd.putResultValue(resultMap, BmtDataKey.BSENSOR_VALID, bsensorValid);
                                Cmd.putResultValue(resultMap, BmtDataKey.SOLAR_EFFICIENCY, solarEfficiency);
                                Cmd.putResultValue(resultMap, BmtDataKey.THIRD_PARTY_PV_ON, thirdpartyPVOn);
                                Cmd.putResultValue(resultMap, BmtDataKey.DUAL_POWER_WAT, dualPowerWat);
                                device.setGridStatus(gridValid ? 2 : 1);
                                device.setThirdpartyPVOn(thirdpartyPVOn);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_GLOBAL_CURRENT_FLOW_INFO);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_GLOBAL_CURRENT_FLOW_INFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_GLOBAL_CURRENT_FLOW_INFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                });
        return msctBuilder.build();
    }

    private MsctDataFactory setEVCMInstanceCharge(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        boolean isOpen = DeviceHelper.getBoolean(arg, BmtDataKey.OPEN, false);
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.SET_EVCHARGINGMODE_INSTANTCHARGE)
                .setIsNeedResult(false)
                .setPayload(new byte[]{(byte) (isOpen ? 0x01 : 0x00)})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.SET_EVCHARGINGMODE_INSTANTCHARGE, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 1) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_EVCHARGINGMODE_INSTANTCHARGE);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }

                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.SET_EVCHARGINGMODE_INSTANTCHARGE);
                                Cmd.putResultValue(resultMap, BmtDataKey.EV_CHARGING_MODE, McuDataHelper.parseEVChargingMode(payload[0]));
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_EVCHARGINGMODE_INSTANTCHARGE);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_EVCHARGINGMODE_INSTANTCHARGE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_EVCHARGINGMODE_INSTANTCHARGE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory setExceptionIgnore(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        final int ignoreException = (int) MapUtils.get(arg, BmtDataKey.IGNORE_EXCEPTION, 0);
        final Mcu.Exception.IgnoreException mode = Mcu.Exception.IgnoreException.valueOf(ignoreException);
        if (mode == null) {
            return null;
        }
        final byte[] payload = new byte[1];
        payload[0] = (byte) mode.getCode();
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.SET_EXCEPTION_IGNORE)
                .setIsNeedResult(false)
                .setPayload(payload)
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (response.isSuccess()) {
                                    printResponse(response);
                                }
                                Map resultMap = Cmd.getDefaultResultMap(response.isSuccess(), BmtCmd.SET_EXCEPTION_IGNORE);
                                resultMap.put(BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_EXCEPTION_IGNORE);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_EXCEPTION_IGNORE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_EXCEPTION_IGNORE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private void notifyError(@NonNull final String cmd, @Nullable final Object msg) {
        final Map resultMap = Cmd.getDefaultResultMap(false, cmd);
        if (null != msg) {
            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, msg);
        }
        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
    }

    private MsctDataFactory setRegion(@NonNull final String cmd, Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        final boolean isPriceTrackingSupported = DeviceHelper.getBoolean(arg, BmtDataKey.IS_PRICE_TRACKING_SUPPORTED, false);
        final String countryCode = DeviceHelper.getString(arg, BmtDataKey.COUNTRY_CODE, "");
        final String countryName = DeviceHelper.getString(arg, BmtDataKey.COUNTRY_NAME, "");
        final String cityName = DeviceHelper.getString(arg, BmtDataKey.CITY_NAME, "");
        final String timeZone = DeviceHelper.getString(arg, BmtDataKey.TIMEZONE, "");
        final String deliveryAreas = DeviceHelper.getString(arg, BmtDataKey.DELIVERY_AREAS, "");
        if (TextUtils.isEmpty(countryCode) || TextUtils.isEmpty(countryName)
                || TextUtils.isEmpty(timeZone)) {
            return null;
        }

        final byte[] isPTSupportedPayload = new byte[]{(byte) (isPriceTrackingSupported ? 0x01 : 0x00)};
        final byte[] countryCodePayload = getStrParamByteArr(countryCode);
        final byte[] countryNamePayLoads = getStrParamByteArr(countryName);
        final byte[] cityNamePayLoads = getStrParamByteArr(cityName);
        final byte[] timezonePayLoads = getStrParamByteArr(timeZone);
        final byte[] deliveryAreasPayLoads = getStrParamByteArr(deliveryAreas);
        int isPTLen = isPTSupportedPayload.length;
        int countryCodeLen = countryCodePayload.length;
        int countryNameLen = countryNamePayLoads.length;
        int cityNameLen = cityNamePayLoads.length;
        int timezoneLen = timezonePayLoads.length;
        int deliveryAreasLen = deliveryAreasPayLoads.length;

        final byte[] payloads = new byte[isPTLen + countryCodeLen + countryNameLen + cityNameLen + timezoneLen + deliveryAreasLen];
        System.arraycopy(isPTSupportedPayload, 0, payloads, 0, isPTLen);
        System.arraycopy(countryCodePayload, 0, payloads, isPTLen, countryCodeLen);
        System.arraycopy(countryNamePayLoads, 0, payloads, isPTLen + countryCodeLen, countryNameLen);
        System.arraycopy(cityNamePayLoads, 0, payloads, isPTLen + countryCodeLen + countryNameLen, cityNameLen);
        System.arraycopy(timezonePayLoads, 0, payloads, isPTLen + countryCodeLen + countryNameLen + cityNameLen, timezoneLen);
        System.arraycopy(deliveryAreasPayLoads, 0, payloads, isPTLen + countryCodeLen + countryNameLen + cityNameLen + timezoneLen, deliveryAreasLen);
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.SET_REGION)
                .setIsNeedResult(false)
                .setPayload(payloads)
                .setContentType(ContentType.rawByte)
                .setCallBack(new BaseMsctRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                Map<String, Object> resultMap;
                                if (!response.isSuccess()) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_REGION);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                EventBus.getDefault().post(new BmtCountryCodeUpdateEvent(device.getId(), countryCode, deliveryAreas));
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.SET_REGION);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            notifyError(cmd, e.getMessage());
                        }
                    }

                    @Override
                    public void onFail() {
                        notifyError(cmd, null);
                    }

                    @Override
                    public void onTimeOut() {
                        notifyError(cmd, "Timeout");
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getEVChargingInfo(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_EVCHARGING_INFO)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_EVCHARGING_INFO, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 4) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_EVCHARGING_INFO);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_EVCHARGING_INFO);
                                int batteryCharged = BinaryUtils.bytes2IntLittle(new byte[]{payload[0], payload[1], 0, 0});
                                int chargeTime = BinaryUtils.bytes2IntLittle(new byte[]{payload[2], payload[3], 0, 0});
                                Cmd.putResultValue(resultMap, BmtDataKey.BATTERY_CHARGED, batteryCharged);
                                Cmd.putResultValue(resultMap, BmtDataKey.CHARGE_TIME, chargeTime);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map<String, Object> resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_EVCHARGING_INFO);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_EVCHARGING_INFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_EVCHARGING_INFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getCurrentEVAdvanceStatus(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_CURRENT_EVADVANCESTATUS)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_CURRENT_EVADVANCESTATUS, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 1) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CURRENT_EVADVANCESTATUS);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_CURRENT_EVADVANCESTATUS);
                                Cmd.putResultValue(resultMap, BmtDataKey.ADVANCE_STATUS, McuDataHelper.parseEVAdvanceStatus(payload[0]));
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map<String, Object> resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CURRENT_EVADVANCESTATUS);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }

                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CURRENT_EVADVANCESTATUS);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CURRENT_EVADVANCESTATUS);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory setEVChargingMode(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }

        final byte[] payload = new byte[9];
        final int evChargingMode = (int) MapUtils.get(arg, BmtDataKey.EV_CHARGING_MODE, 0);
        final List<Integer> weekdays = (List) MapUtils.get(arg, BmtDataKey.WEEKDAYS, null);
        final List<Integer> weekend = (List) MapUtils.get(arg, BmtDataKey.WEEKEND, null);
        final boolean sync = DeviceHelper.getBoolean(arg, BmtDataKey.SYNC, false);
        payload[0] = (byte) (evChargingMode - 1);
        if (weekdays == null || weekdays.size() < 24 || weekend == null || weekend.size() < 24) {
            payload[1] = 0x01;
            for (int i = 2; i < payload.length - 1; i++) {
                payload[i] = 0x00;
            }
        } else {
            payload[1] = 0x00;
            payload[2] = (byte) (Integer.parseInt(splitString(weekdays, 0, 7), 2));
            payload[3] = (byte) (Integer.parseInt(splitString(weekdays, 8, 15), 2));
            payload[4] = (byte) (Integer.parseInt(splitString(weekdays, 16, 23), 2));
            payload[5] = (byte) (Integer.parseInt(splitString(weekend, 0, 7), 2));
            payload[6] = (byte) (Integer.parseInt(splitString(weekend, 8, 15), 2));
            payload[7] = (byte) (Integer.parseInt(splitString(weekend, 16, 23), 2));
        }
        payload[8] = (byte) (sync ? 0x01 : 0x00);
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.SET_EV_CHARGING_MODE)
                .setIsNeedResult(false)
                .setPayload(payload)
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (response.isSuccess()) {
                                    printResponse(response);
                                }
                                Map resultMap = Cmd.getDefaultResultMap(response.isSuccess(), BmtCmd.SET_EV_CHARGING_MODE);
                                resultMap.put(BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                Cmd.putResultValue(resultMap, BmtDataKey.EV_CHARGING_MODE, evChargingMode);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_EV_CHARGING_MODE);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_EV_CHARGING_MODE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_EV_CHARGING_MODE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }


    private String splitString(List<Integer> list, int startIndex, int endIndex) {
        String str = "";
        if (list == null || list.size() < endIndex || startIndex > endIndex) {
            return str;
        }
        for (int i = endIndex; i >= startIndex; i--) {
            str = str + list.get(i);
        }
        return str;
    }

    private MsctDataFactory getEVChargingModeSchedule(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_EV_CHARGING_MODE_SCHEDULE)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_EV_CHARGING_MODE_SCHEDULE, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 7) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_EV_CHARGING_MODE_SCHEDULE);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                List weekdays = new ArrayList();
                                List weekend = new ArrayList();
                                for (int p = 0; p < payload.length - 1; p++) {
                                    int num = BinaryUtils.bytes2IntLittle(new byte[]{payload[p], 0, 0, 0});
                                    for (int i = 0; i <= 7; i++) {
                                        if (p < 3) {
                                            weekdays.add(BinaryUtils.getBitValue(num, i));
                                        } else {
                                            weekend.add(BinaryUtils.getBitValue(num, i));
                                        }
                                    }
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_EV_CHARGING_MODE_SCHEDULE);
                                Cmd.putResultValue(resultMap, BmtDataKey.WEEKDAYS, weekdays);
                                Cmd.putResultValue(resultMap, BmtDataKey.WEEKEND, weekend);
                                // 暂不需用到
                                Cmd.putResultValue(resultMap, BmtDataKey.SYNC, BinaryUtils.getBitValue(payload[6], 1) == 1);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map<String, Object> resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_EV_CHARGING_MODE_SCHEDULE);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }

                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_EV_CHARGING_MODE_SCHEDULE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_EV_CHARGING_MODE_SCHEDULE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getCurrentEVChargingMode(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_CURRENT_EV_CHARGING_MODE)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_CURRENT_EV_CHARGING_MODE, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 6) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CURRENT_EV_CHARGING_MODE);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;

                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_CURRENT_EV_CHARGING_MODE);
                                Cmd.putResultValue(resultMap, BmtDataKey.EV_CHARGING_MODE, McuDataHelper.parseEVChargingMode(payload[0]));
                                Cmd.putResultValue(resultMap, BmtDataKey.FIXED, BinaryUtils.bytes2IntLittle(new byte[]{payload[1], payload[2], 0, 0}));
                                Cmd.putResultValue(resultMap, BmtDataKey.FIXED_FULL, BinaryUtils.bytes2IntLittle(new byte[]{payload[3], payload[4], 0, 0}));
                                Cmd.putResultValue(resultMap, BmtDataKey.PRICE_PERCENT, (int) payload[5]);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CURRENT_EV_CHARGING_MODE);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CURRENT_EV_CHARGING_MODE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CURRENT_EV_CHARGING_MODE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory setReserveMode(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        final int reserveMode = (int) MapUtils.get(arg, BmtDataKey.RESERVE_MODE, 0);
        final Mcu.Reserve.ReserveMode mode = Mcu.Reserve.ReserveMode.valueOf(reserveMode);
        final int smart = (int) MapUtils.get(arg, BmtDataKey.SMART, 0);
        final int emergency = (int) MapUtils.get(arg, BmtDataKey.EMERGENCY, 0);
        final int[] weekdays = (int[]) MapUtils.get(arg, BmtDataKey.WEEKDAYS, new int[0]);
        final int[] weekend = (int[]) MapUtils.get(arg, BmtDataKey.WEEKEND, new int[0]);
        final boolean sync = DeviceHelper.getBoolean(arg, BmtDataKey.SYNC, false);
        final byte[] payload = new byte[53];
        payload[0] = (byte) (reserveMode - 1);
        payload[2] = (byte) smart;
        payload[3] = (byte) emergency;
        if ((mode == Mcu.Reserve.ReserveMode.scheduled || mode == Mcu.Reserve.ReserveMode.aiAdapter) && (weekdays == null || weekdays.length <= 0)
                && (weekend == null || weekend.length <= 0)) {
            payload[1] = 0x01;
            // 忽略时，第5到52byte统一设置为0
            for (int i = 4; i < 52; i++) {
                payload[i] = 0x00;
            }
        } else {
            payload[1] = 0x00;
            for (int i = 0; i < weekdays.length; i++) {
                System.arraycopy(BinaryUtils.intToByteLittle(weekdays[i], false), 0, payload, (i + 4), 1);
            }
            for (int i = 0; i < weekend.length; i++) {
                System.arraycopy(BinaryUtils.intToByteLittle(weekend[i], false), 0, payload, (i + 28), 1);
            }
        }
        payload[52] = (byte) (sync ? 0x01 : 0x00);
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.SET_RESERVE_MODE)
                .setIsNeedResult(false)
                .setPayload(payload)
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (response.isSuccess()) {
                                    printResponse(response);
                                }
                                Map resultMap = Cmd.getDefaultResultMap(response.isSuccess(), BmtCmd.SET_RESERVE_MODE);
                                resultMap.put(BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_RESERVE_MODE);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_RESERVE_MODE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_RESERVE_MODE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getScheduleReserveMode(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_SCHEDULE_RESERVE_MODE)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse msctResponse = (MsctResponse) iMsg;
                                if (!msctResponse.isSuccess()) {
                                    notifyError(BmtCmd.GET_SCHEDULE_RESERVE_MODE, msctResponse.getErrorMsg());
                                    return;
                                }
                                printResponse(msctResponse);
                                byte[] payload = (byte[]) msctResponse.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 53) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_SCHEDULE_RESERVE_MODE);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_SCHEDULE_RESERVE_MODE);
                                final int smart = BinaryUtils.bytes2IntLittle(new byte[]{payload[0], 0, 0, 0});
                                final int emergency = BinaryUtils.bytes2IntLittle(new byte[]{payload[1], 0, 0, 0});
                                final int lowPowerAlert = BinaryUtils.bytes2IntLittle(new byte[]{payload[2], 0, 0, 0});
                                final int batteryProtect = BinaryUtils.bytes2IntLittle(new byte[]{payload[3], 0, 0, 0});
                                final Integer[] weekdays = bytesToArray(payload, 4, 27);
                                final Integer[] weekend = bytesToArray(payload, 28, 51);
                                Cmd.putResultValue(resultMap, BmtDataKey.SMART, smart);
                                Cmd.putResultValue(resultMap, BmtDataKey.EMERGENCY, emergency);
                                Cmd.putResultValue(resultMap, BmtDataKey.LOW_POWER_ALERT, lowPowerAlert);
                                Cmd.putResultValue(resultMap, BmtDataKey.BATTERY_PROTECT, batteryProtect);
                                Cmd.putResultValue(resultMap, BmtDataKey.WEEKDAYS, weekdays);
                                Cmd.putResultValue(resultMap, BmtDataKey.WEEKEND, weekend);
                                Cmd.putResultValue(resultMap, BmtDataKey.SYNC, payload[52] == 0x01);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_SCHEDULE_RESERVE_MODE);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }

                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_SCHEDULE_RESERVE_MODE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_SCHEDULE_RESERVE_MODE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private Integer[] bytesToArray(byte[] payload, int startIndex, int endIndex) {
        if (payload == null || payload.length < startIndex || endIndex < startIndex) {
            return null;
        }

        ArrayList<Integer> list = new ArrayList<>();
        for (int i = startIndex; i <= endIndex; i++) {
            int value = BinaryUtils.bytes2IntLittle(new byte[]{payload[i], 0, 0, 0}, false);
            list.add(value);
        }
        Integer[] array = list.toArray(new Integer[list.size()]);
        return array;
    }

    private MsctDataFactory getPriceTrackReserveMode(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_PRICE_TRACK_RESERVE_MODE)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_PRICE_TRACK_RESERVE_MODE, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 9) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_PRICE_TRACK_RESERVE_MODE);
                                    Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                final int smart = BinaryUtils.bytes2IntLittle(new byte[]{payload[0], 0, 0, 0});
                                final int emergency = BinaryUtils.bytes2IntLittle(new byte[]{payload[1], 0, 0, 0});
                                final int lowPowerAlert = BinaryUtils.bytes2IntLittle(new byte[]{payload[2], 0, 0, 0});
                                final int batteryProtect = BinaryUtils.bytes2IntLittle(new byte[]{payload[3], 0, 0, 0});
                                final int c1 = BinaryUtils.bytes2IntLittle(new byte[]{payload[4], 0, 0, 0}, false);
                                final int c2 = BinaryUtils.bytes2IntLittle(new byte[]{payload[5], 0, 0, 0}, false);
                                final int c3 = BinaryUtils.bytes2IntLittle(new byte[]{payload[6], 0, 0, 0}, false);
                                final int s1 = BinaryUtils.bytes2IntLittle(new byte[]{payload[7], 0, 0, 0}, false);
                                final int s2 = BinaryUtils.bytes2IntLittle(new byte[]{payload[8], 0, 0, 0}, false);

                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_PRICE_TRACK_RESERVE_MODE);
                                Cmd.putResultValue(resultMap, BmtDataKey.SMART, smart);
                                Cmd.putResultValue(resultMap, BmtDataKey.EMERGENCY, emergency);
                                Cmd.putResultValue(resultMap, BmtDataKey.LOW_POWER_ALERT, lowPowerAlert);
                                Cmd.putResultValue(resultMap, BmtDataKey.BATTERY_PROTECT, batteryProtect);
                                Cmd.putResultValue(resultMap, BmtDataKey.C_1, c1);
                                Cmd.putResultValue(resultMap, BmtDataKey.C_2, c2);
                                Cmd.putResultValue(resultMap, BmtDataKey.C_3, c3);
                                Cmd.putResultValue(resultMap, BmtDataKey.S_1, s1);
                                Cmd.putResultValue(resultMap, BmtDataKey.S_2, s2);

                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_PRICE_TRACK_RESERVE_MODE);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_PRICE_TRACK_RESERVE_MODE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_PRICE_TRACK_RESERVE_MODE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getCurrentReserveMode(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_CURRENT_RESERVE_MODE)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse msctResponse = (MsctResponse) iMsg;
                                if (!msctResponse.isSuccess()) {
                                    notifyError(BmtCmd.GET_CURRENT_RESERVE_MODE, msctResponse.getErrorMsg());
                                    return;
                                }
                                printResponse(msctResponse);
                                byte[] payload = (byte[]) msctResponse.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 2) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CURRENT_RESERVE_MODE);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_CURRENT_RESERVE_MODE);
                                int mode = payload[0] == 0 ? 2 : payload[0];
                                Cmd.putResultValue(resultMap, BmtDataKey.RESERVE_MODE, McuDataHelper.parseReserveMode(mode));
                                Cmd.putResultValue(resultMap, BmtDataKey.IS_PRICE_TRACKING_SUPPORTED, payload[1] == 0x01);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CURRENT_RESERVE_MODE);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }

                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CURRENT_RESERVE_MODE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CURRENT_RESERVE_MODE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory updateChips(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.UPDATE_CHIPS)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.UPDATE_CHIPS, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 2) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.UPDATE_CHIPS);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                final int errorStatus = BinaryUtils.bytes2IntLittle(new byte[]{payload[0], 0, 0, 0});
                                final int updateStatus = BinaryUtils.bytes2IntLittle(new byte[]{payload[1], 0, 0, 0});
                                device.setChipsStatus(updateStatus);
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.UPDATE_CHIPS);
                                Cmd.putResultValue(resultMap, BmtDataKey.ERROR, McuDataHelper.parseChipsUpdateErrorState(errorStatus));
                                Cmd.putResultValue(resultMap, BmtDataKey.STATUS, McuDataHelper.parseChipsUpdateState(updateStatus));
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getTimeoutResultMap(BmtCmd.UPDATE_CHIPS);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getOfflineFailResultMap(BmtCmd.UPDATE_CHIPS);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getTimeoutResultMap(BmtCmd.UPDATE_CHIPS);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getChipsUpdateProgress(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_CHIPS_UPDATE_PROGRESS)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_CHIPS_UPDATE_PROGRESS, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 2) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CHIPS_UPDATE_PROGRESS);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                final int progress = BinaryUtils.bytes2IntLittle(new byte[]{payload[0], 0, 0, 0});
                                final int updateStatus = BinaryUtils.bytes2IntLittle(new byte[]{payload[1], 0, 0, 0});
                                device.setChipsStatus(updateStatus);
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_CHIPS_UPDATE_PROGRESS);
                                Cmd.putResultValue(resultMap, BmtDataKey.PROGRESS, progress);
                                Cmd.putResultValue(resultMap, BmtDataKey.FAILED, 255 == progress);
                                Cmd.putResultValue(resultMap, BmtDataKey.STATUS, McuDataHelper.parseChipsUpdateState(updateStatus));
                                if (payload.length >= 4) {
                                    int flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[2], payload[3], 0, 0});
                                    List<Integer> error = McuDataHelper.parseUpgradeError(flag);
                                    Cmd.putResultValue(resultMap, BmtDataKey.ERROR, error);
                                }
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getTimeoutResultMap(BmtCmd.GET_CHIPS_UPDATE_PROGRESS);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getOfflineFailResultMap(BmtCmd.GET_CHIPS_UPDATE_PROGRESS);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getTimeoutResultMap(BmtCmd.GET_CHIPS_UPDATE_PROGRESS);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }


    private MsctDataFactory getChipsStatus(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_CHIPS_STATUS)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_CHIPS_STATUS, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 1) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CHIPS_STATUS);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                final int updateStatus = BinaryUtils.bytes2IntLittle(new byte[]{payload[0], 0, 0, 0});
                                device.setChipsStatus(updateStatus);
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_CHIPS_STATUS);
                                Cmd.putResultValue(resultMap, BmtDataKey.STATUS, McuDataHelper.parseChipsUpdateState(updateStatus));
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getTimeoutResultMap(BmtCmd.GET_CHIPS_STATUS);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getOfflineFailResultMap(BmtCmd.GET_CHIPS_STATUS);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getTimeoutResultMap(BmtCmd.GET_CHIPS_STATUS);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getMode(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_MODE)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_MODE, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 4) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_MODE);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_MODE);
                                Cmd.putResultValue(resultMap, BmtDataKey.MODE, McuDataHelper.parseChargeMode(payload[3]));
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_MODE);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_MODE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_MODE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory rebootInverter(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }

        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.REBOOT_INVERTER)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                Map<String, Object> resultMap;
                                if (!response.isSuccess()) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.REBOOT_INVERTER);
                                    Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }

                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.REBOOT_INVERTER);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.REBOOT_INVERTER);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.REBOOT_INVERTER);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.REBOOT_INVERTER);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getAdvanceInfo(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_ADVANCE_INFO)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_ADVANCE_INFO, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap = Cmd.getDefaultResultMap(response.isSuccess(), BmtCmd.GET_ADVANCE_INFO);
                                if (payload == null || payload.length == 0) {
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }

                                byte wifiLength = payload[0];
                                byte ipLength = payload[1 + wifiLength];
                                byte macLength = payload[1 + wifiLength + 1 + ipLength];
                                byte versionLength = payload[1 + wifiLength + 1 + ipLength + 1 + macLength];
                                int lastVersionLength  = 1 + wifiLength + 1 + ipLength + 1 + macLength + 1 + versionLength;
                                byte hardwareVersionLength = 0;
                                byte ethernetIpLength = 0;
                                byte ethernetMacLength = 0;
                                if (payload.length > (lastVersionLength + 1)) {
                                    hardwareVersionLength = payload[lastVersionLength];
                                    ethernetIpLength = payload[lastVersionLength + 1 + hardwareVersionLength];
                                    ethernetMacLength = payload[lastVersionLength + 1 + hardwareVersionLength + 1 + ethernetIpLength];
                                }

                                String wifiName = "";
                                if (wifiLength > 0) {
                                    byte[] wifiNameArr = new byte[wifiLength];
                                    System.arraycopy(payload, 1, wifiNameArr, 0, wifiLength);
                                    wifiName = new String(wifiNameArr);
                                }

                                String ip = "";
                                if (ipLength > 0) {
                                    byte[] ipArr = new byte[ipLength];
                                    System.arraycopy(payload, 1 + wifiLength + 1, ipArr, 0, ipLength);
                                    ip = new String(ipArr);
                                }

                                String mac = "";
                                if (macLength > 0) {
                                    byte[] macArr = new byte[macLength];
                                    System.arraycopy(payload, 1 + wifiLength + 1 + ipLength + 1, macArr, 0, macLength);
                                    mac = new String(macArr);
                                }

                                String version = "";
                                if (versionLength > 0) {
                                    byte[] versionArr = new byte[versionLength];
                                    System.arraycopy(payload, 1 + wifiLength + 1 + ipLength + 1 + macLength + 1, versionArr, 0, versionLength);
                                    version = new String(versionArr);
                                    device.setIotVersion(version);
                                }

                                String hardwareVersion = "";
                                if (hardwareVersionLength > 0) {
                                    byte[] hvArr = new byte[hardwareVersionLength];
                                    System.arraycopy(payload, 1 + wifiLength + 1 + ipLength + 1 + macLength + 1 + versionLength + 1, hvArr, 0, hardwareVersionLength);
                                    hardwareVersion = new String(hvArr);
                                }

                                String ethernetIp = "";
                                if (ethernetIpLength > 0) {
                                    byte[] ethernetIpArr = new byte[ethernetIpLength];
                                    System.arraycopy(payload, 1 + wifiLength + 1 + ipLength + 1 + macLength + 1 + versionLength + 1 + hardwareVersionLength + 1, ethernetIpArr, 0, ethernetIpLength);
                                    ethernetIp = new String(ethernetIpArr);
                                }

                                String ethernetMac = "";
                                if (ethernetMacLength > 0) {
                                    byte[] ethernetMacArr = new byte[ethernetMacLength];
                                    System.arraycopy(payload, 1 + wifiLength + 1 + ipLength + 1 + macLength + 1 + versionLength + 1 + hardwareVersionLength + 1 + ethernetIpLength + 1, ethernetMacArr, 0, ethernetMacLength);
                                    ethernetMac = new String(ethernetMacArr);
                                }


                                Cmd.putResultValue(resultMap, BmtDataKey.WIFI_NAME, wifiName);
                                Cmd.putResultValue(resultMap, BmtDataKey.IP, ip);
                                Cmd.putResultValue(resultMap, BmtDataKey.MAC, mac);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERSION, version);
                                Cmd.putResultValue(resultMap, BmtDataKey.HARDWARE_VERSION, hardwareVersion);
                                Cmd.putResultValue(resultMap, BmtDataKey.ETHERNET_IP, ethernetIp);
                                Cmd.putResultValue(resultMap, BmtDataKey.ETHERNET_MAC, ethernetMac);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_ADVANCE_INFO);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_ADVANCE_INFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_ADVANCE_INFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory resetDeviceData(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }

        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.RESET_DEVICE_DATA)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                Map<String, Object> resultMap;
                                if (!response.isSuccess()) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.RESET_DEVICE_DATA);
                                    Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.RESET_DEVICE_DATA);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.RESET_DEVICE_DATA);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.RESET_DEVICE_DATA);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.RESET_DEVICE_DATA);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory reset(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }

        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.RESET)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                Map<String, Object> resultMap;
                                if (!response.isSuccess()) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.RESET);
                                    Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                device.onReceiveResetAck();
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.RESET);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.RESET);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.RESET);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.RESET);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getCommunicationSignal(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_COMMUNICATE_SIGNAL)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_COMMUNICATE_SIGNAL, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 5) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_COMMUNICATE_SIGNAL);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_COMMUNICATE_SIGNAL);
                                final int wifiSignal = BinaryUtils.bytes2IntLittle(new byte[]{payload[0], 0, 0, 0});
                                final int cellularSignal = BinaryUtils.bytes2IntLittle(new byte[]{payload[1], 0, 0, 0});
                                final int wifiRSSI = BinaryUtils.bytes2IntLittle(new byte[]{payload[2], 0, 0, 0});
                                final int cellularRSSI = BinaryUtils.bytes2IntLittle(new byte[]{payload[3], 0, 0, 0});
                                final int ethernet = BinaryUtils.bytes2IntLittle(new byte[]{payload[4], 0, 0, 0});
                                Cmd.putResultValue(resultMap, BmtDataKey.WIFI, McuDataHelper.parseWifiSignal(wifiSignal));
                                Cmd.putResultValue(resultMap, BmtDataKey.CELLULAR, McuDataHelper.parseWifiSignal(cellularSignal));
                                Cmd.putResultValue(resultMap, BmtDataKey.WIF_RSSI, wifiRSSI);
                                Cmd.putResultValue(resultMap, BmtDataKey.CELLULAR_RSSI, cellularRSSI);
                                Cmd.putResultValue(resultMap, BmtDataKey.ETHERNET, McuDataHelper.parseEthernetSignal(ethernet));
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_COMMUNICATE_SIGNAL);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_COMMUNICATE_SIGNAL);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_COMMUNICATE_SIGNAL);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getVirtualPowerPlant(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_VIRTUAL_POWER_PLANT)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_VIRTUAL_POWER_PLANT, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 1) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_VIRTUAL_POWER_PLANT);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_VIRTUAL_POWER_PLANT);
                                Cmd.putResultValue(resultMap, BmtDataKey.ON, payload[0] == 0x01);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_VIRTUAL_POWER_PLANT);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_VIRTUAL_POWER_PLANT);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_VIRTUAL_POWER_PLANT);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory setVirtualPowerPlant(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        final boolean on = DeviceHelper.getBoolean(arg, BmtDataKey.ON, false);
        final byte[] params;
        if (UserManager.getInstance().getUser() != null &&
                !TextUtils.isEmpty(UserManager.getInstance().getUser().getUser_id())) {
            String userId = UserManager.getInstance().getUser().getUser_id();
            final byte[] userIdPayloads = getStrParamByteArr(userId);
            final int userIdPayloadsLen = userIdPayloads.length;
            params = new byte[1 + userIdPayloadsLen];
            params[0] = on ? (byte) 0x01 : (byte) 0x00;
            System.arraycopy(userIdPayloads, 0, params, 1, userIdPayloadsLen);
        } else {
            params = new byte[1];
            params[0] = on ? (byte) 0x01 : (byte) 0x00;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.SET_VIRTUAL_POWER_PLANT)
                .setIsNeedResult(false)
                .setPayload(params)
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                Map<String, Object> resultMap;
                                if (!response.isSuccess()) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_VIRTUAL_POWER_PLANT);
                                    Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.SET_VIRTUAL_POWER_PLANT);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_VIRTUAL_POWER_PLANT);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_VIRTUAL_POWER_PLANT);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_VIRTUAL_POWER_PLANT);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getChargeStrategies(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_CHARGE_STRATEGIES)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_CHARGE_STRATEGIES, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 7) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CHARGE_STRATEGIES);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_CHARGE_STRATEGIES);
                                boolean ignoreLowPriceCharge = payload[3] == 0x00;
                                int goodPricePercentage = BinaryUtils.bytes2IntLittle(new byte[]{payload[4], payload[5], 0, 0});
                                boolean ignorePriceCharge = payload[6] == 0x00;
                                int acceptablePricePercentage = BinaryUtils.bytes2IntLittle(new byte[]{payload[7], payload[8], 0, 0});
                                if (ignoreLowPriceCharge) {
                                    goodPricePercentage = -1;
                                }
                                if (ignorePriceCharge) {
                                    acceptablePricePercentage = -1;
                                }
                                Cmd.putResultValue(resultMap, BmtDataKey.STRATEGY_TYPE, Mcu.Strategy.StrategyType.findByValue(payload[0]).getCode());
                                Cmd.putResultValue(resultMap, BmtDataKey.SMARTRESERVE, Utils.byte2Int(payload[1]));
                                Cmd.putResultValue(resultMap, BmtDataKey.EMERGENCYRESERVE, Utils.byte2Int(payload[2]));
                                Cmd.putResultValue(resultMap, BmtDataKey.GOOD_PRICE_PERCENTAGE, goodPricePercentage);
                                Cmd.putResultValue(resultMap, BmtDataKey.ACCEPTABLE_PRICE_PERCENTAGE, acceptablePricePercentage);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CHARGE_STRATEGIES);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CHARGE_STRATEGIES);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CHARGE_STRATEGIES);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory setChargeStrategies(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        int strategyType = (int) MapUtils.get(arg, BmtDataKey.STRATEGY_TYPE, 1);
        int smartReserve = (int) MapUtils.get(arg, BmtDataKey.SMARTRESERVE, 0);
        int goodPricePercentage = (int) MapUtils.get(arg, BmtDataKey.GOOD_PRICE_PERCENTAGE, -1);
        int emergencyReserve = (int) MapUtils.get(arg, BmtDataKey.EMERGENCYRESERVE, 0);
        int acceptablePricePercentage = (int) MapUtils.get(arg, BmtDataKey.ACCEPTABLE_PRICE_PERCENTAGE, -1);

        byte[] params = new byte[9];
        final Mcu.Strategy.StrategyType type = Mcu.Strategy.StrategyType.valueOf(strategyType);
        if (type == null) {
            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_CHARGE_STRATEGIES);
            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, "Unsupport " + BmtDataKey.STRATEGY_TYPE + ": " + strategyType);
            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
            return null;
        }

        params[0] = (byte) (type.getValue());
        params[1] = (byte) smartReserve;
        params[2] = (byte) emergencyReserve;
        params[3] = (byte) ((goodPricePercentage < 0 || goodPricePercentage > 1000) ? 0x00 : 0x01);
        System.arraycopy(BinaryUtils.intToByteLittle(goodPricePercentage), 0, params, 4, 2);
        params[6] = (byte) ((acceptablePricePercentage < 0 || acceptablePricePercentage > 1000) ? 0x00 : 0x01);
        System.arraycopy(BinaryUtils.intToByteLittle(acceptablePricePercentage), 0, params, 7, 2);

        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.SET_CHARGE_STRATEGIES)
                .setIsNeedResult(false)
                .setPayload(params)
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
//                            byte[] payload = Utils.intArrayToByteArray((int[]) ((MsctResponse) iMsg).getMsctContext().getDecodedPayload());
                                Map<String, Object> resultMap;
                                if (!response.isSuccess()) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_CHARGE_STRATEGIES);
                                    Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.SET_CHARGE_STRATEGIES);

                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_CHARGE_STRATEGIES);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_CHARGE_STRATEGIES);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_CHARGE_STRATEGIES);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getEmergencyCharge(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_EMERGENCY_CHARGE)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_EMERGENCY_CHARGE, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 9) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_EMERGENCY_CHARGE);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_EMERGENCY_CHARGE);

                                Cmd.putResultValue(resultMap, BmtDataKey.ON, payload[0] == 0x01);
                                Cmd.putResultValue(resultMap, "startTime", ((long) BinaryUtils.bytes2IntLittle(new byte[]{payload[1], payload[2], payload[3], payload[4]})));
                                Cmd.putResultValue(resultMap, "endTime", ((long) BinaryUtils.bytes2IntLittle(new byte[]{payload[5], payload[6], payload[7], payload[8]})));
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_EMERGENCY_CHARGE);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_EMERGENCY_CHARGE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_EMERGENCY_CHARGE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory setEmergencyCharge(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        int startTime = ((int) ((long) MapUtils.get(arg, "startTime", 0)));
        int endTime = ((int) ((long) MapUtils.get(arg, "endTime", 0)));
        boolean on = (boolean) MapUtils.get(arg, BmtDataKey.ON, false);
        byte[] params = new byte[9];
        params[0] = (byte) (on ? 0x01 : 0x00);
        System.arraycopy(BinaryUtils.intToByteLittle(startTime), 0, params, 1, 4);
        System.arraycopy(BinaryUtils.intToByteLittle(endTime), 0, params, 5, 4);
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.SET_EMERGENCY_CHARGE)
                .setIsNeedResult(false)
                .setPayload(params)
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                printResponse(response);
//                            byte[] payload = Utils.intArrayToByteArray((int[]) ((MsctResponse) iMsg).getMsctContext().getDecodedPayload());
                                Map<String, Object> resultMap;
                                if (!response.isSuccess()) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_EMERGENCY_CHARGE);
                                    Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.SET_EMERGENCY_CHARGE);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_EMERGENCY_CHARGE);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_EMERGENCY_CHARGE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_EMERGENCY_CHARGE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getCabinetAllInfo(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_CABINET_ALLINFO)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_CABINET_ALLINFO, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 1) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CABINET_ALLINFO);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_CABINET_ALLINFO);
                                Cmd.putResultValue(resultMap, BmtDataKey.COUNT, Utils.byte2Int(payload[0]));
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CABINET_ALLINFO);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CABINET_ALLINFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CABINET_ALLINFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getEvState(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_EV_STATE)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctContext msctContext = ((MsctResponse) iMsg).getMsctContext();
                                short status = BinaryUtils.byteToShortLittle(Utils.unSignByteToByte(msctContext.ShouldGetOptionHeader(Exoption.OPTION_STATUS)));
                                if (status != 0) {
                                    notifyError(BmtCmd.GET_EV_STATE, ((MsctResponse) iMsg).getErrorMsg());
                                    return;
                                }
                                printResponse((MsctResponse) iMsg);
                                byte[] payload = (byte[]) ((MsctResponse) iMsg).getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 2) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_EV_STATE);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_EV_STATE);
                                Cmd.putResultValue(resultMap, BmtDataKey.STATE, McuDataHelper.parseEVState(payload[0]));
                                Cmd.putResultValue(resultMap, BmtDataKey.LIGHT_STATE, McuDataHelper.parseEVLightState(payload[1]));
                                Cmd.putResultValue(resultMap, BmtDataKey.EXCEPTIONS, McuDataHelper.parseEVException(BinaryUtils.bytes2IntLittle(new byte[]{payload[2], payload[3], 0, 0})));
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_EV_STATE);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_EV_STATE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_EV_STATE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getMPPTState(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_MPPT_STATE)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_MPPT_STATE, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 7) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_MPPT_STATE);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_MPPT_STATE);
                                Cmd.putResultValue(resultMap, BmtDataKey.VALUE_0_ON, BinaryUtils.getBitValue(payload[0], 0) == Mcu.MPPT.STATE_OPEN);
                                Cmd.putResultValue(resultMap, BmtDataKey.VALUE_1_ON, BinaryUtils.getBitValue(payload[0], 1) == Mcu.MPPT.STATE_OPEN);
                                Cmd.putResultValue(resultMap, BmtDataKey.VALUE_2_ON, BinaryUtils.getBitValue(payload[0], 2) == Mcu.MPPT.STATE_OPEN);
                                Cmd.putResultValue(resultMap, BmtDataKey.VALUE_0, McuDataHelper.parseMPPTException(BinaryUtils.bytes2IntLittle(new byte[]{payload[1], payload[2], 0, 0})));
                                Cmd.putResultValue(resultMap, BmtDataKey.VALUE_1, McuDataHelper.parseMPPTException(BinaryUtils.bytes2IntLittle(new byte[]{payload[3], payload[4], 0, 0})));
                                Cmd.putResultValue(resultMap, BmtDataKey.VALUE_2, McuDataHelper.parseMPPTException(BinaryUtils.bytes2IntLittle(new byte[]{payload[5], payload[6], 0, 0})));
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_MPPT_STATE);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_MPPT_STATE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_MPPT_STATE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getCabinetState(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        int index = (int) MapUtils.get(arg, BmtDataKey.INDEX, 0);
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_CABINET_STATE)
                .setIsNeedResult(false)
                .setPayload(new byte[]{(byte) index})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctContext msctContext = ((MsctResponse) iMsg).getMsctContext();
                                short status = BinaryUtils.byteToShortLittle(Utils.unSignByteToByte(msctContext.ShouldGetOptionHeader(Exoption.OPTION_STATUS)));
                                if (status != 0) {
                                    notifyError(BmtCmd.GET_CABINET_STATE, ((MsctResponse) iMsg).getErrorMsg());
                                    return;
                                }
                                printResponse((MsctResponse) iMsg);
                                byte[] payload = (byte[]) ((MsctResponse) iMsg).getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 5) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CABINET_STATE);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_CABINET_STATE);
                                Cmd.putResultValue(resultMap, BmtDataKey.WATER_STATE, McuDataHelper.parseCabinetWaterState(payload[0]));
                                Cmd.putResultValue(resultMap, BmtDataKey.SMOKE_STATE, McuDataHelper.parseCabinetSmokeState(payload[1]));
                                Cmd.putResultValue(resultMap, BmtDataKey.FAN_STATE, McuDataHelper.parseCabinetFanState(payload[2]));
                                final byte exception = payload[3];
                                List<Integer> exceptions = new ArrayList<>();
                                if (BinaryUtils.getBitValue(exception, 0) == 1) {
                                    exceptions.add(Mcu.Cabinet.CabinetException.Communication.getCode());
                                }
                                Cmd.putResultValue(resultMap, BmtDataKey.EXCEPTIONS, exceptions);
                                int offset = 4;
                                short versionLength = BinaryUtils.byteToShortLittle(new byte[]{payload[offset], 0});
                                String versionInfo = "";
                                offset += 1;
                                if (versionLength > 0) {
                                    byte[] versionInfoArray = new byte[versionLength];
                                    System.arraycopy(payload, offset, versionInfoArray, 0, versionLength);
                                    versionInfo = HexUtil.byte2hex(versionInfoArray);
                                    offset += versionLength;
                                }
                                Cmd.putResultValue(resultMap, BmtDataKey.VERSION, versionInfo);
                                Cmd.putResultValue(resultMap, BmtDataKey.INDEX, Utils.byte2Int(payload[offset]));
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CABINET_STATE);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CABINET_STATE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_CABINET_STATE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getMcuInfo(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_MCU_INFO)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_MCU_INFO, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length == 0) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_MCU_INFO);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                byte idInfoLength = payload[0];
                                byte versionInfoLength = payload[idInfoLength + 1];
                                byte barcodeLength = payload[idInfoLength + 2 + versionInfoLength];

                                String idInfo = "";
                                if (idInfoLength > 0) {
                                    byte[] idinfoArray = new byte[idInfoLength];
                                    System.arraycopy(payload, 1, idinfoArray, 0, idInfoLength);
                                    idInfo = new String(idinfoArray);
                                }

                                String version = "";
                                if (versionInfoLength > 0) {
                                    byte[] versioninfoArray = new byte[versionInfoLength];
                                    System.arraycopy(payload, idInfoLength + 2, versioninfoArray, 0, versionInfoLength);
                                    version = HexUtil.byte2hex(versioninfoArray);
                                }

                                String barcode = "";
                                if (barcodeLength > 0) {
                                    byte[] barcodeArray = new byte[barcodeLength];
                                    System.arraycopy(payload, idInfoLength + 3 + versionInfoLength, barcodeArray, 0, barcodeLength);
                                    barcode = new String(barcodeArray);
                                }

                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_MCU_INFO);
                                Cmd.putResultValue(resultMap, BmtDataKey.ID_INFO, idInfo);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERSION, version);
                                Cmd.putResultValue(resultMap, BmtDataKey.BARCODE, barcode);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_MCU_INFO);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_MCU_INFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_MCU_INFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getGlobalLoadState(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_GLOBALLOAD_STATE)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_GLOBALLOAD_STATE, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 1) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_GLOBALLOAD_STATE);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_GLOBALLOAD_STATE);
                                Cmd.putResultValue(resultMap, BmtDataKey.ON, BinaryUtils.getBitValue(payload[0], 1) == 1);
                                Cmd.putResultValue(resultMap, BmtDataKey.EV_ON, BinaryUtils.getBitValue(payload[0], 0) == 1);
                                Cmd.putResultValue(resultMap, BmtDataKey.OTHER_ON, BinaryUtils.getBitValue(payload[0], 2) == 1);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_GLOBALLOAD_STATE);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_GLOBALLOAD_STATE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_GLOBALLOAD_STATE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getGlobalExceptions(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_GLOBAL_EXCEPTIONS)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_GLOBAL_EXCEPTIONS, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) ((MsctResponse) iMsg).getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;
                                if (payload == null || payload.length < 14) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_GLOBAL_EXCEPTIONS);
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_GLOBAL_EXCEPTIONS);

                                int flag;
                                flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[0], payload[1], 0, 0});
                                List<Integer> vertBatteryExceptions = McuDataHelper.parseInverterBatteryException(flag);
                                flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[2], payload[3], 0, 0});
                                List<Integer> vertExceptions = McuDataHelper.parseInverterException(flag);
                                flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[4], payload[5], 0, 0});
                                List<Integer> vertGridExceptions = McuDataHelper.parseInverterGridException(flag);
                                flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[6], payload[7], 0, 0});
                                List<Integer> vertSystemExceptions = McuDataHelper.parseInverterSystemException(flag);
                                flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[8], payload[9], 0, 0});
                                List<Integer> vertmpptExceptions = McuDataHelper.parseInverterMPPTException(flag);
                                flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[10], payload[11], 0, 0});
                                List<Integer> vertPresentExceptions = McuDataHelper.parseInverterPresentException(flag);
                                flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[12], payload[13], 0, 0});
                                List<Integer> vertDCExceptions = McuDataHelper.parseInverterDCException(flag);
                                List evException = McuDataHelper.parseEVException(BinaryUtils.bytes2IntLittle(new byte[]{payload[14], payload[15], 0, 0}));
                                List mpptException = McuDataHelper.parseMPPTException(BinaryUtils.bytes2IntLittle(new byte[]{payload[16], payload[17], 0, 0}));
                                List cabinetException = McuDataHelper.parseCabinetException(BinaryUtils.bytes2IntLittle(new byte[]{payload[18], payload[19], 0, 0}));
                                List batteryException = McuDataHelper.parseBatteryException(BinaryUtils.bytes2IntLittle(new byte[]{payload[20], payload[21], 0, 0}) >> 2);
                                List systemException = McuDataHelper.parseSystemException(BinaryUtils.bytes2IntLittle(new byte[]{payload[22], payload[23], 0, 0}));
                                List communicationException = McuDataHelper.parseCommunicationException(BinaryUtils.bytes2IntLittle(new byte[]{payload[24], payload[25], 0, 0}));

                                Cmd.putResultValue(resultMap, BmtDataKey.VERT_BATTERY_EXCEPTIONS, vertBatteryExceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERT_EXCEPTIONS, vertExceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERT_GRID_EXCEPTIONS, vertGridExceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERT_SYSTEM_EXCEPTIONS, vertSystemExceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERT_MPPT_EXCEPTIONS, vertmpptExceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERT_PRESENT_EXCEPTIONS, vertPresentExceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERT_DC_EXCEPTIONS, vertDCExceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.EV, evException);
                                Cmd.putResultValue(resultMap, BmtDataKey.MPPT, mpptException);
                                Cmd.putResultValue(resultMap, BmtDataKey.CABINET, cabinetException);
                                Cmd.putResultValue(resultMap, BmtDataKey.BATTERY, batteryException);
                                Cmd.putResultValue(resultMap, BmtDataKey.SYSTEM, systemException);
                                Cmd.putResultValue(resultMap, BmtDataKey.COMMUNICATION, communicationException);
                                if (response.getStatus() == 0) {
                                    device.setExceptions(vertBatteryExceptions, vertExceptions, vertGridExceptions, vertSystemExceptions,
                                            vertmpptExceptions, vertPresentExceptions, vertDCExceptions,
                                            evException, mpptException, cabinetException, batteryException, systemException, communicationException);
                                }
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_GLOBAL_EXCEPTIONS);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_GLOBAL_EXCEPTIONS);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_GLOBAL_EXCEPTIONS);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getBatteryAccessoryState(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        int index = (int) MapUtils.get(arg, BmtDataKey.INDEX, 0);
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_BATTERY_ACCESSORYSTATE)
                .setIsNeedResult(false)
                .setPayload(new byte[]{(byte) index})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_BATTERY_ACCESSORYSTATE, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) ((MsctResponse) iMsg).getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap = Cmd.getDefaultResultMap(response.isSuccess(), BmtCmd.GET_BATTERY_ACCESSORYSTATE);
                                if (payload == null || payload.length < 2) {
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                int index = payload[1];
                                int state = payload[0];
                                boolean heating = false;
                                boolean heatAvailable = false;
                                if (Mcu.Battery.STATE_HEATING_FILM_HEATING_OPEN == state) {
                                    heatAvailable = true;
                                    heating = true;
                                } else if (Mcu.Battery.STATE_HEATING_FILM_HEATING_CLOSE == state) {
                                    heatAvailable = true;
                                    heating = false;
                                } else {
                                    heatAvailable = false;
                                    heating = false;
                                }
                                Cmd.putResultValue(resultMap, BmtDataKey.INDEX, index);
                                Cmd.putResultValue(resultMap, BmtDataKey.HEATING, heating);
                                Cmd.putResultValue(resultMap, BmtDataKey.HEAT_AVAILABLE, heatAvailable);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_BATTERY_ACCESSORYSTATE);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_BATTERY_ACCESSORYSTATE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_BATTERY_ACCESSORYSTATE);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getBatteryInfo(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        int index = (int) MapUtils.get(arg, BmtDataKey.INDEX, 0);
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_BATTERY_INFO)
                .setIsNeedResult(false)
                .setPayload(new byte[]{(byte) index})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_BATTERY_INFO, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap = Cmd.getDefaultResultMap(response.isSuccess(), BmtCmd.GET_BATTERY_INFO);
                                if (payload == null || payload.length == 0) {
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                int rawValue = (BinaryUtils.bytes2IntLittle(new byte[]{payload[0], payload[1], 0, 0}));
                                Cmd.putResultValue(resultMap, BmtDataKey.DISCHARGE_SWITCH_ON, BinaryUtils.getBitValue(rawValue, 0) == 1);
                                Cmd.putResultValue(resultMap, BmtDataKey.CHARGE_SWITCH_ON, BinaryUtils.getBitValue(rawValue, 1) == 1);
                                List exceptions = McuDataHelper.parseBatteryException(rawValue >> 2);
                                Cmd.putResultValue(resultMap, BmtDataKey.EXCEPTIONS, exceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.BMS_TEMP, McuDataHelper.convertTempK(BinaryUtils.bytes2IntLittle(new byte[]{payload[2], payload[3], 0, 0})));
                                Cmd.putResultValue(resultMap, BmtDataKey.ELECTRODE_A_TEMP, McuDataHelper.convertTempK(BinaryUtils.bytes2IntLittle(new byte[]{payload[4], payload[5], 0, 0})));
                                Cmd.putResultValue(resultMap, BmtDataKey.ELECTRODE_B_TEMP, McuDataHelper.convertTempK(BinaryUtils.bytes2IntLittle(new byte[]{payload[6], payload[7], 0, 0})));
                                Cmd.putResultValue(resultMap, BmtDataKey.VOLTAGE, (BinaryUtils.bytes2IntLittle(new byte[]{payload[8], payload[9], 0, 0})));
                                Cmd.putResultValue(resultMap, BmtDataKey.AMPERE, BinaryUtils.bytes2IntLittle(new byte[]{payload[10], payload[11], payload[12], payload[13]}));
                                Cmd.putResultValue(resultMap, BmtDataKey.SOC, (BinaryUtils.bytes2IntLittle(new byte[]{payload[14], payload[15], 0, 0})));
                                Cmd.putResultValue(resultMap, BmtDataKey.CUR_POWER, (BinaryUtils.bytes2IntLittle(new byte[]{payload[16], payload[17], 0, 0})) / 100.0d);
                                Cmd.putResultValue(resultMap, BmtDataKey.FULL_POWER, (BinaryUtils.bytes2IntLittle(new byte[]{payload[18], payload[19], 0, 0})) / 100.0d);
                                Cmd.putResultValue(resultMap, BmtDataKey.RELEASE_TIMES, BinaryUtils.bytes2IntLittle(new byte[]{payload[20], payload[21], 0, 0}));
                                Cmd.putResultValue(resultMap, BmtDataKey.HEALTHY, BinaryUtils.bytes2IntLittle(new byte[]{payload[22], payload[23], 0, 0}));

                                byte idInfoLength = payload[24];
                                byte versionInfoLength = payload[idInfoLength + 25];
                                byte barcodeLength = payload[idInfoLength + 26 + versionInfoLength];

                                String idInfo = "";
                                if (idInfoLength > 0) {
                                    byte[] idinfoArray = new byte[idInfoLength];
                                    System.arraycopy(payload, 25, idinfoArray, 0, idInfoLength);
                                    idInfo = new String(idinfoArray);
                                }

                                String version = "";
                                if (versionInfoLength > 0) {
                                    byte[] versioninfoArray = new byte[versionInfoLength];
                                    System.arraycopy(payload, idInfoLength + 26, versioninfoArray, 0, versionInfoLength);
                                    version = HexUtil.byte2hex(versioninfoArray);
                                }

                                String barcode = "";
                                if (barcodeLength > 0) {
                                    byte[] barcodeArray = new byte[barcodeLength];
                                    System.arraycopy(payload, idInfoLength + 27 + versionInfoLength, barcodeArray, 0, barcodeLength);
                                    barcode = new String(barcodeArray);
                                }

                                int xLength = idInfoLength + versionInfoLength + barcodeLength;
                                int cabinetIndex = payload[27 + xLength];
                                int cabinetPositionIndex = payload[28 + xLength];
                                int batteryIndex = payload[29 + xLength];
                                int capacity = BinaryUtils.bytes2IntLittle(new byte[]{payload[30 + xLength], payload[31 + xLength], 0, 0});

                                Cmd.putResultValue(resultMap, BmtDataKey.ID_INFO, idInfo);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERSION, version);
                                Cmd.putResultValue(resultMap, BmtDataKey.BARCODE, barcode);
                                Cmd.putResultValue(resultMap, BmtDataKey.INDEX, batteryIndex);
                                Cmd.putResultValue(resultMap, BmtDataKey.CABINET_INDEX, cabinetIndex);
                                Cmd.putResultValue(resultMap, BmtDataKey.CABINET_POSITION_INDEX, cabinetPositionIndex);
                                Cmd.putResultValue(resultMap, BmtDataKey.CAPACITY, capacity);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_BATTERY_INFO);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_BATTERY_INFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_BATTERY_INFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory setBatteryAllOff(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.SET_BATTERY_ALLOFF)
                .setIsNeedResult(false)
                .setPayload(new byte[]{Mcu.Battery.CONTROL_BATTERY_OFF})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = (MsctResponse) iMsg;
                                if (response.isSuccess()) {
                                    printResponse(response);
                                }
                                Map resultMap = Cmd.getDefaultResultMap(response.isSuccess(), BmtCmd.SET_BATTERY_ALLOFF);
                                resultMap.put(BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_BATTERY_ALLOFF);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_BATTERY_ALLOFF);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_BATTERY_ALLOFF);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getInverterOutputInfo(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_INVERTER_OUTPUT_INFO)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = ((MsctResponse) iMsg);
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_INVERTER_OUTPUT_INFO, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;

                                if (payload == null || payload.length < 24) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_INVERTER_OUTPUT_INFO);
                                    Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }

                                int flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[0], payload[1], 0, 0});
                                boolean otherOutOn1 = BinaryUtils.getBitValue(flag, 0) == 1;
                                boolean otherOutOn2 = BinaryUtils.getBitValue(flag, 1) == 1;
                                boolean otherOutOn3 = BinaryUtils.getBitValue(flag, 2) == 1;

                                boolean evOn = BinaryUtils.getBitValue(flag, 3) == 1;

                                boolean inverterInOn1 = BinaryUtils.getBitValue(flag, 4) == 1;
                                boolean inverterInOn2 = BinaryUtils.getBitValue(flag, 5) == 1;
                                boolean inverterInOn3 = BinaryUtils.getBitValue(flag, 6) == 1;

                                boolean batteryOn1 = BinaryUtils.getBitValue(flag, 7) == 1;
                                boolean batteryOn2 = BinaryUtils.getBitValue(flag, 8) == 1;
                                boolean batteryOn3 = BinaryUtils.getBitValue(flag, 9) == 1;

                                boolean BSensorOutputOn = BinaryUtils.getBitValue(flag, 10) == 1;

                                int otherOut1 = BinaryUtils.bytes2IntLittle(new byte[]{payload[2], payload[3], 0, 0});
                                int otherOut2 = BinaryUtils.bytes2IntLittle(new byte[]{payload[4], payload[5], 0, 0});
                                int otherOut3 = BinaryUtils.bytes2IntLittle(new byte[]{payload[6], payload[7], 0, 0});

                                int ev = BinaryUtils.bytes2IntLittle(new byte[]{payload[8], payload[9], 0, 0});

                                int inverterIn1 = BinaryUtils.bytes2IntLittle(new byte[]{payload[10], payload[11], 0, 0});
                                int inverterIn2 = BinaryUtils.bytes2IntLittle(new byte[]{payload[12], payload[13], 0, 0});
                                int inverterIn3 = BinaryUtils.bytes2IntLittle(new byte[]{payload[14], payload[15], 0, 0});

                                int battery1 = BinaryUtils.bytes2IntLittle(new byte[]{payload[16], payload[17], 0, 0});
                                int battery2 = BinaryUtils.bytes2IntLittle(new byte[]{payload[18], payload[19], 0, 0});
                                int battery3 = BinaryUtils.bytes2IntLittle(new byte[]{payload[20], payload[21], 0, 0});

                                int BSensorOutput = BinaryUtils.bytes2IntLittle(new byte[]{payload[22], payload[23], 0, 0});

                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_INVERTER_OUTPUT_INFO);

                                Cmd.putResultValue(resultMap, BmtDataKey.EV_ON, evOn);
                                Cmd.putResultValue(resultMap, BmtDataKey.EV, ev);
                                Cmd.putResultValue(resultMap, BmtDataKey.B_SENSOR_OUTPUT_ON, BSensorOutputOn);
                                Cmd.putResultValue(resultMap, BmtDataKey.B_SENSOR_OUTPUT, BSensorOutput);
                                Map map = new HashMap();
                                map.put(BmtDataKey.INVERTER_IN_ON, inverterInOn1);
                                map.put(BmtDataKey.INVERTER_IN, inverterIn1);
                                map.put(BmtDataKey.OTHER_OUT_ON, otherOutOn1);
                                map.put(BmtDataKey.OTHER_OUT, otherOut1);
                                map.put(BmtDataKey.BATTERY_ON, batteryOn1);
                                map.put(BmtDataKey.BATTERY, battery1);
                                Cmd.putResultValue(resultMap, BmtDataKey.VALUE_0, map);
                                map = new HashMap();
                                map.put(BmtDataKey.INVERTER_IN_ON, inverterInOn2);
                                map.put(BmtDataKey.INVERTER_IN, inverterIn2);
                                map.put(BmtDataKey.OTHER_OUT_ON, otherOutOn2);
                                map.put(BmtDataKey.OTHER_OUT, otherOut2);
                                map.put(BmtDataKey.BATTERY_ON, batteryOn2);
                                map.put(BmtDataKey.BATTERY, battery2);
                                Cmd.putResultValue(resultMap, BmtDataKey.VALUE_1, map);
                                map = new HashMap();
                                map.put(BmtDataKey.INVERTER_IN_ON, inverterInOn3);
                                map.put(BmtDataKey.INVERTER_IN, inverterIn3);
                                map.put(BmtDataKey.OTHER_OUT_ON, otherOutOn3);
                                map.put(BmtDataKey.OTHER_OUT, otherOut3);
                                map.put(BmtDataKey.BATTERY_ON, batteryOn3);
                                map.put(BmtDataKey.BATTERY, battery3);
                                Cmd.putResultValue(resultMap, BmtDataKey.VALUE_2, map);

                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_INVERTER_OUTPUT_INFO);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_INVERTER_OUTPUT_INFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_INVERTER_OUTPUT_INFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getInverterInfo(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        int index = (int) MapUtils.get(arg, BmtDataKey.INDEX, 0);
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_INVERTER_INFO)
                .setIsNeedResult(false)
                .setPayload(new byte[]{(byte) index})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = ((MsctResponse) iMsg);
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_INVERTER_INFO, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;

                                if (payload == null || payload.length == 0) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_INVERTER_INFO);
                                    Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }
                                int state = McuDataHelper.parseInverterState(payload[0]);

                                // 解析异常数据
                                int flag;
                                flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[1], payload[2], 0, 0});
                                List<Integer> vertBatteryExceptions = McuDataHelper.parseInverterBatteryException(flag);
                                flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[3], payload[4], 0, 0});
                                List<Integer> vertExceptions = McuDataHelper.parseInverterException(flag);
                                flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[5], payload[6], 0, 0});
                                List<Integer> vertGridExceptions = McuDataHelper.parseInverterGridException(flag);
                                flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[7], payload[8], 0, 0});
                                List<Integer> vertSystemExceptions = McuDataHelper.parseInverterSystemException(flag);
                                flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[9], payload[10], 0, 0});
                                List<Integer> vertmpptExceptions = McuDataHelper.parseInverterMPPTException(flag);
                                flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[11], payload[12], 0, 0});
                                List<Integer> vertPresentExceptions = McuDataHelper.parseInverterPresentException(flag);
                                flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[13], payload[14], 0, 0});
                                List<Integer> vertDCExceptions = McuDataHelper.parseInverterDCException(flag);

                                String idInfo = "";
                                String version = "";
                                int offset = 15;
                                short idInfoLength = BinaryUtils.byteToShortLittle(new byte[]{payload[offset], 0});
                                offset += 1;
                                if (idInfoLength > 0) {
                                    byte[] idinfoArray = new byte[idInfoLength];
                                    System.arraycopy(payload, offset, idinfoArray, 0, idInfoLength);
                                    idInfo = new String(idinfoArray);
                                    offset += idInfoLength;
                                }
                                short versionInfoLength = BinaryUtils.byteToShortLittle(new byte[]{payload[offset], 0});
                                offset += 1;
                                if (versionInfoLength > 0) {
                                    byte[] versioninfoArray = new byte[versionInfoLength];
                                    System.arraycopy(payload, offset, versioninfoArray, 0, versionInfoLength);
                                    version = HexUtil.byte2hex(versioninfoArray);
                                    offset += versionInfoLength;
                                }

                                final byte inverterFanStatus = payload[offset];
                                offset += 1;
                                final int inverterIndex = BinaryUtils.bytes2IntLittle(new byte[]{payload[offset], 0, 0, 0});

                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_INVERTER_INFO);
                                Cmd.putResultValue(resultMap, BmtDataKey.ID_INFO, idInfo);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERSION, version);
                                Cmd.putResultValue(resultMap, BmtDataKey.INDEX, ((int) inverterIndex));
                                Cmd.putResultValue(resultMap, BmtDataKey.STATE, state);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERT_BATTERY_EXCEPTIONS, vertBatteryExceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERT_EXCEPTIONS, vertExceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERT_GRID_EXCEPTIONS, vertGridExceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERT_SYSTEM_EXCEPTIONS, vertSystemExceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERT_MPPT_EXCEPTIONS, vertmpptExceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERT_PRESENT_EXCEPTIONS, vertPresentExceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.VERT_DC_EXCEPTIONS, vertDCExceptions);
                                Cmd.putResultValue(resultMap, BmtDataKey.FAN_STATE, McuDataHelper.parseInverterFanState(inverterFanStatus));
                                device.setVertState(index, state);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_INVERTER_INFO);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_INVERTER_INFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_INVERTER_INFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory setInverterOpen(Map arg) {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        boolean on = (boolean) MapUtils.get(arg, BmtDataKey.ON, false);
        List<Integer> indexList = (List) MapUtils.get(arg, BmtDataKey.INDEX_S, Arrays.asList(0));
        byte index = 0;
        for (int i : indexList) {
            if (i == 0) {
                index = (byte) (index | 0x01);
            } else if (i == 1) {
                index = (byte) (index | 0x02);
            } else if (i == 2) {
                index = (byte) (index | 0x04);
            } else {
                MsctLog.e(TAG, "setInverterOpen: not support index:" + i);
            }
        }

        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.SET_INVERTER_OPEN)
                .setIsNeedResult(false)
                .setPayload(new byte[]{index, (byte) (on ? 0x01 : 0x00)})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = ((MsctResponse) iMsg);
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.SET_INVERTER_OPEN, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;

                                if (payload == null || payload.length == 0) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_INVERTER_OPEN);
                                    Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }

                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.SET_INVERTER_OPEN);
                                List indexs = new ArrayList();
                                for (int i = 0; i < 3; i++) {
                                    if (BinaryUtils.getBitValue(payload[0], i) == 1) {
                                        indexs.add(i);
                                    }
                                }
                                Cmd.putResultValue(resultMap, BmtDataKey.INDEX_S, indexs);
                                Cmd.putResultValue(resultMap, BmtDataKey.ON, on);
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_INVERTER_OPEN);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }


                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_INVERTER_OPEN);
                        Cmd.putResultValue(resultMap, BmtDataKey.INDEX_S, indexList);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.SET_INVERTER_OPEN);
                        Cmd.putResultValue(resultMap, BmtDataKey.INDEX_S, indexList);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getBatteryAllInfo() {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_BATTERY_ALLINFO)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = ((MsctResponse) iMsg);
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_BATTERY_ALLINFO, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;

                                if (payload == null || payload.length < 28) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_BATTERY_ALLINFO);
                                    Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }

                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_BATTERY_ALLINFO);
                                Cmd.putResultValue(resultMap, BmtDataKey.COUNT, ((int) payload[28]));
                                Cmd.putResultValue(resultMap, BmtDataKey.HIGH_VOLT, (BinaryUtils.bytes2IntLittle(new byte[]{payload[0], payload[1], 0, 0})));
                                Cmd.putResultValue(resultMap, BmtDataKey.LOW_VOLT, (BinaryUtils.bytes2IntLittle(new byte[]{payload[2], payload[3], 0, 0})));
                                Cmd.putResultValue(resultMap, BmtDataKey.TOTAL_VOLT, (BinaryUtils.bytes2IntLittle(new byte[]{payload[4], payload[5], 0, 0})));
                                Cmd.putResultValue(resultMap, BmtDataKey.BMS_LOW_TEMP, McuDataHelper.convertTempK(BinaryUtils.bytes2IntLittle(new byte[]{payload[6], payload[7], 0, 0})));
                                Cmd.putResultValue(resultMap, BmtDataKey.BMS_HIGH_TEMP, McuDataHelper.convertTempK(BinaryUtils.bytes2IntLittle(new byte[]{payload[8], payload[9], 0, 0})));
                                Cmd.putResultValue(resultMap, BmtDataKey.ELECTRODE_LOW_TEMP, McuDataHelper.convertTempK(BinaryUtils.bytes2IntLittle(new byte[]{payload[10], payload[11], 0, 0})));
                                Cmd.putResultValue(resultMap, BmtDataKey.ELECTRODE_HIGH_TEMP, McuDataHelper.convertTempK(BinaryUtils.bytes2IntLittle(new byte[]{payload[12], payload[13], 0, 0})));
                                Cmd.putResultValue(resultMap, BmtDataKey.SOC, (BinaryUtils.bytes2IntLittle(new byte[]{payload[14], payload[15], 0, 0})));
                                Cmd.putResultValue(resultMap, BmtDataKey.AMPERE, BinaryUtils.bytes2IntLittle(new byte[]{payload[16], payload[17], payload[18], payload[19]}));
                                Cmd.putResultValue(resultMap, BmtDataKey.CUR_POWER, (BinaryUtils.bytes2IntLittle(new byte[]{payload[20], payload[21], 0, 0})));
                                Cmd.putResultValue(resultMap, BmtDataKey.FULL_POWER, (BinaryUtils.bytes2IntLittle(new byte[]{payload[22], payload[23], 0, 0})));
                                Cmd.putResultValue(resultMap, BmtDataKey.REMAIN_TIME, (BinaryUtils.bytes2IntLittle(new byte[]{payload[24], payload[25], 0, 0})));
                                Cmd.putResultValue(resultMap, BmtDataKey.CHARGE_TIME, (BinaryUtils.bytes2IntLittle(new byte[]{payload[26], payload[27], 0, 0})));
                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_BATTERY_ALLINFO);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_BATTERY_ALLINFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_BATTERY_ALLINFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    private MsctDataFactory getInverterInputInfo() {
        MsctDataFactory.Builder msctBuilder = device.createMsctBuilder();
        if (msctBuilder == null) {
            return null;
        }
        msctBuilder.addOptionHeader(Exoption.OPTION_METHOD, McuCmd.GET_INVERTER_INPUT_INFO)
                .setIsNeedResult(false)
                .setPayload(new byte[]{})
                .setContentType(ContentType.rawByte)
                .setCallBack(new IRequestCallBack() {
                    @Override
                    public void onAckEvent(IMsg iMsg) {
                        try {
                            if (iMsg != null && iMsg instanceof MsctResponse) {
                                MsctResponse response = ((MsctResponse) iMsg);
                                if (!response.isSuccess()) {
                                    notifyError(BmtCmd.GET_INVERTER_INPUT_INFO, response.getErrorMsg());
                                    return;
                                }
                                printResponse(response);
                                byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
                                Map<String, Object> resultMap;

                                if (payload == null || payload.length < 24) {
                                    resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_INVERTER_INPUT_INFO);
                                    Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, response.getErrorMsg());
                                    device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                                    return;
                                }

                                int flag = BinaryUtils.bytes2IntLittle(new byte[]{payload[0], payload[1], 0, 0});
                                boolean citySourceOn0 = BinaryUtils.getBitValue(flag, 0) == 1;
                                boolean citySourceOn1 = BinaryUtils.getBitValue(flag, 1) == 1;
                                boolean citySourceOn2 = BinaryUtils.getBitValue(flag, 2) == 1;
                                boolean mpptOn0 = BinaryUtils.getBitValue(flag, 3) == 1;
                                boolean mpptOn1 = BinaryUtils.getBitValue(flag, 4) == 1;
                                boolean mpptOn2 = BinaryUtils.getBitValue(flag, 5) == 1;
                                boolean evOn = BinaryUtils.getBitValue(flag, 6) == 1;
                                boolean batteryOn0 = BinaryUtils.getBitValue(flag, 7) == 1;
                                boolean batteryOn1 = BinaryUtils.getBitValue(flag, 8) == 1;
                                boolean batteryOn2 = BinaryUtils.getBitValue(flag, 9) == 1;
                                boolean BSensorInputOn = BinaryUtils.getBitValue(flag, 10) == 1;

                                int citySource0 = BinaryUtils.bytes2IntLittle(new byte[]{payload[2], payload[3], 0, 0});
                                int citySource1 = BinaryUtils.bytes2IntLittle(new byte[]{payload[4], payload[5], 0, 0});
                                int citySource2 = BinaryUtils.bytes2IntLittle(new byte[]{payload[6], payload[7], 0, 0});
                                int mppt0 = BinaryUtils.bytes2IntLittle(new byte[]{payload[8], payload[9], 0, 0});
                                int mppt1 = BinaryUtils.bytes2IntLittle(new byte[]{payload[10], payload[11], 0, 0});
                                int mppt2 = BinaryUtils.bytes2IntLittle(new byte[]{payload[12], payload[13], 0, 0});
                                int ev = BinaryUtils.bytes2IntLittle(new byte[]{payload[14], payload[15], 0, 0});
                                int battery0 = BinaryUtils.bytes2IntLittle(new byte[]{payload[16], payload[17], 0, 0});
                                int battery1 = BinaryUtils.bytes2IntLittle(new byte[]{payload[18], payload[19], 0, 0});
                                int battery2 = BinaryUtils.bytes2IntLittle(new byte[]{payload[20], payload[21], 0, 0});
                                int BSensorInput = BinaryUtils.bytes2IntLittle(new byte[]{payload[22], payload[23], 0, 0});

                                resultMap = Cmd.getDefaultResultMap(true, BmtCmd.GET_INVERTER_INPUT_INFO);
                                Cmd.putResultValue(resultMap, BmtDataKey.EV_ON, evOn);
                                Cmd.putResultValue(resultMap, BmtDataKey.EV, ev);
                                Cmd.putResultValue(resultMap, BmtDataKey.B_SENSOR_INPUT_ON, BSensorInputOn);
                                Cmd.putResultValue(resultMap, BmtDataKey.B_SENSOR_INPUT, BSensorInput);
                                Map map = new HashMap();
                                map.put(BmtDataKey.CITY_SOURCE_ON, citySourceOn0);
                                map.put(BmtDataKey.CITY_SOURCE, citySource0);
                                map.put(BmtDataKey.BATTERY_ON, batteryOn0);
                                map.put(BmtDataKey.BATTERY, battery0);
                                map.put(BmtDataKey.MPPT_ON, mpptOn0);
                                map.put(BmtDataKey.MPPT, mppt0);
                                Cmd.putResultValue(resultMap, BmtDataKey.VALUE_0, map);
                                map = new HashMap();
                                map.put(BmtDataKey.CITY_SOURCE_ON, citySourceOn1);
                                map.put(BmtDataKey.CITY_SOURCE, citySource1);
                                map.put(BmtDataKey.BATTERY_ON, batteryOn1);
                                map.put(BmtDataKey.BATTERY, battery1);
                                map.put(BmtDataKey.MPPT_ON, mpptOn1);
                                map.put(BmtDataKey.MPPT, mppt1);
                                Cmd.putResultValue(resultMap, BmtDataKey.VALUE_1, map);
                                map = new HashMap();
                                map.put(BmtDataKey.CITY_SOURCE_ON, citySourceOn2);
                                map.put(BmtDataKey.CITY_SOURCE, citySource2);
                                map.put(BmtDataKey.BATTERY_ON, batteryOn2);
                                map.put(BmtDataKey.BATTERY, battery2);
                                map.put(BmtDataKey.MPPT_ON, mpptOn2);
                                map.put(BmtDataKey.MPPT, mppt2);
                                Cmd.putResultValue(resultMap, BmtDataKey.VALUE_2, map);

                                device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                            }
                        } catch (Exception e) {
                            if (MsctLog.isEnableLog) {
                                e.printStackTrace();
                            }
                            Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_INVERTER_INPUT_INFO);
                            Cmd.putResultValue(resultMap, BmtDataKey.ERROR_MESSAGE, e.getMessage());
                            device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                        }
                    }

                    @Override
                    public void onResultEvent(IMsg iMsg) {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onFinish() {

                    }

                    @Override
                    public void onReStart() {

                    }

                    @Override
                    public void onFail() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_INVERTER_INPUT_INFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }

                    @Override
                    public void onTimeOut() {
                        Map resultMap = Cmd.getDefaultResultMap(false, BmtCmd.GET_INVERTER_INPUT_INFO);
                        device.dispatchResult((String) resultMap.get(BmtDataKey.CMD), resultMap);
                    }
                })
                .build();
        return msctBuilder.build();
    }

    /**
     * 字符串转字节数组, 第一位是字符串长度
     *
     * @param str
     * @return
     */
    private byte[] getStrParamByteArr(String str) {
//        final char[] chars = str.toCharArray();
//        final int len = chars.length;
//        final byte[] strByteArr = new byte[len + 1];
//        strByteArr[0] = (byte) (len & 0xFF);
//        for (int i = 0; i < len; i++) {
//            strByteArr[i + 1] = (byte) (chars[i] & 0xFF);
//        }
//        return strByteArr;

        final byte[] bytes = str.getBytes(StandardCharsets.UTF_8);
        final int len = bytes.length;
        final byte[] strByteArr = new byte[len + 1];
        strByteArr[0] = (byte) (len & 0xFF);
        for (int i = 0; i < len; i++) {
            strByteArr[i + 1] = bytes[i];
        }
        return strByteArr;
    }

    private void printResponse(final MsctResponse response) {
        if (!MsctLog.isEnableLog || null == response) {
            return;
        }

        try {
            MsctContext msctContext = response.getMsctContext();
            short method = BinaryUtils.byteToShortLittle(Utils.unSignByteToByte(msctContext.ShouldGetOptionHeader(Exoption.OPTION_METHOD)));
            int status = response.getStatus();
            byte[] payload = (byte[]) response.getMsctContext().getDecodedPayload();
            MsctLog.d(TAG, String.format("requect convert method:%04x,status:%d,payload:%s", method, status, com.dinsafer.dssupport.utils.HexUtil.bytesToHexString(payload)));
        } catch (Exception e) {

        }
    }
}
