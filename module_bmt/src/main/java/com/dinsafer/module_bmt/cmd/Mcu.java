package com.dinsafer.module_bmt.cmd;

import androidx.annotation.Keep;
import androidx.annotation.Nullable;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2022/12/6
 */
@Keep
public class Mcu {
    @Keep
    public static class System {
        //顺序不能随意修改
        @Keep
        public enum SystemException {
            OfflineOverLoadProtect(1),            // BIT0:离线过载保护。（超过4000W关闭输出）
            SOCTooLow(2),                     // BIT1：pack的soc过低（基础模式低于ALARM+2%关闭输出，其它模式按照SOC OFF的设定值关闭输出）
            MainLineVoltageTooLow(3),            // BIT2:总线电压过低。（告警不关闭输出）
            TestOverTemperature(4),            // BIT3:系统测试的温度过高（MCU测试采样的温度高于80度关闭输出）
            TestLowTemperature(5),            // BIT4:系统测试的温度过低（MCU测试采样的温度低于-30度关闭输出）
            DataError(6),            // BIT5:机柜指示数量与实际读取的数量不匹配（容错处理，会告警不关闭输出）
            BatteryEffect(7),            // BIT6:电池的性能恶化告警（检测电池的健康度指标告警处理不关闭）
            LOUTConnectError(8),        // BIT7:逆变L-OUT接线错误。（即LOUT未开的情况下接入LIN市电电压，检测到异常之后，关闭逆变使能关闭输出。外部异常清除后，自动输出）
            SensorConnectError(9),      // BIT8:电表接线错误.（即电表的输入霍尔传感器的接线一致性有问题，3路输入的功率应该是同向的，检测到不同向时告警处理，不关输出）
            CabinetUnExist(10),      // BIT9：gb_box_unexist_for_hard 9 //硬件指示机柜不存在
            BatteryMaintenance(11);      // BIT10：电池维护状态（当前电池禁止放电）

            private final int code;

            SystemException(int code) {
                this.code = code;
            }

            public int getCode() {
                return code;
            }

            @Nullable
            public static SystemException findByIndex(int index) {
                int length = SystemException.values().length;
                if (index < 0 || index >= length) {
                    return null;
                }
                return SystemException.values()[index];
            }

            @Nullable
            public static SystemException valueOf(int code) {
                for (SystemException value : SystemException.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        //顺序不能随意修改
        @Keep
        public enum CommunicationException {
            IOTtoMCU(1),            // IOT/MCU 通信故障
            BSensor(2);             // 三相电表通信故障

            private int code;

            CommunicationException(int code) {
                this.code = code;
            }

            public int getCode() {
                return code;
            }

            @Nullable
            public static CommunicationException findByIndex(int index) {
                int length = CommunicationException.values().length;
                if (index < 0 || index >= length) {
                    return null;
                }
                return CommunicationException.values()[index];
            }

            @Nullable
            public static CommunicationException valueOf(int code) {
                for (CommunicationException value : CommunicationException.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }
    }

    @Keep
    public static class Battery {
        public static final byte CONTROL_BMS_CLOSE = 0x00;
        public static final byte CONTROL_BMS_OPEN = 0x01;
        public static final byte CONTROL_CLOSE_CHARGE_CLOSE_RELEASE = 0x02;
        public static final byte CONTROL_CLOSE_CHARGE_OPEN_RELEASE = 0x03;
        public static final byte CONTROL_OPEN_CHARGE_CLOSE_RELEASE = 0x04;
        public static final byte CONTROL_OPEN_CHARGE_OPEN_RELEASE = 0x05;
        public static final byte CONTROL_BATTERY_OFF = (byte) 0xff;
        //SOC 告警值
        public static final byte CONTROL_BATTERY_SET_SOC_ALERT = 0x00;
        //SOC 关机值
        public static final byte CONTROL_BATTERY_SET_SOC_SHUTDOWN = 0x01;

        //单个电池包状态
        public static final byte STATE_CLOSE = 0x00;
        public static final byte STATE_NORMAL = 0x01;
        public static final byte STATE_ERROR = 0x02;

        public static final byte STATE_HEATING_FILM_UNKNOW = 0x00;
        public static final byte STATE_HEATING_FILM_HEATING_CLOSE = 0x01;
        public static final byte STATE_HEATING_FILM_HEATING_OPEN = 0x02;

        public static final byte STATE_BMS_CLOSE = 0x00;
        public static final byte STATE_BMS_OPEN = 0x01;

        //顺序不能随意修改
        @Keep
        public enum BatteryException {
            //DFETON(1),            // DFET ON
            //CFETON(2),            // CFET ON
            HardWare(1),                   // 硬件故障

            ReleaseHighTempAlert(2),                   // 放电高温告警
            LowVoltageAlert(3),                   // 低压告警
            ReleaseOverCurrentAlert(4),                   // 放电过流告警
            FetTempProtect(5),                   // fet温度保护

            ChargeHighTempProtect(6),                   // 充电高温保护
            ChargeLowTempProtect(7),                   // 充电低温保护

            ReleaseHighTempProtect(8),                   // 放电过温保护
            ReleaseLowTempProtect(9),                   // 放电低温保护

            ReleaseShortCircuitProtect(10),               //放电短路保护
            ChargeOverCurrentProtect(11),                   // 充电过流保护

            LowVoltageProtect(12),                   // 低压保护
            HighVoltageProtect(13),                   // 高压保护
            Communication(14);                       // 通讯故障

            private int code;

            BatteryException(int code) {
                this.code = code;
            }

            public int getCode() {
                return code;
            }


            @Nullable
            public static BatteryException findByIndex(int index) {
                int length = BatteryException.values().length;
                if (index < 0 || index >= length) {
                    return null;
                }
                return BatteryException.values()[index];
            }

            @Nullable
            public static BatteryException valueOf(int code) {
                for (BatteryException value : BatteryException.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        @Keep
        public static enum BatterySOCState {
            full(0x00),
            lowSOCAlert(0x01),
            lowSOCWarning(0x02),
            lowSOCProtect(0x03);

            private int socState;

            public int getSocState() {
                return socState;
            }

            BatterySOCState(int socState) {
                this.socState = socState;
            }

            @Nullable
            public static BatterySOCState findByState(int state) {
                for (BatterySOCState value : BatterySOCState.values()) {
                    if (value.socState == state) {
                        return value;
                    }
                }
                return null;
            }
        }
    }

    @Keep
    public static class Inverter {

        //顺序不能随意修改
        @Keep
        public enum InverterState {
            ShutDown(1, 0x00),                   // 关闭没有异常
            Working(2, 0x01),                      // 正常开启
            ErrorDown(3, 0x02);                    // 异常关闭

            private int code;
            private int state;

            InverterState(int code, int state) {
                this.code = code;
                this.state = state;
            }

            public int getCode() {
                return code;
            }

            public int getState() {
                return state;
            }

            @Nullable
            public static InverterState findByIndex(int index) {
                int length = InverterState.values().length;
                if (index < 0 || index >= length) {
                    return null;
                }
                return InverterState.values()[index];
            }

            @Nullable
            public static InverterState findByState(int state) {
                for (InverterState value : InverterState.values()) {
                    if (value.state == state) {
                        return value;
                    }
                }
                return null;
            }

            @Nullable
            public static InverterState valueOf(int code) {
                for (InverterState value : InverterState.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        @Keep
        public enum InverterFanState {
            STOP(1, 0x00),                // 正常，没有转动
            RUNNING(2, 0x01);            // 正常，有转动

            private final int code;
            private final int state;

            public int getCode() {
                return code;
            }

            public int getState() {
                return state;
            }

            InverterFanState(int code, int state) {
                this.code = code;
                this.state = state;
            }

            @Nullable
            public static InverterFanState findByIndex(int index) {
                int length = InverterFanState.values().length;
                if (index < 0 || index >= length) {
                    return null;
                }
                return Inverter.InverterFanState.values()[index];
            }

            @Nullable
            public static InverterFanState findByState(int state) {
                for (InverterFanState value : InverterFanState.values()) {
                    if (value.state == state) {
                        return value;
                    }
                }
                return null;
            }

            @Nullable
            public static InverterFanState valueOf(int code) {
                for (InverterFanState value : InverterFanState.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        /**
         * 逆变电池端故
         */
        @Keep
        public enum InverterBatteryException {
            OverVoltage(1),                 // Bit0：电池过压（保护之后，关闭输出，恢复之后，正常输出）
            LackOfVoltage(2),                // Bit1：电池欠压（保护之后，关闭输出，恢复之后，正常输出）
            OverTemperature(3),              // Bit2：电池过温（保护之后，关闭输出，恢复之后，正常输出）
            OverCurrent(4),                  // Bit3：电池过流（锁死）
            HardwareOverCurrent(5),          // Bit4: 电池硬件过流（锁死）
            BoostCooler1OverTemperature(6),  // Bit5: 升压散热器1过温（保护之后，关闭输出，恢复之后，正常输出）
            BoostCooler2OverTemperature(7),  // Bit6：升压散热器2过温（保护之后，关闭输出，恢复之后，正常输出）
            BoostCooler3OverTemperature(8),  // Bit7：升压散热器3过温（保护之后，关闭输出，恢复之后，正常输出）
            BoostCooler1Error(9),            // Bit8：升压散热器1故障（保护之后，关闭输出，恢复之后，正常输出）
            BoostCooler2Error(10),            // Bit9：升压散热器2故障（保护之后，关闭输出，恢复之后，正常输出）
            BoostCooler3Error(11);            // Bit10：升压散热器3故障（保护之后，关闭输出，恢复之后，正常输出）

            private final int code;

            public int getCode() {
                return code;
            }

            InverterBatteryException(int code) {
                this.code = code;
            }

            @Nullable
            public static InverterBatteryException findByIndex(int index) {
                final int len = InverterBatteryException.values().length;
                if (index < 0 || index >= len) {
                    return null;
                }
                return InverterBatteryException.values()[index];
            }

            @Nullable
            public static InverterBatteryException valueOf(int code) {
                for (InverterBatteryException value : InverterBatteryException.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        @Keep
        public enum InverterException {
            OutOverVoltage(1),                 // Bit0：逆变输出过压（锁死）
            InOverVoltage(2),                   // BIT1：逆变输出欠压（锁死）
            Unknown(3),                          // BIT2：预留
            DCPartOverCurrent(4),               // BIT3：逆变电流直流分量高（锁死）
            OverCurrent(5),                     // BIT4：逆变电流过流（锁死）
            HardwareOverCurrent(6),             // BIT5：逆变电流硬件过流（锁死）
            OutShortCircuit(7),                 // BIT6：逆变输出短路（锁死）
            Overload105(8),                     // BIT7：负载过载-105%（锁死）
            Overload120(9),                     // BIT8：负载过载-120%（锁死）
            Overload200(10),                     // BIT9：负载过载-200%（锁死）
            CoolerOverTemperature(11),           // BIT10：逆变散热器过温（保护之后，关闭输出，恢复之后，正常输出）
            CoolerError(12);                     // BIT11：逆变散热器故障（保护之后，关闭输出，恢复之后，正常输出）

            private final int code;

            public int getCode() {
                return code;
            }

            InverterException(int code) {
                this.code = code;
            }

            @Nullable
            public static InverterException findByIndex(int index) {
                final int len = InverterException.values().length;
                if (index < 0 || index >= len) {
                    return null;
                }
                return InverterException.values()[index];
            }

            @Nullable
            public static InverterException valueOf(int code) {
                for (InverterException value : InverterException.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        /**
         * 逆变电网端故障
         */
        @Keep
        public enum InverterGridException {
            SecOverVoltage(1),              // Bit0：电网瞬时值过压（告警类，故障之后，维持输出）
            EffectL1OverVoltage(2),        // Bit1：电网有效值1级过压（告警类，故障之后，维持输出）
            EffectL2OverVoltage(3),        // Bit2：电网有效值2级过压（告警类，故障之后，维持输出）
            EffectL1LackOfVoltage(4),      // Bit3：电网有效值1级欠压（告警类，故障之后，维持输出）
            EffectL2LackOfVoltage(5),      // Bit4：电网有效值2级欠压（告警类，故障之后，维持输出）
            SecLackOfVoltage(6),           // Bit5：电网瞬时值欠压（告警类，故障之后，维持输出）
            L1OverFrequency(7),            // Bit6：频率1级过频（告警类，故障之后，维持输出）
            L2OverFrequency(8),            // Bit7：频率2级过频（告警类，故障之后，维持输出）
            L1LackOfFrequency(9),          // Bit8：频率1级欠频（告警类，故障之后，维持输出）
            L2LackOfFrequency(10),          // Bit9：频率2级欠频（告警类，故障之后，维持输出）
            EnvelopeError(11),              // Bit10：电网包络异常（告警类，故障之后，维持输出）
            ShutdownError(12),              // Bit11：电网锁相异常（告警类，故障之后，维持输出）
            CacheRelayError(13),            // Bit12：缓冲继电器粘死检测（锁死）
            MainRelayError(14);             // Bit13：主继电器粘死检测（锁死）

            private final int code;

            public int getCode() {
                return code;
            }

            InverterGridException(int code) {
                this.code = code;
            }

            @Nullable
            public static InverterGridException findByIndex(int index) {
                final int len = InverterGridException.values().length;
                if (index < 0 || index >= len) {
                    return null;
                }
                return InverterGridException.values()[index];
            }

            @Nullable
            public static InverterGridException valueOf(int code) {
                for (InverterGridException value : InverterGridException.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        /**
         * 逆变系统故障
         */
        @Keep
        public enum InverterSystemException {
            InsulationCheckError(1),              // Bit0：绝缘检测异常（锁死）
            LeakageCheckError(2),              // Bit1：漏电检测异常（锁死）
            Unknown(3),             // Bit2：预留
            MainLine1OverVoltage(4),             // Bit3：母线1级过压（锁死）
            MainLine2OverVoltage(5),             // Bit4：母线2级过压（锁死）
            MainLine1LackOfVoltage(6),             // Bit5：母线1级欠压（锁死）
            MainLine2LackOfVoltage(7),             // Bit6：母线2级欠压（锁死）
            MainLineError(8),             // Bit7：母线故障（锁死）
            PowerDown(9),             // Bit8：PowerDown（锁死）
            OverTemperature(10),             // Bit9：变压器过温（保护之后，关闭输出，恢复之后，正常输出）
            Error(11),             // Bit10：变压器故障（保护之后，关闭输出，恢复之后，正常输出）
            Communication(12),             // Bit11：通信故障（保护之后，关闭输出，恢复之后，正常输出）
            Fan(13);             // Bit12：风机故障（告警类，故障之后，维持输出）

            private final int code;

            public int getCode() {
                return code;
            }

            InverterSystemException(int code) {
                this.code = code;
            }

            @Nullable
            public static InverterSystemException findByIndex(int index) {
                final int len = InverterSystemException.values().length;
                if (index < 0 || index >= len) {
                    return null;
                }
                return InverterSystemException.values()[index];
            }

            @Nullable
            public static InverterSystemException valueOf(int code) {
                for (InverterSystemException value : InverterSystemException.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        /**
         * 逆变光伏故障
         */
        @Keep
        public enum InverterMPPTException {
            OverVoltage(1),              // Bit0：光伏过压（保护之后，关闭输出，恢复之后，正常输出）
            LackOfVoltage(2),              // Bit1：光伏欠压（保护之后，关闭输出，恢复之后，正常输出）
            OverCurrent(3),              // Bit2：光伏过流（锁死）
            CoolerOverTemperature(4),              // Bit3：光伏散热器1过温（保护之后，关闭输出，恢复之后，正常输出）
            CoolerError(5);              // Bit4：光伏散热器1故障（保护之后，关闭输出，恢复之后，正常输出）

            private final int code;

            public int getCode() {
                return code;
            }

            InverterMPPTException(int code) {
                this.code = code;
            }

            @Nullable
            public static InverterMPPTException findByIndex(int index) {
                final int len = InverterMPPTException.values().length;
                if (index < 0 || index >= len) {
                    return null;
                }
                return InverterMPPTException.values()[index];
            }

            @Nullable
            public static InverterMPPTException valueOf(int code) {
                for (InverterMPPTException value : InverterMPPTException.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        /**
         * 逆变显示故障
         */
        @Keep
        public enum InverterPresentException {
            BatteryOverVoltage(1),              // Bit0：电池过压
            BatteryLackOfVoltage(2),              // Bit1：电池欠压
            OverTemperature(3),              // Bit2：逆变器过温
            GridVoltage(4),              // Bit3：电网电压异常
            GridFrequency(5),              // Bit4：电网频率异常
            OutVoltage(6),              // Bit5：输出电压异常
            OutShortCircuit(7),              // Bit6：输出短路
            OutOverload(8),              // Bit7：输出过载
            Error(9),              // Bit8：逆变器故障
            MPPTOverVoltage(10),              // Bit9：光伏过压
            MPPTError(11);              // Bit10：光伏故障

            private final int code;

            public int getCode() {
                return code;
            }

            InverterPresentException(int code) {
                this.code = code;
            }

            @Nullable
            public static InverterPresentException findByIndex(int index) {
                final int len = InverterPresentException.values().length;
                if (index < 0 || index >= len) {
                    return null;
                }
                return InverterPresentException.values()[index];
            }

            @Nullable
            public static InverterPresentException valueOf(int code) {
                for (InverterPresentException value : InverterPresentException.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        /**
         * 逆变DC主动故障
         */
        @Keep
        public enum InverterDCException {
            Error(1),                   // Bit0：逆变器使能硬件故障（锁死）DC判断到逆变使能没开但是可以通信时认为故障
            Communication(2);           // Bit1：DC同逆变器通信故障。（锁死）

            private final int code;

            public int getCode() {
                return code;
            }

            InverterDCException(int code) {
                this.code = code;
            }

            @Nullable
            public static InverterDCException findByIndex(int index) {
                final int len = InverterDCException.values().length;
                if (index < 0 || index >= len) {
                    return null;
                }
                return InverterDCException.values()[index];
            }

            @Nullable
            public static InverterDCException valueOf(int code) {
                for (InverterDCException value : InverterDCException.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }
    }

    @Keep
    public static class MPPT {
        public static final byte STATE_CLOSE = 0x00;
        public static final byte STATE_OPEN = 0x01;

        //顺序不能随意修改
        @Keep
        public enum MPPTException {
            OverVoltage(1),            // Bit0：光伏过压
            LackOfVoltage(2),            // Bit1：光伏欠压
            OverCurrent(3),            // Bit2：光伏过流（锁死）
            CoolerOverTemperature(4),            // Bit3：光伏散热器1过温
            CoolerError(5);            // Bit4：光伏散热器1故障

            private final int code;

            MPPTException(int code) {
                this.code = code;
            }

            public int getCode() {
                return code;
            }

            @Nullable
            public static MPPT.MPPTException findByIndex(int index) {
                final int length = MPPTException.values().length;
                if (index < 0 || index >= length) {
                    return null;
                }
                return MPPT.MPPTException.values()[index];
            }

            @Nullable
            public static MPPTException valueOf(int code) {
                for (MPPTException value : MPPTException.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }
    }

    @Keep
    public static class EV {
        public static final byte STATE_RELEASE = 0x00;
        public static final byte STATE_CHARGE = 0x01;

        @Keep
        enum EVState {
            close(1, 0x00),            // 关闭
            free(2, 0x01),            // 空闲
            output(3, 0x02),              // 输出
            input(4, 0x03);           // 输入
            private int code;
            private int stateValue;

            EVState(int code, int stateValue) {
                this.code = code;
                this.stateValue = stateValue;
            }

            public int getCode() {
                return code;
            }

            public int getStateValue() {
                return stateValue;
            }

            @Nullable
            public static EVState findByState(int state) {
                for (EVState value : EVState.values()) {
                    if (value.stateValue == state) {
                        return value;
                    }
                }
                return null;
            }

            @Nullable
            public static EVState valueOf(int code) {
                for (EVState value : EVState.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        @Keep
        public enum EVLightState {
            stop(1, 0x00),            // 不工作状态
            green(2, 0x01),            // 充电中
            red(3, 0x02);              // 异常

            private int code;
            private int stateValue;

            EVLightState(int code, int stateValue) {
                this.code = code;
                this.stateValue = stateValue;
            }

            public int getCode() {
                return code;
            }

            public int getStateValue() {
                return stateValue;
            }

            @Nullable
            public static EVLightState findByState(int state) {
                for (EVLightState value : EVLightState.values()) {
                    if (value.stateValue == state) {
                        return value;
                    }
                }
                return null;
            }

            @Nullable
            public static EVLightState valueOf(int code) {
                for (EVLightState value : EVLightState.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        //顺序不能随意修改
        @Keep
        public enum EVException {
            LeakageCurrent(1),            // 漏电
            OverVoltage(2),                   // 过压
            lackVoltage(3),                   // 欠压
            OverCurrent(4),                   // 过流
            OverTemperature(5),                   // 过温
            LeakageCurrentChecking(6),                   // 漏电自检异常
            EarthWire(7),                   // 地线异常
            CPLevel(8),                     // CP电平异常
            Relay(9),                     // 继电器异常
            AssistCPU(10),                     // 辅助处理器异常
            System5V(11),                    // 系统5v异常
            Communication(12);              // 通讯故障

            private int code;

            EVException(int code) {
                this.code = code;
            }

            public int getCode() {
                return code;
            }

            @Nullable
            public static EV.EVException findByIndex(int index) {
                int length = EVException.values().length;
                if (index < 0 || index >= length) {
                    return null;
                }
                return EV.EVException.values()[index];
            }

            @Nullable
            public static EV.EVException valueOf(int code) {
                for (EVException value : EVException.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        @Keep
        public enum EVDetailState {
            close(1),            // 关闭
            free(2),            // 空闲
            charging(3),              // 充电中
            carUnvalid(4),            // 车端挂起(车端主动停止充电)
            unvalid(5),            // 桩端挂起(桩端主动停止充电)
            complete(6),            // 充电结束（应用侧可以配合电量量消耗大于5Kwh，来认定是否充满）
            error(7);               // 充电桩故障且停止充电

            private int code;

            EVDetailState(int code) {
                this.code = code;
            }

            public int getCode() {
                return code;
            }

            @Nullable
            public static EV.EVDetailState findByIndex(int index) {
                int length = EVDetailState.values().length;
                if (index < 0 || index >= length) {
                    return null;
                }
                return EV.EVDetailState.values()[index];
            }

            @Nullable
            public static EV.EVDetailState valueOf(int code) {
                for (EVDetailState value : EVDetailState.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        @Keep
        public enum EVChargingMode {
            lowerUtilityRate(1),          // 在接下来的12个小时内，找到电价最低的3个小时充电，每小时最多可充10kWh。
            solarOnly(2),                   // EV充电功率将被限制在太阳能发电功率以内。
            scheduled(3),                   // 在预设的时段内允许EV充电。
            instantChargeFull(4),               // 立即充电，充满为止
            instantChargeFixed(5);               // 每次固定充电

            private int code;

            EVChargingMode(int code) {
                this.code = code;
            }

            public int getCode() {
                return code;
            }

            @Nullable
            public static EV.EVChargingMode findByIndex(int index) {
                int length = EVChargingMode.values().length;
                if (index < 0 || index >= length) {
                    return null;
                }
                return EV.EVChargingMode.values()[index];
            }

            @Nullable
            public static EV.EVChargingMode valueOf(int code) {
                for (EVChargingMode value : EVChargingMode.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        @Keep
        public enum EVAdvanceStatus {
            off(1),                       // 空闲，没有插进车里
            wait(2),                        // 已经插在车里，由于条件未符合，导致还没开始充电
            charging(3),                    // 充电中
            error(4);                       // 有异常发生
            private int evAdvanceStatus;

            EVAdvanceStatus(int evAdvanceStatus) {
                this.evAdvanceStatus = evAdvanceStatus;
            }

            public int getEvAdvanceStatus() {
                return evAdvanceStatus;
            }

            public static EV.EVAdvanceStatus valueOf(int status) {
                for (EVAdvanceStatus value : EVAdvanceStatus.values()) {
                    if (value.evAdvanceStatus == status) {
                        return value;
                    }
                }
                return null;
            }
        }
    }

    @Keep
    public static class Cabinet {

        @Keep
        public enum CabinetWaterState {
            valid(1, 0x00),            // 正常
            exception(2, 0x01);         // 异常

            private int code;
            private int stateValue;

            CabinetWaterState(int code, int stateValue) {
                this.code = code;
                this.stateValue = stateValue;
            }

            public int getCode() {
                return code;
            }

            public int getStateValue() {
                return stateValue;
            }

            @Nullable
            public static Cabinet.CabinetWaterState findByState(int state) {
                for (CabinetWaterState value : CabinetWaterState.values()) {
                    if (value.stateValue == state) {
                        return value;
                    }
                }
                return null;
            }

            @Nullable
            public static CabinetWaterState valueOf(int code) {
                for (CabinetWaterState value : CabinetWaterState.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        @Keep
        public enum CabinetSmokeState {
            valid(1, 0x00),            // 正常
            exception(2, 0x01);         // 异常

            private int code;
            private int stateValue;

            CabinetSmokeState(int code, int stateValue) {
                this.code = code;
                this.stateValue = stateValue;
            }

            public int getCode() {
                return code;
            }

            public int getStateValue() {
                return stateValue;
            }

            @Nullable
            public static Cabinet.CabinetSmokeState findByState(int state) {
                for (CabinetSmokeState value : CabinetSmokeState.values()) {
                    if (value.stateValue == state) {
                        return value;
                    }
                }
                return null;
            }

            @Nullable
            public static CabinetSmokeState valueOf(int code) {
                for (CabinetSmokeState value : CabinetSmokeState.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        @Keep
        public enum CabinetFanState {
            stop(1, 0x00),                // 正常，没有转动
            running(2, 0x01),            // 正常，有转动
            exception(3, 0x02);              // 异常（堵转或者没插）

            private int code;
            private int stateValue;

            CabinetFanState(int code, int stateValue) {
                this.code = code;
                this.stateValue = stateValue;
            }

            public int getCode() {
                return code;
            }

            public int getStateValue() {
                return stateValue;
            }

            @Nullable
            public static Cabinet.CabinetFanState findByState(int state) {
                for (CabinetFanState value : CabinetFanState.values()) {
                    if (value.stateValue == state) {
                        return value;
                    }
                }
                return null;
            }

            @Nullable
            public static CabinetFanState valueOf(int code) {
                for (CabinetFanState value : CabinetFanState.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        //顺序不能随意修改
        @Keep
        public enum CabinetException {
            WaterSensor(1),            // 水感异常
            SmokeSensor(2),            // 烟感异常
            FanSensor(3),              // 风扇异常
            Communication(4);          // 机柜(MCU)通讯故障

            private int code;

            CabinetException(int code) {
                this.code = code;
            }

            public int getCode() {
                return code;
            }

            @Nullable
            public static CabinetException findByIndex(int index) {
                final int length = CabinetException.values().length;
                if (index < 0 || index >= length) {
                    return null;
                }
                return CabinetException.values()[index];
            }

            @Nullable
            public static CabinetException valueOf(int code) {
                for (CabinetException value : CabinetException.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

    }

    @Keep
    public static class BSensor {
        //不在位
        public static final byte STATE_INVALID = 0x00;
        //在位
        public static final byte STATE_VALID = 0x01;
    }

    @Keep
    public static class Strategy {
        @Keep
        public enum StrategyType {
            PriceInsensitive(1, 0x00),            // For users whose Utility Rate Plan is Fast.
            SmartCharge(2, 0x01),            // Track electricity market prices. For users whose Utility Rate Plan is Variable.
            ExtremelySaving(3, 0x02);            // Track electricity market prices with a higher price limit than Smart Charge.

            private int code;
            private int value;

            StrategyType(int code, int stateValue) {
                this.code = code;
                this.value = stateValue;
            }

            public int getCode() {
                return code;
            }

            public int getValue() {
                return value;
            }

            @Nullable
            public static StrategyType findByValue(int value) {
                for (StrategyType type : StrategyType.values()) {
                    if (type.value == value) {
                        return type;
                    }
                }
                return null;
            }

            @Nullable
            public static StrategyType valueOf(int code) {
                for (StrategyType value : StrategyType.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

    }

    @Keep
    public static class CommunicateSignal {
        @Keep
        public enum WIFISignal {
            None(1, 0x00),            // 没信号
            Weak(2, 0x01),                   // 弱
            Medium(3, 0x02),                   // 良好
            Strong(4, 0x03);                   // 很好

            private int code;
            private int value;

            WIFISignal(int code, int value) {
                this.code = code;
                this.value = value;
            }

            public int getCode() {
                return code;
            }

            public int getValue() {
                return value;
            }

            @Nullable
            public static WIFISignal findByValue(int value) {
                for (WIFISignal type : WIFISignal.values()) {
                    if (type.value == value) {
                        return type;
                    }
                }
                return null;
            }

            @Nullable
            public static WIFISignal valueOf(int code) {
                for (WIFISignal value : WIFISignal.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        /**
         * Cellular信号强度
         */
        @Keep
        public enum CellularSignal {
            None(1, 0x00),            // 没信号
            Weak(2, 0x01),                   // 弱
            Medium(3, 0x02),                   // 良好
            Strong(4, 0x03);                   // 很好

            private final int code;
            private final int state;

            public int getCode() {
                return code;
            }

            public int getState() {
                return state;
            }

            CellularSignal(int code, int state) {
                this.code = code;
                this.state = state;
            }

            @Nullable
            public static CellularSignal findByIndex(int index) {
                final int len = CellularSignal.values().length;
                if (index < 0 || index >= len) {
                    return null;
                }
                return CellularSignal.values()[index];
            }

            @Nullable
            public static CellularSignal findByState(int state) {
                for (CellularSignal value : CellularSignal.values()) {
                    if (value.state == state) {
                        return value;
                    }
                }
                return null;
            }

            @Nullable
            public static CellularSignal valueOf(int code) {
                for (CellularSignal value : CellularSignal.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        /**
         * 以太网信号
         */
        @Keep
        public enum EthernetSignal {
            INVALID(1, 0x00),    // 无连接
            VALID(2, 0x01);      // 连接中

            private final int code;
            private final int state;

            public int getCode() {
                return code;
            }

            public int getState() {
                return state;
            }

            EthernetSignal(int code, int state) {
                this.code = code;
                this.state = state;
            }

            @Nullable
            public static EthernetSignal findByState(int state) {
                for (EthernetSignal value : EthernetSignal.values()) {
                    if (value.state == state) {
                        return value;
                    }
                }
                return null;
            }
        }

    }

    @Keep
    public static class Mode {
        @Keep
        public enum ChargeMode {
            MODE1(1, 0x01),
            MODE2(2, 0x02),
            MODE3(3, 0x03),
            MODE4(4, 0x04);

            private int code;
            private int value;

            ChargeMode(int code, int value) {
                this.code = code;
                this.value = value;
            }

            public int getCode() {
                return code;
            }

            public int getValue() {
                return value;
            }

            @Nullable
            public static ChargeMode findByValue(int value) {
                for (ChargeMode type : ChargeMode.values()) {
                    if (type.value == value) {
                        return type;
                    }
                }
                return null;
            }

            @Nullable
            public static ChargeMode valueOf(int code) {
                for (ChargeMode value : ChargeMode.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        @Keep
        public enum ChargeModePolicy {
            MODE_POLICY_1(1, 0x00),
            MODE_POLICY_2(2, 0x01),
            MODE_POLICY_3(3, 0x02),
            MODE_POLICY_4(4, 0x03);

            private int code;
            private int value;

            ChargeModePolicy(int code, int value) {
                this.code = code;
                this.value = value;
            }

            public int getCode() {
                return code;
            }

            public int getValue() {
                return value;
            }

            @Nullable
            public static ChargeModePolicy findByValue(int value) {
                for (ChargeModePolicy type : ChargeModePolicy.values()) {
                    if (type.value == value) {
                        return type;
                    }
                }
                return null;
            }

            @Nullable
            public static ChargeModePolicy valueOf(int code) {
                for (ChargeModePolicy value : ChargeModePolicy.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        @Keep
        public enum ChargeModeFlag {
            FLAG1(0, 0X00),
            FLAG2(1, 0X01);

            private int code;
            private int value;

            ChargeModeFlag(int code, int value) {
                this.code = code;
                this.value = value;
            }

            public int getCode() {
                return code;
            }

            public int getValue() {
                return value;
            }

            @Nullable
            public static ChargeModeFlag findByValue(int value) {
                for (ChargeModeFlag type : ChargeModeFlag.values()) {
                    if (type.value == value) {
                        return type;
                    }
                }
                return null;
            }

            @Nullable
            public static ChargeModeFlag valueOf(int code) {
                for (ChargeModeFlag value : ChargeModeFlag.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

    }

    @Keep
    public static class Chips {

        @Keep
        public enum ChipsUpdateState {
            uptodate(0),                // 没有更新
            updating(1),            // 正在更新
            waitForUpdate(2),        // 等待用户指令更新
            forceUpdate(3);        // 等待用户指令更新

            private final int code;

            public int getCode() {
                return code;
            }

            ChipsUpdateState(int code) {
                this.code = code;
            }

            @Nullable
            public static ChipsUpdateState findByIndex(int index) {
                final int len = ChipsUpdateState.values().length;
                if (index < 0 || index >= len) {
                    return null;
                }
                return ChipsUpdateState.values()[index];
            }

            @Nullable
            public static ChipsUpdateState valueOf(int code) {
                for (ChipsUpdateState value : ChipsUpdateState.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

        @Keep
        public enum ChipsUpdateError {
            none(0),            // 没有问题，执行升级
            notQualify(1);      // 条件不够，暂无法升级

            private final int code;

            public int getCode() {
                return code;
            }

            ChipsUpdateError(int code) {
                this.code = code;
            }

            @Nullable
            public static ChipsUpdateError findByIndex(int index) {
                final int len = ChipsUpdateError.values().length;
                if (index < 0 || index >= len) {
                    return null;
                }
                return ChipsUpdateError.values()[index];
            }

            @Nullable
            public static ChipsUpdateError valueOf(int code) {
                for (ChipsUpdateError value : ChipsUpdateError.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }

    }

    @Keep
    public static class Reserve {
        @Keep
        public enum ReserveMode {
            priceTracking(1),            // Automatically tracks prices and combines solar and battery conditions to get the most profitable charging and discharging strategy.
            scheduled(2),           // Execute the charging and discharging strategy according to the schedule.
            aiAdapter(3);           // Schedule every day's arragement, but also can custom

            private final int code;

            public int getCode() {
                return code;
            }

            ReserveMode(int code) {
                this.code = code;
            }

            @Nullable
            public static Reserve.ReserveMode findByIndex(int index) {
                final int len = Reserve.ReserveMode.values().length;
                if (index < 0 || index >= len) {
                    return null;
                }
                return Reserve.ReserveMode.values()[index];
            }

            @Nullable
            public static Reserve.ReserveMode valueOf(int code) {
                for (Reserve.ReserveMode value : Reserve.ReserveMode.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }
    }

    @Keep
    public static class Exception {
        @Keep
        public enum IgnoreException {
            BSensorCommunication(0),                     // 三相电表通信故障
            InverterGridEffectL1LackOfVoltage(1),          // Bit3：电网有效值1级欠压（告警类，故障之后，维持输出）
            InverterGridEffectL2LackOfVoltage(2),          // Bit4：电网有效值2级欠压（告警类，故障之后，维持输出）
            InverterGridSecLackOfVoltage(3);               // Bit5：电网瞬时值欠压（告警类，故障之后，维持输出）

            private int code;

            public int getCode() {
                return code;
            }

            IgnoreException(int code) {
                this.code = code;
            }

            @Nullable
            public static Exception.IgnoreException findByIndex(int index) {
                final int len = Exception.IgnoreException.values().length;
                if (index < 0 || index >= len) {
                    return null;
                }
                return Exception.IgnoreException.values()[index];
            }

            @Nullable
            public static Exception.IgnoreException valueOf(int code) {
                for (Exception.IgnoreException value : Exception.IgnoreException.values()) {
                    if (value.code == code) {
                        return value;
                    }
                }
                return null;
            }
        }
    }

    @Keep
    public static class Upgrade {
        @Keep
        public enum UpgradeError {
            BatteryLevelLow(1),
            Other(16);
            private int error;

            UpgradeError(int error) {
                this.error = error;
            }

            public int getError() {
                return error;
            }

            @Nullable
            public static Upgrade.UpgradeError findByIndex(int index) {
                final int len = UpgradeError.values().length;
                if (index < 0 || index >= len) {
                    return null;
                }
                return UpgradeError.values()[index];
            }

            public static Upgrade.UpgradeError valueOf(int code) {
                for (Upgrade.UpgradeError value : Upgrade.UpgradeError.values()) {
                    if (value.error == code) {
                        return value;
                    }
                }
                return null;
            }
        }
    }

    @Keep
    public static class Firmware {
        @Keep
        public enum FirmwareType {
            Invertor(0),
            EV(1),
            Battery(2),
            MPPT(3),
            Cabinet(4),
            MCU(5),
            Sidecar(6);

            private int firmwareType;

            FirmwareType(int firmwareType) {
                this.firmwareType = firmwareType;
            }

            public int getFirmwareType() {
                return firmwareType;
            }
        }
    }

    @Keep
    public static class SolarEfficiency {
        @Keep
        public enum SolarEfficiencyType {
            Unknown(0),
            Low(1),
            Normal(2),
            High(3);
            private int solarEfficiencyType;

            SolarEfficiencyType(int solarEfficiencyType) {
                this.solarEfficiencyType = solarEfficiencyType;
            }

            public int getSolarEfficiencyType() {
                return solarEfficiencyType;
            }

            @Nullable
            public static SolarEfficiency.SolarEfficiencyType findByIndex(int index) {
                final int len = SolarEfficiency.SolarEfficiencyType.values().length;
                if (index < 0 || index >= len) {
                    return Unknown;
                }
                return SolarEfficiency.SolarEfficiencyType.values()[index];
            }
        }
    }

    @Keep
    public static class ThirdPartyPVActiveError {

        @Keep
        public enum ThirdPartyPVActiveErrorType {
            Other(1),
            BSensorNotDetect(2),
            GridUnavailable(3),
            MCUNotSupport(4);

            private int thirdPartyPVActiveErrorType;

            ThirdPartyPVActiveErrorType(int thirdPartyPVActiveErrorType) {
                this.thirdPartyPVActiveErrorType = thirdPartyPVActiveErrorType;
            }

            public int getThirdPartyPVActiveErrorType() {
                return thirdPartyPVActiveErrorType;
            }

            @Nullable
            public static ThirdPartyPVActiveError.ThirdPartyPVActiveErrorType findByIndex(int index) {
                final int len = ThirdPartyPVActiveError.ThirdPartyPVActiveErrorType.values().length;
                if (index < 0 || index >= len) {
                    return Other;
                }
                return ThirdPartyPVActiveError.ThirdPartyPVActiveErrorType.values()[index];
            }
        }
    }

    @Keep
    public static class RegulateFrequencyState {
        @Keep
        public  enum RegulateFrequencyStateType {
            Idle(0),                // 没有调频
            OnHold(1),            // 等待调频过程中
            FcrN(2),        // FCR-N 模式
            FcrDUp(3),        // FCR-DUp 模式
            FcrDDown(4),       // FCR-DDown
            FcrDUpDown(5);       // FCR-DDown

            int regulateFrequencyStateType;
            RegulateFrequencyStateType(int regulateFrequencyStateType) {
                this.regulateFrequencyStateType = regulateFrequencyStateType;
            }

            public int getRegulateFrequencyStateType() {
                return regulateFrequencyStateType;
            }

            @Nullable
            public static RegulateFrequencyState.RegulateFrequencyStateType findByIndex(int index) {
                final int len = RegulateFrequencyState.RegulateFrequencyStateType.values().length;
                if (index < 0 || index >= len) {
                    return Idle;
                }
                return RegulateFrequencyState.RegulateFrequencyStateType.values()[index];
            }
        }
    }

    @Keep
    public static class PVPreference {
        @Keep
        public  enum PVPreferenceType {
            ignore(0),
            followEmaldoAI(1),
            load(2),
            batteryCharge(3),
            ;
            int preference;

            PVPreferenceType(int preference) {
                this.preference = preference;
            }

            public int getPreference() {
                return preference;
            }

            @Nullable
            public static PVPreferenceType findByIndex(int index) {
                final int len = PVPreferenceType.values().length;
                if (index < 0 || index >= len) {
                    return ignore;
                }
                return PVPreferenceType.values()[index];
            }
        }
    }

    @Keep
    public static class SmartEVToggleStatus {
        @Keep
        public enum SmartEVToggleStatusType {
            Off(0),
            Ignore(1),        // 无效值，IoT 不会返回该值
            On(2);

            int status;
            SmartEVToggleStatusType(int status) {
                this.status = status;
            }

            public int getStatus() {
                return status;
            }

            @Nullable
            public static SmartEVToggleStatusType findByIndex(int index) {
                final int len = SmartEVToggleStatusType.values().length;
                if (index < 0 || index >= len) {
                    return On;
                }
                return SmartEVToggleStatusType.values()[index];
            }
        }
    }
}
