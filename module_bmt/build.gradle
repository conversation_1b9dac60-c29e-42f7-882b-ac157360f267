plugins {
    id 'com.android.library'
}

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    buildToolsVersion rootProject.ext.android.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode rootProject.ext.android.versionCode
        versionName rootProject.ext.android.versionName

        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            debuggable rootProject.ext.debuggable
            minifyEnabled rootProject.ext.minifyEnabled
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility rootProject.ext.Java_Version
        targetCompatibility rootProject.ext.Java_Version
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'com.android.support:appcompat-v7:28.0.0'
    implementation 'io.reactivex:rxjava:1.1.6'
    implementation 'io.reactivex:rxandroid:1.2.1'
    if (isAar) {
        api 'com.dinsafer.dinsdk:dincore:' + rootProject.ext.android.versionName + rootProject.ext.dependlibVersionPostfixt
    } else {
        api project(':dincore')
    }
}

ext.modulePublishConfig = [
        artifactId: 'bmt',
]

// 使用maven插件上传
apply from: rootProject.file('gradle-scripts/upload.gradle')

// 使用maven-publish插件上传
//apply from: rootProject.file('gradle-scripts/publish.gradle')

