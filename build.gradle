// Top-level build file where you can add configuration options common to all sub-projects/modules.

apply from: rootProject.file('gradle-scripts/config.gradle')

buildscript {
    ext.kotlin_version = '1.5.32'
    repositories {
        google()
        jcenter()
        mavenCentral()
        // 涂鸦云仓库地址
        maven {
            url "https://maven-other.tuya.com/repository/maven-releases/"
        }
        maven {
            url uri("${rootProject.projectDir}/dinsdk-repository")
        }

    }
    dependencies {
        classpath "com.android.tools.build:gradle:4.1.2"
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'org.greenrobot:greendao-gradle-plugin:3.3.0' // add plugin
    }
}

def mavenConfig = rootProject.ext.mavenConfig ?: [:]

allprojects {
    repositories {
        maven {
            url mavenConfig.remoteDownloadRepoUrl
            credentials {
                username = mavenConfig.user
                password = mavenConfig.password
            }
        }
        google()
        jcenter()
        mavenCentral()
        // 涂鸦云仓库地址
        maven {
            url "https://maven-other.tuya.com/repository/maven-releases/"
        }
        maven {
            url uri("${rootProject.projectDir}/dinsdk-repository")
        }
        maven {
            url "https://maven.aliyun.com/repository/public/"
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
