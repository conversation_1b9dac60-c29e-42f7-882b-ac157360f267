package com.dinsafer.module_heartlai;

import static com.dinsafer.module_heartlai.HeartLaiConstants.PPPP_STATUS_CONNECTING;
import static com.dinsafer.module_heartlai.HeartLaiConstants.PPPP_STATUS_CONNECT_ERRER;
import static com.dinsafer.module_heartlai.HeartLaiConstants.PPPP_STATUS_INITIALING;
import static com.dinsafer.module_heartlai.HeartLaiConstants.PPPP_STATUS_ON_LINE;

import androidx.annotation.Nullable;

import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_heartlai.model.GetDeviceListResponse;
import com.dinsafer.module_heartlai.model.IPCDevice;
import com.google.gson.Gson;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class HeartLaiDevice extends IPCDevice implements Serializable {

    private int PPPPStatus;

    private HeartLaiCmdHandler heartLaiCmdHandler;

    public HeartLaiDevice() {
    }

    public HeartLaiDevice(Builder builder) {
        super(builder);
        heartLaiCmdHandler = new HeartLaiCmdHandler(this);
        putInfoToMap(HeartLaiConstants.ATTR_PROVIDER, HeartLaiConstants.PROVIDER_HEARTLAI);
    }

    @Override
    public void submit(Map arg) {
        if (heartLaiCmdHandler != null)
            heartLaiCmdHandler.handleCmd(arg);
    }

    @Override
    public void destory() {
        super.destory();
        if (heartLaiCmdHandler != null) {
            heartLaiCmdHandler.destory();
            heartLaiCmdHandler = null;
        }
    }

    public void needConnectAgain() {
        setFlagLoaded(false);
        setFlagLoading(false);
    }

    public void connect() {
        Map<String, Object> args = new HashMap<>();
        args.put("cmd", HeartLaiCmd.CMD_CONNECT);
        submit(args);
    }

    public void disconnect() {
        Map<String, Object> args = new HashMap<>();
        args.put("cmd", HeartLaiCmd.CMD_DISCONNECT);
        submit(args);
    }

    public void reconnect() {
        disconnect();
        connect();
    }

    public void startLive() {
        Map<String, Object> args = new HashMap<>();
        args.put("cmd", HeartLaiCmd.CMD_START_LIVE);
        submit(args);
    }

    public void stopLive() {
        Map<String, Object> args = new HashMap<>();
        args.put("cmd", HeartLaiCmd.CMD_STOP_LIVE);
        submit(args);
    }

    public void startListen() {
        Map<String, Object> args = new HashMap<>();
        args.put("cmd", HeartLaiCmd.CMD_START_LISTEN);
        submit(args);
    }

    public void stopListen() {
        Map<String, Object> args = new HashMap<>();
        args.put("cmd", HeartLaiCmd.CMD_STOP_LISTEN);
        submit(args);
    }

    public void startTalk() {
        Map<String, Object> args = new HashMap<>();
        args.put("cmd", HeartLaiCmd.CMD_START_TALK);
        submit(args);
    }

    public void stopTalk() {
        Map<String, Object> args = new HashMap<>();
        args.put("cmd", HeartLaiCmd.CMD_STOP_TALK);
        submit(args);
    }

    public void getCurrentWifi() {
        Map<String, Object> args = new HashMap<>();
        args.put("cmd", HeartLaiCmd.CMD_CURRENT_WIFI);
        submit(args);
    }

    public int getPPPPStatus() {
        return PPPPStatus;
    }

    public void setPPPPStatus(int PPPPStatus) {
        this.PPPPStatus = PPPPStatus;
        if (PPPP_STATUS_CONNECTING == PPPPStatus ||
                PPPP_STATUS_INITIALING == PPPPStatus) {
            setConnectStatus(HeartLaiConstants.CAMERA_STATUS_CONNECTING);
        } else if (PPPP_STATUS_ON_LINE == PPPPStatus) {
            setConnectStatus(HeartLaiConstants.CAMERA_STATUS_ONLINE);
        } else if (PPPP_STATUS_CONNECT_ERRER == PPPPStatus) {
            setConnectStatus(HeartLaiConstants.CAMERA_STATUS_OFFLINE);
        } else {
            setConnectStatus(HeartLaiConstants.CAMERA_STATUS_UNKNOW);
        }
    }

    @Override
    public void setFlagLoaded(boolean isLoaded) {
        super.setFlagLoaded(isLoaded);
    }

    @Override
    public void setFlagLoading(boolean isLoading) {
        super.setFlagLoading(isLoading);
    }

    public void onSearchIpcInfo(@Nullable GetDeviceListResponse.DeviceBean info, final boolean success) {
        MsctLog.i(TAG, getCameraPID() + ": onSearchIpcInfo-heartLai, success: " + success);
        if (!success) {
            setFlagLoading(false);
            setFlagLoaded(false);
            return;
        }

        setFlagLoading(false);
        final String currentId = getCameraPID();
        if (null != info && currentId.equals(info.getPid())) {
            DDLog.i("heartlai", "成功找到心赖IPC的信息");
            setName(info.getName());
            setUid(info.getUser());
            setPwd(info.getPassword());
            setSourceData(new Gson().toJson(info));
            setHdMode(info.getHDmode() == null ? true : info.getHDmode());
            setCategory(info.getDtype() == null ? 2 : info.getDtype());
            setSubCategory(info.getStype());

            heartLaiCmdHandler.realConnect();
        } else {
            setFlagDeleted(true);
        }
        // 这个要放在最后，否则外面会短时间切换为离线状态
        setFlagLoaded(true);
    }

    public static Builder newBuilder() {
        return new HeartLaiDevice.Builder();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        HeartLaiDevice heartLaiDevice = (HeartLaiDevice) o;
        return Objects.equals(getId(), heartLaiDevice.getId())
                && Objects.equals(getCameraPID(), heartLaiDevice.getCameraPID())
                && Objects.equals(getProvider(), heartLaiDevice.getProvider());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId(), getCameraPID(), getProvider());
    }

    public static final class Builder extends IPCDevice.Builder {
        public HeartLaiDevice.Builder snapshot(String snapshot) {
            this.snapshot = snapshot;
            return this;
        }


        public HeartLaiDevice.Builder name(String name) {
            this.name = name;
            return this;
        }


        public HeartLaiDevice.Builder description(String description) {
            this.description = description;
            return this;
        }

        public HeartLaiDevice.Builder sourceData(String sourceData) {
            this.sourceData = sourceData;
            return this;
        }

        public HeartLaiDevice.Builder id(String id) {
            this.id = id;
            return this;
        }

        public HeartLaiDevice.Builder password(String password) {
            this.password = password;
            return this;
        }

        public HeartLaiDevice.Builder cameraPID(String cameraPID) {
            this.cameraPID = cameraPID;
            return this;
        }

        public HeartLaiDevice.Builder uid(String uid) {
            this.uid = uid;
            return this;
        }

        public HeartLaiDevice.Builder setConnectStatus(int connectStatus) {
            this.connectStatus = connectStatus;
            return this;
        }

        public HeartLaiDevice.Builder setIpc_type(String ipc_type) {
            this.ipc_type = ipc_type;
            return this;
        }

        public HeartLaiDevice.Builder time(long time) {
            this.time = time;
            return this;
        }

        public HeartLaiDevice.Builder home_id(String home_id) {
            this.home_id = home_id;
            return this;
        }

        @Override
        public HeartLaiDevice.Builder fromCache(boolean fromCache) {
            this.fromCache = fromCache;
            return this;
        }

        public HeartLaiDevice build() {
            return new HeartLaiDevice(this);
        }
    }
}
