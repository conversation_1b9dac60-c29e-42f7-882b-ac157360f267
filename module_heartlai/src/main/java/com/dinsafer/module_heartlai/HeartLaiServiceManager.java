package com.dinsafer.module_heartlai;

import android.app.Application;
import android.content.Intent;

import com.dinsafer.dssupport.utils.DDLog;
import com.heartlai.ipc.BridgeService;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.Vector;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import androidx.annotation.Keep;

@Keep
public class HeartLaiServiceManager {

    private static final String TAG = HeartLaiServiceManager.class.getSimpleName();

    private volatile static HeartLaiServiceManager instance;

    private final ArrayList<BridgeService.IPPlayInterface> mPlayCallbacks = new ArrayList<>();
    private final Vector<BridgeService.IpcamClientInterface> ipcamClientInterfaces = new Vector<>();
    private final Vector<BridgeService.SHIXCOMMONInterface> shixcommonInterfaces = new Vector<>();

    private ExecutorService executorService = Executors.newCachedThreadPool();

    private static Application app;

    private volatile boolean isInit = false;

    public static void init(Application application) {
        app = application;
    }

    public static HeartLaiServiceManager getInstance() {
        if (instance == null) {
            synchronized (HeartLaiServiceManager.class) {
                if (instance == null) {
                    instance = new HeartLaiServiceManager();
                }
            }
        }
        return instance;
    }

    private HeartLaiServiceManager() {
    }

    public void initIpc() {
        synchronized (this) {
            DDLog.i(TAG, "initIpc");
            HeartLaiJniClientProxy.initIPC();
            executorService.submit(new Runnable() {
                @Override
                public void run() {
                    startService();
                }
            });
            //        ipc消息接收，包括连接状态，设备信息
            BridgeService.addIpcamClientInterface(ipcamClientInterface);
            BridgeService.addSHIXCOMMONInterface(shixcommonInterface);
            BridgeService.setIpPlayInterface(ipPlayInterface);
            BridgeService.addPlayInterface(audioCallback);
            isInit = true;
        }
    }

    private static void startService() {
        DDLog.i(TAG, "startService");
        Intent intent = new Intent(app, com.heartlai.ipc.BridgeService.class);
        app.startService(intent);
    }

    public void clear() {
        synchronized (this) {
            DDLog.d(TAG, "clear: ");
            HeartLaiJniClientProxy.stopAll();
            ipcamClientInterfaces.clear();
            shixcommonInterfaces.clear();
            mPlayCallbacks.clear();
            isInit = false;
        }
    }

    public void release() {
        synchronized (this) {
            BridgeService.removeIpcClientInterface(ipcamClientInterface);
            app.stopService(new Intent(app, BridgeService.class));
            isInit = false;
        }
    }


    public void addIpcamClientInterface(BridgeService.IpcamClientInterface ipcamClientInterface) {
        if (!isInit) {
            initIpc();
        }
        if (ipcamClientInterface == null || ipcamClientInterfaces.contains(ipcamClientInterface)) {
            return;
        }
        synchronized (ipcamClientInterfaces) {
            ipcamClientInterfaces.add(ipcamClientInterface);
        }
    }

    public void removeIpcamClientInterface(BridgeService.IpcamClientInterface ipcamClientInterface) {
        if (!isInit) {
            initIpc();
        }
        if (ipcamClientInterface == null || !ipcamClientInterfaces.contains(ipcamClientInterface)) {
            return;
        }
        synchronized (ipcamClientInterfaces) {
            Iterator<BridgeService.IpcamClientInterface> iterator = ipcamClientInterfaces.iterator();
            while (iterator.hasNext()) {
                BridgeService.IpcamClientInterface next = iterator.next();
                if (next.equals(ipcamClientInterface)) {
                    iterator.remove();
                }
            }
        }
    }

    public void addSHIXCOMMONInterface(BridgeService.SHIXCOMMONInterface shixcommonInterface) {
        if (!isInit) {
            initIpc();
        }
        if (shixcommonInterface == null || shixcommonInterfaces.contains(shixcommonInterface)) {
            return;
        }
        synchronized (shixcommonInterfaces) {
            shixcommonInterfaces.add(shixcommonInterface);
        }
    }

    public void removeSHIXCOMMONInterface(BridgeService.SHIXCOMMONInterface shixcommonInterface) {
        if (!isInit) {
            initIpc();
        }
        if (shixcommonInterface == null || !shixcommonInterfaces.contains(shixcommonInterface)) {
            return;
        }
        synchronized (shixcommonInterfaces) {
            Iterator<BridgeService.SHIXCOMMONInterface> iterator = shixcommonInterfaces.iterator();
            while (iterator.hasNext()) {
                BridgeService.SHIXCOMMONInterface next = iterator.next();
                if (next.equals(shixcommonInterface)) {
                    iterator.remove();
                }
            }
        }
    }

    public void removePlayInterface(BridgeService.IPPlayInterface callback) {
        if (!isInit) {
            initIpc();
        }
        synchronized (mPlayCallbacks) {
            mPlayCallbacks.remove(callback);
        }
    }

    public void addPlayInterface(BridgeService.IPPlayInterface callback) {
        if (!isInit) {
            initIpc();
        }
        synchronized (mPlayCallbacks) {
            mPlayCallbacks.add(callback);
        }
    }

    private BridgeService.IpcamClientInterface ipcamClientInterface = new BridgeService.IpcamClientInterface() {

        @Override
        public void BSMsgNotifyData(String cameraPid, int msgType, int param) {
            synchronized (ipcamClientInterfaces) {
                for (BridgeService.IpcamClientInterface interf : ipcamClientInterfaces) {
                    interf.BSMsgNotifyData(cameraPid, msgType, param);
                }
            }

        }

        @Override
        public void BSSnapshotNotify(String s, byte[] bytes, int i) {
            synchronized (ipcamClientInterfaces) {
                for (BridgeService.IpcamClientInterface interf : ipcamClientInterfaces) {
                    interf.BSSnapshotNotify(s, bytes, i);
                }
            }
        }

        @Override
        public void callBackUserParams(String s, String s1, String s2, String s3, String s4, String s5, String s6) {
            synchronized (ipcamClientInterfaces) {
                for (BridgeService.IpcamClientInterface interf : ipcamClientInterfaces) {
                    interf.callBackUserParams(s, s1, s2, s3, s4, s5, s6);
                }
            }
        }

        @Override
        public void callBackConnectLook(String s, int i, int i1) {
            synchronized (ipcamClientInterfaces) {
                for (BridgeService.IpcamClientInterface interf : ipcamClientInterfaces) {
                    interf.callBackConnectLook(s, i, i1);
                }
            }
        }
    };

    private BridgeService.SHIXCOMMONInterface shixcommonInterface = new BridgeService.SHIXCOMMONInterface() {

        @Override
        public synchronized void CallBackSHIXJasonCommon(String cameraPid, String s1) {
            synchronized (shixcommonInterfaces) {
                for (BridgeService.SHIXCOMMONInterface interf : shixcommonInterfaces) {
                    interf.CallBackSHIXJasonCommon(cameraPid, s1);
                }
            }
        }

    };

    private BridgeService.IPPlayInterface ipPlayInterface = new BridgeService.IPPlayInterface() {

        @Override
        public void callBackAudioData(byte[] pcm, int len) {
        }

        @Override
        public void callBackH264Data(byte[] bytes, int i, int i1) {
            synchronized (mPlayCallbacks) {
                for (int a = 0; a < mPlayCallbacks.size(); a++) {
                    mPlayCallbacks.get(a).callBackH264Data(bytes, i, i1);
                }
            }
        }


        @Override
        public void callBackCameraParamNotify(String did, int resolution, int brightness, int contrast, int hue, int saturation, int flip) {
            synchronized (mPlayCallbacks) {
                for (int a = 0; a < mPlayCallbacks.size(); a++) {
                    mPlayCallbacks.get(a).callBackCameraParamNotify(did, resolution, brightness, contrast, hue, saturation, flip);
                }
            }
        }

        @Override
        public void callBaceVideoData(String did, byte[] videobuf, int h264Data, int len, int width, int height) {
            synchronized (mPlayCallbacks) {
//                DDLog.i(TAG, "callbackvideoData: did: " + did);
                for (int a = 0; a < mPlayCallbacks.size(); a++) {
                    mPlayCallbacks.get(a).callBaceVideoData(did, videobuf, h264Data, len, width, height);
                }
            }
        }

        @Override
        public void callBackMessageNotify(String s, int i, int i1) {
            synchronized (mPlayCallbacks) {
                for (int a = 0; a < mPlayCallbacks.size(); a++) {
                    mPlayCallbacks.get(a).callBackMessageNotify(s, i, i1);
                }
            }
        }
    };

    private BridgeService.PlayInterface audioCallback = new BridgeService.PlayInterface() {

        @Override
        public void callBackAudioData(byte[] pcm, int len) {
            synchronized (mPlayCallbacks) {
                for (int i = 0; i < mPlayCallbacks.size(); i++) {
                    mPlayCallbacks.get(i).callBackAudioData(pcm, len);
                }
            }
        }
    };
}
