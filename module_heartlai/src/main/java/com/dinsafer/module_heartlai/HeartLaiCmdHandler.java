package com.dinsafer.module_heartlai;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.dinsafer.dincore.common.Cmd;
import com.dinsafer.dincore.common.DeivceChangeEvent;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.http.NetWorkException;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_heartlai.http.HeartLaiRepository;
import com.dinsafer.module_heartlai.model.GetAlertModeResponse;
import com.dinsafer.module_heartlai.model.GetDeviceListResponse;
import com.dinsafer.module_heartlai.model.SearchIpcParams;
import com.dinsafer.module_heartlai.model.SearchIpcResponse;
import com.dinsafer.module_heartlai.util.SearchIpcHelper;
import com.google.gson.Gson;
import com.heartlai.ipc.BridgeService;
import com.heartlai.ipc.utils.CommonUtil;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Calendar;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.ConcurrentHashMap;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class HeartLaiCmdHandler {
    private String TAG = getClass().getSimpleName();
    private HeartLaiDevice device;
    private HeartLaiRepository heartLaiRepository;
    private static Set<String> mNetworkErrorCameraIds = new HashSet<>();
    private Handler handler;
    private ConcurrentHashMap<String, Runnable> connectTimeoutRunableMap;

    public HeartLaiCmdHandler(HeartLaiDevice device) {
        this.device = device;
        Log.d(TAG, "HeartLaiCmdHandler-->create: " + device.getCameraPID());
        heartLaiRepository = new HeartLaiRepository();
        HeartLaiServiceManager.getInstance().addIpcamClientInterface(ipcamClientInterface);
        HeartLaiServiceManager.getInstance().addSHIXCOMMONInterface(shixcommonInterface);
        handler = new Handler(Looper.getMainLooper());
        connectTimeoutRunableMap = new ConcurrentHashMap<>();
    }

    public void destory() {
        Log.d(TAG, "HeartLaiCmdHandler-->destory: " + device.getCameraPID());
        HeartLaiServiceManager.getInstance().removeIpcamClientInterface(ipcamClientInterface);
        HeartLaiServiceManager.getInstance().removeSHIXCOMMONInterface(shixcommonInterface);
        ipcamClientInterface = null;
        shixcommonInterface = null;
        connectTimeoutRunableMap.clear();
        handler.removeCallbacksAndMessages(null);
    }

    /**
     * 用于获取从缓存创建Device中缓存外的数据
     * 这个一定要请求，不能缓存，因为如果获取不到数据，就表示IPC已经被删除了
     */
    private void requestIpcInfoAndConnect() {
        DDLog.d("heartlai", "requestIpcInfoAndConnect");
        final SearchIpcParams params = new SearchIpcParams(device.getCameraPID(), device.getProvider());
        device.setFlagLoading(true);
        heartLaiRepository.searchIpc(device.getHome_id(), Collections.singletonList(params), new Callback<SearchIpcResponse>() {
            @Override
            public void onResponse(Call<SearchIpcResponse> call, Response<SearchIpcResponse> response) {
                if (response.isSuccessful() && null != response.body() && null != response.body().getResult()) {
                    SearchIpcResponse.ResultBean result = response.body().getResult();
                    List<GetDeviceListResponse.DeviceBean> list = result.getList();
                    boolean found = false;
                    if (null != list && list.size() > 0) {
                        final String myPid = device.getCameraPID();
                        if (!TextUtils.isEmpty(myPid)) {
                            for (int i = 0; i < list.size(); i++) {
                                GetDeviceListResponse.DeviceBean deviceBean = list.get(i);
                                final String pid = deviceBean.getPid();
                                if (myPid.equals(pid)) {
                                    DDLog.i("heartlai", "成功找到心赖IPC的信息");
                                    device.setName(deviceBean.getName());
                                    device.setUid(deviceBean.getUser());
                                    device.setPwd(deviceBean.getPassword());
                                    device.setSourceData(new Gson().toJson(deviceBean));
                                    device.setHdMode(deviceBean.getHDmode());
                                    device.setCategory(deviceBean.getDtype());
                                    device.setSubCategory(deviceBean.getStype());
                                    found = true;
                                    break;
                                }
                            }
                        }
                    }
                    device.setFlagLoaded(true);
                    device.setFlagDeleted(!found);

                    if (found) {
                        mNetworkErrorCameraIds.remove(device.getCameraPID());
                        HeartLaiJniClientProxy.startP2p(device.getCameraPID(),
                                device.getUid(),
                                device.getPwd(), "heartlai");
                    }
                } else {
                    device.setFlagLoading(false);
                    device.setFlagLoaded(false);
                }
            }

            @Override
            public void onFailure(Call<SearchIpcResponse> call, Throwable t) {
                t.printStackTrace();
                device.setFlagLoading(false);
                device.setFlagLoaded(false);
            }
        });
    }

    public void realConnect() {
        if (null == device) {
            return;
        }
        MsctLog.e(TAG, "realConnect heartLai id:" + device.getId());
        mNetworkErrorCameraIds.remove(device.getCameraPID());
        device.setConnectStatus(HeartLaiConstants.CAMERA_STATUS_CONNECTING);
        HeartLaiJniClientProxy.startP2p(device.getCameraPID(),
                device.getUid(),
                device.getPwd(), "heartlai");
    }


    public void handleCmd(Map arg) {
        if (device == null || arg == null || arg.size() == 0) {
            return;
        }
        String cmd = (String) arg.get("cmd");
        DDLog.d("heartlai", "handleCmd: " + cmd);
        // 还没有Device的详细数据，需要请求
        if (!device.getFlagLoaded()) {
            if (HeartLaiCmd.CMD_CONNECT.equals(cmd)) {
                final boolean discardCache = DeviceHelper.getBoolean(arg, "discardCache", false);
                if (discardCache) {
                    MsctLog.i(TAG, device.getId() + ": connect- discardCache = true");
                    device.setFlagLoaded(false);
                    device.setFlagLoading(true);
                    SearchIpcHelper.get().startSearchIpcSingle(device.getHome_id(), device);
                    return;
                }

                if (device.getFlagLoading()) {
                    MsctLog.i(TAG, device.getId() + ": connect- loading flag is true");
                    return;
                }

                if (device.getFlagLoaded()) {
                    realConnect();
                    return;
                }

                device.setFlagLoading(true);
                SearchIpcHelper.get().addTask(device.getHome_id(), device);
                // requestIpcInfoAndConnect();
                return;
            } else {
                DDLog.e("heartlai", "这是根据缓存创建的Device，还没有获取其他信息，不能进行连接外的其他操作");
                Map<String, Object> result = new HashMap<>();
                result.put("cmd", cmd);
                result.put("status", 0);
                device.dispatchResult(cmd, result);
            }
            return;
        }

        switch (cmd) {
            case HeartLaiCmd.CMD_CONNECT:
                final boolean discardCache = DeviceHelper.getBoolean(arg, "discardCache", false);
                if (discardCache) {
                    MsctLog.i(TAG, device.getId() + ": connect- discardCache = true");
                    device.setFlagLoaded(false);
                    device.setFlagLoading(true);
                    SearchIpcHelper.get().startSearchIpcSingle(device.getHome_id(), device);
                    return;
                }

                realConnect();
                break;
            case HeartLaiCmd.CMD_SCAN_WIFI:
                HeartLaiJniClientProxy.transferMessage(device.getCameraPID(),
                        CommonUtil.scanWifi(device.getUid(), device.getPwd()), 0);
                break;
            case HeartLaiCmd.CMD_SET_WIFI:
                HeartLaiJniClientProxy.transferMessage(device.getCameraPID(),
                        CommonUtil.SHIX_SetWifi(device.getUid(),
                                device.getPwd(), ((String) arg.get("wifiName")),
                                ((String) arg.get("password")), ((Integer) arg.get("encryption"))), 0);
                break;
            case HeartLaiCmd.CMD_CURRENT_WIFI:
                HeartLaiJniClientProxy.transferMessage(device.getCameraPID(),
                        CommonUtil.getWifiParms(device.getUid(), device.getPwd()), 0);
                break;

            case HeartLaiCmd.CMD_START_LIVE:
//                根据接口数据库的清晰度来播放，0：高清，1：非高清
                int type = device.isHdMode() ? 0 : 1;
                HeartLaiJniClientProxy.startPPPPLiveStream(device.getCameraPID(), type, type);
                break;
            case HeartLaiCmd.CMD_STOP_LIVE:
                HeartLaiJniClientProxy.stopPPPPLiveStream(device.getCameraPID());  // 停止 实时播放
                break;

            case HeartLaiCmd.CMD_START_LISTEN:
                HeartLaiJniClientProxy.pPPPStartAudio(device.getCameraPID());
                break;
            case HeartLaiCmd.CMD_STOP_LISTEN:
                HeartLaiJniClientProxy.pPPPStopAudio(device.getCameraPID());
                break;

            case HeartLaiCmd.CMD_START_TALK:
                HeartLaiJniClientProxy.transferMessage(device.getCameraPID(),
                        CommonUtil.SHIX_StartTalk(device.getUid(), device.getPwd()), 0);
                HeartLaiJniClientProxy.pPPPStartTalk(device.getCameraPID());
                break;
            case HeartLaiCmd.CMD_STOP_TALK:
                HeartLaiJniClientProxy.transferMessage(device.getCameraPID(),
                        CommonUtil.SHIX_StopTalk(device.getUid(), device.getPwd()), 0);
                HeartLaiJniClientProxy.pPPPStopTalk(device.getCameraPID());
                break;

            case HeartLaiCmd.CMD_SNAPSHOT:
                HeartLaiJniClientProxy.takePic(device.getCameraPID());
                break;

            case HeartLaiCmd.CMD_GET_SETTING:
                if (!device.isConnected()) {
                    return;
                }
                HeartLaiJniClientProxy.transferMessage(device.getCameraPID(),
                        CommonUtil.SHIX_getCameraParms(device.getUid(), device.getPwd()), 0);
                break;

            case HeartLaiCmd.CMD_CHANGE_PASSWORD:
                HeartLaiJniClientProxy.transferMessage(device.getCameraPID(),
                        CommonUtil.editUsersParms(device.getUid(), device.getPwd(), device.getUid(),
                                device.getUid(), (String) arg.get("password")), 0);
                break;

            case HeartLaiCmd.CMD_FORMAT_SD:
                HeartLaiJniClientProxy.transferMessage(device.getCameraPID(),
                        CommonUtil.formatSDParms(device.getUid(), device.getPwd()), 0);
                break;

            case HeartLaiCmd.CMD_GET_SD_INFO:
                HeartLaiJniClientProxy.transferMessage(device.getCameraPID(), CommonUtil.getSDParms(device.getUid(), device.getPwd()), 0);
                break;

            case HeartLaiCmd.CMD_MIRROR:
                boolean horOn = (boolean) arg.get("mirror");
                boolean verOn = (boolean) arg.get("flip");
                if (horOn && verOn) {
                    HeartLaiJniClientProxy.transferMessage(device.getCameraPID(),
                            CommonUtil.SHIX_Control_FlipMirror(device.getUid(), device.getPwd()), 0);
//            device.setMirror_mode(3);
                } else if (horOn && !verOn) {
                    HeartLaiJniClientProxy.transferMessage(device.getCameraPID(),
                            CommonUtil.SHIX_Control_Mirror(device.getUid(), device.getPwd()), 0);
//            device.setMirror_mode(1);
                } else if (!horOn && verOn) {
                    HeartLaiJniClientProxy.transferMessage(device.getCameraPID(),
                            CommonUtil.SHIX_Control_Flip(device.getUid(), device.getPwd()), 0);
//            device.setMirror_mode(2);
                } else {
//            device.setMirror_mode(0);
                    HeartLaiJniClientProxy.transferMessage(device.getCameraPID(),
                            CommonUtil.SHIX_Control_CanFlipMirror(device.getUid(), device.getPwd()), 0);
                }
                break;

            case HeartLaiCmd.CMD_SYNC_TIMEZONE:
                Calendar calendar = Calendar.getInstance();
                int now = (int) (calendar.getTimeInMillis() / 1000);
                TimeZone timeZone = TimeZone.getDefault();
                DDLog.e("timezone", "setDate: " + timeZone.getDisplayName() + "   " + timeZone.getRawOffset());
                int dstOffset = calendar.get(Calendar.DST_OFFSET);
                int tz = -(timeZone.getRawOffset() + dstOffset);
                HeartLaiJniClientProxy.transferMessage(device.getCameraPID(),
                        CommonUtil.SHIX_SetDateParms(device.getUid(),
                                device.getPwd(), tz / (60 * 1000), now, CommonUtil.DATE_SETTING_MODE), 0);
                break;

            case HeartLaiCmd.CMD_GET_RECORD_CONFIG:
                HeartLaiJniClientProxy.transferMessage(device.getCameraPID(),
                        CommonUtil.SHIX_GetVideoRecordParms(device.getUid(),
                                device.getPwd()), 0);
                break;

            case HeartLaiCmd.CMD_CONFIG_RECORD:
                arg.put("cmd", 122);
                JSONObject jsonObject = new JSONObject(arg);
                HeartLaiJniClientProxy.transferMessage(device.getCameraPID(), jsonObject.toString(), 0);
                break;

            case HeartLaiCmd.CMD_GET_ALERT_INFO:
                HeartLaiJniClientProxy.transferMessage(device.getCameraPID(),
                        CommonUtil.getAlarmParms(device.getUid(),
                                device.getPwd(), CommonUtil.MOTION_DETECT), 0);
                break;

            case HeartLaiCmd.CMD_SET_ALERT_INFO:
                arg.put("cmd", "108");
                String savedInfo = (String) arg.get("savedInfo");
                HeartLaiJniClientProxy.transferMessage(device.getCameraPID(), savedInfo, 0);
                break;

            case HeartLaiCmd.CMD_CHANGE_HDMODE:
                boolean hdMode = (boolean) arg.get("hdMode");
                device.setHdMode(hdMode);
                heartLaiRepository.updateHDMode(device.getCameraPID(), device.getHome_id(), hdMode, new Callback<StringResponseEntry>() {
                    final Map<String, Object> result = new HashMap<>();

                    @Override
                    public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                        result.put("cmd", HeartLaiCmd.CMD_CHANGE_HDMODE);
                        if (null != response.body()
                                && response.body().getStatus() == 1) {
                            result.put("status", 1);
                        } else {
                            result.put("status", 0);
                        }
                        device.dispatchResult(HeartLaiCmd.CMD_CHANGE_HDMODE, result);
                    }

                    @Override
                    public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                        result.put("cmd", HeartLaiCmd.CMD_CHANGE_HDMODE);
                        result.put("status", 0);
                        device.dispatchResult(HeartLaiCmd.CMD_CHANGE_HDMODE, result);
                    }
                });
                break;

            case HeartLaiCmd.CMD_ADD:
                String name = (String) arg.get("name");
                String sourceData = (String) arg.get("sourceData");
                JSONObject data = null;
                try {
                    data = new JSONObject(sourceData);
                    data.put("name", name);
                    data.put("home_id", HeartLaiService.currentHomeId);
                } catch (JSONException e) {
                    e.printStackTrace();
                }

                heartLaiRepository.getAddTpIpcCall(data, new Callback<com.dinsafer.dincore.http.StringResponseEntry>() {
                    Map<String, Object> result = new HashMap<>();

                    @Override
                    public void onResponse(Call<com.dinsafer.dincore.http.StringResponseEntry> call, Response<com.dinsafer.dincore.http.StringResponseEntry> response) {
                        com.dinsafer.dincore.http.StringResponseEntry responseEntry = response.body();
                        if (responseEntry.getStatus() == 1) {
                            result.put("cmd", HeartLaiCmd.CMD_ADD);
                            result.put("status", 1);
                            device.dispatchResult(HeartLaiCmd.CMD_ADD, result);
                        } else {
                            result.put("cmd", HeartLaiCmd.CMD_ADD);
                            result.put("status", 0);
                            device.dispatchResult(HeartLaiCmd.CMD_ADD, result);
                        }
                    }

                    @Override
                    public void onFailure(Call<com.dinsafer.dincore.http.StringResponseEntry> call, Throwable t) {
                        result.put("cmd", HeartLaiCmd.CMD_ADD);
                        result.put("status", 0);
                        device.dispatchResult(HeartLaiCmd.CMD_ADD, result);
                    }
                });
                break;

            case HeartLaiCmd.CMD_DEL:
                if (device.getFlagDeleted()) {
                    MsctLog.i(TAG, "ipc之前已经被删除");
                    Map<String, Object> result = new HashMap<>();
                    result.put("status", 1);
                    device.dispatchResult(HeartLaiCmd.CMD_DEL, result);

                    DeivceChangeEvent deivceChangeEvent = new DeivceChangeEvent(device);
                    deivceChangeEvent.setAdd(false);
                    deivceChangeEvent.setRemove(true);
                    EventBus.getDefault().post(deivceChangeEvent);
                    break;
                }

                heartLaiRepository.delDevice(device.getCameraPID(), (String) arg.get("home_id"), new Callback<StringResponseEntry>() {

                    Map<String, Object> result = new HashMap<>();

                    @Override
                    public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                        StringResponseEntry body = response.body();
                        result.put("status", body.getStatus());
                        device.dispatchResult(HeartLaiCmd.CMD_DEL, result);

                        DeivceChangeEvent deivceChangeEvent = new DeivceChangeEvent(device);
                        deivceChangeEvent.setAdd(false);
                        deivceChangeEvent.setRemove(true);
                        EventBus.getDefault().post(deivceChangeEvent);
                    }

                    @Override
                    public void onFailure(Call<StringResponseEntry> call, Throwable t) {

                        result.put("status", 0);
                        if (t instanceof NetWorkException) {
                            result.put("errorMessage", ((NetWorkException) t).getMsgDes());
                            Cmd.putResultValue(result, "originStatus", ((NetWorkException) t).getStatus());
                        } else {
                            result.put("errorMessage", "mes:" + t.getMessage());
                        }
                        device.dispatchResult(HeartLaiCmd.CMD_DEL, result);
                    }
                });
                break;

            case HeartLaiCmd.CMD_CHANGE_NAME:
                heartLaiRepository.renameDevice(device.getCameraPID(), (String) arg.get("home_id"), (String) arg.get("name"), new Callback<StringResponseEntry>() {

                    Map<String, Object> result = new HashMap<>();

                    @Override
                    public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                        StringResponseEntry body = response.body();
                        result.put("status", body.getStatus());
                        device.dispatchResult(HeartLaiCmd.CMD_CHANGE_NAME, result);
                    }

                    @Override
                    public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                        result.put("status", 0);
                        device.dispatchResult(HeartLaiCmd.CMD_CHANGE_NAME, result);
                    }
                });
                break;

            case HeartLaiCmd.CMD_SAVE_PASSWORD_TO_SERVER:
                heartLaiRepository.updateServerPassword(device.getCameraPID(), (String) arg.get("home_id"), (String) arg.get("password"), new Callback<StringResponseEntry>() {

                    Map<String, Object> result = new HashMap<>();

                    @Override
                    public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                        StringResponseEntry body = response.body();
                        result.put("cmd", HeartLaiCmd.CMD_SAVE_PASSWORD_TO_SERVER);
                        result.put("status", body.getStatus());
                        device.dispatchResult(HeartLaiCmd.CMD_SAVE_PASSWORD_TO_SERVER, result);
                    }

                    @Override
                    public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                        result.put("cmd", HeartLaiCmd.CMD_SAVE_PASSWORD_TO_SERVER);
                        result.put("status", 0);
                        device.dispatchResult(HeartLaiCmd.CMD_SAVE_PASSWORD_TO_SERVER, result);
                    }
                });
                break;
            case HeartLaiCmd.CMD_DISCONNECT:
                HeartLaiJniClientProxy.disconnect(device.getCameraPID());
                break;

            case HeartLaiCmd.CMD_CONTROL_DIRECTION:
//            {
//                "cmd": "operate_camera_control_direction"
//                "direction": [必填, int] - 0: 向上，1: 向下，2: 向左，3: 向右
//                "eventID": [选填, String] - 用户自定义字段，表明是哪个操作事件
//            }
                int direction = DeviceHelper.getInt(arg, "direction", -1);
                if (direction == 0) {
                    HeartLaiJniClientProxy.transferMessage(device.getCameraPID(),
                            CommonUtil.SHIX_Control_Up(device.getUid(),
                                    device.getPwd()), 0);
                } else if (direction == 1) {
                    HeartLaiJniClientProxy.transferMessage(device.getCameraPID(),
                            CommonUtil.SHIX_Control_Down(device.getUid(),
                                    device.getPwd()), 0);
                } else if (direction == 2) {
                    HeartLaiJniClientProxy.transferMessage(device.getCameraPID(),
                            CommonUtil.SHIX_Control_Left(device.getUid(),
                                    device.getPwd()), 0);
                } else if (direction == 3) {
                    HeartLaiJniClientProxy.transferMessage(device.getCameraPID(),
                            CommonUtil.SHIX_Control_Rigth(device.getUid(),
                                    device.getPwd()), 0);
                }

                break;

            case HeartLaiCmd.CMD_STOP_DIRECTION:
                HeartLaiJniClientProxy.transferMessage(device.getCameraPID(),
                        CommonUtil.SHIX_Control_Stop(device.getUid(), device.getPwd()), 0);
                break;

            case HeartLaiCmd.CMD_GET_FILE_PARMS:
                HeartLaiJniClientProxy.transferMessage(device.getCameraPID(), CommonUtil.SHIX_Record_day(device.getUid(), device.getPwd(), 0), 0);
                break;

            case HeartLaiCmd.CMD_GET_VIDEO:
                HeartLaiJniClientProxy.transferMessage(device.getCameraPID(), CommonUtil.SHIX_GetVideoFiles(device.getUid(), device.getPwd(), ((String) arg.get("dateStringStart")), ((String) arg.get("dateStringEnd"))), 0);
                break;

            case HeartLaiCmd.CMD_START_VIDEO:
                HeartLaiJniClientProxy.transferMessage(device.getCameraPID(), CommonUtil.SHIX_StartVideoFiles(device.getUid(), device.getPwd(), (String) arg.get("startTime")), 0);
                break;

            case HeartLaiCmd.CMD_STOP_VIDEO:
                HeartLaiJniClientProxy.transferMessage(device.getCameraPID(), CommonUtil.SHIX_StopVideoFiles(device.getUid(), device.getPwd()), 0);
                break;

            case HeartLaiCmd.CMD_START_PLAY_BACK:
                HeartLaiJniClientProxy.startPlayBack(device.getCameraPID(), (String) arg.get("strFilePath"), (Integer) arg.get("var2"));
                break;

            case HeartLaiCmd.CMD_STOP_PLAY_BACK:
                HeartLaiJniClientProxy.stopPlayBack(device.getCameraPID());
                break;

            case HeartLaiCmd.CMD_SET_VIDEO_OFFSET:
                HeartLaiJniClientProxy.transferMessage(device.getCameraPID(), CommonUtil.SHIX_StartVideoOffset(device.getUid(), device.getPwd(), (String) arg.get("BarMoveFinishCurrentTime")), 0);
                break;

            case HeartLaiCmd.CMD_SET_ALERT_MODE:
                setAlertMode(arg);
                break;

            case HeartLaiCmd.CMD_GET_ALERT_MODE:
                getAlertMode(arg);
                break;
            default:
                break;

        }

    }

    private BridgeService.IpcamClientInterface ipcamClientInterface = new BridgeService.IpcamClientInterface() {

        @Override
        public void BSMsgNotifyData(String cameraPid, int msgType, int param) {
            DDLog.i("heartlai", "HeartLaiCmdHandler-->BSMsgNotifyData cameraPid: " + cameraPid + ", msgType: " + msgType + ", PPPPStatus: " + param + " /object:" + ipcamClientInterface.hashCode());
            if (device == null || !cameraPid.equals(device.getCameraPID())) {
                return;
            }

            if (HeartLaiConstants.PPPP_STATUS_CONNECTING == param && device.getPPPPStatus() == param && mNetworkErrorCameraIds.contains(cameraPid)) {
                DDLog.i("heartlai", "HeartLaiCmdHandler-->BSMsgNotifyData cameraPid: " + cameraPid + "  状态是连接中且当前已经是连接中的状态，不需要更新");
                return;
            }

            if (param == HeartLaiConstants.PPPP_STATUS_CONNECT_ERRER) {
                mNetworkErrorCameraIds.add(cameraPid);
            }

            device.setPPPPStatus(param);

            if (connectTimeoutRunableMap.containsKey(cameraPid) && connectTimeoutRunableMap.get(cameraPid) != null) {
                handler.removeCallbacks(connectTimeoutRunableMap.get(cameraPid));
                connectTimeoutRunableMap.remove(cameraPid);
                DDLog.i("heartlai", "HeartLaiCmdHandler-->BSMsgNotifyData cameraPid:" + device.getCameraPID() + " 删除连接超时任务。");
            }

            if (param != HeartLaiConstants.PPPP_STATUS_ON_LINE && param != HeartLaiConstants.PPPP_STATUS_CONNECTING) {
                DDLog.i("heartlai", "HeartLaiCmdHandler-->BSMsgNotifyData cameraPid: " + cameraPid + "  如果状态是不在线的，则必需disconnect，否则无法正常使用");
                device.disconnect();
            }

            if (param == HeartLaiConstants.PPPP_STATUS_CONNECT_ERRER) {
                DDLog.i("heartlai", "HeartLaiCmdHandler-->BSMsgNotifyData cameraPid: " + cameraPid + " 用户名或者密码错误");
                if (device.getPwd().equals(HeartLaiConstants.DEFAULT_HEARTLAI_PWD)) {
                    DDLog.i("heartlai", "HeartLaiCmdHandler-->BSMsgNotifyData cameraPid: " + cameraPid + " 密码为默认密码，需要重新设置密码");
                    device.setConnectStatus(HeartLaiConstants.CAMERA_STATUS_WRONG_PASSWORD);
                    device.disconnect();
                } else {
                    //                                    密码不相同，使用默认密码重连，并且设置camera的密码为默认密码
//                                    如果重连还不成功，证明密码错误，走上面if流程
                    DDLog.i("heartlai", "HeartLaiCmdHandler-->BSMsgNotifyData cameraPid: " + cameraPid + " 密码不是默认密码，使用默认密码重连，并且设置camera的密码为默认密码");
                    device.setPwd(HeartLaiConstants.DEFAULT_HEARTLAI_PWD);
                    device.setPPPPStatus(HeartLaiConstants.PPPP_STATUS_CONNECTING);
                    device.connect();
                }
            }

            if (device.isConnected()) {
                try {
                    JSONObject jsonObject = new JSONObject(device.getSourceData());
                    if (!DDJSONUtil.getString(jsonObject, "password").equals(device.getPwd())) {
                        DDLog.d("heartlai", "HeartLaiCmdHandler-->BSMsgNotifyData cameraPid: " + cameraPid + "密码与服务器不相同，需要同步密码到服务器");
                        jsonObject.put("password", device.getPwd());
                        device.setSourceData(jsonObject.toString());
                        Map<String, Object> args = new HashMap<>();
                        args.put("cmd", HeartLaiCmd.CMD_SAVE_PASSWORD_TO_SERVER);
                        args.put("home_id", device.getHome_id());
                        args.put("password", device.getPwd());
                        device.submit(args);

                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }

                device.dispatchOnline();
            }

            JSONObject cameraStatus = new JSONObject();
            try {
                cameraStatus.put("cameraStatus", device.getConnectStatus());
            } catch (JSONException e) {
                e.printStackTrace();
            }

            if (device.getConnectStatus() == HeartLaiConstants.CAMERA_STATUS_CONNECTING) {
                Runnable runnable = new Runnable() {
                    @Override
                    public void run() {
                        DDLog.i("heartlai", "HeartLaiCmdHandler-->BSMsgNotifyData cameraPid:" + device.getCameraPID() + " 超时啦。");
                        JSONObject cameraStatus = new JSONObject();
                        try {
                            cameraStatus.put("cameraStatus", HeartLaiConstants.CAMERA_STATUS_WRONG_PASSWORD);
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }

                        Map<String, Object> args = new HashMap<>();
                        args.put("cmd", HeartLaiCmd.CMD_STATUS_CHANGE);
                        args.put("status", 1);
                        args.put("result", jsonToMap(cameraStatus));
                        device.dispatchResult(HeartLaiCmd.CMD_STATUS_CHANGE, args);
                    }
                };

                connectTimeoutRunableMap.put(device.getCameraPID(), runnable);
                handler.postDelayed(runnable, 20000);
                DDLog.i("heartlai", "HeartLaiCmdHandler-->BSMsgNotifyData cameraPid:" + device.getCameraPID() + " 添加连接超时任务。。。");
            }

            Map<String, Object> args = new HashMap<>();
            args.put("cmd", HeartLaiCmd.CMD_STATUS_CHANGE);
            args.put("status", 1);
            args.put("result", jsonToMap(cameraStatus));
            device.dispatchResult(HeartLaiCmd.CMD_STATUS_CHANGE, args);
            DDLog.i("heartlai", "HeartLaiCmdHandler-->BSMsgNotifyData cameraPid:" + device.getCameraPID() + " cameraStatus:" + device.getConnectStatus());

        }

        @Override
        public void BSSnapshotNotify(String s, byte[] bytes, int i) {

        }

        @Override
        public void callBackUserParams(String s, String s1, String s2, String s3, String s4, String s5, String s6) {

        }

        @Override
        public void callBackConnectLook(String s, int i, int i1) {

        }
    };

    private BridgeService.SHIXCOMMONInterface shixcommonInterface = new BridgeService.SHIXCOMMONInterface() {

        @Override
        public synchronized void CallBackSHIXJasonCommon(String cameraPid, String message) {
            DDLog.i(TAG, this.hashCode() + " onmessage " + cameraPid + " -->" + message);
            if (device == null || !cameraPid.equals(device.getCameraPID())) {
                DDLog.e(TAG, this.hashCode() + " onmessage " + cameraPid + " -->" + message);
                return;
            }
            Map<String, Object> result;
            try {
                JSONObject jsonObject = new JSONObject(message);
                if (DDJSONUtil.has(jsonObject, "cmd")) {
                    int cmd = DDJSONUtil.getInt(jsonObject, "cmd");
                    int status = DDJSONUtil.getInt(jsonObject, "result") == 0 ? 1 : 0;
                    switch (cmd) {
                        case 101:
                            device.setCameraPID(DDJSONUtil.getString(jsonObject, "p2pid"));
                            device.setMirroMode(DDJSONUtil.getInt(jsonObject, "mirror_mode"));
                            device.setVideoMode(DDJSONUtil.getInt(jsonObject, "video_mode"));
                            device.setIPaddress(DDJSONUtil.getString(jsonObject, "IPaddress"));
                            result = new HashMap<>();
                            result.put("cmd", HeartLaiCmd.CMD_GET_SETTING);
                            result.put("status", status);
                            result.put("result", jsonToMap(jsonObject));
                            device.dispatchResult(HeartLaiCmd.CMD_GET_SETTING, result);
                            break;
                        case 109:
                            result = new HashMap<>();
                            result.put("cmd", HeartLaiCmd.CMD_GET_SD_INFO);
                            result.put("status", status);
                            result.put("result", jsonToMap(jsonObject));
                            device.dispatchResult(HeartLaiCmd.CMD_GET_SD_INFO, result);
                            break;

                        case 110:
                            result = new HashMap<>();
                            result.put("cmd", HeartLaiCmd.CMD_FORMAT_SD);
                            result.put("status", status);
                            result.put("result", jsonToMap(jsonObject));
                            device.dispatchResult(HeartLaiCmd.CMD_FORMAT_SD, result);
                            break;

                        case 126:
                            result = new HashMap<>();
                            result.put("cmd", HeartLaiCmd.CMD_SYNC_TIMEZONE);
                            result.put("status", status);
                            result.put("result", jsonToMap(jsonObject));
                            device.dispatchResult(HeartLaiCmd.CMD_SYNC_TIMEZONE, result);
                            break;

                        case 107:
                            result = new HashMap<>();
                            result.put("cmd", HeartLaiCmd.CMD_GET_ALERT_INFO);
                            result.put("status", status);
                            result.put("result", jsonToMap(jsonObject));
                            device.dispatchResult(HeartLaiCmd.CMD_GET_ALERT_INFO, result);
                            break;

                        case 108:
                            result = new HashMap<>();
                            result.put("cmd", HeartLaiCmd.CMD_SET_ALERT_INFO);
                            result.put("status", status);
                            result.put("result", jsonToMap(jsonObject));
                            device.dispatchResult(HeartLaiCmd.CMD_SET_ALERT_INFO, result);
                            break;

                        case 121:
                            result = new HashMap<>();
                            result.put("cmd", HeartLaiCmd.CMD_GET_RECORD_CONFIG);
                            result.put("status", status);
                            result.put("result", jsonToMap(jsonObject));
                            device.dispatchResult(HeartLaiCmd.CMD_GET_RECORD_CONFIG, result);
                            break;

                        case 122:
                            result = new HashMap<>();
                            result.put("cmd", HeartLaiCmd.CMD_CONFIG_RECORD);
                            result.put("status", status);
                            result.put("result", jsonToMap(jsonObject));
                            device.dispatchResult(HeartLaiCmd.CMD_CONFIG_RECORD, result);
                            break;

                        case 106:
                            if (status == 1) {
                                String newUser = jsonObject.getString("user[0]");
                                String newPwd = jsonObject.getString("pwd[0]");
                                device.setPwd(newPwd);
                                try {
                                    JSONObject source = new JSONObject(device.getSourceData());
                                    source.put("password", device.getPwd());
                                    device.setSourceData(source.toString());
                                    device.reconnect();

                                    new HeartLaiRepository().updateServerPassword(device.getCameraPID(), device.getHome_id(), device.getPwd(), new Callback<StringResponseEntry>() {
                                        @Override
                                        public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                                            Map<String, Object> parms = new HashMap<>();
                                            parms.put("cmd", HeartLaiCmd.CMD_CHANGE_PASSWORD);
                                            parms.put("status", 1);
                                            parms.put("user[0]", device.getUid());
                                            parms.put("pwd[0]", device.getPwd());
                                            device.dispatchResult(HeartLaiCmd.CMD_CHANGE_PASSWORD, parms);
                                        }

                                        @Override
                                        public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                                            Map<String, Object> parms = new HashMap<>();
                                            parms.put("cmd", HeartLaiCmd.CMD_CHANGE_PASSWORD);
                                            parms.put("status", 0);
                                            parms.put("result", jsonToMap(jsonObject));
                                            device.dispatchResult(HeartLaiCmd.CMD_CHANGE_PASSWORD, parms);
                                        }
                                    });

                                } catch (JSONException e) {
                                    e.printStackTrace();
                                }
                            } else {
                                Map<String, Object> parms = new HashMap<>();
                                parms.put("cmd", HeartLaiCmd.CMD_CHANGE_PASSWORD);
                                parms.put("status", 0);
                                parms.put("result", jsonToMap(jsonObject));
                                device.dispatchResult(HeartLaiCmd.CMD_CHANGE_PASSWORD, parms);
                            }
                            break;

                        case 113:
                            result = new HashMap<>();
                            result.put("cmd", HeartLaiCmd.CMD_SCAN_WIFI);
                            result.put("status", status);
                            result.put("result", jsonObject);
                            device.dispatchResult(HeartLaiCmd.CMD_SCAN_WIFI, result);
                            break;

                        case 114:
                            result = new HashMap<>();
                            result.put("cmd", HeartLaiCmd.CMD_SET_WIFI);
                            result.put("status", status);
                            result.put("result", jsonObject);
                            device.dispatchResult(HeartLaiCmd.CMD_SET_WIFI, result);
                            break;
                        case 112:
                            device.setWifiSsid(DDJSONUtil.getString(jsonObject, "ssid"));
                            result = new HashMap<>();
                            result.put("cmd", HeartLaiCmd.CMD_CURRENT_WIFI);
                            result.put("status", status);
                            result.put("result", jsonObject);
                            device.dispatchResult(HeartLaiCmd.CMD_CURRENT_WIFI, result);
                            break;

                        default:
                            break;
                    }


                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }

    };

    private void getAlertMode(Map arg) {
        heartLaiRepository.getAlertMode((String) arg.get("home_id"), device.getCameraPID(),
                new Callback<GetAlertModeResponse>() {
                    @Override
                    public void onResponse(Call<GetAlertModeResponse> call, Response<GetAlertModeResponse> response) {
                        GetAlertModeResponse dsCamAlertModeResponse = response.body();
                        String alertMode = dsCamAlertModeResponse.getResult().getAlert_mode();
//                        if (alertMode.equals("critical")) {
//                            alertMode = "normal";
//                        }
//                        convertToInfo();
                        Map result = Cmd.getDefaultResultMap(true, HeartLaiCmd.CMD_GET_ALERT_MODE);
                        result.put("alert_mode", alertMode);
                        device.dispatchResult(HeartLaiCmd.CMD_GET_ALERT_MODE, result);
                    }

                    @Override
                    public void onFailure(Call<GetAlertModeResponse> call, Throwable t) {
                        Map result = Cmd.getDefaultResultMap(false, HeartLaiCmd.CMD_GET_ALERT_MODE);
                        result.put("errorMessage", "sender is null");
                        device.dispatchResult(HeartLaiCmd.CMD_GET_ALERT_MODE, result);
                    }
                });
    }

    private void setAlertMode(Map arg) {
        heartLaiRepository.setAlertMode((String) arg.get("home_id"), device.getCameraPID(), (String) arg.get("alert_mode"),
                new Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
//                        String alertMode = (String) arg.get("alert_mode");
//                        if (alertMode.equals("critical")) {
//                            alertMode = "normal";
//                        }
//                convertToInfo();
                        Map result = Cmd.getDefaultResultMap(true, HeartLaiCmd.CMD_SET_ALERT_MODE);
                        device.dispatchResult(HeartLaiCmd.CMD_SET_ALERT_MODE, result);
                    }

                    @Override
                    public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                        Map result = Cmd.getDefaultResultMap(false, HeartLaiCmd.CMD_SET_ALERT_MODE);
                        result.put("errorMessage", "sender is null");
                        device.dispatchResult(HeartLaiCmd.CMD_SET_ALERT_MODE, result);
                    }
                });
    }

    private Map<String, Object> jsonToMap(JSONObject jsonObject) {
        if (jsonObject == null) {
            return new HashMap<>();
        }

        Map<String, Object> data = new HashMap<>();
        Iterator it = jsonObject.keys();
        while (it.hasNext()) {
            String key = (String) it.next();
            try {
                data.put(key, jsonObject.get(key));
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        return data;
    }


}
