package com.dinsafer.module_heartlai;

import android.app.Application;
import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.common.CommonCmdEvent;
import com.dinsafer.dincore.common.DeivceChangeEvent;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dincore.common.IService;
import com.dinsafer.dincore.db.cache.DeviceCacheHelper;
import com.dinsafer.dincore.user.bean.DinUser;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dincore.utils.MapUtils;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_heartlai.add.HeartLaiBinder;
import com.dinsafer.module_heartlai.http.HeartLaiRepository;
import com.dinsafer.module_heartlai.model.GetDeviceListResponse;
import com.dinsafer.module_heartlai.model.HeartCacheInfo;
import com.dinsafer.module_heartlai.model.IPCDevice;
import com.dinsafer.module_heartlai.util.SearchIpcHelper;
import com.google.gson.Gson;
import com.heartlai.ipc.JniClient;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Response;

@Keep
public class HeartLaiService implements IService {
    private static final String BINDER_KEY_HEARTLAI = "heartlai_binder";
    private static final String CACHE_IDENTIFY = "HeartLaiService"; // 缓存key后缀
    private static final int PAGE_SIZE_DEFAULT = 30; // 加载ipc列表一页的数量

    private static final String TAG = HeartLaiService.class.getSimpleName();

    private ArrayList<Device> deviceList = new ArrayList<>();
    public static String currentHomeId;
    private HeartLaiRepository heartLaiRepository;

    private final Object lock = new Object();
    private final byte[] listLock = new byte[0];

    private Application application;
    private final HeartCacheInfo cacheInfo = new HeartCacheInfo();

    public HeartLaiService(Application application) {
        this.application = application;
    }

    @Override
    public void load() {
        EventBus.getDefault().register(this);
        HeartLaiServiceManager.init(application);
        heartLaiRepository = new HeartLaiRepository();
    }

    @Override
    public void unLoad() {
        EventBus.getDefault().unregister(this);
        heartLaiRepository = null;
        HeartLaiServiceManager.getInstance().clear();
    }

    @Override
    public void config(Map<String, Object> map) {
        final String lastHomeId = currentHomeId;
        currentHomeId = (String) MapUtils.get(map, "homeID", "");
        SearchIpcHelper.get().setCurrentHomeId(currentHomeId);
        if (!TextUtils.isEmpty(lastHomeId) && !lastHomeId.equals(currentHomeId)) {
            // 切换了家庭
            for (Device device : deviceList) {
                device.destory();
            }
            deviceList.clear();
            cacheInfo.updateFrom(null);
        }
    }

    @Override
    public List fetchDevices() {
        synchronized (lock) {
            createDeviceFromCache();
            markDevicesFromCache();
            requestDeviceCirculate(cacheInfo.getNewestAddTime());
            DDLog.i(TAG, "get heartlai device finish");
        }
        return new ArrayList<>(deviceList);
    }

    private void requestDeviceCirculate(final long startTimeStamp) {
        synchronized (lock) {
            final String loadingHomeId = currentHomeId;
            long addTime = startTimeStamp;
            DDLog.i(TAG, "get heartlai device start...,startTimeStamp: " + addTime);
            // !!!! 循环获取配件
            final List<GetDeviceListResponse.DeviceBean> netDeviceList = new ArrayList<>();
            while (true) {
                if (!TextUtils.isEmpty(loadingHomeId) && !loadingHomeId.equals(currentHomeId)) {
                    break;
                }

                final List<GetDeviceListResponse.DeviceBean> result = requestIpcOnPageSync(addTime, PAGE_SIZE_DEFAULT, false);
                if (null == result || result.size() == 0) {
                    break;
                }

                netDeviceList.addAll(result);
                for (GetDeviceListResponse.DeviceBean bean : result) {
                    if (null != bean.getAddtime() && bean.getAddtime() > addTime) {
                        addTime = bean.getAddtime();
                    }
                }

                if (result.size() < PAGE_SIZE_DEFAULT) {
                    break;
                }
            }
            // 切换了房间，返回空数据
            if (!TextUtils.isEmpty(loadingHomeId) && !loadingHomeId.equals(currentHomeId)) {
                return;
            }

            if (netDeviceList.size() > 0) {
                final List<Device> netDevices = createDeviceFromNet(netDeviceList);
                if (netDevices.size() > 0) {
                    saveDeviceCache();
                }
            }
        }
    }

    @Nullable
    private List<GetDeviceListResponse.DeviceBean> requestIpcOnPageSync(final long addTime, final int pageSize, final boolean orderDesc) {
        DDLog.d(TAG, "requestIpcOnePage, addTime: " + (addTime + 1) + ", pageSize: " + pageSize + ", orderDesc: " + orderDesc);
        Response<GetDeviceListResponse> response = heartLaiRepository.listTpIpcSync(currentHomeId, addTime + 1, pageSize, orderDesc);
        if (null == response || !response.isSuccessful()) {
            return null;
        }
        final GetDeviceListResponse body = response.body();
        if (null == body) {
            return null;
        }

        final List<GetDeviceListResponse.DeviceBean> result = body.getResult();
        if (null == result || result.size() == 0) {
            return null;
        }

        return result;
    }

    @Override
    public Device getDevice(String id) {
        if (TextUtils.isEmpty(id)) {
            return null;
        }
        for (Device heartLaiDevice : deviceList) {
            if (heartLaiDevice.getId().equals(id)) {
                return heartLaiDevice;
            }
        }
        return null;
    }

    @Override
    public Device getDevice(String s, String s1) {
        return getDevice(s);
    }

    @Override
    public List<Device> getDeviceByType(String sub) {
        if (isSupportedDeviceType(sub)) {
            fetchDevices();
            return deviceList;
        }
        return null;
    }

    @Override
    public List<Device> getDeviceByType(String sub, boolean cacheFirst) {
        if (!isSupportedDeviceType(sub)) {
            return null;
        }

        synchronized (lock) {
            if (cacheFirst) {
                // 1. 该模式下，有缓存返回缓存，没有直接加载全部
                final List<Device> deviceFromCache = createDeviceFromCache();
                if (deviceFromCache.size() > 0) {
                    return deviceFromCache;
                } else {
                    return fetchDevices();
                }
            }

            // 2. 该模式下，仅加载下一页
            markDevicesFromCache();

            final String loadingHomeId = currentHomeId;
            final long addTime = cacheInfo.getNewestAddTime();
            final List<GetDeviceListResponse.DeviceBean> netDeviceList = requestIpcOnPageSync(addTime, PAGE_SIZE_DEFAULT, false);
            if (null != netDeviceList && netDeviceList.size() > 0) {
                long maxAddTime = addTime;
                for (GetDeviceListResponse.DeviceBean bean : netDeviceList) {
                    if (null != bean.getAddtime() && bean.getAddtime() > maxAddTime) {
                        maxAddTime = bean.getAddtime();
                    }
                }

                final List<Device> netDevices = createDeviceFromNet(netDeviceList);

                // 切换了房间，返回空数据
                if (!TextUtils.isEmpty(loadingHomeId) && !loadingHomeId.equals(currentHomeId)) {
                    return null;
                }

                if (0 < netDevices.size()) {
                    saveDeviceCache();
                }
            }
        }
        return new ArrayList<>(deviceList);
    }

    @Nullable
    @Override
    public List<Device> getCacheDeviceByType(String type) {
        if (!isSupportedDeviceType(type)) {
            return null;
        }
        final List<Device> result = new ArrayList<>();
        final List<Device> cacheDevices = createDeviceFromCache();
        if (cacheDevices.size() > 0) {
            markDevicesFromCache();
            result.addAll(cacheDevices);
        }
        return result;
    }

    @Nullable
    @Override
    public List<Device> getLocalAndNewDeviceByType(String type) {
        if (!isSupportedDeviceType(type)) {
            return null;
        }
        return fetchDevices();
    }

    @Nullable
    @Override
    public List<Device> getAllDeviceByType(String type) {
        if (!isSupportedDeviceType(type)) {
            return null;
        }
        synchronized (lock) {
            createDeviceFromCache();
            markDevicesFromCache();
            requestDeviceCirculate(0);
            DDLog.i(TAG, "getAllDeviceByType finish: heartlai");
        }
        return new ArrayList<>(deviceList);
    }

    @Override
    public boolean isSupportedDeviceType(String type) {
        return !TextUtils.isEmpty(type) && HeartLaiConstants.PROVIDER_HEARTLAI.equals(type);
    }

    @Override
    public boolean removeDeviceCacheById(String sub) {
        if (TextUtils.isEmpty(sub)) {
            return false;
        }

        synchronized (listLock) {
            boolean remove = false;
            for (Device device : deviceList) {
                if (sub.equals(device.getId())) {
                    IPCDevice d = (IPCDevice) device;
                    remove = cacheInfo.removeDevice(sub, d.getCameraPID(), d.getProvider());
                    if (remove) {
                        deviceList.remove(device);
                    }
                    break;
                }
            }
            saveDeviceCache();

            DDLog.i(TAG, "removeDeviceCacheById. " + sub + " remove succeed ? " + remove);
            return remove;
        }
    }

    @Override
    public boolean removeDeviceCacheByIdAndSub(String s, String s1) {
        return removeDeviceCacheById(s);
    }

    @Override
    public boolean removeDeviceCacheByType(String sub) {
        return HeartLaiConstants.PROVIDER_HEARTLAI.equals(sub);
    }

    @Override
    public boolean releaseDeviceByType(String s) {
        if (isSupportedDeviceType(s)) {
            final List<Device> localList = deviceList;
            if (null != localList && localList.size() > 0) {
                for (Device device : localList) {
                    HeartLaiDevice heartLaiDevice = (HeartLaiDevice) device;
                    heartLaiDevice.needConnectAgain();
                }
            }
            JniClient.stopAll();
            return true;
        }
        return false;
    }

    @Override
    public Device acquireTemporaryDevices(String id, String model) {
        return null;
    }
    @Override
    public BasePluginBinder createPluginBinder(Context context, @NonNull String type) {
        if (BINDER_KEY_HEARTLAI.equals(type)) {
            return new HeartLaiBinder(context);
        }
        return null;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(CommonCmdEvent commonCmdEvent) {
        DDLog.i(TAG, "on Event: commonCmdEvent " + commonCmdEvent.getCmd() + " /" + commonCmdEvent.getExtra());
        if (CommonCmdEvent.CMD.DSCAM_ADD.equals(commonCmdEvent.getCmd())
                && !TextUtils.isEmpty(commonCmdEvent.getExtra())) {
//             手动插入device
            try {
                JSONObject object = new JSONObject(commonCmdEvent.getExtra());
                if (!HeartLaiConstants.PROVIDER_HEARTLAI.equals(DDJSONUtil.getString(object, "provider"))) {
                    return;
                }

                final String id = DDJSONUtil.getString(object, "id");
                final String pid = DDJSONUtil.getString(object, "pid");
                final String provider = DDJSONUtil.getString(object, HeartLaiConstants.ATTR_PROVIDER);
                HeartLaiDevice device = HeartLaiDevice.newBuilder()
                        .id(id)
                        .home_id(currentHomeId)
                        .name(DDJSONUtil.getString(object, HeartLaiConstants.ATTR_NAME))
                        .setIpc_type(provider)
                        .uid(DDJSONUtil.getString(object, "user"))
                        .password(DDJSONUtil.getString(object, HeartLaiConstants.ATTR_PASSWORD))
                        .cameraPID(pid)
                        .time(DDJSONUtil.getLong(object, "addtime"))
                        .sourceData(object.toString())
                        .build();
                device.setHdMode(true);
                device.setCategory(DDJSONUtil.getInt(object, "dtype"));
                device.setSubCategory(DDJSONUtil.getString(object, "stype"));

                // 若已存在，则修改缓存中对应的device
                for (Device dev : deviceList) {
                    if (id.equals(dev.getId()) && dev.getFlagDeleted()) {
                        ((IPCDevice)dev).setFlagDeleted(false);
                    }
                }
                cacheInfo.addDevice(id, pid, provider);
                saveDeviceCache();
                addDeviceIfNotExit(device);

                DeivceChangeEvent event = new DeivceChangeEvent(device);
                event.setAdd(true);
                EventBus.getDefault().post(event);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        } else if (CommonCmdEvent.CMD.DSCAM_DELETE.equals(commonCmdEvent.getCmd())
                && !TextUtils.isEmpty(commonCmdEvent.getExtra())) {
            try {
                JSONObject object = new JSONObject(commonCmdEvent.getExtra());
                if (!HeartLaiConstants.PROVIDER_HEARTLAI.equals(DDJSONUtil.getString(object, "provider"))) {
                    return;
                }
                String pid = DDJSONUtil.getString(object, "pid");
                for (Device device : deviceList) {
                    if (pid.equals(DeviceHelper.getString(device, HeartLaiConstants.ATTR_CAMERA_PID, ""))) {
                        DeivceChangeEvent event = new DeivceChangeEvent(device);
                        event.setRemove(true);
                        EventBus.getDefault().post(event);
                        break;
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        } else if (CommonCmdEvent.CMD.DSCAM_RENAME.equals(commonCmdEvent.getCmd())
                && !TextUtils.isEmpty(commonCmdEvent.getExtra())) {
            try {
                JSONObject object = new JSONObject(commonCmdEvent.getExtra());
                if (!HeartLaiConstants.PROVIDER_HEARTLAI.equals(DDJSONUtil.getString(object, "provider"))) {
                    return;
                }
                String pid = DDJSONUtil.getString(object, "pid");
                for (Device device : deviceList) {
                    if (pid.equals(DeviceHelper.getString(device, HeartLaiConstants.ATTR_CAMERA_PID, ""))) {
                        DDLog.i(TAG, "onEvent1: " + device.toString());
                        HeartLaiDevice device1 = (HeartLaiDevice) device;
                        DDLog.i(TAG, "onEvent2: " + device1.toString());
                        device1.setName(DDJSONUtil.getString(object, "name"));
                        Map<String, Object> result = new HashMap<>();
                        result.put("status", 1);
                        ((IPCDevice)device).dispatchResult(HeartLaiCmd.CMD_CHANGE_NAME, result);
                        break;
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        } else if (CommonCmdEvent.CMD.ON_BEFORE_HOME_DISCONNECT.equals(commonCmdEvent.getCmd())) {
            final List<Device> localList = deviceList;
            if (null != localList && localList.size() > 0) {
                for (Device device : localList) {
                    HeartLaiDevice heartLaiDevice = (HeartLaiDevice) device;
                    heartLaiDevice.needConnectAgain();
                }
            }
            HeartLaiJniClientProxy.stopAll();
        } else if (CommonCmdEvent.CMD.ON_BEFORE_HOME_SWITCH.equals(commonCmdEvent.getCmd())) {
            SearchIpcHelper.get().release();
        } else if (CommonCmdEvent.CMD.LOGIN_SUCCESS.equals(commonCmdEvent.getCmd())) {
            cacheInfo.updateFrom(null);
            for (Device device : deviceList) {
                device.destory();
            }
            deviceList.clear();
        }
    }

    private void markDevicesFromCache() {
        if (deviceList.size() > 0) {
            for (Device device : deviceList) {
                ((IPCDevice)device).setFlagCache(true);
            }
        }
    }

    @NonNull
    private List<Device> createDeviceFromNet(@NonNull List<GetDeviceListResponse.DeviceBean> deviceBeanList) {
        List<Device> netDevices = new ArrayList<>();
        for (GetDeviceListResponse.DeviceBean deviceBean : deviceBeanList) {
            String id = deviceBean.getId();
            if (TextUtils.isEmpty(id)) {
                continue;
            }
            // 已经在缓存中，修改状态
            final Device cacheDevice = getDevice(id);
            if (null != cacheDevice) {
                final IPCDevice d = (IPCDevice) cacheDevice;
                d.setFlagCache(true);
                d.setFlagDeleted(false);
                continue;
            }

            // 不在缓存中，创建并缓
            HeartLaiDevice model = HeartLaiDevice.newBuilder()
                    .id(deviceBean.getId())
                    .home_id(currentHomeId)
                    .name(deviceBean.getName())
                    .setIpc_type(deviceBean.getProvider())
                    .uid(deviceBean.getUser())
                    .password(deviceBean.getPassword())
                    .cameraPID(deviceBean.getPid())
                    .time(deviceBean.getAddtime())
                    .sourceData(new Gson().toJson(deviceBean))
                    .build();
            if (null == deviceBean.getHDmode()) {
                // 默认HDMode为true
                model.setHdMode(true);
            } else {
                model.setHdMode(deviceBean.getHDmode());
            }
            model.setCategory(deviceBean.getDtype() == null ? 2 : deviceBean.getDtype());
            model.setSubCategory(deviceBean.getStype());
            boolean added = addDeviceIfNotExit(model);
            if (added) {
                netDevices.add(model);
                HeartCacheInfo.CacheInfo cache = new HeartCacheInfo.CacheInfo(deviceBean.getId(), deviceBean.getPid(), deviceBean.getProvider());
                final Long addTime = deviceBean.getAddtime();
                if (null != addTime) {
                    cache.setAddTime(addTime);
                }
                cacheInfo.addDevice(cache);
            }
        }

        DDLog.i(TAG, CACHE_IDENTIFY + ":cache----netdevice: " + netDevices.size());
        return netDevices;
    }


    @NonNull
    private List<Device> createDeviceFromCache() {
        if (deviceList.size() > 0) {
            DDLog.d(TAG, CACHE_IDENTIFY + ":cache--------不从缓存创建");
            return new ArrayList<>(deviceList);
        }

        final String homeId = currentHomeId;
        DinUser user = DinCore.getUserInstance().getUser();
        final String userId = null != user ? user.getUser_id() : null;
        final HeartCacheInfo cache = DeviceCacheHelper.getCache(homeId, userId, CACHE_IDENTIFY, HeartCacheInfo.class);
        cacheInfo.updateFrom(cache);
        DDLog.d(TAG, CACHE_IDENTIFY + ":cache--------reedCache: " + cacheInfo);

        List<Device> cacheDevices = new ArrayList<>();
        if (!cacheInfo.isCacheEmpty()) {
            final List<HeartCacheInfo.CacheInfo> cacheList = cacheInfo.getCacheInfoList();
            for (HeartCacheInfo.CacheInfo info : cacheList) {
                final String id = info.getId();
                final String pid = info.getPid();
                final String provider = info.getProvider();
                if (TextUtils.isEmpty(id) || TextUtils.isEmpty(pid) || TextUtils.isEmpty(provider)) {
                    continue;
                }

                // 已经存在对应的Device
                final Device cacheDevice = getDevice(pid);
                if (null != cacheDevice) {
                    ((IPCDevice)cacheDevice).setFlagCache(true);
                    cacheDevices.add(cacheDevice);
                    continue;
                }

                HeartLaiDevice model = HeartLaiDevice.newBuilder()
                        .id(id)
                        .home_id(currentHomeId)
                        .setIpc_type(provider)
                        .cameraPID(pid)
                        .fromCache(true)
                        .build();
                // model.setHdMode(deviceBean.getHDmode());
                // model.setCategory(deviceBean.getDtype());
                // model.setSubCategory(deviceBean.getStype());
                addDeviceIfNotExit(model);

                cacheDevices.add(model);
            }
        }
        DDLog.i(TAG, CACHE_IDENTIFY + ":cache----缓存device: " + cacheDevices.size());
        return cacheDevices;
    }

    private void saveDeviceCache() {
        final String homeId = currentHomeId;
        DinUser user = DinCore.getUserInstance().getUser();
        final String userId = null != user ? user.getUser_id() : null;
        DeviceCacheHelper.saveCacheAsync(homeId, userId, CACHE_IDENTIFY, cacheInfo);
        DDLog.d(TAG, CACHE_IDENTIFY + ":cache--------savecache: " + cacheInfo);
    }

    private void clearDeviceCache() {
        final String homeId = currentHomeId;
        DinUser user = DinCore.getUserInstance().getUser();
        final String userId = null != user ? user.getUser_id() : null;
        DeviceCacheHelper.removeCacheASync(homeId, userId, CACHE_IDENTIFY);
    }

    private boolean addDeviceIfNotExit(HeartLaiDevice device) {
        if (null != device) {
            if (!deviceList.contains(device)) {
                DDLog.d(TAG, CACHE_IDENTIFY + ":添加Device到列表");
                deviceList.add(device);
                return true;
            } else {
                device.setFlagDeleted(false);
            }
        }
        return false;
    }


    @Subscribe
    public void onEvent(DeivceChangeEvent event) {
        if (event.isAdd()) {

        } else if (event.isRemove()) {
            for (Device device : deviceList) {
                try {
                    IPCDevice d = (IPCDevice) device;
                    if (device.getId().equals(event.getDevice().getId())) {
                        cacheInfo.removeDevice(device.getId(), d.getCameraPID(), d.getProvider());
                        saveDeviceCache();
                        deviceList.remove(device);
                        DDLog.i(TAG, " DeivceChangeEvent: 删除ipc：" + deviceList.size());
                        break;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
