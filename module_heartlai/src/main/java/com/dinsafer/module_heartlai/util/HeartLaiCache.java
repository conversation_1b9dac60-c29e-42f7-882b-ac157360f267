package com.dinsafer.module_heartlai.util;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;

import java.io.File;
import java.io.FileNotFoundException;

/**
 * Created by rinfon on 15/6/14.
 */
public class HeartLaiCache {

    /**
     * sdcard
     * Androidx后无法通过File的方式用流进行直接操作
     * Androidx后将使用 {@link Context#getExternalCacheDir()} 作为根目录
     */
    private static final String ROOT_DIR = Environment
            .getExternalStorageDirectory().toString();

    /**
     * HeartLai录像文件路径
     */
    public static String DINNET_RECORD_FILE_PATH ;

    static {
        DINNET_RECORD_FILE_PATH = ROOT_DIR + "/Dinnet/." + "record/heartlai/";
    }

    /**
     * Daydow path
     */
    private static final String DINNET_FOLDER = ROOT_DIR + "/Dinnet/";

    /**
     * log path
     */
    private static final String LOG_FOLDER = ROOT_DIR + "/Dinnet/log/";

    /**
     * log name
     */
    private static final String LOG_FILENAME = "dinnet.log";

    /**
     * image cache path
     * 由于心赖摄像头需要提供截图路径，SDK没有直接保存到相册，需要提供该路径
     */
    private static final String IMAGE_CACHE = ROOT_DIR + "/Dinnet/image_cache/";


    // ************Androidx后使用以下方式获取目录**************//

    private static volatile HeartLaiCache instance;
    private static String mRootDir;

    private HeartLaiCache() {
    }

    public static HeartLaiCache getInstance() {
        if (null == instance) {
            synchronized (HeartLaiCache.class) {
                if (null == instance) {
                    instance = new HeartLaiCache();
                }
            }
        }
        return instance;
    }

    /**
     * 设置Androidx缓存根目录
     * 建议设置为 {@link Context#getExternalCacheDir()}
     *
     * @param rootDir Androidx缓存根目录
     */
    public void init(String rootDir) {
        mRootDir = rootDir;
    }

    /**
     * Androidx兼容获取日志缓存根目录
     *
     * @return 日志缓存根目录
     */
    public String getLogFolder() {
        if (aboveAndroidx()) {
            return mRootDir + "/Dinnet/log/";
        } else {
            return LOG_FOLDER;
        }
    }

    /**
     * Androidx兼容获取图片缓存根目录
     *
     * @return 图片缓存根目录
     */
    public String getImageFolder() {
        if (aboveAndroidx()) {
            return mRootDir + "/Dinnet/image_cache/";
        } else {
            return IMAGE_CACHE;
        }
    }



    private boolean aboveAndroidx() {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q;
    }

    public static void updatePhoto(Activity activity, File file) {
        //将截图保存至相册并广播通知系统刷新
        try {
            MediaStore.Images.Media.insertImage(activity.getContentResolver(), file.getAbsolutePath(), file.getName(), null);
            Intent intent = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(file));
            activity.sendBroadcast(intent);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
    }
}
