package com.dinsafer.module_heartlai.util;

import android.content.Context;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;

public class WifiUtil {

    private final WifiInfo mWifiInfo;
    // 定义WifiManager对象
    private WifiManager mWifiManager;

    private static WifiUtil util;

    /**
     * 单例方法
     *
     * @param context
     * @return
     */
    public static WifiUtil getInstance(Context context) {
        if (util == null) {
            synchronized (WifiUtil.class) {
                util = new WifiUtil(context);
            }
        }
        return util;
    }

    // 构造器
    private WifiUtil(Context context) {
        // 取得WifiManager对象
        mWifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);

        // 取得WifiInfo对象
        mWifiInfo = mWifiManager.getConnectionInfo();
    }

    // 打开WIFI
    public void openWifi() {
        if (!mWifiManager.isWifiEnabled()) {
            mWifiManager.setWifiEnabled(true);
        }
    }

    // 关闭WIFI
    public void closeWifi() {
        if (mWifiManager.isWifiEnabled()) {
            mWifiManager.setWifiEnabled(false);
        }
    }

    // 检查当前WIFI状态
    public int checkState() {
        return mWifiManager.getWifiState();
    }

    // 得到连接的ID
    public int getNetworkId() {
        return (mWifiInfo == null) ? 0 : mWifiInfo.getNetworkId();
    }

    // 得到WifiInfo的所有信息包
    public String getWifiInfo() {
        return (mWifiInfo == null) ? "NULL" : mWifiInfo.toString();
    }


    /**
     * 断开WiFi连接
     */
    public void disconnectWiFiNetWork() {
        // 断开所有网络连接
        mWifiManager.disconnect();
    }


    /**
     * 断开WiFi连接
     *
     * @param networkId
     */
    public void disconnectWiFiNetWork(int networkId) {
        // 设置对应的wifi网络停用
        mWifiManager.disableNetwork(networkId);

        // 断开所有网络连接
        mWifiManager.disconnect();
    }

}
