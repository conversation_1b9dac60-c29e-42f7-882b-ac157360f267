package com.dinsafer.module_heartlai.util;

import androidx.annotation.Keep;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.utils.MapUtils;
import com.dinsafer.module_heartlai.HeartLaiConstants;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2021/6/23
 */
@Keep
public class HeartLaiUtils {

    public static boolean isHeartLaiDevice(Device device) {
        if (device == null) {
            return false;
        }
        if (HeartLaiConstants.PROVIDER_HEARTLAI.equals(DeviceHelper.getString(device, HeartLaiConstants.ATTR_PROVIDER, ""))) {
             return true;
        }
        return false;
    }

    public static boolean isDeviceConnected(Device device) {
        if (device == null) {
            return false;
        }
        return ((int) MapUtils.get(device.getInfo(), HeartLaiConstants.ATTR_CAMERA_STATUS, HeartLaiConstants.CAMERA_STATUS_UNKNOW)) == HeartLaiConstants.CAMERA_STATUS_ONLINE;
    }

    public static boolean isDeviceConnecting(Device device) {
        if (device == null) {
            return false;
        }
        return ((int) MapUtils.get(device.getInfo(), HeartLaiConstants.ATTR_CAMERA_STATUS, HeartLaiConstants.CAMERA_STATUS_UNKNOW)) == HeartLaiConstants.CAMERA_STATUS_CONNECTING;
    }

    public static boolean isDeviceWrongPassword(Device device) {
        if (device == null) {
            return false;
        }
        return ((int) MapUtils.get(device.getInfo(), HeartLaiConstants.ATTR_CAMERA_STATUS, HeartLaiConstants.CAMERA_STATUS_UNKNOW)) == HeartLaiConstants.CAMERA_STATUS_WRONG_PASSWORD;
    }

    public static boolean isDefaultPassword(Device device) {
        if (device == null) {
            return false;
        }
        return HeartLaiConstants.DEFAULT_HEARTLAI_PWD.equals(DeviceHelper.getString(device,HeartLaiConstants.ATTR_PASSWORD,""));
    }

    public static boolean isMirrorOn(int mirror) {
        return mirror == 3 || mirror == 1;
    }

    public static boolean isFlipOn(int mirror) {
        return mirror == 3 || mirror == 2;
    }

    public static int getSourceDataIpcType(Device device){
        int type = 0;
        try {
            JSONObject jsonObject = new JSONObject(DeviceHelper.getString(device, HeartLaiConstants.ATTR_SOURCE_DATA, "{}"));
            type  = jsonObject.getInt("ipc_type");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return type;
    }
}
