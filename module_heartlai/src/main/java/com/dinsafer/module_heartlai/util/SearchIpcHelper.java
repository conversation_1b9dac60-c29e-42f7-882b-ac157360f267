package com.dinsafer.module_heartlai.util;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.module_heartlai.HeartLaiDevice;
import com.dinsafer.module_heartlai.http.HeartLaiApi;
import com.dinsafer.module_heartlai.model.GetDeviceListResponse;
import com.dinsafer.module_heartlai.model.SearchIpcParams;
import com.dinsafer.module_heartlai.model.SearchIpcResponse;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2023/1/4 3:03 下午
 */
public class SearchIpcHelper {

    private final static String TAG = "SearchIpcHelper-HeartLai";
    private final static long LOAD_INTERVAL = 2L * 1000; // 加载时间间隔

    private String currentHomeId;
    private Call<SearchIpcResponse> mSearchIpcCall;

    private final ConcurrentHashMap<String, HeartLaiDevice> loadingMap = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, HeartLaiDevice> needLoadMap = new ConcurrentHashMap<>();

    private volatile boolean scheduled = false;
    private final Handler mHandler = new Handler(Looper.getMainLooper());
    private final Runnable searchTask = () -> {
        cancel(true);

        synchronized (SearchIpcHelper.this) {
            if (needLoadMap.size() > 0) {
                final List<SearchIpcParams> params = new ArrayList<>(needLoadMap.size());
                for (Map.Entry<String, HeartLaiDevice> entry : needLoadMap.entrySet()) {
                    params.add(new SearchIpcParams(entry.getKey(), entry.getValue().getProvider()));
                }
                loadingMap.putAll(needLoadMap);
                needLoadMap.clear();
                startSearchIpc(currentHomeId, params);
            }
        }
        scheduled = false;
    };

    /**
     * 添加到查询心赖IPC信息队列，等时间到时统一请求
     *
     * @param homeId
     * @param dsDevice
     */
    public void addTask(final String homeId, final HeartLaiDevice dsDevice) {
        MsctLog.v(TAG, "addTask, homeId: " + homeId + ", deviceId: " + dsDevice.getCameraPID());
        if (TextUtils.isEmpty(currentHomeId) || !currentHomeId.equals(homeId)) {
            MsctLog.w(TAG, "addTask, currentHomeId is not equal to device's homeId, ignore!");
            return;
        }

        final String deviceId = dsDevice.getCameraPID();
        if (TextUtils.isEmpty(deviceId)) {
            MsctLog.w(TAG, "addTask, empty device's id!");
            return;
        }

        synchronized (this) {
            if (loadingMap.containsKey(deviceId)
                    || needLoadMap.containsKey(deviceId)) {
                MsctLog.w(TAG, "addTask, device's id is loading or needLoad!");
                return;
            }

            needLoadMap.put(deviceId, dsDevice);

            schedule(false);
        }
    }

    private void schedule(final boolean now) {
        synchronized (this) {
            if (needLoadMap.size() > 0 && !scheduled) {
                scheduled = true;
                mHandler.postDelayed(searchTask, now ? 0 : LOAD_INTERVAL);
            }
        }
    }

    public void setCurrentHomeId(final String homeId) {
        final String lastHomeId = currentHomeId;
        this.currentHomeId = homeId;
        if (!TextUtils.isEmpty(lastHomeId) && !lastHomeId.equals(currentHomeId)) {
            release();
        }
    }

    private void startSearchIpc(String homeID, @NonNull List<SearchIpcParams> params) {
        mSearchIpcCall = HeartLaiApi.getApi().searchIpc(homeID, params);
        mSearchIpcCall.enqueue(new Callback<SearchIpcResponse>() {
            @Override
            public void onResponse(Call<SearchIpcResponse> call, Response<SearchIpcResponse> response) {
                SearchIpcResponse body = response.body();
                if (response.isSuccessful() && null != body && body.getResult() != null) {
                    SearchIpcResponse.ResultBean result = body.getResult();
                    synchronized (SearchIpcHelper.this) {
                        if (loadingMap.size() == 0) {
                            return;
                        }
                        final ConcurrentHashMap<String, HeartLaiDevice> devices = new ConcurrentHashMap<>(loadingMap);
                        loadingMap.clear();
                        notifyResult(devices, result.getList(), true);
                    }
                } else {
                    synchronized (SearchIpcHelper.this) {
                        if (loadingMap.size() == 0) {
                            return;
                        }
                        final ConcurrentHashMap<String, HeartLaiDevice> devices = new ConcurrentHashMap<>(loadingMap);
                        loadingMap.clear();
                        notifyResult(devices, null, false);
                    }
                }
                mSearchIpcCall = null;
                schedule(true);
            }

            @Override
            public void onFailure(Call<SearchIpcResponse> call, Throwable t) {
                t.printStackTrace();
                synchronized (SearchIpcHelper.this) {
                    if (loadingMap.size() > 0) {
                        final ConcurrentHashMap<String, HeartLaiDevice> devices = new ConcurrentHashMap<>(loadingMap);
                        loadingMap.clear();
                        notifyResult(devices, null, false);
                    }
                }
                mSearchIpcCall = null;
                schedule(true);
            }
        });
    }

    /**
     * 直接查询指定的心赖IPC信息
     *
     * @param homeID
     * @param heartLaiDevice
     */
    public void startSearchIpcSingle(String homeID, @NonNull final HeartLaiDevice heartLaiDevice) {
        final String deviceId = heartLaiDevice.getCameraPID();
        final String provider = heartLaiDevice.getProvider();
        if (TextUtils.isEmpty(homeID) || TextUtils.isEmpty(deviceId) || TextUtils.isEmpty(provider)) {
            MsctLog.e(TAG, "startSearchIpcempty homeId or empty deviceId or empty proviider");
            return;
        }
        final SearchIpcParams params = new SearchIpcParams(deviceId, provider);
        Call<SearchIpcResponse> searchIpcResponseCall = HeartLaiApi.getApi().searchIpc(homeID, Collections.singletonList(params));
        searchIpcResponseCall.enqueue(new Callback<SearchIpcResponse>() {
            @Override
            public void onResponse(Call<SearchIpcResponse> call, Response<SearchIpcResponse> response) {
                SearchIpcResponse body = response.body();
                if (response.isSuccessful() && null != body && body.getResult() != null) {
                    SearchIpcResponse.ResultBean result = body.getResult();
                    List<GetDeviceListResponse.DeviceBean> list = result.getList();
                    GetDeviceListResponse.DeviceBean info = null;
                    if (null != list && list.size() > 0 && !TextUtils.isEmpty(deviceId)) {
                        for (int i = 0; i < list.size(); i++) {
                            GetDeviceListResponse.DeviceBean listBean = list.get(i);
                            if (deviceId.equals(listBean.getPid())) {
                                info = listBean;
                                break;
                            }
                        }
                    }
                    heartLaiDevice.onSearchIpcInfo(info, true);
                } else {
                    heartLaiDevice.onSearchIpcInfo(null, true);
                }
            }

            @Override
            public void onFailure(Call<SearchIpcResponse> call, Throwable t) {
                t.printStackTrace();
                heartLaiDevice.onSearchIpcInfo(null, false);
            }
        });
    }

    private void notifyResult(
            final @NonNull ConcurrentHashMap<String, HeartLaiDevice> HeartLaiDevices,
            final @Nullable List<GetDeviceListResponse.DeviceBean> list, boolean success) {
        if (HeartLaiDevices.size() == 0) {
            return;
        }

        for (HeartLaiDevice HeartLaiDevice : HeartLaiDevices.values()) {
            GetDeviceListResponse.DeviceBean info = null;
            final String deviceId = HeartLaiDevice.getCameraPID();
            if (null != list && list.size() > 0 && !TextUtils.isEmpty(deviceId)) {
                for (int i = 0; i < list.size(); i++) {
                    GetDeviceListResponse.DeviceBean listBean = list.get(i);
                    if (deviceId.equals(listBean.getPid())) {
                        info = listBean;
                        break;
                    }
                }
            }
            HeartLaiDevice.onSearchIpcInfo(info, success);
        }
    }

    public void release() {
        mHandler.removeCallbacksAndMessages(null);
        synchronized (SearchIpcHelper.this) {
            if (loadingMap.size() == 0 && needLoadMap.size() == 0) {
                return;
            }
            final ConcurrentHashMap<String, HeartLaiDevice> devices = new ConcurrentHashMap<>(loadingMap);
            devices.putAll(needLoadMap);
            notifyResult(devices, null, false);

            cancel(false);

            needLoadMap.clear();
            loadingMap.clear();
        }
    }

    /**
     * @param reload 是否需要重新加入等待队列
     */
    private void cancel(final boolean reload) {
        if (null != mSearchIpcCall && !mSearchIpcCall.isCanceled()) {
            if (reload) {
                synchronized (this) {
                    needLoadMap.putAll(loadingMap);
                    loadingMap.clear();
                }
            }

            mSearchIpcCall.cancel();
            mSearchIpcCall = null;
        }
    }

    private SearchIpcHelper() {
    }

    private final static class Holder {
        private static final SearchIpcHelper INSTANCE = new SearchIpcHelper();
    }

    public static SearchIpcHelper get() {
        return Holder.INSTANCE;
    }
}
