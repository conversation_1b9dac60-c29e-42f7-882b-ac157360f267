package com.dinsafer.module_heartlai;

import android.content.Context;
import android.util.Log;
import android.view.Surface;

import com.dinsafer.dssupport.utils.DDLog;
import com.heartlai.ipc.JniClient;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 心赖JniClient静态代理类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2020/10/16 5:14 PM
 */
public class HeartLaiJniClientProxy {


    private volatile static HeartLaiJniClientProxy INSTANCE;

    private static final ExecutorService mExecutorService = Executors.newCachedThreadPool();

    private HeartLaiJniClientProxy() {
    }

    public static HeartLaiJniClientProxy getInstance() {
        if (null == INSTANCE) {
            synchronized (HeartLaiJniClientProxy.class) {
                INSTANCE = new HeartLaiJniClientProxy();
            }
        }

        return INSTANCE;
    }

    /* ************************** 代理方法，开始******************************* */

    public static void init() {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.init();
            }
        });
    }

    public static void free() {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.free();
            }
        });
    }

    public static int startP2p(String var0, String var1, String var2, String var3) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                DDLog.d("heartlai", "HeartLaiJniClientProxy stopP2p: " + var0);
                JniClient.stopP2p(var0);
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                DDLog.d("heartlai", "HeartLaiJniClientProxy startP2p: " + var0 + " /var1:" + var1 + " /var2:" + var2 + " /var3:" + var3);
                JniClient.startP2p(var0, var1, var2, var3);
            }
        });
        return 0;
    }

    public static int stopP2p(String var0) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.stopP2p(var0);
            }
        });
        return 0;
    }

    public static void stopAll() {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.stopAll();
            }
        });
    }

    public static int deleteP2p(String var0) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.deleteP2p(var0);
            }
        });
        return 0;
    }

    public static void setCallBackContext(Context var0) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.setCallBackContext(var0);
            }
        });
    }

    public static int transferMessage(String var0, String var1, int var2) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.transferMessage(var0, var1, var2);
            }
        });
        return 0;
    }

    public static void startSearch() {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.startSearch();
            }
        });
    }

    public static void stopSearch() {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.stopSearch();
            }
        });
    }

    public static void setSurface(Surface var0) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.setSurface(var0);
            }
        });
    }

    public static int startPPPPLiveStream(String var0, int var1, int var2) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.startPPPPLiveStream(var0, var1, var2);
            }
        });
        return 0;
    }

    public static int stopPPPPLiveStream(String var0) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.stopPPPPLiveStream(var0);
            }
        });
        return 0;
    }

    public static int resetLiveBuffer(String var0) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.resetLiveBuffer(var0);
            }
        });
        return 0;
    }

    public static void takePic(String var0) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.takePic(var0);
            }
        });
    }

    public static void takeVideo(String var0, String var1, boolean var2) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.takeVideo(var0, var1, var2);
            }
        });
    }

    public static void realTimeRecord(String var0, String var1, boolean var2) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.realTimeRecord(var0, var1, var2);
            }
        });
    }

    public static void playBackRecord(String var0, String var1, boolean var2) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.playBackRecord(var0, var1, var2);
            }
        });
    }

    public static int pPPPStartAudio(String var0) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.pPPPStartAudio(var0);
            }
        });
        return 0;
    }

    public static int pPPPStopAudio(String var0) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.pPPPStopAudio(var0);
            }
        });
        return 0;
    }

    public static int pPPPStartTalk(String var0) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.pPPPStartTalk(var0);
            }
        });
        return 0;
    }

    public static int pPPPStopTalk(String var0) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.pPPPStopTalk(var0);
            }
        });
        return 0;
    }

    public static int pPPPTalkAudioData(String var0, byte[] var1, int var2) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.pPPPTalkAudioData(var0, var1, var2);
            }
        });
        return 0;
    }

    public static int pPPPNetworkDetect() {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.pPPPNetworkDetect();
            }
        });
        return 0;
    }

    public static int pPPPInitial(String var0) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.pPPPInitial(var0);
            }
        });
        return 0;
    }

    public static int startPlayBack(String var0, String var1, int var2) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.startPlayBack(var0, var1, var2);
            }
        });
        return 0;
    }

    public static int stopPlayBack(String var0) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.stopPlayBack(var0);
            }
        });
        return 0;
    }

    public static int pPPPSendFileData(String var0, byte[] var1, int var2, int var3, int var4) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.pPPPSendFileData(var0, var1, var2, var3, var4);
            }
        });
        return 0;
    }

    public static int pPPPCheckReadBuffer(String var0, int var1) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.pPPPCheckReadBuffer(var0, var1);
            }
        });
        return 0;
    }

    public static int pPPPCheckWriteBuffer(String var0, int var1) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.pPPPCheckWriteBuffer(var0, var1);
            }
        });
        return 0;
    }

    public static void yUV4202RGB565(byte[] var0, byte[] var1, int var2, int var3) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.yUV4202RGB565(var0, var1, var2, var3);
            }
        });
    }

    public static void setPhoneScreen(int var0, int var1) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.setPhoneScreen(var0, var1);
            }
        });
    }

    public static void hwySocketInit(String var0) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.hwySocketInit(var0);
            }
        });
    }

    public static void sendMsgHwy(String var0, String var1) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.sendMsgHwy(var0, var1);
            }
        });
    }

    public static void hwyPlayer(String var0, int var1) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.hwyPlayer(var0, var1);
            }
        });
    }

    public static void hwySocketClose() {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.hwySocketClose();
            }
        });
    }

    public static int checkIpcStatus(String var0) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.checkIpcStatus(var0);
            }
        });
        return 0;
    }

    public static void test(String var0, String var1) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.test(var0, var1);
            }
        });
    }
    /* ************************** 代理方法，结束******************************* */

    /* ************************** 自定义方法，开始******************************* */

    /**
     * 初始化心赖摄像头
     */
    public static void initIPC() {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                Log.d("heartlai", "run: 初始化心赖摄像头");
                JniClient.init();
                JniClient.pPPPInitial("heartlai");
                JniClient.pPPPNetworkDetect();
            }
        });
    }

    /**
     * 重新连接
     *
     * @param var0
     * @param var1
     * @param var2
     * @param var3
     * @return
     */
    public static int reconnect(String var0, String var1, String var2, String var3) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                JniClient.stopP2p(var0);
//                JniClient.deleteP2p(var0);
                JniClient.startP2p(var0, var1, var2, "heartlai");
            }
        });
        return 0;
    }

    /**
     * 断开连接
     *
     * @param var0
     * @return
     */
    public static int disconnect(String var0) {
        mExecutorService.submit(new Runnable() {
            @Override
            public void run() {
                DDLog.d("heartlai", "HeartLaiJniClientProxy disconnect: " + var0);
                JniClient.stopP2p(var0);
//                JniClient.deleteP2p(var0);
            }
        });
        return 0;
    }

    /* ************************** 自定义方法，结束******************************* */


}
