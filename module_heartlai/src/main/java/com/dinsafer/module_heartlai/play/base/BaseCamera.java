package com.dinsafer.module_heartlai.play.base;

import com.dinsafer.module_heartlai.play.player.IPlayer;

import java.util.LinkedList;

/**
 * 摄像头基类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/27 6:18 PM
 */
public abstract class BaseCamera implements ICamera {
    protected IPlayer mPlayer;
    protected ICameraConnector mConnector;
    protected final LinkedList<ICameraStateCallback> mConnectCallbacks = new LinkedList<>();
    protected final LinkedList<IPlayerListener> mPlayerCallbacks = new LinkedList<>();

    protected @CameraConstant.CameraConnectState
    int mCameraConnectState;

    protected boolean mPlaying, mListening, mTalking;

    @Override
    public boolean isConnecting() {
        return CameraConstant.CONNECTING == mCameraConnectState;
    }

    @Override
    public boolean isConnected() {
        return CameraConstant.CONNECTED == mCameraConnectState;
    }

    @Override
    public boolean isOffline() {
        return CameraConstant.OFFLINE == mCameraConnectState;
    }

    @Override
    public boolean isWrongPassword() {
        return CameraConstant.WRONG_PASSWORD == mCameraConnectState;
    }


    @Override
    public boolean isListening() {
        return mListening;
    }

    @Override
    public boolean isTalking() {
        return mTalking;
    }

    @Override
    public boolean isPlaying() {
        return mPlaying;
    }

    @Override
    public IPlayer getCameraPlayer() {
        return mPlayer;
    }

    @Override
    public ICameraConnector getCameraConnector() {
        return mConnector;
    }

    @Override
    public int getCameraConnectState() {
        return mCameraConnectState;
    }

    @Override
    public void addCameraStateCallback(ICameraStateCallback callBack) {
        if (null == callBack) {
            return;
        }
        synchronized (mConnectCallbacks) {
            mConnectCallbacks.add(callBack);
        }
    }

    @Override
    public void removeCameraStateCallback(ICameraStateCallback callBack) {
        if (null == callBack) {
            return;
        }
        synchronized (mConnectCallbacks) {
            mConnectCallbacks.remove(callBack);
        }
    }

    @Override
    public void addPlayCallback(IPlayerListener playerCallback) {
        if (null == playerCallback) {
            return;
        }
        synchronized (mPlayerCallbacks) {
            mPlayerCallbacks.add(playerCallback);
        }
    }

    @Override
    public void removePlayCallback(IPlayerListener playerCallback) {
        if (null == playerCallback) {
            return;
        }
        synchronized (mPlayerCallbacks) {
            mPlayerCallbacks.remove(playerCallback);
        }
    }

    @Override
    public void destroy() {

    }
}
