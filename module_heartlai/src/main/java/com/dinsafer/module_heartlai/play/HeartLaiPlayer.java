package com.dinsafer.module_heartlai.play;

import android.app.Activity;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.Point;
import android.media.ThumbnailUtils;
import android.os.Build;
import android.os.Handler;
import android.os.Message;
import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dssupport.msctlib.db.KV;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_heartlai.HeartLaiCmd;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.module_heartlai.HeartLaiDevice;
import com.dinsafer.module_heartlai.HeartLaiJniClientProxy;
import com.dinsafer.module_heartlai.HeartLaiServiceManager;
import com.dinsafer.module_heartlai.play.base.IPlayerListener;
import com.dinsafer.module_heartlai.play.player.IPlayer;
import com.dinsafer.module_heartlai.play.player.IPlayerSnapshotCallback;
import com.dinsafer.module_heartlai.util.HeartLaiCache;
import com.heartlai.ipc.BridgeService;
import com.heartlai.ipc.entity.CustomBuffer;
import com.heartlai.ipc.entity.CustomBufferData;
import com.heartlai.ipc.entity.CustomBufferHead;
import com.heartlai.ipc.utils.AudioPlayer;
import com.heartlai.ipc.utils.CustomAudioRecorder;
import com.heartlai.ipc.utils.ScreenUtil;
import com.heartlai.ipc.widget.MyLiveViewGLMonitor;

import org.json.JSONObject;

import java.io.File;
import java.io.FileOutputStream;
import java.lang.ref.WeakReference;
import java.nio.ByteBuffer;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Keep
public class HeartLaiPlayer implements IPlayer, BridgeService.IpcamClientInterface, BridgeService.TakePicInterface, CustomAudioRecorder.AudioRecordResult, BridgeService.PlayInterface, BridgeService.IPPlayInterface, View.OnTouchListener, MyLiveViewGLMonitor.OnfigCangeInterface {

    private static final String TAG = HeartLaiPlayer.class.getSimpleName();

    private String pid;

    private String uname;

    private String pwd;

    private String name;

    private HeartLaiDevice vo;

    private MyLiveViewGLMonitor mMonitor;

    private IPlayerListener playerListener;

    private IPlayerSnapshotCallback snapshotCall;

    private CustomAudioRecorder customAudioRecorder = null;  // 音频录制

    private CustomBuffer audioBuffer = null;

    private AudioPlayer audioPlayer = null;  // 音频 播放

    boolean isListen = false;

    boolean isTalking = false;

    boolean isPlaying = false;

    private static final int AUDIO_BUFFER_START_CODE = 0xff00ff;

    private Activity activity;

    private byte[] videodata = null;
    private int videoDataLen = 0;
    private int nVideoWidth = 0;
    private int nVideoHeight = 0;
    boolean isOneShow = true;
    boolean bDisplayFinished = false;
    boolean isStartAudio = false;
    private MyHander myHander;
    boolean isCallExit;
    boolean isDataComeOn = false;
    //   记录上一次摄像头的状态
    int lastCamStatus = HeartLaiConstants.PPPP_STATUS_CONNECTING;

    private ExecutorService executorService = Executors.newCachedThreadPool();
    private final Runnable autoTakePictureTask = () -> {
        mAutoTakePicture = true;
        getSnapshot(null);
    };
    private String mQrCodeId;

    private HeartLaiPlayer(Builder builder) {
        pid = builder.pid;
        uname = builder.user;
        pwd = builder.pwd;
        name = builder.name;
        mMonitor = builder.monitor;
        activity = builder.activity;
        vo = (HeartLaiDevice) builder.device;
    }

    public static Builder newBuilder() {
        return new Builder();
    }


    @Override
    public void loadData() {
//        vo = HeartLaiServiceManager.getInstance().getDeviceByPid(pid);
        if (vo == null) {
            vo = new HeartLaiDevice();
        }
        vo.setCameraPID(pid);
        vo.setUid(uname);
        vo.setPwd(pwd);
        vo.setName(name);
        mQrCodeId = vo.getId();

        audioBuffer = new CustomBuffer();
        audioPlayer = new AudioPlayer(audioBuffer);
        customAudioRecorder = new CustomAudioRecorder(this);
        BridgeService.addTakePicInterface(this);
        HeartLaiServiceManager.getInstance().addPlayInterface(this);
        HeartLaiServiceManager.getInstance().addIpcamClientInterface(this);

//        mMonitor.setOnTouchListener(this);
//        mMonitor.setOnFigListener(this);
        mMonitor.setEnabled(false);
        mMonitor.setScreenSize(ScreenUtil.getScreenWidth(activity.getApplicationContext()),
                ScreenUtil.getScreenHeight(activity.getApplicationContext()));

        myHander = new MyHander(activity);
    }

    @Override
    public void play() {
        executorService.submit(new Runnable() {
            @Override
            public void run() {
                try {
                    vo.startLive();
                    startPlay = true;
                    isPlaying = true;
                    if (playerListener != null) {
                        playerListener.onPlaying();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    @Override
    public void pausePlay() {
        vo.stopLive();
    }

    @Override
    public void resumePlay() {

    }

    @Override
    public void stopPlay() {

    }

    @Override
    public void destory() {
        cancelAutoTakePicture();

        synchronized (mPlayerListener) {
            for (IPlayerListener mPlayerListener : mPlayerListener) {
                mPlayerListener.onDestroy();
            }
        }

        vo.stopLive();
        isPlaying = false;
        stopListen();

        BridgeService.removeTakePicInterface(this);
        HeartLaiServiceManager.getInstance().removePlayInterface(this);
        HeartLaiServiceManager.getInstance().removeIpcamClientInterface(this);
        if (customAudioRecorder != null) {
            customAudioRecorder.releaseRecord();
        }
        activity = null;
    }

    @Override
    public void startListen() {
        synchronized (this) {
            audioBuffer.ClearAll();
            audioPlayer.AudioPlayStart();
            vo.startListen();
            isListen = true;
        }
    }

    @Override
    public void startTalk() {
        if (customAudioRecorder != null) {
            isTalking = true;
            vo.startTalk();
            customAudioRecorder.StartRecord();
        }
    }

    @Override
    public void stopListen() {
        synchronized (this) {
            if (audioPlayer != null)
                audioPlayer.AudioPlayStop();
            if (audioBuffer != null)
                audioBuffer.ClearAll();
            vo.stopListen();
            isListen = false;
        }
    }

    @Override
    public void stopTalk() {
        if (customAudioRecorder != null) {
            isTalking = false;
            vo.stopTalk();
            customAudioRecorder.StopRecord();
        }
    }

    @Override
    public boolean isConnected() {
        return vo.isConnected();
    }

    @Override
    public boolean isListening() {
        return isListen;
    }

    @Override
    public boolean isTalking() {
        return isTalking;
    }

    @Override
    public void getSnapshot(IPlayerSnapshotCallback iPlayerSnapshotCallback) {
        this.snapshotCall = iPlayerSnapshotCallback;
        Map<String, Object> args = new HashMap<>();
        args.put("cmd", HeartLaiCmd.CMD_SNAPSHOT);
        vo.submit(args);
    }

    @Override
    public boolean isPlaying() {
        return isPlaying;
    }

    @Override
    public void BSMsgNotifyData(String s, int i, int i1) {
        DDLog.i(TAG, "ipc 状态变化1111：" + i1 + " /lastCamStatus:" + lastCamStatus + " /isPlaying:" + isPlaying);
        if (vo == null || !vo.getCameraPID().equals(s)) {
            return;
        }
        vo.setPPPPStatus(i1);
        DDLog.i(TAG, "ipc 状态变化：" + i1);
        if (vo.getPPPPStatus() == HeartLaiConstants.PPPP_STATUS_CONNECTING) {
            lastCamStatus = vo.getPPPPStatus();
        } else if (vo.getPPPPStatus() == HeartLaiConstants.PPPP_STATUS_ON_LINE && lastCamStatus == HeartLaiConstants.PPPP_STATUS_CONNECTING && isPlaying) {
//            这里是为了处理当在直播的时候，出现了断开ipc，sdk又自动重连，直播画面卡主问题
//            当判断出当前ipc是处于正在播放的情况下，并且经过断开重连的情况下，需要重新调用startpppplive和startaudio
            DDLog.i(TAG, "ipc 断开了，并且重连上了，需要重新播放了");
            lastCamStatus = vo.getPPPPStatus();
//            必须先停止后播放，否则还是会卡主
            pausePlay();
            play();
            if (isListen) {
                startListen();
            }

        }
    }

    @Override
    public void BSSnapshotNotify(String s, byte[] bytes, int i) {

    }

    @Override
    public void callBackUserParams(String s, String s1, String s2, String s3, String s4, String s5, String s6) {

    }

    @Override
    public void callBackConnectLook(String s, int i, int i1) {

    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getUname() {
        return uname;
    }

    public void setUname(String uname) {
        this.uname = uname;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public HeartLaiDevice getVo() {
        return vo;
    }

    public void setVo(HeartLaiDevice vo) {
        this.vo = vo;
    }

    public MyLiveViewGLMonitor getMonitor() {
        return mMonitor;
    }

    public void setMonitor(MyLiveViewGLMonitor monitor) {
        this.mMonitor = monitor;
    }

    public IPlayerListener getPlayerListener() {
        return playerListener;
    }

    public void setPlayerListener(IPlayerListener playerListener) {
        this.playerListener = playerListener;
    }

    @RequiresApi(api = Build.VERSION_CODES.CUPCAKE)
    @Override
    public void onTakePic(byte[] buf, int width, int height) {
        ByteBuffer buffer = ByteBuffer.wrap(buf);
        Bitmap mBmp = Bitmap
                .createBitmap(width, height, Bitmap.Config.RGB_565);
        mBmp.copyPixelsFromBuffer(buffer);
        DDLog.i("HeartLai", "onTakePic:" + (mBmp != null));

//        savePicture(mBmp);

        if (mBmp != null) {
            if (snapshotCall != null) {
                snapshotCall.onSnapshot(mBmp);
            }
        }
    }

    //     录音的数据回调 用于传送给ipc
    @Override
    public void AudioRecordData(byte[] data, int len) {
        if (isTalking && len > 0) {
            HeartLaiJniClientProxy.pPPPTalkAudioData(vo.getCameraPID(), data, len);
        }
    }

    @Override
    public void callBackMessageNotify(String s, int i, int i1) {

    }

    //    音频回调
    @Override
    public void callBackAudioData(byte[] pcm, int len) {
        if (!audioPlayer.isAudioPlaying()) {
            return;
        }

        if (!isListen) {
            return;
        }
        CustomBufferHead head = new CustomBufferHead();
        CustomBufferData data = new CustomBufferData();
        head.length = len;
        head.startcode = AUDIO_BUFFER_START_CODE;
        data.head = head;
        data.data = pcm;
        audioBuffer.addData(data);
    }

    @Override
    public void callBackH264Data(byte[] bytes, int i, int i1) {

    }


    @Override
    public void callBackCameraParamNotify(String did, int resolution, int brightness, int contrast, int hue, int saturation, int flip) {

    }

    @Override
    public void callBaceVideoData(String did, byte[] videobuf, int h264Data, int len, int width, int height) {
        if (!did.equals(vo.getCameraPID())) return;
        if (startPlay) {
            autoTakePicture();

            synchronized (mPlayerListener) {
                for (IPlayerListener mPlayerListener : mPlayerListener) {
                    mPlayerListener.onPlay();
                }
                startPlay = false;
            }
        }

        bDisplayFinished = false;

        videodata = videobuf;
        videoDataLen = len;
        Message msg = new Message();

        // mBmp = Bitmap.createBitmap(videobuf, width, height,
        // Bitmap.Config.RGB_565);
        // h264Data = 0;

        nVideoWidth = width;
        nVideoHeight = height;


        msg.what = 1;
        myHander.sendMessage(msg);
    }


    class MyHander extends Handler {
        WeakReference<Activity> reference;

        public MyHander(Activity activity) {
            reference = new WeakReference<>(activity);
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);

            if (null == reference || null == reference.get()) {
                return;
            }
            Activity activity = reference.get();

            if (isOneShow) {
                Log.e("realtime", "run: onrender");
                isDataComeOn = true;
                isOneShow = false;

            }
            int width = activity.getWindowManager().getDefaultDisplay().getWidth();
            int height = activity.getWindowManager().getDefaultDisplay().getHeight();
            if (activity.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
//					 setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
                FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(
                        width, width * 2 / 3);
//                lp.gravity = Gravity.CENTER;
                mMonitor.setLayoutParams(lp);
            } else if (activity.getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE) {
//					 setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
                FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(
                        width, height);
                lp.gravity = Gravity.CENTER;
                mMonitor.setLayoutParams(lp);
            }

            mMonitor.setYuvFrameData(videodata, nVideoWidth, nVideoHeight);

            bDisplayFinished = true;
        }
    }


    private boolean isDown = false;
    private boolean isSecondDown = false;
    private float x1 = 0;
    private float x2 = 0;
    private float y1 = 0;
    private float y2 = 0;
    boolean isHD = false;
    int xlenOld;
    int ylenOld;
    float lastX;
    float lastY;
    double nLenStart = 0;
    private float action_down_x;
    private float action_down_y;
    float move_x;
    float move_y;
    boolean isPort = false;


    public boolean onTouch(View view, MotionEvent event) {

        //TODO
//        if (view.getId() == R.id.glmonitor) {
        if (view.getId() == 0) {

            int nCnt = event.getPointerCount();

            // int n = event.getAction();
            if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_POINTER_DOWN
                    && 2 == nCnt)// <span
            // style="color:#ff0000;">2表示两个手指</span>
            {

                mMonitor.setTouchMove(2);
                // mMonitor.setState(3);
                for (int i = 0; i < nCnt; i++) {
                    float x = event.getX(i);
                    float y = event.getY(i);
                    Point pt = new Point((int) x, (int) y);
                }

                xlenOld = Math.abs((int) event.getX(0) - (int) event.getX(1));
                ylenOld = Math.abs((int) event.getY(0) - (int) event.getY(1));

                // HiLog.e("event.getX(0):"+(int)event.getX(0)+"(int)event.getX(1):"+(int)event.getX(1));

                nLenStart = Math.sqrt((double) xlenOld * xlenOld
                        + (double) ylenOld * ylenOld);

            } else if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_MOVE
                    && 2 == nCnt) {

                mMonitor.setTouchMove(2);
                // mMonitor.setState(3);
                for (int i = 0; i < nCnt; i++) {
                    float x = event.getX(i);
                    float y = event.getY(i);

                    Point pt = new Point((int) x, (int) y);

                }

                int xlen = Math.abs((int) event.getX(0) - (int) event.getX(1));
                int ylen = Math.abs((int) event.getY(0) - (int) event.getY(1));

                int moveX = Math.abs(xlen - xlenOld);
                int moveY = Math.abs(ylen - ylenOld);

                double nLenEnd = Math.sqrt((double) xlen * xlen + (double) ylen
                        * ylen);
                if (moveX < 20 && moveY < 20) {
                    return false;
                }

                if (!isPort) {
                    if (nLenEnd > nLenStart) {
                        resetMonitorSize(true, nLenEnd);
                    } else {
                        resetMonitorSize(false, nLenEnd);
                    }
                }

                xlenOld = xlen;
                ylenOld = ylen;
                nLenStart = nLenEnd;

                return true;
            } else if (nCnt == 1) {

                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        action_down_x = event.getRawX();
                        action_down_y = event.getRawY();

                        lastX = action_down_x;
                        lastY = action_down_y;

                        x1 = event.getX();
                        y1 = event.getY();

                        // HiLog.e("ACTION_DOWN");
                        mMonitor.setTouchMove(0);
                        break;
                    case MotionEvent.ACTION_MOVE:
                        if (!isDown) {
                            x1 = event.getX();
                            y1 = event.getY();
                            isDown = true;
                        }
                        x2 = event.getX();
                        y2 = event.getY();
                        if (mMonitor.getTouchMove() != 0)
                            break;
                        move_x = event.getRawX();
                        move_y = event.getRawY();


                        if (Math.abs(move_x - action_down_x) > 40
                                || Math.abs(move_y - action_down_y) > 40) {
                            mMonitor.setTouchMove(1);
                        }

                        break;
                    case MotionEvent.ACTION_UP: {
                        x2 = event.getX();
                        y2 = event.getY();

                        if (Math.abs((x1 - x2)) < 50 && Math.abs((y1 - y2)) < 50) {

                            isSecondDown = false;
                        } else {
                        }
                        x1 = 0;
                        x2 = 0;
                        y1 = 0;
                        y2 = 0;
                        isDown = false;

                        if (mMonitor.getTouchMove() != 0) {
                            break;
                        }

                        break;
                    }
                    default:
                        break;
                }
            }

            return false;
        }
//        if (event.getAction() == MotionEvent.ACTION_DOWN){
//            StartTalk();
//
//        }else if (event.getAction() == MotionEvent.ACTION_UP){
//            StopTalk();
//        }
        return false;
    }


    @Override
    public void CallBackOnfigStatu(int var1) {

    }


    int moveX;
    int moveY;

    private void resetMonitorSize(boolean large, double move) {

        if (mMonitor.height == 0 && mMonitor.width == 0) {
            initMatrix((int) mMonitor.screen_width,
                    (int) mMonitor.screen_height);

        }


        if (move == 0) {
            mMonitor.setState(0);
            mMonitor.setMatrix(0, 0, (int) mMonitor.screen_width,
                    (int) mMonitor.screen_height);
            return;
        }


        moveX = (int) (move / 2);
        moveY = (int) ((move * mMonitor.screen_height / mMonitor.screen_width) / 2);

        if (large) {
            if (mMonitor.width <= 2 * mMonitor.screen_width
                    && mMonitor.height <= 2 * mMonitor.screen_height) {
                mMonitor.left -= (moveX / 2);
                mMonitor.bottom -= (moveY / 2);
                mMonitor.width += (moveX);
                mMonitor.height += (moveY);
            }
        } else {
            mMonitor.left += (moveX / 2);
            mMonitor.bottom += (moveY / 2);
            mMonitor.width -= (moveX);
            mMonitor.height -= (moveY);
        }

        if (mMonitor.left > 0 || mMonitor.width < (int) mMonitor.screen_width
                || mMonitor.height < (int) mMonitor.screen_height
                || mMonitor.bottom > 0) {
            initMatrix((int) mMonitor.screen_width,
                    (int) mMonitor.screen_height);
        }

        if (mMonitor.width > (int) mMonitor.screen_width) {
            mMonitor.setState(1);
        } else {
            mMonitor.setState(0);
        }

        mMonitor.setMatrix(mMonitor.left, mMonitor.bottom, mMonitor.width,
                mMonitor.height);

    }

    private void initMatrix(int screen_width, int screen_height) {
        mMonitor.left = 0;
        mMonitor.bottom = 0;

        mMonitor.width = screen_width;
        mMonitor.height = screen_height;
    }

    @Keep
    public static final class Builder {
        public Activity activity;
        private String pid;
        private String user;
        private String pwd;
        private String name;
        private MyLiveViewGLMonitor monitor;
        private Device device;

        private Builder() {
        }

        public Builder pid(String pid) {
            this.pid = pid;
            return this;
        }

        public Builder user(String user) {
            this.user = user;
            return this;
        }

        public Builder pwd(String pwd) {
            this.pwd = pwd;
            return this;
        }

        public Builder name(String name) {
            this.name = name;
            return this;
        }

        public Builder monitor(MyLiveViewGLMonitor monitor) {
            this.monitor = monitor;
            return this;
        }

        public Builder activity(Activity activity) {
            this.activity = activity;
            return this;
        }

        public Builder device(Device device) {
            this.device = device;
            return this;
        }

        public HeartLaiPlayer build() {
            return new HeartLaiPlayer(this);
        }
    }

    private void autoTakePicture() {
        DDLog.i(TAG, "autoTakePicture");
        if (myHander != null) {
            myHander.postDelayed(autoTakePictureTask, 2 * 1000);
        }
    }

    private void cancelAutoTakePicture() {
        DDLog.i(TAG, "cancelAutoTakePicture");
        if (myHander != null) {
            myHander.removeCallbacksAndMessages(autoTakePictureTask);
        }
    }

    private boolean startPlay;
    private boolean mAutoTakePicture;
    private final ArrayList<IPlayerListener> mPlayerListener = new ArrayList<>();

    public void addPlayListener(IPlayerListener playerListener) {
        if (null == playerListener) {
            return;
        }
        synchronized (mPlayerListener) {
            mPlayerListener.add(playerListener);
        }
    }

    public void removePlayListener(IPlayerListener playerListener) {
        if (null == playerListener) {
            return;
        }
        synchronized (mPlayerListener) {
            mPlayerListener.add(playerListener);
        }
    }

    private void savePicture(Bitmap bitmap) {
        DDLog.i(TAG, "save picture, auto: " + mAutoTakePicture);
        if (bitmap == null || TextUtils.isEmpty(mQrCodeId)) {
            DDLog.e(TAG, "Error on save picture.");
            return;
        }
        final String ipcId = mQrCodeId;
        if (mAutoTakePicture) {
            try {
                DDLog.i(TAG, "自动化截图");
                // 将图片截小；
                Bitmap ipcImage = ThumbnailUtils.extractThumbnail(bitmap, HeartLaiConstants.IMAGE_WIDTH, HeartLaiConstants.IMAGE_HIGH,
                        ThumbnailUtils.OPTIONS_RECYCLE_INPUT);

                String genPath = HeartLaiCache.getInstance().getImageFolder();
                File genFile = new File(genPath);
                if (!genFile.exists()) {
                    genFile.mkdirs();
                }

                String path = genPath + ".ipc";
                File file = new File(path);
                if (!file.exists()) {
                    file.mkdir();
                }
                //fileName为文件绝对路径+文件名
                String fileName = path + "/" + ipcId + ".jpg";
                File snapshotFile = new File(fileName);
                if (snapshotFile.exists()) {
                    snapshotFile.delete();
                }
                if (!file.exists()) {
                    file.mkdir();
                }
                snapshotFile.createNewFile();
                FileOutputStream fos = new FileOutputStream(snapshotFile);
                ipcImage.compress(Bitmap.CompressFormat.JPEG, 100, fos);
                fos.flush();
                fos.close();

                JSONObject jsonObject;
                if (KV.containKey(ipcId)) {
                    jsonObject = new JSONObject(KV.getString(ipcId, ""));
                } else {
                    jsonObject = new JSONObject();
                }

                jsonObject.put(HeartLaiConstants.ATTR_SNAPSHOT, fileName);
                jsonObject.put(HeartLaiConstants.LAST_OPEN_TIME, (System.currentTimeMillis()));
//                model.setSnapshot(fileName);
                KV.putString(ipcId, jsonObject.toString());
//                这里如果没有用到这个事件的话,就会出现白屏
//                EventBus.getDefault().post(new IPCUpdateEvent());
                DDLog.i(TAG, "自动化截图 完毕: " + jsonObject.toString());
            } catch (Exception e) {
                DDLog.e(TAG, "保存自动化截图错误");
                e.printStackTrace();
            }
            mAutoTakePicture = false;
        } else {
//        用户主动点击时候的截图
            DDLog.i(TAG, "截图 开始");
            Bitmap ipcImage = ThumbnailUtils.extractThumbnail(bitmap, HeartLaiConstants.IMAGE_WIDTH, HeartLaiConstants.IMAGE_HIGH,
                    ThumbnailUtils.OPTIONS_RECYCLE_INPUT);

            File file = new File(HeartLaiCache.getInstance().getImageFolder());
            if (!file.exists()) {
                file.mkdirs();
            }

            String strDate = new SimpleDateFormat("yyyy.MM.dd_HH.mm.ss").format(new Date());
            //fileName为文件绝对路径+文件名
            String fileName = HeartLaiCache.getInstance().getImageFolder() + ipcId + "_" + strDate + ".jpg";
            File snapshotFile = new File(fileName);
            if (snapshotFile.exists()) {
                snapshotFile.delete();
            }
            try {
                snapshotFile.createNewFile();
                FileOutputStream fos = new FileOutputStream(snapshotFile);
                ipcImage.compress(Bitmap.CompressFormat.JPEG, 100, fos);
                fos.flush();
                fos.close();
                HeartLaiCache.updatePhoto(activity, snapshotFile);
                DDLog.i(TAG, "手动截图 完毕: " + fileName);
            } catch (Exception e) {
                DDLog.e(TAG, "手动保存截图错误");
                e.printStackTrace();
            }
        }
    }
}


