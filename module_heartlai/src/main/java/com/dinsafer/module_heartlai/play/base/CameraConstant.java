package com.dinsafer.module_heartlai.play.base;

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * 摄像头相关常量
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/27 6:11 PM
 */
public class CameraConstant {
    /**
     * 摄像头连接状态定义
     */
    public static final int OFFLINE = 0;
    public static final int CONNECTING = 1;
    public static final int CONNECTED = 2;
    public static final int WRONG_PASSWORD = 3;

    @IntDef({OFFLINE, CONNECTING, CONNECTED, WRONG_PASSWORD})
    @Retention(RetentionPolicy.SOURCE)
    public @interface CameraConnectState {
    }
}
