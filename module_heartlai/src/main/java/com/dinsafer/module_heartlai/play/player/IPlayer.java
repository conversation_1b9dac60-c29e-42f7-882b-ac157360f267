package com.dinsafer.module_heartlai.play.player;

import androidx.annotation.Keep;

/**
 * 播放器接口
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/27 5:53 PM
 */
@Keep
public interface IPlayer {


    void loadData();

    boolean isConnected();

    void play();

    void pausePlay();

    void resumePlay();

    void stopPlay();

    boolean isPlaying();

    void destory();

    void startListen();

    void stopListen();

    boolean isListening();

    void startTalk();

    void stopTalk();

    boolean isTalking();

    void getSnapshot(IPlayerSnapshotCallback iPlayerSnapshotCallback);

}
