package com.dinsafer.module_heartlai.play.base;

/**
 * 摄像头连接状态回调
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/27 5:50 PM
 */
public interface ICameraStateCallback {
    /**
     * 连接成功
     *
     * @param cameraId 摄像头ID
     */
    void onConnected(String cameraId);

    /**
     * 断开连接
     *
     * @param cameraId 摄像头ID
     */
    void onDisconnected(String cameraId);

    /**
     * 连接中
     *
     * @param cameraId 摄像头ID
     */
    void onConnecting(String cameraId);


    /**
     * 密码错误
     *
     * @param cameraId 摄像头ID
     */
    void onWrongPassword(String cameraId);

}
