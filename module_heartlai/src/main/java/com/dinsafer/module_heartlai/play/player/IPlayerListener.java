package com.dinsafer.module_heartlai.play.base;

import androidx.annotation.Keep;

/**
 * 播放器播放状态回调
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/27 6:02 PM
 */
@Keep
public interface IPlayerListener {

    void onLoadData();

    void onPlay();

    void onPlaying();

    void onPausePlay();

    void onResumePlay();

    void onStopPlay();

    void onDestroy();

    void onStartListen();

    void onStartTalk();

    void onStopListen();

    void onStopTalk();

    void onGetSnapshot();

    boolean onStartListening();

    boolean onStartTalking();

}
