package com.dinsafer.module_heartlai.play.player;

import androidx.annotation.Keep;

@Keep
public class DinsaferPlayer {

    private IPlayer iPlayer;

    public IPlayer getPlayer() {
        return iPlayer;
    }

    public void setPlayer(IPlayer iPlayer) {
        this.iPlayer = iPlayer;
    }

    public void startPlay() {
        if (iPlayer == null) {
            return;
        }
        iPlayer.loadData();
        iPlayer.play();
    }

    public void pausePlay() {
        if (iPlayer == null) {
            return;
        }
        iPlayer.pausePlay();
    }

    public void resumePlay() {
        if (iPlayer == null) {
            return;
        }
        iPlayer.resumePlay();
    }

    public void stopPlay() {
        if (iPlayer == null) {
            return;
        }
        iPlayer.stopPlay();
    }

    public void releasePlay() {
        if (iPlayer == null) {
            return;
        }
        iPlayer.destory();
    }

    public boolean isConnected() {
        if (iPlayer == null) {
            return false;
        }
        return iPlayer.isConnected();
    }

    public void startListen() {
        if (iPlayer == null) {
            return;
        }
        iPlayer.startListen();
    }

    public void stopListen() {
        if (iPlayer == null) {
            return;
        }
        iPlayer.stopListen();
    }

    public boolean isListening() {
        if (iPlayer == null) {
            return false;
        }
        return iPlayer.isListening();
    }

    public boolean isPlaying() {
        if (iPlayer == null) {
            return false;
        }
        return iPlayer.isPlaying();
    }

    /**
     * 用来保存讲话前是否在聆听的状态
     */
    boolean isNeedToResumeListening = false;

    public void startTalk() {
        if (iPlayer == null) {
            return;
        }
        if (iPlayer.isListening()) {
            isNeedToResumeListening = true;
            iPlayer.stopListen();
        }
        iPlayer.startTalk();
    }

    public void stopTalk() {
        if (iPlayer == null) {
            return;
        }
        iPlayer.stopTalk();
        if (isNeedToResumeListening) {
            isNeedToResumeListening = false;
            iPlayer.startListen();
        }
    }

    public boolean isTalking() {
        if (iPlayer == null) {
            return false;
        }
        return iPlayer.isTalking();
    }

    public void getSnapshot(IPlayerSnapshotCallback iPlayerSnapshotCallback) {
        if (iPlayer == null) {
            if (iPlayerSnapshotCallback != null) {
                iPlayerSnapshotCallback.onSnapshot(null);
            }
            return;
        }
        iPlayer.getSnapshot(iPlayerSnapshotCallback);
    }

}