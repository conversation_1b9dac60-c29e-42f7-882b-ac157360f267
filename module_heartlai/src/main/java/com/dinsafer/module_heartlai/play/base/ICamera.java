package com.dinsafer.module_heartlai.play.base;

import com.dinsafer.module_heartlai.play.player.IPlayer;
import com.dinsafer.module_heartlai.play.base.IPlayerListener;

/**
 * 摄像头管理
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/4/27 6:05 PM
 */
public interface ICamera {
    // addition
    int getCameraConnectState();

    /**
     * 获取摄像头播放管理器
     */
    IPlayer getCameraPlayer();

    /**
     * 获取摄像头连接管理器
     */
    ICameraConnector getCameraConnector();

    /**
     * 初始化
     */
    void initCamera();

    boolean isConnecting();

    boolean isConnected();

    boolean isOffline();

    boolean isWrongPassword();

    void addCameraStateCallback(ICameraStateCallback callBack);

    void removeCameraStateCallback(ICameraStateCallback callBack);

    void addPlayCallback(IPlayerListener playerCallback);

    void removePlayCallback(IPlayerListener playerCallback);

    // connector
    void startConnect();

    void stopConnect();

    void reconnect();

    // player
    void loadData();

    void play();

    void pausePlay();

    void resumePlay();

    void stopPlay();

    void destroy();

    void startListen();

    void startTalk();

    void stopListen();

    void stopTalk();

    void getSnapshot();

    boolean isListening();

    boolean isTalking();

    boolean isPlaying();
}
