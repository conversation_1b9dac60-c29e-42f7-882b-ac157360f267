package com.dinsafer.module_heartlai;

import androidx.annotation.Keep;

@Keep
public class HeartLaiConstants {

    //截图大小
    public static final int IMAGE_WIDTH = 1280;
    public static final int IMAGE_HIGH = 720;

    public static final String PROVIDER_HEARTLAI = "heartlai";
    public static final String DEFAULT_HEARTLAI_PWD = "admin";
    public static final String HEART_LAI_IPC_HD = "HEART_LAI_IPC_HD";
    public static final String LAST_OPEN_TIME = "last_open_time";

    /**
     * 摄像头状态
     */
    public static final int CAMERA_STATUS_UNKNOW = 0;
    public static final int CAMERA_STATUS_CONNECTING = 1;
    public static final int CAMERA_STATUS_ONLINE = 2;
    public static final int CAMERA_STATUS_OFFLINE = 3;
    public static final int CAMERA_STATUS_WRONG_PASSWORD = 4;

    /**
     * 心籁返回摄像头的状态
     */
    public static final int PPPP_STATUS_CONNECTING = 0;
    public static final int PPPP_STATUS_INITIALING = 1;
    public static final int PPPP_STATUS_ON_LINE = 2;
    public static final int PPPP_STATUS_CONNECT_FAILED = 3;
    public static final int PPPP_STATUS_DISCONNECT = 4;
    public static final int PPPP_STATUS_INVALID_ID = 5;
    public static final int PPPP_STATUS_DEVICE_NOT_ON_LINE = 6;
    public static final int PPPP_STATUS_CONNECT_TIMEOUT = 7;
    public static final int PPPP_STATUS_CONNECT_ERRER = 8;
    public static final int PPPP_STATUS_UNKNOWN = 0xffffffff;

    /**
     * 属性
     */
    public static final String ATTR_NAME = "name";
    public static final String ATTR_HOME_ID = "home_id";
    public static final String ATTR_CAMERA_STATUS = "cameraStatus";
    public static final String ATTR_LISTEN = "listen";
    public static final String ATTR_SHAKE = "shake";
    public static final String ATTR_TALK = "talk";
    public static final String ATTR_IS_LISTENING = "isListening";
    public static final String ATTR_IS_TALKING = "isTalking";
    public static final String ATTR_IS_LIVING = "isLiving";
    public static final String ATTR_CAMERA_PID = "cameraPID";
    public static final String ATTR_UID = "uid";
    public static final String ATTR_PASSWORD = "password";
    public static final String ATTR_TIME = "time";
    public static final String ATTR_MIRRO_MODE = "mirroMode";
    public static final String ATTR_HD_MODE = "hdMode";
    public static final String ATTR_VIDEO_MODE = "videoMode";
    public static final String ATTR_AP = "ap";
    public static final String ATTR_IPC_TYPE = "ipc_type";
    public static final String ATTR_IP_ADDRESS = "IPaddress";
    public static final String ATTR_SNAPSHOT = "snapshot";
    public static final String ATTR_PROVIDER = "provider";
    public static final String ATTR_DESCRIPTION = "description";
    public static final String ATTR_SOURCE_DATA = "sourceData";
    public static final String ATTR_WIFI_SSID = "wifi_ssid";


}
