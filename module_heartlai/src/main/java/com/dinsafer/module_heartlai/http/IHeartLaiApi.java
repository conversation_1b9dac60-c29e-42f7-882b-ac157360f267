package com.dinsafer.module_heartlai.http;

import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.module_heartlai.model.GetAlertModeResponse;
import com.dinsafer.module_heartlai.model.SearchIpcParams;
import com.dinsafer.module_heartlai.model.SearchIpcResponse;

import java.util.Map;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;
import retrofit2.http.Url;

public interface IHeartLaiApi {


    @POST()
    @FormUrlEncoded
    Call<StringResponseEntry> newRequestCall(@Url String url, @FieldMap Map<String, Object> map);

    /**
     * 添加IPC
     *
     * @param url
     * @param map
     * @return
     */
    @POST()
    @FormUrlEncoded
    Call<StringResponseEntry> getAddIPCCall(@Url String url, @FieldMap Map<String, Object> map);

    /**
     * 添加第三方IPC
     *
     * @param url
     * @param map
     * @return
     */
    @POST()
    @FormUrlEncoded
    Call<StringResponseEntry> getAddTpIPCCall(@Url String url, @FieldMap Map<String, Object> map);


    @POST()
    @FormUrlEncoded
    Call<com.dinsafer.module_heartlai.model.GetDeviceListResponse> getListTpIPCCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST()
    @FormUrlEncoded
    Call<GetAlertModeResponse> getAlertModeCall(@Url String url, @FieldMap Map<String, Object> map);


    @POST()
    @FormUrlEncoded
    Call<StringResponseEntry> setAlertModeCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST()
    @FormUrlEncoded
    Call<SearchIpcResponse> searchIpc(@Url String url, @FieldMap Map<String, Object> map);
}
