package com.dinsafer.module_heartlai.http;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.module_heartlai.model.GetAlertModeResponse;
import com.dinsafer.module_heartlai.model.GetDeviceListResponse;
import com.dinsafer.module_heartlai.model.SearchIpcParams;
import com.dinsafer.module_heartlai.model.SearchIpcResponse;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

import retrofit2.Callback;
import retrofit2.Response;

public class HeartLaiRepository {
    public void getAddIpcCall(JSONObject data, String messageid, Callback<StringResponseEntry> callback) {
        HeartLaiApi.getApi().getAddIpcCall(data, messageid).enqueue(callback);
    }


    public void getAddTpIpcCall(JSONObject data, Callback<StringResponseEntry> callback) {
        HeartLaiApi.getApi().getAddTpIpcCall(data).enqueue(callback);
    }

    @Nullable
    public Response<com.dinsafer.module_heartlai.model.GetDeviceListResponse> listTpIpcSync(String home_id, long addTime, int pageSize, boolean orderDesc) {
        List<String> providers = new ArrayList<>();
        providers.add(HeartLaiConstants.PROVIDER_HEARTLAI);
        JSONObject data = new JSONObject();

        Response<GetDeviceListResponse> response = null;
        try {
            data.put("home_id", home_id);
            JSONArray jsonArray = new JSONArray(providers);
            data.put("providers", jsonArray);
            data.put("page_size", pageSize);
            data.put("addtime", addTime);
            data.put("order", orderDesc ? "desc" : "asc");

            response = HeartLaiApi.getApi().getListTpIpcCall(data).execute();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return response;
    }

    public void delDevice(String pid, String home_id, Callback<StringResponseEntry> callback) {
        JSONObject data = new JSONObject();
        try {
            data.put("pid", pid);
            data.put("home_id", home_id);
            data.put("provider", HeartLaiConstants.PROVIDER_HEARTLAI);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        HeartLaiApi.getApi().getDelTpIpcCall(data).enqueue(callback);
    }

    public void renameDevice(String pid, String home_id, String name, Callback<StringResponseEntry> callback) {
        JSONObject data = new JSONObject();
        try {
            data.put("pid", pid);
            data.put("home_id", home_id);
            data.put("name", name);
            data.put("provider", HeartLaiConstants.PROVIDER_HEARTLAI);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        HeartLaiApi.getApi().getRenameTpIpcCall(data).enqueue(callback);
    }

    public void updateServerPassword(String pid, String home_id, String password, Callback<StringResponseEntry> callback) {
        JSONObject data = new JSONObject();
        try {
            data.put("pid", pid);
            data.put("home_id", home_id);
            data.put("password", password);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        HeartLaiApi.getApi().getUpdateTpIpcCall(data).enqueue(callback);
    }

    public void updateHDMode(String pid, String home_id, boolean hdMode, Callback<StringResponseEntry> callback) {
        JSONObject data = new JSONObject();
        try {
            data.put("pid", pid);
            data.put("home_id", home_id);
            data.put("HDmode", hdMode);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        HeartLaiApi.getApi().getUpdateTpIpcCall(data).enqueue(callback);
    }


    public void setAlertMode(String homeID, String pid, String mode, Callback<StringResponseEntry> callback) {
        HeartLaiApi.getApi().setAlertMode(homeID, pid, mode).enqueue(callback);
    }

    public void getAlertMode(String homeID, String pid, Callback<GetAlertModeResponse> callback) {
        HeartLaiApi.getApi().getAlertMode(homeID, pid).enqueue(callback);
    }

    public void searchIpc(String homeID, @NonNull List<SearchIpcParams> ipcs, Callback<SearchIpcResponse> callback) {
        HeartLaiApi.getApi().searchIpc(homeID, ipcs).enqueue(callback);
    }

}
