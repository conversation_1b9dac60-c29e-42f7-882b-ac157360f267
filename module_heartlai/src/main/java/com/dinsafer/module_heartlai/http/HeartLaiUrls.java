package com.dinsafer.module_heartlai.http;

public class HeartLaiUrls {
    static final String ADD_IPC = "/device/addipc/";

    /**
     * 添加第三方IPC
     */
    static final String ADD_THIRD_PART_IPC = "/ipc/new-tp-ipc/";

    /**
     * 获取第三方厂商IPC列表
     */
    static final String LIST_THIRD_PART_IPC = "/ipc/list-tp-ipc/";

    /**
     * 删除第三方IPC
     */
    static final String DEL_THIRD_PART_IPC = "/ipc/del-tp-ipc/";

    /**
     * 重命名
     */
    static final String RENAME_THIRD_PART_IPC = "/ipc/rename-tp-ipc/";

    /**
     * 修改第三方IPC配置
     */
    static final String UPDATE_THIRD_PART_IPC = "/ipc/update-tp-ipc/";

    /**
     * 设置AlertMode
     */
    static final String URL_SET_ALERT_MODE = "/ipc/update-ipc-service-settings/";

    /**
     * 获取AlertMode设置
     */
    static final String URL_GET_ALERT_MODE = "/ipc/get-ipc-service-settings/";

    /**
     * 查询指定 pid 的第三方 ipc
     */
    static final String URL_SEARCH_IPC = "/ipc/search-tp-ipc/";
}
