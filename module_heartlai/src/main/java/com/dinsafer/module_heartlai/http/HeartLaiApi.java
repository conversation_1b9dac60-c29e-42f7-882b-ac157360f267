package com.dinsafer.module_heartlai.http;

import androidx.annotation.NonNull;
import android.text.TextUtils;

import com.dinsafer.dincore.http.Api;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.module_heartlai.model.GetAlertModeResponse;
import com.dinsafer.module_heartlai.model.SearchIpcParams;
import com.dinsafer.module_heartlai.model.SearchIpcResponse;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import okhttp3.ResponseBody;
import retrofit2.Call;

public class HeartLaiApi {

    private final IHeartLaiApi services;
    public static final String GM = "gm";
    public static final String GMTIME = "gmtime";

    private HeartLaiApi() {
        services = Api.getApi().getRetrofit().create(IHeartLaiApi.class);
    }

    private static class Holder {
        private static final HeartLaiApi INSTANT = new HeartLaiApi();
    }

    public static HeartLaiApi getApi() {
        return Holder.INSTANT;
    }

    private Map<String, Object> getGM(Map<String, Object> map) {
        map.put(GM, 1);
        return map;
    }

    private JSONObject getGMTime(JSONObject jsonObject) {
        try {
            jsonObject.put(GMTIME, System.currentTimeMillis() * 1000);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObject;
    }

    public Call<StringResponseEntry> getAddIpcCall(JSONObject data, String messageid) {
        Map<String, Object> map = new HashMap<>();
        try {
            data.put("messageid", messageid);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", data);
        String userToken = UserManager.getInstance().getToken();
        if (!TextUtils.isEmpty(userToken)) {
            map.put("token", userToken);
        } else {
            map.put("token", "");
        }
        return services.getAddIPCCall(Api.getApi().getUrl(HeartLaiUrls.ADD_IPC), map);
    }

    public Call<StringResponseEntry> getAddTpIpcCall(JSONObject data) {
        Map<String, Object> map = new HashMap<>();
        map.put("json", getGMTime(data));
        String userToken = UserManager.getInstance().getToken();
        if (!TextUtils.isEmpty(userToken)) {
            map.put("token", userToken);
        } else {
            map.put("token", "");
        }
        return services.newRequestCall(Api.getApi().getUrl(HeartLaiUrls.ADD_THIRD_PART_IPC), map);
    }

    public Call<com.dinsafer.module_heartlai.model.GetDeviceListResponse> getListTpIpcCall(JSONObject data) {
        Map<String, Object> map = new HashMap<>();
        map.put("json", data);
        String userToken = UserManager.getInstance().getToken();
        if (!TextUtils.isEmpty(userToken)) {
            map.put("token", userToken);
        } else {
            map.put("token", "");
        }
        return services.getListTpIPCCall(Api.getApi().getUrl(HeartLaiUrls.LIST_THIRD_PART_IPC), map);
    }


    public Call<StringResponseEntry> getDelTpIpcCall(JSONObject data) {
        Map<String, Object> map = new HashMap<>();
        map.put("json", getGMTime(data));
        String userToken = UserManager.getInstance().getToken();
        if (!TextUtils.isEmpty(userToken)) {
            map.put("token", userToken);
        } else {
            map.put("token", "");
        }
        return services.newRequestCall(Api.getApi().getUrl(HeartLaiUrls.DEL_THIRD_PART_IPC), map);
    }

    public Call<StringResponseEntry> getRenameTpIpcCall(JSONObject data) {
        Map<String, Object> map = new HashMap<>();
        map.put("json", getGMTime(data));
        String userToken = UserManager.getInstance().getToken();
        if (!TextUtils.isEmpty(userToken)) {
            map.put("token", userToken);
        } else {
            map.put("token", "");
        }
        return services.newRequestCall(Api.getApi().getUrl(HeartLaiUrls.RENAME_THIRD_PART_IPC), map);
    }


    public Call<StringResponseEntry> getUpdateTpIpcCall(JSONObject data) {
        Map<String, Object> map = new HashMap<>();
        map.put("json", getGMTime(data));
        String userToken = UserManager.getInstance().getToken();
        if (!TextUtils.isEmpty(userToken)) {
            map.put("token", userToken);
        } else {
            map.put("token", "");
        }
        return services.newRequestCall(Api.getApi().getUrl(HeartLaiUrls.UPDATE_THIRD_PART_IPC), map);
    }

    public Call<StringResponseEntry> setAlertMode(String homeID, String pid, String mode) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("provider", HeartLaiConstants.PROVIDER_HEARTLAI);
            jsonObject.put("pid", pid);
            jsonObject.put("alert_mode", mode);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.setAlertModeCall(Api.getApi()
                .getUrl(HeartLaiUrls.URL_SET_ALERT_MODE), map);
    }

    public Call<GetAlertModeResponse> getAlertMode(String homeID, String pid) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("provider", HeartLaiConstants.PROVIDER_HEARTLAI);
            jsonObject.put("pid", pid);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getAlertModeCall(Api.getApi()
                .getUrl(HeartLaiUrls.URL_GET_ALERT_MODE), map);
    }

    /**
     * 查询指定 pid 的自研 ipc
     */
    public Call<SearchIpcResponse> searchIpc(String homeID, @NonNull List<SearchIpcParams> ipcs) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            final JSONArray pidList = new JSONArray();
            if (null != ipcs && ipcs.size() > 0) {
                for (SearchIpcParams ipc : ipcs) {
                    final String pid = ipc.getPid();
                    final String provider = ipc.getProvider();
                    if (!TextUtils.isEmpty(pid) && !TextUtils.isEmpty(provider)) {
                        final JSONObject obj = new JSONObject();
                        obj.put("pid", pid);
                        obj.put("provider", provider);
                        pidList.put(obj);
                    }
                }
            }
            jsonObject.put("home_id", homeID);
            jsonObject.put("pids", pidList);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.searchIpc(Api.getApi()
                .getUrl(HeartLaiUrls.URL_SEARCH_IPC), map);
    }

}
