package com.dinsafer.module_heartlai.model;

import android.text.TextUtils;

import androidx.annotation.Nullable;
import androidx.annotation.RestrictTo;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.google.gson.Gson;

import java.io.Serializable;
import java.util.Map;

public class IPCDevice extends Device implements Serializable {
    protected final String TAG = this.getClass().getSimpleName();

    private String home_id;
    private String cameraPID;
    private String uid;
    private String pwd;
    private String name;
    private String snapshot;
    private String description;
    private String ipc_type;
    private String wifi_ssid;

    //    摄像头的状态；0:未知，1:链接中，2:在线，3:离线，4:密码错误
    private int connectStatus = HeartLaiConstants.CAMERA_STATUS_UNKNOW;

    private boolean shake;
    private boolean listen, isListening;
    private boolean talk, isTalking;
    private boolean isLiving;
    private boolean hdMode;

    //    服务器返回的数据
    private String sourceData;
    private int mirroMode;
    private int videoMode;
    private boolean ap;
    private String IPaddress;
    private int battery = -1;
    private long createTime;

    public IPCDevice() {
        initFlagOnReadCache();
    }

    @Override
    public void submit(Map map) {

    }

    public IPCDevice(Builder builder) {
        if (builder.fromCache) {
            initFlagOnReadCache();
        } else {
            initFlagOnNetwork();
        }

        setSnapshot(builder.snapshot);
        setName(builder.name);
        setDescription(builder.description);
        setSourceData(builder.sourceData);
        setId(builder.id);
        setPwd(builder.password);
        setCameraPID(builder.cameraPID);
        setUid(builder.uid);
        setConnectStatus(builder.connectStatus);
        setIpc_type(builder.ipc_type);
        setCreateTime(builder.time);
        setHome_id(builder.home_id);
        setWifiSsid(builder.wifi_ssid);
        setHdMode(builder.hdMode);
    }

    public boolean isDefaultPassword() {
        if (getProvider().equals(HeartLaiConstants.PROVIDER_HEARTLAI)) {
            return getPwd().equals(HeartLaiConstants.DEFAULT_HEARTLAI_PWD);
        }

        return false;
    }

    public String getWifiSsid() {
        return String.valueOf(getInfoFromMap(HeartLaiConstants.ATTR_WIFI_SSID));
    }

    public void setWifiSsid(String wifi_ssid) {
        this.wifi_ssid = wifi_ssid;
        putInfoToMap(HeartLaiConstants.ATTR_WIFI_SSID, wifi_ssid);
    }

    public String getSourceData() {
        return String.valueOf(getInfoFromMap(HeartLaiConstants.ATTR_SOURCE_DATA));
    }

    public void setSourceData(String sourceData) {
        this.sourceData = sourceData;
        putInfoToMap(HeartLaiConstants.ATTR_SOURCE_DATA, sourceData);
    }

    public String getUid() {
        return String.valueOf(getInfoFromMap(HeartLaiConstants.ATTR_UID));
    }

    public void setUid(String uid) {
        this.uid = uid;
        putInfoToMap(HeartLaiConstants.ATTR_UID, this.uid);
    }

    public String getPwd() {
        return String.valueOf(getInfoFromMap(HeartLaiConstants.ATTR_PASSWORD));
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
        putInfoToMap(HeartLaiConstants.ATTR_PASSWORD, pwd);
    }

    public String getName() {
        return String.valueOf(getInfoFromMap(HeartLaiConstants.ATTR_NAME));
    }

    public void setName(String name) {
        this.name = name;
//        DDLog.i("heartlai", "setName: " + name);
        putInfoToMap(HeartLaiConstants.ATTR_NAME, this.name);
//        DDLog.i("heartlai", "info hash: " + getInfo().toString());
    }

    public int getConnectStatus() {
        return Integer.parseInt(String.valueOf(getInfoFromMap(HeartLaiConstants.ATTR_CAMERA_STATUS)));
    }

    public void setConnectStatus(int connectStatus) {
        this.connectStatus = connectStatus;
        putInfoToMap(HeartLaiConstants.ATTR_CAMERA_STATUS, this.connectStatus);
    }

    public boolean isShake() {
        return shake;
    }

    public void setShake(boolean shake) {
        this.shake = shake;
        putInfoToMap(HeartLaiConstants.ATTR_SHAKE, this.shake);
    }

    public boolean isListen() {
        return listen;
    }

    public void setListen(boolean listen) {
        this.listen = listen;
        putInfoToMap(HeartLaiConstants.ATTR_LISTEN, this.listen);
    }

    public boolean isListening() {
        return isListening;
    }

    public void setListening(boolean listening) {
        isListening = listening;
        putInfoToMap(HeartLaiConstants.ATTR_IS_LISTENING, this.isListening);
    }

    public boolean isTalk() {
        return talk;
    }

    public void setTalk(boolean talk) {
        this.talk = talk;
        putInfoToMap(HeartLaiConstants.ATTR_TALK, this.talk);
    }

    public boolean isTalking() {
        return isTalking;
    }

    public void setTalking(boolean talking) {
        isTalking = talking;
        putInfoToMap(HeartLaiConstants.ATTR_IS_TALKING, this.isTalking);
    }

    public boolean isLiving() {
        return isLiving;
    }

    public void setLiving(boolean living) {
        isLiving = living;
        putInfoToMap(HeartLaiConstants.ATTR_IS_LIVING, this.isLiving);
    }

    public String getCameraPID() {
        return String.valueOf(getInfoFromMap(HeartLaiConstants.ATTR_CAMERA_PID));
    }

    public void setCameraPID(String cameraPID) {
        this.cameraPID = cameraPID;
        putInfoToMap(HeartLaiConstants.ATTR_CAMERA_PID, this.cameraPID);
    }

    public boolean isHdMode() {
        return hdMode;
    }

    public void setHdMode(boolean hdMode) {
        this.hdMode = hdMode;
        putInfoToMap(HeartLaiConstants.ATTR_HD_MODE, this.hdMode);
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
        putInfoToMap(HeartLaiConstants.ATTR_TIME, this.createTime);
    }

    public int getMirroMode() {
        return mirroMode;
    }

    public void setMirroMode(int mirroMode) {
        this.mirroMode = mirroMode;
        putInfoToMap(HeartLaiConstants.ATTR_MIRRO_MODE, this.mirroMode);
    }

    public int getVideoMode() {
        return videoMode;
    }

    public void setVideoMode(int videoMode) {
        this.videoMode = videoMode;
        putInfoToMap(HeartLaiConstants.ATTR_VIDEO_MODE, videoMode);
    }

    public boolean isAp() {
        return ap;
    }

    public void setAp(boolean ap) {
        this.ap = ap;
        putInfoToMap(HeartLaiConstants.ATTR_AP, ap);
    }

    public String getIPaddress() {
        return String.valueOf(getInfoFromMap(HeartLaiConstants.ATTR_IP_ADDRESS));
    }

    public void setIPaddress(String IPaddress) {
        this.IPaddress = IPaddress;
        putInfoToMap(HeartLaiConstants.ATTR_IP_ADDRESS, IPaddress);
    }

    public String getSnapshot() {
        return String.valueOf(getInfoFromMap(HeartLaiConstants.ATTR_SNAPSHOT));
    }

    public void setSnapshot(String snapshot) {
        this.snapshot = snapshot;
        putInfoToMap(HeartLaiConstants.ATTR_SNAPSHOT, snapshot);
    }

    public String getDescription() {
        return String.valueOf(getInfoFromMap(HeartLaiConstants.ATTR_DESCRIPTION));
    }

    public void setDescription(String description) {
        this.description = description;
        putInfoToMap(HeartLaiConstants.ATTR_DESCRIPTION, description);
    }

    public String getIpc_type() {
        return ipc_type;
    }

    public void setIpc_type(String ipc_type) {
        this.ipc_type = ipc_type;
        putInfoToMap(HeartLaiConstants.ATTR_IPC_TYPE, ipc_type);
    }

    public int getBattery() {
        return battery;
    }

    public void setBattery(int battery) {
        this.battery = battery;
        putInfoToMap("battery", battery);
    }

    public String getHome_id() {
        return String.valueOf(getInfoFromMap(HeartLaiConstants.ATTR_HOME_ID));
    }

    public void setHome_id(String home_id) {
        this.home_id = home_id;
        putInfoToMap(HeartLaiConstants.ATTR_HOME_ID, home_id);
    }

    protected void convertToInfo() {
        putInfoToMap(HeartLaiConstants.ATTR_NAME, this.name);
        putInfoToMap(HeartLaiConstants.ATTR_HOME_ID, this.home_id);
        putInfoToMap(HeartLaiConstants.ATTR_CAMERA_STATUS, this.connectStatus);
        putInfoToMap(HeartLaiConstants.ATTR_SHAKE, this.shake);
        putInfoToMap(HeartLaiConstants.ATTR_LISTEN, this.listen);
        putInfoToMap(HeartLaiConstants.ATTR_TALK, this.talk);
        putInfoToMap(HeartLaiConstants.ATTR_IS_LISTENING, this.isListening);
        putInfoToMap(HeartLaiConstants.ATTR_IS_TALKING, this.isTalking);
        putInfoToMap(HeartLaiConstants.ATTR_IS_LIVING, this.isLiving);
        putInfoToMap(HeartLaiConstants.ATTR_CAMERA_PID, this.cameraPID);
        putInfoToMap(HeartLaiConstants.ATTR_UID, this.uid);
        putInfoToMap(HeartLaiConstants.ATTR_PASSWORD, this.pwd);
        putInfoToMap(HeartLaiConstants.ATTR_TIME, this.createTime);
        putInfoToMap(HeartLaiConstants.ATTR_MIRRO_MODE, this.mirroMode);
        putInfoToMap(HeartLaiConstants.ATTR_HD_MODE, this.hdMode);
        putInfoToMap(HeartLaiConstants.ATTR_VIDEO_MODE, this.videoMode);
        putInfoToMap(HeartLaiConstants.ATTR_AP, this.ap);
        putInfoToMap(HeartLaiConstants.ATTR_IPC_TYPE, this.ipc_type);
        putInfoToMap(HeartLaiConstants.ATTR_IP_ADDRESS, IPaddress);
        putInfoToMap(HeartLaiConstants.ATTR_SOURCE_DATA, sourceData);
        putInfoToMap(HeartLaiConstants.ATTR_WIFI_SSID, wifi_ssid);
    }

    protected void putInfoToMap(String key, Object value) {
//        DDLog.i("heartlai", "putInfoToMap-->key:" + key + " /value:" + value);
        if (value == null) {
            return;
        }
        getInfo().put(key, value);
//        DDLog.i("heartlai", "putInfoToMap-->key:" + key + " /value:" + getInfoFromMap(key));

    }

    protected Object getInfoFromMap(String key) {
        if (TextUtils.isEmpty(key)) {
            return null;
        }
        if (getInfo().containsKey(key)) {
            return getInfo().get(key);
        }
        return null;
    }

    public boolean isConnected() {
        return ((int) getInfo().get(HeartLaiConstants.ATTR_CAMERA_STATUS)) == HeartLaiConstants.CAMERA_STATUS_ONLINE;
    }

    public boolean isConnecting() {
        return ((int) getInfo().get(HeartLaiConstants.ATTR_CAMERA_STATUS)) == HeartLaiConstants.CAMERA_STATUS_CONNECTING;
    }

    public boolean isFlipOn() {
        return getMirroMode() == 3 || getMirroMode() == 2;
    }

    public boolean isMirrorOn() {
        return getMirroMode() == 3 || getMirroMode() == 1;
    }

    public String getProvider() {
        return String.valueOf(getInfoFromMap(HeartLaiConstants.ATTR_PROVIDER));
    }

    public static <T extends Builder> T newBuilder() {
        return (T) new Builder();
    }

    public static class Builder {
        protected String snapshot;
        protected String name;
        protected String description;
        protected String sourceData;
        protected String id;
        protected String home_id;
        protected String password;
        protected String cameraPID;
        protected String uid;
        protected int connectStatus;
        protected String ipc_type;
        protected long time;
        protected String wifi_ssid;
        protected boolean hdMode;
        protected boolean fromCache = false;

        public Builder() {
        }

        public Builder snapshot(String snapshot) {
            this.snapshot = snapshot;
            return this;
        }


        public Builder name(String name) {
            this.name = name;
            return this;
        }


        public Builder description(String description) {
            this.description = description;
            return this;
        }

        public Builder sourceData(String sourceData) {
            this.sourceData = sourceData;
            return this;
        }

        public Builder id(String id) {
            this.id = id;
            return this;
        }

        public Builder password(String password) {
            this.password = password;
            return this;
        }

        public Builder cameraPID(String cameraPID) {
            this.cameraPID = cameraPID;
            return this;
        }

        public Builder uid(String uid) {
            this.uid = uid;
            return this;
        }

        public Builder setConnectStatus(int connectStatus) {
            this.connectStatus = connectStatus;
            return this;
        }

        public Builder setIpc_type(String ipc_type) {
            this.ipc_type = ipc_type;
            return this;
        }

        public Builder time(long time) {
            this.time = time;
            return this;
        }

        public Builder home_id(String home_id) {
            this.home_id = home_id;
            return this;
        }

        public Builder wifi_ssid(String wifi_ssid) {
            this.wifi_ssid = wifi_ssid;
            return this;
        }

        public Builder hdMode(boolean hdMode) {
            this.hdMode = hdMode;
            return this;
        }

        public Builder fromCache(boolean fromCache) {
            this.fromCache = fromCache;
            return this;
        }

        public IPCDevice build() {
            return new IPCDevice(this);
        }
    }

    @Override
    @RestrictTo(RestrictTo.Scope.LIBRARY)
    public void dispatchResult(String cmd, Map result) {
        super.dispatchResult(cmd, result);
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY)
    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    @RestrictTo(RestrictTo.Scope.LIBRARY)
    public void setFlagDeleted(boolean isDeleted) {
        super.setFlagDeleted(isDeleted);
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY)
    @Override
    public void setFlagCache(boolean isCache) {
        super.setFlagCache(isCache);
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY)
    @Override
    public void setCategory(int category) {
        super.setCategory(category);
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY)
    @Override
    public void setSubCategory(String subCategory) {
        super.setSubCategory(subCategory);
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY)
    @Override
    public void dispatchOnline() {
        super.dispatchOnline();
    }
}
