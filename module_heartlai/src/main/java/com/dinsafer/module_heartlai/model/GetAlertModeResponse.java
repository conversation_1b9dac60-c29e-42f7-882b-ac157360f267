package com.dinsafer.module_heartlai.model;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

@Keep
public class GetAlertModeResponse extends BaseHttpEntry {

    /**
     * Cmd :
     * Result : {"alert_mode":"normal"}
     */

    private String Cmd;
    private ResultBean Result;

    public String getCmd() {
        return Cmd;
    }

    public void setCmd(String Cmd) {
        this.Cmd = Cmd;
    }

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        /**
         * alert_mode : normal
         */

        private String alert_mode;

        public String getAlert_mode() {
            return alert_mode;
        }

        public void setAlert_mode(String alert_mode) {
            this.alert_mode = alert_mode;
        }
    }
}
