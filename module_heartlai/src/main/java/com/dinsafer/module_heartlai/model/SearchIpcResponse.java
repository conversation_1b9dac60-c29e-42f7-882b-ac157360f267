package com.dinsafer.module_heartlai.model;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.util.List;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/11/23 16:49
 */
@Keep
public class SearchIpcResponse extends BaseHttpEntry {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        private int count;
        private long gmtime;
        private List<GetDeviceListResponse.DeviceBean> list;

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        public List<GetDeviceListResponse.DeviceBean> getList() {
            return list;
        }

        public void setList(List<GetDeviceListResponse.DeviceBean> list) {
            this.list = list;
        }

    }
}
