package com.dinsafer.module_heartlai.model;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.util.List;

@Keep
public class GetDeviceListResponse extends BaseHttpEntry {

    private List<DeviceBean> Result;

    public List<DeviceBean> getResult() {
        return Result;
    }

    public void setResult(List<DeviceBean> result) {
        Result = result;
    }

    @Keep
    public static class DeviceBean {
        private Long addtime;
        private Boolean ap;
        private Boolean api;
        private Integer dtype;
        private Long gmtime;
        private String id;
        private Integer ipc_type;
        private Boolean listen;
        private String name;
        private String password;
        private String pid;
        private String provider;
        private Boolean shake;
        private String stype;
        private Boolean talk;
        private String user;
        private Boolean wave;
        private Boolean HDmode;

        public Long getAddtime() {
            return addtime;
        }

        public void setAddtime(Long addtime) {
            this.addtime = addtime;
        }

        public Boolean getAp() {
            return null != ap && ap;
        }

        public void setAp(Boolean ap) {
            this.ap = ap;
        }

        public Boolean getApi() {
            return null != api && api;
        }

        public void setApi(Boolean api) {
            this.api = api;
        }

        public Integer getDtype() {
            return dtype;
        }

        public void setDtype(Integer dtype) {
            this.dtype = dtype;
        }

        public Long getGmtime() {
            return gmtime;
        }

        public void setGmtime(Long gmtime) {
            this.gmtime = gmtime;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public Integer getIpc_type() {
            return ipc_type;
        }

        public void setIpc_type(Integer ipc_type) {
            this.ipc_type = ipc_type;
        }

        public Boolean getListen() {
            return null != listen && listen;
        }

        public void setListen(Boolean listen) {
            this.listen = listen;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public String getPid() {
            return pid;
        }

        public void setPid(String pid) {
            this.pid = pid;
        }

        public String getProvider() {
            return provider;
        }

        public void setProvider(String provider) {
            this.provider = provider;
        }

        public Boolean getShake() {
            return null != shake && shake;
        }

        public void setShake(Boolean shake) {
            this.shake = shake;
        }

        public String getStype() {
            return stype;
        }

        public void setStype(String stype) {
            this.stype = stype;
        }

        public Boolean getTalk() {
            return null != talk && talk;
        }

        public void setTalk(Boolean talk) {
            this.talk = talk;
        }

        public String getUser() {
            return user;
        }

        public void setUser(String user) {
            this.user = user;
        }

        public Boolean getWave() {
            return null != wave && wave;
        }

        public void setWave(Boolean wave) {
            this.wave = wave;
        }

        public Boolean getHDmode() {
            return HDmode;
        }

        public void setHDmode(Boolean HDmode) {
            this.HDmode = HDmode;
        }
    }
}
