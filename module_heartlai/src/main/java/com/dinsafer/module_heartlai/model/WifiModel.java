package com.dinsafer.module_heartlai.model;

import androidx.annotation.Keep;

import com.heartlai.ipc.utils.CommonUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.Serializable;

@Keep
public class WifiModel implements Serializable {
    private int cmd;
    private int result;
    private String ssid;
    private String ssidutf8;
    private String wifipwd;
    private int encryption;
    private int signal;
    private int conmode;

    public int getConmode() {
        return conmode;
    }

    public void setConmode(int conmode) {
        this.conmode = conmode;
    }

    public int getSignal() {
        return signal;
    }

    public void setSignal(int signal) {
        this.signal = signal;
    }




    public int getCmd() {
        return cmd;
    }

    public void setCmd(int cmd) {
        this.cmd = cmd;
    }

    public int getResult() {
        return result;
    }

    public void setResult(int result) {
        this.result = result;
    }

    public String getSsid() {
        return ssid;
    }

    public void setSsid(String ssid) {
        this.ssid = ssid;
    }

    public String getWifipwd() {
        return wifipwd;
    }

    public void setWifipwd(String wifipwd) {
        this.wifipwd = wifipwd;
    }

    public int getEncryption() {
        return encryption;
    }

    public String getSsidutf8() {
        return ssidutf8;
    }

    public void setSsidutf8(String ssidutf8) {
        this.ssidutf8 = ssidutf8;
    }

    public void setEncryption(int encryption) {
        this.encryption = encryption;
    }
    public static WifiModel jsonToModel(String json) throws JSONException {
        WifiModel parmsModel = new WifiModel();
        JSONObject obj = new JSONObject(json);
        parmsModel.setCmd(CommonUtil.jasonPaseInt(obj,"cmd", CommonUtil.SHIXFINAL_ERRORINT));
        parmsModel.setResult(CommonUtil.jasonPaseInt(obj,"result", CommonUtil.SHIXFINAL_ERRORINT));
        parmsModel.setSsid(CommonUtil.jasonPaseString(obj,"ssid"));
        parmsModel.setSignal(CommonUtil.jasonPaseInt(obj,"signal", CommonUtil.SHIXFINAL_ERRORINT));
        parmsModel.setEncryption(CommonUtil.jasonPaseInt(obj,"encryption", CommonUtil.SHIXFINAL_ERRORINT));
        parmsModel.setConmode(CommonUtil.jasonPaseInt(obj,"connected", CommonUtil.SHIXFINAL_ERRORINT));


        return parmsModel;
    }



    public static String toJson(WifiModel wifiModel, String user, String pwd) throws JSONException {
        JSONObject obj = new JSONObject();
        obj.put("pro", "set_sd");
        obj.put("cmd", 114);
        obj.put("user", user);
        obj.put("pwd", pwd);
        obj.put("ssid", wifiModel.getSsid());
        obj.put("wifipwd", wifiModel.getWifipwd());
        obj.put("encryption", wifiModel.getEncryption());
        return obj.toString();
    }

}
