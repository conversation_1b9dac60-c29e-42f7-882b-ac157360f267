package com.dinsafer.module_heartlai.model;

import androidx.annotation.Keep;
import androidx.annotation.Nullable;

import android.text.TextUtils;

import com.dinsafer.dincore.db.cache.ICacheInfo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/11/28 11:27
 */
@Keep
public class HeartCacheInfo implements ICacheInfo {

    private static final long serialVersionUID = 2012588783602595762L;
    private long addTime;
    private List<CacheInfo> cacheInfoList;

    public HeartCacheInfo() {
        this.cacheInfoList = new ArrayList<>();
    }

    public long getAddTime() {
        return addTime;
    }

    public long getNewestAddTime() {
        long newestAddTime = 0L;
        if (0 < cacheInfoList.size()) {
            for (CacheInfo cacheInfo : cacheInfoList) {
                long addTime = cacheInfo.getAddTime();
                if (addTime > newestAddTime) {
                    newestAddTime = addTime;
                }
            }
        }
        return newestAddTime;
    }

    public List<CacheInfo> getCacheInfoList() {
        return cacheInfoList;
    }

    public void setAddTime(long addTime) {
        this.addTime = addTime;
    }

    public void updateFrom(@Nullable final HeartCacheInfo src) {
        this.cacheInfoList.clear();
        if (null == src) {
            this.addTime = 0;
            return;
        }

        this.addTime = src.addTime;
        if (null != src.cacheInfoList) {
            this.cacheInfoList.addAll(src.cacheInfoList);
        }
        if (null == this.cacheInfoList || cacheInfoList.size() == 0) {
            addTime = 1;
        }
    }

    public boolean addDevice(final String id, final String pid, final String provider) {
        final CacheInfo newCache = new CacheInfo(id, pid, provider);
        return addDevice(newCache);
    }

    public boolean addDevice(CacheInfo cacheInfo) {
        if (null != cacheInfo && !TextUtils.isEmpty(cacheInfo.getPid())
                && !TextUtils.isEmpty(cacheInfo.getId())
                && !cacheInfoList.contains(cacheInfo)) {
            cacheInfoList.add(cacheInfo);
            return true;
        }
        return false;
    }

    public boolean removeDevice(final String id, final String pid, final String provider) {
        CacheInfo newCache = new CacheInfo(id, pid, provider);
        if (!TextUtils.isEmpty(pid)) {
            Iterator<CacheInfo> it = cacheInfoList.iterator();
            while (it.hasNext()) {
                if (it.next().getPid().equals(newCache.getPid())) {
                    it.remove();
                    return true;
                }
            }
        }
        return false;
    }

    public boolean isCacheEmpty() {
        return null == cacheInfoList || cacheInfoList.size() == 0;
    }

    @Override
    public String toString() {
        return "HeartCacheInfo{" +
                "addTime=" + addTime +
                ", cacheInfoList=" + cacheInfoList +
                '}';
    }

    @Override
    public boolean isNeedSaveCache() {
        return true;
    }

    @Keep
    public static class CacheInfo implements Serializable {
        private static final long serialVersionUID = 6691206659966098616L;
        private String id;
        private String pid;
        private String provider;

        private long addTime;

        public CacheInfo() {
        }

        public CacheInfo(String id, String pid, String provider) {
            this.id = id;
            this.pid = pid;
            this.provider = provider;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getPid() {
            return pid;
        }

        public void setPid(String pid) {
            this.pid = pid;
        }

        public String getProvider() {
            return provider;
        }

        public void setProvider(String provider) {
            this.provider = provider;
        }

        public long getAddTime() {
            return addTime;
        }

        public void setAddTime(long addTime) {
            this.addTime = addTime;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            CacheInfo cacheInfo = (CacheInfo) o;
            return Objects.equals(id, cacheInfo.id) && Objects.equals(pid, cacheInfo.pid) && Objects.equals(provider, cacheInfo.provider);
        }

        @Override
        public int hashCode() {
            return Objects.hash(id, pid, pid);
        }

        @Override
        public String toString() {
            return "CacheInfo{" +
                    "id='" + id + '\'' +
                    ", pid='" + pid + '\'' +
                    ", provider='" + provider + '\'' +
                    ", addTime=" + addTime +
                    '}';
        }
    }
}
