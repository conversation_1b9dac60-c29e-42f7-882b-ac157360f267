package com.dinsafer.module_heartlai;

import androidx.annotation.Keep;

import com.dinsafer.dincore.common.Cmd;

@Keep
public class HeartLaiCmd extends Cmd {

    /**
     * 连接摄像头
     */
    public static final String CMD_CONNECT = "operate_camera_connect";

    /**
     * 摄像头连接状态改变
     */
    public static final String CMD_STATUS_CHANGE = "camera_status_change";

    /**
     * 断开摄像头
     */
    public static final String CMD_DISCONNECT = "operate_camera_disconnect";

    /**
     * 摄像头扫描Wi-Fi
     */
    public static final String CMD_SCAN_WIFI = "operate_camera_scan_wifi";

    /**
     * 摄像头当前连接的Wi-Fi
     */
    public static final String CMD_CURRENT_WIFI = "operate_camera_get_wifi";

    /**
     * 摄像头设置网络
     */
    public static final String CMD_SET_WIFI = "operate_camera_set_wifi";

    /**
     * 添加摄像头
     */
    public static final String CMD_ADD = "operate_camera_add";

    /**
     * 删除摄像头
     */
    public static final String CMD_DEL = "operate_camera_delete";

    /**
     * 摄像头播放直播
     */
    public static final String CMD_START_LIVE = "operate_camera_start_live";

    /**
     * 摄像头结束直播
     */
    public static final String CMD_STOP_LIVE = "operate_camera_stop_live";


    /**
     * 摄像头打开声音监听
     */
    public static final String CMD_START_LISTEN = "operate_camera_start_listen";

    /**
     * 摄像头关闭声音监听
     */
    public static final String CMD_STOP_LISTEN = "operate_camera_stop_listen";

    /**
     * 摄像头打开话筒
     */
    public static final String CMD_START_TALK = "operate_camera_start_talk";

    /**
     * 摄像头关闭话筒
     */
    public static final String CMD_STOP_TALK = "operate_camera_stop_talk";

    /**
     * 获取摄像头设置信息
     */
    public static final String CMD_GET_SETTING = "operate_camera_get_setting";

    /**
     * 获取摄像头截图
     */
    public static final String CMD_SNAPSHOT = "operate_camera_snapShot";

    /**
     * 控制摄像头方向
     */
    public static final String CMD_CONTROL_DIRECTION = "operate_camera_control_direction";

    /**
     * 停止摄像头移动
     */
    public static final String CMD_STOP_DIRECTION = "operate_camera_stop_direction";

    /**
     * 修改摄像头密码
     */
    public static final String CMD_CHANGE_PASSWORD = "operate_camera_change_password";

    /**
     * 格式化摄像头SD卡
     */
    public static final String CMD_FORMAT_SD = "operate_camera_format_sd";

    /**
     * 获取摄像头SD卡信息
     */
    public static final String CMD_GET_SD_INFO = "operate_camera_get_sd_info";

    /**
     * 修改摄像头画面翻转模式
     */
    public static final String CMD_MIRROR = "operate_camera_mirror";

    /**
     * 摄像头同步时区
     */
    public static final String CMD_SYNC_TIMEZONE = "operate_camera_sync_timezone";

    /**
     * 获取摄像头录像设置信息
     */
    public static final String CMD_GET_RECORD_CONFIG = "operate_camera_get_record_config";

    /**
     * 设置摄像头录像设置信息
     */
    public static final String CMD_CONFIG_RECORD = "operate_camera_config_record";

    /**
     * 更新摄像头密码
     */
    public static final String CMD_PASSWORD = "operate_camera_password";

    /**
     * 获取摄像头录像列表
     */
    public static final String CMD_GET_VIDEO_LIST = "operate_camera_get_video_list";

    /**
     * 修改摄像头移动侦测设置
     */
    public static final String CMD_SET_ALERT_INFO = "operate_camera_set_alarm_info";

    /**
     * 获取摄像头移动侦测设置
     */
    public static final String CMD_GET_ALERT_INFO = "operate_camera_get_alarm_info";

    /**
     * 获取有回放文件的日期
     */
    public static final String CMD_GET_FILE_PARMS = "operate_camera_get_file_params";

    /**
     * 获取回放文件列表
     */
    public static final String CMD_GET_VIDEO = "operate_camera_get_video";

    /**
     * 打开回放文件
     */
    public static final String CMD_START_VIDEO = "operate_camera_start_video";

    /**
     * 关闭回放文件
     */
    public static final String CMD_STOP_VIDEO = "operate_camera_stop_video";

    /**
     * 播放回放文件
     */
    public static final String CMD_START_PLAY_BACK = "operate_camera_start_play_back";

    /**
     * 停止回放文件
     */
    public static final String CMD_STOP_PLAY_BACK = "operate_camera_stop_play_back";

    /**
     * 设置回放文件的播放进度
     */
    public static final String CMD_SET_VIDEO_OFFSET = "operate_camera_start_video_offset";

    /**
     * 更新摄像头的HDMode
     */
    public static final String CMD_CHANGE_HDMODE = "operate_camera_hdMode";

    /**
     * 更新自己保存在服务器的摄像头密码
     */
    public static final String CMD_SAVE_PASSWORD_TO_SERVER = "operate_camera_change_server_password";

    /**
     * 更新摄像头名字
     */
    public static final String CMD_CHANGE_NAME = "operate_camera_name";

    /**
     * 设置AlertMode
     */
    public static final String CMD_SET_ALERT_MODE = "set_alert_mode";

    /**
     * 获取AlertMode设置
     */
    public static final String CMD_GET_ALERT_MODE = "get_alert_mode";

}
