//package com.dinsafer.module_heartlai.settting.impl;
//
//import android.content.Context;
//import android.os.Bundle;
//import android.util.Log;
//
//import com.dinsafer.dincore.utils.DDJSONUtil;
//import com.dinsafer.dssupport.msctlib.db.KV;
//import com.dinsafer.dssupport.utils.DDLog;
//import com.dinsafer.module_heartlai.HeartLaiConstants;
//import com.dinsafer.module_heartlai.HeartLaiServiceManager;
//import com.dinsafer.module_heartlai.HeartLaiCmd;
//import com.dinsafer.module_heartlai.HeartLaiDevice;
//import com.dinsafer.module_heartlai.HeartLaiJniClientProxy;
//import com.dinsafer.module_heartlai.add.NetworkConfigurer;
//import com.dinsafer.module_heartlai.add.impl.HeartLaiNetworkConfigurer;
//import com.dinsafer.module_heartlai.settting.AbsIPCSettingConfigurer;
//import com.dinsafer.module_heartlai.model.ICameraMessageCallBack;
//import com.dinsafer.module_heartlai.model.ICameraStatusCallBack;
//import com.heartlai.ipc.utils.CommonUtil;
//
//import org.json.JSONException;
//import org.json.JSONObject;
//
//import java.util.Calendar;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.TimeZone;
//
///**
// * Description:
// * Author: MiraclesHed
// * Date: 2020/10/23
// */
//public class HeartLaiIPCSettingConfigurer extends AbsIPCSettingConfigurer implements ICameraStatusCallBack {
//
//    private HeartLaiDevice vo;
//    private boolean isConnect;
//    private String devID;
//    private String pwd;
//
//    private OnGetIPCSettingStatusCallback getVideoHorizontalStatusCallback;
//    private OnGetIPCSettingStatusCallback getVideoVerticalStatusCallback;
//    private OnIPCSettingActionCallback syncTimeStatusCallback;
//    private OnIPCSettingActionCallback formatSDCardStatusCallback;
//    private OnIPCSettingActionCallback checkSDCardStatusCallback;
//    private OnIPCSettingActionCallback setPasswordActionCallback;
//
//    @Override
//    public boolean setConfigParms(Context context, Bundle bundle) {
//        super.setConfigParms(context, bundle);
//
//        isConnect = bundle.getBoolean("isConnect");
//        devID = bundle.getString("devId");
//        pwd = bundle.getString("pwd");
//        DDLog.d(TAG, "setConfigParms: " + devID);
//
//        boolean isGetCameraDataSuccess = getCameraData();
//        HeartLaiServiceManager.getInstance().addCameraStatusCallBack(this);
//        return isGetCameraDataSuccess;
//    }
//
//    private boolean getCameraData() {
//        vo = HeartLaiServiceManager.getInstance().getDeviceByPid(devID);
//        if (vo == null) {
////            toClose();
//            return false;
//        }
//        if (!isConnect) {
//            return true;
//        }
//        HeartLaiJniClientProxy.transferMessage(devID,
//                CommonUtil.SHIX_getCameraParms(vo.getUid(), vo.getPwd()), 0);
//        return true;
//    }
//
//    @Override
//    public IPCState getIPCConnectState() {
//        if (isConnect) {
//            return IPCState.CONNECTED;
//        } else {
//            return IPCState.OFFLINE;
//        }
//    }
//
//    @Override
//    public String getIPCPassword() {
//        if (vo != null) {
//            return vo.getPwd();
//        }
//        return HeartLaiConstants.DEFAULT_HEARTLAI_PWD;
//    }
//
//    @Override
//    public void setIPCPassword(String pwd, OnIPCSettingActionCallback onIPCSettingActionCallback) {
//        this.setPasswordActionCallback = onIPCSettingActionCallback;
//        HeartLaiDevice model = (HeartLaiDevice) HeartLaiServiceManager.getInstance().getDeviceByPid(vo.getCameraPID());
//        if (model == null) {
//            if (onIPCSettingActionCallback != null) {
//                onIPCSettingActionCallback.onFail();
//            }
//            return;
//        }
//
//        Map<String, Object> parms = new HashMap<>();
//        parms.put("cmd", HeartLaiCmd.CMD_CHANGE_PASSWORD);
//        parms.put("password", pwd);
//        model.submit(parms);
//
//    }
//
//    @Override
//    public String getONVIFID() {
//        if (vo != null) {
//            return vo.getCameraPID();
//        }
//        return null;
//    }
//
//    @Override
//    public String getIPAddress() {
//        Log.d(TAG, "getIPAddress: " + vo.getIPaddress());
//        if (vo != null) {
//           return vo.getIPaddress();
//        }
//        return null;
//    }
//
//    @Override
//    public boolean supportSoundWave() {
//        return false;
//    }
//
//    @Override
//    public boolean supportAp() {
//        return true;
//    }
//
//    @Override
//    public boolean supportBluetooth() {
//        return false;
//    }
//
//    @Override
//    public void getMotionStatus(OnGetIPCSettingStatusCallback onGetIPCSettingStatusCallback) {
//
//    }
//
//    @Override
//    public void setMotionStatus(boolean isOpen, OnIPCSettingActionCallback onIPCSettingActionCallback) {
//
//    }
//
//    @Override
//    public void getVideoQualityStatus(OnGetIPCSettingStatusCallback onGetIPCSettingStatusCallback) {
//        if (onGetIPCSettingStatusCallback == null) {
//            return;
//        }
//        if (KV.containKey(HeartLaiConstants.HEART_LAI_IPC_HD + getONVIFID())) {
//            if (KV.getBool(HeartLaiConstants.HEART_LAI_IPC_HD + getONVIFID(),false)) {
////                videoQualityBtn.setOn(true);
//                onGetIPCSettingStatusCallback.onGetIPCSettingStatusCallback(true);
//
//            } else {
////                videoQualityBtn.setOn(false);
//                onGetIPCSettingStatusCallback.onGetIPCSettingStatusCallback(false);
//            }
//        } else {
//            KV.putBool(HeartLaiConstants.HEART_LAI_IPC_HD + getONVIFID(), true);
////            videoQualityBtn.setOn(true);
//            onGetIPCSettingStatusCallback.onGetIPCSettingStatusCallback(true);
//        }
//    }
//
//    @Override
//    public void setVideoQualityStatus(boolean isOpen, OnIPCSettingActionCallback onIPCSettingActionCallback) {
//        KV.putBool(HeartLaiConstants.HEART_LAI_IPC_HD + devID, isOpen);
//    }
//
//    @Override
//    public void getVideoHorizontalStatus(OnGetIPCSettingStatusCallback onGetIPCSettingStatusCallback) {
//        this.getVideoHorizontalStatusCallback = onGetIPCSettingStatusCallback;
//        HeartLaiServiceManager.getInstance().addCameraMessageCallBack(getVideoModeMessageCallback);
//    }
//
//    @Override
//    public void setVideoHorizontalStatus(boolean isOpen, Bundle bundle, OnIPCSettingActionCallback onIPCSettingActionCallback) {
//        super.setVideoHorizontalStatus(isOpen, bundle, onIPCSettingActionCallback);
//        boolean verOn = bundle.getBoolean("verOn");
//        setFlip(isOpen, verOn);
//
//    }
//
//    @Override
//    public void getVideoVerticalStatus(OnGetIPCSettingStatusCallback onGetIPCSettingStatusCallback) {
//        this.getVideoVerticalStatusCallback = onGetIPCSettingStatusCallback;
//        HeartLaiServiceManager.getInstance().addCameraMessageCallBack(getVideoModeMessageCallback);
//    }
//
//    @Override
//    public void setVideoVerticalStatus(boolean isOpen, Bundle bundle, OnIPCSettingActionCallback onIPCSettingActionCallback) {
//        super.setVideoVerticalStatus(isOpen, bundle, onIPCSettingActionCallback);
//        boolean horOn = bundle.getBoolean("horOn");
//        setFlip(horOn, isOpen);
//    }
//
//    private void setFlip(boolean horOn, boolean verOn) {
////        if (callBack != null) {
////            callBack.onHorizontalFlipChange(horizontalFlip);
////            callBack.onVerticalFlipChange(verticalFlip);
////        }
//
//        if (horOn && verOn) {
//            HeartLaiJniClientProxy.transferMessage(vo.getCameraPID(),
//                    CommonUtil.SHIX_Control_FlipMirror(vo.getUid(), vo.getPwd()), 0);
////            vo.setMirror_mode(3);
//        } else if (horOn && !verOn) {
//            HeartLaiJniClientProxy.transferMessage(vo.getCameraPID(),
//                    CommonUtil.SHIX_Control_Mirror(vo.getUid(), vo.getPwd()), 0);
////            vo.setMirror_mode(1);
//        } else if (!horOn && verOn) {
//            HeartLaiJniClientProxy.transferMessage(vo.getCameraPID(),
//                    CommonUtil.SHIX_Control_Flip(vo.getUid(), vo.getPwd()), 0);
////            vo.setMirror_mode(2);
//        } else {
////            vo.setMirror_mode(0);
//            HeartLaiJniClientProxy.transferMessage(vo.getCameraPID(),
//                    CommonUtil.SHIX_Control_CanFlipMirror(vo.getUid(), vo.getPwd()), 0);
//        }
//    }
//
//
//    private ICameraMessageCallBack getVideoModeMessageCallback = new ICameraMessageCallBack() {
//        @Override
//        public void onMessage(String cameraId, String message) {
//            DDLog.i(TAG, "onmessage " + cameraId + " -->" + message);
//            if (cameraId.equals(devID)) {
//                try {
//                    JSONObject jsonObject = new JSONObject(message);
//                    if (DDJSONUtil.has(jsonObject, "cmd")) {
//                        int cmd = DDJSONUtil.getInt(jsonObject, "cmd");
//                        if (cmd == 101) {
//                            vo.setVideoMode(DDJSONUtil.getInt(jsonObject, "video_mode"));
//                            vo.setMirroMode(DDJSONUtil.getInt(jsonObject, "mirror_mode"));
//                            vo.setIPaddress(DDJSONUtil.getString(jsonObject, "IPaddress"));
//                            boolean horOn = false;
//                            boolean verOn = false;
//
//                            if (vo.getMirroMode() == 0) {
////                                        horizontalFlipBtn.setOn(false);
////                                        verticalFlipBtn.setOn(false);
//                                horOn = false;
//                                verOn = false;
//                            } else if (vo.getMirroMode() == 1) {
////                                        horizontalFlipBtn.setOn(true);
////                                        verticalFlipBtn.setOn(false);
//                                horOn = true;
//                                verOn = false;
//                            } else if (vo.getMirroMode() == 2) {
////                                        horizontalFlipBtn.setOn(false);
////                                        verticalFlipBtn.setOn(true);
//                                horOn = false;
//                                verOn = true;
//                            } else if (vo.getMirroMode() == 3) {
////                                        horizontalFlipBtn.setOn(true);
////                                        verticalFlipBtn.setOn(true);
//                                horOn = true;
//                                verOn = true;
//                            }
//                            if (getVideoHorizontalStatusCallback != null) {
//                                getVideoHorizontalStatusCallback.onGetIPCSettingStatusCallback(horOn);
//                                getVideoHorizontalStatusCallback = null;
//                            }
//                            if (getVideoVerticalStatusCallback != null) {
//                                getVideoVerticalStatusCallback.onGetIPCSettingStatusCallback(verOn);
//                                getVideoVerticalStatusCallback = null;
//                            }
//                            HeartLaiServiceManager.getInstance().removeCameraMessageCallBack(getVideoModeMessageCallback);
//                        }
//                    }
//                } catch (JSONException e) {
//                    e.printStackTrace();
//                }
//            }
//
//        }
//    };
//
//    @Override
//    public void checkSDCard(OnIPCSettingActionCallback onIPCSettingActionCallback) {
//        this.checkSDCardStatusCallback = onIPCSettingActionCallback;
//        HeartLaiJniClientProxy.transferMessage(vo.getCameraPID(), CommonUtil.getSDParms(vo.getUid(), vo.getPwd()), 0);
//        HeartLaiServiceManager.getInstance().addCameraMessageCallBack(checkSDCardMessageCallback);
//    }
//
//    private ICameraMessageCallBack checkSDCardMessageCallback = new ICameraMessageCallBack() {
//        @Override
//        public void onMessage(String cameraId, String message) {
//            DDLog.i(TAG, "onmessage " + cameraId + " -->" + message);
//            if (cameraId.equals(devID)) {
//                try {
//                    JSONObject jsonObject = new JSONObject(message);
//                    if (DDJSONUtil.has(jsonObject, "cmd")) {
//                        int cmd = DDJSONUtil.getInt(jsonObject, "cmd");
//                        if (cmd == 109) {
//                            HeartLaiServiceManager.getInstance().removeCameraMessageCallBack(checkSDCardMessageCallback);
//                            int status = DDJSONUtil.getInt(jsonObject, "status");
//                            if (status == 1) {
//                                checkSDCardStatusCallback.onSuccess();
//                            } else {
//                                checkSDCardStatusCallback.onFail();
//                            }
//                        }
//                    }
//                } catch (JSONException e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//    };
//
//    @Override
//    public void formatSDCard(OnIPCSettingActionCallback onIPCSettingActionCallback) {
//        this.formatSDCardStatusCallback = onIPCSettingActionCallback;
//        HeartLaiJniClientProxy.transferMessage(vo.getCameraPID(),
//                CommonUtil.formatSDParms(vo.getUid(), vo.getPwd()), 0);
//
//        HeartLaiServiceManager.getInstance().addCameraMessageCallBack(formatSDCardMessageCallback);
//    }
//
//    private ICameraMessageCallBack formatSDCardMessageCallback = new ICameraMessageCallBack() {
//        @Override
//        public void onMessage(String cameraId, String message) {
//            DDLog.i(TAG, "onmessage " + cameraId + " -->" + message);
//            if (cameraId.equals(devID)) {
//                try {
//                    JSONObject jsonObject = new JSONObject(message);
//                    if (DDJSONUtil.has(jsonObject, "cmd")) {
//                        int cmd = DDJSONUtil.getInt(jsonObject, "cmd");
//                        if (cmd == 110) {
//                            int status = DDJSONUtil.getInt(jsonObject, "result");
//                            if (status == 0) {
//                                if (formatSDCardStatusCallback != null) {
//                                    formatSDCardStatusCallback.onSuccess();
//                                    formatSDCardStatusCallback = null;
//                                    HeartLaiServiceManager.getInstance().removeCameraMessageCallBack(formatSDCardMessageCallback);
//                                }
//                            } else {
//                                if (formatSDCardStatusCallback != null) {
//                                    formatSDCardStatusCallback.onFail();
//                                    formatSDCardStatusCallback = null;
//                                    HeartLaiServiceManager.getInstance().removeCameraMessageCallBack(formatSDCardMessageCallback);
//                                }
//                            }
//
//                        }
//                    }
//                } catch (JSONException e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//    };
//
//    @Override
//    public void syncTimezone(OnIPCSettingActionCallback onIPCSettingActionCallback) {
//        this.syncTimeStatusCallback = onIPCSettingActionCallback;
//        HeartLaiServiceManager.getInstance().addCameraMessageCallBack(syncTimeMessageCallback);
//        Calendar calendar = Calendar.getInstance();
//        int now = (int) (calendar.getTimeInMillis() / 1000);
//        TimeZone timeZone = TimeZone.getDefault();
//        DDLog.e("timezone", "setDate: " + timeZone.getDisplayName() + "   " + timeZone.getRawOffset());
//        int dstOffset = calendar.get(Calendar.DST_OFFSET);
//        int tz = -(timeZone.getRawOffset() + dstOffset);
//        HeartLaiJniClientProxy.transferMessage(vo.getCameraPID(),
//                CommonUtil.SHIX_SetDateParms(vo.getUid(),
//                        vo.getPwd(), tz / (60 * 1000), now, CommonUtil.DATE_SETTING_MODE), 0);
//    }
//
//    private ICameraMessageCallBack syncTimeMessageCallback = new ICameraMessageCallBack() {
//        @Override
//        public void onMessage(String cameraId, String message) {
//            DDLog.i(TAG, "onmessage " + cameraId + " -->" + message);
//            if (cameraId.equals(devID)) {
//                try {
//                    JSONObject jsonObject = new JSONObject(message);
//                    if (DDJSONUtil.has(jsonObject, "cmd")) {
//                        int cmd = DDJSONUtil.getInt(jsonObject, "cmd");
//                        if (cmd == 126) {
//                            int status = DDJSONUtil.getInt(jsonObject, "result");
//                            if (status == 0) {
//                                if (syncTimeStatusCallback != null) {
//                                    syncTimeStatusCallback.onSuccess();
//                                    syncTimeStatusCallback = null;
//                                    HeartLaiServiceManager.getInstance().removeCameraMessageCallBack(syncTimeMessageCallback);
//                                }
//                            } else {
//                                if (syncTimeStatusCallback != null) {
//                                    syncTimeStatusCallback.onFail();
//                                    syncTimeStatusCallback = null;
//                                    HeartLaiServiceManager.getInstance().removeCameraMessageCallBack(syncTimeMessageCallback);
//                                }
//                            }
//
//                        }
//                    }
//                } catch (JSONException e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//    };
//
//    @Override
//    public NetworkConfigurer getNetworkConfigurer() {
//        NetworkConfigurer networkConfigurer = new HeartLaiNetworkConfigurer();
//
//        Bundle bundle = new Bundle();
//        bundle.putSerializable("ipcmodel", HeartLaiServiceManager.getInstance().getDeviceByPid(devID));
//        bundle.putBoolean("isAdd", false);
//        bundle.putBoolean("isAutoDisconnectAp", true);
//        networkConfigurer.setConfigParms(context, bundle);
//
//        return networkConfigurer;
//    }
//
//    @Override
//    public void destory() {
//        super.destory();
//        HeartLaiServiceManager.getInstance().removeCameraStatusCallBack(this);
//        HeartLaiServiceManager.getInstance().removeCameraMessageCallBack(getVideoModeMessageCallback);
//        HeartLaiServiceManager.getInstance().removeCameraMessageCallBack(syncTimeMessageCallback);
//        HeartLaiServiceManager.getInstance().removeCameraMessageCallBack(formatSDCardMessageCallback);
//        HeartLaiServiceManager.getInstance().removeCameraMessageCallBack(checkSDCardMessageCallback);
//    }
//
//    @Override
//    public void onCameraUpdata(String cameraId) {
//
//    }
//
//    @Override
//    public void onCameraPasswordModify(String cameraPid, boolean isSuccess) {
////        ipcPasswordText.setText(vo.getPwd());
//        DDLog.d(TAG, "onCameraPasswordModify: " + cameraPid + " /devId:" + devID);
//        if (cameraPid.equals(devID) && isSuccess && setPasswordActionCallback != null) {
//            setPasswordActionCallback.onSuccess();
//        }
//    }
//}
