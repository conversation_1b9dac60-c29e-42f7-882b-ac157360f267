package com.dinsafer.module_heartlai.settting;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.Keep;

import com.dinsafer.module_heartlai.add.NetworkConfigurer;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2020/10/21
 */
public interface IPCSettingConfigurer {
    boolean setConfigParms(Context context, Bundle bundle);

    void destory();

    //详细信息

    IPCState getIPCConnectState();

    boolean isConnected();

    String getIPCPassword();

    void setIPCPassword(String pwd, OnIPCSettingActionCallback onIPCSettingActionCallback);

    String getONVIFID();

    String getIPAddress();

    boolean supportSoundWave();

    boolean supportAp();

    boolean supportBluetooth();

    //高级设置

    //获取移动侦测开光状态
    public void getMotionStatus(OnGetIPCSettingStatusCallback onGetIPCSettingStatusCallback);

    public void setMotionStatus(boolean isOpen, OnIPCSettingActionCallback onIPCSettingActionCallback);

    //获取高清画质状态
    public void getVideoQualityStatus(OnGetIPCSettingStatusCallback onGetIPCSettingStatusCallback);

    public void setVideoQualityStatus(boolean isOpen, OnIPCSettingActionCallback onIPCSettingActionCallback);

    //获取画面水平翻转状态
    public void getVideoHorizontalStatus(OnGetIPCSettingStatusCallback onGetIPCSettingStatusCallback);

    public void setVideoHorizontalStatus(boolean isOpen, OnIPCSettingActionCallback onIPCSettingActionCallback);

    public void setVideoHorizontalStatus(boolean isOpen, Bundle bundle, OnIPCSettingActionCallback onIPCSettingActionCallback);

    //获取画面垂直翻转状态
    public void getVideoVerticalStatus(OnGetIPCSettingStatusCallback onGetIPCSettingStatusCallback);

    public void setVideoVerticalStatus(boolean isOpen, OnIPCSettingActionCallback onIPCSettingActionCallback);

    public void setVideoVerticalStatus(boolean isOpen, Bundle bundle, OnIPCSettingActionCallback onIPCSettingActionCallback);

    //格式化内存卡
    public void formatSDCard(OnIPCSettingActionCallback onIPCSettingActionCallback);

    void checkSDCard(OnIPCSettingActionCallback onIPCSettingActionCallback);

    //同步时区
    public void syncTimezone(OnIPCSettingActionCallback onIPCSettingActionCallback);



    public NetworkConfigurer getNetworkConfigurer();

    public interface OnGetIPCSettingStatusCallback {
        void onGetIPCSettingStatusCallback(boolean open);
    }

    public interface OnIPCSettingActionCallback {
        void onSuccess();

        void onFail();
    }


    @Keep
    public enum IPCState {
        CONNECTING,
        CONNECTED,
        OFFLINE
    }
}
