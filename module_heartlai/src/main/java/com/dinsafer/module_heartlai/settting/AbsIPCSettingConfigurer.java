package com.dinsafer.module_heartlai.settting;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2020/10/21
 */
public abstract class AbsIPCSettingConfigurer implements IPCSettingConfigurer {
    protected String TAG = getClass().getSimpleName();
    protected Context context;
    protected Bundle configParm;
    protected IPCState ipcStatus;
    protected String password;

    @Override
    public boolean setConfigParms(Context context, Bundle bundle) {
        this.context = context;
        this.configParm = bundle;
        return true;
    }

    @Override
    public boolean isConnected() {
        return IPCState.CONNECTED.equals(getIPCConnectState());
    }

    @Override
    public void setVideoHorizontalStatus(boolean isOpen, OnIPCSettingActionCallback onIPCSettingActionCallback) {

    }

    @Override
    public void setVideoHorizontalStatus(boolean isOpen, Bundle bundle, OnIPCSettingActionCallback onIPCSettingActionCallback) {

    }

    @Override
    public void setVideoVerticalStatus(boolean isOpen, OnIPCSettingActionCallback onIPCSettingActionCallback) {

    }

    @Override
    public void setVideoVerticalStatus(boolean isOpen, Bundle bundle, OnIPCSettingActionCallback onIPCSettingActionCallback) {

    }

    @Override
    public void destory() {
        Log.d(TAG, "destory: ");
        context = null;
    }

}
