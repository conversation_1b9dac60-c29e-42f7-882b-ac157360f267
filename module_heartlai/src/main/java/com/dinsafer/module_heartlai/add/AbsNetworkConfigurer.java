package com.dinsafer.module_heartlai.add;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2020/10/13
 */
public abstract class AbsNetworkConfigurer implements NetworkConfigurer {
    protected String TAG = getClass().getSimpleName();

    protected Context context;
    protected Bundle parms;
    protected String ssid;
    protected String wifiPwd;
    protected Object wifiExtra;

    protected OnConfigNetworkCallback onConfigNetworkCallback;
    protected GetWifiListCallback getWifiListCallback;

    @Override
    public void setConfigParms(Context context, Bundle bundle) {
        this.context = context;
        this.parms = bundle;
    }

    @Override
    public void setWifiSSID(String ssid) {
        Log.d(TAG, "setWifiSSID: " + ssid);
        this.ssid = ssid;
    }

    @Override
    public String getWifiSSID() {
        return ssid;
    }

    @Override
    public void setWifiPWD(String pwd) {
        Log.d(TAG, "setWifiPWD: " + pwd);
        this.wifiPwd = pwd;
    }

    @Override
    public String getWifiPWD() {
        return wifiPwd;
    }

    @Override
    public void setWifiExtra(Object extra) {
        this.wifiExtra = extra;
    }

    @Override
    public Object getWifiExtra() {
        return wifiExtra;
    }

    @Override
    public void setOnConfigNetworkCallback(OnConfigNetworkCallback onConfigNetworkCallback) {
        this.onConfigNetworkCallback = onConfigNetworkCallback;
    }

    @Override
    public void removeOnConfigNetworkCallback(OnConfigNetworkCallback onConfigNetworkCallback) {
        this.onConfigNetworkCallback = null;
    }

    @Override
    public void getWifiList(GetWifiListCallback getWifiListCallback) {
        this.getWifiListCallback = getWifiListCallback;
    }

    @Override
    public void startConfig() {
        Log.d(TAG, "startConfig: ");
    }

    @Override
    public void stopConfig() {
        Log.d(TAG, "stopConfig: ");
    }

    @Override
    public void destory() {
        Log.d(TAG, "destory: ");
        this.context = null;
        this.parms = null;
        getWifiListCallback = null;
        onConfigNetworkCallback = null;
    }

}
