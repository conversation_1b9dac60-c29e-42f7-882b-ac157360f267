package com.dinsafer.module_heartlai.add.impl;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import androidx.annotation.Keep;
import android.util.Log;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dincore.common.IDeviceStatusListener;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_heartlai.HeartLaiCmd;
import com.dinsafer.module_heartlai.HeartLaiConstants;
import com.dinsafer.module_heartlai.HeartLaiDevice;
import com.dinsafer.module_heartlai.add.ap.AbsAPNetworkConfigurer;
import com.dinsafer.module_heartlai.model.WifiModel;
import com.dinsafer.module_heartlai.util.WifiUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import rx.Observable;
import rx.android.schedulers.AndroidSchedulers;
import rx.functions.Action1;
import rx.functions.Func1;
import rx.schedulers.Schedulers;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2020/10/22
 */
@Keep
public class HeartLaiNetworkConfigurer extends AbsAPNetworkConfigurer {
    private HeartLaiDevice device;

    private List<WifiModel> wifilist = new ArrayList<WifiModel>();
    private ArrayList<String> mData = new ArrayList<String>();
    ;
    private boolean mGetWifiFailed;

    static Handler handler = new Handler();

    private String id;
    private boolean isAdd = false;
    private boolean isAutoDisconnectAp = false;

    @Override
    public void setConfigParms(Context context, Bundle bundle) {
        super.setConfigParms(context, bundle);

        id = bundle.getString("id");
        isAdd = bundle.getBoolean("isAdd", false);
        isAutoDisconnectAp = bundle.getBoolean("isAutoDisconnectAp", false);
        String data = bundle.getString("data");
        JSONObject object = null;
        try {
            object = new JSONObject(data);
            device = (HeartLaiDevice) HeartLaiDevice.newBuilder()
                    .id(DDJSONUtil.getString(object, "id"))
                    .home_id(bundle.getString("home_id"))
                    .name(DDJSONUtil.getString(object, "name"))
                    .setIpc_type(HeartLaiConstants.PROVIDER_HEARTLAI)
                    .uid(DDJSONUtil.getString(object, "user"))
                    .password(DDJSONUtil.getString(object, "password"))
                    .cameraPID(DDJSONUtil.getString(object, "pid"))
                    .sourceData(data)
                    .build();
        } catch (Exception e) {
            e.printStackTrace();
        }
        device.registerDeviceCallBack(iDeviceCallBack);
    }

    @Override
    public Device getCurrentConfigDevice() {
        return device;
    }

    @Override
    public void connect() {
        super.connect();
        if (device != null) {
            device.registerDeviceStatusListener(connectStatusListener);
            device.connect();
        }
    }

    @Override
    public void reconnect() {
        super.reconnect();
        if (device != null) {
            device.registerDeviceStatusListener(connectStatusListener);
            device.reconnect();
        }
    }

    @Override
    public void getWifiList(GetWifiListCallback getWifiListCallback) {
        super.getWifiList(getWifiListCallback);
        if (device == null) {
            if (getWifiListCallback != null) {
                getWifiListCallback.onGetWifiListFail();
            }
            return;
        }

        if (mGetWifiFailed) {
            // 出错时，先重连再发送获取WiFi列表指令
            reconnect();
            if (getWifiListCallback != null) {
                getWifiListCallback.onGetWifiListFail();
            }
            mGetWifiFailed = false;
            return;
        }

        mGetWifiFailed = true;

        Map<String, Object> args = new HashMap<>();
        args.put("cmd", HeartLaiCmd.CMD_SCAN_WIFI);
        device.submit(args);
    }

    @Override
    public void startConfig() {
        super.startConfig();
        device.unregisterDeviceStatusListener(connectStatusListener);
        device.registerDeviceStatusListener(configNetworkICameraStatusCallBack);

        //一分钟后重连，连接成功表示配网成功
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (device != null && device.isConnected()) {
                    if (onConfigNetworkCallback != null) {
                        onConfigNetworkCallback.onConfigNetworkSuccess();
                    }
                } else {
                    tryReconnect();
                }
            }
        }, 60 * 1000);

        Map<String, Object> args = new HashMap<>();
        args.put("cmd", HeartLaiCmd.CMD_SET_WIFI);
        args.put("wifiName", getWifiSSID());
        args.put("password", getWifiPWD());
        args.put("encryption", ((int) getWifiExtra()));
        device.submit(args);
    }

    @Override
    public void stopConfig() {
        super.stopConfig();
        device.unregisterDeviceStatusListener(configNetworkICameraStatusCallBack);
        handler.removeCallbacksAndMessages(null);
    }

    @Override
    public void destory() {
        super.destory();
        stopConfig();
        handler.removeCallbacksAndMessages(null);
        if (device != null) {
            device.unregisterDeviceStatusListener(connectStatusListener);
            device.unregisterDeviceCallBack(iDeviceCallBack);
            device.destory();
        }
    }

    private IDeviceStatusListener connectStatusListener = new IDeviceStatusListener() {
        @Override
        public void online(String id, String subCategory) {
            DDLog.d(TAG, "onCameraUpdata: " + device.getId() + " /conected?" + device.isConnected());
            if (id.equals(device.getId()) && device.isConnected()) {
                DDLog.i(TAG, "connectICameraStatusCallBack-->success, connected: " + device.isConnected());
                for (OnConnectListener onConnectListener : connectListenerList) {
                    onConnectListener.onConnectSuccess();
                }
            }
        }

        @Override
        public void offline(String id, String subCategory, String s1) {

        }
    };

    private IDeviceStatusListener configNetworkICameraStatusCallBack = new IDeviceStatusListener() {
        @Override
        public void online(String id, String subCategory) {
            DDLog.d(TAG, "onCameraUpdata: " + device.getId() + " /conected?" + device.isConnected());
            if (id.equals(device.getId()) && device.isConnected()) {
                DDLog.i(TAG, "configNetworkICameraStatusCallBack-->success, connected: " + device.isConnected());
                finishSetting();
            }

        }

        @Override
        public void offline(String id, String subCategory, String s1) {

        }
    };

    private IDeviceCallBack iDeviceCallBack = new IDeviceCallBack() {
        @Override
        public void onCmdCallBack(String id, String subCategory, String cmd, Map map) {
            if (!id.equals(device.getId())) {
                return;
            }

            switch (cmd) {
                case HeartLaiCmd.CMD_SCAN_WIFI:
                    JSONObject result = (JSONObject) map.get("result");
                    mGetWifiFailed = false;
                    handlerWifiList(result.toString());
                    break;

                case HeartLaiCmd.CMD_SET_WIFI:
                    if (((int) map.get("status")) == 1) {
                        if (isAutoDisconnectAp) {
//                            如果是添加设备的配置网络，那么就要
                            WifiUtil.getInstance(context).disconnectWiFiNetWork();
                        }
                    } else {
                        if (onConfigNetworkCallback != null) {
                            onConfigNetworkCallback.onConfigNetworkFail();
                        }
                        handler.removeCallbacksAndMessages(null);
                    }
                    break;
                default:
                    break;
            }
        }
    };

    private void handlerWifiList(String message) {
        DDLog.i(TAG, "handlerWifiList");
        Observable.just(message)
                .map(new Func1<String, List<WifiModel>>() {

                    @Override
                    public List<WifiModel> call(String s) {
                        JSONObject dataJson = null;
                        try {
                            dataJson = new JSONObject(s);

                            int status = DDJSONUtil.getInt(dataJson, "result");
                            ArrayList<WifiModel> list = new ArrayList<>();
                            if (status >= 0) {
                                int ap_number = dataJson.getInt("count");
                                wifilist.clear();
                                mData.clear();
                                for (int i = 0; i < ap_number; i++) {
                                    String key1 = "ssid[" + i + "]";
                                    String key4 = "ssidutf8[" + i + "]";
                                    String key2 = "signal[" + i + "]";
                                    String key3 = "encryption[" + i + "]";
                                    WifiModel mode1 = new WifiModel();
                                    mode1.setSsid(dataJson.get(key1).toString().trim());
                                    mode1.setEncryption(dataJson.getInt(key3));
                                    if (dataJson.has(key4)) {
                                        mode1.setSsidutf8(dataJson.getString(key4));
                                    }
                                    int sig = dataJson.getInt(key2);
                                    mode1.setSignal(sig);

                                    if (mode1.getSsid() != null && mode1.getSsid().length() > 0) {
                                        mData.add(dataJson.get(key1).toString().trim());
                                        wifilist.add(mode1);
                                    }
                                }

                            }
                            return list;
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        return new ArrayList<>();
                    }
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Action1<List<WifiModel>>() {
                    @Override
                    public void call(List<WifiModel> wifiModels) {
                        if (getWifiListCallback != null) {
                            getWifiListCallback.onGetWifiListSuccess(mData, wifilist);
                        }
                    }
                }, new Action1<Throwable>() {
                    @Override
                    public void call(Throwable throwable) {
                        if (getWifiListCallback != null) {
                            getWifiListCallback.onGetWifiListSuccess(mData, wifilist);
                        }
                    }
                });
    }

    private void tryReconnect() {
        DDLog.i(TAG, "tryReconnect");
        device.reconnect();

        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "run: ");
                if (device != null && device.isConnected()) {
                    if (onConfigNetworkCallback != null) {
                        onConfigNetworkCallback.onConfigNetworkSuccess();
                    }
                } else {
                    if (onConfigNetworkCallback != null) {
                        onConfigNetworkCallback.onConfigNetworkFail();
                    }
                }
            }
        }, 60 * 1000);
    }

    private void finishSetting() {
        handler.removeCallbacksAndMessages(null);
        if (onConfigNetworkCallback != null) {
            onConfigNetworkCallback.onConfigNetworkSuccess();
        }
    }
}
