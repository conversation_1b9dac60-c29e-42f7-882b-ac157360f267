package com.dinsafer.module_heartlai.add.ap;

import android.util.Log;

import com.dinsafer.module_heartlai.add.AbsNetworkConfigurer;

import java.util.ArrayList;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2020/10/13
 */
public abstract class AbsAPNetworkConfigurer extends AbsNetworkConfigurer implements APNetworkConfigurer {
    protected ArrayList<OnConnectListener> connectListenerList = new ArrayList<>();

    @Override
    public void connect() {
        Log.d(TAG, "connect: ");

    }

    @Override
    public void reconnect() {
        Log.d(TAG, "reconnect: ");
    }

    @Override
    public void disconnect() {
        Log.d(TAG, "disconnect: ");
    }

    @Override
    public void addConnectListener(OnConnectListener onConnectListener) {
        if (connectListenerList != null && !connectListenerList.contains(onConnectListener)) {
            connectListenerList.add(onConnectListener);
        }
    }

    @Override
    public void removeConnectListener(OnConnectListener onConnectListener) {
        if (connectListenerList != null && connectListenerList.contains(onConnectListener)) {
            connectListenerList.remove(onConnectListener);
        }
    }

    @Override
    public void destory() {
        super.destory();
        if (connectListenerList != null) {
            connectListenerList.clear();
        }

    }
}
