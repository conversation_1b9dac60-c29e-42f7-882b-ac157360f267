package com.dinsafer.module_heartlai.add.ap;


import androidx.annotation.Keep;

import com.dinsafer.module_heartlai.add.NetworkConfigurer;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2020/10/13
 */
@Keep
public interface APNetworkConfigurer extends NetworkConfigurer {
    public void connect();

    public void reconnect();

    public void disconnect();

    public void addConnectListener(OnConnectListener onConnectListener);

    public void removeConnectListener(OnConnectListener onConnectListener);

    @Keep
    public interface OnConnectListener {
        void onConnectStateChange(int state);

        void onConnectSuccess();

        void onConnectFail(int state);
    }

}
