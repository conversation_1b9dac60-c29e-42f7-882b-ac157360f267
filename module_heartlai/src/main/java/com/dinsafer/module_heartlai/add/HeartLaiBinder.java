package com.dinsafer.module_heartlai.add;

import android.content.Context;
import androidx.annotation.Keep;

import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.activtor.bean.Plugin;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.module_heartlai.HeartLaiService;
import com.dinsafer.module_heartlai.add.impl.HeartLaiNetworkConfigurer;
import com.dinsafer.module_heartlai.http.HeartLaiRepository;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

import static com.dinsafer.dincore.common.ErrorCode.ACTIVTOR_BIND_DEVICE_FAIL;

@Keep
public class HeartLaiBinder extends BasePluginBinder implements IHeartLaiBinder {

    private NetworkConfigurer networkConfigurer;

    private HeartLaiRepository heartLaiRepository;

    public HeartLaiBinder(Context mContext) {
        super(mContext);
        networkConfigurer = new HeartLaiNetworkConfigurer();
        heartLaiRepository = new HeartLaiRepository();
    }

    @Override
    public void bindDevice(Plugin plugin) {
        JSONObject data = null;
        try {
            data = new JSONObject(plugin.getSourceData());
            data.put("name", plugin.getPluginName());
            data.put("home_id", HeartLaiService.currentHomeId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        heartLaiRepository.getAddTpIpcCall(data, new Callback<StringResponseEntry>() {
            Map<String, Object> result = new HashMap<>();

            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                com.dinsafer.dincore.http.StringResponseEntry responseEntry = response.body();
                if (responseEntry.getStatus() == 1) {
                    callBackBindResult(1, "");
                } else {
                    callBackBindResult(ACTIVTOR_BIND_DEVICE_FAIL, responseEntry.getErrorMessage());
                }
            }

            @Override
            public void onFailure(Call<com.dinsafer.dincore.http.StringResponseEntry> call, Throwable t) {
                callBackBindResult(ACTIVTOR_BIND_DEVICE_FAIL, t.getMessage());
            }
        });
    }

    @Override
    public NetworkConfigurer getNetworkConfigurer() {
        return networkConfigurer;
    }
}
