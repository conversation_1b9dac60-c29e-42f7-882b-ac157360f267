package com.dinsafer.module_heartlai.add;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.Keep;

import com.dinsafer.dincore.common.Device;

import java.io.Serializable;
import java.util.List;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2020/10/13
 */
@Keep
public interface NetworkConfigurer extends Serializable {

    public void setConfigParms(Context context, Bundle bundle);

    public Device getCurrentConfigDevice();

    public void setWifiSSID(String ssid);

    public String getWifiSSID();

    public void setWifiPWD(String pwd);

    public String getWifiPWD();

    public void setWifiExtra(Object extra);

    public Object getWifiExtra();

    public void getWifiList(GetWifiListCallback getWifiListCallback);

    public void setOnConfigNetworkCallback(OnConfigNetworkCallback onConfigNetworkCallback);

    public void removeOnConfigNetworkCallback(OnConfigNetworkCallback onConfigNetworkCallback);

    public void startConfig();

    public void stopConfig();

    public void destory();

    @Keep
    public interface OnConfigListener extends Serializable {
        void onConfigSuccess();

        void onConfigFail();
    }

    @Keep
    public interface GetWifiListCallback<T> extends Serializable {
        void onGetWifiListSuccess(List<String> wifiList, List<T> data);

        void onGetWifiListFail();
    }


    @Keep
    public interface OnConfigNetworkCallback extends Serializable {
        void onConfigNetworkSuccess();

        void onConfigNetworkFail();

        void receiveData(Object... args);
    }


}
