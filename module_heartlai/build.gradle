plugins {
    id 'com.android.library'
}

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    buildToolsVersion rootProject.ext.android.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode rootProject.ext.android.versionCode
        versionName rootProject.ext.android.versionName

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            debuggable rootProject.ext.debuggable
            minifyEnabled rootProject.ext.minifyEnabled
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility rootProject.ext.Java_Version
        targetCompatibility rootProject.ext.Java_Version
    }
}

dependencies {
    implementation 'androidx.annotation:annotation:1.0.0'
    api 'io.reactivex:rxjava:1.1.6'
    api 'io.reactivex:rxandroid:1.2.1'
    api files('libs/heartlai.jar')
    if (isAar) {
        compileOnly 'com.dinsafer.dinsdk:dincore:' + rootProject.ext.android.versionName + rootProject.ext.dependlibVersionPostfixt
    } else {
        compileOnly project(':dincore')
    }
}

ext.modulePublishConfig = [
        artifactId: 'heartlai',
]

// 使用maven插件上传
apply from: rootProject.file('gradle-scripts/upload.gradle')

// 使用maven-publish插件上传
//apply from: rootProject.file('gradle-scripts/publish.gradle')

