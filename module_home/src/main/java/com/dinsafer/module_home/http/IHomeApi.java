package com.dinsafer.module_home.http;

import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.module_home.bean.BalanceContractRecordsResponse;
import com.dinsafer.module_home.bean.BalanceContractSignTemplateResponse;
import com.dinsafer.module_home.bean.BalanceContractUnsignTemplateResponse;
import com.dinsafer.module_home.bean.BmtBalanceContractParticipationHoursResponse;
import com.dinsafer.module_home.bean.BmtIsTaskTimeInUpdatedRangeResponse;
import com.dinsafer.module_home.bean.CreateHomeResponse;
import com.dinsafer.module_home.bean.DailyMemoriesVideoResponse;
import com.dinsafer.module_home.bean.E2ELoginResponse;
import com.dinsafer.module_home.bean.EventListByFilterEntry;
import com.dinsafer.module_home.bean.EventListEntry;
import com.dinsafer.module_home.bean.FamilyBalanceContractInfoResponse;
import com.dinsafer.module_home.bean.GetHomeResponse;
import com.dinsafer.module_home.bean.HomeLocationResponse;
import com.dinsafer.module_home.bean.KeypadMemberPwdInfoGetResponse;
import com.dinsafer.module_home.bean.GetWidgetCountResponse;
import com.dinsafer.module_home.bean.HomeContactResponse;
import com.dinsafer.module_home.bean.HomeInfoResponse;
import com.dinsafer.module_home.bean.HomeInitResponse;
import com.dinsafer.module_home.bean.HomeListResponse;
import com.dinsafer.module_home.bean.HomeMemberResponse;
import com.dinsafer.module_home.bean.IPCEventMotionRecordsResponse;
import com.dinsafer.module_home.bean.IPCFirmwareVersionResponse;
import com.dinsafer.module_home.bean.IPCMotionDetectionRecordResponse;
import com.dinsafer.module_home.bean.IPCMotionEventRecordCountResponse;
import com.dinsafer.module_home.bean.IsOnlyAdminResponse;
import com.dinsafer.module_home.bean.JoinHomeResponse;
import com.dinsafer.module_home.bean.KeypadMemberPwdResetResponse;
import com.dinsafer.module_home.bean.KeypadMemberPwdUpdateResponse;
import com.dinsafer.module_home.bean.LastMotionIpcListResponse;
import com.dinsafer.module_home.bean.ListPanelTokensResponse;
import com.dinsafer.module_home.bean.MemberAvatarsResponse;
import com.dinsafer.module_home.bean.MotionEventCoverResponse;
import com.dinsafer.module_home.bean.MotionEventDatesResponse;
import com.dinsafer.module_home.bean.MotionEventResponse;
import com.dinsafer.module_home.bean.MotionEventVideoResponse;
import com.dinsafer.module_home.bean.RegionCountriesResponse;
import com.dinsafer.module_home.bean.RegionElectricitySupplierResponse;
import com.dinsafer.module_home.bean.StatusResponse;
import com.dinsafer.module_home.bean.UploadTokenResponse;

import java.util.Map;

import retrofit2.Call;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;
import retrofit2.http.Url;

/**
 * 房間相关网络请求接口
 *
 * @since 2021/5/12 2:56 PM
 */
interface IHomeApi {

    @POST
    @FormUrlEncoded
    Call<HomeListResponse> queryHomeList(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<CreateHomeResponse> createHome(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> getStringCall(@Url String url, @FieldMap Map<String, Object> map);


    @POST
    @FormUrlEncoded
    Call<HomeMemberResponse> queryHomeMemberList(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> updateHomeMember(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> newHomeContact(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<HomeContactResponse> listHomeContact(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> removeHomeContact(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> updateHomeContact(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<JoinHomeResponse> verifyInvitationFamilyMember(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<HomeInfoResponse> getHomeInfo(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> setHomeNotificationLanguage(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> getHomeNotificationLanguage(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<E2ELoginResponse> loginHomeE2E(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> logoutHomeE2E(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<MemberAvatarsResponse> getHomeMemberAvatars(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> bindPanel(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<IPCMotionDetectionRecordResponse> getIPCMotionDetectionRecord(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> getDeleteIPCMotionDetectionRecord(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> getIPCMotionDetectionRecordVideoUrl(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<IsOnlyAdminResponse> getIsOnlyAdminUrl(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> getForceDeleteHomeUrl(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<IPCMotionEventRecordCountResponse> getTotalMotionRecordCount(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<IPCEventMotionRecordsResponse> listEventMotionRecords(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<EventListEntry> getEventList(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<EventListByFilterEntry> getEventListByFilter(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<IPCFirmwareVersionResponse> getIPCFirmwareVersion(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<MotionEventDatesResponse> getMotionEventDates(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<MotionEventVideoResponse> getMotionEventVideos(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<MotionEventResponse> getMotionEvents(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<LastMotionIpcListResponse> getLastMotionIpcList(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<MotionEventCoverResponse> getMotionEventCover(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<GetHomeResponse> getHome(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<GetWidgetCountResponse> getWidgetCount(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<ListPanelTokensResponse> listPanelTokens(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<KeypadMemberPwdInfoGetResponse> getKeypadMemberPwdInfo(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<KeypadMemberPwdUpdateResponse> updateKeypadMemberPwdInfo(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<KeypadMemberPwdResetResponse> resetKeypadMemberPwdInfo(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<DailyMemoriesVideoResponse> getDailyMemoriesVideoUrl(@Url String url, @FieldMap Map<String, Object> map);

    // ********************* 兼容模式（cawa）
    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> deleteMemberCompat(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> deleteHomeCompat(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<HomeInitResponse> initHomeAndMemberCompat(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<FamilyBalanceContractInfoResponse> getFamilyBalanceContractInfo(@Url String url, @FieldMap Map<String, Object> map);
    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> saveFamilyBalanceContractInfo(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<UploadTokenResponse> getBmtUploadToken(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<RegionElectricitySupplierResponse> getRegionElectricitySupplier(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<RegionCountriesResponse> getCountyList(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> updateBalanceContractBank(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> terminateBalanceContract(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<BalanceContractRecordsResponse> getBalanceContractRecords(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<BalanceContractUnsignTemplateResponse> getBalanceContractUnsignTemplate(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<BalanceContractSignTemplateResponse> getBalanceContractSignTemplate(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StatusResponse> checkBmtBalanceContractStatus(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> updateBalanceContractData(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<BmtBalanceContractParticipationHoursResponse> bmtBalanceContractParticipationHours(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> bmtAddBalanceContractParticipationHours(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> bmtUpdateBalanceContractParticipationHours(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> bmtDeleteBalanceContractParticipationHours(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<BmtIsTaskTimeInUpdatedRangeResponse> bmtIsTaskTimeInUpdatedRange(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<HomeLocationResponse> getHomeLocation(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> bmtSaveLocation(@Url String url, @FieldMap Map<String, Object> map);

}
