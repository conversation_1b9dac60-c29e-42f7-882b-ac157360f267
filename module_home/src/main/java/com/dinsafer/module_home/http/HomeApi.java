package com.dinsafer.module_home.http;

import android.text.TextUtils;

import com.dinsafer.dincore.http.Api;
import com.dinsafer.dincore.http.FileUploadService;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.dincore.utils.QiNiuUploadManager;
import com.dinsafer.module_home.bean.AddContactParams;
import com.dinsafer.module_home.bean.BalanceContractRecordsResponse;
import com.dinsafer.module_home.bean.BalanceContractSignTemplateResponse;
import com.dinsafer.module_home.bean.BalanceContractUnsignTemplateResponse;
import com.dinsafer.module_home.bean.BmtBalanceContractParticipationHoursResponse;
import com.dinsafer.module_home.bean.BmtIsTaskTimeInUpdatedRangeResponse;
import com.dinsafer.module_home.bean.CreateHomeResponse;
import com.dinsafer.module_home.bean.DailyMemoriesVideoResponse;
import com.dinsafer.module_home.bean.E2ELoginResponse;
import com.dinsafer.module_home.bean.EventListByFilterEntry;
import com.dinsafer.module_home.bean.EventListEntry;
import com.dinsafer.module_home.bean.FamilyBalanceContract;
import com.dinsafer.module_home.bean.FamilyBalanceContractInfoResponse;
import com.dinsafer.module_home.bean.GetHomeResponse;
import com.dinsafer.module_home.bean.GetWidgetCountResponse;
import com.dinsafer.module_home.bean.HomeContact;
import com.dinsafer.module_home.bean.HomeContactResponse;
import com.dinsafer.module_home.bean.HomeInfoResponse;
import com.dinsafer.module_home.bean.HomeInitResponse;
import com.dinsafer.module_home.bean.HomeListResponse;
import com.dinsafer.module_home.bean.HomeLocationResponse;
import com.dinsafer.module_home.bean.HomeMember;
import com.dinsafer.module_home.bean.HomeMemberResponse;
import com.dinsafer.module_home.bean.IPCEventMotionRecordsResponse;
import com.dinsafer.module_home.bean.IPCFirmwareVersionResponse;
import com.dinsafer.module_home.bean.IPCMotionDetectionRecordResponse;
import com.dinsafer.module_home.bean.IPCMotionEventRecordCountResponse;
import com.dinsafer.module_home.bean.InitHomeCompatParams;
import com.dinsafer.module_home.bean.IsOnlyAdminResponse;
import com.dinsafer.module_home.bean.JoinHomeResponse;
import com.dinsafer.module_home.bean.KeypadMemberPwdInfoGetResponse;
import com.dinsafer.module_home.bean.KeypadMemberPwdResetResponse;
import com.dinsafer.module_home.bean.KeypadMemberPwdUpdateResponse;
import com.dinsafer.module_home.bean.LastMotionIpcListResponse;
import com.dinsafer.module_home.bean.ListPanelTokensResponse;
import com.dinsafer.module_home.bean.MemberAvatarsResponse;
import com.dinsafer.module_home.bean.MotionEventCoverResponse;
import com.dinsafer.module_home.bean.MotionEventDatesResponse;
import com.dinsafer.module_home.bean.MotionEventResponse;
import com.dinsafer.module_home.bean.MotionEventVideoResponse;
import com.dinsafer.module_home.bean.ParticipationHour;
import com.dinsafer.module_home.bean.RegionCountriesResponse;
import com.dinsafer.module_home.bean.RegionElectricitySupplierResponse;
import com.dinsafer.module_home.bean.SingleBalanceContractRecordResponse;
import com.dinsafer.module_home.bean.StatusResponse;
import com.dinsafer.module_home.bean.UploadTokenResponse;
import com.qiniu.android.storage.UpCompletionHandler;

import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/12 2:55 PM
 */
public class HomeApi {

    private final IHomeApi services;
    public static final String GM = "gm";
    public static final String GMTIME = "gmtime";

    private HomeApi() {
        services = Api.getApi().getRetrofit().create(IHomeApi.class);
    }

    private static class Holder {
        private static final HomeApi INSTANT = new HomeApi();
    }

    public static HomeApi getInstance() {
        return Holder.INSTANT;
    }

    private Map<String, Object> getGM(Map<String, Object> map) {
        map.put(GM, 1);
        return map;
    }

    private JSONObject getGMTime(JSONObject jsonObject) {
        try {
            jsonObject.put(GMTIME, System.currentTimeMillis() * 1000);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObject;
    }

    public Call<HomeListResponse> queryHomeList() {
        Map<String, Object> map = new HashMap<>();
        map.put("token", UserManager.getInstance().getToken());
        return services.queryHomeList(Api.getApi()
                .getUrl(HomeUrls.URL_QUERY_HOME_LIST), map);
    }

    public Call<CreateHomeResponse> createHome(String homeName, String language) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_name", homeName);
            jsonObject.put("language", language);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.createHome(Api.getApi()
                .getUrl(HomeUrls.URL_CREATE_HOME), map);
    }

    public Call<StringResponseEntry> reNameHome(String homeID, String homeName) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("home_name", homeName);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getStringCall(Api.getApi()
                .getUrl(HomeUrls.URL_RENAME_HOME), map);
    }

    /**
     * level   当前最后数据的用户权限登记(如果没有则传0)
     * addTime 当前最后数据的用户加入纳秒时间戳(如果没有则传0)
     */
    public Call<HomeMemberResponse> queryHomeMemberList(String homeID) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            // 服务器限制了，一次最多只能拿50条数据
            jsonObject.put("page_size", 50);
            jsonObject.put("level", 0);
            jsonObject.put("addtime", 0);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.queryHomeMemberList(Api.getApi()
                .getUrl(HomeUrls.URL_LIST_HOME_MEMBERS), map);
    }

    public Call<StringResponseEntry> updateHomeMember(String homeId, String userId, int level,
                                                      HomeMember homeMember) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
            jsonObject.put("user_id", userId);
            jsonObject.put("level", level);

            if (null != homeMember) {
                jsonObject.put("push", homeMember.isPush());
                jsonObject.put("push_sos", homeMember.isPush_sos());
                jsonObject.put("push_info", homeMember.isPush_info());
                jsonObject.put("push_sys", homeMember.isPush_sys());
                if (!homeMember.isCompatMode()) {
                    jsonObject.put("sms", homeMember.isSms());
                    jsonObject.put("sms_sos", homeMember.isSms_sos());
                    jsonObject.put("sms_info", homeMember.isSms_info());
                    jsonObject.put("sms_sys", homeMember.isSms_sys());
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.updateHomeMember(Api.getApi()
                .getUrl(HomeUrls.URL_UPDATE_HOME_MEMBER), map);
    }

    public Call<StringResponseEntry> newHomeContact(String homeId, @NotNull AddContactParams params) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
            jsonObject.put("sms", params.isSms());
            jsonObject.put("sms_sos", params.isSms_sos());
            jsonObject.put("sms_info", params.isSms_info());
            jsonObject.put("sms_sys", params.isSms_sys());

            if (null != params.getContacts() && 0 < params.getContacts().size()) {
                JSONArray contacts = new JSONArray();
                JSONObject contact;
                for (AddContactParams.ContactBean paramsContact : params.getContacts()) {
                    contact = new JSONObject();
                    contact.put("name", paramsContact.getName());
                    contact.put("phone", paramsContact.getPhone());
                    contacts.put(contact);
                }
                jsonObject.put("contacts", contacts);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.newHomeContact(Api.getApi()
                .getUrl(HomeUrls.URL_NEW_HOME_CONTACT), map);
    }

    /**
     * contact_id 返回数组中最后一个联系人的联系人id，如果列表首页则为空
     * （因为联系人有可能批量添加，添加时间会相同，所以这里不是通过添加时间去排序）
     */
    public Call<HomeContactResponse> listHomeContact(String homeId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
            // 服务器限制了，一次最多只能拿30条数据
            jsonObject.put("page_size", 30);
//            jsonObject.put("contact_id", "");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.listHomeContact(Api.getApi()
                .getUrl(HomeUrls.URL_LIST_HOME_CONTACT), map);
    }

    public Call<StringResponseEntry> removeHomeContact(String homeId, String contactId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
            jsonObject.put("contact_id", contactId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.removeHomeContact(Api.getApi()
                .getUrl(HomeUrls.URL_REMOVE_HOME_CONTACT), map);
    }

    public Call<StringResponseEntry> updateHomeContact(String homeId, @NotNull HomeContact newContact) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
            jsonObject.put("contact_id", newContact.getContact_id());
            jsonObject.put("sms", newContact.isSms());
            jsonObject.put("sms_sos", newContact.isSms_sos());
            jsonObject.put("sms_info", newContact.isSms_info());
            jsonObject.put("sms_sys", newContact.isSms_sys());
            jsonObject.put("name", newContact.getName());
            jsonObject.put("phone", newContact.getPhone());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.updateHomeContact(Api.getApi()
                .getUrl(HomeUrls.URL_UPDATE_HOME_CONTACT), map);
    }

    public Call<StringResponseEntry> getInvitationCode(String homeID, int level) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("level", level);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getStringCall(Api.getApi()
                .getUrl(HomeUrls.URL_NEW_INVITATION_CODE), map);
    }

    public Call<JoinHomeResponse> verifyInvitationFamilyMemberCode(String code) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("code", code);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.verifyInvitationFamilyMember(Api.getApi()
                .getUrl(HomeUrls.URL_VERIFY_INVITATION_CODE), map);
    }

    public Call<StringResponseEntry> changeFamilyMemberPermission(String homeID, String user_id, int level) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("user_id", user_id);
            jsonObject.put("level", level);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getStringCall(Api.getApi()
                .getUrl(HomeUrls.URL_VERIFY_INVITATION_CODE), map);
    }


    public Call<StringResponseEntry> removeFamilyMember(String homeID, String user_id) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("user_id", user_id);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getStringCall(Api.getApi()
                .getUrl(HomeUrls.URL_REMOVE_MEMBER), map);
    }


    public Call<StringResponseEntry> removeHome(String homeID) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getStringCall(Api.getApi()
                .getUrl(HomeUrls.URL_REMOVE_HOME), map);
    }

    public Call<HomeInfoResponse> getHomeInfo(String homeID) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getHomeInfo(Api.getApi()
                .getUrl(HomeUrls.URL_GET_HOME_INFO), map);
    }

    public Call<StringResponseEntry> getHomeNotificationLanguage(String homeID) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getHomeNotificationLanguage(Api.getApi()
                .getUrl(HomeUrls.URL_GET_HOME_NOTIFICATION_LANGUAGE), map);
    }

    public Call<StringResponseEntry> setHomeNotificationLanguage(String homeID, String language) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("language", language);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.setHomeNotificationLanguage(Api.getApi()
                .getUrl(HomeUrls.URL_SET_HOME_NOTIFICATION_LANGUAGE), map);
    }

    public Call<E2ELoginResponse> loginHomeE2E(String homeID) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            if (UserManager.getInstance().isThirdPartyUser()) {
                jsonObject.put("user_id", UserManager.getInstance().getUser().getThirdPartyUID());
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.loginHomeE2E(Api.getApi()
                .getUrl(HomeUrls.URL_E2E_LOGIN_HOME), map);
    }

    public Call<StringResponseEntry> logoutHomeE2E(String homeID) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            if (UserManager.getInstance().isThirdPartyUser()) {
                jsonObject.put("user_id", UserManager.getInstance().getUser().getThirdPartyUID());
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.logoutHomeE2E(Api.getApi()
                .getUrl(HomeUrls.URL_E2E_LOGOUT_HOME), map);
    }

    public Call<MemberAvatarsResponse> getHomeMemberAvatars(String homeId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getHomeMemberAvatars(Api.getApi()
                .getUrl(HomeUrls.URL_HOME_MEMBER_AVATARS), map);
    }

    public Call<StringResponseEntry> bindPanel(String homeId, String panelId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
            jsonObject.put("deviceid", panelId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.bindPanel(Api.getApi()
                .getUrl(HomeUrls.URL_HOME_BIND_PANEL), map);
    }

    public Call<StringResponseEntry> deleteIPCMotionDetectionRecord(List<String> event_ids, String home_id) {

        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("event_ids", new JSONArray(event_ids));
            jsonObject.put("home_id", home_id);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        jsonObject = getGMTime(jsonObject);

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        Call<StringResponseEntry> getDeviceCmdCall = services.getDeleteIPCMotionDetectionRecord(Api.getApi()
                .getUrl(HomeUrls.URL_DELETE_MOTION_RECORDS), map);
        return getDeviceCmdCall;
    }

    public Call<IPCMotionDetectionRecordResponse> getIPCMotionDetectionRecord(String home_id, List<String> providers, long addtime, int pagesize) {

        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            JSONArray providerArray = new JSONArray(providers);
            jsonObject.put("home_id", home_id);
            jsonObject.put("providers", providerArray);
            jsonObject.put("page_size", pagesize);
            if (addtime > 0)
                jsonObject.put("addtime", addtime);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        jsonObject = getGMTime(jsonObject);
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        Call<IPCMotionDetectionRecordResponse> call = services.getIPCMotionDetectionRecord(Api.getApi()
                .getUrl(HomeUrls.URL_LIST_MOTION_RECORDS), map);
        return call;
    }

    public Call<StringResponseEntry> getIPCMotionDetectionRecordVideoUrl(String home_id, String record_id) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
            jsonObject.put("record_id", record_id);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        jsonObject = getGMTime(jsonObject);
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        Call<StringResponseEntry> call = services.getIPCMotionDetectionRecordVideoUrl(Api.getApi()
                .getUrl(HomeUrls.URL_GET_MOTION_RECORD_VIDEO_URL), map);
        return call;
    }

    /**
     * 判断是否唯一管理员
     */
    public Call<IsOnlyAdminResponse> getIsOnlyAdmin(String home_id) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        getGMTime(jsonObject);
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getIsOnlyAdminUrl(Api.getApi()
                .getUrl(HomeUrls.URL_IS_ONLY_ADMIN), map);
    }

    /**
     * 强制删除家庭(唯一管理员时调用)
     */
    public Call<StringResponseEntry> getForceDeleteHome(String home_id) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        getGMTime(jsonObject);
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getForceDeleteHomeUrl(Api.getApi()
                .getUrl(HomeUrls.URL_FORCE_DELETE_HOME), map);
    }


    public Call<IPCMotionEventRecordCountResponse> getTotalMotionRecordCount(String home_id, String event_id) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
            jsonObject.put("event_id", event_id);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        jsonObject = getGMTime(jsonObject);
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        Call<IPCMotionEventRecordCountResponse> call = services.getTotalMotionRecordCount(Api.getApi()
                .getUrl(HomeUrls.URL_GET_TOTAL_MOTION_RECORDS_COUNT), map);
        return call;
    }


    public Call<IPCEventMotionRecordsResponse> listEventMotionRecords(String home_id, String event_id, int start_index, int page_size) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
            jsonObject.put("event_id", event_id);
            jsonObject.put("start_index", start_index);
            jsonObject.put("page_size", page_size);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        jsonObject = getGMTime(jsonObject);
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        Call<IPCEventMotionRecordsResponse> call = services.listEventMotionRecords(Api.getApi()
                .getUrl(HomeUrls.URL_LIST_EVENT_MOTION_RECORDS), map);
        return call;
    }

    /**
     * 获取EventList的数据
     */
    public Call<EventListEntry> getEventListData(String homeId, String deviceid, int limit, long timestamp, String filters) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            JSONArray f;
            try {
                f = new JSONArray(filters);
            } catch (Exception e) {
                f = new JSONArray();
                e.printStackTrace();
            }
            jsonObject.put("deviceid", TextUtils.isEmpty(deviceid) ? "" : deviceid);
            jsonObject.put("limit", limit);
            jsonObject.put("timestamp", timestamp);
            jsonObject.put("filters", f);
            jsonObject.put("home_id", homeId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getEventList(Api.getApi().getUrl(HomeUrls.URL_GET_EVENT_LIST), map);
    }


    /**
     * 获取 event list
     */
    public Call<EventListByFilterEntry> getEventListDataByFilter(String homeId, int limit, long time, String typeIds) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            JSONArray f;
            try {
                f = new JSONArray(typeIds);
            } catch (Exception e) {
                f = new JSONArray();
                e.printStackTrace();
            }
            jsonObject.put("home_id", homeId);
            jsonObject.put("limit", limit);
            jsonObject.put("time", time);
            jsonObject.put("type_ids", f);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getEventListByFilter(Api.getApi().getUrl(HomeUrls.URL_GET_EVENT_LIST_BY_FILTER), map);
    }

    public Call<IPCFirmwareVersionResponse> getIPCFirmwareVersion(String home_id) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
            //目标供应商集合，传空时可获取所有供应商的ipc版本数据
            jsonObject.put("providers", new JSONArray());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        jsonObject = getGMTime(jsonObject);
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        Call<IPCFirmwareVersionResponse> call = services.getIPCFirmwareVersion(Api.getApi()
                .getUrl(HomeUrls.URL_GET_IPC_FIRMWARE_VERSION), map);
        return call;
    }

    /**
     * 获取有事件触发的日期（事件触发日历）
     *
     * @param timezone 如+08
     */
    public Call<MotionEventDatesResponse> getMotionEventDates(String homeId, String timezone) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
            jsonObject.put("timezone", timezone);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        jsonObject = getGMTime(jsonObject);
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getMotionEventDates(Api.getApi()
                .getUrl(HomeUrls.URL_GET_MOTION_EVENT_DATES), map);
    }

    /**
     * 获取指定事件的视频
     */
    public Call<MotionEventVideoResponse> getMotionEventVideos(String homeId, String ipcId, String eventId, long eventStartTime, int limit) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
            jsonObject.put("event_id", eventId);
            jsonObject.put("ipc_id", ipcId);
            jsonObject.put("event_start_time", eventStartTime);
            jsonObject.put("limit", limit);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        jsonObject = getGMTime(jsonObject);
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getMotionEventVideos(Api.getApi()
                .getUrl(HomeUrls.URL_GET_MOTION_EVENT_VIDEOS), map);
    }

    /**
     * 获取指定时间段的事件（时间轴上的事件列表）
     */
    public Call<MotionEventResponse> getMotionEvents(String homeId, List<String> ipcIds, long startTime, long endTime) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();

        try {
            jsonObject.put("home_id", homeId);
            jsonObject.put("start_time", startTime);
            jsonObject.put("end_time", endTime);

            JSONArray ipcList = new JSONArray();
            if (null != ipcIds && 0 < ipcIds.size()) {
                for (String ipcId : ipcIds) {
                    ipcList.put(ipcId);
                }
            }
            jsonObject.put("ipc_ids", ipcList);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        jsonObject = getGMTime(jsonObject);
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getMotionEvents(Api.getApi()
                .getUrl(HomeUrls.URL_GET_MOTION_EVENTS), map);
    }

    /**
     * 获取 ipc 最新的移动侦测触发时间（页面的 ipc 排序、列表）
     */
    public Call<LastMotionIpcListResponse> getLastMotionIpcList(String homeId, long startTime, long endTime) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();

        try {
            jsonObject.put("home_id", homeId);
            jsonObject.put("start_time", startTime);
            jsonObject.put("end_time", endTime);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        jsonObject = getGMTime(jsonObject);
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getLastMotionIpcList(Api.getApi()
                .getUrl(HomeUrls.URL_GET_LAST_MOTION_IPC_LIST), map);
    }

    public Call<MotionEventCoverResponse> getMotionEventCover(String homeId, List<String> eventIds) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();

        try {
            JSONArray ids = new JSONArray();
            if (null != eventIds && eventIds.size() > 0) {
                for (String eventId : eventIds) {
                    ids.put(eventId);
                }
            }

            jsonObject.put("home_id", homeId);
            jsonObject.put("event_ids", ids);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        jsonObject = getGMTime(jsonObject);
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getMotionEventCover(Api.getApi()
                .getUrl(HomeUrls.URL_GET_MOTION_EVENT_COVER), map);
    }

    public Call<GetHomeResponse> getHome(String homeID) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getHome(Api.getApi()
                .getUrl(HomeUrls.URL_GET_HOME), map);
    }

    /**
     * 获取家庭中所有 widget 配件的数量
     *
     * @param panelId 防止以后有多个主机，这里也要指定主机 id\
     */
    public Call<GetWidgetCountResponse> getWidgetCount(String homeID, String panelId,
                                                       List<String> ipcProviders, List<String> sTypeList) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            JSONArray ipcs = new JSONArray();
            if (null != ipcProviders && ipcProviders.size() > 0) {
                for (String provider : ipcProviders) {
                    ipcs.put(provider);
                }
            }
            JSONArray sTypes = new JSONArray();
            if (null != sTypeList && sTypeList.size() > 0) {
                for (String sType : sTypeList) {
                    sTypes.put(sType);
                }
            }

            jsonObject.put("home_id", homeID);
            jsonObject.put("device_id", null == panelId ? "" : panelId);
            jsonObject.put("providers", ipcs);
            jsonObject.put("stypes", sTypes);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getWidgetCount(Api.getApi()
                .getUrl(HomeUrls.URL_GET_WIDGET_COUNT), map);
    }

    public Call<ListPanelTokensResponse> listPanelTokens(String homeID, long addTime, boolean orderDesc, int pageSize) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("addtime", addTime);
            jsonObject.put("order", orderDesc ? "desc" : "asc");
            jsonObject.put("page_size", pageSize <= 0 ? 20 : pageSize);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.listPanelTokens(Api.getApi()
                .getUrl(HomeUrls.URL_LIST_PANEL_TOKENS), map);
    }

    public Call<KeypadMemberPwdInfoGetResponse> getKeypadMemberPwdInfo(final String homeId, final String panelId, final String userId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
            jsonObject.put("device_id", panelId);
            jsonObject.put("user_id", userId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getKeypadMemberPwdInfo(Api.getApi()
                .getUrl(HomeUrls.URL_KEYPAD_GET_MEMBER_PWD_INFO), map);
    }

    public Call<KeypadMemberPwdUpdateResponse> updateKeypadMemberPwdInfo(final String homeId, final String panelId,
                                                                         final String userId, final boolean enabled) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
            jsonObject.put("device_id", panelId);
            jsonObject.put("user_id", userId);
            jsonObject.put("pwd_enable", enabled);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.updateKeypadMemberPwdInfo(Api.getApi()
                .getUrl(HomeUrls.URL_KEYPAD_UPDATE_MEMBER_PWD_INFO), map);
    }

    public Call<KeypadMemberPwdResetResponse> resetKeypadMemberPwdInfo(final String homeId, final String panelId,
                                                                       final String userId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
            jsonObject.put("device_id", panelId);
            jsonObject.put("user_id", userId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.resetKeypadMemberPwdInfo(Api.getApi()
                .getUrl(HomeUrls.URL_KEYPAD_RESET_MEMBER_PWD_INFO), map);
    }

    public Call<DailyMemoriesVideoResponse> getDailyMemoriesVideoUrl(String homeId, String record_id) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
            jsonObject.put("record_id", record_id);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getDailyMemoriesVideoUrl(Api.getApi().getUrl(HomeUrls.URL_GET_DAILY_MEMORIES_VIDEO_URL), map);
    }


    // ********************* 兼容模式（cawa）

    /**
     * nova接入自研ipc删除家庭成员
     */
    public Call<StringResponseEntry> deleteMemberCompat(final String homeId, final String userId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
            jsonObject.put("user_id", userId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        getGMTime(jsonObject);
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.deleteMemberCompat(Api.getApi()
                .getUrl(HomeUrls.URL_REMOVE_MEMBER_COMPAT), map);
    }

    /**
     * nova接入自研ipc删除家庭
     */
    public Call<StringResponseEntry> deleteHomeCompat(String home_id) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        getGMTime(jsonObject);
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.deleteHomeCompat(Api.getApi()
                .getUrl(HomeUrls.URL_DELETE_HOME_COMPAT), map);
    }

    /**
     * nova接入自研ipc初始化主机家庭及成员
     */
    public Call<HomeInitResponse> initHomeAndMemberCompat(@NotNull final InitHomeCompatParams params) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("device_id", params.getDeviceId());
            jsonObject.put("device_token", params.getDeviceToken());
            jsonObject.put("language", params.getLanguage());
            jsonObject.put("device_name", params.getDeviceName());
            jsonObject.put("level", params.getLevel());
            jsonObject.put("push", params.isPush());
            jsonObject.put("notification", params.getNotification());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        getGMTime(jsonObject);
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.initHomeAndMemberCompat(Api.getApi()
                .getUrl(HomeUrls.URL_INIT_HOME_AND_MEMBER_COMPAT), map);
    }

    public Call<FamilyBalanceContractInfoResponse> getFamilyBalanceContractInfo(String home_id) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", home_id);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getFamilyBalanceContractInfo(Api.getApi().getUrl(HomeUrls.URL_BMT_GET_FAMILY_BALANCE_CONTRACT_INFO), map);
    }

    public Call<StringResponseEntry> saveFamilyBalanceContractInfo(String home_id, FamilyBalanceContract familyBalanceContract) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();

        try {
            jsonObject.put("home_id", home_id);
            jsonObject.put("source", FileUploadService.getInstance().getUploadMode().ordinal());

            JSONObject data = new JSONObject();
            data.put("IBAN", familyBalanceContract.getIBAN());
            data.put("cardholder", familyBalanceContract.getCardholder());
            data.put("city", familyBalanceContract.getCity());
            data.put("company_name", familyBalanceContract.getCompany_name());
            data.put("country_code", familyBalanceContract.getCountry_code());
            data.put("electricity_supplier", familyBalanceContract.getElectricitySupplier());
            data.put("electricity_supplier_id", familyBalanceContract.getElectricitySupplierId());
            data.put("email_address", familyBalanceContract.getEmailAddress());
            data.put("eu_vat_number", familyBalanceContract.getEuVatNumber());
            data.put("name", familyBalanceContract.getName());
            data.put("phone_number", familyBalanceContract.getPhoneNumber());
            data.put("sign", familyBalanceContract.getSign());
            data.put("street_name_and_number", familyBalanceContract.getStreetNameAndNumber());
            data.put("type", familyBalanceContract.getType());
            data.put("version", familyBalanceContract.getVersion());
            data.put("zip_code", familyBalanceContract.getZipCode());

            jsonObject.put("data", data);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.saveFamilyBalanceContractInfo(Api.getApi().getUrl(HomeUrls.URL_BMT_SAVE_FAMILY_BALANCE_CONTRACT_INFO_V3), map);

    }

    /**
     * 获取七牛上传 token
     */
    public Call<UploadTokenResponse> getBmtUploadToken() {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getBmtUploadToken(Api.getApi().getUrl(HomeUrls.URL_BMT_UPLOAD_TOKEN), map);
    }
    public void uploadSignByQiNiu(String uploadToken, String filePath, String key, final UpCompletionHandler handler) {
        QiNiuUploadManager.getInstance().getUploadManager().put(filePath, key, uploadToken, handler, null);
    }
    public Call<RegionElectricitySupplierResponse> getBmtRegionElectricitySupplier(String countryCode) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("country_code", countryCode);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getRegionElectricitySupplier(Api.getApi().getUrl(HomeUrls.URL_BMT_LIST_REGION_ELECTRICITY_SUPPLIER), map);
    }

    public Call<RegionCountriesResponse> getCountryList() {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getCountyList(Api.getApi()
                .getUrl(HomeUrls.URL_GET_COUNTY_LIST), map);
    }

    public Call<StringResponseEntry> updateBalanceContractBank(String homeId, String cardholder, String iban, String pwd) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();

        try {
            jsonObject.put("IBAN", iban);
            jsonObject.put("cardholder", cardholder);
            jsonObject.put("home_id", homeId);
            jsonObject.put("pwd", pwd);
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.updateBalanceContractBank(Api.getApi().getUrl(HomeUrls.URL_UPDATE_BALANCE_CONTRACT_BANK), map);
    }

    public Call<StringResponseEntry> terminateBalanceContract(String homeId, String sign) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
            jsonObject.put("sign", sign);
            jsonObject.put("source", FileUploadService.getInstance().getUploadMode().ordinal());
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.terminateBalanceContract(Api.getApi().getUrl(HomeUrls.URL_TERMINATE_CONTRACT_V3), map);
    }

    public Call<BalanceContractRecordsResponse> getBalanceContractRecords(long createTime, String homeId, int pageSize) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("create_time", createTime);
            jsonObject.put("home_id", homeId);
            jsonObject.put("page_size", pageSize);
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getBalanceContractRecords(Api.getApi().getUrl(HomeUrls.URL_GET_BALANCE_CONTRACT_RECORDS_V3), map);
    }

    public Call<SingleBalanceContractRecordResponse> getBalanceContractRecordsById(String homeId, String recordId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("record_id", recordId);
            jsonObject.put("home_id", homeId);
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getBalanceContractRecordsById(Api.getApi().getUrl(HomeUrls.URL_GET_BALANCE_CONTRACT_RECORDS_BY_ID), map);
    }

    /**
     * 获取原始JSON响应（用于调试）
     */
    public Call<okhttp3.ResponseBody> getBalanceContractRecordsByIdRaw(String homeId, String recordId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("record_id", recordId);
            jsonObject.put("home_id", homeId);
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getBalanceContractRecordsByIdRaw(Api.getApi().getUrl(HomeUrls.URL_GET_BALANCE_CONTRACT_RECORDS_BY_ID), map);
    }



    public Call<BalanceContractUnsignTemplateResponse> getBalanceContractUnsignTemplate(String homeId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getBalanceContractUnsignTemplate(Api.getApi().getUrl(HomeUrls.URL_GET_BALANCE_CONTRACT_UNSIGN_TEMPLATE_V3), map);
    }

    public Call<BalanceContractSignTemplateResponse> getBalanceContractSignTemplate(String countryCode) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("country_code", countryCode);
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getBalanceContractSignTemplate(Api.getApi().getUrl(HomeUrls.URL_GET_BALANCE_CONTRACT_SIGN_TEMPLATE), map);
    }

    public Call<StatusResponse> checkBmtDeviceBalanceContractStatus(String homeId, String deviceId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("device_id", deviceId);
            jsonObject.put("home_id", homeId);
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.checkBmtBalanceContractStatus(Api.getApi().getUrl(HomeUrls.URL_BMT_CHECK_DEVICE_BALANCE_CONTRACT_STATUS), map);
    }

    public Call<StringResponseEntry> updateBalanceContractData(String homeId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.updateBalanceContractData(Api.getApi().getUrl(HomeUrls.URL_UPDATE_BALANCE_CONTRACT_DATA), map);
    }

    /**
     * 获取调频计划时间列表
     * @param homeId
     * @return
     */
    public Call<BmtBalanceContractParticipationHoursResponse> bmtBalanceContractParticipationHours(long createTime, String homeId, int pageSize) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("create_time", createTime);
            jsonObject.put("home_id", homeId);
            jsonObject.put("page_size", pageSize);
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.bmtBalanceContractParticipationHours(Api.getApi().getUrl(HomeUrls.URL_BMT_LIST_BALANCE_CONTRACT_PARTICIPATION_HOURS), map);
    }

    /**
     * 新增调频时间
     * @param home_id
     * @param participationHour
     * @return
     */
    public Call<StringResponseEntry> bmtAddBalanceContractParticipationHours(String home_id, ParticipationHour participationHour) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();

        try {
            jsonObject.put("home_id", home_id);
            jsonObject.put("start", participationHour.getStart());
            jsonObject.put("end", participationHour.getEnd());
            jsonObject.put("all_day", participationHour.getAll_day());
            jsonObject.put("repeat", new JSONArray(participationHour.getRepeat()));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.bmtAddBalanceContractParticipationHours(Api.getApi().getUrl(HomeUrls.URL_BMT_ADD_BALANCE_CONTRACT_PARTICIPATION_HOURS), map);
    }

    /**
     * 修改调频时间
     * @param home_id
     * @param participationHour
     * @return
     */
    public Call<StringResponseEntry> bmtUpdateBalanceContractParticipationHours(String home_id, ParticipationHour participationHour) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();

        try {
            jsonObject.put("home_id", home_id);
            jsonObject.put("id", participationHour.getId());
            if (participationHour.getStart() != null) {
                jsonObject.put("start", participationHour.getStart());
            }
            if (participationHour.getEnd() != null) {
                jsonObject.put("end", participationHour.getEnd());
            }
            if (participationHour.getAll_day() != null) {
                jsonObject.put("all_day", participationHour.getAll_day());
            }
            if (participationHour.getRepeat() != null) {
                jsonObject.put("repeat", new JSONArray(participationHour.getRepeat()));
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.bmtUpdateBalanceContractParticipationHours(Api.getApi().getUrl(HomeUrls.URL_BMT_UPDATE_BALANCE_CONTRACT_PARTICIPATION_HOURS), map);
    }

    /**
     * 删除调频时间
     * @param home_id
     * @param id
     * @return
     */
    public Call<StringResponseEntry> bmtDeleteBalanceContractParticipationHours(String home_id, String id) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();

        try {
            jsonObject.put("home_id", home_id);
            jsonObject.put("id", id);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.bmtDeleteBalanceContractParticipationHours(Api.getApi().getUrl(HomeUrls.URL_BMT_DELETE_BALANCE_CONTRACT_PARTICIPATION_HOURS), map);
    }

    /**
     * 当前家庭现存调频任务时间是否在修改后的时间范围内（子集）
     * @param home_id
     * @param participationHour
     * @return
     */
    public Call<BmtIsTaskTimeInUpdatedRangeResponse> bmtIsTaskTimeInUpdatedRange(String home_id, ParticipationHour participationHour) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();

        try {
            jsonObject.put("home_id", home_id);
            String id = participationHour.getId();
            if (id != null) {
                jsonObject.put("id", id);
            }
            if (participationHour.getStart() != null) {
                jsonObject.put("start", participationHour.getStart());
            }
            if (participationHour.getEnd() != null) {
                jsonObject.put("end", participationHour.getEnd());
            }
            Boolean all_day = participationHour.getAll_day();
            if (all_day != null) {
                jsonObject.put("all_day", all_day);
            }
            if (participationHour.getRepeat() != null) {
                jsonObject.put("repeat", new JSONArray(participationHour.getRepeat()));
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.bmtIsTaskTimeInUpdatedRange(Api.getApi().getUrl(HomeUrls.URL_BMT_IS_TASK_TIME_IN_UPDATED_RANGE), map);
    }

    /**
     * 获取家庭定位信息
     * @param homeId
     * @return
     */
    public Call<HomeLocationResponse> getHomeLocation(String homeId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getHomeLocation(Api.getApi().getUrl(HomeUrls.URL_BMT_GET_HOME_LOCATION), map);
    }

    public Call<StringResponseEntry> bmtSaveLocation(String homeId, double latitude, double longitude) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeId);
            jsonObject.put("latitude", latitude);
            jsonObject.put("longitude", longitude);
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.bmtSaveLocation(Api.getApi().getUrl(HomeUrls.URL_BMT_SAVE_LOCATION), map);
    }
}
