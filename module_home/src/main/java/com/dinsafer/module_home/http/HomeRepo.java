package com.dinsafer.module_home.http;

import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.module_home.bean.AddContactParams;
import com.dinsafer.module_home.bean.BalanceContractRecordsResponse;
import com.dinsafer.module_home.bean.BalanceContractSignTemplateResponse;
import com.dinsafer.module_home.bean.BalanceContractUnsignTemplateResponse;
import com.dinsafer.module_home.bean.BmtBalanceContractParticipationHoursResponse;
import com.dinsafer.module_home.bean.BmtIsTaskTimeInUpdatedRangeResponse;
import com.dinsafer.module_home.bean.CreateHomeResponse;
import com.dinsafer.module_home.bean.DailyMemoriesVideoResponse;
import com.dinsafer.module_home.bean.E2ELoginResponse;
import com.dinsafer.module_home.bean.EventListByFilterEntry;
import com.dinsafer.module_home.bean.EventListEntry;
import com.dinsafer.module_home.bean.GetHomeResponse;
import com.dinsafer.module_home.bean.GetWidgetCountResponse;
import com.dinsafer.module_home.bean.HomeContact;
import com.dinsafer.module_home.bean.HomeContactResponse;
import com.dinsafer.module_home.bean.HomeInfoResponse;
import com.dinsafer.module_home.bean.HomeInitResponse;
import com.dinsafer.module_home.bean.HomeListResponse;
import com.dinsafer.module_home.bean.HomeLocationResponse;
import com.dinsafer.module_home.bean.HomeMember;
import com.dinsafer.module_home.bean.HomeMemberResponse;
import com.dinsafer.module_home.bean.IPCEventMotionRecordsResponse;
import com.dinsafer.module_home.bean.IPCFirmwareVersionResponse;
import com.dinsafer.module_home.bean.IPCMotionDetectionRecordResponse;
import com.dinsafer.module_home.bean.IPCMotionEventRecordCountResponse;
import com.dinsafer.module_home.bean.InitHomeCompatParams;
import com.dinsafer.module_home.bean.IsOnlyAdminResponse;
import com.dinsafer.module_home.bean.JoinHomeResponse;
import com.dinsafer.module_home.bean.KeypadMemberPwdInfoGetResponse;
import com.dinsafer.module_home.bean.KeypadMemberPwdResetResponse;
import com.dinsafer.module_home.bean.KeypadMemberPwdUpdateResponse;
import com.dinsafer.module_home.bean.LastMotionIpcListResponse;
import com.dinsafer.module_home.bean.ListPanelTokensResponse;
import com.dinsafer.module_home.bean.MemberAvatarsResponse;
import com.dinsafer.module_home.bean.MotionEventCoverResponse;
import com.dinsafer.module_home.bean.MotionEventDatesResponse;
import com.dinsafer.module_home.bean.MotionEventResponse;
import com.dinsafer.module_home.bean.MotionEventVideoResponse;
import com.dinsafer.module_home.bean.ParticipationHour;
import com.dinsafer.module_home.bean.SingleBalanceContractRecordResponse;
import com.dinsafer.module_home.bean.StatusResponse;

import org.jetbrains.annotations.NotNull;

import java.util.List;


import retrofit2.Call;
import retrofit2.Callback;

public class HomeRepo {
    public HomeRepo() {
    }

    public void queryHomeList(Callback<HomeListResponse> callback) {
        HomeApi.getInstance().queryHomeList()
                .enqueue(callback);
    }

    public void createHome(String homeName, String language, Callback<CreateHomeResponse> callback) {
        HomeApi.getInstance().createHome(homeName, language)
                .enqueue(callback);
    }

    public void reNameHome(String homeID, String homeName, Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().reNameHome(homeID, homeName)
                .enqueue(callback);
    }

    public void queryHomeMemberList(String homeID, Callback<HomeMemberResponse> callback) {
        HomeApi.getInstance().queryHomeMemberList(homeID)
                .enqueue(callback);
    }

    public void updateHomeMember(String homeId, String userId, int level, HomeMember homeMember,
                                 Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().updateHomeMember(homeId, userId, level, homeMember)
                .enqueue(callback);
    }

    public void newHomeContact(String homeId, @NotNull AddContactParams params,
                               Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().newHomeContact(homeId, params)
                .enqueue(callback);
    }

    public void listHomeContact(String homeId, Callback<HomeContactResponse> callback) {
        HomeApi.getInstance().listHomeContact(homeId)
                .enqueue(callback);
    }

    public void removeHomeContact(String homeId, String contactId,
                                  Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().removeHomeContact(homeId, contactId)
                .enqueue(callback);
    }

    public void updateHomeContact(String homeId, @NotNull HomeContact param,
                                  Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().updateHomeContact(homeId, param)
                .enqueue(callback);
    }

    public void getInvitationFamilyCode(String homeID, int level, Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().getInvitationCode(homeID, level)
                .enqueue(callback);
    }


    public void verifyInvitationFamilyMemberCode(String code, Callback<JoinHomeResponse> callback) {
        HomeApi.getInstance().verifyInvitationFamilyMemberCode(code)
                .enqueue(callback);
    }

    public void changeFamilyMemberPermission(String homeID, String user_id, int level,
                                             Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().changeFamilyMemberPermission(homeID, user_id, level)
                .enqueue(callback);
    }

    public void removeFamilyMember(String homeID, String user_id,
                                   Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().removeFamilyMember(homeID, user_id)
                .enqueue(callback);
    }

    public void removeHome(String homeID, Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().removeHome(homeID)
                .enqueue(callback);
    }

    public void getHomeInfo(String homeID, Callback<HomeInfoResponse> callback) {
        HomeApi.getInstance().getHomeInfo(homeID)
                .enqueue(callback);
    }

    public void getHomeNotificationLanguage(String homeID,
                                            Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().getHomeNotificationLanguage(homeID).enqueue(callback);
    }

    public void setHomeNotificationLanguage(String homeID, String language,
                                            Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().setHomeNotificationLanguage(homeID, language)
                .enqueue(callback);
    }

    public void loginHomeE2E(String homeID, Callback<E2ELoginResponse> callback) {
        HomeApi.getInstance().loginHomeE2E(homeID)
                .enqueue(callback);
    }

    public void logoutHomeE2E(String homeID, Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().logoutHomeE2E(homeID)
                .enqueue(callback);
    }

    public void getHomeMemberAvatars(String homeID, Callback<MemberAvatarsResponse> callback) {
        HomeApi.getInstance().getHomeMemberAvatars(homeID)
                .enqueue(callback);
    }

    public void bindPanel(String homeID, String panelId, Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().bindPanel(homeID, panelId)
                .enqueue(callback);
    }

    public void getMotionDetectionRecordList(String homeId, List<String> provider, long addtime, int pagesize, Callback<IPCMotionDetectionRecordResponse> callback) {
        HomeApi.getInstance().getIPCMotionDetectionRecord(homeId, provider, addtime, pagesize).enqueue(callback);
    }

    public void deleteMotionDetectionRecord(String homeId, List<String> event_ids, Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().deleteIPCMotionDetectionRecord(event_ids, homeId).enqueue(callback);
    }

    public void getMotionDetectionRecordVideoUrl(String homeId, String record_id, Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().getIPCMotionDetectionRecordVideoUrl(homeId, record_id).enqueue(callback);
    }

    public void getIsOnlyAdmin(String homeId, Callback<IsOnlyAdminResponse> callback) {
        HomeApi.getInstance().getIsOnlyAdmin(homeId).enqueue(callback);
    }

    public void getForceDeleteHome(String homeId, Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().getForceDeleteHome(homeId).enqueue(callback);
    }

    public void getTotalMotionRecordCount(String homeId, String event_id, Callback<IPCMotionEventRecordCountResponse> callback) {
        HomeApi.getInstance().getTotalMotionRecordCount(homeId, event_id).enqueue(callback);
    }


    public void listEventMotionRecords(String home_id, String event_id, int start_index, int page_size, Callback<IPCEventMotionRecordsResponse> callback) {
        HomeApi.getInstance().listEventMotionRecords(home_id, event_id, start_index, page_size).enqueue(callback);
    }

    /**
     * 获取EventList的数据
     */
    public void getEventListData(String homeId, String panelId, int limit, long timestamp, String filters, Callback<EventListEntry> callback) {
        HomeApi.getInstance().getEventListData(homeId, panelId, limit, timestamp, filters).enqueue(callback);
    }


    /**
     * get EventList by HomeID
     */
    public void getEventListDataByFilter(String homeId, int limit, long timestamp, String typeIds, Callback<EventListByFilterEntry> callback) {
        HomeApi.getInstance().getEventListDataByFilter(homeId, limit, timestamp, typeIds).enqueue(callback);
    }

    public void getIPCFirmwareVersion(String homeId, Callback<IPCFirmwareVersionResponse> callback) {
        HomeApi.getInstance().getIPCFirmwareVersion(homeId).enqueue(callback);
    }

    public void getMotionEventDates(String homeId, String timezone, Callback<MotionEventDatesResponse> callback) {
        HomeApi.getInstance().getMotionEventDates(homeId, timezone).enqueue(callback);
    }

    public void getMotionEventVideos(String homeId, String ipcId, String eventId, long eventStartTime, int limit, Callback<MotionEventVideoResponse> callback) {
        HomeApi.getInstance().getMotionEventVideos(homeId, ipcId, eventId, eventStartTime, limit).enqueue(callback);
    }

    public Call<?> getMotionEvents(String homeId, List<String> ipcIds, long startTime, long endTime, Callback<MotionEventResponse> callback) {
        Call<MotionEventResponse> call = HomeApi.getInstance().getMotionEvents(homeId, ipcIds, startTime, endTime);
        call.enqueue(callback);
        return call;
    }

    public void getLastMotionIpcList(String homeId, long startTime, long endTime, Callback<LastMotionIpcListResponse> callback) {
        HomeApi.getInstance().getLastMotionIpcList(homeId, startTime, endTime).enqueue(callback);
    }

    public void getMotionEventCover(String homeId, List<String> eventIds, Callback<MotionEventCoverResponse> callback) {
        HomeApi.getInstance().getMotionEventCover(homeId, eventIds).enqueue(callback);
    }

    public void getHome(String homeId, Callback<GetHomeResponse> callback) {
        HomeApi.getInstance().getHome(homeId).enqueue(callback);
    }

    public void getWidgetCount(String homeID, String panelId, List<String> ipcProviders,
                               List<String> sTypeList, Callback<GetWidgetCountResponse> callback) {
        HomeApi.getInstance().getWidgetCount(homeID, panelId, ipcProviders, sTypeList).enqueue(callback);
    }

    /**
     * 获取指定家庭下的主机 token
     *
     * @param addTime   开区间，返回数据根据 "addtime" 排序
     * @param orderDesc 是否倒序，true:倒序
     */
    public void listPanelTokens(String homeID, long addTime, boolean orderDesc, int pageSize, Callback<ListPanelTokensResponse> callback) {
        HomeApi.getInstance().listPanelTokens(homeID, addTime, orderDesc, pageSize).enqueue(callback);
    }

    public void getKeypadMemberPwdInfo(final String homeId, final String panelId, final String userId,
                                       Callback<KeypadMemberPwdInfoGetResponse> callback) {
        HomeApi.getInstance().getKeypadMemberPwdInfo(homeId, panelId, userId).enqueue(callback);
    }

    public void updateKeypadMemberPwdInfo(final String homeId, final String panelId, final String userId,
                                          final boolean enabled, Callback<KeypadMemberPwdUpdateResponse> callback) {
        HomeApi.getInstance().updateKeypadMemberPwdInfo(homeId, panelId, userId, enabled).enqueue(callback);
    }

    public void resetKeypadMemberPwdInfo(final String homeId, final String panelId, final String userId,
                                         Callback<KeypadMemberPwdResetResponse> callback) {
        HomeApi.getInstance().resetKeypadMemberPwdInfo(homeId, panelId, userId).enqueue(callback);
    }

    public void getDailyMemoriesVideoUrl(String homeId, String record_id, Callback<DailyMemoriesVideoResponse> callback) {
        HomeApi.getInstance().getDailyMemoriesVideoUrl(homeId, record_id).enqueue(callback);
    }

    // ********************* 兼容模式（cawa）

    public void deleteMemberCompat(final String homeId, final String userId,
                                   Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().deleteMemberCompat(homeId, userId).enqueue(callback);
    }

    public void deleteHomeCompat(final String homeId, Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().deleteHomeCompat(homeId).enqueue(callback);
    }

    public void initHomeAndMemberCompat(@NotNull final InitHomeCompatParams params, Callback<HomeInitResponse> callback) {
        HomeApi.getInstance().initHomeAndMemberCompat(params).enqueue(callback);
    }

    public void updateBalanceContractBank(final String homeId, final String cardholder, final String iban, final String pwd, Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().updateBalanceContractBank(homeId, cardholder, iban, pwd).enqueue(callback);
    }

    public void terminateBalanceContract(final String homeId, final String sign, Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().terminateBalanceContract(homeId, sign).enqueue(callback);
    }

    public void getBalanceContractRecords(long createTime, String homeId, int pageSize, Callback<BalanceContractRecordsResponse> callback) {
        HomeApi.getInstance().getBalanceContractRecords(createTime, homeId, pageSize).enqueue(callback);
    }

    public void getBalanceContractRecordsById(String homeId, String recordId, Callback<SingleBalanceContractRecordResponse> callback) {
        HomeApi.getInstance().getBalanceContractRecordsById(homeId, recordId).enqueue(callback);
    }

    /**
     * 获取原始响应（用于调试）
     */
    public Call<okhttp3.ResponseBody> getBalanceContractRecordsByIdRaw(String homeId, String recordId) {
        return HomeApi.getInstance().getBalanceContractRecordsByIdRaw(homeId, recordId);
    }

    public void getBalanceContractUnsignTemplate(String homeId, Callback<BalanceContractUnsignTemplateResponse> callback) {
        HomeApi.getInstance().getBalanceContractUnsignTemplate(homeId).enqueue(callback);
    }

    public void getBalanceContractSignTemplate(String countryCode, Callback<BalanceContractSignTemplateResponse> callback) {
        HomeApi.getInstance().getBalanceContractSignTemplate(countryCode).enqueue(callback);
    }

    public void checkBmtDeviceBalanceContractStatus(String homeId, String deviceId,  Callback<StatusResponse> callback) {
        HomeApi.getInstance().checkBmtDeviceBalanceContractStatus(homeId, deviceId).enqueue(callback);
    }

    public void updateBalanceContractData(String homeId, Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().updateBalanceContractData(homeId).enqueue(callback);
    }

    public void bmtBalanceContractParticipationHours(long createTime, String homeId, int pageSize, Callback<BmtBalanceContractParticipationHoursResponse> callback) {
        HomeApi.getInstance().bmtBalanceContractParticipationHours(createTime, homeId, pageSize).enqueue(callback);
    }

    public void bmtAddBalanceContractParticipationHours(String homeId, ParticipationHour participationHour, Callback<StringResponseEntry>  callBack) {
        HomeApi.getInstance().bmtAddBalanceContractParticipationHours(homeId, participationHour).enqueue(callBack);
    }

    public void bmtUpdateBalanceContractParticipationHours(String homeId, ParticipationHour participationHour, Callback<StringResponseEntry>  callBack) {
        HomeApi.getInstance().bmtUpdateBalanceContractParticipationHours(homeId, participationHour).enqueue(callBack);
    }

    public void bmtDeleteBalanceContractParticipationHours(String homeId, String id, Callback<StringResponseEntry>  callBack) {
        HomeApi.getInstance().bmtDeleteBalanceContractParticipationHours(homeId, id).enqueue(callBack);
    }

    public void bmtIsTaskTimeInUpdatedRange(String homeId, ParticipationHour participationHour, Callback<BmtIsTaskTimeInUpdatedRangeResponse>  callBack) {
        HomeApi.getInstance().bmtIsTaskTimeInUpdatedRange(homeId, participationHour).enqueue(callBack);
    }

    public void getHomeLocation(String homeId,  Callback<HomeLocationResponse> callback) {
        HomeApi.getInstance().getHomeLocation(homeId).enqueue(callback);
    }

    public void bmtSaveLocation(String homeId, double latitude, double longitude,  Callback<StringResponseEntry> callback) {
        HomeApi.getInstance().bmtSaveLocation(homeId, latitude, longitude).enqueue(callback);
    }
}
