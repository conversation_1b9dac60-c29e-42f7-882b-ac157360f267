package com.dinsafer.module_home.http;

/**
 * Home相关网络请求路径
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/12 3:05 PM
 */
class HomeUrls {
    static final String URL_QUERY_HOME_LIST = "/home/<USER>/";
    static final String URL_CREATE_HOME = "/home/<USER>/";
    static final String URL_RENAME_HOME = "/home/<USER>/";
    static final String URL_LIST_HOME_MEMBERS = "/home/<USER>/";
    static final String URL_UPDATE_HOME_MEMBER = "/home/<USER>/";
    static final String URL_NEW_HOME_CONTACT = "/home/<USER>/";
    static final String URL_LIST_HOME_CONTACT = "/home/<USER>/";
    static final String URL_REMOVE_HOME_CONTACT = "/home/<USER>/";
    static final String URL_UPDATE_HOME_CONTACT = "/home/<USER>/";
    static final String URL_NEW_INVITATION_CODE = "/home/<USER>/";
    static final String URL_VERIFY_INVITATION_CODE = "/home/<USER>/";
    static final String URL_REMOVE_MEMBER = "/home/<USER>/";
    static final String URL_REMOVE_HOME = "/home/<USER>/";
    static final String URL_GET_HOME_INFO = "/home/<USER>/";
    static final String URL_SET_HOME_NOTIFICATION_LANGUAGE = "/home/<USER>/";
    static final String URL_GET_HOME_NOTIFICATION_LANGUAGE = "/home/<USER>/";
    static final String URL_E2E_LOGIN_HOME = "/home/<USER>/";
    static final String URL_E2E_LOGOUT_HOME = "/home/<USER>/";
    static final String URL_HOME_MEMBER_AVATARS = "/home/<USER>/";
    static final String URL_HOME_BIND_PANEL = "/device/bind-home/";
    static final String URL_LIST_MOTION_RECORDS = "/ipc/md/get-event-list/";
    static final String URL_DELETE_MOTION_RECORDS = "/ipc/md/delete-event-motion-records/";
    static final String URL_GET_MOTION_RECORD_VIDEO_URL = "/ipc/md/get-ipc-video-url/";
    static final String URL_IS_ONLY_ADMIN = "/home/<USER>/";
    static final String URL_FORCE_DELETE_HOME = "/home/<USER>/";
    static final String URL_GET_TOTAL_MOTION_RECORDS_COUNT = "/ipc/md/get-total-motion-records/";
    static final String URL_LIST_EVENT_MOTION_RECORDS = "/ipc/md/list-event-motion-records/";
    static final String URL_GET_EVENT_LIST = "/device/list-eventlists/";
    static final String URL_GET_EVENT_LIST_BY_FILTER = "/device/get-event-list/";
    static final String URL_GET_DAILY_MEMORIES_VIDEO_URL = "/ipc/md/get-daily-memories-video-url/";
    static final String URL_GET_IPC_FIRMWARE_VERSION = "/ipc/get-ipc-version-datas/";
    static final String URL_GET_MOTION_EVENT_DATES = "/ipc/md/get-event-dates/";
    static final String URL_GET_MOTION_EVENT_VIDEOS = "/ipc/md/get-event-videos/";
    static final String URL_GET_MOTION_EVENTS = "/ipc/md/get-events/";
    static final String URL_GET_LAST_MOTION_IPC_LIST = "/ipc/md/get-last-triggered-time/";
    static final String URL_GET_MOTION_EVENT_COVER = "/ipc/md/get-event-covers/";

    static final String URL_GET_HOME = "/home/<USER>/"; // 获取指定家庭的信息
    static final String URL_GET_WIDGET_COUNT = "/home/<USER>/"; // 获取家庭中所有 widget 配件的数量
    static final String URL_LIST_PANEL_TOKENS = "/device/list-device-tokens/"; // 获取指定家庭下的主机 token

    // ****************** 定制键盘操作密码相关

    static final String URL_KEYPAD_GET_MEMBER_PWD_INFO = "/home/<USER>/";
    static final String URL_KEYPAD_UPDATE_MEMBER_PWD_INFO = "/home/<USER>/";
    static final String URL_KEYPAD_RESET_MEMBER_PWD_INFO = "/home/<USER>/";

    // ********************* 兼容模式（cawa）
    static final String URL_REMOVE_MEMBER_COMPAT = "/home/<USER>/delete-member/";
    static final String URL_DELETE_HOME_COMPAT = "/home/<USER>/delete-home/";
    static final String URL_INIT_HOME_AND_MEMBER_COMPAT = "/home/<USER>/init-home-and-member/";

    static final String URL_BMT_GET_FAMILY_BALANCE_CONTRACT_INFO = "/bmt/get-family-balance-contract-info/";
    static final String URL_BMT_UPLOAD_TOKEN = "/bmt/get-upload-token-app/";
    static final String URL_BMT_CLOUDFLARE_UPLOAD_TOKEN = "/bmt/get-app-cloudflare-r2-upload-token/";
    static final String URL_BMT_SAVE_FAMILY_BALANCE_CONTRACT_INFO = "/bmt/save-family-balance-contract-info-v2/";
    static final String URL_BMT_SAVE_FAMILY_BALANCE_CONTRACT_INFO_V3 = "/bmt/save-family-balance-contract-info-v3/";
    static final String URL_BMT_LIST_REGION_ELECTRICITY_SUPPLIER = "/bmt/list-region-electricity-supplier-v2/";
    static final String URL_GET_COUNTY_LIST = "/bmt/list-region-countries/";
    // 修改调频银行账户接口
    static final String URL_UPDATE_BALANCE_CONTRACT_BANK = "/bmt/update-balance-contract-bank/";
    // 获取授权记录
    static final String URL_GET_BALANCE_CONTRACT_RECORDS = "/bmt/get-balance-contract-records/";
    static final String URL_GET_BALANCE_CONTRACT_RECORDS_V3 = "/bmt/get-balance-contract-records-v3/";
    // 调频解约
    static final String URL_TERMINATE_CONTRACT = "/bmt/terminate-balance-contract/";
    static final String URL_TERMINATE_CONTRACT_V3 = "/bmt/terminate-balance-contract-v3/";
    // 获取解约模板、被授权人信息相关数据接口
    static final String URL_GET_BALANCE_CONTRACT_UNSIGN_TEMPLATE = "/bmt/get-balance-contract-unsign-template/";
    static final String URL_GET_BALANCE_CONTRACT_UNSIGN_TEMPLATE_V3 = "/bmt/get-balance-contract-unsign-template-v3/";
    // 获取签约模板数据接口
    static final String URL_GET_BALANCE_CONTRACT_SIGN_TEMPLATE = "/bmt/get-balance-contract-sign-template/";
    // 检查设备是否签约状态
    static final String URL_BMT_CHECK_DEVICE_BALANCE_CONTRACT_STATUS = "/bmt/check-device-balance-contract-status/";

    // 处理调频签约的存量数据-Confirm
    static final String URL_UPDATE_BALANCE_CONTRACT_DATA = "/bmt/update-balance-contract-data/";
    // 获取调频计划时间列表
    static final String URL_BMT_LIST_BALANCE_CONTRACT_PARTICIPATION_HOURS = "/bmt/list-balance-contract-participation-hours/";
    // 新增调频时间
    static final String URL_BMT_ADD_BALANCE_CONTRACT_PARTICIPATION_HOURS = "/bmt/add-balance-contract-participation-hours/";
    // 修改调频时间
    static final String URL_BMT_UPDATE_BALANCE_CONTRACT_PARTICIPATION_HOURS = "/bmt/update-balance-contract-participation-hours/";
    // 删除调频时间
    static final String URL_BMT_DELETE_BALANCE_CONTRACT_PARTICIPATION_HOURS = "/bmt/delete-balance-contract-participation-hours/";
    // 当前家庭现存调频任务时间是否在修改后的时间范围内（子集）
    static final String URL_BMT_IS_TASK_TIME_IN_UPDATED_RANGE = "/bmt/is-task-time-in-updated-range/";
    // 获取家庭定位信息
    static final String URL_BMT_GET_HOME_LOCATION = "/bmt/get-home-location/";
    // 提交授权定位
    static final String URL_BMT_SAVE_LOCATION = "/bmt/save-location/";



}
