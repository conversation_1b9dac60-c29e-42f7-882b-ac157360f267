package com.dinsafer.module_home;

import android.text.TextUtils;

import com.dinsafer.dincore.common.CommonCmdEvent;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dincore.http.NetWorkException;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.user.api.ILogoutCallback;
import com.dinsafer.dincore.user.bean.LogoutEvent;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dssupport.msctlib.msct.MsctResponse;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_home.api.IHomeCallBack;
import com.dinsafer.module_home.api.IHomeListCallBack;
import com.dinsafer.module_home.bean.AddContactParams;
import com.dinsafer.module_home.bean.BalanceContractRecordsResponse;
import com.dinsafer.module_home.bean.BalanceContractSignTemplateResponse;
import com.dinsafer.module_home.bean.BalanceContractUnsignTemplateResponse;
import com.dinsafer.module_home.bean.BmtBalanceContractParticipationHoursResponse;
import com.dinsafer.module_home.bean.BmtIsTaskTimeInUpdatedRangeResponse;
import com.dinsafer.module_home.bean.DailyMemoriesVideoResponse;
import com.dinsafer.module_home.bean.EventListByFilterEntry;
import com.dinsafer.module_home.bean.EventListEntry;
import com.dinsafer.module_home.bean.Home;
import com.dinsafer.module_home.bean.HomeConstants;
import com.dinsafer.module_home.bean.HomeContact;
import com.dinsafer.module_home.bean.HomeInfo;
import com.dinsafer.module_home.bean.HomeInfoResponse;
import com.dinsafer.module_home.bean.HomeInitResponse;
import com.dinsafer.module_home.bean.HomeLocationResponse;
import com.dinsafer.module_home.bean.HomeMember;
import com.dinsafer.module_home.bean.InitHomeCompatParams;
import com.dinsafer.module_home.bean.MemberAvatars;
import com.dinsafer.module_home.bean.ParticipationHour;
import com.dinsafer.module_home.bean.SingleBalanceContractRecordResponse;

import org.greenrobot.eventbus.EventBus;
import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 兼容模式（cawa）家庭管理
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/3/22 4:52 下午
 */
class CompatHome extends BaseHome {
    private static final String ERROR_INFO = "Unsupported API";
    private static final String ERROR_INFO_EXT = ": not support on compat mode!";

    @Override
    protected void dispatchHomeMessage(MsctResponse msctResponse) {
        try {
            JSONObject msgObj = new JSONObject((String) msctResponse.getMsctContext().getDecodedPayload());
            final String cmd = DDJSONUtil.getString(msgObj, "cmd");
            if (!TextUtils.isEmpty(cmd)) {
                switch (cmd) {
                    case HomeConstants.CMD.AUTHORITY_UPDATED:
                        String homeId = DDJSONUtil.getString(msgObj, "home_id");
                        int level = DDJSONUtil.getInt(msgObj, "level");
                        if (null != currentHome && currentHome.getHomeID().equals(homeId)) {
                            currentHome.setLevel(level);
                            if (null != mCurrentHomeInfo) {
                                mCurrentHomeInfo.setLevel(level);
                            }
                        }
                        break;
                    case HomeConstants.CMD.MEMBER_DELETED:
                        homeId = DDJSONUtil.getString(msgObj, "home_id");
                        if (null != currentHome
                                && !TextUtils.isEmpty(currentHome.getHomeID())
                                && currentHome.getHomeID().equals(homeId)) {
                            DDLog.i(TAG, "当前用户被从房间移除了");
                            mHomeConnectionManager.releaseHomeConnect();
                            currentHome = null;
                            mCurrentHomeInfo = null;
                        }
                        break;
                    case HomeConstants.CMD.DEVICE_DELETED:
                        homeId = DDJSONUtil.getString(msgObj, "home_id");
                        String operatorUserId = DDJSONUtil.getString(msgObj, "userid");
                        if (null != mCurrentHomeInfo
                                && !TextUtils.isEmpty(mCurrentHomeInfo.getHomeId())
                                && mCurrentHomeInfo.getHomeId().equals(homeId)
                                && null != mCurrentHomeInfo.getDevice()) {

                            mCurrentHomeInfo.getDevice().setDeviceid("");
                            mCurrentHomeInfo.getDevice().setToken("");
                            mCurrentHomeInfo.getDevice().setName("");
                            if (!TextUtils.isEmpty(operatorUserId)
                                    && null != DinSDK.getUserInstance().getUser()
                                    && operatorUserId.equals(DinSDK.getUserInstance().getUser().getUser_id())) {
                                DDLog.i(TAG, "Delete offline panel by self!!!");
                                break;
                            }

                            CommonCmdEvent event = new CommonCmdEvent(CommonCmdEvent.CMD.DELETE_OFFLINE_PANEL);
                            try {
                                JSONObject args = new JSONObject();
                                args.put("home_id", homeId);
                                event.setExtra(args.toString());
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            EventBus.getDefault().post(event);
                        }
                        break;
                    case HomeConstants.CMD.FORCE_LOGOUT:
                        DinSDK.getUserInstance().logout(new ILogoutCallback() {
                            @Override
                            public void onSuccess() {
                                EventBus.getDefault().post(new LogoutEvent(false));
                            }
                        });
                        break;
                    case CommonCmdEvent.CMD.DSCAM_ADD:
                    case CommonCmdEvent.CMD.DSCAM_DELETE:
                    case CommonCmdEvent.CMD.DSCAM_RENAME:
                    case CommonCmdEvent.CMD.DSDOORBELL_ADD:
                    case CommonCmdEvent.CMD.DSDOORBELL_DELETE:
                    case CommonCmdEvent.CMD.DSDOORBELL_RENAME:
                    case CommonCmdEvent.CMD.BMT_ADD:
                    case CommonCmdEvent.CMD.BMT_DELETE:
                    case CommonCmdEvent.CMD.BMT_RENAME:
                    case CommonCmdEvent.CMD.UPDATE_REGION:
                        CommonCmdEvent event = new CommonCmdEvent(cmd);
                        msgObj.put("homeID", currentHome.getHomeID());
                        event.setExtra(msgObj.toString());
                        EventBus.getDefault().post(event);
                        break;
                    default:
                        DDLog.e(TAG, "Unhandled cmd: " + cmd);
                        break;
                }
            }
        } catch (Exception e) {
            DDLog.e(TAG, "Error on process message from home msct!!!");
            e.printStackTrace();
        }
        for (IHomeCallBack homeCallBack : homeCallBacks) {
            homeCallBack.onMessage((String) msctResponse.getMsctContext().getDecodedPayload());
        }
    }

    @Override
    public void createHome(String homeName, String language, IDefaultCallBack2<Home> callBack) {
        onDefaultError(callBack);
    }

    @Override
    public void reNameHome(String homeID, String homeName, IDefaultCallBack callBack) {
        if (TextUtils.isEmpty(homeID)
                || TextUtils.isEmpty(homeName)) {
            DDLog.e(TAG, "Empty homeId or homeName");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId or homeName.");
            }
            return;
        }
        homeRepo.reNameHome(homeID, homeName, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (null != mCurrentHomeInfo) {
                    mCurrentHomeInfo.setName(homeName);
                }
                if (null != currentHome) {
                    currentHome.setHomeName(homeName);
                }
                if (callBack != null) {
                    callBack.onSuccess();
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }


    @Override
    public void queryHomeList(IHomeListCallBack callback) {
        if (null != callback) {
            callback.onError(ErrorCode.ERROR_UNSUPPORTED_API, ERROR_INFO + ERROR_INFO_EXT);
        }
    }

    @Override
    public void removeHome(String homeID, IDefaultCallBack callBack) {
        onDefaultError(callBack);
    }

    @Override
    public void queryHomeMemberList(String homeID, IDefaultCallBack2<List<HomeMember>> callBack) {
        onDefaultError(callBack);
    }

    @Override
    public void newHomeContact(String homeId, @NotNull AddContactParams params, IDefaultCallBack callBack) {
        onDefaultError(callBack);
    }

    @Override
    public void listHomeContact(String homeId, IDefaultCallBack2<List<HomeContact>> callBack) {
        onDefaultError(callBack);
    }

    @Override
    public void removeHomeContact(String homeId, String contactId, IDefaultCallBack callBack) {
        onDefaultError(callBack);
    }

    @Override
    public void updateHomeContact(String homeId, @NotNull HomeContact param, IDefaultCallBack callBack) {
        onDefaultError(callBack);
    }

    @Override
    public void getInvitationFamilyMemberCode(String homeID, int level, IDefaultCallBack2<String> callBack) {
        onDefaultError(callBack);
    }

    @Override
    public void verifyInvitationFamilyMemberCode(String code, IDefaultCallBack2<Home> callBack) {
        onDefaultError(callBack);
    }

    @Override
    public void changeFamilyMemberPermission(String homeID, String user_id, int level, IDefaultCallBack callBack) {
        onDefaultError(callBack);
    }

    @Override
    public void removeFamilyMember(String homeID, String user_id, IDefaultCallBack callBack) {
        homeRepo.deleteMemberCompat(homeID, user_id, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (callBack != null) {
                    callBack.onSuccess();
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void switchHomeNotGetInfo(String homeID, IDefaultCallBack callBack) {
        onDefaultError(callBack);
    }

    @Override
    public void switchHome(String homeID, IDefaultCallBack2<HomeInfoResponse> callBack) {
        onDefaultError(callBack);
    }

    @Override
    public void refreshCurrentHomeInfo(IDefaultCallBack2<HomeInfoResponse> callBack) {
        onDefaultError(callBack);
    }

    @Override
    public void getHomeNotificationLanguage(String homeID, IDefaultCallBack2<String> callBack) {
        onDefaultError(callBack);
    }

    @Override
    public void getHomeMemberAvatars(String homeID, IDefaultCallBack2<MemberAvatars> callBack) {
        onDefaultError(callBack);
    }

    @Override
    public void bindPanel(String homeId, String panelId, IDefaultCallBack callBack) {
        onDefaultError(callBack);
    }

    @Override
    public void isOnlyAdmin(String homeId, IDefaultCallBack2<Boolean> callBack2) {
        onDefaultError(callBack2);
    }

    @Override
    public void forceDeleteHome(String homeId, IDefaultCallBack callBack) {
        DDLog.i(TAG, "forceDeleteHome");
        if (TextUtils.isEmpty(homeId)) {
            DDLog.e(TAG, "Empty homeId.");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId.");
            }
            return;
        }

        homeRepo.deleteHomeCompat(homeId, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (callBack != null) {
                    if (null == response.body()) {
                        callBack.onError(ErrorCode.DEFAULT, "Empty result.");
                    } else if (response.body().getStatus() != 1) {
                        callBack.onError(response.body().getStatus(), response.body().getErrorMessage());
                    } else {
                        callBack.onSuccess();
                    }
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                DDLog.e("home", "forceDeleteHome onFailure:" + t.getMessage());
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void getEventListData(String panelId, int limit, long timestamp, String filters, IDefaultCallBack2<EventListEntry> callback) {
        onDefaultError(callback);
    }

    @Override
    public void getEventListDataByFilter(int limit, long time, String type_ids, IDefaultCallBack2<EventListByFilterEntry> callback) {
        onDefaultError(callback);
    }

    @Override
    public void getDailyMemoriesVideoUrl(String record_id, IDefaultCallBack2<DailyMemoriesVideoResponse> callback) {
        onDefaultError(callback);
    }

    @Override
    public void initHomeWithPanel(@NotNull InitHomeCompatParams params, IDefaultCallBack2<HomeInfo> callback) {
        if (!params.checkParams()) {
            DDLog.e(TAG, "Params error: mpty device id or token");
            if (null != callback) {
                callback.onError(ErrorCode.PARAM_ERROR, "Empty device id or token.");
            }
            return;
        }

        homeRepo.initHomeAndMemberCompat(params, new Callback<HomeInitResponse>() {
            @Override
            public void onResponse(Call<HomeInitResponse> call, Response<HomeInitResponse> response) {
                if (callback != null) {
                    if (null == response.body()) {
                        callback.onError(ErrorCode.DEFAULT, "Empty result.");
                    } else if (response.body().getStatus() != 1) {
                        callback.onError(response.body().getStatus(), response.body().getErrorMessage());
                    } else {
                        final HomeInitResponse.ResultBean info = response.body().getResult();
                        final HomeInfo homeInfo = new HomeInfo();
                        homeInfo.setHomeId(info.getHome_id());
                        homeInfo.setLanguage(info.getLanguage());
                        homeInfo.setLevel(info.getLevel());
                        final Home home = new Home(homeInfo.getHomeId(), homeInfo.getName());
                        home.setLevel(info.getLevel());
                        onSwitchHome(home);

                        callback.onSuccess(homeInfo);
                    }
                }
            }

            @Override
            public void onFailure(Call<HomeInitResponse> call, Throwable t) {
                DDLog.e("home", "initHomeWithPanel onFailure:" + t.getMessage());
                t.printStackTrace();
                if (callback != null) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callback.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void updateBalanceContractBank(String homeId, String cardholder, String iban, String pwd, IDefaultCallBack callback) {
        onDefaultError(callback);
    }

    @Override
    public void terminateBalanceContract(String homeId, String sign, IDefaultCallBack callback) {
        onDefaultError(callback);
    }

    @Override
    public void getBalanceContractRecordsById(String homeId, String recordId, IDefaultCallBack2<SingleBalanceContractRecordResponse.ResultBean> callback) {
        onDefaultError(callback);
    }

    @Override
    public void getBalanceContractRecords(long createTime, String homeId, int pageSize, IDefaultCallBack2<BalanceContractRecordsResponse.ResultBean> callback) {
        onDefaultError(callback);
    }

    @Override
    public void getBalanceContractUnsignTemplate(String homeId, IDefaultCallBack2<BalanceContractUnsignTemplateResponse.ResultBean> callback) {
        onDefaultError(callback);
    }

    @Override
    public void getBalanceContractSignTemplate(String countryCode, IDefaultCallBack2<BalanceContractSignTemplateResponse.ResultBean> callback) {
        onDefaultError(callback);
    }

    @Override
    public void checkBmtDeviceBalanceContractStatus(String homeId, String deviceId, IDefaultCallBack2<Integer> callback) {
        onDefaultError(callback);
    }

    @Override
    public void bmtBalanceContractParticipationHours(long createTime, String homeId, int pageSize, IDefaultCallBack2<BmtBalanceContractParticipationHoursResponse.ResultBean> callback) {
        onDefaultError(callback);
    }

    @Override
    public void bmtAddBalanceContractParticipationHours(String homeId, ParticipationHour participationHour, IDefaultCallBack callback) {
        onDefaultError(callback);
    }

    @Override
    public void bmtUpdateBalanceContractParticipationHours(String homeId, ParticipationHour participationHour, IDefaultCallBack callback) {
        onDefaultError(callback);
    }

    @Override
    public void bmtDeleteBalanceContractParticipationHours(String homeId, String id, IDefaultCallBack callback) {
        onDefaultError(callback);
    }

    @Override
    public void bmtIsTaskTimeInUpdatedRange(String homeId, ParticipationHour participationHour, IDefaultCallBack2<BmtIsTaskTimeInUpdatedRangeResponse.ResultBean> callback) {
        onDefaultError(callback);
    }

    @Override
    public void updateBalanceContractData(String homeId, IDefaultCallBack callBack) {
        onDefaultError(callBack);
    }

    @Override
    public void getHomeLocation(String homeId, IDefaultCallBack2<HomeLocationResponse.ResultBean> callback) {
        onDefaultError(callback);
    }

    @Override
    public void bmtSaveLocation(String homeId, double latitude, double longitude, IDefaultCallBack callback) {
        onDefaultError(callback);
    }

    private void onDefaultError(IDefaultCallBack callback) {
        onDefaultError(ErrorCode.ERROR_UNSUPPORTED_API, ERROR_INFO + ERROR_INFO_EXT, callback);
    }

    private void onDefaultError(IDefaultCallBack2<?> callback) {
        onDefaultError(ErrorCode.ERROR_UNSUPPORTED_API, ERROR_INFO + ERROR_INFO_EXT, callback);
    }
}
