package com.dinsafer.module_home.debug;

import android.text.TextUtils;

import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dssupport.utils.DDLog;

import java.io.File;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.RequestBody;

/**
 * 文件上传调试助手
 * 用于诊断ClassCastException问题
 * 
 * <AUTHOR> Assistant
 * @since 2025/7/16
 */
public class UploadDebugHelper {
    private static final String TAG = "UploadDebugHelper";
    
    /**
     * 调试RequestBody创建过程
     */
    public static void debugRequestBodyCreation(String filePath, String contentType) {
        DDLog.i(TAG, "=== 调试RequestBody创建过程 ===");
        
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                DDLog.e(TAG, "文件不存在: " + filePath);
                return;
            }
            
            DDLog.i(TAG, "文件路径: " + filePath);
            DDLog.i(TAG, "文件大小: " + file.length() + " bytes");
            DDLog.i(TAG, "Content-Type: " + contentType);
            
            // 步骤1: 创建MediaType
            DDLog.i(TAG, "步骤1: 创建MediaType...");
            MediaType mediaType = MediaType.parse(contentType);
            if (mediaType != null) {
                DDLog.i(TAG, "MediaType创建成功: " + mediaType.toString());
                DDLog.i(TAG, "MediaType类型: " + mediaType.type());
                DDLog.i(TAG, "MediaType子类型: " + mediaType.subtype());
            } else {
                DDLog.e(TAG, "MediaType创建失败");
                return;
            }
            
            // 步骤2: 创建RequestBody
            DDLog.i(TAG, "步骤2: 创建RequestBody...");
            RequestBody requestBody = RequestBody.create(mediaType, file);
            if (requestBody != null) {
                DDLog.i(TAG, "RequestBody创建成功");
                DDLog.i(TAG, "RequestBody类型: " + requestBody.getClass().getName());
                DDLog.i(TAG, "RequestBody ContentType: " + requestBody.contentType());
                
                try {
                    long contentLength = requestBody.contentLength();
                    DDLog.i(TAG, "RequestBody ContentLength: " + contentLength);
                } catch (IOException e) {
                    DDLog.e(TAG, "获取ContentLength失败: " + e.getMessage());
                }
            } else {
                DDLog.e(TAG, "RequestBody创建失败");
            }
            
        } catch (Exception e) {
            DDLog.e(TAG, "调试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 调试OkHttpClient创建过程
     */
    public static OkHttpClient debugOkHttpClientCreation() {
        DDLog.i(TAG, "=== 调试OkHttpClient创建过程 ===");
        
        try {
            DDLog.i(TAG, "创建OkHttpClient.Builder...");
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            
            DDLog.i(TAG, "设置超时时间...");
            builder.connectTimeout(30, TimeUnit.SECONDS);
            builder.writeTimeout(60, TimeUnit.SECONDS);
            builder.readTimeout(30, TimeUnit.SECONDS);
            
            DDLog.i(TAG, "构建OkHttpClient...");
            OkHttpClient client = builder.build();
            
            DDLog.i(TAG, "OkHttpClient创建成功");
            DDLog.i(TAG, "OkHttpClient类型: " + client.getClass().getName());
            DDLog.i(TAG, "连接超时: " + client.connectTimeoutMillis() + "ms");
            DDLog.i(TAG, "写入超时: " + client.writeTimeoutMillis() + "ms");
            DDLog.i(TAG, "读取超时: " + client.readTimeoutMillis() + "ms");
            
            return client;
            
        } catch (Exception e) {
            DDLog.e(TAG, "OkHttpClient创建失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 调试HTTP请求构建过程
     */
    public static okhttp3.Request debugRequestBuilding(String uploadUrl, RequestBody requestBody) {
        DDLog.i(TAG, "=== 调试HTTP请求构建过程 ===");
        
        try {
            DDLog.i(TAG, "上传URL: " + uploadUrl);
            DDLog.i(TAG, "RequestBody类型: " + (requestBody != null ? requestBody.getClass().getName() : "null"));
            
            if (requestBody == null) {
                DDLog.e(TAG, "RequestBody为null，无法构建请求");
                return null;
            }
            
            DDLog.i(TAG, "创建Request.Builder...");
            okhttp3.Request.Builder requestBuilder = new okhttp3.Request.Builder();
            
            DDLog.i(TAG, "设置URL...");
            requestBuilder.url(uploadUrl);
            
            DDLog.i(TAG, "设置PUT方法和RequestBody...");
            requestBuilder.put(requestBody);
            
            DDLog.i(TAG, "构建Request...");
            okhttp3.Request request = requestBuilder.build();
            
            DDLog.i(TAG, "Request构建成功");
            DDLog.i(TAG, "Request方法: " + request.method());
            DDLog.i(TAG, "Request URL: " + request.url());
            DDLog.i(TAG, "Request Headers: " + request.headers());
            
            return request;
            
        } catch (Exception e) {
            DDLog.e(TAG, "Request构建失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 完整的调试上传流程
     */
    public static void debugCompleteUploadProcess(String uploadUrl, String filePath, String contentType, 
                                                 IDefaultCallBack2<String> callback) {
        DDLog.i(TAG, "=== 开始完整调试上传流程 ===");
        
        try {
            // 步骤1: 验证参数
            DDLog.i(TAG, "步骤1: 验证参数...");
            if (TextUtils.isEmpty(filePath)) {
                DDLog.e(TAG, "文件路径为空");
                if (callback != null) callback.onError(ErrorCode.PARAM_ERROR, "文件路径为空");
                return;
            }
            
            if (TextUtils.isEmpty(uploadUrl)) {
                DDLog.e(TAG, "上传URL为空");
                if (callback != null) callback.onError(ErrorCode.PARAM_ERROR, "上传URL为空");
                return;
            }
            
            File file = new File(filePath);
            if (!file.exists()) {
                DDLog.e(TAG, "文件不存在: " + filePath);
                if (callback != null) callback.onError(ErrorCode.PARAM_ERROR, "文件不存在");
                return;
            }
            
            DDLog.i(TAG, "参数验证通过");
            
            // 步骤2: 调试RequestBody创建
            DDLog.i(TAG, "步骤2: 调试RequestBody创建...");
            debugRequestBodyCreation(filePath, contentType);
            
            // 步骤3: 创建实际的RequestBody
            DDLog.i(TAG, "步骤3: 创建实际的RequestBody...");
            MediaType mediaType = MediaType.parse(contentType);
            RequestBody requestBody = RequestBody.create(mediaType, file);
            
            // 步骤4: 调试OkHttpClient创建
            DDLog.i(TAG, "步骤4: 调试OkHttpClient创建...");
            OkHttpClient client = debugOkHttpClientCreation();
            if (client == null) {
                DDLog.e(TAG, "OkHttpClient创建失败");
                if (callback != null) callback.onError(ErrorCode.DEFAULT, "OkHttpClient创建失败");
                return;
            }
            
            // 步骤5: 调试Request构建
            DDLog.i(TAG, "步骤5: 调试Request构建...");
            okhttp3.Request request = debugRequestBuilding(uploadUrl, requestBody);
            if (request == null) {
                DDLog.e(TAG, "Request构建失败");
                if (callback != null) callback.onError(ErrorCode.DEFAULT, "Request构建失败");
                return;
            }
            
            // 步骤6: 执行请求
            DDLog.i(TAG, "步骤6: 执行请求...");
            client.newCall(request).enqueue(new okhttp3.Callback() {
                @Override
                public void onFailure(okhttp3.Call call, IOException e) {
                    DDLog.e(TAG, "请求执行失败: " + e.getMessage());
                    DDLog.e(TAG, "异常类型: " + e.getClass().getName());
                    e.printStackTrace();
                    
                    // 分析异常类型
                    if (e instanceof ClassCastException) {
                        DDLog.e(TAG, "发现ClassCastException！");
                        DDLog.e(TAG, "这通常是RequestBody类型转换问题");
                    }
                    
                    if (callback != null) {
                        callback.onError(ErrorCode.DEFAULT, "请求执行失败: " + e.getMessage());
                    }
                }
                
                @Override
                public void onResponse(okhttp3.Call call, okhttp3.Response response) throws IOException {
                    try {
                        DDLog.i(TAG, "收到响应，状态码: " + response.code());
                        DDLog.i(TAG, "响应Headers: " + response.headers());
                        
                        if (response.isSuccessful()) {
                            String responseBody = response.body() != null ? response.body().string() : "";
                            DDLog.i(TAG, "请求成功，响应内容: " + responseBody);
                            if (callback != null) {
                                callback.onSuccess(responseBody);
                            }
                        } else {
                            String errorBody = response.body() != null ? response.body().string() : "";
                            DDLog.e(TAG, "请求失败，错误内容: " + errorBody);
                            if (callback != null) {
                                callback.onError(response.code(), "请求失败: " + errorBody);
                            }
                        }
                    } finally {
                        response.close();
                    }
                }
            });
            
        } catch (Exception e) {
            DDLog.e(TAG, "调试过程中发生异常: " + e.getMessage());
            DDLog.e(TAG, "异常类型: " + e.getClass().getName());
            e.printStackTrace();
            
            if (callback != null) {
                callback.onError(ErrorCode.DEFAULT, "调试过程异常: " + e.getMessage());
            }
        }
    }
}
