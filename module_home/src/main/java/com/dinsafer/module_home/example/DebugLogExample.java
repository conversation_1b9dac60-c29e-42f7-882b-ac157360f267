package com.dinsafer.module_home.example;

import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.module_home.CommonHome;
import com.dinsafer.module_home.bean.SingleBalanceContractRecordResponse;
import com.dinsafer.dssupport.utils.DDLog;

/**
 * 调试日志使用示例
 * 展示如何使用新增的详细日志来排查问题
 * 
 * <AUTHOR> Assistant
 * @since 2025/7/16
 */
public class DebugLogExample {
    private static final String TAG = "DebugLogExample";
    
    private CommonHome commonHome;
    
    public DebugLogExample(CommonHome commonHome) {
        this.commonHome = commonHome;
    }
    
    /**
     * 演示如何使用详细日志排查问题
     */
    public void demonstrateDebugLogging() {
        DDLog.i(TAG, "=== 开始演示调试日志功能 ===");
        
        String homeId = "test_home_id";
        String recordId = "test_record_id";
        
        DDLog.i(TAG, "调用getBalanceContractRecordsById方法");
        DDLog.i(TAG, "现在会输出详细的调试信息，包括：");
        DDLog.i(TAG, "1. 请求参数验证");
        DDLog.i(TAG, "2. HTTP响应状态");
        DDLog.i(TAG, "3. 响应体详细信息");
        DDLog.i(TAG, "4. 字段级别的数据检查");
        DDLog.i(TAG, "5. 错误情况的详细分析");
        
        commonHome.getBalanceContractRecordsById(homeId, recordId, new IDefaultCallBack2<SingleBalanceContractRecordResponse.ResultBean>() {
            @Override
            public void onSuccess(SingleBalanceContractRecordResponse.ResultBean result) {
                DDLog.i(TAG, "=== 业务层收到成功回调 ===");
                DDLog.i(TAG, "Result对象: " + result);
                
                // 这里可以进一步处理业务逻辑
                handleSuccessResult(result);
            }
            
            @Override
            public void onError(int errorCode, String errorMsg) {
                DDLog.e(TAG, "=== 业务层收到错误回调 ===");
                DDLog.e(TAG, "错误代码: " + errorCode);
                DDLog.e(TAG, "错误信息: " + errorMsg);
                
                // 根据错误信息进行相应处理
                handleError(errorCode, errorMsg);
            }
        });
    }
    
    /**
     * 测试不同的错误场景
     */
    public void testErrorScenarios() {
        DDLog.i(TAG, "=== 测试不同的错误场景 ===");
        
        // 场景1: 空参数
        DDLog.i(TAG, "场景1: 测试空参数");
        commonHome.getBalanceContractRecordsById("", "", new IDefaultCallBack2<SingleBalanceContractRecordResponse.ResultBean>() {
            @Override
            public void onSuccess(SingleBalanceContractRecordResponse.ResultBean result) {
                DDLog.w(TAG, "场景1意外成功");
            }
            
            @Override
            public void onError(int errorCode, String errorMsg) {
                DDLog.i(TAG, "场景1预期错误: " + errorMsg);
            }
        });
        
        // 场景2: 无效的recordId
        DDLog.i(TAG, "场景2: 测试无效的recordId");
        commonHome.getBalanceContractRecordsById("valid_home_id", "invalid_record_id", new IDefaultCallBack2<SingleBalanceContractRecordResponse.ResultBean>() {
            @Override
            public void onSuccess(SingleBalanceContractRecordResponse.ResultBean result) {
                DDLog.i(TAG, "场景2成功: " + result);
            }
            
            @Override
            public void onError(int errorCode, String errorMsg) {
                DDLog.i(TAG, "场景2错误: " + errorMsg);
                analyzeError(errorCode, errorMsg);
            }
        });
    }
    
    /**
     * 处理成功结果
     */
    private void handleSuccessResult(SingleBalanceContractRecordResponse.ResultBean result) {
        DDLog.i(TAG, "--- 处理成功结果 ---");
        
        if (result != null) {
            DDLog.i(TAG, "Result不为空，开始处理业务逻辑");
            
            // 这里可以根据实际的ResultBean字段进行处理
            // 例如：
            // DDLog.i(TAG, "记录ID: " + result.getId());
            // DDLog.i(TAG, "创建时间: " + result.getCreateTime());
            // DDLog.i(TAG, "状态: " + result.getStatus());
            
        } else {
            DDLog.w(TAG, "Result为空，这不应该发生在成功回调中");
        }
    }
    
    /**
     * 分析错误信息
     */
    private void analyzeError(int errorCode, String errorMsg) {
        DDLog.e(TAG, "--- 错误分析 ---");
        
        if (errorMsg.contains("params error")) {
            DDLog.e(TAG, "参数错误：请检查homeId和recordId是否正确");
        } else if (errorMsg.contains("HTTP")) {
            DDLog.e(TAG, "HTTP错误：服务器返回了错误状态码");
            if (errorMsg.contains("404")) {
                DDLog.e(TAG, "可能的原因：记录不存在或URL错误");
            } else if (errorMsg.contains("500")) {
                DDLog.e(TAG, "可能的原因：服务器内部错误");
            }
        } else if (errorMsg.contains("响应体为空")) {
            DDLog.e(TAG, "响应体为空：服务器返回了空响应");
        } else if (errorMsg.contains("Result字段为空")) {
            DDLog.e(TAG, "Result字段为空：服务器返回了响应但Result字段为空");
        } else if (errorMsg.contains("网络请求失败")) {
            DDLog.e(TAG, "网络请求失败：请检查网络连接");
        } else {
            DDLog.e(TAG, "未知错误类型：" + errorMsg);
        }
    }
    
    /**
     * 处理错误
     */
    private void handleError(int errorCode, String errorMsg) {
        DDLog.e(TAG, "--- 处理错误 ---");
        
        // 根据错误类型进行不同的处理
        if (errorMsg.contains("params error")) {
            // 参数错误，提示用户检查输入
            showUserMessage("请检查输入参数");
        } else if (errorMsg.contains("网络")) {
            // 网络错误，可以尝试重试
            showUserMessage("网络连接失败，请检查网络后重试");
        } else if (errorMsg.contains("HTTP 404")) {
            // 记录不存在
            showUserMessage("记录不存在");
        } else if (errorMsg.contains("HTTP 500")) {
            // 服务器错误
            showUserMessage("服务器暂时不可用，请稍后重试");
        } else {
            // 其他错误
            showUserMessage("操作失败，请重试");
        }
    }
    
    /**
     * 显示用户消息（示例方法）
     */
    private void showUserMessage(String message) {
        DDLog.i(TAG, "显示用户消息: " + message);
        // 实际实现中可能是Toast、Dialog或其他UI提示
    }
    
    /**
     * 日志分析指南
     */
    public void logAnalysisGuide() {
        DDLog.i(TAG, "=== 日志分析指南 ===");
        
        DDLog.i(TAG, "1. 请求阶段日志:");
        DDLog.i(TAG, "   - 查看请求参数是否正确");
        DDLog.i(TAG, "   - 确认参数验证是否通过");
        
        DDLog.i(TAG, "2. 网络响应日志:");
        DDLog.i(TAG, "   - HTTP状态码：200表示成功，4xx表示客户端错误，5xx表示服务器错误");
        DDLog.i(TAG, "   - 响应头信息：可能包含有用的调试信息");
        
        DDLog.i(TAG, "3. 响应体分析:");
        DDLog.i(TAG, "   - 响应体是否为空");
        DDLog.i(TAG, "   - 响应体的toString输出");
        DDLog.i(TAG, "   - 基础字段（status, message）的值");
        
        DDLog.i(TAG, "4. Result字段分析:");
        DDLog.i(TAG, "   - Result是否为空");
        DDLog.i(TAG, "   - Result的具体字段值");
        DDLog.i(TAG, "   - 通过反射查看所有字段");
        
        DDLog.i(TAG, "5. 错误情况分析:");
        DDLog.i(TAG, "   - 网络异常的具体类型和信息");
        DDLog.i(TAG, "   - 请求URL和方法");
        DDLog.i(TAG, "   - NetWorkException的详细信息");
        
        DDLog.i(TAG, "常见问题排查:");
        DDLog.i(TAG, "- 如果响应体为空但HTTP状态码成功：检查服务器实现");
        DDLog.i(TAG, "- 如果Result字段为空：检查JSON字段映射");
        DDLog.i(TAG, "- 如果toString输出为空：检查toString方法实现");
        DDLog.i(TAG, "- 如果反射显示字段有值但toString为空：检查toString方法");
    }
}
