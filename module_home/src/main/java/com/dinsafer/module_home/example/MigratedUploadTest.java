package com.dinsafer.module_home.example;

import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dincore.http.Api;
import com.dinsafer.module_home.BaseHome;
import com.dinsafer.dssupport.utils.DDLog;

/**
 * 测试移植到dincore后的文件上传功能
 * 
 * <AUTHOR> Assistant
 * @since 2025/7/16
 */
public class MigratedUploadTest {
    private static final String TAG = "MigratedUploadTest";
    
    private BaseHome baseHome;
    
    public MigratedUploadTest(BaseHome baseHome) {
        this.baseHome = baseHome;
    }
    
    /**
     * 测试通过BaseHome调用dincore的上传功能
     */
    public void testBaseHomeUpload() {
        DDLog.i(TAG, "=== 测试BaseHome调用dincore上传功能 ===");
        
        String testFilePath = "/sdcard/test_image.jpg";
        String contentType = "image/jpeg";
        int type = Api.getFileTypeByContentType(contentType);
        
        DDLog.i(TAG, "架构说明:");
        DDLog.i(TAG, "• BaseHome.uploadFileToCloudflareR2() -> Api.uploadFileToCloudflareR2()");
        DDLog.i(TAG, "• 获取token: dincore/Api.getCloudflareUploadToken()");
        DDLog.i(TAG, "• 文件上传: dincore/Api.uploadFileToCloudflareR2()");
        DDLog.i(TAG, "• 注意: 上传过程不使用token，token仅用于获取上传地址");
        
        baseHome.uploadFileToCloudflareR2(testFilePath, contentType, type, new IDefaultCallBack2<String>() {
            @Override
            public void onSuccess(String result) {
                DDLog.i(TAG, "🎉 BaseHome上传成功！");
                DDLog.i(TAG, "这证明移植到dincore成功");
                DDLog.i(TAG, "响应: " + result);
                
                analyzeSuccess();
            }
            
            @Override
            public void onError(int errorCode, String errorMsg) {
                DDLog.e(TAG, "❌ BaseHome上传失败");
                DDLog.e(TAG, "错误: " + errorCode + " - " + errorMsg);
                
                analyzeFailure(errorCode, errorMsg);
            }
        });
    }
    
    /**
     * 测试直接调用dincore的Api
     */
    public void testDirectApiUpload() {
        DDLog.i(TAG, "=== 测试直接调用dincore Api ===");
        
        String testFilePath = "/sdcard/test_image.jpg";
        String contentType = "image/jpeg";
        String homeId = "test_home_id"; // 实际使用时应该是真实的homeId
        int type = Api.getFileTypeByContentType(contentType);
        
        DDLog.i(TAG, "直接调用Api.getApi().uploadFileToCloudflareR2()");
        
        Api.getApi().uploadFileToCloudflareR2(testFilePath, contentType, homeId, type, new IDefaultCallBack2<String>() {
            @Override
            public void onSuccess(String result) {
                DDLog.i(TAG, "🎉 直接Api调用成功！");
                DDLog.i(TAG, "响应: " + result);
            }
            
            @Override
            public void onError(int errorCode, String errorMsg) {
                DDLog.e(TAG, "❌ 直接Api调用失败");
                DDLog.e(TAG, "错误: " + errorCode + " - " + errorMsg);
            }
        });
    }
    
    /**
     * 测试不同文件类型
     */
    public void testDifferentFileTypes() {
        DDLog.i(TAG, "=== 测试不同文件类型 ===");
        
        String[] testFiles = {
            "/sdcard/test_image.jpg",
            "/sdcard/test_video.mp4",
            "/sdcard/test_document.pdf"
        };
        
        String[] contentTypes = {
            "image/jpeg",
            "video/mp4", 
            "application/pdf"
        };
        
        for (int i = 0; i < testFiles.length; i++) {
            final String filePath = testFiles[i];
            final String contentType = contentTypes[i];
            final int type = Api.getFileTypeByContentType(contentType);
            final int index = i + 1;
            
            DDLog.i(TAG, "测试文件 " + index + ": " + contentType + " (type=" + type + ")");
            
            baseHome.uploadFileToCloudflareR2(filePath, contentType, type, new IDefaultCallBack2<String>() {
                @Override
                public void onSuccess(String result) {
                    DDLog.i(TAG, "✅ 文件 " + index + " 上传成功: " + filePath);
                }
                
                @Override
                public void onError(int errorCode, String errorMsg) {
                    DDLog.e(TAG, "❌ 文件 " + index + " 上传失败: " + errorMsg);
                }
            });
            
            // 添加延迟
            try {
                Thread.sleep(1500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
    
    /**
     * 分析成功原因
     */
    private void analyzeSuccess() {
        DDLog.i(TAG, "--- 移植成功分析 ---");
        DDLog.i(TAG, "✓ dincore/Api.java 实现了完整的上传流程");
        DDLog.i(TAG, "✓ dincore/CloudflareUploadTokenResponse 响应类正确");
        DDLog.i(TAG, "✓ BaseHome 成功调用 dincore 的 Api");
        DDLog.i(TAG, "✓ 上传过程正确分离：token获取 + 文件上传");
        
        DDLog.i(TAG, "架构优势:");
        DDLog.i(TAG, "• 代码复用：其他模块也可以使用dincore的上传功能");
        DDLog.i(TAG, "• 统一管理：所有网络请求都在dincore中");
        DDLog.i(TAG, "• 易于维护：上传逻辑集中在一个地方");
        DDLog.i(TAG, "• 一致性：使用相同的错误处理和回调机制");
    }
    
    /**
     * 分析失败原因
     */
    private void analyzeFailure(int errorCode, String errorMsg) {
        DDLog.e(TAG, "--- 移植失败分析 ---");
        
        if (errorMsg.contains("token")) {
            DDLog.e(TAG, "❌ token获取阶段失败");
            DDLog.e(TAG, "可能原因:");
            DDLog.e(TAG, "1. URL_CLOUDFLARE_UPLOAD_TOKEN 配置错误");
            DDLog.e(TAG, "2. getCloudflareUploadToken API参数错误");
            DDLog.e(TAG, "3. CloudflareUploadTokenResponse 解析错误");
        } else if (errorMsg.contains("上传失败")) {
            DDLog.e(TAG, "❌ 文件上传阶段失败");
            DDLog.e(TAG, "可能原因:");
            DDLog.e(TAG, "1. 上传URL无效");
            DDLog.e(TAG, "2. 文件读取权限问题");
            DDLog.e(TAG, "3. 网络连接问题");
        } else if (errorMsg.contains("文件")) {
            DDLog.e(TAG, "❌ 文件相关问题");
            DDLog.e(TAG, "检查文件路径和权限");
        } else {
            DDLog.e(TAG, "❌ 其他问题: " + errorMsg);
        }
        
        DDLog.e(TAG, "调试建议:");
        DDLog.e(TAG, "1. 检查dincore/Urls.java中的URL配置");
        DDLog.e(TAG, "2. 检查dincore/Api.java中的方法实现");
        DDLog.e(TAG, "3. 检查CloudflareUploadTokenResponse类定义");
        DDLog.e(TAG, "4. 使用测试URL验证基本功能");
    }
    
    /**
     * 对比移植前后的差异
     */
    public void compareBeforeAfterMigration() {
        DDLog.i(TAG, "=== 移植前后对比 ===");
        
        DDLog.i(TAG, "移植前 (module_home):");
        DDLog.i(TAG, "• HomeApi.getCloudflareUploadToken()");
        DDLog.i(TAG, "• HomeApi.uploadFileToCloudflareR2()");
        DDLog.i(TAG, "• BaseHome 包含完整上传逻辑");
        DDLog.i(TAG, "• CloudflareUploadTokenResponse 在 module_home");
        
        DDLog.i(TAG, "移植后 (dincore):");
        DDLog.i(TAG, "• Api.getCloudflareUploadToken()");
        DDLog.i(TAG, "• Api.uploadFileToCloudflareR2()");
        DDLog.i(TAG, "• BaseHome 只是简单调用 dincore Api");
        DDLog.i(TAG, "• CloudflareUploadTokenResponse 在 dincore");
        
        DDLog.i(TAG, "改进点:");
        DDLog.i(TAG, "✓ 代码复用性更好");
        DDLog.i(TAG, "✓ 架构更清晰");
        DDLog.i(TAG, "✓ 维护成本更低");
        DDLog.i(TAG, "✓ 其他模块可以直接使用");
    }
    
    /**
     * 生成使用指南
     */
    public void generateUsageGuide() {
        DDLog.i(TAG, "=== 移植后使用指南 ===");
        
        DDLog.i(TAG, "方式1: 通过BaseHome使用 (推荐给home模块)");
        DDLog.i(TAG, "baseHome.uploadFileToCloudflareR2(filePath, contentType, type, callback);");
        
        DDLog.i(TAG, "方式2: 直接使用dincore Api (推荐给其他模块)");
        DDLog.i(TAG, "Api.getApi().uploadFileToCloudflareR2(filePath, contentType, homeId, type, callback);");
        
        DDLog.i(TAG, "参数说明:");
        DDLog.i(TAG, "• filePath: 文件完整路径");
        DDLog.i(TAG, "• contentType: MIME类型 (如 'image/jpeg')");
        DDLog.i(TAG, "• homeId: 家庭ID (BaseHome会自动获取)");
        DDLog.i(TAG, "• type: 文件类型编号 (使用 Api.getFileTypeByContentType() 获取)");
        DDLog.i(TAG, "• callback: 回调接口");
        
        DDLog.i(TAG, "注意事项:");
        DDLog.i(TAG, "• 确保文件存在且有读取权限");
        DDLog.i(TAG, "• 上传过程分两步：获取token + 上传文件");
        DDLog.i(TAG, "• token仅用于获取上传地址，上传时不需要token");
        DDLog.i(TAG, "• 支持各种文件类型：图片、视频、音频、文档等");
    }
}
