package com.dinsafer.module_home.example;

import android.content.Context;
import android.webkit.MimeTypeMap;

import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.module_home.BaseHome;
import com.dinsafer.dssupport.utils.DDLog;

import java.io.File;

/**
 * 文件上传使用示例
 * 
 * <AUTHOR> Assistant
 * @since 2025/7/16
 */
public class FileUploadExample {
    private static final String TAG = "FileUploadExample";
    
    private BaseHome baseHome;
    
    public FileUploadExample(BaseHome baseHome) {
        this.baseHome = baseHome;
    }
    
    /**
     * 上传图片文件示例
     */
    public void uploadImageExample(String imagePath) {
        // 检查文件是否存在
        File imageFile = new File(imagePath);
        if (!imageFile.exists()) {
            DDLog.e(TAG, "图片文件不存在: " + imagePath);
            return;
        }
        
        // 根据文件扩展名确定MIME类型
        String contentType = getMimeType(imagePath);
        if (contentType == null) {
            contentType = "image/jpeg"; // 默认为JPEG格式
        }
        
        DDLog.i(TAG, "开始上传图片: " + imagePath + ", Content-Type: " + contentType);
        
        baseHome.uploadFileToCloudflareR2(imagePath, contentType, new IDefaultCallBack2<String>() {
            @Override
            public void onSuccess(String result) {
                DDLog.i(TAG, "图片上传成功: " + result);
                // 这里可以处理上传成功后的逻辑，比如保存文件URL等
            }
            
            @Override
            public void onError(int errorCode, String errorMsg) {
                DDLog.e(TAG, "图片上传失败: " + errorCode + " - " + errorMsg);
                // 这里可以处理上传失败的逻辑，比如重试或提示用户
            }
        });
    }
    
    /**
     * 上传视频文件示例
     */
    public void uploadVideoExample(String videoPath) {
        File videoFile = new File(videoPath);
        if (!videoFile.exists()) {
            DDLog.e(TAG, "视频文件不存在: " + videoPath);
            return;
        }
        
        String contentType = getMimeType(videoPath);
        if (contentType == null) {
            contentType = "video/mp4"; // 默认为MP4格式
        }
        
        DDLog.i(TAG, "开始上传视频: " + videoPath + ", Content-Type: " + contentType);
        
        baseHome.uploadFileToCloudflareR2(videoPath, contentType, new IDefaultCallBack2<String>() {
            @Override
            public void onSuccess(String result) {
                DDLog.i(TAG, "视频上传成功: " + result);
            }
            
            @Override
            public void onError(int errorCode, String errorMsg) {
                DDLog.e(TAG, "视频上传失败: " + errorCode + " - " + errorMsg);
            }
        });
    }
    
    /**
     * 上传PDF文档示例
     */
    public void uploadPdfExample(String pdfPath) {
        File pdfFile = new File(pdfPath);
        if (!pdfFile.exists()) {
            DDLog.e(TAG, "PDF文件不存在: " + pdfPath);
            return;
        }
        
        String contentType = "application/pdf";
        
        DDLog.i(TAG, "开始上传PDF: " + pdfPath + ", Content-Type: " + contentType);
        
        baseHome.uploadFileToCloudflareR2(pdfPath, contentType, new IDefaultCallBack2<String>() {
            @Override
            public void onSuccess(String result) {
                DDLog.i(TAG, "PDF上传成功: " + result);
            }
            
            @Override
            public void onError(int errorCode, String errorMsg) {
                DDLog.e(TAG, "PDF上传失败: " + errorCode + " - " + errorMsg);
            }
        });
    }
    
    /**
     * 批量上传文件示例
     */
    public void uploadMultipleFilesExample(String[] filePaths) {
        for (String filePath : filePaths) {
            File file = new File(filePath);
            if (!file.exists()) {
                DDLog.w(TAG, "跳过不存在的文件: " + filePath);
                continue;
            }
            
            String contentType = getMimeType(filePath);
            if (contentType == null) {
                contentType = "application/octet-stream"; // 默认二进制类型
            }
            
            DDLog.i(TAG, "上传文件: " + filePath);
            
            baseHome.uploadFileToCloudflareR2(filePath, contentType, new IDefaultCallBack2<String>() {
                @Override
                public void onSuccess(String result) {
                    DDLog.i(TAG, "文件上传成功: " + filePath + " -> " + result);
                }
                
                @Override
                public void onError(int errorCode, String errorMsg) {
                    DDLog.e(TAG, "文件上传失败: " + filePath + " - " + errorCode + " - " + errorMsg);
                }
            });
            
            // 添加延迟避免并发过多
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
    
    /**
     * 根据文件路径获取MIME类型
     */
    private String getMimeType(String filePath) {
        String extension = getFileExtension(filePath);
        if (extension != null && !extension.isEmpty()) {
            return MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension.substring(1)); // 去掉点号
        }
        return null;
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return null;
        }
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filePath.length() - 1) {
            return filePath.substring(lastDotIndex);
        }
        return null;
    }
    
    /**
     * 获取文件大小的可读格式
     */
    private String getReadableFileSize(long size) {
        if (size <= 0) return "0 B";
        
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        
        return String.format("%.1f %s", size / Math.pow(1024, digitGroups), units[digitGroups]);
    }
    
    /**
     * 检查文件大小是否在允许范围内
     */
    private boolean isFileSizeValid(String filePath, long maxSizeInBytes) {
        File file = new File(filePath);
        if (!file.exists()) {
            return false;
        }
        
        long fileSize = file.length();
        if (fileSize > maxSizeInBytes) {
            DDLog.w(TAG, "文件过大: " + getReadableFileSize(fileSize) + 
                    ", 最大允许: " + getReadableFileSize(maxSizeInBytes));
            return false;
        }
        
        return true;
    }
    
    /**
     * 带文件大小检查的上传示例
     */
    public void uploadWithSizeCheckExample(String filePath, long maxSizeInMB) {
        long maxSizeInBytes = maxSizeInMB * 1024 * 1024; // 转换为字节
        
        if (!isFileSizeValid(filePath, maxSizeInBytes)) {
            DDLog.e(TAG, "文件大小超出限制，取消上传");
            return;
        }
        
        String contentType = getMimeType(filePath);
        if (contentType == null) {
            contentType = "application/octet-stream";
        }
        
        baseHome.uploadFileToCloudflareR2(filePath, contentType, new IDefaultCallBack2<String>() {
            @Override
            public void onSuccess(String result) {
                DDLog.i(TAG, "文件上传成功: " + result);
            }
            
            @Override
            public void onError(int errorCode, String errorMsg) {
                DDLog.e(TAG, "文件上传失败: " + errorCode + " - " + errorMsg);
            }
        });
    }
}
