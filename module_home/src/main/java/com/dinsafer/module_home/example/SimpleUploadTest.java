package com.dinsafer.module_home.example;

import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.module_home.BaseHome;
import com.dinsafer.dssupport.utils.DDLog;

/**
 * 简单的文件上传测试类
 * 用于调试ClassCastException问题
 * 
 * <AUTHOR> Assistant
 * @since 2025/7/16
 */
public class SimpleUploadTest {
    private static final String TAG = "SimpleUploadTest";
    
    private BaseHome baseHome;
    
    public SimpleUploadTest(BaseHome baseHome) {
        this.baseHome = baseHome;
    }
    
    /**
     * 测试最基本的文件上传功能
     * 不使用token，直接上传到指定URL
     */
    public void testBasicUpload() {
        // 测试用的文件路径（请替换为实际存在的文件）
        String testFilePath = "/sdcard/test_image.jpg";
        
        // 测试用的上传URL（请替换为实际的上传地址）
        String testUploadUrl = "https://httpbin.org/put"; // 这是一个测试服务，会返回请求信息
        
        // 文件类型
        String contentType = "image/jpeg";
        
        DDLog.i(TAG, "开始测试基本上传功能...");
        DDLog.i(TAG, "文件路径: " + testFilePath);
        DDLog.i(TAG, "上传URL: " + testUploadUrl);
        DDLog.i(TAG, "Content-Type: " + contentType);
        
        baseHome.uploadFileDirectly(testUploadUrl, testFilePath, contentType, new IDefaultCallBack2<String>() {
            @Override
            public void onSuccess(String result) {
                DDLog.i(TAG, "=== 上传成功 ===");
                DDLog.i(TAG, "响应内容: " + result);
                
                // 如果使用httpbin.org，响应会包含请求的详细信息
                // 可以检查Content-Type和Content-Length是否正确设置
            }
            
            @Override
            public void onError(int errorCode, String errorMsg) {
                DDLog.e(TAG, "=== 上传失败 ===");
                DDLog.e(TAG, "错误代码: " + errorCode);
                DDLog.e(TAG, "错误信息: " + errorMsg);
                
                // 分析错误类型
                if (errorMsg.contains("ClassCastException")) {
                    DDLog.e(TAG, "发现ClassCastException错误，这是RequestBody类型转换问题");
                } else if (errorMsg.contains("ConnectException")) {
                    DDLog.e(TAG, "网络连接错误，请检查URL和网络状态");
                } else if (errorMsg.contains("FileNotFoundException")) {
                    DDLog.e(TAG, "文件不存在，请检查文件路径");
                } else {
                    DDLog.e(TAG, "其他错误类型");
                }
            }
        });
    }
    
    /**
     * 测试不同文件类型的上传
     */
    public void testDifferentFileTypes() {
        String[] testFiles = {
            "/sdcard/test_image.jpg",
            "/sdcard/test_video.mp4", 
            "/sdcard/test_document.pdf"
        };
        
        String[] contentTypes = {
            "image/jpeg",
            "video/mp4",
            "application/pdf"
        };
        
        String testUploadUrl = "https://httpbin.org/put";
        
        for (int i = 0; i < testFiles.length; i++) {
            final String filePath = testFiles[i];
            final String contentType = contentTypes[i];
            
            DDLog.i(TAG, "测试文件类型: " + contentType);
            
            baseHome.uploadFileDirectly(testUploadUrl, filePath, contentType, new IDefaultCallBack2<String>() {
                @Override
                public void onSuccess(String result) {
                    DDLog.i(TAG, "文件上传成功: " + filePath);
                }
                
                @Override
                public void onError(int errorCode, String errorMsg) {
                    DDLog.e(TAG, "文件上传失败: " + filePath + " - " + errorMsg);
                }
            });
            
            // 添加延迟避免并发过多
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
    
    /**
     * 测试错误处理
     */
    public void testErrorHandling() {
        DDLog.i(TAG, "=== 测试错误处理 ===");
        
        // 测试1: 不存在的文件
        DDLog.i(TAG, "测试1: 不存在的文件");
        baseHome.uploadFileDirectly("https://httpbin.org/put", "/sdcard/nonexistent_file.jpg", "image/jpeg", 
            new IDefaultCallBack2<String>() {
                @Override
                public void onSuccess(String result) {
                    DDLog.w(TAG, "意外成功: 不存在的文件不应该上传成功");
                }
                
                @Override
                public void onError(int errorCode, String errorMsg) {
                    DDLog.i(TAG, "预期错误: " + errorMsg);
                }
            });
        
        // 测试2: 无效的URL
        DDLog.i(TAG, "测试2: 无效的URL");
        baseHome.uploadFileDirectly("invalid-url", "/sdcard/test_image.jpg", "image/jpeg", 
            new IDefaultCallBack2<String>() {
                @Override
                public void onSuccess(String result) {
                    DDLog.w(TAG, "意外成功: 无效URL不应该上传成功");
                }
                
                @Override
                public void onError(int errorCode, String errorMsg) {
                    DDLog.i(TAG, "预期错误: " + errorMsg);
                }
            });
        
        // 测试3: 空的Content-Type
        DDLog.i(TAG, "测试3: 空的Content-Type");
        baseHome.uploadFileDirectly("https://httpbin.org/put", "/sdcard/test_image.jpg", "", 
            new IDefaultCallBack2<String>() {
                @Override
                public void onSuccess(String result) {
                    DDLog.i(TAG, "空Content-Type上传成功: " + result);
                }
                
                @Override
                public void onError(int errorCode, String errorMsg) {
                    DDLog.e(TAG, "空Content-Type上传失败: " + errorMsg);
                }
            });
    }
    
    /**
     * 调试信息输出
     */
    public void printDebugInfo() {
        DDLog.i(TAG, "=== 调试信息 ===");
        DDLog.i(TAG, "Android版本: " + android.os.Build.VERSION.RELEASE);
        DDLog.i(TAG, "API级别: " + android.os.Build.VERSION.SDK_INT);
        
        // 检查OkHttp版本（如果可能的话）
        try {
            String okHttpVersion = okhttp3.OkHttp.VERSION;
            DDLog.i(TAG, "OkHttp版本: " + okHttpVersion);
        } catch (Exception e) {
            DDLog.w(TAG, "无法获取OkHttp版本: " + e.getMessage());
        }
        
        // 检查可用内存
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        
        DDLog.i(TAG, "最大内存: " + (maxMemory / 1024 / 1024) + " MB");
        DDLog.i(TAG, "总内存: " + (totalMemory / 1024 / 1024) + " MB");
        DDLog.i(TAG, "可用内存: " + (freeMemory / 1024 / 1024) + " MB");
    }
}
