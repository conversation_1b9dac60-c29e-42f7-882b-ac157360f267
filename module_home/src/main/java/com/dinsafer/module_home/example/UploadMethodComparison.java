package com.dinsafer.module_home.example;

import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.module_home.BaseHome;
import com.dinsafer.dssupport.utils.DDLog;

/**
 * 不同上传方式的对比测试
 * 用于找出哪种方式能避免ClassCastException
 * 
 * <AUTHOR> Assistant
 * @since 2025/7/16
 */
public class UploadMethodComparison {
    private static final String TAG = "UploadMethodComparison";
    
    private BaseHome baseHome;
    private String testFilePath;
    private String testUploadUrl;
    private String contentType;
    
    public UploadMethodComparison(BaseHome baseHome) {
        this.baseHome = baseHome;
        // 设置测试参数
        this.testFilePath = "/sdcard/test_image.jpg";  // 请替换为实际文件路径
        this.testUploadUrl = "https://httpbin.org/put"; // 测试URL
        this.contentType = "image/jpeg";
    }
    
    /**
     * 设置测试参数
     */
    public void setTestParameters(String filePath, String uploadUrl, String contentType) {
        this.testFilePath = filePath;
        this.testUploadUrl = uploadUrl;
        this.contentType = contentType;
    }
    
    /**
     * 测试所有上传方式
     */
    public void testAllUploadMethods() {
        DDLog.i(TAG, "=== 开始测试所有上传方式 ===");
        DDLog.i(TAG, "测试文件: " + testFilePath);
        DDLog.i(TAG, "上传URL: " + testUploadUrl);
        DDLog.i(TAG, "Content-Type: " + contentType);
        
        // 方法1: 直接上传（PUT + RequestBody）
        testDirectUpload();
        
        // 等待2秒后测试下一个方法
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 方法2: MultipartBody上传（POST + MultipartBody）
        testMultipartUpload();
    }
    
    /**
     * 测试方法1: 直接上传
     * 使用PUT请求 + RequestBody.create()
     */
    public void testDirectUpload() {
        DDLog.i(TAG, "=== 测试方法1: 直接上传 (PUT + RequestBody) ===");
        
        baseHome.uploadFileDirectly(testUploadUrl, testFilePath, contentType, new IDefaultCallBack2<String>() {
            @Override
            public void onSuccess(String result) {
                DDLog.i(TAG, "✅ 方法1成功: 直接上传");
                DDLog.i(TAG, "响应: " + result);
                
                // 分析响应内容
                analyzeResponse("方法1", result);
            }
            
            @Override
            public void onError(int errorCode, String errorMsg) {
                DDLog.e(TAG, "❌ 方法1失败: 直接上传");
                DDLog.e(TAG, "错误代码: " + errorCode);
                DDLog.e(TAG, "错误信息: " + errorMsg);
                
                // 分析错误类型
                analyzeError("方法1", errorCode, errorMsg);
            }
        });
    }
    
    /**
     * 测试方法2: MultipartBody上传
     * 使用POST请求 + MultipartBody
     */
    public void testMultipartUpload() {
        DDLog.i(TAG, "=== 测试方法2: MultipartBody上传 (POST + MultipartBody) ===");
        
        // 对于MultipartBody，通常使用POST URL
        String postUrl = testUploadUrl.replace("/put", "/post");
        
        baseHome.uploadFileAsMultipart(postUrl, testFilePath, contentType, new IDefaultCallBack2<String>() {
            @Override
            public void onSuccess(String result) {
                DDLog.i(TAG, "✅ 方法2成功: MultipartBody上传");
                DDLog.i(TAG, "响应: " + result);
                
                // 分析响应内容
                analyzeResponse("方法2", result);
            }
            
            @Override
            public void onError(int errorCode, String errorMsg) {
                DDLog.e(TAG, "❌ 方法2失败: MultipartBody上传");
                DDLog.e(TAG, "错误代码: " + errorCode);
                DDLog.e(TAG, "错误信息: " + errorMsg);
                
                // 分析错误类型
                analyzeError("方法2", errorCode, errorMsg);
            }
        });
    }
    
    /**
     * 分析成功响应
     */
    private void analyzeResponse(String method, String response) {
        DDLog.i(TAG, "--- " + method + " 响应分析 ---");
        
        if (response.contains("\"Content-Type\"")) {
            DDLog.i(TAG, "✓ 服务器收到了Content-Type头");
        }
        
        if (response.contains("\"Content-Length\"")) {
            DDLog.i(TAG, "✓ 服务器收到了Content-Length头");
        }
        
        if (response.contains("multipart/form-data")) {
            DDLog.i(TAG, "✓ 使用了multipart/form-data格式");
        }
        
        if (response.contains("\"files\"")) {
            DDLog.i(TAG, "✓ 文件数据被正确解析");
        }
        
        // 检查请求方法
        if (response.contains("\"method\": \"PUT\"")) {
            DDLog.i(TAG, "✓ 使用了PUT方法");
        } else if (response.contains("\"method\": \"POST\"")) {
            DDLog.i(TAG, "✓ 使用了POST方法");
        }
    }
    
    /**
     * 分析错误信息
     */
    private void analyzeError(String method, int errorCode, String errorMsg) {
        DDLog.e(TAG, "--- " + method + " 错误分析 ---");
        
        if (errorMsg.contains("ClassCastException")) {
            DDLog.e(TAG, "🔍 发现ClassCastException");
            if (errorMsg.contains("FormBody")) {
                DDLog.e(TAG, "   → 代码期望FormBody但收到了其他类型");
                DDLog.e(TAG, "   → 建议: 检查拦截器或API接口定义");
            }
            if (errorMsg.contains("RequestBody")) {
                DDLog.e(TAG, "   → RequestBody类型转换问题");
                DDLog.e(TAG, "   → 建议: 尝试MultipartBody方式");
            }
        } else if (errorMsg.contains("FileNotFoundException")) {
            DDLog.e(TAG, "🔍 文件不存在");
            DDLog.e(TAG, "   → 检查文件路径: " + testFilePath);
            DDLog.e(TAG, "   → 检查文件权限");
        } else if (errorMsg.contains("ConnectException") || errorMsg.contains("UnknownHostException")) {
            DDLog.e(TAG, "🔍 网络连接问题");
            DDLog.e(TAG, "   → 检查网络连接");
            DDLog.e(TAG, "   → 检查URL: " + testUploadUrl);
        } else if (errorCode >= 400 && errorCode < 500) {
            DDLog.e(TAG, "🔍 客户端错误 (4xx)");
            DDLog.e(TAG, "   → 检查请求格式");
            DDLog.e(TAG, "   → 检查认证信息");
        } else if (errorCode >= 500) {
            DDLog.e(TAG, "🔍 服务器错误 (5xx)");
            DDLog.e(TAG, "   → 服务器内部问题");
        } else {
            DDLog.e(TAG, "🔍 其他错误");
            DDLog.e(TAG, "   → 错误信息: " + errorMsg);
        }
    }
    
    /**
     * 生成测试报告
     */
    public void generateTestReport() {
        DDLog.i(TAG, "=== 测试报告 ===");
        DDLog.i(TAG, "如果方法1成功:");
        DDLog.i(TAG, "  → 您的服务器支持PUT + RequestBody直接上传");
        DDLog.i(TAG, "  → ClassCastException可能来自项目的拦截器配置");
        DDLog.i(TAG, "  → 建议使用独立的OkHttpClient");
        
        DDLog.i(TAG, "如果方法2成功:");
        DDLog.i(TAG, "  → 您的服务器期望multipart/form-data格式");
        DDLog.i(TAG, "  → 应该使用POST + MultipartBody");
        DDLog.i(TAG, "  → 这是标准的文件上传方式");
        
        DDLog.i(TAG, "如果两种方法都失败:");
        DDLog.i(TAG, "  → 检查文件路径和权限");
        DDLog.i(TAG, "  → 检查网络连接");
        DDLog.i(TAG, "  → 检查服务器API文档");
        
        DDLog.i(TAG, "如果仍有ClassCastException:");
        DDLog.i(TAG, "  → 问题可能在项目的网络配置中");
        DDLog.i(TAG, "  → 检查Retrofit拦截器");
        DDLog.i(TAG, "  → 检查OkHttp配置");
    }
    
    /**
     * 快速测试（只测试一种方法）
     */
    public void quickTest() {
        DDLog.i(TAG, "=== 快速测试 ===");
        DDLog.i(TAG, "先测试最简单的直接上传方式...");
        
        testDirectUpload();
    }
}
