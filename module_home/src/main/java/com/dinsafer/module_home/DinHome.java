package com.dinsafer.module_home;

import static com.dinsafer.dincore.common.ErrorCode.DEFAULT;

import android.content.Context;
import android.text.TextUtils;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.common.Cmd;
import com.dinsafer.dincore.common.CommonCmdEvent;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dincore.common.IDeviceListChangeListener;
import com.dinsafer.dincore.http.Api;
import com.dinsafer.dincore.http.NetWorkException;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.user.api.IResultCallback2;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_home.api.IHome;
import com.dinsafer.module_home.api.IHomeCallBack;
import com.dinsafer.module_home.api.IHomeListCallBack;
import com.dinsafer.module_home.bean.AddContactParams;
import com.dinsafer.module_home.bean.BalanceContractRecordsResponse;
import com.dinsafer.module_home.bean.BalanceContractSignTemplateResponse;
import com.dinsafer.module_home.bean.BalanceContractUnsignTemplateResponse;
import com.dinsafer.module_home.bean.BmtBalanceContractParticipationHoursResponse;
import com.dinsafer.module_home.bean.BmtIsTaskTimeInUpdatedRangeResponse;
import com.dinsafer.module_home.bean.CountryBean;
import com.dinsafer.module_home.bean.DailyMemoriesVideoResponse;
import com.dinsafer.module_home.bean.E2EInfo;
import com.dinsafer.module_home.bean.ElectricitySupplierBean;
import com.dinsafer.module_home.bean.EventListByFilterEntry;
import com.dinsafer.module_home.bean.EventListEntry;
import com.dinsafer.module_home.bean.FamilyBalanceContract;
import com.dinsafer.module_home.bean.FamilyBalanceContractInfoResponse;
import com.dinsafer.module_home.bean.GetWidgetCountResponse;
import com.dinsafer.module_home.bean.Home;
import com.dinsafer.module_home.bean.HomeContact;
import com.dinsafer.module_home.bean.HomeInfo;
import com.dinsafer.module_home.bean.HomeInfoResponse;
import com.dinsafer.module_home.bean.HomeLocationResponse;
import com.dinsafer.module_home.bean.HomeMember;
import com.dinsafer.module_home.bean.IPCEventMotionRecordsResponse;
import com.dinsafer.module_home.bean.IPCFirmwareVersionResponse;
import com.dinsafer.module_home.bean.IPCMotionDetectionRecordResponse;
import com.dinsafer.module_home.bean.InitHomeCompatParams;
import com.dinsafer.module_home.bean.KeypadMemberPwdInfoGetResponse;
import com.dinsafer.module_home.bean.KeypadMemberPwdResetResponse;
import com.dinsafer.module_home.bean.KeypadMemberPwdUpdateResponse;
import com.dinsafer.module_home.bean.LastMotionIpcListResponse;
import com.dinsafer.module_home.bean.MemberAvatars;
import com.dinsafer.module_home.bean.MotionEventCoverResponse;
import com.dinsafer.module_home.bean.MotionEventDatesResponse;
import com.dinsafer.module_home.bean.MotionEventResponse;
import com.dinsafer.module_home.bean.MotionEventVideoResponse;
import com.dinsafer.module_home.bean.ParticipationHour;
import com.dinsafer.module_home.bean.RegionCountriesResponse;
import com.dinsafer.module_home.bean.RegionElectricitySupplierResponse;
import com.dinsafer.module_home.bean.SingleBalanceContractRecordResponse;
import com.dinsafer.module_home.http.HomeApi;
import com.qiniu.android.http.ResponseInfo;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.List;
import java.util.Random;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@Keep
public class DinHome implements IHome {
    private static final String TAG = "DinHome";

    protected static volatile byte[] sInstanceLock = new byte[0];
    private static DinHome sInstance;

    private final BaseHome proxy;

    private DinHome() {
        proxy = DinCore.getInstance().isCompatMode()
                ? new CompatHome()
                : new CommonHome();
        EventBus.getDefault().register(this);
    }

    @Keep
    public static DinHome getInstance() {
        if (sInstance == null) {
            synchronized (sInstanceLock) {
                if (sInstance == null) {
                    sInstance = new DinHome();
                }
            }
        }
        return sInstance;
    }

    @Override
    public void loginTuya(String countryCode, String uid, String pwd,
                          boolean isRegister, IDefaultCallBack callBack) {
        proxy.loginTuya(countryCode, uid, pwd, isRegister, callBack);
    }

    @Keep
    @Override
    public List<Device> getDevices() {
        return proxy.getDevices();
    }

    @Keep
    @Override
    public List<Device> fetchDevices() throws Exception {
        return proxy.fetchDevices();
    }

    @Keep
    @Override
    public Device getDevice(String id) {
        return proxy.getDevice(id);
    }

    @Keep
    @Override
    public Device getDevice(String id, String sub) {
        return proxy.getDevice(id, sub);
    }

    @Keep
    @Override
    public List<Device> getDeviceByType(String sub) {
        return proxy.getDeviceByType(sub);
    }

    @Override
    public List<Device> getDeviceByType(String sub, boolean cacheFirst) {
        return proxy.getDeviceByType(sub, cacheFirst);
    }

    @Nullable
    @Override
    public List<Device> getCacheDeviceByType(String sub) {
        return proxy.getCacheDeviceByType(sub);
    }

    @Nullable
    @Override
    public List<Device> getLocalAndNewDeviceByType(String sub) {
        return proxy.getLocalAndNewDeviceByType(sub);
    }

    @Nullable
    @Override
    public List<Device> getAllDeviceByType(String sub) {
        return proxy.getAllDeviceByType(sub);
    }

    @Override
    public void removeDeviceCacheById(String sub) {
        proxy.removeDeviceCacheById(sub);
    }

    @Override
    public void removeDeviceCacheByIdAndSub(String id, String sub) {
        proxy.removeDeviceCacheByIdAndSub(id, sub);
    }

    @Override
    public void removeDeviceCacheByType(String sub) {

    }

    @Override
    public BasePluginBinder createPluginBinder(Context context, String type) {
        return proxy.createPluginBinder(context, type);
    }

    @Keep
    @Override
    public boolean releaseDeviceByType(String sub) {
        return proxy.releaseDeviceByType(sub);
    }

    @Override
    public Device acquireTemporaryDevices(@NonNull String sub, String id, String model) {
        return proxy.acquireTemporaryDevices(sub, id, model);
    }

    @Keep
    @Override
    public void registerDeviceListChangeListener(IDeviceListChangeListener listChangeListener) {
        proxy.registerDeviceListChangeListener(listChangeListener);
    }

    @Keep
    @Override
    public void unRegisterDeviceListChangeListener(IDeviceListChangeListener listChangeListener) {
        proxy.unRegisterDeviceListChangeListener(listChangeListener);
    }

    @Keep
    @Override
    public HomeInfo getCurrentHomeInfo() {
        return proxy.getCurrentHomeInfo();
    }

    @Keep
    @Override
    public Home getCurrentHome() {
        return proxy.getCurrentHome();
    }

    @Override
    public int getHomeStatus() {
        return proxy.getHomeStatus();
    }


    @Override
    public void getFamilyBalanceContractInfo(String homeId, IDefaultCallBack2<FamilyBalanceContractInfoResponse.ResultBean> callBack) {
        if (TextUtils.isEmpty(homeId)) {
            if (callBack != null) {
                callBack.onError(DEFAULT, "getFamilyBalanceContractInfo params null");
            }
            return;
        }

        HomeApi.getInstance().getFamilyBalanceContractInfo(homeId).enqueue(new Callback<FamilyBalanceContractInfoResponse>() {
            @Override
            public void onResponse(Call<FamilyBalanceContractInfoResponse> call, Response<FamilyBalanceContractInfoResponse> response) {
                FamilyBalanceContractInfoResponse infoResponse = response.body();
                if (response.isSuccessful()) {
                    if (null != infoResponse && null != infoResponse.getResult()) {
                        if (callBack != null) {
                            callBack.onSuccess(infoResponse.getResult());
                        }
                    }
                } else {
                    if (callBack != null) {
                        callBack.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<FamilyBalanceContractInfoResponse> call, Throwable t) {
                if (null != callBack) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), t.getMessage());
                        return;
                    }

                    callBack.onError(DEFAULT, "Unknown Error: " + t.getMessage());
                }
            }
        });
    }

    @Override
    public void saveFamilyBalanceContractInfo(String homeId, FamilyBalanceContract data, IDefaultCallBack callBack) {
        if (TextUtils.isEmpty(homeId) || null == data) {
            if (callBack != null) {
                callBack.onError(DEFAULT, "saveFamilyBalanceContractInfo params null");
            }
            return;
        }

        HomeApi.getInstance().saveFamilyBalanceContractInfo(homeId, data).enqueue(new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (response.isSuccessful()) {
                    if (callBack != null) {
                        callBack.onSuccess();
                    }
                } else {
                    if (callBack != null) {
                        callBack.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                if (null != callBack) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), t.getMessage());
                        return;
                    }

                    callBack.onError(DEFAULT, "Unknown Error: " + t.getMessage());
                }
            }
        });
    }

    public static String QIUNIU_PATH_KEY_FEEDBACK = "feedback/";
    public static String QIUNIU_PATH_KEY_CONTRACT = "balance/contracts/";
    @Override
    public void getUploadImageKey(String imagePath, String pathKey, IDefaultCallBack2<String> callBack) {
        proxy.getUploadImageKey(imagePath, pathKey, callBack);
    }

    @Override
    public void getBmtElectricitySupplier(String countryCode, IDefaultCallBack2<List<ElectricitySupplierBean>> callback) {
        if (TextUtils.isEmpty(countryCode)) {
            if (callback != null) {
                callback.onError(DEFAULT, "countryCode params error");
            }
            return;
        }
        HomeApi.getInstance().getBmtRegionElectricitySupplier(countryCode).enqueue(new Callback<RegionElectricitySupplierResponse>() {
            @Override
            public void onResponse(Call<RegionElectricitySupplierResponse> call, Response<RegionElectricitySupplierResponse> response) {
                RegionElectricitySupplierResponse body = response.body();
                if (response.isSuccessful() && null != body && body.getResult() != null) {
                    callback.onSuccess(body.getResult().getElectricity_supplier());
                } else {
                    callback.onError(-1, "fail or body is null");
                }
            }

            @Override
            public void onFailure(Call<RegionElectricitySupplierResponse> call, Throwable t) {
                callback.onError(-1, "fail");
            }
        });
    }

    @Override
    public void getBmtRegionCountries(IDefaultCallBack2<List<CountryBean>> callback) {
        HomeApi.getInstance().getCountryList().enqueue(new Callback<RegionCountriesResponse>() {
            @Override
            public void onResponse(Call<RegionCountriesResponse> call, Response<RegionCountriesResponse> response) {
                RegionCountriesResponse body = response.body();
                if (response.isSuccessful() && body.getResult() != null) {
                    callback.onSuccess(body.getResult());
                } else {
                    callback.onError(-1, "fail or body is null");
                }
            }

            @Override
            public void onFailure(Call<RegionCountriesResponse> call, Throwable t) {
                t.printStackTrace();
                if (callback != null) {
                    callback.onError(-1, "unknown error");
                }
            }
        });
    }

    @Override
    public void updateBalanceContractBank(String homeId, String cardholder, String iban, String pwd, IDefaultCallBack callback) {
        proxy.updateBalanceContractBank(homeId, cardholder, iban, pwd, callback);
    }

    @Override
    public void terminateBalanceContract(String homeId, String sign, IDefaultCallBack callback) {
        proxy.terminateBalanceContract(homeId, sign, callback);
    }

    @Override
    public void getBalanceContractRecordsById(String homeId, String recordId, IDefaultCallBack2<SingleBalanceContractRecordResponse.ResultBean> callback) {
        proxy.getBalanceContractRecordsById(homeId, recordId, callback);
    }
    @Override
    public void getBalanceContractRecords(long createTime, String homeId, int pageSize, IDefaultCallBack2<BalanceContractRecordsResponse.ResultBean> callback) {
        proxy.getBalanceContractRecords(createTime, homeId, pageSize, callback);
    }

    @Override
    public void getBalanceContractUnsignTemplate(String homeId, IDefaultCallBack2<BalanceContractUnsignTemplateResponse.ResultBean> callback) {
        proxy.getBalanceContractUnsignTemplate(homeId, callback);
    }

    @Override
    public void getBalanceContractSignTemplate(String countryCode, IDefaultCallBack2<BalanceContractSignTemplateResponse.ResultBean> callback) {
        proxy.getBalanceContractSignTemplate(countryCode, callback);
    }

    @Override
    public void checkBmtDeviceBalanceContractStatus(String homeId, String deviceId, IDefaultCallBack2<Integer> callback) {
        proxy.checkBmtDeviceBalanceContractStatus(homeId, deviceId, callback);
    }

    @Override
    public void updateBalanceContractData(String homeId, IDefaultCallBack callBack) {
        proxy.updateBalanceContractData(homeId, callBack);
    }

    @Override
    public void bmtBalanceContractParticipationHours(long createTime, String homeId, int pageSize, IDefaultCallBack2<BmtBalanceContractParticipationHoursResponse.ResultBean> callback) {
        proxy.bmtBalanceContractParticipationHours(createTime, homeId, pageSize, callback);
    }

    @Override
    public void bmtAddBalanceContractParticipationHours(String homeId, ParticipationHour participationHour, IDefaultCallBack callback) {
        proxy.bmtAddBalanceContractParticipationHours(homeId, participationHour, callback);
    }

    @Override
    public void bmtUpdateBalanceContractParticipationHours(String homeId, ParticipationHour participationHour, IDefaultCallBack callback) {
        proxy.bmtUpdateBalanceContractParticipationHours(homeId, participationHour, callback);
    }

    @Override
    public void bmtDeleteBalanceContractParticipationHours(String homeId, String id, IDefaultCallBack callback) {
        proxy.bmtDeleteBalanceContractParticipationHours(homeId, id, callback);
    }

    @Override
    public void bmtIsTaskTimeInUpdatedRange(String homeId, ParticipationHour participationHour, IDefaultCallBack2<BmtIsTaskTimeInUpdatedRangeResponse.ResultBean> callback) {
        proxy.bmtIsTaskTimeInUpdatedRange(homeId, participationHour, callback);
    }

    @Override
    public void getHomeLocation(String homeId, IDefaultCallBack2<HomeLocationResponse.ResultBean> callback) {
        proxy.getHomeLocation(homeId, callback);
    }

    @Override
    public void bmtSaveLocation(String homeId, double latitude, double longitude, IDefaultCallBack callback) {
        proxy.bmtSaveLocation(homeId, latitude, longitude, callback);
    }

    @Keep
    @Override
    public void createHome(String homeName, String language, IDefaultCallBack2<Home> callback) {
        proxy.createHome(homeName, language, callback);
    }

    @Keep
    @Override
    public void reNameHome(String homeID, String homeName, IDefaultCallBack callBack) {
        proxy.reNameHome(homeID, homeName, callBack);
    }

    @Keep
    @Override
    public void queryHomeList(IHomeListCallBack callback) {
        proxy.queryHomeList(callback);
    }

    @Keep
    @Override
    public void removeHome(String homeID, IDefaultCallBack callBack) {
        proxy.removeHome(homeID, callBack);
    }

    @Keep
    @Override
    public void queryHomeMemberList(String homeID, IDefaultCallBack2<List<HomeMember>> callBack) {
        proxy.queryHomeMemberList(homeID, callBack);
    }

    @Keep
    @Override
    public void updateHomeMember(String homeId, String userId, int level,
                                 HomeMember homeMember, IDefaultCallBack callBack) {
        proxy.updateHomeMember(homeId, userId, level, homeMember, callBack);
    }

    @Keep
    @Override
    public void newHomeContact(String homeId, @NotNull AddContactParams params, IDefaultCallBack callBack) {
        proxy.newHomeContact(homeId, params, callBack);
    }

    @Keep
    @Override
    public void listHomeContact(String homeId, IDefaultCallBack2<List<HomeContact>> callBack) {
        proxy.listHomeContact(homeId, callBack);
    }

    @Keep
    @Override
    public void removeHomeContact(String homeId, String contactId, IDefaultCallBack callBack) {
        proxy.removeHomeContact(homeId, contactId, callBack);
    }

    @Keep
    @Override
    public void updateHomeContact(String homeId, @NotNull HomeContact param, IDefaultCallBack callBack) {
        proxy.updateHomeContact(homeId, param, callBack);
    }

    @Keep
    @Override
    public void getInvitationFamilyMemberCode(String homeID, int level, IDefaultCallBack2<String> callBack) {
        proxy.getInvitationFamilyMemberCode(homeID, level, callBack);
    }

    @Keep
    @Override
    public void verifyInvitationFamilyMemberCode(String code, IDefaultCallBack2<Home> callBack) {
        proxy.verifyInvitationFamilyMemberCode(code, callBack);
    }

    @Keep
    @Override
    public void changeFamilyMemberPermission(String homeID, String user_id,
                                             int level, IDefaultCallBack callBack) {
        proxy.changeFamilyMemberPermission(homeID, user_id, level, callBack);
    }

    @Keep
    @Override
    public void removeFamilyMember(String homeID, String user_id, IDefaultCallBack callBack) {
        proxy.removeFamilyMember(homeID, user_id, callBack);
    }

    @Override
    public void switchHomeNotGetInfo(String homeID, IDefaultCallBack callBack) {
        proxy.switchHomeNotGetInfo(homeID, callBack);
    }

    @Keep
    @Override
    public void switchHome(String homeID, IDefaultCallBack2<HomeInfoResponse> callBack) {
        proxy.switchHome(homeID, callBack);
    }

    @Override
    public void refreshCurrentHomeInfo(IDefaultCallBack2<HomeInfoResponse> callBack) {
        proxy.refreshCurrentHomeInfo(callBack);
    }

    @Keep
    @Override
    public void getHomeNotificationLanguage(String homeID, IDefaultCallBack2<String> callBack) {
        proxy.getHomeNotificationLanguage(homeID, callBack);
    }

    @Keep
    @Override
    public void setHomeNotificationLanguage(String homeID, String language, IDefaultCallBack callBack) {
        proxy.setHomeNotificationLanguage(homeID, language, callBack);
    }

    @Override
    public void loginHomeE2E(String homeID, IDefaultCallBack2<E2EInfo> callBack) {
        proxy.loginHomeE2E(homeID, callBack);
    }

    @Override
    public void logoutHomeE2E(String homeID, IDefaultCallBack callBack) {
        proxy.logoutHomeE2E(homeID, callBack);
    }

    @Keep
    @Override
    public void getHomeMemberAvatars(String homeID, IDefaultCallBack2<MemberAvatars> callBack) {
        proxy.getHomeMemberAvatars(homeID, callBack);
    }

    @Keep
    @Override
    public void bindPanel(String homeId, String panelId, IDefaultCallBack callBack) {
        proxy.bindPanel(homeId, panelId, callBack);
    }

    @Keep
    @Override
    public void addHomeStatusCallback(IHomeCallBack callBack) {
        proxy.addHomeStatusCallback(callBack);
    }

    @Keep
    @Override
    public void removeHomeStatueCallback(IHomeCallBack callBack) {
        proxy.removeHomeStatueCallback(callBack);
    }

    @Keep
    @Override
    public void stopE2EConnection(boolean needLogout) {
        proxy.stopE2EConnection(needLogout);
    }

    @Override
    public void getMotionDetectionRecordList(String homeId, List<String> provider, long addtime, int pagesize,
                                             IDefaultCallBack2<IPCMotionDetectionRecordResponse> callBack) {
        proxy.getMotionDetectionRecordList(homeId, provider, addtime, pagesize, callBack);
    }

    @Override
    public void deleteMotionDetectionRecord(String homeId, List<String> event_ids, IDefaultCallBack callBack) {
        proxy.deleteMotionDetectionRecord(homeId, event_ids, callBack);
    }

    @Override
    public void getMotionDetectionRecordVideoUrl(String homeId, String record_id, IDefaultCallBack2<String> callBack) {
        proxy.getMotionDetectionRecordVideoUrl(homeId, record_id, callBack);
    }

    @Override
    public void isOnlyAdmin(String homeId, IDefaultCallBack2<Boolean> callBack) {
        proxy.isOnlyAdmin(homeId, callBack);
    }

    @Override
    public void forceDeleteHome(String homeId, IDefaultCallBack callBack) {
        proxy.forceDeleteHome(homeId, callBack);
    }

    @Override
    public void getTotalMotionRecordCount(String homeId, String event_id, IDefaultCallBack2<Integer> callBack) {
        proxy.getTotalMotionRecordCount(homeId, event_id, callBack);
    }

    @Override
    public void listEventMotionRecords(String home_id, String event_id, int start_index, int page_size,
                                       IDefaultCallBack2<IPCEventMotionRecordsResponse> callBack) {
        proxy.listEventMotionRecords(home_id, event_id, start_index, page_size, callBack);
    }

    @Override
    public void getEventListData(String panelId, int limit, long timestamp, String filters,
                                 IDefaultCallBack2<EventListEntry> callBack) {
        proxy.getEventListData(panelId, limit, timestamp, filters, callBack);
    }

    @Override
    public void getEventListDataByFilter(int limit, long time, String type_ids, IDefaultCallBack2<EventListByFilterEntry> callback) {
        proxy.getEventListDataByFilter(limit, time, type_ids, callback);
    }


    @Override
    public void getIPCFirmwareVersion(IDefaultCallBack2<IPCFirmwareVersionResponse> callBack) {
        proxy.getIPCFirmwareVersion(callBack);
    }

    @Override
    public void getMotionEventDates(String homeId, String timezone, IDefaultCallBack2<MotionEventDatesResponse> callback) {
        proxy.getMotionEventDates(homeId, timezone, callback);
    }

    @Override
    public void getMotionEventVideos(String homeId, String ipcId, String eventId, long eventStartTime, IDefaultCallBack2<MotionEventVideoResponse> callback) {
        proxy.getMotionEventVideos(homeId, ipcId, eventId, eventStartTime, callback);
    }

    @Override
    public Call<?> getMotionEvents(String homeId, List<String> ipcIds, long startTime, long endTime, IDefaultCallBack2<MotionEventResponse> callback) {
        return proxy.getMotionEvents(homeId, ipcIds, startTime, endTime, callback);
    }

    @Override
    public void getLastMotionIpcList(String homeId, long startTime, long endTime, IDefaultCallBack2<LastMotionIpcListResponse> callback) {
        proxy.getLastMotionIpcList(homeId, startTime, endTime, callback);
    }

    @Override
    public void getMotionEventCover(String homeId, List<String> eventIds, IDefaultCallBack2<MotionEventCoverResponse> callback) {
        proxy.getMotionEventCover(homeId, eventIds, callback);
    }

    @Override
    public void getWidgetCount(String homeID, String panelId, List<String> ipcProviders, List<String> sTypeList, IDefaultCallBack2<GetWidgetCountResponse> callback) {
        proxy.getWidgetCount(homeID, panelId, ipcProviders, sTypeList, callback);
    }

    @Override
    public void getKeypadMemberPwdInfo(String homeId, String panelId, String userId, IDefaultCallBack2<KeypadMemberPwdInfoGetResponse> callback) {
        proxy.getKeypadMemberPwdInfo(homeId, panelId, userId, callback);
    }

    @Override
    public void updateKeypadMemberPwdInfo(String homeId, String panelId, String userId, boolean enabled, IDefaultCallBack2<KeypadMemberPwdUpdateResponse> callback) {
        proxy.updateKeypadMemberPwdInfo(homeId, panelId, userId, enabled, callback);
    }

    @Override
    public void resetKeypadMemberPwdInfo(String homeId, String panelId, String userId, IDefaultCallBack2<KeypadMemberPwdResetResponse> callback) {
        proxy.resetKeypadMemberPwdInfo(homeId, panelId, userId, callback);
    }

    @Override
    public void getDailyMemoriesVideoUrl(String record_id, IDefaultCallBack2<DailyMemoriesVideoResponse> callback) {
        proxy.getDailyMemoriesVideoUrl(record_id, callback);
    }

    @Override
    public void initHomeWithPanel(@NotNull InitHomeCompatParams params, IDefaultCallBack2<HomeInfo> callback) {
        proxy.initHomeWithPanel(params, callback);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(CommonCmdEvent event) {
        if (Cmd.COMMON_CMD_DISCONNECT_HOME.equals(event.getCmd())) {
            proxy.onCmdDisconnectHome();
        } else if (CommonCmdEvent.CMD.LOGOUT_SUCCESS.equals(event.getCmd())) {
            proxy.onCmdLogoutSuccess();
        }
    }
}
