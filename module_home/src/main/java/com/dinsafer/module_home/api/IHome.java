package com.dinsafer.module_home.api;

import android.content.Context;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dincore.common.IDeviceListChangeListener;
import com.dinsafer.module_home.bean.AddContactParams;
import com.dinsafer.module_home.bean.BalanceContractRecordsResponse;
import com.dinsafer.module_home.bean.BalanceContractSignTemplateResponse;
import com.dinsafer.module_home.bean.BalanceContractUnsignTemplateResponse;
import com.dinsafer.module_home.bean.BmtBalanceContractParticipationHoursResponse;
import com.dinsafer.module_home.bean.BmtIsTaskTimeInUpdatedRangeResponse;
import com.dinsafer.module_home.bean.CountryBean;
import com.dinsafer.module_home.bean.DailyMemoriesVideoResponse;
import com.dinsafer.module_home.bean.E2EInfo;
import com.dinsafer.module_home.bean.ElectricitySupplierBean;
import com.dinsafer.module_home.bean.EventListByFilterEntry;
import com.dinsafer.module_home.bean.EventListEntry;
import com.dinsafer.module_home.bean.FamilyBalanceContract;
import com.dinsafer.module_home.bean.FamilyBalanceContractInfoResponse;
import com.dinsafer.module_home.bean.GetWidgetCountResponse;
import com.dinsafer.module_home.bean.Home;
import com.dinsafer.module_home.bean.HomeContact;
import com.dinsafer.module_home.bean.HomeInfo;
import com.dinsafer.module_home.bean.HomeInfoResponse;
import com.dinsafer.module_home.bean.HomeLocationResponse;
import com.dinsafer.module_home.bean.HomeMember;
import com.dinsafer.module_home.bean.IPCEventMotionRecordsResponse;
import com.dinsafer.module_home.bean.IPCFirmwareVersionResponse;
import com.dinsafer.module_home.bean.IPCMotionDetectionRecordResponse;
import com.dinsafer.module_home.bean.InitHomeCompatParams;
import com.dinsafer.module_home.bean.KeypadMemberPwdInfoGetResponse;
import com.dinsafer.module_home.bean.KeypadMemberPwdResetResponse;
import com.dinsafer.module_home.bean.KeypadMemberPwdUpdateResponse;
import com.dinsafer.module_home.bean.LastMotionIpcListResponse;
import com.dinsafer.module_home.bean.MemberAvatars;
import com.dinsafer.module_home.bean.MotionEventCoverResponse;
import com.dinsafer.module_home.bean.MotionEventDatesResponse;
import com.dinsafer.module_home.bean.MotionEventResponse;
import com.dinsafer.module_home.bean.MotionEventVideoResponse;
import com.dinsafer.module_home.bean.ParticipationHour;
import com.dinsafer.module_home.bean.SingleBalanceContractRecordResponse;

import org.jetbrains.annotations.NotNull;

import java.util.List;

import retrofit2.Call;

@Keep
public interface IHome {

    void loginTuya(String countryCode, String uid, String pwd, boolean isRegister, IDefaultCallBack callBack);

    List<Device> getDevices();

    List<Device> fetchDevices() throws Exception;

    Device getDevice(String id);

    Device getDevice(String id, String sub);

    //    此方法为同步方法，需要自行加锁处理
    List<Device> getDeviceByType(String sub);

    /**
     * 不能在主线程调用，必须是同步方法
     *
     * @param cacheFirst 是否先读取缓存-true先读取缓存；false等同于{{@link #getDeviceByType(String)}}
     */
    List<Device> getDeviceByType(String sub, boolean cacheFirst);


    /**
     * 获取缓存的Device
     *
     * @param sub
     * @return 仅返回缓存Device
     */
    @Nullable
    List<Device> getCacheDeviceByType(String sub);

    /**
     * 获取全部device
     *
     * @return 返回全部Device，但请求网络数据的时候是请求缓存时间戳后的device，
     * 如果没有缓存，相当于{{@link #getAllDeviceByType(String)}}
     */
    @Nullable
    List<Device> getLocalAndNewDeviceByType(String sub);

    /**
     * 获取全部Device
     *
     * @return 返回全部Device，但请求网络数据的时候是请求全部Device
     */
    @Nullable
    List<Device> getAllDeviceByType(String sub);

    void removeDeviceCacheById(String id);

    void removeDeviceCacheByIdAndSub(String id, String sub);

    void removeDeviceCacheByType(String sub);

    BasePluginBinder createPluginBinder(Context context, String type);

    // 清楚device列表缓存
    boolean releaseDeviceByType(String sub);

    /**
     * 获取单个临时设备
     *
     * @param sub   模块名
     * @param id
     * @param model
     * @return
     */
    Device acquireTemporaryDevices(@NonNull String sub, String id, String model);

    void registerDeviceListChangeListener(IDeviceListChangeListener listChangeListener);

    void unRegisterDeviceListChangeListener(IDeviceListChangeListener listChangeListener);

    void createHome(String homeName, String language, IDefaultCallBack2<Home> callBack);

    void reNameHome(String homeID, String homeName, IDefaultCallBack callBack);

    void queryHomeList(IHomeListCallBack callback);

    void removeHome(String homeID, IDefaultCallBack callBack);

    void queryHomeMemberList(String homeID, IDefaultCallBack2<List<HomeMember>> callBack);

    void updateHomeMember(String homeId, String userId, int level, HomeMember homeMember, IDefaultCallBack callBack);

    void newHomeContact(String homeId, @NotNull AddContactParams params, IDefaultCallBack callBack);

    void listHomeContact(String homeId, IDefaultCallBack2<List<HomeContact>> callBack);

    void removeHomeContact(String homeId, String contactId, IDefaultCallBack callBack);

    void updateHomeContact(String homeId, @NotNull HomeContact param, IDefaultCallBack callBack);

    void getInvitationFamilyMemberCode(String homeID, int level, IDefaultCallBack2<String> callBack);

    void verifyInvitationFamilyMemberCode(String code, IDefaultCallBack2<Home> callBack);

    void changeFamilyMemberPermission(String homeID, String user_id, int level, IDefaultCallBack callBack);

    void removeFamilyMember(String homeID, String user_id, IDefaultCallBack callBack);

    void switchHomeNotGetInfo(String homeID, IDefaultCallBack callBack);

    void switchHome(String homeID, IDefaultCallBack2<HomeInfoResponse> callBack);

    void refreshCurrentHomeInfo(IDefaultCallBack2<HomeInfoResponse> callBack);

    void getHomeNotificationLanguage(String homeID, IDefaultCallBack2<String> callBack);

    void setHomeNotificationLanguage(String homeID, String language, IDefaultCallBack callBack);

    void loginHomeE2E(String homeID, IDefaultCallBack2<E2EInfo> callBack);

    void logoutHomeE2E(String homeID, IDefaultCallBack callBack);

    void getHomeMemberAvatars(String homeID, IDefaultCallBack2<MemberAvatars> callBack);

    void bindPanel(String homeId, String panelId, IDefaultCallBack callBack);

    HomeInfo getCurrentHomeInfo();

    Home getCurrentHome();

    void addHomeStatusCallback(IHomeCallBack callBack);

    void removeHomeStatueCallback(IHomeCallBack callBack);

    void stopE2EConnection(boolean needLogout);

    void getMotionDetectionRecordList(String homeId, List<String> provider, long addtime, int pagesize, IDefaultCallBack2<IPCMotionDetectionRecordResponse> callBack2);

    void deleteMotionDetectionRecord(String homeId, List<String> event_ids, IDefaultCallBack callBack);

    void getMotionDetectionRecordVideoUrl(String homeId, String record_id, IDefaultCallBack2<String> callBack2);

    void isOnlyAdmin(String homeId, IDefaultCallBack2<Boolean> callBack2);

    void forceDeleteHome(String homeId, IDefaultCallBack callBack2);

    void getTotalMotionRecordCount(String homeId, String event_id, IDefaultCallBack2<Integer> callBack2);

    void listEventMotionRecords(String home_id, String event_id, int start_index, int page_size, IDefaultCallBack2<IPCEventMotionRecordsResponse> callBack2);

    void getEventListData(String panelId, int limit, long timestamp, String filters, IDefaultCallBack2<EventListEntry> callback);

    void getEventListDataByFilter(int limit, long time, String type_ids, IDefaultCallBack2<EventListByFilterEntry> callback);

    void getIPCFirmwareVersion(IDefaultCallBack2<IPCFirmwareVersionResponse> callback);

    void getMotionEventDates(String homeId, String timezone, IDefaultCallBack2<MotionEventDatesResponse> callback);

    void getMotionEventVideos(String homeId, String ipcId, String eventId, long eventStartTime, IDefaultCallBack2<MotionEventVideoResponse> callback);

    Call<?> getMotionEvents(String homeId, List<String> ipcIds, long startTime, long endTime, IDefaultCallBack2<MotionEventResponse> callback);

    void getLastMotionIpcList(String homeId, long startTime, long endTime, IDefaultCallBack2<LastMotionIpcListResponse> callback);

    void getMotionEventCover(String homeId, List<String> eventIds, IDefaultCallBack2<MotionEventCoverResponse> callback);

    void getWidgetCount(String homeID, String panelId, List<String> ipcProviders, List<String> sTypeList, IDefaultCallBack2<GetWidgetCountResponse> callback);

    void getKeypadMemberPwdInfo(final String homeId, final String panelId, final String userId, IDefaultCallBack2<KeypadMemberPwdInfoGetResponse> callback);

    void updateKeypadMemberPwdInfo(final String homeId, final String panelId, final String userId, final boolean enabled, IDefaultCallBack2<KeypadMemberPwdUpdateResponse> callback);

    void resetKeypadMemberPwdInfo(final String homeId, final String panelId, final String userId, IDefaultCallBack2<KeypadMemberPwdResetResponse> callback);

    void getDailyMemoriesVideoUrl(String record_id, IDefaultCallBack2<DailyMemoriesVideoResponse> callback);


    // ********************* 兼容模式（cawa）

    void initHomeWithPanel(@NotNull final InitHomeCompatParams params, IDefaultCallBack2<HomeInfo> callback);

    int getHomeStatus();


    void getFamilyBalanceContractInfo(String homeId, IDefaultCallBack2<FamilyBalanceContractInfoResponse.ResultBean> callBack);

    void saveFamilyBalanceContractInfo(String homeId, FamilyBalanceContract data, IDefaultCallBack callBack);

    void getUploadImageKey(String imagePath, String pathKey, IDefaultCallBack2<String> callBack);

    void getBmtElectricitySupplier(String countryCode, IDefaultCallBack2<List<ElectricitySupplierBean>> callback);

    void getBmtRegionCountries(IDefaultCallBack2<List<CountryBean>> callback);

    void updateBalanceContractBank(final String homeId, final String cardholder, final String iban, final String pwd, IDefaultCallBack callback);

    void terminateBalanceContract(final String homeId, final String sign, IDefaultCallBack callback);
    void getBalanceContractRecordsById(String homeId, String recordId, IDefaultCallBack2<SingleBalanceContractRecordResponse.ResultBean> callback);
    void getBalanceContractRecords(long createTime, String homeId, int pageSize, IDefaultCallBack2<BalanceContractRecordsResponse.ResultBean> callback);
    void getBalanceContractUnsignTemplate(String homeId, IDefaultCallBack2<BalanceContractUnsignTemplateResponse.ResultBean> callback);
    void getBalanceContractSignTemplate(String countryCode, IDefaultCallBack2<BalanceContractSignTemplateResponse.ResultBean> callback);
    void checkBmtDeviceBalanceContractStatus(String homeId, String deviceId, IDefaultCallBack2<Integer> callback);
    void updateBalanceContractData(String homeId, IDefaultCallBack callBack);

    void bmtBalanceContractParticipationHours(long createTime, String homeId, int pageSize, IDefaultCallBack2<BmtBalanceContractParticipationHoursResponse.ResultBean> callback);
    void bmtAddBalanceContractParticipationHours(String homeId, ParticipationHour participationHour, IDefaultCallBack callback);
    void bmtUpdateBalanceContractParticipationHours(String homeId, ParticipationHour participationHour, IDefaultCallBack callback);
    void bmtDeleteBalanceContractParticipationHours(String homeId, String id, IDefaultCallBack callback);
    void bmtIsTaskTimeInUpdatedRange(String homeId, ParticipationHour participationHour, IDefaultCallBack2<BmtIsTaskTimeInUpdatedRangeResponse.ResultBean> callback);

    void getHomeLocation(String homeId, IDefaultCallBack2<HomeLocationResponse.ResultBean> callback);
    void bmtSaveLocation(String homeId, double latitude, double longitude, IDefaultCallBack callback);
}
