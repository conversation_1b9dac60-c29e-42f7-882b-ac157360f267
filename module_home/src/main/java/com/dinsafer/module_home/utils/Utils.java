package com.dinsafer.module_home.utils;

import android.text.TextUtils;

import java.util.Arrays;

public class Utils {

    public static int byteLen(int[] src) {
        if (src == null) {
            return 0;
        }

        return src.length;

    }

    public static int[] SubByte(int[] data, int start, int end) {
        if (data == null || start < 0 || end > data.length || end < start) {
            return data;
        }

        int[] result = new int[end - start];
        for (int i = 0; i < (end - start); i++) {
            result[i] = data[i + start];
        }
        return result;
    }

    public static int[] stringTounSignByte(String src) {
        if (TextUtils.isEmpty(src)) {
            return new int[0];
        }
        byte[] array = src.getBytes();
        int[] unsigned_array = new int[array.length];
        for (int i = 0; i < array.length; i++) {
            unsigned_array[i] = array[i] >= 0 ? array[i] : array[i] + 256;
        }
        return unsigned_array;
    }

    public static int[] objectToSignByte(Object src) {
        if (src instanceof String) {
            return stringTounSignByte((String) src);
        }

        if (src instanceof Integer) {
            return intArrayToByteArray((int) src);
        }
        return new int[]{};
    }


    public static String unSignByteToString(int src[]) {

        byte[] signed_array = new byte[src.length];
        for (int i = 0; i < src.length; i++) {
            signed_array[i] = (byte) src[i];
        }
        if (src.length <= 0) {
            return "";
        }
        String s = new String(signed_array);
        return s;
    }

    public static int byteArrayToLeInt(byte[] encodedValue) {
        int value = (encodedValue[3] << (Byte.SIZE * 3));
        value |= (encodedValue[2] & 0xFF) << (Byte.SIZE * 2);
        value |= (encodedValue[1] & 0xFF) << (Byte.SIZE * 1);
        value |= (encodedValue[0] & 0xFF);
        return value;
    }

    //    byte to big endian 只有两位
    public static int byteArrayToUnInt(byte[] encodedValue) {
        final int length = encodedValue.length;
        byte[] res = new byte[length];
        for (int i = 0; i < length; i++) {
            res[length - i - 1] = encodedValue[i];
        }
        int value = (res[1] & 0xFF) << (Byte.SIZE * 1);
        value |= (res[0] & 0xFF);
        return value;
    }

    public static byte[] unSignByteToByte(int src[]) {

        byte[] signed_array = new byte[src.length];
        for (int i = 0; i < src.length; i++) {
            signed_array[i] = (byte) src[i];
        }
        return signed_array;
    }

    public static final byte[] intToByteArray(int value) {
        return new byte[]{
                (byte) (value >>> 24),
                (byte) (value >>> 16),
                (byte) (value >>> 8),
                (byte) value};
    }

    public static byte intTobyte(int a) {
        return (byte) (a & 0xFF);
    }

//    public static int byte2Int(byte b) {
//        return (int) b;
//    }

    public static int byte2Int(byte b) {
        return (b & 0xff);
    }

    public static byte[] intArrayToByteArray(int[] ints) {
        byte[] bytes = new byte[ints.length];

        for (int i = 0; i < ints.length; i++) {
            bytes[i] = intTobyte(ints[i]);
        }
        return bytes;
    }

    public static int[] byteArrayToIntArray(byte[] bytes) {
        int[] ints = new int[bytes.length];

        for (int i = 0; i < bytes.length; i++) {
            ints[i] = byte2Int(bytes[i]);
        }
        return ints;
    }

    public static <T> T[] concatAll(T[] first, T[]... rest) {
        int totalLength = first.length;
        for (T[] array : rest) {
            totalLength += array.length;
        }
        T[] result = Arrays.copyOf(first, totalLength);
        int offset = first.length;
        for (T[] array : rest) {
            System.arraycopy(array, 0, result, offset, array.length);
            offset += array.length;
        }
        return result;
    }

    public static int[] concatAll(int[] first, int[]... rest) {
        int totalLength = first.length;
        for (int[] array : rest) {
            totalLength += array.length;
        }
        int[] result = Arrays.copyOf(first, totalLength);
        int offset = first.length;
        for (int[] array : rest) {
            System.arraycopy(array, 0, result, offset, array.length);
            offset += array.length;
        }
        return result;
    }

    public static int[] intArrayToByteArray(int object) {
        byte[] temp = intToByteArray(object);
        return byteArrayToIntArray(temp);
    }
}
