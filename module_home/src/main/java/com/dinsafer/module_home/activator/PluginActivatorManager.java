package com.dinsafer.module_home.activator;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.dinsafer.dincore.activtor.api.PanelPluginScanner;
import com.dinsafer.dincore.activtor.api.base.IPluginScanner;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginActivator;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.activtor.bean.Plugin;
import com.dinsafer.dincore.common.DeviceHelper;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.db.DBKey;
import com.dinsafer.dssupport.msctlib.db.KV;
import com.dinsafer.dssupport.plugin.PluginConstants;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_home.DinHome;

import java.util.HashMap;
import java.util.Map;

/**
 * 配件管理器
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/8 2:14 PM
 */
@Keep
public class PluginActivatorManager extends BasePluginActivator {
    private static final String BINDER_KEY_DSCAM = "dscam_binder";
    private static final String BINDER_KEY_DSCAM_NETWORK_MANAGER = "dscam_network_binder";
    private static final String BINDER_KEY_DSCAM_V006 = "dscam_v006_binder";
    private static final String BINDER_KEY_DSCAM_V006_NETWORK_MANAGER = "dscam_v006_network_binder";
    private static final String BINDER_KEY_DSCAM_V015 = "dscam_v015_binder";
    private static final String BINDER_KEY_DSCAM_V015_NETWORK_MANAGER = "dscam_v015_network_binder";
    private static final String BINDER_KEY_DSDOORBELL = "dsdoorbell_binder";
    private static final String BINDER_KEY_DSDOORBELL_NETWORK_MANAGER = "dsdoorbell_network_binder";
    private static final String BINDER_KEY_HEARTLAI = "heartlai_binder";
    private static final String BINDER_KEY_TUYA = "tuya_binder";
    private static final String BINDER_KEY_PANEL = "panel_binder";
    private static final String BINDER_KEY_DIN_PLUGIN = "din_plugin_binder";
    private static final String BINDER_KEY_TRIGGER_PLUGIN = "trigger_device_binder";
    private static final String BINDER_KEY_BMT_HP5000 = "bmt_hp5000_binder";
    private static final String BINDER_KEY_BMT_HP5000_NETWORK_MANAGER = "bmt_hp5000_network_binder";
    private static final String BINDER_KEY_BMT_HP5001 = "bmt_hp5001_binder";
    private static final String BINDER_KEY_BMT_HP5001_NETWORK_MANAGER = "bmt_hp5001_network_binder";
    private static final String BINDER_KEY_BMT_POWER_CORE_20 = "bmt_pc1bak1hs10_binder";
    private static final String BINDER_KEY_BMT_POWER_CORE_20_NETWORK_MANAGER = "bmt_pc1bak15hs10_network_binder";
    private static final String BINDER_KEY_BMT_POWER_STORE = "bmt_ps1bak10hs10_binder";
    private static final String BINDER_KEY_BMT_POWER_STORE_NETWORK_MANAGER = "bmt_ps1bak10hs10_network_binder";
    private static final String BINDER_KEY_BMT_POWER_PULSE = "bmt_vb1bak5hs10_binder";
    private static final String BINDER_KEY_BMT_POWER_PULSE_NETWORK_MANAGER = "bmt_vb1bak5hs10_network_binder";
    private static final String BINDER_KEY_BMT_POWER_CORE_30 = "bmt_pc3_binder";
    private static final String BINDER_KEY_BMT_POWER_CORE_30_NETWORK_MANAGER = "bmt_pc3_network_binder";
    private static class Holder {
        private static final PluginActivatorManager instance = new PluginActivatorManager();
    }

    public static PluginActivatorManager getInstance() {
        return Holder.instance;
    }

    private PluginActivatorManager() {
    }

    public void setup(Context context) {
        final String panelId = KV.getString(DBKey.CURRENT_DEVICE_ID, null);
        final String devToken = DeviceHelper.getString(DinHome.getInstance().getDevice(panelId), "deviceToken", null);
        this.setup(context, panelId, devToken);
    }


    /**
     * 扫描结果处理
     *
     * @param pluginID 二维码内容
     */
    @Keep
    @Override
    public void scan(@NonNull String pluginID) {
        checkSetup();
        if (mPluginScanner == null) {
            mPluginScanner = new PanelPluginScanner();
            mPluginScanner.addScanCallBack(this);
        }
        Map<String, Object> args = new HashMap<>();
        if (DinHome.getInstance().getCurrentHome() != null) {
            args.put(IPluginScanner.HOME_ID, DinHome.getInstance().getCurrentHome().getHomeID());
        }
        getPluginScanner().scan(pluginID, mDeviceId, args);
    }

    @Keep
    @Override
    public void bindDevice(Plugin plugin) {
        checkSetup();
        if (plugin == null) {
            callBackBindResult(ErrorCode.ACTIVTOR_BIND_DEVICE_PLUGIN_NULL, "plugin is null");
            return;
        }

        // 根据配件类型，初始化配件绑定器
        if (!TextUtils.isEmpty(plugin.getPluginTypeName())
                && (PluginConstants.NAME_TUYA_BULB.equals(plugin.getPluginTypeName())
                || PluginConstants.NAME_TUYA_SMART_PLUGIN.equals(plugin.getPluginTypeName()))) {
            // 涂鸦配件
            if (!isHadSetWifi()) {
                throw new NullPointerException("You must call method getWifiSSID and setWifiPassword before bind tuya plugin.");
            }
            mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_TUYA);
            if (null != mPluginBinder) {
                Map<String, Object> args = new HashMap<>();
                args.put("ssid", mWifiSSID);
                args.put("ssidPwd", mWifiPassword);
                mPluginBinder.configBinder(args);
            }
        } else if (PluginConstants.TYPE_1F.equals(plugin.getPluginTypeName())) {
            mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_HEARTLAI);
        } else {
            // 自家配件
            mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_DIN_PLUGIN);
        }

        if (null == getPluginBinder()) {
            DDLog.e(TAG, "Empty plugin binder");
            callBackBindResult(ErrorCode.ACTIVTOR_BIND_DEVICE_ACTIVTOR_NULL, "Undefine plugin binder.");
            return;
        }

        mPluginBinder.addBindCallBack(this);
        getPluginBinder().bindDevice(plugin);
    }

    /**
     * 创建主机添加器
     */
    @Keep
    public BasePluginBinder createPanelBinder() {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_PANEL);
        return mPluginBinder;
    }

    /**
     * 创建dscam添加器
     */
    @Keep
    public BasePluginBinder createDsCamBinder() {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_DSCAM);
        return mPluginBinder;
    }


    /**
     * 创建dscam网络修改器
     */
    @Keep
    public BasePluginBinder createDsCamNetworkManager() {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_DSCAM_NETWORK_MANAGER);
        return mPluginBinder;
    }


    /**
     * 创建配件触发配对添加器
     *
     * @param panelIp 主机IP
     */
    @Keep
    public BasePluginBinder createTriggerDeviceBinder(String panelIp) {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_TRIGGER_PLUGIN);
        if (null != mPluginBinder) {
            Map<String, Object> args = new HashMap<>();
            args.put("panelIp", panelIp);
            mPluginBinder.configBinder(args);
        }
        return mPluginBinder;
    }

    /**
     * 创建dsdoorbell添加器
     */
    @Keep
    public BasePluginBinder createDsDoorbellBinder() {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_DSDOORBELL);
        return mPluginBinder;
    }


    /**
     * 创建dsdoorbell网络修改器
     */
    @Keep
    public BasePluginBinder createDsDoorbellNetworkManager() {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_DSDOORBELL_NETWORK_MANAGER);
        return mPluginBinder;
    }

    /**
     * 创建DsCamV006添加器
     */
    @Keep
    public BasePluginBinder createDsCamV006Binder() {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_DSCAM_V006);
        return mPluginBinder;
    }

    /**
     * 创建DsCamV006网络修改器
     */
    @Keep
    public BasePluginBinder createDsCamV006NetworkManager() {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_DSCAM_V006_NETWORK_MANAGER);
        return mPluginBinder;
    }

    /**
     * 创建DsCamV015添加器
     */
    @Keep
    public BasePluginBinder createDsCamV015Binder() {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_DSCAM_V015);
        return mPluginBinder;
    }

    /**
     * 创建DsCamV015网络修改器
     */
    @Keep
    public BasePluginBinder createDsCamV015NetworkManager() {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_DSCAM_V015_NETWORK_MANAGER);
        return mPluginBinder;
    }

    /**
     * 创建bmt HP5000添加器
     *
     * @return
     */
    @Keep
    public BasePluginBinder createBmtHP5000Binder() {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_BMT_HP5000);
        return mPluginBinder;
    }

    /**
     * 创建BMT HP5000网络修改器
     */
    @Keep
    public BasePluginBinder createBmtHP500NetworkManager() {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_BMT_HP5000_NETWORK_MANAGER);
        return mPluginBinder;
    }

    /**
     * 创建bmt HP5001添加器
     *
     * @return
     */
    @Keep
    public BasePluginBinder createBmtHP5001Binder() {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_BMT_HP5001);
        return mPluginBinder;
    }

    /**
     * 创建BMT HP5001网络修改器
     */
    @Keep
    public BasePluginBinder createBmtHP501NetworkManager() {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_BMT_HP5001_NETWORK_MANAGER);
        return mPluginBinder;
    }

    /**
     * 创建Power Core 2.0添加器
     *
     * @return
     */
    @Keep
    public BasePluginBinder createBmtPowerCore20Binder() {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_BMT_POWER_CORE_20);
        return mPluginBinder;
    }

    /**
     * 创建Power Core 2.0网络修改器
     */
    @Keep
    public BasePluginBinder createBmtPowerCore20NetworkManager() {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_BMT_POWER_CORE_20_NETWORK_MANAGER);
        return mPluginBinder;
    }

    /**
     * 创建Power Store添加器
     *
     * @return
     */
    @Keep
    public BasePluginBinder createBmtPowerStoreBinder() {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_BMT_POWER_STORE);
        return mPluginBinder;
    }

    /**
     * 创建Power Store网络修改器
     */
    @Keep
    public BasePluginBinder createBmtPowerStoreNetworkManager() {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_BMT_POWER_STORE_NETWORK_MANAGER);
        return mPluginBinder;
    }

    /**
     * 创建Power Pulse添加器
     * @return
     */
    @Keep
    public BasePluginBinder createBmtPowerPulseBinder() {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_BMT_POWER_PULSE);
        return mPluginBinder;
    }

    /**
     * 创建Power Pulse网络修改器
     * @return
     */
    @Keep
    public BasePluginBinder createBmtPowerPulseNetworkManager() {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_BMT_POWER_PULSE_NETWORK_MANAGER);
        return mPluginBinder;
    }

    /**
     * 创建Power Core 3.0添加器
     *
     * @return
     */
    @Keep
    public BasePluginBinder createBmtPowerCore30Binder() {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_BMT_POWER_CORE_30);
        return mPluginBinder;
    }

    /**
     * 创建Power Core 3.0网络修改器
     */
    @Keep
    public BasePluginBinder createBmtPowerCore30NetworkManager() {
        mPluginBinder = DinHome.getInstance().createPluginBinder(mContext, BINDER_KEY_BMT_POWER_CORE_30_NETWORK_MANAGER);
        return mPluginBinder;
    }
}
