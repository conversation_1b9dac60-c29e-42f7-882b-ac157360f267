package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;

/**
 * @ClassName CloudflareUploadTokenResponse
 * @Date 2025/7/16
 * <AUTHOR>
 * @Description
 */
@Keep
public class CloudflareUploadTokenResponse  extends BaseHttpEntry implements Serializable {
    private ResultBean Result;


    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        Result = result;
    }

    @Keep
    public static class ResultBean implements Serializable {

        private int expires_in;
        private String access;
        private String token;


        public int getExpires_in() {
            return expires_in;
        }

        public void setExpires_in(int expires_in) {
            this.expires_in = expires_in;
        }

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public String getAccess() {
            return access;
        }

        public void setAccess(String access) {
            this.access = access;
        }
    }
}
