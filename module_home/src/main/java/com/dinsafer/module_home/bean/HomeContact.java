package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

/**
 * 家庭手机联系人信息
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/6/10 15:48
 */
@Keep
public class HomeContact {
    private String contact_id;
    private String name;
    private String phone;
    private long addtime;
    private boolean sms;
    private boolean sms_sys;
    private boolean sms_info;
    private boolean sms_sos;

    public String getContact_id() {
        return contact_id;
    }

    public void setContact_id(String contact_id) {
        this.contact_id = contact_id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public long getAddtime() {
        return addtime;
    }

    public void setAddtime(long addtime) {
        this.addtime = addtime;
    }

    public boolean isSms() {
        return sms;
    }

    public void setSms(boolean sms) {
        this.sms = sms;
    }

    public boolean isSms_sys() {
        return sms_sys;
    }

    public void setSms_sys(boolean sms_sys) {
        this.sms_sys = sms_sys;
    }

    public boolean isSms_info() {
        return sms_info;
    }

    public void setSms_info(boolean sms_info) {
        this.sms_info = sms_info;
    }

    public boolean isSms_sos() {
        return sms_sos;
    }

    public void setSms_sos(boolean sms_sos) {
        this.sms_sos = sms_sos;
    }
}
