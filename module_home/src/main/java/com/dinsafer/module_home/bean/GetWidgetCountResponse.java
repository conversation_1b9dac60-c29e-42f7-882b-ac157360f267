package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.util.List;

/**
 * 获取家庭中所有 widget 配件的数量
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/11/23 14:45
 */
@Keep
public class GetWidgetCountResponse extends BaseHttpEntry {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        private int device;
        private long gmtime;
        private List<ProvidersBean> providers;
        private List<StypesBean> stypes;

        public int getDevice() {
            return device;
        }

        public void setDevice(int device) {
            this.device = device;
        }

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        public List<ProvidersBean> getProviders() {
            return providers;
        }

        public void setProviders(List<ProvidersBean> providers) {
            this.providers = providers;
        }

        public List<StypesBean> getStypes() {
            return stypes;
        }

        public void setStypes(List<StypesBean> stypes) {
            this.stypes = stypes;
        }

        @Keep
        public static class ProvidersBean {
            private int count;
            private String provider;

            public int getCount() {
                return count;
            }

            public void setCount(int count) {
                this.count = count;
            }

            public String getProvider() {
                return provider;
            }

            public void setProvider(String provider) {
                this.provider = provider;
            }
        }

        @Keep
        public static class StypesBean {
            private int count;
            private String stype;

            public int getCount() {
                return count;
            }

            public void setCount(int count) {
                this.count = count;
            }

            public String getStype() {
                return stype;
            }

            public void setStype(String stype) {
                this.stype = stype;
            }
        }
    }
}
