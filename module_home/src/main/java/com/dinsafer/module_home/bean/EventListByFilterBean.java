package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

/**
 * @describe：
 * @date：2023/6/2
 * @author: create by <PERSON><PERSON><PERSON>
 */
@Keep
public class EventListByFilterBean {
    /**
     * {
     * "bmt_model": "string",
     * "category": 0,
     * "cmd_name": "string",
     * "data": { },
     * "duration": 0,
     * "message_id": "string",
     * "photo": "string",
     * "plugin_id": "string",
     * "result": 0,
     * "rid": "string",
     * "screenshot": "string",
     * "sub_category": "string",
     * "time": 0,
     * "type": "string",
     * "user": "string"
     * }
     */

    private String bmt_model;
    private String cmd_name;
    private String photo;
    private long time;
    private String user;
    private int category;
    private String sub_category;
    private String plugin_id;
    private int result;
    private String message_id;
    private long duration;
    private String type;
    private String screenshot;
    private String rid;

    private Data data;

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public long getDuration() {
        return duration;
    }

    public void setDuration(long duration) {
        this.duration = duration;
    }

    public String getMessage_id() {
        return message_id;
    }

    public void setMessage_id(String message_id) {
        this.message_id = message_id;
    }

    public int getResult() {
        return result;
    }

    public void setResult(int result) {
        this.result = result;
    }

    public String getCmd_name() {
        return cmd_name;
    }

    public void setCmd_name(String cmd_name) {
        this.cmd_name = cmd_name;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public long getTime() {
        return time;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public int getCategory() {
        return category;
    }

    public void setCategory(int category) {
        this.category = category;
    }

    public void setSub_category(String sub_category) {
        this.sub_category = sub_category;
    }

    public String getSub_category() {
        return sub_category;
    }

    public String getPlugin_id() {
        return plugin_id;
    }

    public void setPlugin_id(String plugin_id) {
        this.plugin_id = plugin_id;
    }

    public String getScreenshot() {
        return screenshot;
    }

    public void setScreenshot(String screenshot) {
        this.screenshot = screenshot;
    }

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public String getBmt_model() {
        return bmt_model;
    }

    public void setBmt_model(String bmt_model) {
        this.bmt_model = bmt_model;
    }

    @Override
    public String toString() {
        return "EventListByFilterBean{" +
                "bmt_model='" + bmt_model + '\'' +
                ", cmd_name='" + cmd_name + '\'' +
                ", photo='" + photo + '\'' +
                ", time=" + time +
                ", user='" + user + '\'' +
                ", category=" + category +
                ", sub_category='" + sub_category + '\'' +
                ", plugin_id='" + plugin_id + '\'' +
                ", result=" + result +
                ", message_id='" + message_id + '\'' +
                ", duration=" + duration +
                ", type='" + type + '\'' +
                ", screenshot='" + screenshot + '\'' +
                ", rid='" + rid + '\'' +
                ", data=" + data +
                '}';
    }

    @Keep
    public static class Data {
        private String uid;
        private int newpermission;
        private int oldpermission;
        private boolean powerstatus;
        private String battery_index;
        private String cabinet_index;
        private Integer charge_quantity;
        private Integer day;
        private String device_name;

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public int getNewpermission() {
            return newpermission;
        }

        public void setNewpermission(int newpermission) {
            this.newpermission = newpermission;
        }

        public int getOldpermission() {
            return oldpermission;
        }

        public void setOldpermission(int oldpermission) {
            this.oldpermission = oldpermission;
        }

        public boolean isPowerstatus() {
            return powerstatus;
        }

        public void setPowerstatus(boolean powerstatus) {
            this.powerstatus = powerstatus;
        }

        public String getBattery_index() {
            return battery_index;
        }

        public void setBattery_index(String battery_index) {
            this.battery_index = battery_index;
        }

        public String getCabinet_index() {
            return cabinet_index;
        }

        public void setCabinet_index(String cabinet_index) {
            this.cabinet_index = cabinet_index;
        }

        public Integer getCharge_quantity() {
            return charge_quantity;
        }

        public void setCharge_quantity(Integer charge_quantity) {
            this.charge_quantity = charge_quantity;
        }

        public Integer getDay() {
            return day;
        }

        public void setDay(Integer day) {
            this.day = day;
        }

        public String getDevice_name() {
            return device_name;
        }

        public void setDevice_name(String device_name) {
            this.device_name = device_name;
        }
    }
}

