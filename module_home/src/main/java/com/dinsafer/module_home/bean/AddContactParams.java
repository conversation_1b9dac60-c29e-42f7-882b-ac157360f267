package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import java.util.ArrayList;
import java.util.List;

/**
 * 添加手机联系人提交参数
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/6/10 16:11
 */
@Keep
public class AddContactParams {
    private boolean sms;
    private boolean sms_sys;
    private boolean sms_info;
    private boolean sms_sos;
    private List<ContactBean> contacts;

    public AddContactParams(boolean sms, boolean sms_sys, boolean sms_info,
                            boolean sms_sos, List<ContactBean> contacts) {
        this.sms = sms;
        this.sms_sys = sms_sys;
        this.sms_info = sms_info;
        this.sms_sos = sms_sos;
        this.contacts = contacts;
    }

    public AddContactParams() {
    }

    public boolean isSms() {
        return sms;
    }

    public void setSms(boolean sms) {
        this.sms = sms;
    }

    public boolean isSms_sys() {
        return sms_sys;
    }

    public void setSms_sys(boolean sms_sys) {
        this.sms_sys = sms_sys;
    }

    public boolean isSms_info() {
        return sms_info;
    }

    public void setSms_info(boolean sms_info) {
        this.sms_info = sms_info;
    }

    public boolean isSms_sos() {
        return sms_sos;
    }

    public void setSms_sos(boolean sms_sos) {
        this.sms_sos = sms_sos;
    }

    public List<ContactBean> getContacts() {
        return contacts;
    }

    public void setContacts(List<ContactBean> contacts) {
        this.contacts = contacts;
    }

    @Keep
    public static class ContactBean {
        private String name;
        private String phone;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public ContactBean(String name, String phone) {
            this.name = name;
            this.phone = phone;
        }

        @Override
        public String toString() {
            return "ContactBean{" +
                    "name='" + name + '\'' +
                    ", phone='" + phone + '\'' +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "AddContactParams{" +
                "sms=" + sms +
                ", sms_sys=" + sms_sys +
                ", sms_info=" + sms_info +
                ", sms_sos=" + sms_sos +
                ", contacts=" + contacts +
                '}';
    }

    @Keep
    public static class Builder {
        private boolean sms;
        private boolean sms_sys;
        private boolean sms_info;
        private boolean sms_sos;
        private List<AddContactParams.ContactBean> contacts;

        @Keep
        public Builder setSms(boolean sms) {
            this.sms = sms;
            return this;
        }

        @Keep
        public Builder setSms_sys(boolean sms_sys) {
            this.sms_sys = sms_sys;
            return this;
        }

        @Keep
        public Builder setSms_info(boolean sms_info) {
            this.sms_info = sms_info;
            return this;
        }

        @Keep
        public Builder setSms_sos(boolean sms_sos) {
            this.sms_sos = sms_sos;
            return this;
        }

        @Keep
        public Builder addContacts(List<AddContactParams.ContactBean> contacts) {
            if (null == this.contacts) {
                this.contacts = new ArrayList<>();
            }
            if (null != contacts) {
                this.contacts.addAll(contacts);
            }
            return this;
        }

        @Keep
        public Builder addContact(AddContactParams.ContactBean contact) {
            if (null == contacts) {
                contacts = new ArrayList<>();
            }
            contacts.add(contact);
            return this;
        }

        @Keep
        public AddContactParams createAddContactParams() {
            return new AddContactParams(sms, sms_sys, sms_info, sms_sos, contacts);
        }
    }
}
