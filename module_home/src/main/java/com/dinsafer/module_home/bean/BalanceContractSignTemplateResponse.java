package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

@Keep
public class BalanceContractSignTemplateResponse {

    public ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        Result = result;
    }

    @Keep
    public static class ResultBean {

        private String attorney_auth_url;
        private String attorney_template_url;
        private String authorization_address;
        private String authorization_company_name;
        private String authorization_email_address;
        private String authorization_organization_number;
        private String country_code;
        private String family_attorney_template_url;
        private String family_terminate_template_url;
        private long gmtime;
        private String terminate_template_url;
        private String version;

        public String getAttorney_auth_url() {
            return attorney_auth_url;
        }

        public void setAttorney_auth_url(String attorney_auth_url) {
            this.attorney_auth_url = attorney_auth_url;
        }

        public String getAttorney_template_url() {
            return attorney_template_url;
        }

        public void setAttorney_template_url(String attorney_template_url) {
            this.attorney_template_url = attorney_template_url;
        }

        public String getAuthorization_address() {
            return authorization_address;
        }

        public void setAuthorization_address(String authorization_address) {
            this.authorization_address = authorization_address;
        }

        public String getAuthorization_company_name() {
            return authorization_company_name;
        }

        public void setAuthorization_company_name(String authorization_company_name) {
            this.authorization_company_name = authorization_company_name;
        }

        public String getAuthorization_email_address() {
            return authorization_email_address;
        }

        public void setAuthorization_email_address(String authorization_email_address) {
            this.authorization_email_address = authorization_email_address;
        }

        public String getAuthorization_organization_number() {
            return authorization_organization_number;
        }

        public void setAuthorization_organization_number(String authorization_organization_number) {
            this.authorization_organization_number = authorization_organization_number;
        }

        public String getCountry_code() {
            return country_code;
        }

        public void setCountry_code(String country_code) {
            this.country_code = country_code;
        }

        public String getFamily_attorney_template_url() {
            return family_attorney_template_url;
        }

        public void setFamily_attorney_template_url(String family_attorney_template_url) {
            this.family_attorney_template_url = family_attorney_template_url;
        }

        public String getFamily_terminate_template_url() {
            return family_terminate_template_url;
        }

        public void setFamily_terminate_template_url(String family_terminate_template_url) {
            this.family_terminate_template_url = family_terminate_template_url;
        }

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        public String getTerminate_template_url() {
            return terminate_template_url;
        }

        public void setTerminate_template_url(String terminate_template_url) {
            this.terminate_template_url = terminate_template_url;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }
    }
}
