package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

@Keep
public class HomeMember {
    private String user_id;
    private String uid;
    private String avatar;
    private int level;
    private boolean bind_phone;
    private boolean push;
    private boolean sms;
    private boolean push_sys;
    private boolean push_info;
    private boolean push_sos;
    private boolean sms_sys;
    private boolean sms_info;
    private boolean sms_sos;
    private long addtime;
    private boolean compatMode = false;

    public String getUser_id() {
        return user_id;
    }

    public void setUser_id(String user_id) {
        this.user_id = user_id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public boolean isBind_phone() {
        return bind_phone;
    }

    public void setBind_phone(boolean bind_phone) {
        this.bind_phone = bind_phone;
    }

    public boolean isPush() {
        return push;
    }

    public void setPush(boolean push) {
        this.push = push;
    }

    public boolean isSms() {
        return sms;
    }

    public void setSms(boolean sms) {
        this.sms = sms;
    }

    public boolean isPush_sys() {
        return push_sys;
    }

    public void setPush_sys(boolean push_sys) {
        this.push_sys = push_sys;
    }

    public boolean isPush_info() {
        return push_info;
    }

    public void setPush_info(boolean push_info) {
        this.push_info = push_info;
    }

    public boolean isPush_sos() {
        return push_sos;
    }

    public void setPush_sos(boolean push_sos) {
        this.push_sos = push_sos;
    }

    public boolean isSms_sys() {
        return sms_sys;
    }

    public void setSms_sys(boolean sms_sys) {
        this.sms_sys = sms_sys;
    }

    public boolean isSms_info() {
        return sms_info;
    }

    public void setSms_info(boolean sms_info) {
        this.sms_info = sms_info;
    }

    public boolean isSms_sos() {
        return sms_sos;
    }

    public void setSms_sos(boolean sms_sos) {
        this.sms_sos = sms_sos;
    }

    public long getAddtime() {
        return addtime;
    }

    public void setAddtime(long addtime) {
        this.addtime = addtime;
    }

    public boolean isCompatMode() {
        return compatMode;
    }

    public void setCompatMode(boolean compatMode) {
        this.compatMode = compatMode;
    }
}
