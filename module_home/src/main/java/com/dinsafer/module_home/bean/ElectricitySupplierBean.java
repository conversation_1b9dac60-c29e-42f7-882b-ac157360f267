package com.dinsafer.module_home.bean;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.Keep;

import java.io.Serializable;

@Keep
public class ElectricitySupplierBean implements Serializable, Parcelable {
    private String id;
    private String name;
    private boolean support;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isSupport() {
        return support;
    }

    public void setSupport(boolean support) {
        this.support = support;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.id);
        dest.writeString(this.name);
        dest.writeByte(this.support ? (byte) 1 : (byte) 0);
    }

    public void readFromParcel(Parcel source) {
        this.id = source.readString();
        this.name = source.readString();
        this.support = source.readByte() != 0;
    }

    public ElectricitySupplierBean() {
    }

    protected ElectricitySupplierBean(Parcel in) {
        this.id = in.readString();
        this.name = in.readString();
        this.support = in.readByte() != 0;
    }

    public static final Creator<ElectricitySupplierBean> CREATOR = new Creator<ElectricitySupplierBean>() {
        @Override
        public ElectricitySupplierBean createFromParcel(Parcel source) {
            return new ElectricitySupplierBean(source);
        }

        @Override
        public ElectricitySupplierBean[] newArray(int size) {
            return new ElectricitySupplierBean[size];
        }
    };
}
