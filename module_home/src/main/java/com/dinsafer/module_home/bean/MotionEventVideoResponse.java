package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.util.List;

/**
 * 请求获取指定事件的视频接口返回
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/8/10 11:26 上午
 */
@Keep
public class MotionEventVideoResponse extends BaseHttpEntry {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        this.Result = result;
    }

    @Keep
    public static class ResultBean {
        private long gmtime;
        private List<VideosBean> videos;

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        public List<VideosBean> getVideos() {
            return videos;
        }

        public void setVideos(List<VideosBean> videos) {
            this.videos = videos;
        }

    }

    @Keep
    public static class VideosBean {
        private long len;
        private long start_time;
        private String url;

        public long getLen() {
            return len;
        }

        public void setLen(long len) {
            this.len = len;
        }

        public long getStart_time() {
            return start_time;
        }

        public void setStart_time(long start_time) {
            this.start_time = start_time;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }
}
