package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;

@Keep
public class StatusResponse extends BaseHttpEntry implements Serializable {

    private StatusBean Result;

    public StatusBean getResult() {
        return Result;
    }

    public void setResult(StatusBean result) {
        Result = result;
    }

    @Keep
    public static class StatusBean {
        Integer status;

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }
    }
}
