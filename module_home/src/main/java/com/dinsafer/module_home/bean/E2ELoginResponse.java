package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;
import com.google.gson.annotations.SerializedName;

/**
 * E2e登陆返回结果
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/6/10 17:16
 */
@Keep
public class E2ELoginResponse extends BaseHttpEntry {

    @SerializedName("Cmd")
    private String cmd;
    @SerializedName("Result")
    private E2EInfo result;

    public String getCmd() {
        return cmd;
    }

    public void setCmd(String cmd) {
        this.cmd = cmd;
    }

    public E2EInfo getResult() {
        return result;
    }

    public void setResult(E2EInfo result) {
        this.result = result;
    }

}
