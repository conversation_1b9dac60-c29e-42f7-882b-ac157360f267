package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;
import android.text.TextUtils;

/**
 * 兼容模式初始化家庭配置参数
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/3/22 4:10 下午
 */
@Keep
public class InitHomeCompatParams {
    private String deviceId;
    private String deviceToken;
    private String language;
    private String deviceName;
    private int level;
    private boolean push;
    private long notification;

    public InitHomeCompatParams(String deviceId, String deviceToken, String language, String deviceName,
                                int level, boolean push, long notification) {
        this.deviceId = deviceId;
        this.deviceToken = deviceToken;
        this.language = language;
        this.deviceName = deviceName;
        this.level = level;
        this.push = push;
        this.notification = notification;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public String getDeviceToken() {
        return deviceToken;
    }

    public String getLanguage() {
        return language;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public int getLevel() {
        return level;
    }

    public boolean isPush() {
        return push;
    }

    public long getNotification() {
        return notification;
    }

    public boolean checkParams() {
        return !TextUtils.isEmpty(deviceId)
                && !TextUtils.isEmpty(deviceToken);
    }

    @Keep
    public static class Builder {
        private String deviceId;
        private String deviceToken;
        private String language;
        private String deviceName;
        private int level = 10;
        private boolean push = true;
        private long notification = 807;

        public Builder setDeviceId(String deviceId) {
            this.deviceId = deviceId;
            return this;
        }

        public Builder setDeviceToken(String deviceToken) {
            this.deviceToken = deviceToken;
            return this;
        }

        public Builder setLanguage(String language) {
            this.language = language;
            return this;
        }

        public Builder setDeviceName(String deviceName) {
            this.deviceName = deviceName;
            return this;
        }

        public Builder setLevel(int level) {
            this.level = level;
            return this;
        }

        public Builder setPush(boolean push) {
            this.push = push;
            return this;
        }

        public Builder setNotification(long notification) {
            this.notification = notification;
            return this;
        }

        public InitHomeCompatParams createInitHomeCompatParams() {
            return new InitHomeCompatParams(deviceId, deviceToken, language, deviceName, level,
                    push, notification);
        }
    }
}
