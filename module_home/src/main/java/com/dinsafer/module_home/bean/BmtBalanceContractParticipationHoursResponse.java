package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;
import java.util.List;

@Keep
public class BmtBalanceContractParticipationHoursResponse extends BaseHttpEntry implements Serializable {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        Result = result;
    }

    @Keep
    public static class ResultBean implements Serializable {

        private List<ParticipationHour> datas;

        public List<ParticipationHour> getDatas() {
            return datas;
        }

        public void setDatas(List<ParticipationHour> datas) {
            this.datas = datas;
        }
    }
}
