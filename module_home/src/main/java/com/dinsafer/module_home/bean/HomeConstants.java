package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

/**
 * 家庭相关常量
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/7/1 4:26 PM
 */
@Keep
public final class HomeConstants {
    @Keep
    public static final class CMD {
        /**
         * 权限有变化时通知(推送/msct)：
         */
        @Keep
        public static final String AUTHORITY_UPDATED = "AuthorityUpdated";

        /**
         * 成员被删除时通知(推送/msct)
         */
        @Keep
        public static final String MEMBER_DELETED = "MemberDeleted";

        /**
         * 主机开始升级
         */
        @Keep
        public static final String DEVICE_UPGRADING = "DeviceUpgrading";

        /**
         * 主机升级完成
         */
        @Keep
        public static final String DEVICE_UPGRADE_COMPLETE = "DeviceUpgradeComplete";

        /**
         * 删除离线主机
         */
        @Keep
        public static final String DEVICE_DELETED = "DeviceDeleted";

        /**
         * 添加主机主机
         */
        @Keep
        public static final String DEVICE_ADD = "DeviceAdded";

        /**
         * 用户已在其他APP登录相同的家庭
         */
        @Keep
        public static final String FORCE_LOGOUT = "force-logout";

        /**
         * IPC低电通知
         */
        public static final String IPC_LOW_BATTERY = "IPC_LOW_BATTERY";

        /**
         * 一天内移动帧测触发次数太多
         */
        public static final String FREQUENCY_IS_TOO_HIGH = "FREQUENCY_IS_TOO_HIGH";

        /**
         * 成员键盘操作密码被新增
         */
        public static final String KEYPAD_MEMBER_PWD_ENABLE = "MemberPwdEnable";

        /**
         * 成员键盘操作密码被删除
         */
        public static final String KEYPAD_MEMBER_PWD_UNABLE = "MemberPwdUnable";

        /**
         * 成员键盘操作密码被重置
         */
        public static final String KEYPAD_MEMBER_PWD_RESET = "MemberPwdReset";
    }
}
