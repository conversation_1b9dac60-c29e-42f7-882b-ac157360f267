package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;
import java.util.List;

/**
 * @describe：
 * @date：2024/10/25
 * @author: create by Sydnee
 */
@Keep
public class FamilyBalanceContractInfoResponse extends BaseHttpEntry implements Serializable {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        Result = result;
    }

    @Keep
    public static class ResultBean implements Serializable {
        Data data;
        boolean first_sign;
        long gmtime;
        List<SignedDevices> signed_devices;
        boolean signing;

        public Data getData() {
            return data;
        }

        public void setData(Data data) {
            this.data = data;
        }

        public boolean isFirst_sign() {
            return first_sign;
        }

        public void setFirst_sign(boolean first_sign) {
            this.first_sign = first_sign;
        }

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        public List<SignedDevices> getSigned_devices() {
            return signed_devices;
        }

        public void setSigned_devices(List<SignedDevices> signed_devices) {
            this.signed_devices = signed_devices;
        }

        public void setSigning(boolean signing) {
            this.signing = signing;
        }

        public boolean isSigning() {
            return signing;
        }

        @Keep
        public static class Data implements Serializable {
            String IBAN;
            String cardholder;
            String city;
            String company_name;
            String country_code;
            String country_name;
            String country_name_display;
            String electricity_supplier;
            String electricity_supplier_id;
            String email_address;
            String eu_vat_number;
            String name;
            String phone_number;
            String street_name_and_number;
            int type;
            String zip_code;

            public String getIBAN() {
                return IBAN;
            }

            public void setIBAN(String IBAN) {
                this.IBAN = IBAN;
            }

            public String getCardholder() {
                return cardholder;
            }

            public void setCardholder(String cardholder) {
                this.cardholder = cardholder;
            }

            public String getCity() {
                return city;
            }

            public void setCity(String city) {
                this.city = city;
            }

            public String getCompany_name() {
                return company_name;
            }

            public void setCompany_name(String company_name) {
                this.company_name = company_name;
            }

            public String getCountry_code() {
                return country_code;
            }

            public void setCountry_code(String country_code) {
                this.country_code = country_code;
            }

            public String getCountry_name() {
                return country_name;
            }

            public void setCountry_name(String country_name) {
                this.country_name = country_name;
            }

            public String getCountry_name_display() {
                return country_name_display;
            }

            public void setCountry_name_display(String country_name_display) {
                this.country_name_display = country_name_display;
            }

            public String getElectricity_supplier() {
                return electricity_supplier;
            }

            public void setElectricity_supplier(String electricity_supplier) {
                this.electricity_supplier = electricity_supplier;
            }

            public String getElectricity_supplier_id() {
                return electricity_supplier_id;
            }

            public void setElectricity_supplier_id(String electricity_supplier_id) {
                this.electricity_supplier_id = electricity_supplier_id;
            }

            public String getEmail_address() {
                return email_address;
            }

            public void setEmail_address(String email_address) {
                this.email_address = email_address;
            }

            public String getEu_vat_number() {
                return eu_vat_number;
            }

            public void setEu_vat_number(String eu_vat_number) {
                this.eu_vat_number = eu_vat_number;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getPhone_number() {
                return phone_number;
            }

            public void setPhone_number(String phone_number) {
                this.phone_number = phone_number;
            }

            public String getStreet_name_and_number() {
                return street_name_and_number;
            }

            public void setStreet_name_and_number(String street_name_and_number) {
                this.street_name_and_number = street_name_and_number;
            }

            public int getType() {
                return type;
            }

            public void setType(int type) {
                this.type = type;
            }

            public String getZip_code() {
                return zip_code;
            }

            public void setZip_code(String zip_code) {
                this.zip_code = zip_code;
            }
        }

        @Keep
        public static class SignedDevices implements Serializable {
            String authorization_company_name;
            String device_id;
            long effective_time;

            public String getAuthorization_company_name() {
                return authorization_company_name;
            }

            public void setAuthorization_company_name(String authorization_company_name) {
                this.authorization_company_name = authorization_company_name;
            }

            public String getDevice_id() {
                return device_id;
            }

            public void setDevice_id(String device_id) {
                this.device_id = device_id;
            }

            public long getEffective_time() {
                return effective_time;
            }

            public void setEffective_time(long effective_time) {
                this.effective_time = effective_time;
            }
        }


    }


    @Override
    public String toString() {
        return "FamilyBalanceContractInfoResponse{" +
                "Result=" + Result +
                '}';
    }
}
