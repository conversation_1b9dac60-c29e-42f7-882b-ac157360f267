package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.util.List;

/**
 * 请求获取指定时间段的事件（时间轴上的事件列表）接口返回
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/8/10 11:26 上午
 */
@Keep
public class MotionEventResponse extends BaseHttpEntry {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        this.Result = result;
    }

    @Keep
    public static class EventsBean {
        private String event_id;
        private long event_start_time;
        private String ipc_id;
        private long len;
        private String cover;

        public String getEvent_id() {
            return event_id;
        }

        public void setEvent_id(String event_id) {
            this.event_id = event_id;
        }

        public long getEvent_start_time() {
            return event_start_time;
        }

        public void setEvent_start_time(long event_start_time) {
            this.event_start_time = event_start_time;
        }

        public String getIpc_id() {
            return ipc_id;
        }

        public void setIpc_id(String ipc_id) {
            this.ipc_id = ipc_id;
        }

        public long getLen() {
            return len;
        }

        public void setLen(long len) {
            this.len = len;
        }

        public String getCover() {
            return cover;
        }

        public void setCover(String cover) {
            this.cover = cover;
        }
    }

    @Keep
    public static class ResultBean {
        private List<EventsBean> events;
        private long gmtime;

        public List<EventsBean> getEvents() {
            return events;
        }

        public void setEvents(List<EventsBean> events) {
            this.events = events;
        }

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }
    }
}
