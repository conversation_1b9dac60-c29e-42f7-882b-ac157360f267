package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;
import java.util.List;

@Keep
public class RegionElectricitySupplierResponse extends BaseHttpEntry implements Serializable {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        Result = result;
    }

    @Keep
    public static class ResultBean {
        private List<ElectricitySupplierBean> electricity_supplier;
        private long gmtime;
        private int total;

        public List<ElectricitySupplierBean> getElectricity_supplier() {
            return electricity_supplier;
        }

        public void setElectricity_supplier(List<ElectricitySupplierBean> electricity_supplier) {
            this.electricity_supplier = electricity_supplier;
        }

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        public int getTotal() {
            return total;
        }

        public void setTotal(int total) {
            this.total = total;
        }
    }
}
