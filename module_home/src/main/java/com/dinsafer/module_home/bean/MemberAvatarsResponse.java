package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;
import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/6/17 4:55 PM
 */
@Keep
public class MemberAvatarsResponse extends BaseHttpEntry {

    @SerializedName("Cmd")
    private String cmd;
    @SerializedName("Result")
    private MemberAvatars result;

    public String getCmd() {
        return cmd;
    }

    public void setCmd(String cmd) {
        this.cmd = cmd;
    }

    public MemberAvatars getResult() {
        return result;
    }

    public void setResult(MemberAvatars result) {
        this.result = result;
    }
}
