package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.util.List;

/**
 * Description:
 * Author: MiraclesHed
 * Date: 2021/10/27
 */
@Keep
public class IPCEventMotionRecordsResponse extends BaseHttpEntry {

    private List<RecordsBean> Result;


    public List<RecordsBean> getResult() {
        return Result;
    }

    public void setResult(List<RecordsBean> result) {
        this.Result = result;
    }

    @Keep
    public static class RecordsBean {
        private Integer index;
        private String url;

        public Integer getIndex() {
            return index;
        }

        public void setIndex(Integer index) {
            this.index = index;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }

}
