package com.dinsafer.module_home.bean;

import com.dinsafer.dincore.http.BaseHttpEntry;

import androidx.annotation.Keep;

/**
 * <AUTHOR>
 * @date 2023/7/28
 */
@Keep
public class DailyMemoriesVideoResponse extends BaseHttpEntry {

    private ResultDTO Result;

    public ResultDTO getResult() {
        return Result;
    }

    public void setResult(ResultDTO result) {
        this.Result = result;
    }

    @Keep
    public static class ResultDTO {
        private long starting_time;
        private String url;

        public long getStartingTime() {
            return starting_time;
        }

        public void setStartingTime(long startingTime) {
            this.starting_time = startingTime;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }
}
