package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * @ClassName SingleBalanceContractRecordResponse
 * @Date 2025/7/18
 * <AUTHOR>
 * @Description
 */
public class SingleBalanceContractRecordResponse extends BaseHttpEntry implements Serializable{

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        this.Result = result;
    }

    @Keep
    public static class ResultBean implements Serializable {

        private String contract_file;
        private int create_time;
        private String home_id;
        private String record_id;
        private String seq;
        private String serial_number;
        private int status;

        public String getContract_file() {
            return contract_file;
        }

        public void setContract_file(String contract_file) {
            this.contract_file = contract_file;
        }

        public int getCreate_time() {
            return create_time;
        }

        public void setCreate_time(int create_time) {
            this.create_time = create_time;
        }

        public String getHome_id() {
            return home_id;
        }

        public void setHome_id(String home_id) {
            this.home_id = home_id;
        }

        public String getRecord_id() {
            return record_id;
        }

        public void setRecord_id(String record_id) {
            this.record_id = record_id;
        }

        public String getSeq() {
            return seq;
        }

        public void setSeq(String seq) {
            this.seq = seq;
        }

        public String getSerial_number() {
            return serial_number;
        }

        public void setSerial_number(String serial_number) {
            this.serial_number = serial_number;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }
    }
}
