package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import java.io.Serializable;
import com.dinsafer.dincore.http.BaseHttpEntry;

/**
 * @describe：
 * @date：2024/11/6
 * @author: create by Sydnee
 */
@Keep
public class BalanceContractUnsignTemplateResponse extends BaseHttpEntry implements Serializable {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        this.Result = result;
    }

    @Keep
    public static class ResultBean implements Serializable {

        private String attorney_auth_url;
        private String authorization_address;
        private String authorization_company_name;
        private String authorization_email_address;
        private String authorization_organization_number;
        private String city;
        private String company_name;
        private String country_name;
        private String country_code;
        private String electricity_supplier;
        private String email_address;
        private String eu_vat_number;
        private String family_attorney_template_url;
        private String family_terminate_template_url;
        private long gmtime;
        private String name;
        private String phone_number;
        private String street_name_and_number;
        private String terminate_template_url;
        private int type;
        private String zip_code;

        public String getFamily_terminate_template_url() {
            return family_terminate_template_url;
        }

        public void setFamily_terminate_template_url(String family_terminate_template_url) {
            this.family_terminate_template_url = family_terminate_template_url;
        }

        public String getFamily_attorney_template_url() {
            return family_attorney_template_url;
        }

        public void setFamily_attorney_template_url(String family_attorney_template_url) {
            this.family_attorney_template_url = family_attorney_template_url;
        }

        public String getCountry_name() {
            return country_name;
        }

        public void setCountry_name(String country_name) {
            this.country_name = country_name;
        }

        public String getAttorney_auth_url() {
            return attorney_auth_url;
        }

        public void setAttorney_auth_url(String attorney_auth_url) {
            this.attorney_auth_url = attorney_auth_url;
        }

        public String getAuthorization_address() {
            return authorization_address;
        }

        public void setAuthorization_address(String authorization_address) {
            this.authorization_address = authorization_address;
        }

        public String getAuthorization_company_name() {
            return authorization_company_name;
        }

        public void setAuthorization_company_name(String authorization_company_name) {
            this.authorization_company_name = authorization_company_name;
        }

        public String getAuthorization_email_address() {
            return authorization_email_address;
        }

        public void setAuthorization_email_address(String authorization_email_address) {
            this.authorization_email_address = authorization_email_address;
        }

        public String getAuthorization_organization_number() {
            return authorization_organization_number;
        }

        public void setAuthorization_organization_number(String authorization_organization_number) {
            this.authorization_organization_number = authorization_organization_number;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getCompany_name() {
            return company_name;
        }

        public void setCompany_name(String company_name) {
            this.company_name = company_name;
        }

        public String getCountry_code() {
            return country_code;
        }

        public void setCountry_code(String country_code) {
            this.country_code = country_code;
        }

        public String getElectricity_supplier() {
            return electricity_supplier;
        }

        public void setElectricity_supplier(String electricity_supplier) {
            this.electricity_supplier = electricity_supplier;
        }

        public String getEmail_address() {
            return email_address;
        }

        public void setEmail_address(String email_address) {
            this.email_address = email_address;
        }

        public String getEu_vat_number() {
            return eu_vat_number;
        }

        public void setEu_vat_number(String eu_vat_number) {
            this.eu_vat_number = eu_vat_number;
        }

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPhone_number() {
            return phone_number;
        }

        public void setPhone_number(String phone_number) {
            this.phone_number = phone_number;
        }

        public String getStreet_name_and_number() {
            return street_name_and_number;
        }

        public void setStreet_name_and_number(String street_name_and_number) {
            this.street_name_and_number = street_name_and_number;
        }

        public String getTerminate_template_url() {
            return terminate_template_url;
        }

        public void setTerminate_template_url(String terminate_template_url) {
            this.terminate_template_url = terminate_template_url;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getZip_code() {
            return zip_code;
        }

        public void setZip_code(String zip_code) {
            this.zip_code = zip_code;
        }
    }
}
