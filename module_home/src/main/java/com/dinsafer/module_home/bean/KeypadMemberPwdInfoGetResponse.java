package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;
import com.google.gson.annotations.SerializedName;

/**
 * 获取指定成员的键盘操作密码信息返回结果
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2023/4/11 16:24
 */
@Keep
public class KeypadMemberPwdInfoGetResponse extends BaseHttpEntry {

    @SerializedName("Result")
    private ResultBean result;

    public ResultBean getResult() {
        return result;
    }

    public void setResult(ResultBean result) {
        this.result = result;
    }

    @Keep
    public static class ResultBean {
        @SerializedName("has_device")
        private Boolean hasDevice;
        @SerializedName("has_key_board")
        private Boolean hasKeyBoard;
        @SerializedName("is_member_exists")
        private Boolean isMemberExists;
        @SerializedName("pwd")
        private String pwd;
        @SerializedName("pwd_enable")
        private Boolean pwdEnable;

        public Boolean getHasDevice() {
            return hasDevice;
        }

        public void setHasDevice(Boolean hasDevice) {
            this.hasDevice = hasDevice;
        }

        public Boolean getHasKeyBoard() {
            return hasKeyBoard;
        }

        public void setHasKeyBoard(Boolean hasKeyBoard) {
            this.hasKeyBoard = hasKeyBoard;
        }

        public Boolean getIsMemberExists() {
            return isMemberExists;
        }

        public void setIsMemberExists(Boolean isMemberExists) {
            this.isMemberExists = isMemberExists;
        }

        public String getPwd() {
            return pwd;
        }

        public void setPwd(String pwd) {
            this.pwd = pwd;
        }

        public Boolean getPwdEnable() {
            return pwdEnable;
        }

        public void setPwdEnable(Boolean pwdEnable) {
            this.pwdEnable = pwdEnable;
        }
    }
}
