package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

@Keep
public class Home {

    private String homeID;
    private String homeName;
    private int level;

    public Home(String homeID, String homeName) {
        this.homeID = homeID;
        this.homeName = homeName;
    }

    public String getHomeID() {
        return homeID;
    }

    public void setHomeID(String homeID) {
        this.homeID = homeID;
    }

    public String getHomeName() {
        return homeName;
    }

    public void setHomeName(String homeName) {
        this.homeName = homeName;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }
}
