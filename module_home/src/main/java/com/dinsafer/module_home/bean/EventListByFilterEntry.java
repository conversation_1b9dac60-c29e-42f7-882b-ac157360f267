package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.util.List;

/**
 * @describe：
 * @date：2023/6/2
 * @author: create by <PERSON>ydnee
 */
@Keep
public class EventListByFilterEntry extends BaseHttpEntry {

    private String Cmd;
    private EventListResult Result;

    public String getCmd() {
        return Cmd;
    }

    public void setCmd(String Cmd) {
        this.Cmd = Cmd;
    }

    public EventListResult getResult() {
        return Result;
    }

    public void setResult(EventListResult result) {
        Result = result;
    }

    @Keep
    public static class EventListResult {
        /**
         * {
         * "gm_time": 1685695980788088219,
         * "event_list": [
         * {
         * "bmt_model": "string",
         * "category": 0,
         * "cmd_name": "string",
         * "data": { },
         * "duration": 0,
         * "message_id": "string",
         * "photo": "string",
         * "plugin_id": "string",
         * "result": 0,
         * "rid": "string",
         * "screenshot": "string",
         * "sub_category": "string",
         * "time": 0,
         * "type": "string",
         * "user": "string"
         * }
         * ]
         *  }
         */
        private long get_time;

        private List<EventListByFilterBean> event_list;

        public long getGet_time() {
            return get_time;
        }

        public void setGet_time(long get_time) {
            this.get_time = get_time;
        }

        public List<EventListByFilterBean> getEvent_list() {
            return event_list;
        }

        public void setEvent_list(List<EventListByFilterBean> event_list) {
            this.event_list = event_list;
        }
    }

}
