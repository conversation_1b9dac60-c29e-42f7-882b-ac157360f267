package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import java.util.Objects;

/**
 * 家庭信息
 *
 * <AUTHOR>
 * @date 2021/6/10 15:30
 */
@Keep
public class HomeInfo {
    private int level;
    private String domain;
    private TuyaBean tuya;
    private DeviceBean device;
    private String homeId;
    private String name;
    private String language;

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public TuyaBean getTuya() {
        return tuya;
    }

    public void setTuya(TuyaBean tuya) {
        this.tuya = tuya;
    }

    public DeviceBean getDevice() {
        return device;
    }

    public void setDevice(DeviceBean device) {
        this.device = device;
    }

    public String getHomeId() {
        return homeId;
    }

    public void setHomeId(String homeId) {
        this.homeId = homeId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    @Keep
    public static class TuyaBean {
        private String password;
        private String username;
        private String countrycode;
        private String uid;

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getCountrycode() {
            return countrycode;
        }

        public void setCountrycode(String countrycode) {
            this.countrycode = countrycode;
        }

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            TuyaBean tuyaBean = (TuyaBean) o;
            return Objects.equals(password, tuyaBean.password) &&
                    Objects.equals(username, tuyaBean.username) &&
                    Objects.equals(countrycode, tuyaBean.countrycode) &&
                    Objects.equals(uid, tuyaBean.uid);
        }

        @Override
        public int hashCode() {
            return Objects.hash(password, username, countrycode, uid);
        }
    }

    @Keep
    public static class DeviceBean {
        private String deviceid;
        private String name;
        private String token;
        private String sim_network;

        public String getDeviceid() {
            return deviceid;
        }

        public void setDeviceid(String deviceid) {
            this.deviceid = deviceid;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public String getSim_network() {
            return sim_network;
        }

        public void setSim_network(String sim_network) {
            this.sim_network = sim_network;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            DeviceBean that = (DeviceBean) o;
            return Objects.equals(deviceid, that.deviceid) &&
                    Objects.equals(name, that.name) &&
                    Objects.equals(token, that.token);
        }

        @Override
        public int hashCode() {
            return Objects.hash(deviceid, name, token);
        }
    }
}
