package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/6/10 17:18
 */
@Keep
public class E2EInfo {
    private String group_id;
    private String end_id;
    private String end_secret;
    private String chat_secret;
    private String host;
    private String util_host;
    private long gmtime;

    public String getGroup_id() {
        return group_id;
    }

    public void setGroup_id(String group_id) {
        this.group_id = group_id;
    }

    public String getEnd_id() {
        return end_id;
    }

    public void setEnd_id(String end_id) {
        this.end_id = end_id;
    }

    public String getEnd_secret() {
        return end_secret;
    }

    public void setEnd_secret(String end_secret) {
        this.end_secret = end_secret;
    }

    public String getChat_secret() {
        return chat_secret;
    }

    public void setChat_secret(String chat_secret) {
        this.chat_secret = chat_secret;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getUtil_host() {
        return util_host;
    }

    public void setUtil_host(String util_host) {
        this.util_host = util_host;
    }

    public long getGmtime() {
        return gmtime;
    }

    public void setGmtime(long gmtime) {
        this.gmtime = gmtime;
    }
}
