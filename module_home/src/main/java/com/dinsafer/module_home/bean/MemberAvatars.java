package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import java.util.List;

/**
 * 主机用户数量和头像信息
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/6/17 4:54 PM
 */
@Keep
public class MemberAvatars {
    private List<AvatarsBean> avatars;
    private int total;
    private long gmtime;


    public List<AvatarsBean> getAvatars() {
        return avatars;
    }

    public void setAvatars(List<AvatarsBean> avatars) {
        this.avatars = avatars;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public long getGmtime() {
        return gmtime;
    }

    public void setGmtime(long gmtime) {
        this.gmtime = gmtime;
    }

    @Keep
    public static class AvatarsBean {
        private String name;
        private String avatar;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }
    }
}
