package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.util.List;

/**
 * 获取指定家庭下的主机 token的返回
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/11/23 15:34
 */
@Keep
public class ListPanelTokensResponse extends BaseHttpEntry {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        private int count;
        private long gmtime;
        private List<TokensBean> tokens;

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        public List<TokensBean> getTokens() {
            return tokens;
        }

        public void setTokens(List<TokensBean> tokens) {
            this.tokens = tokens;
        }

        @Keep
        public static class TokensBean {
            private String id;
            private String token;

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getToken() {
                return token;
            }

            public void setToken(String token) {
                this.token = token;
            }
        }
    }
}
