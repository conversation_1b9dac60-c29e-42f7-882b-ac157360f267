package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.util.List;

/**
 * 获取指定事件的封面结果
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/9/5 11:03 上午
 */
@Keep
public class MotionEventCoverResponse extends BaseHttpEntry {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        this.Result = result;
    }

    @Keep
    public static class ResultBean {
        private int count;
        private List<CoversBean> covers;
        private long gmtime;

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }

        public List<CoversBean> getCovers() {
            return covers;
        }

        public void setCovers(List<CoversBean> covers) {
            this.covers = covers;
        }

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }
    }

    @Keep
    public static class CoversBean {
        private String cover;
        private String event_id;

        public String getCover() {
            return cover;
        }

        public void setCover(String cover) {
            this.cover = cover;
        }

        public String getEvent_id() {
            return event_id;
        }

        public void setEvent_id(String event_id) {
            this.event_id = event_id;
        }
    }
}
