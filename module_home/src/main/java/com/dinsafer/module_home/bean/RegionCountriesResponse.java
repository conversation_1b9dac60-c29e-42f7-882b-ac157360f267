package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;
import java.util.List;

@Keep
public class RegionCountriesResponse  extends BaseHttpEntry implements Serializable {

    private List<CountryBean> Result;

    public List<CountryBean> getResult() {
        return Result;
    }

    public void setResult(List<CountryBean> result) {
        Result = result;
    }

    //    @Keep
//    public static class ResultBean implements Serializable{
//
//        private long gmtime;
//        private List<CountryBean> list;
//
//        public long getGmtime() {
//            return gmtime;
//        }
//
//        public void setGmtime(long gmtime) {
//            this.gmtime = gmtime;
//        }
//
//        public List<CountryBean> getList() {
//            return list;
//        }
//
//        public void setList(List<CountryBean> list) {
//            this.list = list;
//        }
//    }
}
