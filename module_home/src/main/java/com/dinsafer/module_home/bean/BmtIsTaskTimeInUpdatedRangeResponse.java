package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;

@Keep
public class BmtIsTaskTimeInUpdatedRangeResponse extends BaseHttpEntry implements Serializable {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        Result = result;
    }

    @Keep
    public static class ResultBean implements Serializable {

        private boolean is_task_time_in_updated_range;

        public boolean isIs_task_time_in_updated_range() {
            return is_task_time_in_updated_range;
        }

        public void setIs_task_time_in_updated_range(boolean is_task_time_in_updated_range) {
            this.is_task_time_in_updated_range = is_task_time_in_updated_range;
        }
    }
}
