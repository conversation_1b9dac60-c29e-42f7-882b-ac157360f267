package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;
import java.util.List;

@Keep
public class HomeListResponse extends BaseHttpEntry implements Serializable {


    /**
     * Cmd :
     * Result : [{"home_id":"","home_name":""},{"home_id":"","home_name":""}]
     */

    private List<ResultBean> Result;

    public List<ResultBean> getResult() {
        return Result;
    }

    public void setResult(List<ResultBean> Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean implements Serializable {
        /**
         * home_id :
         * home_name :
         */

        private String home_id;
        private String home_name;
        private int level;

        public String getHome_id() {
            return home_id;
        }

        public void setHome_id(String home_id) {
            this.home_id = home_id;
        }

        public String getHome_name() {
            return home_name;
        }

        public void setHome_name(String home_name) {
            this.home_name = home_name;
        }

        public int getLevel() {
            return level;
        }

        public void setLevel(int level) {
            this.level = level;
        }
    }
}
