package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.util.List;

/**
 * 请求获取 ipc 最新的移动侦测触发时间（页面的 ipc 排序、列表）接口返回
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/8/10 11:26 上午
 */
@Keep
public class LastMotionIpcListResponse extends BaseHttpEntry {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        this.Result = result;
    }

    @Keep
    public static class ResultBean {
        private long gmtime;
        private List<IpcsBean> ipcs;

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        public List<IpcsBean> getIpcs() {
            return ipcs;
        }

        public void setIpcs(List<IpcsBean> ipcs) {
            this.ipcs = ipcs;
        }
    }

    @Keep
    public static class IpcsBean {
        private String ipc_id;
        private long time;
        private String name;

        public String getIpc_id() {
            return ipc_id;
        }

        public void setIpc_id(String ipc_id) {
            this.ipc_id = ipc_id;
        }

        public long getTime() {
            return time;
        }

        public void setTime(long time) {
            this.time = time;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
}
