package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.util.List;

/**
 * DsCam固件版本
 */
@Keep
public class IPCFirmwareVersionResponse extends BaseHttpEntry {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        this.Result = result;
    }

    @Keep
    public static class ResultBean {
        private CAMBean DSCAM;
        private CAMBean DSCAM_V006;
        private CAMBean DSCAM_V015;
        private CAMBean DSDOORBELL;

        public CAMBean getDSCAM() {
            return DSCAM;
        }

        public void setDSCAM(CAMBean dSCAM) {
            this.DSCAM = dSCAM;
        }

        public CAMBean getDSDOORBELL() {
            return DSDOORBELL;
        }

        public void setDSDOORBELL(CAMBean dSDOORBELL) {
            this.DSDOORBELL = dSDOORBELL;
        }

        public CAMBean getDSCAM_V006() {
            return DSCAM_V006;
        }

        public void setDSCAM_V006(CAMBean DSCAM_V006) {
            this.DSCAM_V006 = DSCAM_V006;
        }

        public CAMBean getDSCAM_V015() {
            return DSCAM_V015;
        }

        public void setDSCAM_V015(CAMBean DSCAM_V015) {
            this.DSCAM_V015 = DSCAM_V015;
        }
    }

    @Keep
    public static class CAMBean {
        private List<FirmwareInfo> hi3518ev300;
        private List<FirmwareInfo> gk7202v300;

        public List<FirmwareInfo> getHi3518ev300() {
            return hi3518ev300;
        }

        public void setHi3518ev300(List<FirmwareInfo> hi3518ev300) {
            this.hi3518ev300 = hi3518ev300;
        }

        public List<FirmwareInfo> getGk7202v300() {
            return gk7202v300;
        }

        public void setGk7202v300(List<FirmwareInfo> gk7202v300) {
            this.gk7202v300 = gk7202v300;
        }

    }

    @Keep
    public static class FirmwareInfo {
        private String last_version;
        private String min_version;
        private String url;
        private String md5;
        private String type;

        public String getLast_version() {
            return last_version;
        }

        public void setLast_version(String last_version) {
            this.last_version = last_version;
        }

        public String getMin_version() {
            return min_version;
        }

        public void setMin_version(String min_version) {
            this.min_version = min_version;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getMd5() {
            return md5;
        }

        public void setMd5(String md5) {
            this.md5 = md5;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }
    }
}
