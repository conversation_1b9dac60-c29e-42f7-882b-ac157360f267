package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;
import com.google.gson.annotations.SerializedName;

/**
 * 创建房间结果返回
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/6/25 2:43 PM
 */
@Keep
public class CreateHomeResponse extends BaseHttpEntry {

    @SerializedName("Cmd")
    private String cmd;
    @SerializedName("Result")
    private ResultBean result;

    public String getCmd() {
        return cmd;
    }

    public void setCmd(String cmd) {
        this.cmd = cmd;
    }

    public ResultBean getResult() {
        return result;
    }

    public void setResult(ResultBean result) {
        this.result = result;
    }

    @Keep
    public static class ResultBean {
        private long gmtime;
        private String group_id;
        private String home_id;

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        public String getGroup_id() {
            return group_id;
        }

        public void setGroup_id(String group_id) {
            this.group_id = group_id;
        }

        public String getHome_id() {
            return home_id;
        }

        public void setHome_id(String home_id) {
            this.home_id = home_id;
        }
    }
}
