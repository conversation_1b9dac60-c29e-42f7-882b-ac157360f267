package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;
import java.util.List;


/**
 * @describe：
 * @date：2024/11/6
 * @author: create by Sydnee
 */
@Keep
public class BalanceContractRecordsResponse extends BaseHttpEntry implements Serializable {

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        Result = result;
    }

    @Keep
    public static class ResultBean implements Serializable {
        List<Record> records;

        public List<Record> getRecords() {
            return records;
        }

        public void setRecords(List<Record> records) {
            this.records = records;
        }
    }

    @Keep
    public static class Record implements Serializable {
        private String attorney_auth;
        private String contract_file;
        private long create_time;
        private String home_id;
        private String home_name;
        private String seq;
        private String serial_number;
        private int status;

        public String getAttorney_auth() {
            return attorney_auth;
        }

        public void setAttorney_auth(String attorney_auth) {
            this.attorney_auth = attorney_auth;
        }

        public String getContract_file() {
            return contract_file;
        }

        public void setContract_file(String contract_file) {
            this.contract_file = contract_file;
        }

        public long getCreate_time() {
            return create_time;
        }

        public void setCreate_time(long create_time) {
            this.create_time = create_time;
        }

        public String getHome_id() {
            return home_id;
        }

        public void setHome_id(String home_id) {
            this.home_id = home_id;
        }

        public String getHome_name() {
            return home_name;
        }

        public void setHome_name(String home_name) {
            this.home_name = home_name;
        }

        public String getSeq() {
            return seq;
        }

        public void setSeq(String seq) {
            this.seq = seq;
        }

        public String getSerial_number() {
            return serial_number;
        }

        public void setSerial_number(String serial_number) {
            this.serial_number = serial_number;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }


    }
}