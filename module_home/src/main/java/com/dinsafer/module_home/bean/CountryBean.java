package com.dinsafer.module_home.bean;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.Keep;

import java.io.Serializable;

@Keep
public class CountryBean  implements Serializable, Parcelable {

    private boolean balance_contract_support;
    private String country_code;
    private String country_name;
    private String country_name_display;
    private boolean elec_support;
    private boolean grid_conn_support;
    private boolean grid_to_battery;
    private String timezone;

    public boolean isBalance_contract_support() {
        return balance_contract_support;
    }

    public void setBalance_contract_support(boolean balance_contract_support) {
        this.balance_contract_support = balance_contract_support;
    }

    public String getCountry_code() {
        return country_code;
    }

    public void setCountry_code(String country_code) {
        this.country_code = country_code;
    }

    public String getCountry_name() {
        return country_name;
    }

    public void setCountry_name(String country_name) {
        this.country_name = country_name;
    }

    public String getCountry_name_display() {
        return country_name_display;
    }

    public void setCountry_name_display(String country_name_display) {
        this.country_name_display = country_name_display;
    }

    public boolean isElec_support() {
        return elec_support;
    }

    public void setElec_support(boolean elec_support) {
        this.elec_support = elec_support;
    }

    public boolean isGrid_conn_support() {
        return grid_conn_support;
    }

    public void setGrid_conn_support(boolean grid_conn_support) {
        this.grid_conn_support = grid_conn_support;
    }

    public boolean isGrid_to_battery() {
        return grid_to_battery;
    }

    public void setGrid_to_battery(boolean grid_to_battery) {
        this.grid_to_battery = grid_to_battery;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeByte(this.balance_contract_support ? (byte) 1 : (byte) 0);
        dest.writeString(this.country_code);
        dest.writeString(this.country_name);
        dest.writeString(this.country_name_display);
        dest.writeByte(this.elec_support ? (byte) 1 : (byte) 0);
        dest.writeByte(this.grid_conn_support ? (byte) 1 : (byte) 0);
        dest.writeByte(this.grid_to_battery ? (byte) 1 : (byte) 0);
        dest.writeString(this.timezone);
    }

    public void readFromParcel(Parcel source) {
        this.balance_contract_support = source.readByte() != 0;
        this.country_code = source.readString();
        this.country_name = source.readString();
        this.country_name_display = source.readString();
        this.elec_support = source.readByte() != 0;
        this.grid_conn_support = source.readByte() != 0;
        this.grid_to_battery = source.readByte() != 0;
        this.timezone = source.readString();
    }

    public CountryBean() {
    }

    protected CountryBean(Parcel in) {
        this.balance_contract_support = in.readByte() != 0;
        this.country_code = in.readString();
        this.country_name = in.readString();
        this.country_name_display = in.readString();
        this.elec_support = in.readByte() != 0;
        this.grid_conn_support = in.readByte() != 0;
        this.grid_to_battery = in.readByte() != 0;
        this.timezone = in.readString();
    }

    public static final Creator<CountryBean> CREATOR = new Creator<CountryBean>() {
        @Override
        public CountryBean createFromParcel(Parcel source) {
            return new CountryBean(source);
        }

        @Override
        public CountryBean[] newArray(int size) {
            return new CountryBean[size];
        }
    };
}
