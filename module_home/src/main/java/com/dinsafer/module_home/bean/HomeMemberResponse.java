package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

@Keep
public class HomeMemberResponse extends BaseHttpEntry implements Serializable {


    @SerializedName("Cmd")
    private String cmd;
    @SerializedName("Result")
    private List<HomeMember> result;

    public String getCmd() {
        return cmd;
    }

    public void setCmd(String cmd) {
        this.cmd = cmd;
    }

    public List<HomeMember> getResult() {
        return result;
    }

    public void setResult(List<HomeMember> result) {
        this.result = result;
    }
}
