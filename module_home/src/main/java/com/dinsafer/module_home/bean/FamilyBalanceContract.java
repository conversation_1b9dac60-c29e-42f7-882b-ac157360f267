package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import java.io.Serializable;
import java.util.List;

/**
 * @describe：
 * @date：2024/10/27
 * @author: create by Sydnee
 */
@Keep
public class FamilyBalanceContract implements Serializable {
    private String IBAN;
    private String cardholder;
    private String city;
    private String company_name;
    private String country_code;
    private String electricitySupplier;
    private String electricitySupplierId;
    private String emailAddress;
    private String euVatNumber;
    private String name;
    private String phoneNumber;
    private String sign;
    private String streetNameAndNumber;
    private Integer type;
    private String version;
    private String zipCode;

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getCountry_code() {
        return country_code;
    }

    public void setCountry_code(String country_code) {
        this.country_code = country_code;
    }

    public String getIBAN() {
        return IBAN;
    }

    public void setIBAN(String IBAN) {
        this.IBAN = IBAN;
    }

    public String getCardholder() {
        return cardholder;
    }

    public void setCardholder(String cardholder) {
        this.cardholder = cardholder;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCompany_name() {
        return company_name;
    }

    public void setCompany_name(String company_name) {
        this.company_name = company_name;
    }
    public String getElectricitySupplier() {
        return electricitySupplier;
    }

    public void setElectricitySupplier(String electricitySupplier) {
        this.electricitySupplier = electricitySupplier;
    }

    public String getElectricitySupplierId() {
        return electricitySupplierId;
    }

    public void setElectricitySupplierId(String electricitySupplierId) {
        this.electricitySupplierId = electricitySupplierId;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getEuVatNumber() {
        return euVatNumber;
    }

    public void setEuVatNumber(String euVatNumber) {
        this.euVatNumber = euVatNumber;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getStreetNameAndNumber() {
        return streetNameAndNumber;
    }

    public void setStreetNameAndNumber(String streetNameAndNumber) {
        this.streetNameAndNumber = streetNameAndNumber;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }



}
