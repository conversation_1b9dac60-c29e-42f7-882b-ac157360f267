package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.util.List;

/**
 * Created by Rinfon on 16/12/27.
 */
@Keep
public class IPCMotionDetectionRecordResponse extends BaseHttpEntry {

    /**
     * Cmd :
     * Result : [{"id":"5a4d998f789df14bd1000012","ipcname":"ipc1111","recordtime":1515035023009860654,"cover":"Fm2JY2rI-hJHHGMtHBnocIu9X3Ti","video":"FhkI7f-hoAYYkYVK4iAyiW7gxRE_"},{"id":"5a4d944c789df14bd1000011","ipcname":"ipc1111","recordtime":1515033676122562682,"cover":"FogBdpSmhbKjipQEmzrNZTGlPv3J","video":"FmIO2bxKs2unKnf5YcAuNbyIWunr"},{"id":"5a4d9436789df14bd1000010","ipcname":"ipc1111","recordtime":1515033654019955566,"cover":"FniLH6-ChfR0iF2H2XFA7o-3lONz","video":"FiFZoO_8deAyH4heWQcJPO16n_1V"},{"id":"5a4d918b789df14bd100000f","ipcname":"ipc1111","recordtime":1515032971594307267,"cover":"FmVUJwnlUReCZMwgfYKJjyfSp4gT","video":"FiPhLkR8ztGvKoz43_t2oyIJG665"},{"id":"5a4d8b11789df14bd100000e","ipcname":"ipc1111","recordtime":1515031313288564416,"cover":"FsB3d_XG8fgp_Qn_x_m-4rlcJTqF","video":"FhAd_yr7SPhU8fKRRcDLhGcg-0Vy"},{"id":"5a4d89aa789df14bd100000d","ipcname":"ipc1111","recordtime":1515030954118413805,"cover":"FtIxy_iowmjRLTCUCHcR1qe0Gx4F","video":"FjGpU18lef5wP3nJSxgBTAtNjxLW"},{"id":"5a4d898a789df14bd100000c","ipcname":"ipc1111","recordtime":1515030922320325908,"cover":"Fuc9WqKtGHbNX3OcaZrfEYcNstTX","video":"FsWi345vTRO1tkV48sLEOLVabl0s"},{"id":"5a45dee9789df138b5000006","ipcname":"ipc1111","recordtime":1514528489150104822,"cover":"FiEi7LBBhLsurKseHgFujmW5rHlO","video":"FhkI7f-hoAYYkYVK4iAyiW7gxRE_"},{"id":"5a45cac0789df138b5000005","ipcname":"ipc1111","recordtime":1514523328643905201,"cover":"FjP9f1LTB5URgPSNnhblmXvrTAtA","video":"FhkI7f-hoAYYkYVK4iAyiW7gxRE_"},{"id":"5a45c88f789df138b5000004","ipcname":"ipc1111","recordtime":1514522767066929295,"cover":"FjP9f1LTB5URgPSNnhblmXvrTAtA","video":"FhkI7f-hoAYYkYVK4iAyiW7gxRE_"},{"id":"5a45c83c789df138b5000003","ipcname":"ipc1111","recordtime":1514522684828242015,"cover":"FjP9f1LTB5URgPSNnhblmXvrTAtA","video":"FhkI7f-hoAYYkYVK4iAyiW7gxRE_"},{"id":"5a43a8de789df16e43000006","ipcname":"ipc2222","recordtime":1514383582047779640,"cover":"FiEi7LBBhLsurKseHgFujmW5rHlO","video":"FhkI7f-hoAYYkYVK4iAyiW7gxRE_"},{"id":"5a436cc9b59e5739ed167e92","ipcname":"ipc2222","recordtime":1514383234361841230,"cover":"Fto5o-5ea0sNMlW_75VgGJCv2AcJ","video":"FhkI7f-hoAYYkYVK4iAyiW7gxRE_"},{"id":"5a43a782789df16e43000003","ipcname":"ipc2222","recordtime":1514383234342841230,"cover":"FiEi7LBBhLsurKseHgFujmW5rHlO","video":"FhkI7f-hoAYYkYVK4iAyiW7gxRE_"}]
     */

    private String Cmd;
    private long gmtime;
    private List<RecordBean> Result;

    public String getCmd() {
        return Cmd;
    }

    public void setCmd(String Cmd) {
        this.Cmd = Cmd;
    }

    public long getGmtime() {
        return gmtime;
    }

    public void setGmtime(long gmtime) {
        this.gmtime = gmtime;
    }

    public List<RecordBean> getResult() {
        return Result;
    }

    public void setResult(List<RecordBean> result) {
        Result = result;
    }

    @Keep
    public static class RecordBean {
        /**
         * id : 5a4d998f789df14bd1000012
         * ipcname : ipc1111
         * recordtime : 1515035023009860654
         * cover : Fm2JY2rI-hJHHGMtHBnocIu9X3Ti
         * video : FhkI7f-hoAYYkYVK4iAyiW7gxRE_
         */

        private String id;
        private String ipcname;
        private long recordtime;
        private String cover;
        private String ipc_id;
        private String provider;
        private String event_id;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getIpcname() {
            return ipcname;
        }

        public void setIpcname(String ipcname) {
            this.ipcname = ipcname;
        }

        public long getRecordtime() {
            return recordtime;
        }

        public void setRecordtime(long recordtime) {
            this.recordtime = recordtime;
        }

        public String getCover() {
            return cover;
        }

        public void setCover(String cover) {
            this.cover = cover;
        }

        public String getIpcId() {
            return ipc_id;
        }

        public void setIpcId(String pid) {
            this.ipc_id = pid;
        }

        public String getProvider() {
            return provider;
        }

        public void setProvider(String provider) {
            this.provider = provider;
        }

        public String getEvent_id() {
            return event_id;
        }

        public void setEvent_id(String event_id) {
            this.event_id = event_id;
        }
    }
}
