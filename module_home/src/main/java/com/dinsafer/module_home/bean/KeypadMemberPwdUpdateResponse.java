package com.dinsafer.module_home.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;
import com.google.gson.annotations.SerializedName;

/**
 * 修改指定成员的键盘操作密码开关状态返回结果
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2023/4/11 16:24
 */
@Keep
public class KeypadMemberPwdUpdateResponse extends BaseHttpEntry {

    @SerializedName("Result")
    private ResultBean result;

    public ResultBean getResult() {
        return result;
    }

    public void setResult(ResultBean result) {
        this.result = result;
    }

    @Keep
    public static class ResultBean {
        @SerializedName("pwd")
        private String pwd;

        public String getPwd() {
            return pwd;
        }

        public void setPwd(String pwd) {
            this.pwd = pwd;
        }
    }
}
