# 文件上传ClassCastException调试指南

## 问题描述

您遇到的错误：
```
文件上传失败: canceled due to java.lang.ClassCastException: okhttp3.RequestBody$Companion$asRequestBody$1 cannot be cast to okhttp3.FormBody
```

这个错误表明代码中某个地方期望的是`FormBody`类型，但实际提供的是普通的`RequestBody`。

## 调试步骤

### 1. 使用简化版本测试

首先使用最简单的上传方法，不涉及token获取：

```java
// 在您的Activity或Fragment中
BaseHome baseHome = // 获取BaseHome实例

// 测试基本上传功能
baseHome.uploadFileDirectly(
    "https://httpbin.org/put",  // 测试URL
    "/sdcard/test_image.jpg",   // 文件路径
    "image/jpeg",               // Content-Type
    new IDefaultCallBack2<String>() {
        @Override
        public void onSuccess(String result) {
            Log.i("Upload", "上传成功: " + result);
        }
        
        @Override
        public void onError(int errorCode, String errorMsg) {
            Log.e("Upload", "上传失败: " + errorMsg);
        }
    }
);
```

### 2. 使用调试版本获取详细信息

如果基本版本仍然失败，使用调试版本：

```java
// 使用调试版本
baseHome.uploadFileWithDebug(
    "https://httpbin.org/put",
    "/sdcard/test_image.jpg",
    "image/jpeg",
    new IDefaultCallBack2<String>() {
        @Override
        public void onSuccess(String result) {
            Log.i("Upload", "调试上传成功: " + result);
        }
        
        @Override
        public void onError(int errorCode, String errorMsg) {
            Log.e("Upload", "调试上传失败: " + errorMsg);
        }
    }
);
```

### 3. 使用测试工具类

```java
// 创建测试实例
SimpleUploadTest uploadTest = new SimpleUploadTest(baseHome);

// 输出调试信息
uploadTest.printDebugInfo();

// 测试基本上传
uploadTest.testBasicUpload();

// 测试错误处理
uploadTest.testErrorHandling();
```

## 可能的问题原因

### 1. Retrofit拦截器问题

如果您的项目中有自定义的Retrofit拦截器，可能会修改RequestBody类型：

```java
// 检查Api.getApi().getRetrofit()的配置
OkHttpClient client = Api.getApi().getRetrofit().callFactory() instanceof OkHttpClient ? 
        (OkHttpClient) Api.getApi().getRetrofit().callFactory() : 
        new OkHttpClient();
```

**解决方案**：使用独立的OkHttpClient
```java
// 创建独立的客户端，避免项目配置干扰
OkHttpClient client = new OkHttpClient.Builder()
    .connectTimeout(30, TimeUnit.SECONDS)
    .writeTimeout(60, TimeUnit.SECONDS)
    .readTimeout(30, TimeUnit.SECONDS)
    .build();
```

### 2. MediaType解析问题

某些Content-Type可能导致RequestBody创建失败：

```java
// 调试MediaType创建
MediaType mediaType = MediaType.parse(contentType);
if (mediaType == null) {
    Log.e("Upload", "MediaType解析失败: " + contentType);
    // 使用默认类型
    mediaType = MediaType.parse("application/octet-stream");
}
```

### 3. 文件访问权限问题

Android 6.0+需要运行时权限：

```java
// 检查文件读取权限
if (ContextCompat.checkSelfPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE) 
    != PackageManager.PERMISSION_GRANTED) {
    // 请求权限
}
```

### 4. OkHttp版本兼容性

检查项目中的OkHttp版本：

```gradle
// 在build.gradle中检查
implementation 'com.squareup.okhttp3:okhttp:4.x.x'
```

## 调试输出分析

### 正常输出示例：
```
创建OkHttp客户端...
创建RequestBody...
RequestBody类型: okhttp3.RequestBody$Companion$asRequestBody$1
构建HTTP请求...
开始执行上传请求...
收到上传响应，状态码: 200
```

### 异常输出示例：
```
创建RequestBody...
RequestBody类型: okhttp3.RequestBody$Companion$asRequestBody$1
构建HTTP请求...
请求执行失败: ClassCastException: okhttp3.RequestBody$Companion$asRequestBody$1 cannot be cast to okhttp3.FormBody
异常类型: java.lang.ClassCastException
```

## 解决方案

### 方案1：使用MultipartBody（如果服务器支持）

```java
MultipartBody.Builder builder = new MultipartBody.Builder()
    .setType(MultipartBody.FORM);

RequestBody fileBody = RequestBody.create(MediaType.parse(contentType), file);
builder.addFormDataPart("file", file.getName(), fileBody);

RequestBody multipartBody = builder.build();
```

### 方案2：使用自定义RequestBody

```java
public class CustomRequestBody extends RequestBody {
    private final File file;
    private final MediaType contentType;
    
    public CustomRequestBody(File file, String contentType) {
        this.file = file;
        this.contentType = MediaType.parse(contentType);
    }
    
    @Override
    public MediaType contentType() {
        return contentType;
    }
    
    @Override
    public long contentLength() throws IOException {
        return file.length();
    }
    
    @Override
    public void writeTo(BufferedSink sink) throws IOException {
        try (Source source = Okio.source(file)) {
            sink.writeAll(source);
        }
    }
}
```

### 方案3：完全绕过Retrofit

```java
// 使用原生OkHttp，不依赖项目配置
OkHttpClient client = new OkHttpClient();
RequestBody body = RequestBody.create(MediaType.parse(contentType), file);
Request request = new Request.Builder()
    .url(uploadUrl)
    .put(body)
    .build();
    
client.newCall(request).execute();
```

## 测试建议

1. **先测试网络连接**：使用httpbin.org等测试服务
2. **逐步增加复杂度**：从最简单的上传开始
3. **检查日志输出**：关注RequestBody的具体类型
4. **测试不同文件类型**：确保Content-Type处理正确
5. **验证文件权限**：确保应用有文件读取权限

## 常见错误排查

| 错误信息 | 可能原因 | 解决方案 |
|---------|---------|---------|
| ClassCastException | RequestBody类型转换问题 | 使用独立OkHttpClient |
| FileNotFoundException | 文件不存在或权限不足 | 检查文件路径和权限 |
| ConnectException | 网络连接问题 | 检查URL和网络状态 |
| IllegalArgumentException | MediaType解析失败 | 检查Content-Type格式 |

通过这些调试工具和方法，应该能够定位到ClassCastException的具体原因并找到解决方案。
