package com.dinsafer.module_home;

import static com.dinsafer.dincore.common.ErrorCode.DEFAULT;
import static com.dinsafer.dincore.common.ErrorCode.MEMBER_EXSPIRED;
import static com.dinsafer.dssupport.msctlib.msct.Exoption.OPTION_STATUS;

import android.content.Context;
import android.os.Looper;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.common.CommonCmdEvent;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.DeviceManager;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dincore.common.IDeviceListChangeListener;
import com.dinsafer.dincore.common.IDeviceManager;
import com.dinsafer.dincore.http.Api;
import com.dinsafer.dincore.http.FileUploadService;
import com.dinsafer.dincore.http.NetWorkException;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.user.api.IResultCallback2;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.msctlib.msct.Exoption;
import com.dinsafer.dssupport.msctlib.msct.MsctResponse;
import com.dinsafer.dssupport.msctlib.netty.IMsctSender;
import com.dinsafer.dssupport.msctlib.netty.IMultipleSender;
import com.dinsafer.dssupport.msctlib.netty.IMultipleSenderCallBack;
import com.dinsafer.dssupport.msctlib.netty.ISenderCallBack;
import com.dinsafer.dssupport.msctlib.netty.MultipleSender;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_home.api.IHome;
import com.dinsafer.module_home.api.IHomeCallBack;

import com.dinsafer.module_home.bean.CountryBean;
import com.dinsafer.module_home.bean.E2EInfo;
import com.dinsafer.module_home.bean.E2ELoginResponse;
import com.dinsafer.module_home.bean.ElectricitySupplierBean;
import com.dinsafer.module_home.bean.FamilyBalanceContract;
import com.dinsafer.module_home.bean.FamilyBalanceContractInfoResponse;
import com.dinsafer.module_home.bean.GetWidgetCountResponse;
import com.dinsafer.module_home.bean.Home;
import com.dinsafer.module_home.bean.HomeInfo;
import com.dinsafer.module_home.bean.HomeMember;
import com.dinsafer.module_home.bean.IPCEventMotionRecordsResponse;
import com.dinsafer.module_home.bean.IPCFirmwareVersionResponse;
import com.dinsafer.module_home.bean.IPCMotionDetectionRecordResponse;
import com.dinsafer.module_home.bean.IPCMotionEventRecordCountResponse;
import com.dinsafer.module_home.bean.KeypadMemberPwdInfoGetResponse;
import com.dinsafer.module_home.bean.KeypadMemberPwdResetResponse;
import com.dinsafer.module_home.bean.KeypadMemberPwdUpdateResponse;
import com.dinsafer.module_home.bean.LastMotionIpcListResponse;
import com.dinsafer.module_home.bean.MotionEventCoverResponse;
import com.dinsafer.module_home.bean.MotionEventDatesResponse;
import com.dinsafer.module_home.bean.MotionEventResponse;
import com.dinsafer.module_home.bean.MotionEventVideoResponse;
import com.dinsafer.module_home.bean.RegionCountriesResponse;
import com.dinsafer.module_home.bean.RegionElectricitySupplierResponse;
import com.dinsafer.module_home.bean.SingleBalanceContractRecordResponse;
import com.dinsafer.module_home.http.HomeApi;
import com.dinsafer.module_home.http.HomeRepo;
import com.dinsafer.module_home.utils.Utils;
import com.qiniu.android.http.ResponseInfo;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/3/22 4:52 下午
 */
abstract class BaseHome implements IHome {
    protected static final String TAG = BaseHome.class.getSimpleName();

    protected IDeviceManager mDeviceManger;
    protected HomeRepo homeRepo;
    protected HomeConnectionManager mHomeConnectionManager;

    protected HomeInfo mCurrentHomeInfo;
    protected ISenderCallBack homeSenderCallBack;
    protected Home currentHome;
    protected List<IHomeCallBack> homeCallBacks = new ArrayList<>();

    private final static int HOME_STATUS_DISCONNECT = 0;
    private final static int HOME_STATUS_CONNECTED = 1;

    private int homeStatus = HOME_STATUS_DISCONNECT;


    protected BaseHome() {
        mDeviceManger = new DeviceManager();
        homeRepo = new HomeRepo();
        mHomeConnectionManager = new HomeConnectionManager();
        mHomeConnectionManager.initLocalMultipleSender();

        homeSenderCallBack = new ISenderCallBack() {
            @Override
            public void onReceive(MsctResponse msctResponse) {
                DDLog.i(TAG, "home receive msg");
                if (!msctResponse.getMsctContext().hasOptionHeader()) {
                    dispatchHomeMessage(msctResponse);
                    return;
                }
                if (!msctResponse.getMsctContext().hasOptionHeader(Exoption.OPTION_METHOD) ||
                        !msctResponse.getMsctContext().hasOptionHeader(Exoption.OPTION_STATUS)) {
                    dispatchHomeMessage(msctResponse);
                    return;
                }
                String method = Utils.unSignByteToString(
                        msctResponse.getMsctContext().ShouldGetOptionHeader(Exoption.OPTION_METHOD));
                if (!"alive".equals(method)) {
                    dispatchHomeMessage(msctResponse);
                    return;
                }

                byte[] bStatus = Utils.unSignByteToByte(msctResponse.getMsctContext()
                        .ShouldGetOptionHeader(OPTION_STATUS));
                int status = Utils.byteArrayToUnInt(bStatus);
                if (status == MEMBER_EXSPIRED && currentHome != null) {
                    //  登录房间通讯
                    mHomeConnectionManager.loginHomeE2E(currentHome.getHomeID(), homeSenderCallBack, null);

                }
            }

            @Override
            public void onDisconnect(String s) {
                DDLog.i(TAG, "home onDisconnect:" + s);
                for (IHomeCallBack homeCallBack : homeCallBacks) {
                    homeCallBack.onDisconnect(s);
                }
                homeStatus = HOME_STATUS_DISCONNECT;
            }

            @Override
            public void onConnenct() {
                DDLog.i(TAG, "home onConnect");
                for (IHomeCallBack homeCallBack : homeCallBacks) {
                    homeCallBack.onConnect();
                }
                homeStatus = HOME_STATUS_CONNECTED;
            }

            @Override
            public void onReconnect() {
                DDLog.i(TAG, "home onReconnect");
                for (IHomeCallBack homeCallBack : homeCallBacks) {
                    homeCallBack.onConnect();
                }
            }
        };
    }

    protected abstract void dispatchHomeMessage(MsctResponse msctResponse);

    protected void onDefaultError(int code, String msg, IDefaultCallBack callback) {
        if (null != callback) {
            callback.onError(code, msg);
        }
    }

    protected void onDefaultError(int code, String msg, IDefaultCallBack2<?> callback) {
        if (null != callback) {
            callback.onError(code, msg);
        }
    }

    protected void onSwitchHome(Home home) {
        Map<String, Object> args = new HashMap<>();
        args.put("homeID", home.getHomeID());
        args.put("multipleSender", getMultipleSender());
        mDeviceManger.config(args);
        //  请求房间详细信息接口，获取涂鸦账号还有主机列表信息，然后登陆涂鸦，连接主机ws
        if (currentHome != null) {
            final Home lastHome = currentHome;
            currentHome = home;

            mHomeConnectionManager.logoutHomeE2E(lastHome.getHomeID(), new IDefaultCallBack() {
                @Override
                public void onSuccess() {
                    // 一定要logout成功后，再login，虽然你是顺讯调用logout login，但是okhttp不会顺序执行
                    // 登录房间通讯
                    mHomeConnectionManager.loginHomeE2E(home.getHomeID(), homeSenderCallBack, null);
                }

                @Override
                public void onError(int i, String s) {
                }
            });
            return;
        }
        currentHome = home;
        // 登录房间通讯
        mHomeConnectionManager.loginHomeE2E(home.getHomeID(), homeSenderCallBack, null);
    }

    private IMultipleSender getMultipleSender() {
        return mHomeConnectionManager.getMultipleSender();
    }

    protected void onCmdDisconnectHome() {
        if (currentHome != null) {
            // 断开上个房间的
            mHomeConnectionManager.logoutHomeE2E(currentHome.getHomeID(), null);
        }
    }

    protected void onCmdLogoutSuccess() {
        stopE2EConnection(false);
        currentHome = null;
    }

    @Override
    public void loginTuya(String countryCode, String uid, String pwd, boolean isRegister, IDefaultCallBack callBack) {

    }

    @Override
    public List<Device> getDevices() {
        return mDeviceManger.getDevices();
    }

    @Override
    public List<Device> fetchDevices() throws Exception {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            throw new Exception("getDevices is running UI Thread,please use new Thread to call getDevices");
        }
        return mDeviceManger.fetchDevice();
    }

    @Override
    public Device getDevice(String id) {
        return mDeviceManger.getDevice(id);
    }

    @Override
    public Device getDevice(String id, String sub) {
        return mDeviceManger.getDevice(id, sub);
    }

    @Override
    public List<Device> getDeviceByType(String sub) {
        return mDeviceManger.getDeviceByType(sub);
    }


    @Nullable
    @Override
    public List<Device> getCacheDeviceByType(String sub) {
        return mDeviceManger.getCacheDeviceByType(sub);
    }

    @Nullable
    @Override
    public List<Device> getLocalAndNewDeviceByType(String sub) {
        return mDeviceManger.getLocalAndNewDeviceByType(sub);
    }

    @Nullable
    @Override
    public List<Device> getAllDeviceByType(String sub) {
        return mDeviceManger.getAllDeviceByType(sub);
    }

    @Override
    public List<Device> getDeviceByType(String sub, boolean cacheFirst) {
        return mDeviceManger.getDeviceByType(sub, cacheFirst);
    }

    @Override
    public void removeDeviceCacheById(String sub) {
        mDeviceManger.removeDeviceCacheById(sub);
    }

    @Override
    public void removeDeviceCacheByIdAndSub(String id, String sub) {
        mDeviceManger.removeDeviceCacheByIdAndSub(id, sub);
    }

    @Override
    public void removeDeviceCacheByType(String sub) {

    }

    @Override
    public Device acquireTemporaryDevices(@NonNull String sub, String id, String model) {
        return mDeviceManger.acquireTemporaryDevices(sub, id, model);
    }

    @Override
    public BasePluginBinder createPluginBinder(Context context, String type) {
        return mDeviceManger.createPluginBinder(context, type);
    }

    @Override
    public boolean releaseDeviceByType(String sub) {
        return mDeviceManger.releaseDeviceByType(sub);
    }

    @Override
    public void registerDeviceListChangeListener(IDeviceListChangeListener listChangeListener) {
        mDeviceManger.registerDeviceListChangeListener(listChangeListener);
    }

    @Override
    public void unRegisterDeviceListChangeListener(IDeviceListChangeListener listChangeListener) {
        mDeviceManger.unRegisterDeviceListChangeListener(listChangeListener);
    }

    @Override
    public void addHomeStatusCallback(IHomeCallBack callBack) {
        if (homeCallBacks.contains(callBack)) {
            return;
        }
        homeCallBacks.add(callBack);
    }

    @Override
    public void removeHomeStatueCallback(IHomeCallBack callBack) {
        homeCallBacks.remove(callBack);
    }

    @Override
    public HomeInfo getCurrentHomeInfo() {
        return mCurrentHomeInfo;
    }

    @Override
    public Home getCurrentHome() {
        return currentHome;
    }

    @Override
    public int getHomeStatus() {
        return homeStatus;
    }

    @Override
    public void loginHomeE2E(String homeID, IDefaultCallBack2<E2EInfo> callBack) {
        DDLog.i(TAG, "loginHomeE2E");
        if (TextUtils.isEmpty(homeID)) {
            DDLog.e(TAG, "Empty homeId");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId");
            }
            return;
        }
        homeRepo.loginHomeE2E(homeID, new Callback<E2ELoginResponse>() {
            @Override
            public void onResponse(Call<E2ELoginResponse> call, Response<E2ELoginResponse> response) {
                E2ELoginResponse body = response.body();
                E2EInfo info = null;
                if (null != body) {
                    info = body.getResult();
                }
                if (callBack != null) {
                    if (null != info)
                        callBack.onSuccess(info);
                    else
                        callBack.onError(ErrorCode.DEFAULT, "Empty E2EInfo");
                }
            }

            @Override
            public void onFailure(Call<E2ELoginResponse> call, Throwable t) {
                DDLog.e("home", "loginHomeE2E onFailure:" + t.getMessage());
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void logoutHomeE2E(String homeID, IDefaultCallBack callBack) {
        DDLog.i(TAG, "logoutHomeE2E");
        if (TextUtils.isEmpty(homeID)) {
            DDLog.e(TAG, "Empty homeId");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId.");
            }
            return;
        }
        homeRepo.logoutHomeE2E(homeID, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (callBack != null) {
                    callBack.onSuccess();
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                DDLog.e("home", "logoutHomeE2E onFailure:" + t.getMessage());
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void stopE2EConnection(boolean needLogout) {
        EventBus.getDefault().post(new CommonCmdEvent(CommonCmdEvent.CMD.ON_BEFORE_HOME_DISCONNECT));
        releaseDeviceByType("dscam");
        releaseDeviceByType("HP5000");
        releaseDeviceByType("PC1-BAK15-HS10");
        releaseDeviceByType("PS1-BAK10-HS10");
        releaseDeviceByType("VB1-BAK5-HS10");
        releaseDeviceByType("PC3");
        DDLog.i(TAG, "stopE2EConnection");
        if (currentHome != null) {
            mHomeConnectionManager.releaseHomeConnect();
            // 如果已经退出登录了的，不能再调用该接口，否则会导致-12的错误
            if (needLogout) {
                mHomeConnectionManager.logoutHomeE2E(currentHome.getHomeID(), null);
            }
        }
    }

    @Override
    public void getMotionDetectionRecordList(String homeId, List<String> provider, long addtime, int pagesize, IDefaultCallBack2<IPCMotionDetectionRecordResponse> callBack) {
        homeRepo.getMotionDetectionRecordList(homeId, provider, addtime, pagesize, new Callback<IPCMotionDetectionRecordResponse>() {
            @Override
            public void onResponse(Call<IPCMotionDetectionRecordResponse> call, Response<IPCMotionDetectionRecordResponse> response) {
                if (callBack != null) {
                    if (response != null && response.body() != null) {
                        callBack.onSuccess(response.body());
                    } else {
                        callBack.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<IPCMotionDetectionRecordResponse> call, Throwable t) {
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void getTotalMotionRecordCount(String homeId, String event_id, IDefaultCallBack2<Integer> callBack) {
        homeRepo.getTotalMotionRecordCount(homeId, event_id, new Callback<IPCMotionEventRecordCountResponse>() {
            @Override
            public void onResponse(Call<IPCMotionEventRecordCountResponse> call, Response<IPCMotionEventRecordCountResponse> response) {
                if (callBack != null) {
                    if (response != null && response.body() != null && response.body().getResult() != null) {
                        int count = Integer.valueOf(TextUtils.isEmpty(response.body().getResult()) ? "0" : response.body().getResult());
                        callBack.onSuccess(count);
                    } else {
                        callBack.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<IPCMotionEventRecordCountResponse> call, Throwable t) {
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void listEventMotionRecords(String home_id, String event_id, int start_index, int page_size, IDefaultCallBack2<IPCEventMotionRecordsResponse> callBack) {
        homeRepo.listEventMotionRecords(home_id, event_id, start_index, page_size, new Callback<IPCEventMotionRecordsResponse>() {
            @Override
            public void onResponse(Call<IPCEventMotionRecordsResponse> call, Response<IPCEventMotionRecordsResponse> response) {
                if (callBack != null) {
                    if (response != null && response.body() != null) {
                        callBack.onSuccess(response.body());
                    } else {
                        callBack.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<IPCEventMotionRecordsResponse> call, Throwable t) {
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void deleteMotionDetectionRecord(String homeId, List<String> event_ids, IDefaultCallBack callBack) {
        homeRepo.deleteMotionDetectionRecord(homeId, event_ids, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (callBack != null) {
                    if (response != null && response.body() != null) {
                        callBack.onSuccess();
                    } else {
                        callBack.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void getMotionDetectionRecordVideoUrl(String homeId, String record_id, IDefaultCallBack2<String> callBack) {
        homeRepo.getMotionDetectionRecordVideoUrl(homeId, record_id, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (callBack != null) {
                    if (response != null && response.body() != null && !TextUtils.isEmpty(response.body().getResult())) {
                        callBack.onSuccess(response.body().getResult());
                    } else {
                        callBack.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void updateHomeMember(String homeId, String userId, int level, HomeMember homeMember, IDefaultCallBack callBack) {
        DDLog.i(TAG, "updateHomeMember");
        if (TextUtils.isEmpty(homeId) || TextUtils.isEmpty(userId)) {
            DDLog.e(TAG, "Empty homeId or userId");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId or userId");
            }
            return;
        }
        homeRepo.updateHomeMember(homeId, userId, level, homeMember, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (callBack != null) {
                    callBack.onSuccess();
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                DDLog.e("home", "updateHomeMember onFailure:" + t.getMessage());
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void setHomeNotificationLanguage(String homeID, String language, IDefaultCallBack callBack) {
        DDLog.i(TAG, "setHomeNotificationLanguage");
        if (TextUtils.isEmpty(homeID)
                || TextUtils.isEmpty(language)) {
            DDLog.e(TAG, "Empty homeId or language");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId or language.");
            }
            return;
        }
        homeRepo.setHomeNotificationLanguage(homeID, language, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (callBack != null) {
                    callBack.onSuccess();
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                DDLog.e("home", "setHomeNotificationLanguage onFailure:" + t.getMessage());
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void getIPCFirmwareVersion(IDefaultCallBack2<IPCFirmwareVersionResponse> callBack) {
        DDLog.i(TAG, "getIPCFirmwareVersion.");
        if (currentHome == null
                || TextUtils.isEmpty(currentHome.getHomeID())) {
            DDLog.e(TAG, "Empty currentHome or homeId");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty currentHome or homeId.");
            }
            return;
        }
        homeRepo.getIPCFirmwareVersion(currentHome.getHomeID(), new Callback<IPCFirmwareVersionResponse>() {
            @Override
            public void onResponse(Call<IPCFirmwareVersionResponse> call, Response<IPCFirmwareVersionResponse> response) {
                if (callBack != null) {
                    if (response != null && response.body() != null) {
                        callBack.onSuccess(response.body());
                    } else {
                        callBack.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<IPCFirmwareVersionResponse> call, Throwable t) {
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void getMotionEventDates(String homeId, String timezone, IDefaultCallBack2<MotionEventDatesResponse> callBack) {
        DDLog.i(TAG, "getMotionEventDates");
        if (TextUtils.isEmpty(homeId)
                || TextUtils.isEmpty(timezone)) {
            DDLog.e(TAG, "Empty homeId or timezone");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId or timezone.");
            }
            return;
        }
        homeRepo.getMotionEventDates(homeId, timezone, new Callback<MotionEventDatesResponse>() {
            @Override
            public void onResponse(Call<MotionEventDatesResponse> call, Response<MotionEventDatesResponse> response) {
                if (callBack != null) {
                    if (response.body() != null) {
                        callBack.onSuccess(response.body());
                    } else {
                        callBack.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<MotionEventDatesResponse> call, Throwable t) {
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void getMotionEventVideos(String homeId, String ipcId, String eventId, long eventStartTime, IDefaultCallBack2<MotionEventVideoResponse> callBack) {
        DDLog.i(TAG, "getMotionEventVideos");
        if (TextUtils.isEmpty(homeId)
                || TextUtils.isEmpty(eventId)) {
            DDLog.e(TAG, "Empty homeId or eventId");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId or eventId.");
            }
            return;
        }
        homeRepo.getMotionEventVideos(homeId, ipcId, eventId, eventStartTime, 20, new Callback<MotionEventVideoResponse>() {
            @Override
            public void onResponse(Call<MotionEventVideoResponse> call, Response<MotionEventVideoResponse> response) {
                if (callBack != null) {
                    if (response.body() != null) {
                        callBack.onSuccess(response.body());
                    } else {
                        callBack.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<MotionEventVideoResponse> call, Throwable t) {
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public Call<?> getMotionEvents(String homeId, List<String> ipcIds, long startTime, long endTime, IDefaultCallBack2<MotionEventResponse> callBack) {
        DDLog.i(TAG, "getMotionEvents");
        if (TextUtils.isEmpty(homeId)
                || null == ipcIds || 0 == ipcIds.size()) {
            DDLog.e(TAG, "Empty homeId or ipcIds");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId or ipcIds.");
            }
            return null;
        }

        return homeRepo.getMotionEvents(homeId, ipcIds, startTime, endTime, new Callback<MotionEventResponse>() {
            @Override
            public void onResponse(Call<MotionEventResponse> call, Response<MotionEventResponse> response) {
                if (callBack != null) {
                    if (response.body() != null) {
                        callBack.onSuccess(response.body());
                    } else {
                        callBack.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<MotionEventResponse> call, Throwable t) {
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void getLastMotionIpcList(String homeId, long startTime, long endTime, IDefaultCallBack2<LastMotionIpcListResponse> callBack) {
        DDLog.i(TAG, "getLastMotionIpcList");
        if (TextUtils.isEmpty(homeId)) {
            DDLog.e(TAG, "Empty homeId");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId.");
            }
            return;
        }
        homeRepo.getLastMotionIpcList(homeId, startTime, endTime, new Callback<LastMotionIpcListResponse>() {
            @Override
            public void onResponse(Call<LastMotionIpcListResponse> call, Response<LastMotionIpcListResponse> response) {
                if (callBack != null) {
                    if (response.body() != null) {
                        callBack.onSuccess(response.body());
                    } else {
                        callBack.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<LastMotionIpcListResponse> call, Throwable t) {
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void getMotionEventCover(String homeId, List<String> eventIds, IDefaultCallBack2<MotionEventCoverResponse> callBack) {
        DDLog.i(TAG, "getMotionEventCover");
        if (TextUtils.isEmpty(homeId) || null == eventIds || eventIds.size() == 0) {
            DDLog.e(TAG, "Empty params");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty params.");
            }
            return;
        }
        homeRepo.getMotionEventCover(homeId, eventIds, new Callback<MotionEventCoverResponse>() {
            @Override
            public void onResponse(Call<MotionEventCoverResponse> call, Response<MotionEventCoverResponse> response) {
                if (callBack != null) {
                    if (response.body() != null) {
                        callBack.onSuccess(response.body());
                    } else {
                        callBack.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<MotionEventCoverResponse> call, Throwable t) {
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void getWidgetCount(String homeID, String panelId, List<String> ipcProviders, List<String> sTypeList, IDefaultCallBack2<GetWidgetCountResponse> callBack) {
        DDLog.i(TAG, "getWidgetCount");
        if (TextUtils.isEmpty(homeID)) {
            DDLog.e(TAG, "Empty homeId.");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId.");
            }
            return;
        }

        homeRepo.getWidgetCount(homeID, panelId, ipcProviders, sTypeList, new Callback<GetWidgetCountResponse>() {
            @Override
            public void onResponse(Call<GetWidgetCountResponse> call, Response<GetWidgetCountResponse> response) {
                if (callBack != null) {
                    if (response.body() != null) {
                        callBack.onSuccess(response.body());
                    } else {
                        callBack.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<GetWidgetCountResponse> call, Throwable t) {
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void getKeypadMemberPwdInfo(String homeId, String panelId, String userId, IDefaultCallBack2<KeypadMemberPwdInfoGetResponse> callback) {
        DDLog.i(TAG, "getKeypadMemberPwdInfo");
        if (TextUtils.isEmpty(homeId) || TextUtils.isEmpty(panelId) || TextUtils.isEmpty(userId)) {
            DDLog.e(TAG, "Empty homeId or panelId or userId");
            if (null != callback) {
                callback.onError(ErrorCode.PARAM_ERROR, "Empty homeId or panelId or userId");
            }
            return;
        }

        homeRepo.getKeypadMemberPwdInfo(homeId, panelId, userId, new Callback<KeypadMemberPwdInfoGetResponse>() {
            @Override
            public void onResponse(Call<KeypadMemberPwdInfoGetResponse> call, Response<KeypadMemberPwdInfoGetResponse> response) {
                if (callback != null) {
                    if (response.body() != null) {
                        callback.onSuccess(response.body());
                    } else {
                        callback.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<KeypadMemberPwdInfoGetResponse> call, Throwable t) {
                t.printStackTrace();
                if (callback != null) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callback.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void updateKeypadMemberPwdInfo(String homeId, String panelId, String userId, boolean enabled, IDefaultCallBack2<KeypadMemberPwdUpdateResponse> callback) {
        DDLog.i(TAG, "updateKeypadMemberPwdInfo");
        if (TextUtils.isEmpty(homeId) || TextUtils.isEmpty(panelId) || TextUtils.isEmpty(userId)) {
            DDLog.e(TAG, "Empty homeId or panelId or userId");
            if (null != callback) {
                callback.onError(ErrorCode.PARAM_ERROR, "Empty homeId or panelId or userId");
            }
            return;
        }

        homeRepo.updateKeypadMemberPwdInfo(homeId, panelId, userId, enabled, new Callback<KeypadMemberPwdUpdateResponse>() {
            @Override
            public void onResponse(Call<KeypadMemberPwdUpdateResponse> call, Response<KeypadMemberPwdUpdateResponse> response) {
                if (callback != null) {
                    if (response.body() != null) {
                        callback.onSuccess(response.body());
                    } else {
                        callback.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<KeypadMemberPwdUpdateResponse> call, Throwable t) {
                t.printStackTrace();
                if (callback != null) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callback.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void resetKeypadMemberPwdInfo(String homeId, String panelId, String userId, IDefaultCallBack2<KeypadMemberPwdResetResponse> callback) {
        DDLog.i(TAG, "resetKeypadMemberPwdInfo");
        if (TextUtils.isEmpty(homeId) || TextUtils.isEmpty(panelId) || TextUtils.isEmpty(userId)) {
            DDLog.e(TAG, "Empty homeId or panelId or userId");
            if (null != callback) {
                callback.onError(ErrorCode.PARAM_ERROR, "Empty homeId or panelId or userId");
            }
            return;
        }

        homeRepo.resetKeypadMemberPwdInfo(homeId, panelId, userId, new Callback<KeypadMemberPwdResetResponse>() {
            @Override
            public void onResponse(Call<KeypadMemberPwdResetResponse> call, Response<KeypadMemberPwdResetResponse> response) {
                if (callback != null) {
                    if (response.body() != null) {
                        callback.onSuccess(response.body());
                    } else {
                        callback.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<KeypadMemberPwdResetResponse> call, Throwable t) {
                t.printStackTrace();
                if (callback != null) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callback.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }


    protected void onBeforeSwitchHome(@NonNull final String targetHomeId) {
        final boolean changedHome = null == currentHome
                || TextUtils.isEmpty(currentHome.getHomeID())
                || !currentHome.getHomeID().equals(targetHomeId);
        if (changedHome) {
            final JSONObject args = new JSONObject();
            try {
                args.put("newHomeId", targetHomeId);
            } catch (Exception e) {
                MsctLog.v(TAG, "onBeforeSwitchHome, put new home's id error");
            }
            CommonCmdEvent event = new CommonCmdEvent(CommonCmdEvent.CMD.ON_BEFORE_HOME_SWITCH);
            event.setExtra(args.toString());
            EventBus.getDefault().post(event);
        }
    }

    /**
     * 家庭连接管理类
     */
    final class HomeConnectionManager implements IMultipleSenderCallBack {
        private final byte[] mRootLock = new byte[0];
        private final byte[] mHomeLock = new byte[0];
        private IMultipleSender mRootSender;
        private IMsctSender mHomeSender;

        void initLocalMultipleSender() {
            new Thread(() -> {
                synchronized (mRootLock) {
                    mRootSender = new MultipleSender(
                            DinSDK.getInstance().getE2eDomain(),
                            DinSDK.getInstance().getPort());
                    mRootSender.addStatusListener(HomeConnectionManager.this);
                    mRootSender.connect();
                }
            }).start();
        }

        void releaseHomeConnect() {
            synchronized (mHomeLock) {
                if (mHomeSender != null) {
                    // 断开上一次连接房间，清除资源，重新建立新的连接
                    mHomeSender.disconnect();
                    mHomeSender.release();
                    mRootSender.removeMsct(mHomeSender.getEnd_id());
                    mHomeSender = null;
                }
            }
        }

        void createHomeConnect(E2EInfo e2EInfo, ISenderCallBack homeSenderCallBack) {
            releaseHomeConnect();
            synchronized (mHomeLock) {
                mHomeSender = mRootSender.createHeartBitMsct(
                        "", e2EInfo.getGroup_id(),
                        e2EInfo.getChat_secret(),
                        e2EInfo.getEnd_id(),
                        e2EInfo.getEnd_secret());
                mHomeSender.setNickName("Home");
                mHomeSender.addSenderCallBack(homeSenderCallBack);
                mHomeSender.connect();
            }
        }

        IMultipleSender getMultipleSender() {
            synchronized (mRootLock) {
                return mRootSender;
            }
        }

        void logoutHomeE2E(final String homeID, IDefaultCallBack callBack) {
            releaseHomeConnect();
            BaseHome.this.logoutHomeE2E(homeID, callBack);
        }

        void loginHomeE2E(final String homeID, final ISenderCallBack homeSenderCallBack, IDefaultCallBack2<E2EInfo> callBack) {
            BaseHome.this.loginHomeE2E(homeID, new IDefaultCallBack2<E2EInfo>() {
                @Override
                public void onSuccess(E2EInfo e2EInfo) {
                    final String tempHost = e2EInfo.getHost();
                    final String newHost;
                    int newPort;
                    if (TextUtils.isEmpty(tempHost)) {
                        newHost = DinSDK.getInstance().getE2eDomain();
                        newPort = DinSDK.getInstance().getPort();
                    } else if (tempHost.contains(":")) {
                        String[] hostPort = tempHost.split(":");
                        newHost = hostPort[0];
                        try {
                            newPort = Integer.parseInt(hostPort[1]);
                            DinSDK.getInstance().setE2eDomain(newHost);
                            DinSDK.getInstance().setPort(newPort);
                        } catch (Exception e) {
                            MsctLog.e(TAG, "Error on parse port");
                            e.printStackTrace();
                            newPort = DinSDK.getInstance().getPort();
                        }
                    } else {
                        newHost = tempHost;
                        newPort = DinSDK.getInstance().getPort();
                    }
                    // 更新P2P地址-只有更新了p2p地址，才能进行p2p的通讯
                    final String utilHost = e2EInfo.getUtil_host();
                    String p2pHost = "";
                    int p2pPort = 2051;
                    if (!TextUtils.isEmpty(utilHost) && utilHost.contains(":")) {
                        String[] utilHostPort = utilHost.split(":");
                        p2pHost = utilHostPort[0];
                        try {
                            p2pPort = Integer.parseInt(utilHostPort[1]);
                        } catch (Exception e) {
                            MsctLog.e(TAG, "Error on parse util port");
                        }
                    }
                    // 更新为服务器返回p2p地址-空表示不支持p2p
                    DinSDK.getInstance().setE2eHelpDomain(p2pHost);
                    DinSDK.getInstance().setE2eHelpPort(p2pPort);
                    MsctLog.w(TAG, "更新p2p地址为: " + p2pHost + ":" + p2pPort);

                    // 通知服务器地址改变
                    final int newServerPort = newPort;
                    mRootSender.updateRemoteIpPort(newHost, newServerPort,
                            inetSocketAddress -> {
                                MsctLog.w("RemoteAddressUpdated", "成功更新通讯地址为: " + newHost);
                                createHomeConnect(e2EInfo, homeSenderCallBack);
                                if (null != callBack) {
                                    callBack.onSuccess(e2EInfo);
                                }
                                CommonCmdEvent cmdEvent = new CommonCmdEvent(CommonCmdEvent.CMD.REMOTE_ADDRESS_UPDATED);
                                try {
                                    JSONObject args = new JSONObject();
                                    args.put("home_id", homeID);
                                    args.put("e2e_port", newServerPort);
                                    args.put("e2e_domain", newHost);
                                    cmdEvent.setExtra(args.toString());
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                EventBus.getDefault().post(cmdEvent);
                            });
                }

                @Override
                public void onError(int i, String s) {
                    DDLog.e(TAG, "loginhome e2e fail");
                    if (null != callBack) {
                        callBack.onError(i, s);
                    }
                }
            });
        }

        @Override
        public void onDisconnect(String s) {
            MsctLog.e(TAG, "onDisconnect!!!!!!!!" + s);

        }

        @Override
        public void onConnenct() {
            MsctLog.i(TAG, "onConnenct!!!!!!!!");
        }

        @Override
        public void onReconnect() {
            MsctLog.i(TAG, "onReconnect!!!!!!!!");
        }
    }

    @Override
    public void getFamilyBalanceContractInfo(String homeId, IDefaultCallBack2<FamilyBalanceContractInfoResponse.ResultBean> callBack) {
        if (TextUtils.isEmpty(homeId)) {
            if (callBack != null) {
                callBack.onError(DEFAULT, "getFamilyBalanceContractInfo params null");
            }
            return;
        }

        HomeApi.getInstance().getFamilyBalanceContractInfo(homeId).enqueue(new Callback<FamilyBalanceContractInfoResponse>() {
            @Override
            public void onResponse(Call<FamilyBalanceContractInfoResponse> call, Response<FamilyBalanceContractInfoResponse> response) {
                FamilyBalanceContractInfoResponse infoResponse = response.body();
                if (response.isSuccessful()) {
                    if (null != infoResponse && null != infoResponse.getResult()) {
                        if (callBack != null) {
                            callBack.onSuccess(infoResponse.getResult());
                        }
                    }
                } else {
                    if (callBack != null) {
                        callBack.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<FamilyBalanceContractInfoResponse> call, Throwable t) {
                if (null != callBack) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), t.getMessage());
                        return;
                    }

                    callBack.onError(DEFAULT, "Unknown Error: " + t.getMessage());
                }
            }
        });
    }
    @Override
    public void saveFamilyBalanceContractInfo(String homeId, FamilyBalanceContract data, IDefaultCallBack callBack) {
        if (TextUtils.isEmpty(homeId) || null == data) {
            if (callBack != null) {
                callBack.onError(DEFAULT, "saveFamilyBalanceContractInfo params null");
            }
            return;
        }

        HomeApi.getInstance().saveFamilyBalanceContractInfo(homeId, data).enqueue(new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (response.isSuccessful()) {
                    if (callBack != null) {
                        callBack.onSuccess();
                    }
                } else {
                    if (callBack != null) {
                        callBack.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                if (null != callBack) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), t.getMessage());
                        return;
                    }

                    callBack.onError(DEFAULT, "Unknown Error: " + t.getMessage());
                }
            }
        });
    }

    @Override
    public void getUploadImageKey(String imagePath, String pathKey, IDefaultCallBack2<String> callBack) {
        if (TextUtils.isEmpty(imagePath)
                || TextUtils.isEmpty(pathKey)
                || !(new File(imagePath).exists())) {
            if (callBack != null) {
                callBack.onError(DEFAULT, "imagePath params error");
            }
            return;
        }
        if (FileUploadService.getInstance().getUploadMode() == FileUploadService.UploadMode.SOURCE_CLOUDFLARE) {
            // 上传到Cloudflare
            FileUploadService.getInstance().uploadFile(imagePath, currentHome.getHomeID(), 4, new IResultCallback2<String>() {
                @Override
                public void onError(int i, String s) {
                    callBack.onError(i, s);
                }

                @Override
                public void onSuccess(String key) {
                    if (null != callBack) {
                        callBack.onSuccess(key);
                    }
                }
            });
        } else {
            // 上传到七牛
            // 暂先使用上传头像获取token的方法
            Api.getApi().getUploadToken().enqueue(new Callback<StringResponseEntry>() {
                @Override
                public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                    StringResponseEntry responseEntry = response.body();
                    if (null != responseEntry) {
                        String token = responseEntry.getResult();
                        if (!TextUtils.isEmpty(token)) {
                            uploadImageByQiuNiu(imagePath, pathKey, token, new IResultCallback2<String>() {
                                @Override
                                public void onError(int i, String s) {
                                    callBack.onError(i, s);
                                }

                                @Override
                                public void onSuccess(String s) {
                                    DDLog.e(TAG, "getUploadImageKey, onSuccess.");
                                    callBack.onSuccess(s);
                                }
                            });
                        }
                    }
                }

                @Override
                public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                    DDLog.e(TAG, "Error on: getBmtUploadToken  " + t.getMessage());
                    callBack.onError(-1, "network error");
                }
            });
        }
    }

    private void uploadImageByQiuNiu(final String imageFilePath, final String path, final String token, final IResultCallback2<String> callback) {
        // 生成随机字符串
        final String CHAR_LOWER = "abcdefghijklmnopqrstuvwxyz";
        final String DEFAULT_PASSWORD_CHARS = CHAR_LOWER + System.currentTimeMillis();

        StringBuilder builder = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 30; i++) {
            int index = random.nextInt(DEFAULT_PASSWORD_CHARS.length());
            builder.append(DEFAULT_PASSWORD_CHARS.charAt(index));
        }
        final String pathKey = path + builder;

        HomeApi.getInstance().uploadSignByQiNiu(token, imageFilePath, pathKey,
                (String key, ResponseInfo info, JSONObject response) -> {
                    DDLog.i(TAG, "Upload image success by qiu niu.");
                    if (info.isOK()) {
                        try {
                            final String imageKey = response.getString("key");
                            if (null != callback) {
                                callback.onSuccess(imageKey);
                            }
                        } catch (JSONException e) {
                            DDLog.e(TAG, "Can't get image's key");
                            e.printStackTrace();

                            if (null != callback) {
                                callback.onError(DEFAULT, "Can't get image's key");
                            }
                        }
                    } else {
                        DDLog.e(TAG, "Upload image by qiu niu error.");
                        if (null != callback) {
                            callback.onError(DEFAULT, "Upload image by qiu niu error.");
                        }
                    }
                });
    }

    @Override
    public void getBmtElectricitySupplier(String countryCode, IDefaultCallBack2<List<ElectricitySupplierBean>> callback) {
        if (TextUtils.isEmpty(countryCode)) {
            if (callback != null) {
                callback.onError(DEFAULT, "imagePath params error");
            }
            return;
        }

        if (TextUtils.isEmpty(countryCode)) {
            if (callback != null) {
                callback.onError(DEFAULT, "countryCode params error");
            }
            return;
        }
        HomeApi.getInstance().getBmtRegionElectricitySupplier(countryCode).enqueue(new Callback<RegionElectricitySupplierResponse>() {
            @Override
            public void onResponse(Call<RegionElectricitySupplierResponse> call, Response<RegionElectricitySupplierResponse> response) {
                RegionElectricitySupplierResponse body = response.body();
                if (response.isSuccessful() && null != body && body.getResult() != null) {
                    callback.onSuccess(body.getResult().getElectricity_supplier());
                } else {
                    callback.onError(-1, "fail or body is null");
                }
            }

            @Override
            public void onFailure(Call<RegionElectricitySupplierResponse> call, Throwable t) {
                callback.onError(-1, "fail");
            }
        });
    }

    @Override
    public void getBmtRegionCountries(IDefaultCallBack2<List<CountryBean>> callback) {
        HomeApi.getInstance().getCountryList().enqueue(new Callback<RegionCountriesResponse>() {
            @Override
            public void onResponse(Call<RegionCountriesResponse> call, Response<RegionCountriesResponse> response) {
                RegionCountriesResponse body = response.body();
                if (response.isSuccessful() && body.getResult() != null) {
                    callback.onSuccess(body.getResult());
                } else {
                    callback.onError(-1, "fail or body is null");
                }
            }

            @Override
            public void onFailure(Call<RegionCountriesResponse> call, Throwable t) {
                t.printStackTrace();
                if (callback != null) {
                    callback.onError(-1, "unknown error");
                }
            }
        });
    }
}
