package com.dinsafer.module_home;

import static com.dinsafer.dincore.common.ErrorCode.DEFAULT;

import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.Keep;

import com.alibaba.fastjson.JSON;
import com.dinsafer.dincore.common.CommonCmdEvent;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dincore.http.NetWorkException;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.user.api.ILogoutCallback;
import com.dinsafer.dincore.user.bean.LogoutEvent;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dssupport.msctlib.msct.MsctResponse;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_home.api.IHomeCallBack;
import com.dinsafer.module_home.api.IHomeListCallBack;
import com.dinsafer.module_home.bean.AddContactParams;
import com.dinsafer.module_home.bean.BalanceContractRecordsResponse;
import com.dinsafer.module_home.bean.BalanceContractSignTemplateResponse;
import com.dinsafer.module_home.bean.BalanceContractUnsignTemplateResponse;
import com.dinsafer.module_home.bean.BmtBalanceContractParticipationHoursResponse;
import com.dinsafer.module_home.bean.BmtIsTaskTimeInUpdatedRangeResponse;
import com.dinsafer.module_home.bean.CreateHomeResponse;
import com.dinsafer.module_home.bean.DailyMemoriesVideoResponse;
import com.dinsafer.module_home.bean.EventListByFilterEntry;
import com.dinsafer.module_home.bean.EventListEntry;
import com.dinsafer.module_home.bean.Home;
import com.dinsafer.module_home.bean.HomeConstants;
import com.dinsafer.module_home.bean.HomeContact;
import com.dinsafer.module_home.bean.HomeContactResponse;
import com.dinsafer.module_home.bean.HomeInfo;
import com.dinsafer.module_home.bean.HomeInfoResponse;
import com.dinsafer.module_home.bean.HomeListResponse;
import com.dinsafer.module_home.bean.HomeLocationResponse;
import com.dinsafer.module_home.bean.HomeMember;
import com.dinsafer.module_home.bean.HomeMemberResponse;
import com.dinsafer.module_home.bean.InitHomeCompatParams;
import com.dinsafer.module_home.bean.IsOnlyAdminResponse;
import com.dinsafer.module_home.bean.JoinHomeResponse;
import com.dinsafer.module_home.bean.MemberAvatars;
import com.dinsafer.module_home.bean.MemberAvatarsResponse;
import com.dinsafer.module_home.bean.ParticipationHour;
import com.dinsafer.module_home.bean.SingleBalanceContractRecordResponse;
import com.dinsafer.module_home.bean.StatusResponse;
import com.dinsafer.module_home.http.HomeApi;

import org.greenrobot.eventbus.EventBus;
import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;


/**
 * 完整独立的家庭管理
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/3/22 4:52 下午
 */
class CommonHome extends BaseHome {
    private static final String ERROR_INFO = "Unsupported API";
    private static final String ERROR_INFO_EXT = ": not support on common mode!";

    private final List<Home> homeList;

    public CommonHome() {
        super();
        homeList = new ArrayList<>();
    }

    @Override
    protected void onCmdLogoutSuccess() {
        super.onCmdLogoutSuccess();
        if (null != homeList) {
            homeList.clear();
        }
    }

    @Override
    protected void dispatchHomeMessage(MsctResponse msctResponse) {
        try {
            JSONObject msgObj = new JSONObject((String) msctResponse.getMsctContext().getDecodedPayload());
            final String cmd = DDJSONUtil.getString(msgObj, "cmd");
            if (!TextUtils.isEmpty(cmd)) {
                switch (cmd) {
                    case HomeConstants.CMD.AUTHORITY_UPDATED:
                        String homeId = DDJSONUtil.getString(msgObj, "home_id");
                        int level = DDJSONUtil.getInt(msgObj, "level");
                        if (null != homeList && 0 < homeList.size()) {
                            for (int i = 0; i < homeList.size(); i++) {
                                if (!TextUtils.isEmpty(homeList.get(i).getHomeID())
                                        && homeList.get(i).getHomeID().equals(homeId)) {
                                    homeList.get(i).setLevel(level);
                                    break;
                                }
                            }
                        }
                        if (null != currentHome && currentHome.getHomeID().equals(homeId)) {
                            currentHome.setLevel(level);
                            if (null != mCurrentHomeInfo) {
                                mCurrentHomeInfo.setLevel(level);
                            }
                        }
                        break;
                    case HomeConstants.CMD.MEMBER_DELETED:
                        homeId = DDJSONUtil.getString(msgObj, "home_id");
                        if (null != homeList && 0 < homeList.size()) {
                            for (int i = 0; i < homeList.size(); i++) {
                                if (!TextUtils.isEmpty(homeList.get(i).getHomeID())
                                        && homeList.get(i).getHomeID().equals(homeId)) {
                                    homeList.remove(i);
                                    break;
                                }
                            }
                        }
                        if (null != currentHome
                                && !TextUtils.isEmpty(currentHome.getHomeID())
                                && currentHome.getHomeID().equals(homeId)) {
                            DDLog.i(TAG, "当前用户被从房间移除了");
                            mHomeConnectionManager.releaseHomeConnect();
                            currentHome = null;
                            mCurrentHomeInfo = null;
                        }
                        break;
                    case HomeConstants.CMD.DEVICE_DELETED:
                        homeId = DDJSONUtil.getString(msgObj, "home_id");
                        String operatorUserId = DDJSONUtil.getString(msgObj, "userid");
                        if (null != mCurrentHomeInfo
                                && !TextUtils.isEmpty(mCurrentHomeInfo.getHomeId())
                                && mCurrentHomeInfo.getHomeId().equals(homeId)
                                && null != mCurrentHomeInfo.getDevice()) {

                            mCurrentHomeInfo.getDevice().setDeviceid("");
                            mCurrentHomeInfo.getDevice().setToken("");
                            mCurrentHomeInfo.getDevice().setName("");
                            if (!TextUtils.isEmpty(operatorUserId)
                                    && null != DinSDK.getUserInstance().getUser()
                                    && operatorUserId.equals(DinSDK.getUserInstance().getUser().getUser_id())) {
                                DDLog.i(TAG, "Delete offline panel by self!!!");
                                break;
                            }

                            CommonCmdEvent event = new CommonCmdEvent(CommonCmdEvent.CMD.DELETE_OFFLINE_PANEL);
                            try {
                                JSONObject args = new JSONObject();
                                args.put("home_id", homeId);
                                event.setExtra(args.toString());
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            EventBus.getDefault().post(event);
                        }
                        break;
                    case HomeConstants.CMD.FORCE_LOGOUT:
                        DinSDK.getUserInstance().logout(new ILogoutCallback() {
                            @Override
                            public void onSuccess() {
                                EventBus.getDefault().post(new LogoutEvent(false));
                            }
                        });
                        break;
                    case CommonCmdEvent.CMD.DSCAM_ADD:
                    case CommonCmdEvent.CMD.DSCAM_DELETE:
                    case CommonCmdEvent.CMD.DSCAM_RENAME:
                    case CommonCmdEvent.CMD.DSDOORBELL_ADD:
                    case CommonCmdEvent.CMD.DSDOORBELL_DELETE:
                    case CommonCmdEvent.CMD.DSDOORBELL_RENAME:
                    case CommonCmdEvent.CMD.BMT_ADD:
                    case CommonCmdEvent.CMD.BMT_DELETE:
                    case CommonCmdEvent.CMD.BMT_RENAME:
                    case CommonCmdEvent.CMD.UPDATE_REGION:
                        CommonCmdEvent event = new CommonCmdEvent(cmd);
                        msgObj.put("homeID", currentHome.getHomeID());
                        event.setExtra(msgObj.toString());
                        EventBus.getDefault().post(event);
                        break;
                    default:
                        DDLog.e(TAG, "Unhandled cmd: " + cmd);
                        break;
                }
            }
        } catch (Exception e) {
            DDLog.e(TAG, "Error on process message from home msct!!!");
            e.printStackTrace();
        }
        for (IHomeCallBack homeCallBack : homeCallBacks) {
            homeCallBack.onMessage((String) msctResponse.getMsctContext().getDecodedPayload());
        }
    }

    @Keep
    @Override
    public void createHome(String homeName, String language, IDefaultCallBack2<Home> callback) {
        homeRepo.createHome(homeName, language, new Callback<CreateHomeResponse>() {
            @Override
            public void onResponse(Call<CreateHomeResponse> call, Response<CreateHomeResponse> response) {
                CreateHomeResponse response1 = response.body();
                Home home = new Home(response1.getResult().getHome_id(), homeName);
                home.setLevel(30);
                homeList.add(home);
                if (callback != null) {
                    callback.onSuccess(home);
                }
            }

            @Override
            public void onFailure(Call<CreateHomeResponse> call, Throwable t) {
                if (callback != null) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callback.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Keep
    @Override
    public void reNameHome(String homeID, String homeName, IDefaultCallBack callBack) {
        if (TextUtils.isEmpty(homeID)
                || TextUtils.isEmpty(homeName)) {
            DDLog.e(TAG, "Empty homeId or homeName");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId or homeName.");
            }
            return;
        }
        homeRepo.reNameHome(homeID, homeName, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                for (Home home : homeList) {
                    if (home.getHomeID().equals(homeID)) {
                        home.setHomeName(homeName);
                        break;
                    }
                }
                if (callBack != null) {
                    callBack.onSuccess();
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Keep
    @Override
    public void queryHomeList(IHomeListCallBack callback) {
        homeRepo.queryHomeList(new Callback<HomeListResponse>() {
            @Override
            public void onResponse(Call<HomeListResponse> call, Response<HomeListResponse> response) {
                HomeListResponse response1 = response.body();
                homeList.clear();
                DDLog.i("home", "gethome list:" + response1.getResult().size());
                for (HomeListResponse.ResultBean resultBean : response1.getResult()) {
                    Home home = new Home(resultBean.getHome_id(), resultBean.getHome_name());
                    home.setLevel(resultBean.getLevel());
                    homeList.add(home);
                }
                if (callback != null) {
                    callback.onSuccess(homeList);
                }
            }

            @Override
            public void onFailure(Call<HomeListResponse> call, Throwable t) {
                DDLog.i("home", "gethome list onFailure:" + t.getMessage());
                t.printStackTrace();
                if (callback != null) {
                    if (homeList != null) {
                        callback.onSuccess(homeList);
                    } else {
                        if (t instanceof NetWorkException) {
                            callback.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                        } else {
                            callback.onError(ErrorCode.DEFAULT, t.getMessage());
                        }
                    }
                }

            }
        });
    }

    @Keep
    @Override
    public void removeHome(String homeID, IDefaultCallBack callBack) {
        if (TextUtils.isEmpty(homeID)) {
            DDLog.e(TAG, "Empty homeId");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId");
            }
            return;
        }
        homeRepo.removeHome(homeID, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (callBack != null) {
                    callBack.onSuccess();
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                DDLog.e("home", "remove home onFailure:" + t.getMessage());
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Keep
    @Override
    public void queryHomeMemberList(String homeID, IDefaultCallBack2<List<HomeMember>> callBack) {
        DDLog.i(TAG, "queryHomeMemberList");
        if (TextUtils.isEmpty(homeID)) {
            DDLog.e(TAG, "Empty homeId");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId");
            }
            return;
        }
        homeRepo.queryHomeMemberList(homeID, new Callback<HomeMemberResponse>() {
            @Override
            public void onResponse(Call<HomeMemberResponse> call, Response<HomeMemberResponse> response) {
                HomeMemberResponse response1 = response.body();
                List<HomeMember> homeMembers = new ArrayList<>();
                if (null != response1 &&
                        null != response1.getResult()
                        && 0 < response1.getResult().size()) {
                    homeMembers.addAll(response1.getResult());
                    DDLog.i("home", "gethome member list:" + homeMembers.size());
                }
                if (callBack != null) {
                    callBack.onSuccess(homeMembers);
                }
            }

            @Override
            public void onFailure(Call<HomeMemberResponse> call, Throwable t) {
                DDLog.e("home", "gethome member list onFailure:" + t.getMessage());
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Keep
    @Override
    public void newHomeContact(String homeId, @NotNull AddContactParams params, IDefaultCallBack callBack) {
        DDLog.i(TAG, "newHomeContact");
        if (TextUtils.isEmpty(homeId)
                || null == params.getContacts()
                || 0 >= params.getContacts().size()) {
            DDLog.e(TAG, "Empty homeId or contacts");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId or contacts");
            }
            return;
        }
        homeRepo.newHomeContact(homeId, params, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (callBack != null) {
                    callBack.onSuccess();
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                DDLog.e("home", "newHomeContact onFailure:" + t.getMessage());
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Keep
    @Override
    public void listHomeContact(String homeId, IDefaultCallBack2<List<HomeContact>> callBack) {
        DDLog.i(TAG, "listHomeContact");
        if (TextUtils.isEmpty(homeId)) {
            DDLog.e(TAG, "Empty homeId");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId.");
            }
            return;
        }
        homeRepo.listHomeContact(homeId, new Callback<HomeContactResponse>() {
            @Override
            public void onResponse(Call<HomeContactResponse> call, Response<HomeContactResponse> response) {
                HomeContactResponse response1 = response.body();
                List<HomeContact> homeContacts = new ArrayList<>();
                if (null != response1 &&
                        null != response1.getResult()
                        && 0 < response1.getResult().size()) {
                    homeContacts.addAll(response1.getResult());
                    DDLog.i("home", "gethome contact list:" + homeContacts.size());
                }
                if (callBack != null) {
                    callBack.onSuccess(homeContacts);
                }
            }

            @Override
            public void onFailure(Call<HomeContactResponse> call, Throwable t) {
                DDLog.e("home", "listHomeContact onFailure:" + t.getMessage());
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Keep
    @Override
    public void removeHomeContact(String homeId, String contactId, IDefaultCallBack callBack) {
        DDLog.i(TAG, "removeHomeContact");
        if (TextUtils.isEmpty(homeId) || TextUtils.isEmpty(contactId)) {
            DDLog.e(TAG, "Empty homeId or contactId");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId or contactId");
            }
            return;
        }
        homeRepo.removeHomeContact(homeId, contactId, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (callBack != null) {
                    callBack.onSuccess();
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                DDLog.e("home", "removeHomeContact onFailure:" + t.getMessage());
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Keep
    @Override
    public void updateHomeContact(String homeId, @NotNull HomeContact param, IDefaultCallBack callBack) {
        DDLog.i(TAG, "updateHomeContact");
        if (TextUtils.isEmpty(homeId) || TextUtils.isEmpty(param.getContact_id())) {
            DDLog.e(TAG, "Empty homeId or contactId");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId or contactId");
            }
            return;
        }
        homeRepo.updateHomeContact(homeId, param, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (callBack != null) {
                    callBack.onSuccess();
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                DDLog.e("home", "updateHomeContact onFailure:" + t.getMessage());
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Keep
    @Override
    public void getInvitationFamilyMemberCode(String homeID, int level, IDefaultCallBack2<String> callBack) {
        homeRepo.getInvitationFamilyCode(homeID, level, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                StringResponseEntry response1 = response.body();
                if (callBack != null) {
                    callBack.onSuccess(response1.getResult());
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });

    }

    @Keep
    @Override
    public void verifyInvitationFamilyMemberCode(String code, IDefaultCallBack2<Home> callBack) {
        homeRepo.verifyInvitationFamilyMemberCode(code, new Callback<JoinHomeResponse>() {
            @Override
            public void onResponse(Call<JoinHomeResponse> call, Response<JoinHomeResponse> response) {
                JoinHomeResponse response1 = response.body();
                Home home = new Home(response1.getResult().getHome_id()
                        , response1.getResult().getHome_name());
                home.setLevel(response1.getResult().getLevel());
                homeList.add(home);
                if (callBack != null) {
                    callBack.onSuccess(home);
                }
            }

            @Override
            public void onFailure(Call<JoinHomeResponse> call, Throwable t) {
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Keep
    @Override
    public void changeFamilyMemberPermission(String homeID, String user_id,
                                             int level, IDefaultCallBack callBack) {
        onDefaultError(callBack);
    }

    @Keep
    @Override
    public void removeFamilyMember(String homeID, String user_id, IDefaultCallBack callBack) {
        homeRepo.removeFamilyMember(homeID, user_id, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
//                TODO 如果移除的是自己，那么要更新房间列表
                if (callBack != null) {
                    callBack.onSuccess();
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void switchHomeNotGetInfo(String homeID, IDefaultCallBack callBack) {
        DDLog.i(TAG, "switchHomeNotGetInfo");
        if (TextUtils.isEmpty(homeID)) {
            DDLog.e(TAG, "Empty homeId");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId.");
            }
            return;
        }
        onBeforeSwitchHome(homeID);
        for (int i = 0; i < homeList.size(); i++) {
            if (homeID.equals(homeList.get(i).getHomeID())) {
                DDLog.i(TAG, "switchHome");
                onSwitchHome(homeList.get(i));
                callBack.onSuccess();
                break;
            }
        }

    }

    @Keep
    @Override
    public void switchHome(String homeID, IDefaultCallBack2<HomeInfoResponse> callBack) {
        DDLog.i(TAG, "getHomeInfo");
        if (TextUtils.isEmpty(homeID)) {
            DDLog.e(TAG, "Empty homeId");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId.");
            }
            return;
        }
        onBeforeSwitchHome(homeID);
        homeRepo.getHomeInfo(homeID, new Callback<HomeInfoResponse>() {
            @Override
            public void onResponse(@NotNull Call<HomeInfoResponse> call,
                                   @NotNull Response<HomeInfoResponse> response) {
                for (int i = 0; i < homeList.size(); i++) {
                    if (homeID.equals(homeList.get(i).getHomeID())) {
                        DDLog.i(TAG, "switchHome");
                        onSwitchHome(homeList.get(i));
                        if (null != response.body()
                                && null != response.body().getResult()
                                && !TextUtils.isEmpty(response.body().getResult().getName())) {
                            homeList.get(i).setHomeName(response.body().getResult().getName());
                        }
                        break;
                    }
                }

                if (null != callBack) {
                    HomeInfoResponse infoResponse = response.body();
                    if (null != infoResponse && 1 == infoResponse.getStatus()) {
                        try {
                            JSONObject args = new JSONObject();
                            JSONObject panelInfo = new JSONObject();
                            JSONObject tuyaInfo = new JSONObject();
                            if (null != infoResponse.getResult()) {
                                tuyaInfo.put("domain", infoResponse.getResult().getDomain());
                                if (null != infoResponse.getResult().getTuya()) {
                                    tuyaInfo.put("countrycode", infoResponse.getResult().getTuya().getCountrycode());
                                    tuyaInfo.put("uid", infoResponse.getResult().getTuya().getUid());
                                    tuyaInfo.put("username", infoResponse.getResult().getTuya().getUsername());
                                    tuyaInfo.put("password", infoResponse.getResult().getTuya().getPassword());
                                }
                                if (null != infoResponse.getResult().getDevice()) {
                                    panelInfo.put("deviceid", infoResponse.getResult().getDevice().getDeviceid());
                                    panelInfo.put("name", infoResponse.getResult().getDevice().getName());
                                    panelInfo.put("token", infoResponse.getResult().getDevice().getToken());
                                    panelInfo.put("sim_network", infoResponse.getResult().getDevice().getSim_network());
                                }
                            }
                            args.put("panelInfo", panelInfo);
                            args.put("tuyaInfo", tuyaInfo);

                            CommonCmdEvent event = new CommonCmdEvent(CommonCmdEvent.CMD.GET_HOME_INFO);
                            event.setExtra(args.toString());
                            DDLog.i(TAG, "通知成功获取家庭主机和涂鸦账号信息");
                            EventBus.getDefault().post(event);
                        } catch (Exception e) {
                            DDLog.e(TAG, "getHomeInfo, Error!!!!");
                            e.printStackTrace();
                        }

                        mCurrentHomeInfo = infoResponse.getResult();
                        mCurrentHomeInfo.setHomeId(homeID);
                        callBack.onSuccess(infoResponse);
                    } else {
                        callBack.onError(
                                null == infoResponse ? ErrorCode.DEFAULT : infoResponse.getStatus(),
                                null == infoResponse ? "Empty response" : infoResponse.getErrorMessage());
                    }
                }
            }

            @Override
            public void onFailure(@NotNull Call<HomeInfoResponse> call, @NotNull Throwable t) {
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void refreshCurrentHomeInfo(IDefaultCallBack2<HomeInfoResponse> callBack) {
        DDLog.i(TAG, "refreshCurrentHomeInfo");
        if (null == mCurrentHomeInfo || TextUtils.isEmpty(mCurrentHomeInfo.getHomeId())) {
            DDLog.e(TAG, "Empty currentHomeInfo or home id.");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty currentHomeInfo or home id, can't refresh.");
            }
            return;
        }

        final String currentHomeId = mCurrentHomeInfo.getHomeId();
        homeRepo.getHomeInfo(currentHomeId, new Callback<HomeInfoResponse>() {
            @Override
            public void onResponse(@NotNull Call<HomeInfoResponse> call,
                                   @NotNull Response<HomeInfoResponse> response) {
                if (null != callBack) {
                    HomeInfoResponse infoResponse = response.body();
                    if (null != infoResponse && 1 == infoResponse.getStatus()) {
                        for (int i = 0; i < homeList.size(); i++) {
                            if (currentHomeId.equals(homeList.get(i).getHomeID())) {
                                if (null != response.body()
                                        && null != response.body().getResult()
                                        && !TextUtils.isEmpty(response.body().getResult().getName())) {
                                    homeList.get(i).setHomeName(response.body().getResult().getName());
                                }
                                break;
                            }
                        }
                        try {
                            JSONObject args = new JSONObject();
                            JSONObject panelInfo = new JSONObject();
                            JSONObject tuyaInfo = new JSONObject();
                            if (null != infoResponse.getResult()) {
                                tuyaInfo.put("domain", infoResponse.getResult().getDomain());
                                if (null != infoResponse.getResult().getTuya()) {
                                    tuyaInfo.put("countrycode", infoResponse.getResult().getTuya().getCountrycode());
                                    tuyaInfo.put("uid", infoResponse.getResult().getTuya().getUid());
                                    tuyaInfo.put("username", infoResponse.getResult().getTuya().getUsername());
                                    tuyaInfo.put("password", infoResponse.getResult().getTuya().getPassword());
                                }
                                if (null != infoResponse.getResult().getDevice()) {
                                    panelInfo.put("deviceid", infoResponse.getResult().getDevice().getDeviceid());
                                    panelInfo.put("name", infoResponse.getResult().getDevice().getName());
                                    panelInfo.put("token", infoResponse.getResult().getDevice().getToken());
                                    panelInfo.put("sim_network", infoResponse.getResult().getDevice().getSim_network());
                                }
                            }
                            args.put("panelInfo", panelInfo);
                            args.put("tuyaInfo", tuyaInfo);

                            CommonCmdEvent event = new CommonCmdEvent(CommonCmdEvent.CMD.GET_HOME_INFO);
                            event.setExtra(args.toString());
                            DDLog.i(TAG, "通知成功更新家庭主机和涂鸦账号信息");
                            EventBus.getDefault().post(event);
                        } catch (Exception e) {
                            DDLog.e(TAG, "refreshHomeInfo, Error!!!!");
                            e.printStackTrace();
                        }

                        mCurrentHomeInfo = infoResponse.getResult();
                        mCurrentHomeInfo.setHomeId(currentHomeId);
                        callBack.onSuccess(infoResponse);
                    } else {
                        callBack.onError(
                                null == infoResponse ? ErrorCode.DEFAULT : infoResponse.getStatus(),
                                null == infoResponse ? "Empty response" : infoResponse.getErrorMessage());
                    }
                }
            }

            @Override
            public void onFailure(@NotNull Call<HomeInfoResponse> call, @NotNull Throwable t) {
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Keep
    @Override
    public void getHomeNotificationLanguage(String homeID, IDefaultCallBack2<String> callBack) {
        DDLog.i(TAG, "getHomeNotificationLanguage");
        if (TextUtils.isEmpty(homeID)) {
            DDLog.e(TAG, "Empty homeId");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId.");
            }
            return;
        }
        homeRepo.getHomeNotificationLanguage(homeID, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                String language = "";
                StringResponseEntry languageResponse = response.body();
                if (null != languageResponse && null != languageResponse.getResult()) {
                    language = languageResponse.getResult();
                }
                if (callBack != null) {
                    callBack.onSuccess(language);
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                DDLog.e("home", "getHomeNotificationLanguage onFailure:" + t.getMessage());
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Keep
    @Override
    public void getHomeMemberAvatars(String homeID, IDefaultCallBack2<MemberAvatars> callBack) {
        DDLog.i(TAG, "getHomeMemberAvatars");
        if (TextUtils.isEmpty(homeID)) {
            DDLog.e(TAG, "Empty homeId");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId.");
            }
            return;
        }
        homeRepo.getHomeMemberAvatars(homeID, new Callback<MemberAvatarsResponse>() {
            @Override
            public void onResponse(Call<MemberAvatarsResponse> call, Response<MemberAvatarsResponse> response) {
                if (callBack != null) {
                    if (null == response.body()) {
                        callBack.onError(ErrorCode.DEFAULT, "Empty result.");
                    } else {
                        callBack.onSuccess(response.body().getResult());
                    }
                }
            }

            @Override
            public void onFailure(Call<MemberAvatarsResponse> call, Throwable t) {
                DDLog.e("home", "getHomeMemberAvatars onFailure:" + t.getMessage());
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Keep
    @Override
    public void bindPanel(String homeId, String panelId, IDefaultCallBack callBack) {
        DDLog.i(TAG, "bindPanel");
        if (TextUtils.isEmpty(homeId)
                || TextUtils.isEmpty(panelId)) {
            DDLog.e(TAG, "Empty homeId or panelId");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId or panelId.");
            }
            return;
        }
        homeRepo.bindPanel(homeId, panelId, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (callBack != null) {
                    if (null == response.body()) {
                        callBack.onError(ErrorCode.DEFAULT, "Empty result.");
                    } else {
                        callBack.onSuccess();
                    }
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                DDLog.e("home", "bindPanel onFailure:" + t.getMessage());
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }


    @Override
    public void isOnlyAdmin(String homeId, IDefaultCallBack2<Boolean> callBack) {
        DDLog.i(TAG, "isOnlyAdmin");
        if (TextUtils.isEmpty(homeId)) {
            DDLog.e(TAG, "Empty homeId.");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId.");
            }
            return;
        }

        homeRepo.getIsOnlyAdmin(homeId, new Callback<IsOnlyAdminResponse>() {
            @Override
            public void onResponse(Call<IsOnlyAdminResponse> call, Response<IsOnlyAdminResponse> response) {
                if (callBack != null) {
                    if (null == response.body()) {
                        callBack.onError(ErrorCode.DEFAULT, "Empty result.");
                    } else {
                        try {
                            Boolean isOnlyAdmin = Boolean.valueOf(response.body().getResult());
                            callBack.onSuccess(isOnlyAdmin);
                        } catch (Exception e) {
                            e.printStackTrace();
                            callBack.onError(ErrorCode.DEFAULT, "Error on parse result to Boolean.");
                        }
                    }
                }
            }

            @Override
            public void onFailure(Call<IsOnlyAdminResponse> call, Throwable t) {
                DDLog.e("home", "isOnlyAdmin onFailure:" + t.getMessage());
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void forceDeleteHome(String homeId, IDefaultCallBack callBack) {
        DDLog.i(TAG, "forceDeleteHome");
        if (TextUtils.isEmpty(homeId)) {
            DDLog.e(TAG, "Empty homeId.");
            if (null != callBack) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId.");
            }
            return;
        }

        homeRepo.getForceDeleteHome(homeId, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (callBack != null) {
                    if (null == response.body()) {
                        callBack.onError(ErrorCode.DEFAULT, "Empty result.");
                    } else if (response.body().getStatus() != 1) {
                        callBack.onError(response.body().getStatus(), response.body().getErrorMessage());
                    } else {
                        callBack.onSuccess();
                    }
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                DDLog.e("home", "forceDeleteHome onFailure:" + t.getMessage());
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void getEventListData(String panelId, int limit, long timestamp, String filters, IDefaultCallBack2<EventListEntry> callBack) {
        DDLog.i(TAG, "getEventListData");
        if (timestamp <= 0) {
            timestamp = System.currentTimeMillis() * 1000 * 1000;
        }
        if (limit <= 1) {
            limit = 20;
        }
        homeRepo.getEventListData(getCurrentHome().getHomeID(), panelId, limit, timestamp, filters,
                new Callback<EventListEntry>() {
                    @Override
                    public void onResponse(Call<EventListEntry> call, Response<EventListEntry> response) {
                        if (callBack != null) {
                            if (response != null && response.body() != null
                                    && response.isSuccessful()) {
                                callBack.onSuccess(response.body());
                            } else {
                                callBack.onError(response.code(), response.message());
                            }
                        }
                    }

                    @Override
                    public void onFailure(Call<EventListEntry> call, Throwable t) {
                        t.printStackTrace();
                        if (callBack != null) {
                            if (t instanceof NetWorkException) {
                                callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                            } else {
                                callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                            }
                        }
                    }
                });
    }

    @Override
    public void getEventListDataByFilter(int limit, long time, String type_ids, IDefaultCallBack2<EventListByFilterEntry> callBack) {
        DDLog.i(TAG, "getEventListDataByFilter");
        if (time <= 0) {
            time = System.currentTimeMillis() * 1000 * 1000;
        }
        if (limit <= 1) {
            limit = 20;
        }
        homeRepo.getEventListDataByFilter(getCurrentHome().getHomeID(), limit, time, type_ids,
                new Callback<EventListByFilterEntry>() {
                    @Override
                    public void onResponse(Call<EventListByFilterEntry> call, Response<EventListByFilterEntry> response) {
                        if (callBack != null) {
                            if (response != null && response.body() != null
                                    && response.isSuccessful()) {
                                callBack.onSuccess(response.body());
                            } else {
                                callBack.onError(response.code(), response.message());
                            }
                        }
                    }

                    @Override
                    public void onFailure(Call<EventListByFilterEntry> call, Throwable t) {
                        t.printStackTrace();
                        if (callBack != null) {
                            if (t instanceof NetWorkException) {
                                callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                            } else {
                                callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                            }
                        }
                    }
                });
    }

    @Override
    public void getDailyMemoriesVideoUrl(String record_id, IDefaultCallBack2<DailyMemoriesVideoResponse> callBack) {
        homeRepo.getDailyMemoriesVideoUrl(getCurrentHome().getHomeID(), record_id,
                new Callback<DailyMemoriesVideoResponse>() {
                    @Override
                    public void onResponse(Call<DailyMemoriesVideoResponse> call, Response<DailyMemoriesVideoResponse> response) {
                        if (callBack != null) {
                            if (response != null && response.body() != null
                                    && response.isSuccessful()) {
                                callBack.onSuccess(response.body());
                            } else {
                                callBack.onError(response.code(), response.message());
                            }
                        }
                    }

                    @Override
                    public void onFailure(Call<DailyMemoriesVideoResponse> call, Throwable t) {
                        t.printStackTrace();
                        if (callBack != null) {
                            if (t instanceof NetWorkException) {
                                callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                            } else {
                                callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                            }
                        }
                    }
                });
    }

    @Override
    public void initHomeWithPanel(@NotNull InitHomeCompatParams params, IDefaultCallBack2<HomeInfo> callback) {
        onDefaultError(callback);
    }

    @Override
    public void updateBalanceContractBank(String homeId, String cardholder, String iban, String pwd, IDefaultCallBack callback) {
        if (TextUtils.isEmpty(homeId)
                || TextUtils.isEmpty(cardholder)
                || TextUtils.isEmpty(iban)
                || TextUtils.isEmpty(pwd)) {
            DDLog.i(TAG, "updateBalanceContractBank. Empty params!");
            if (callback != null) {
                callback.onError(ErrorCode.PARAM_ERROR, "Empty homeId or iban or pwd or cardholder");
            }
            return;
        }
        homeRepo.updateBalanceContractBank(homeId, cardholder, iban, pwd, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (callback != null) {
                    if (response != null && response.body() != null
                            && response.isSuccessful()) {
                        callback.onSuccess();
                    } else {
                        callback.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                t.printStackTrace();
                if (callback != null) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callback.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });

    }

    @Override
    public void terminateBalanceContract(String homeId, String sign, IDefaultCallBack callback) {
        if (TextUtils.isEmpty(homeId) || TextUtils.isEmpty(sign)) {
            DDLog.i(TAG, "terminateBalanceContract. Empty params!");
            if (callback != null) {
                callback.onError(ErrorCode.PARAM_ERROR, "Empty homeId or sign");
            }
            return;
        }
        homeRepo.terminateBalanceContract(homeId, sign, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (callback != null) {
                    if (response != null && response.body() != null
                            && response.isSuccessful()) {
                        callback.onSuccess();
                    } else {
                        callback.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                t.printStackTrace();
                if (callback != null) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callback.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void getBalanceContractRecordsById(String homeId, String recordId, IDefaultCallBack2<SingleBalanceContractRecordResponse.ResultBean> callback) {

        if (TextUtils.isEmpty(recordId) || TextUtils.isEmpty(homeId)) {
            DDLog.e(TAG, "参数验证失败 - recordId或homeId为空");
            if (callback != null) {
                callback.onError(DEFAULT, "recordId or homeId params error");
            }
            return;
        }

        homeRepo.getBalanceContractRecordsById(homeId, recordId, new Callback<SingleBalanceContractRecordResponse>() {
            @Override
            public void onResponse(Call<SingleBalanceContractRecordResponse> call, Response<SingleBalanceContractRecordResponse> response) {
                if (callback != null) {
                    if (response != null && response.body() != null
                            && response.isSuccessful()) {
                        SingleBalanceContractRecordResponse singleBalanceContractRecordResponse = response.body();

                        // 详细的调试日志
                        DDLog.i(TAG, "=== 响应解析调试 ===");
                        DDLog.i(TAG, "响应对象是否为空: " + (singleBalanceContractRecordResponse == null));

                        if (singleBalanceContractRecordResponse != null) {
                            // 输出完整的JSON字符串
                            String jsonString = JSON.toJSONString(singleBalanceContractRecordResponse);
                            DDLog.i(TAG, "完整JSON: " + jsonString);

                            // 检查基础字段
                            DDLog.i(TAG, "Status: " + singleBalanceContractRecordResponse.getStatus());
                            DDLog.i(TAG, "Message: " + singleBalanceContractRecordResponse.getErrorMessage());

                            // 重点检查Result字段
                            SingleBalanceContractRecordResponse.ResultBean result = singleBalanceContractRecordResponse.getResult();
                            DDLog.i(TAG, "Result字段是否为空: " + (result == null));

                            if (result != null) {
                                DDLog.i(TAG, "✓ Result字段解析成功");
                                DDLog.i(TAG, "Result toString: " + result.toString());
                            } else {
                                DDLog.e(TAG, "❌ Result字段为空，检查JSON字段映射");

                                // 检查原始JSON中的字段名
                                if (jsonString.contains("\"Result\"")) {
                                    DDLog.i(TAG, "JSON中包含Result字段（大写R）");
                                } else if (jsonString.contains("\"result\"")) {
                                    DDLog.e(TAG, "JSON中包含result字段（小写r）");
                                    DDLog.e(TAG, "建议检查@SerializedName注解");
                                } else {
                                    DDLog.e(TAG, "JSON中不包含Result相关字段");
                                }

                                // 尝试手动解析
                                try {
                                    org.json.JSONObject jsonObj = new org.json.JSONObject(jsonString);
                                    DDLog.i(TAG, "JSON中的所有字段: " + jsonObj.keys().toString());

                                    // 检查可能的字段名变体
                                    String[] possibleKeys = {"Result", "result", "data", "Data", "content", "Content"};
                                    for (String key : possibleKeys) {
                                        if (jsonObj.has(key)) {
                                            DDLog.i(TAG, "找到可能的Result字段: " + key);
                                            DDLog.i(TAG, key + "字段内容: " + jsonObj.get(key).toString());
                                        }
                                    }
                                } catch (Exception e) {
                                    DDLog.e(TAG, "手动解析JSON失败: " + e.getMessage());
                                }
                            }
                        }

                        Log.i(TAG, "onResponse: "+ JSON.toJSONString(singleBalanceContractRecordResponse));
                        callback.onSuccess(singleBalanceContractRecordResponse.getResult());
                    } else {
                        callback.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<SingleBalanceContractRecordResponse> call, Throwable t) {
                t.printStackTrace();
                if (callback != null) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callback.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }
    @Override
    public void getBalanceContractRecords(long createTime, String homeId, int pageSize, IDefaultCallBack2<BalanceContractRecordsResponse.ResultBean> callback) {
        if (TextUtils.isEmpty(homeId)) {
            DDLog.i(TAG, "getBalanceContractRecords. Empty params!");
            if (callback != null) {
                callback.onError(ErrorCode.PARAM_ERROR, "Empty homeId");
            }
            return;
        }
        homeRepo.getBalanceContractRecords(createTime, homeId, pageSize, new Callback<BalanceContractRecordsResponse>() {
            @Override
            public void onResponse(Call<BalanceContractRecordsResponse> call, Response<BalanceContractRecordsResponse> response) {
                if (callback != null) {
                    if (response != null && response.body() != null
                            && response.isSuccessful()) {
                        BalanceContractRecordsResponse balanceContractRecordsResponse = response.body();
                        callback.onSuccess(balanceContractRecordsResponse.getResult());
                    } else {
                        callback.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<BalanceContractRecordsResponse> call, Throwable t) {
                t.printStackTrace();
                if (callback != null) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callback.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void getBalanceContractUnsignTemplate(String homeId, IDefaultCallBack2<BalanceContractUnsignTemplateResponse.ResultBean> callback) {
        if (TextUtils.isEmpty(homeId)) {
            DDLog.i(TAG, "getBalanceContractUnsignTemplate. Empty params!");
            if (callback != null) {
                callback.onError(ErrorCode.PARAM_ERROR, "Empty homeId or deviceId");
            }
            return;
        }
        homeRepo.getBalanceContractUnsignTemplate(homeId, new Callback<BalanceContractUnsignTemplateResponse>() {
            @Override
            public void onResponse(Call<BalanceContractUnsignTemplateResponse> call, Response<BalanceContractUnsignTemplateResponse> response) {
                if (callback != null) {
                    if (response != null && response.body() != null
                            && response.isSuccessful()) {
                        BalanceContractUnsignTemplateResponse balanceContractUnsignTemplateResponse = response.body();
                        callback.onSuccess(balanceContractUnsignTemplateResponse.getResult());
                    } else {
                        callback.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<BalanceContractUnsignTemplateResponse> call, Throwable t) {
                DDLog.i(TAG, "getBalanceContractUnsignTemplate. onFailure");

                t.printStackTrace();
                if (callback != null) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callback.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void getBalanceContractSignTemplate(String countryCode, IDefaultCallBack2<BalanceContractSignTemplateResponse.ResultBean> callback) {
        if (TextUtils.isEmpty(countryCode)) {
            DDLog.i(TAG, "getBalanceContractSignTemplate. Empty params!");
            if (callback != null) {
                callback.onError(ErrorCode.PARAM_ERROR, "Empty countryCode");
            }
            return;
        }
        homeRepo.getBalanceContractSignTemplate(countryCode, new Callback<BalanceContractSignTemplateResponse>() {
            @Override
            public void onResponse(Call<BalanceContractSignTemplateResponse> call, Response<BalanceContractSignTemplateResponse> response) {
                if (callback != null) {
                    if (response != null && response.body() != null
                            && response.isSuccessful()) {
                        BalanceContractSignTemplateResponse balanceContractSignTemplateResponse = response.body();
                        callback.onSuccess(balanceContractSignTemplateResponse.getResult());
                    } else {
                        callback.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<BalanceContractSignTemplateResponse> call, Throwable t) {
                DDLog.i(TAG, "getBalanceContractSignTemplate. onFailure");

                t.printStackTrace();
                if (callback != null) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callback.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void checkBmtDeviceBalanceContractStatus(String homeId, String deviceId, IDefaultCallBack2<Integer> callback) {
        if (TextUtils.isEmpty(homeId) || TextUtils.isEmpty(deviceId)) {
            DDLog.i(TAG, "checkBmtDeviceBalanceContractStatus. Empty params!");
            if (callback != null) {
                callback.onError(ErrorCode.PARAM_ERROR, "Empty homeId or deviceId");
            }
            return;
        }
        homeRepo.checkBmtDeviceBalanceContractStatus(homeId, deviceId, new Callback<StatusResponse>() {

            @Override
            public void onResponse(Call<StatusResponse> call, Response<StatusResponse> response) {
                if (callback != null) {
                    if (response != null && response.body() != null
                            && response.isSuccessful()) {
                        StatusResponse statusResponse = response.body();
                        callback.onSuccess(statusResponse.getResult().getStatus());
                    } else {
                        callback.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<StatusResponse> call, Throwable t) {
                DDLog.i(TAG, "getBalanceContractSignTemplate. onFailure");

                t.printStackTrace();
                if (callback != null) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callback.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void updateBalanceContractData(String homeId, IDefaultCallBack callBack) {
        if (TextUtils.isEmpty(homeId)) {
            DDLog.i(TAG, "updateBalanceContractData. Empty params!");
            if (callBack != null) {
                callBack.onError(ErrorCode.PARAM_ERROR, "Empty homeId");
            }
            return;
        }

        homeRepo.updateBalanceContractData(homeId, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (callBack != null) {
                    if (response != null && response.body() != null
                            && response.isSuccessful()) {
                        callBack.onSuccess();
                    } else {
                        callBack.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                t.printStackTrace();
                if (callBack != null) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callBack.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void bmtBalanceContractParticipationHours(long createTime, String homeId, int pageSize, IDefaultCallBack2<BmtBalanceContractParticipationHoursResponse.ResultBean> callback) {
        if (TextUtils.isEmpty(homeId)) {
            DDLog.i(TAG, "bmtBalanceContractParticipationHours. Empty params!");
            if (callback != null) {
                callback.onError(ErrorCode.PARAM_ERROR, "Empty homeId");
            }
            return;
        }
        homeRepo.bmtBalanceContractParticipationHours(createTime, homeId, pageSize, new Callback<BmtBalanceContractParticipationHoursResponse>() {
            @Override
            public void onResponse(Call<BmtBalanceContractParticipationHoursResponse> call, Response<BmtBalanceContractParticipationHoursResponse> response) {
                if (callback != null) {
                    if (response != null && response.body() != null
                            && response.isSuccessful()) {
                        BmtBalanceContractParticipationHoursResponse participationHoursResponse = response.body();
                        callback.onSuccess(participationHoursResponse.getResult());
                    } else {
                        callback.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<BmtBalanceContractParticipationHoursResponse> call, Throwable t) {
                t.printStackTrace();
                if (callback != null) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callback.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void bmtAddBalanceContractParticipationHours(String homeId, ParticipationHour participationHour, IDefaultCallBack callBack) {
        if (TextUtils.isEmpty(homeId) || null == participationHour) {
            if (callBack != null) {
                callBack.onError(ErrorCode.DEFAULT, "saveFamilyBalanceContractInfo params null");
            }
            return;
        }
        homeRepo.bmtAddBalanceContractParticipationHours(homeId, participationHour, new Callback<StringResponseEntry>() {

            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (response.isSuccessful()) {
                    if (callBack != null) {
                        callBack.onSuccess();
                    }
                } else {
                    if (callBack != null) {
                        callBack.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                if (null != callBack) {
                    if (t instanceof NetWorkException) {
                        callBack.onError(((NetWorkException) t).getStatus(), t.getMessage());
                        return;
                    }

                    callBack.onError(ErrorCode.DEFAULT, "Unknown Error: " + t.getMessage());
                }
            }
        });
    }

    @Override
    public void bmtUpdateBalanceContractParticipationHours(String homeId, ParticipationHour participationHour, IDefaultCallBack callback) {
        if (TextUtils.isEmpty(homeId) || null == participationHour) {
            if (callback != null) {
                callback.onError(ErrorCode.DEFAULT, "saveFamilyBalanceContractInfo params null");
            }
            return;
        }
        homeRepo.bmtUpdateBalanceContractParticipationHours(homeId, participationHour, new Callback<StringResponseEntry>() {

            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (response.isSuccessful()) {
                    if (callback != null) {
                        callback.onSuccess();
                    }
                } else {
                    if (callback != null) {
                        callback.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                if (null != callback) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), t.getMessage());
                        return;
                    }

                    callback.onError(ErrorCode.DEFAULT, "Unknown Error: " + t.getMessage());
                }
            }
        });
    }

    @Override
    public void bmtDeleteBalanceContractParticipationHours(String homeId, String id, IDefaultCallBack callback) {
        homeRepo.bmtDeleteBalanceContractParticipationHours(homeId, id, new Callback<StringResponseEntry>() {

            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (response.isSuccessful()) {
                    if (callback != null) {
                        callback.onSuccess();
                    }
                } else {
                    if (callback != null) {
                        callback.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                if (null != callback) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), t.getMessage());
                        return;
                    }

                    callback.onError(ErrorCode.DEFAULT, "Unknown Error: " + t.getMessage());
                }
            }
        });
    }

    @Override
    public void bmtIsTaskTimeInUpdatedRange(String homeId, ParticipationHour participationHour, IDefaultCallBack2<BmtIsTaskTimeInUpdatedRangeResponse.ResultBean> callback) {
        homeRepo.bmtIsTaskTimeInUpdatedRange(homeId, participationHour, new Callback<BmtIsTaskTimeInUpdatedRangeResponse>() {

            @Override
            public void onResponse(Call<BmtIsTaskTimeInUpdatedRangeResponse> call, Response<BmtIsTaskTimeInUpdatedRangeResponse> response) {
                if (callback != null) {
                    if (response != null && response.body() != null
                            && response.isSuccessful()) {
                        BmtIsTaskTimeInUpdatedRangeResponse participationHoursResponse = response.body();
                        callback.onSuccess(participationHoursResponse.getResult());
                    } else {
                        callback.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<BmtIsTaskTimeInUpdatedRangeResponse> call, Throwable t) {
                if (null != callback) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), t.getMessage());
                        return;
                    }

                    callback.onError(ErrorCode.DEFAULT, "Unknown Error: " + t.getMessage());
                }
            }
        });
    }

    @Override
    public void getHomeLocation(String homeId, IDefaultCallBack2<HomeLocationResponse.ResultBean> callback) {
        if (TextUtils.isEmpty(homeId)) {
            DDLog.i(TAG, "getHomeLocation. Empty params!");
            if (callback != null) {
                callback.onError(ErrorCode.PARAM_ERROR, "Empty homeId");
            }
            return;
        }

        homeRepo.getHomeLocation(homeId, new Callback<HomeLocationResponse>() {

            @Override
            public void onResponse(Call<HomeLocationResponse> call, Response<HomeLocationResponse> response) {
                if (callback != null) {
                    if (response != null && response.body() != null
                            && response.isSuccessful()) {
                        HomeLocationResponse homeLocationResponse = response.body();
                        callback.onSuccess(homeLocationResponse.getResult());
                    } else {
                        callback.onError(response.code(), response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<HomeLocationResponse> call, Throwable t) {
                DDLog.i(TAG, "getHomeLocation. onFailure");

                t.printStackTrace();
                if (callback != null) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callback.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    @Override
    public void bmtSaveLocation(String homeId, double latitude, double longitude, IDefaultCallBack callback) {
        if (TextUtils.isEmpty(homeId)) {
            DDLog.i(TAG, "bmtSaveLocation. Empty params!");
            if (callback != null) {
                callback.onError(ErrorCode.PARAM_ERROR, "Empty homeId or deviceId");
            }
            return;
        }

        homeRepo.bmtSaveLocation(homeId, latitude, longitude, new Callback<StringResponseEntry>() {

            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                DDLog.i(TAG, "bmtSaveLocation. onFailure");

                t.printStackTrace();
                if (callback != null) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMsgDes());
                    } else {
                        callback.onError(ErrorCode.DEFAULT, t.getMessage());
                    }
                }
            }
        });
    }

    private void onDefaultError(IDefaultCallBack callback) {
        onDefaultError(ErrorCode.ERROR_UNSUPPORTED_API, ERROR_INFO + ERROR_INFO_EXT, callback);
    }

    private void onDefaultError(IDefaultCallBack2<?> callback) {
        onDefaultError(ErrorCode.ERROR_UNSUPPORTED_API, ERROR_INFO + ERROR_INFO_EXT, callback);
    }
}
