package com.dinsafer.module_home;

import android.app.Application;
import android.text.TextUtils;

import androidx.annotation.Keep;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.common.PluginServiceManager;
import com.dinsafer.dincore.user.api.IUser;
import com.dinsafer.module_bmt.BmtService;
import com.dinsafer.module_dscam.DsCamService;
import com.dinsafer.module_heartlai.HeartLaiService;
import com.dinsafer.module_home.activator.PluginActivatorManager;
import com.dinsafer.module_home.api.IHome;
import com.dinsafer.panel.PanelService;

@Keep
public class DinSDK {
    // 定义支持的配件类型
    /**
     * 主机
     */
    public static final int SERVICE_TYPE_PANEL = 1;
    /**
     * 心赖摄像头
     */
    public static final int SERVICE_TYPE_HEARTLAI = 1 << 1;
    /**
     * 自研IPC
     */
    public static final int SERVICE_TYPE_DSCAM = 1 << 3;
    /**
     * 电池
     */
    public static final int SERVICE_TYPE_BMT = 1 << 4;

    private Application application;
    private String domain;
    private String appID;
    private String appSecret;
    private boolean debugMode;
    private String tuyaKey, tuyaSecret;
    private String e2eDomain;
    private int port;
    private String e2eHelpDomain;
    private int e2eHelpPort;
    // 是否cawa兼容模式
    private boolean compatMode;

    private static volatile DinSDK instance;
    private String helioCamSecret;

    // bmt 域名
    private String bmtDomain;
    // 支持的功能模块
    private int supportServices = (SERVICE_TYPE_PANEL | SERVICE_TYPE_HEARTLAI
            | SERVICE_TYPE_DSCAM | SERVICE_TYPE_BMT);

    @Keep
    public static DinSDK getInstance() {
        return instance;
    }

    @Keep
    public DinSDK() {

    }

    @Keep
    public DinSDK(Application application, String domain, String appID, String appSecret,
                  boolean debugMode, String tuyaKey, String tuyaSecret) {
        this(application, domain, appID, appSecret, debugMode, false, tuyaKey, tuyaSecret);
    }

    @Keep
    public DinSDK(Application application, String domain, String appID, String appSecret,
                  boolean debugMode, boolean compatMode, String tuyaKey, String tuyaSecret) {
        this(application, domain, appID, appSecret, debugMode, compatMode, tuyaKey, tuyaSecret, "");
    }

    @Keep
    public DinSDK(Application application, String domain, String appID, String appSecret,
                  boolean debugMode, boolean compatMode, String tuyaKey, String tuyaSecret, String bmtDomain) {
        this.application = application;
        this.domain = domain;
        this.appID = appID;
        this.appSecret = appSecret;
        this.debugMode = debugMode;
        this.tuyaKey = tuyaKey;
        this.tuyaSecret = tuyaSecret;
        this.compatMode = compatMode;
        this.bmtDomain = bmtDomain;

        if (0 != (SERVICE_TYPE_DSCAM & supportServices)) {
            PluginServiceManager.getInstance().register(new DsCamService(application));
        }
        if (0 != (SERVICE_TYPE_HEARTLAI & supportServices)) {
            PluginServiceManager.getInstance().register(new HeartLaiService(application));
        }
        if (0 != (SERVICE_TYPE_BMT & supportServices)) {
            PluginServiceManager.getInstance().register(new BmtService(application));
        }
        if (0 != (SERVICE_TYPE_PANEL & supportServices)) {
            PluginServiceManager.getInstance().register(new PanelService(application, domain));
        }
    }

    @Keep
    public static PluginActivatorManager getPluginActivtor() {
        return PluginActivatorManager.getInstance();
    }

    @Keep
    public static IUser getUserInstance() {
        return DinCore.getUserInstance();
    }

    @Keep
    public static IHome getHomeInstance() {
        return DinHome.getInstance();
    }

    public Application getApplication() {
        return application;
    }

    @Keep
    public DinSDK setApplication(Application application) {
        this.application = application;
        return this;
    }

    public String getDomain() {
        return domain;
    }

    @Keep
    public DinSDK setDomain(String domain) {
        this.domain = domain;
        return this;
    }

    @Keep
    public DinSDK setE2eDomain(String e2eDomain) {
        this.e2eDomain = e2eDomain;
        DinCore.getInstance().setE2eDomain(e2eDomain);
        return this;
    }

    public DinSDK setPort(int port) {
        this.port = port;
        DinCore.getInstance().setE2ePort(port);
        return this;
    }

    @Keep
    public DinSDK setE2eHelpDomain(String e2eHelpDomain) {
        this.e2eHelpDomain = e2eHelpDomain;
        DinCore.getInstance().setE2eHelpDomain(e2eHelpDomain);
        return this;
    }

    @Keep
    public DinSDK setE2eHelpPort(int e2eHelpPort) {
        this.e2eHelpPort = e2eHelpPort;
        DinCore.getInstance().setE2eHelpDomainPort(e2eHelpPort);
        return this;
    }

    public String getAppID() {
        return appID;
    }

    public DinSDK setAppID(String appID) {
        this.appID = appID;
        return this;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public String getE2eHelpDomain() {
        return e2eHelpDomain;
    }

    public int getE2eHelpPort() {
        return e2eHelpPort;
    }

    public boolean isCompatMode() {
        return compatMode;
    }

    @Keep
    public DinSDK setAppSecret(String appSecret) {
        this.appSecret = appSecret;
        return this;
    }

    @Keep
    public boolean isDebugMode() {
        return debugMode;
    }

    @Keep
    public DinSDK setDebugMode(boolean debugMode) {
        this.debugMode = debugMode;
        return this;
    }

    public String getHelioCamSecret() {
        return helioCamSecret;
    }

    @Keep
    public DinSDK setHelioCamSecret(String helioCamSecret) {
        this.helioCamSecret = helioCamSecret;
        return this;
    }

    public String getBmtDomain() {
        return bmtDomain;
    }

    @Keep
    public void setBmtDomain(String bmtDomain) {
        this.bmtDomain = bmtDomain;
    }

    public String getE2eDomain() {
        return e2eDomain;
    }

    public int getPort() {
        return port;
    }

    public void addSupportService(int service) {
        supportServices |= service;
    }

    public void removeSupportService(int service) {
        supportServices &= ~service;
    }

    public void addAllSupportServices() {
        supportServices = (SERVICE_TYPE_PANEL | SERVICE_TYPE_HEARTLAI
                | SERVICE_TYPE_DSCAM | SERVICE_TYPE_BMT);
    }

    public void removeAllSupportServices() {
        supportServices = 0;
    }

    @Keep
    public static final class DinSDKBuilder {
        private Application application;
        private String domain;
        private String appID;
        private String appSecret;
        private boolean debugMode;
        private String tuyaKey;
        private String tuyaSecret;
        private String helioCamSecret;
        //        udp地址
        private String e2eDomain;
        private String e2eHelpDomain;
        private int port;
        private int e2eHelpDomainPort;
        // 是否cawa兼容模式
        private boolean compatMode;

        // bmt 域名
        private String bmtDomain;

        // 支持的功能模块
        private int supportServices = (SERVICE_TYPE_PANEL | SERVICE_TYPE_HEARTLAI
                | SERVICE_TYPE_DSCAM | SERVICE_TYPE_BMT);

        @Keep
        private DinSDKBuilder() {
        }

        @Keep
        public static DinSDK.DinSDKBuilder create() {
            return new DinSDK.DinSDKBuilder();
        }

        @Keep
        public DinSDK.DinSDKBuilder withApplication(Application application) {
            this.application = application;
            return this;
        }

        @Keep
        public DinSDK.DinSDKBuilder withDomain(String domain) {
            this.domain = domain;
            return this;
        }

        @Keep
        public DinSDK.DinSDKBuilder withAppID(String appID) {
            this.appID = appID;
            return this;
        }

        @Keep
        public DinSDK.DinSDKBuilder withAppSecret(String appSecret) {
            this.appSecret = appSecret;
            return this;
        }

        @Keep
        public DinSDK.DinSDKBuilder withDebugMode(boolean debugMode) {
            this.debugMode = debugMode;
            return this;
        }

        @Keep
        public DinSDK.DinSDKBuilder withTuyaAppKey(String key) {
            this.tuyaKey = key;
            return this;
        }

        @Keep
        public DinSDK.DinSDKBuilder withTuyaAppSecret(String secret) {
            this.tuyaSecret = secret;
            return this;
        }

        @Keep
        public DinSDK.DinSDKBuilder withHelioCamSecret(String secret) {
            this.helioCamSecret = secret;
            return this;
        }

        @Keep
        public DinSDK.DinSDKBuilder withE2EDomain(String domain) {
            this.e2eDomain = domain;
            return this;
        }

        @Keep
        public DinSDK.DinSDKBuilder withE2EPort(int port) {
            this.port = port;
            return this;
        }

        @Keep
        public DinSDK.DinSDKBuilder withE2EHelpDomain(String domain) {
            this.e2eHelpDomain = domain;
            return this;
        }

        @Keep
        public DinSDK.DinSDKBuilder withE2EHelpPort(int port) {
            this.e2eHelpDomainPort = port;
            return this;
        }

        @Keep
        public DinSDK.DinSDKBuilder withCompatMode(boolean compatMode) {
            this.compatMode = compatMode;
            return this;
        }

        @Keep
        public DinSDK.DinSDKBuilder withBmtDomain(String bmtDomain) {
            this.bmtDomain = bmtDomain;
            return this;
        }

        @Keep
        public DinSDK.DinSDKBuilder addSupportService(int service) {
            supportServices |= service;
            return this;
        }

        @Keep
        public DinSDK.DinSDKBuilder removeSupportService(int service) {
            supportServices &= ~service;
            return this;
        }

        @Keep
        public DinSDK build() {
            DinSDK dinSDK = new DinSDK();
            dinSDK.appID = this.appID;
            dinSDK.application = this.application;
            dinSDK.debugMode = this.debugMode;
            dinSDK.appSecret = this.appSecret;
            dinSDK.domain = this.domain;
            dinSDK.tuyaKey = this.tuyaKey;
            dinSDK.tuyaSecret = this.tuyaSecret;
            dinSDK.helioCamSecret = this.helioCamSecret;
            dinSDK.compatMode = this.compatMode;
            dinSDK.bmtDomain = this.bmtDomain;
            if (!TextUtils.isEmpty(this.e2eDomain)) {
                dinSDK.e2eDomain = this.e2eDomain;
            } else {
                dinSDK.e2eDomain = "dou-test.dinsafer.com";
            }

            if (this.port > 0) {
                dinSDK.port = this.port;
            } else {
                dinSDK.port = 2050;
            }

            if (!TextUtils.isEmpty(this.e2eHelpDomain)) {
                dinSDK.e2eHelpDomain = this.e2eHelpDomain;
            } else {
                dinSDK.e2eHelpDomain = "";
            }

            if (this.e2eHelpDomainPort > 0) {
                dinSDK.e2eHelpPort = this.e2eHelpDomainPort;
            } else {
                dinSDK.e2eHelpPort = 2051;
            }

            instance = dinSDK;

            DinCore.DinCoreBuilder.create()
                    .withApplication(application)
                    .withAppID(appID)
                    .withAppSecret(appSecret)
                    .withDebugMode(debugMode)
                    .withDomain(domain)
                    .withHelioCamSecret(helioCamSecret)
                    .withUDPAddress(e2eDomain + ":" + port)
                    .withE2eHelpDomain(dinSDK.e2eHelpDomain)
                    .withE2eHelpPor(dinSDK.e2eHelpPort)
                    .withCompatMode(compatMode)
                    .withBmtDomain(bmtDomain)
                    .build();
            if (0 != (SERVICE_TYPE_DSCAM & supportServices)) {
                PluginServiceManager.getInstance().register(new DsCamService(dinSDK.application));
            }
            if (0 != (SERVICE_TYPE_HEARTLAI & supportServices)) {
                PluginServiceManager.getInstance().register(new HeartLaiService(dinSDK.application));
            }
            if (0 != (SERVICE_TYPE_BMT & supportServices)) {
                PluginServiceManager.getInstance().register(new BmtService(dinSDK.application));
            }
            if (0 != (SERVICE_TYPE_PANEL & supportServices)) {
                PluginServiceManager.getInstance().register(new PanelService(dinSDK.application, domain));
            }
            return dinSDK;
        }
    }


}
