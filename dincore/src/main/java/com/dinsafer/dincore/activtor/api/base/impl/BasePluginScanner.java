package com.dinsafer.dincore.activtor.api.base.impl;

import com.dinsafer.dincore.activtor.api.base.IPluginScanCallback;
import com.dinsafer.dincore.activtor.api.base.IPluginScanner;
import com.dinsafer.dincore.activtor.bean.Plugin;
import com.dinsafer.dincore.crypt.HelioCamCrypt;
import com.dinsafer.dincore.crypt.ICrypt;

import java.util.ArrayList;
import java.util.List;

/**
 * 配件扫描器基类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/8 11:53 AM
 */
public abstract class BasePluginScanner implements IPluginScanner {
    protected final String TAG = this.getClass().getSimpleName();

    private final List<IPluginScanCallback> callBackList = new ArrayList<>();
    protected Plugin plugin;
    protected final ICrypt crypt = new HelioCamCrypt();

    @Override
    public void addScanCallBack(IPluginScanCallback callBack) {
        if (callBackList.contains(callBack)) {
            return;
        }

        callBackList.add(callBack);
    }

    @Override
    public void removeScanCallBack(IPluginScanCallback callBack) {
        if (!callBackList.contains(callBack)) {
            return;
        }

        callBackList.remove(callBack);
    }

    @Override
    public void destroyScanner() {
        callBackList.clear();
    }

    protected void callBackScanResult() {
        callBackScanResult(1);
    }

    protected void callBackScanResult(int code) {
        for (IPluginScanCallback callback :
                callBackList) {
            callback.onScanResult(code, plugin);
        }
    }
}
