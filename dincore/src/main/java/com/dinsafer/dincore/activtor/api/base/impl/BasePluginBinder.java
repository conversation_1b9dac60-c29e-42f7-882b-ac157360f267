package com.dinsafer.dincore.activtor.api.base.impl;

import android.content.Context;

import androidx.annotation.Keep;

import com.dinsafer.dincore.activtor.api.base.IPluginBindCallBack;
import com.dinsafer.dincore.activtor.api.base.IPluginBinder;
import com.dinsafer.dincore.activtor.api.base.IPluginEthernetCallback;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 配件添加器基类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/8 2:07 PM
 */
@Keep
public abstract class BasePluginBinder implements IPluginBinder {
    protected final String TAG = this.getClass().getSimpleName();

    protected Context mContext;
    private final List<IPluginBindCallBack> callBackList = new ArrayList<>();
    private final List<IPluginEthernetCallback> ethernetCallbacksList = new ArrayList<>();

    public BasePluginBinder(Context mContext) {
        this.mContext = mContext;
    }

    @Override
    public void configBinder(Map<String, Object> args) {

    }

    @Override
    public void addBindCallBack(IPluginBindCallBack callBack) {
        if (callBackList.contains(callBack)) {
            return;
        }

        callBackList.add(callBack);
    }

    @Override
    public void removeBindCallBack(IPluginBindCallBack callBack) {
        if (!callBackList.contains(callBack)) {
            return;
        }
        callBackList.remove(callBack);
    }

    @Override
    public void addEthernetCallBack(IPluginEthernetCallback callback) {
        if (ethernetCallbacksList.contains(callback)) {
            return;
        }
        ethernetCallbacksList.add(callback);
    }

    @Override
    public void removeEthernetCallBack(IPluginEthernetCallback callback) {
        if (!ethernetCallbacksList.contains(callback)) {
            return;
        }
        ethernetCallbacksList.remove(callback);
    }

    @Override
    public void stop() {

    }

    @Override
    public void destroyBinder() {
        callBackList.clear();
        ethernetCallbacksList.clear();
    }

    protected void callBackBindResult(int code, String msg) {
        for (IPluginBindCallBack callback :
                callBackList) {
            callback.onBindResult(code, msg);
        }
    }

    protected void callBackEthernetResult(int status, int ethernetState) {
        for (IPluginEthernetCallback callback : ethernetCallbacksList) {
            callback.onEthernetCallback(status, ethernetState);
        }
    }
}
