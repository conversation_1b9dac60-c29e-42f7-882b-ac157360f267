package com.dinsafer.dincore.activtor;

/**
 * Created by Rinfon on 17/3/24.
 */
public class AddPlugsBuilder {
    String uid;
    String plugid;
    String messageid;
    String deviceToken;
    String plugName;
    String decodeid;
    String ipcData;
    String sirenSetting;
    boolean isOffical;
    String sType;

    public AddPlugsBuilder() {
    }

    public static AddPlugsBuilder newInstance() {
        return new AddPlugsBuilder();
    }

    public String getUid() {
        return uid;
    }

    public AddPlugsBuilder setUid(String uid) {
        this.uid = uid;
        return this;
    }

    public String getPlugid() {
        return plugid;
    }

    public AddPlugsBuilder setPlugid(String plugid) {
        this.plugid = plugid;
        return this;
    }

    public String getMessageid() {
        return messageid;
    }

    public AddPlugsBuilder setMessageid(String messageid) {
        this.messageid = messageid;
        return this;
    }

    public String getDeviceToken() {
        return deviceToken;
    }

    public AddPlugsBuilder setDeviceToken(String deviceToken) {
        this.deviceToken = deviceToken;
        return this;
    }

    public String getPlugName() {
        return plugName;
    }

    public AddPlugsBuilder setPlugName(String plugName) {
        this.plugName = plugName;
        return this;
    }

    public String getDecodeid() {
        return decodeid;
    }

    public AddPlugsBuilder setDecodeid(String decodeid) {
        this.decodeid = decodeid;
        return this;
    }

    public String getIpcData() {
        return ipcData;
    }

//    注意，ipcdata在添加的时候需要加密
//    if (!TextUtils.isEmpty(builder.getIpcData())) {
//                jsonObject.put("ipcdata", DDSecretUtil.getSC(builder.getIpcData()));
//            }
    public AddPlugsBuilder setIpcData(String ipcData) {
        this.ipcData = ipcData;
        return this;
    }

    public String getSirenSetting() {
        return sirenSetting;
    }

    public AddPlugsBuilder setSirenSetting(String sirenSetting) {
        this.sirenSetting = sirenSetting;
        return this;
    }

    public boolean isOffical() {
        return isOffical;
    }

    public AddPlugsBuilder setOffical(boolean offical) {
        isOffical = offical;
        return this;
    }

    public String getsType() {
        return sType;
    }

    public AddPlugsBuilder setsType(String sType) {
        this.sType = sType;
        return this;
    }
}
