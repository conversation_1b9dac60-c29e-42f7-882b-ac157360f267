package com.dinsafer.dincore.activtor.bean;

public class PluginActivtorException extends Throwable {

    public static int DEFAULT = 0;

    int code;

    String msg;

    public PluginActivtorException(String msg) {
        this.msg = msg;
    }

    public PluginActivtorException(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
