package com.dinsafer.dincore.activtor.api.base;

import com.dinsafer.dincore.activtor.bean.Plugin;

import java.util.Map;

/**
 *
 */
public interface IPluginBinder {
    void configBinder(Map<String, Object> args);

    void addBindCallBack(IPluginBindCallBack callBack);

    void removeBindCallBack(IPluginBindCallBack callBack);

    void addEthernetCallBack(IPluginEthernetCallback callback);
    void removeEthernetCallBack(IPluginEthernetCallback callback);

    void bindDevice(Plugin plugin);

    void stop();

    void destroyBinder();
}
