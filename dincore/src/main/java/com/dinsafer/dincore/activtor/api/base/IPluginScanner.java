package com.dinsafer.dincore.activtor.api.base;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import java.util.Map;

@Keep
public interface IPluginScanner {
    public static final String HOME_ID = "home_id";

    void scan(@NonNull String pluginID, String deviceId, Map<String, Object> args);

    void addScanCallBack(IPluginScanCallback callBack);

    void removeScanCallBack(IPluginScanCallback callBack);

    void destroyScanner();
}
