package com.dinsafer.dincore.activtor.api;

import static com.dinsafer.dssupport.plugin.PluginTypeHelper.TUYA_COLOR_LIGHT_PRODUCTID;
import static com.dinsafer.dssupport.plugin.PluginTypeHelper.TUYA_SMART_PLUGIN_PRODUCTID;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.dinsafe.Dinsafe;
import com.dinsafer.dincore.activtor.api.base.IPluginScanner;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginScanner;
import com.dinsafer.dincore.activtor.bean.Plugin;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.http.Api;
import com.dinsafer.dincore.utils.DDJSONUtil;
import com.dinsafer.dssupport.plugin.PluginConstants;
import com.dinsafer.dssupport.plugin.PluginTypeHelper;
import com.dinsafer.dssupport.utils.DDLog;

import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.Arrays;
import java.util.Map;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 有主机的配件扫描器
 */
public class PanelPluginScanner extends BasePluginScanner {

    private String mDeviceId;

    public PanelPluginScanner() {
    }

    public static IPluginScanner create() {
        return new PanelPluginScanner();
    }

    @Override
    public void scan(@NonNull @NotNull String pluginID, String deviceId, Map<String, Object> args) {
        this.plugin = new Plugin(pluginID);
        this.mDeviceId = deviceId;
        String homeId = null;
        if (args != null && args.size() > 0) {
            if (args.containsKey(HOME_ID)) {
                homeId = (String) args.get(HOME_ID);
            }
        }

        //         dcsam 二维码
        if (pluginID.startsWith("dscam_")) {
            this.plugin.setQrCode(pluginID);
            plugin.setPluginTypeName("dscam");
            callBackScanResult();
            return;

        }

        //         dsdoorbell 二维码
        if (pluginID.startsWith("dsdoorbell_")) {
            this.plugin.setQrCode(pluginID);
            plugin.setPluginTypeName("DSDOORBELL");
            callBackScanResult();
            return;
        }

        //         chime 二维码
        if (pluginID.startsWith("chime_")) {
            this.plugin.setQrCode(pluginID);
            plugin.setPluginTypeName("CHIME");
            callBackScanResult();
            return;
        }

        //         dcsam v006 二维码
        if (pluginID.startsWith("dscamv006_")) {
            this.plugin.setQrCode(pluginID);
            plugin.setPluginTypeName("DSCAM_V006");
            callBackScanResult();
            return;
        }

        //         dcsam v015 二维码
        if (pluginID.startsWith("dscamv015_")) {
            this.plugin.setQrCode(pluginID);
            plugin.setPluginTypeName("DSCAM_V015");
            callBackScanResult();
            return;
        }

        // 电池
        if (pluginID.startsWith("bmt_hp5000")) {
            this.plugin.setQrCode(pluginID);
            plugin.setPluginTypeName("HP5000");
            callBackScanResult();
            return;
        }

        // TODO 暂不支持5001-蓝牙UUID那些也还没有支持
        // if (pluginID.startsWith("bmt_hp5001")) {
        //     this.plugin.setQrCode(pluginID);
        //     plugin.setPluginTypeName("HP5001");
        //     callBackScanResult();
        //     return;
        // }

        if (pluginID.startsWith("PC1-BAK15-HS10")) {
            this.plugin.setQrCode(pluginID);
            plugin.setPluginTypeName("PC1-BAK15-HS10");
            callBackScanResult();
            return;
        }


        if (pluginID.startsWith("PS1-BAK10-HS10")) {
            this.plugin.setQrCode(pluginID);
            plugin.setPluginTypeName("PS1-BAK10-HS10");
            callBackScanResult();
            return;
        }

        if (pluginID.startsWith("VB1-BAK5-HS10")) {
            this.plugin.setQrCode(pluginID);
            plugin.setPluginTypeName("VB1-BAK5-HS10");
            callBackScanResult();
            return;
        }

        // TODO 主机-暂定二维码为16位
        if (16 == pluginID.length()) {
            this.plugin.setQrCode(pluginID);
            plugin.setPluginTypeName("PANEL");
            callBackScanResult();
            return;
        }

        if (plugin.getPluginID().startsWith("!I")) {
            thirdPartIPCScan(homeId);
            return;
        }

        if (plugin.getPluginID().startsWith("!")) {
            newQRCodeScan();
            return;
        }

        oldPluginAdd();
    }

    public void setDeviceId(String deviceId) {
        this.mDeviceId = deviceId;
    }

    private void oldPluginAdd() {
        if (!checkIsPlugs(plugin.getPluginID())) {
            callBackScanResult(ErrorCode.ACTIVTOR_ILLEGAID);
            return;
        }
        plugin.setPluginTypeName(PluginTypeHelper.getInstance().getSTypeByID(plugin.getPluginID()));
        callBackScanResult();
    }

    public boolean checkIsPlugs(String id) {
        try {
            String code = Dinsafe.str64ToHexStr(id);

            if (code.length() == 11 || code.length() == 15) {
                String type = code.substring(1, 3);
                int bigType = Integer.parseInt(code.substring(0, 1));
                if (bigType >= 0 && bigType <= 9) {
                    return PluginTypeHelper.getInstance().checkHasKey(type);
                }
            }
        } catch (Exception ex) {
            return false;
        }
        return false;

    }

    private void newQRCodeScan() {
        Api.getApi().getNewQRCodeScan(plugin.getPluginID(), mDeviceId)
                .enqueue(new Callback<ResponseBody>() {
                    @Override
                    public void onResponse(Call<ResponseBody> call, Response<ResponseBody> response) {
                        String json = "";
                        try {
                            json = response.body().string();
                            plugin.setSourceData(json);
                            DDLog.d(TAG, "onResponse: " + json);
                            if (!TextUtils.isEmpty(json) && json.startsWith("{")) {
                                JSONObject jsonObject = new JSONObject(json);
                                String oldCode;
                                if (jsonObject != null && !TextUtils.isEmpty(oldCode = DDJSONUtil.getString(jsonObject, "oldcode"))) {
                                    //TODO 配件旧码转换新二维码返回数据
                                    plugin.setQrCode(plugin.getPluginID());
                                    plugin.setPluginID(oldCode);
                                    oldPluginAdd();
                                    return;
                                }
                            }

                            if ("I".equals(String.valueOf(plugin.getPluginID().charAt(1)))) {

//                               because ipc data has encrypt
                                String result = null;
                                byte[] decode = crypt.decode(json.getBytes());
                                if (decode != null) {
                                    result = new String(decode);
                                }
                                if (TextUtils.isEmpty(result)) {
                                    result = json;
                                }
                                plugin.setPluginTypeName(PluginConstants.TYPE_1F);
                                plugin.setSourceData(result);
                                callBackScanResult();
                                return;
                            }
                            JSONObject jsonObject = new JSONObject(json);
                            if (DDJSONUtil.getInt(jsonObject, "dtype")
                                    == PluginConstants.CATEGORY_12) {
                                //涂鸦的增加事件
                                DDLog.d(TAG, "tuya plugin data: " + jsonObject.toString());

                                plugin.setSourceData(json);
                                /**
                                 * 因为：灯泡，插座分开单独开关了。所以这里先判断是属于哪个类别。通过productid去判断
                                 */

                                String productid = DDJSONUtil.getString(jsonObject, "productid");
                                if (Arrays.asList(TUYA_COLOR_LIGHT_PRODUCTID).contains(productid)) {
                                    plugin.setPluginTypeName(PluginConstants.NAME_TUYA_BULB);
                                    callBackScanResult();
                                    return;
                                }

                                if (Arrays.asList(TUYA_SMART_PLUGIN_PRODUCTID).contains(productid)) {
                                    plugin.setPluginTypeName(PluginConstants.NAME_TUYA_SMART_PLUGIN);
                                    callBackScanResult();
                                    return;
                                }
                                callBackScanResult(ErrorCode.ACTIVTOR_ILLEGAID);
                                return;
                            }

                            if (DDJSONUtil.getString(jsonObject, "stype").equals("12")) {
                                plugin.setPluginTypeName(PluginTypeHelper.getInstance().getSType("12"));
                                callBackScanResult();
                                return;
                            }

                            plugin.setPluginTypeName(PluginTypeHelper.getInstance()
                                    .getNameByBigIDAndSType(DDJSONUtil.getInt(jsonObject, "dtype"),
                                            DDJSONUtil.getString(jsonObject, "stype")));
                            callBackScanResult();
                        } catch (IOException | JSONException e) {
                            callBackScanResult(ErrorCode.ACTIVTOR_ILLEGAID);
                        }
                    }

                    @Override
                    public void onFailure(Call<ResponseBody> call, Throwable t) {
                        if ("message: code:403".equals(t.getMessage())) {
                            callBackScanResult(ErrorCode.ACTIVTOR_ALREAD_HAS_PLUGIN);
                        } else {
                            callBackScanResult(ErrorCode.ACTIVTOR_ILLEGAID);
                        }

                    }
                });
    }

    private void thirdPartIPCScan(String homeId) {
        Api.getApi().getNewQRCodetpIpcScan(plugin.getPluginID(), mDeviceId, homeId)
                .enqueue(new Callback<ResponseBody>() {
                    @Override
                    public void onResponse(Call<ResponseBody> call, Response<ResponseBody> response) {
                        String json = "";
                        try {
                            json = response.body().string();
                            plugin.setSourceData(json);
                            DDLog.d(TAG, "onResponse: " + json);
                            if (!TextUtils.isEmpty(json) && json.startsWith("{")) {
                                JSONObject jsonObject = new JSONObject(json);
                                String oldCode;
                                if (jsonObject != null && !TextUtils.isEmpty(oldCode = DDJSONUtil.getString(jsonObject, "oldcode"))) {
                                    //TODO 配件旧码转换新二维码返回数据
                                    plugin.setQrCode(plugin.getPluginID());
                                    plugin.setPluginID(oldCode);
                                    oldPluginAdd();
                                    return;
                                }
                            }

                            if ("I".equals(String.valueOf(plugin.getPluginID().charAt(1)))) {

//                               because ipc data has encrypt
                                String result = null;
                                byte[] decode = crypt.decode(json.getBytes());
                                if (decode != null) {
                                    result = new String(decode);
                                }
                                if (TextUtils.isEmpty(result)) {
                                    result = json;
                                }
                                plugin.setPluginTypeName(PluginConstants.TYPE_1F);
                                plugin.setSourceData(result);
                                callBackScanResult();
                                return;
                            }

                        } catch (IOException | JSONException e) {
                            callBackScanResult(ErrorCode.ACTIVTOR_ILLEGAID);
                        }
                    }

                    @Override
                    public void onFailure(Call<ResponseBody> call, Throwable t) {
                        plugin.setPluginTypeName(PluginConstants.TYPE_1F);
                        if ("message: code:400".equals(t.getMessage())) {
                            callBackScanResult(ErrorCode.ACTIVTOR_ALREAD_HAS_PLUGIN);
                        } else if ("message: code:416".equals(t.getMessage())) {
                            callBackScanResult(ErrorCode.ACTIVTOR_BIND_IPC_REACHED_LIMIT);
                        } else {
                            callBackScanResult(ErrorCode.ACTIVTOR_ILLEGAID);
                        }

                    }
                });
    }

}
