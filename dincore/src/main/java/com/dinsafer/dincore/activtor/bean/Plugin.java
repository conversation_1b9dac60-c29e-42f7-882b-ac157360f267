package com.dinsafer.dincore.activtor.bean;

import androidx.annotation.Keep;

import java.io.Serializable;

@Keep
public class Plugin implements Serializable {

    private String pluginID;

    private String qrCode;

    private String pluginName;

    private String pluginTypeName;

    private String sourceData;

    public Plugin(String pluginID) {
        this.pluginID = pluginID;
    }

    public String getPluginID() {
        return pluginID;
    }

    public void setPluginID(String pluginID) {
        this.pluginID = pluginID;
    }

    public String getPluginName() {
        return pluginName;
    }

    public void setPluginName(String pluginName) {
        this.pluginName = pluginName;
    }

    public String getPluginTypeName() {
        return pluginTypeName;
    }

    public String getQrCode() {
        return qrCode;
    }

    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }

    public void setPluginTypeName(String pluginTypeName) {
        this.pluginTypeName = pluginTypeName;
    }

    public String getSourceData() {
        return sourceData;
    }

    public void setSourceData(String sourceData) {
        this.sourceData = sourceData;
    }


    @Override
    public String toString() {
        return "Plugin{" +
                "pluginID='" + pluginID + '\'' +
                ", qrCode='" + qrCode + '\'' +
                ", pluginName='" + pluginName + '\'' +
                ", pluginTypeName='" + pluginTypeName + '\'' +
                ", sourceData='" + sourceData + '\'' +
                '}';
    }
}
