package com.dinsafer.dincore.activtor.api.base.impl;

import android.content.Context;
import android.text.TextUtils;

import com.dinsafer.dincore.activtor.api.base.IPluginActivator;
import com.dinsafer.dincore.activtor.api.base.IPluginBindCallBack;
import com.dinsafer.dincore.activtor.api.base.IPluginEthernetCallback;
import com.dinsafer.dincore.activtor.api.base.IPluginScanCallback;
import com.dinsafer.dincore.activtor.bean.Plugin;

import java.util.ArrayList;
import java.util.List;

/**
 * 配件关联器基类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/8 2:15 PM
 */
public abstract class BasePluginActivator implements IPluginActivator, IPluginScanCallback, IPluginBindCallBack, IPluginEthernetCallback {
    protected final String TAG = this.getClass().getSimpleName();

    protected Context mContext;
    protected String mDeviceId, mDeviceToken;
    protected String mWifiSSID, mWifiPassword;

    protected BasePluginScanner mPluginScanner;
    protected BasePluginBinder mPluginBinder;
    private final List<IPluginScanCallback> mScanCallback = new ArrayList<>();
    private final List<IPluginBindCallBack> mBindCallback = new ArrayList<>();
    private final List<IPluginEthernetCallback> mEthernetCallback = new ArrayList<>();

    @Override
    public BasePluginScanner getPluginScanner() {
        return mPluginScanner;
    }

    @Override
    public BasePluginBinder getPluginBinder() {
        return mPluginBinder;
    }

    @Override
    public void setPluginBinder(BasePluginBinder binder) {
        this.mPluginBinder = binder;
    }

    @Override
    public void addBindCallBack(IPluginBindCallBack callBack) {
        if (mBindCallback.contains(callBack)) {
            return;
        }

        mBindCallback.add(callBack);
    }

    @Override
    public void removeBindCallBack(IPluginBindCallBack callBack) {
        if (!mBindCallback.contains(callBack)) {
            return;
        }

        mBindCallback.remove(callBack);
    }

    @Override
    public void addEthernetCallBack(IPluginEthernetCallback callback) {
        if (mEthernetCallback.contains(callback)) {
            return;
        }
        mEthernetCallback.add(callback);
    }

    @Override
    public void removeEthernetCallBack(IPluginEthernetCallback callback) {
        if (!mEthernetCallback.contains(callback)) {
            return;
        }
        mEthernetCallback.remove(callback);
    }

    @Override
    public void addScanCallBack(IPluginScanCallback callBack) {
        if (mScanCallback.contains(callBack)) {
            return;
        }

        mScanCallback.add(callBack);
    }

    @Override
    public void removeScanCallBack(IPluginScanCallback callBack) {
        if (!mScanCallback.contains(callBack)) {
            return;
        }

        mScanCallback.remove(callBack);
    }

    protected void callBackBindResult(int code, String msg) {
        for (IPluginBindCallBack callback :
                mBindCallback) {
            callback.onBindResult(code, msg);
        }
    }

    protected void callBackEthernetResult(int status, int ethernetState) {
        for (IPluginEthernetCallback callback : mEthernetCallback) {
            callback.onEthernetCallback(status, ethernetState);
        }
    }

    protected void callBackScanResult(int code, Plugin plugin) {
        for (IPluginScanCallback callback :
                mScanCallback) {
            callback.onScanResult(code, plugin);
        }
    }

    @Override
    public void setup(Context context, String deviceId, String deviceToken) {
        this.mContext = context.getApplicationContext();
        this.mDeviceId = deviceId;
        this.mDeviceToken = deviceToken;
    }

    @Override
    public void setup(Context context) {
        this.mContext = context.getApplicationContext();
    }

    @Override
    public void onBindResult(int code, String msg) {
        callBackBindResult(code, msg);
    }

    @Override
    public void onEthernetCallback(int status, int ethernetState) {
        callBackEthernetResult(status, ethernetState);
    }


    @Override
    public void onScanResult(int code, Plugin plugin) {
        callBackScanResult(code, plugin);
    }

    @Override
    public void stop() {
        if (mPluginBinder != null) {
            mPluginBinder.stop();
        }
    }

    @Override
    public void destroyActivator() {
        mBindCallback.clear();
        mEthernetCallback.clear();
        mScanCallback.clear();

        if (null != mPluginScanner) {
            mPluginScanner.destroyScanner();
            mPluginScanner = null;
        }
        if (null != mPluginBinder) {
            mPluginBinder.destroyBinder();
            mPluginBinder = null;
        }
    }

    @Override
    public boolean isHavePanel() {
        checkSetup();
        return TextUtils.isEmpty(mDeviceId) || TextUtils.isEmpty(mDeviceToken);
    }

    @Override
    public void setWifiSSID(String ssid) {
        this.mWifiSSID = ssid;
    }

    @Override
    public void setWifiPassword(String wifiPwd) {
        this.mWifiPassword = wifiPwd;
    }

    @Override
    public String getWifiSSID() {
        return mWifiSSID;
    }

    @Override
    public String getWifiPassword() {
        return mWifiPassword;
    }

    @Override
    public boolean isHadSetWifi() {
        return !TextUtils.isEmpty(mWifiSSID) && !TextUtils.isEmpty(mWifiPassword);
    }

    /**
     * 检查是否已经初始化
     */
    protected void checkSetup() {
        if (null == mContext) {
            throw new NullPointerException("Empty context, you must call setup method before scan or bind.");
        }
    }

}
