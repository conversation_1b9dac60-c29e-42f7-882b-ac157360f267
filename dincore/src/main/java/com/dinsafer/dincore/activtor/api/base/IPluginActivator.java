package com.dinsafer.dincore.activtor.api.base;

import android.content.Context;

import androidx.annotation.NonNull;

import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.activtor.api.base.impl.BasePluginScanner;
import com.dinsafer.dincore.activtor.bean.Plugin;

/**
 * 配件扫描+绑定管理
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/8 11:48 AM
 */
public interface IPluginActivator {
    void setup(Context context, String deviceId, String deviceToken);

    void setup(Context context);

    void scan(@NonNull String pluginID);

    void addScanCallBack(IPluginScanCallback callBack);

    void removeScanCallBack(IPluginScanCallback callBack);

    void addBindCallBack(IPluginBindCallBack callBack);

    void removeBindCallBack(IPluginBindCallBack callBack);

    void addEthernetCallBack(IPluginEthernetCallback callback);

    void removeEthernetCallBack(IPluginEthernetCallback callback);

    void bindDevice(Plugin plugin);

    BasePluginScanner getPluginScanner();

    BasePluginBinder getPluginBinder();

    void setPluginBinder(BasePluginBinder binder);

    void stop();

    void destroyActivator();

    /**
     * 是否有主机
     *
     * @return true 有主机
     */
    boolean isHavePanel();

    void setWifiSSID(String ssid);

    void setWifiPassword(String wifiPwd);

    String getWifiSSID();

    String getWifiPassword();

    boolean isHadSetWifi();
}
