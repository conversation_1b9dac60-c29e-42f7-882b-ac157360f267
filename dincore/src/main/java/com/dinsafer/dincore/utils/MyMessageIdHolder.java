package com.dinsafer.dincore.utils;

import com.dinsafer.dincore.utils.RandomStringUtils;

import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.Map;

/**
 * 判断是否自己消息的工具类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/12/7 11:58 上午
 */
public class MyMessageIdHolder {
    private final Map<String, String> mMessageIdMap = new HashMap<>();

    public String createMessageId(@NotNull final String cmd) {
        String messageId = RandomStringUtils.getMessageId();
        mMessageIdMap.put(cmd, messageId);
        return messageId;
    }

    public String removeMessageIdByCmd(@NotNull final String cmd) {
        return mMessageIdMap.remove(cmd);
    }

    /**
     * 是否自己的MessageId
     *
     * @param cmd
     * @param curMsgId
     * @return 1: 是自己的;
     * -1: 不是自己的;
     * 0:该cmd之前没有添加过messageId
     */
    public int isMyMessageId(@NotNull final String cmd, @NotNull final String curMsgId) {
        if (!mMessageIdMap.containsKey(cmd)) {
            return 0;
        }

        final String myMsgId = mMessageIdMap.get(cmd);
        if (null != myMsgId && myMsgId.equals(curMsgId)) {
//            mMessageIdMap.remove(cmd);
            return 1;
        }
        return -1;
    }

    public void cleanAllMessageId(){
        mMessageIdMap.clear();
    }
}
