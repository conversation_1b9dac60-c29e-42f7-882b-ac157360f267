package com.dinsafer.dincore.utils;

import androidx.annotation.Nullable;

import java.security.MessageDigest;

public abstract class MD5 {

    public static final int INT = 53;

    @Nullable
    public static byte[] get(byte[] btInput) {
        try {
            //信息摘要是安全的单向哈希函数，它接收任意大小的数据，并输出固定长度的哈希值。
            //注意：这里传入md5与MD5得到的结果是不一样的，传入md5得到的加密串 字母是小写的，传入MD5得到的加密串 字母是大写的。
            MessageDigest mdInst = MessageDigest.getInstance("md5");
            //MessageDigest对象通过使用 update方法处理数据， 使用指定的byte数组更新摘要
            mdInst.update(btInput);
            // 摘要更新之后，通过调用digest（）执行哈希计算，获得密文
            return mdInst.digest();
        } catch (Exception e) {
            return null;
        }
    }
}
