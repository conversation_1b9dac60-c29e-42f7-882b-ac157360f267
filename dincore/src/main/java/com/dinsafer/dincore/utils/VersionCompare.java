package com.dinsafer.dincore.utils;

import com.dinsafer.dssupport.utils.DDLog;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class VersionCompare implements Comparable<VersionCompare>{
    private final String TAG = VersionCompare.class.getSimpleName();
    private int major;
    private int minor;
    private int patch;
    private String preReleaseId;
    private Integer preReleaseNumber; // 使用Integer来允许null值
    private boolean isMatch;

    public VersionCompare(String versionString) {

        Pattern pattern = Pattern.compile("v(\\d+)\\.(\\d+)\\.(\\d+)(-([a-zA-Z]+)(\\.(\\d+))?)?");
        Matcher matcher = pattern.matcher(versionString);

        if (matcher.matches()) {
            isMatch = true;
            this.major = Integer.parseInt(matcher.group(1));
            this.minor = Integer.parseInt(matcher.group(2));
            this.patch = Integer.parseInt(matcher.group(3));

            if (matcher.group(5) != null) {
                this.preReleaseId = matcher.group(5);
                if (matcher.group(7) != null) {
                    this.preReleaseNumber = Integer.parseInt(matcher.group(7));
                }
            }
        } else {
            isMatch = false;
            DDLog.i(TAG,"Invalid version format: " + versionString);
        }
    }

    @Override
    public int compareTo(VersionCompare other) {
        if (isMatch) {
            if (this.major != other.major) {
                return Integer.compare(this.major, other.major);
            }
            if (this.minor != other.minor) {
                return Integer.compare(this.minor, other.minor);
            }
            if (this.patch != other.patch) {
                return Integer.compare(this.patch, other.patch);
            }
            if (this.preReleaseId == null && other.preReleaseId != null) {
                return 1; // 正式版本大于预发布版本
            }
            if (this.preReleaseId != null && other.preReleaseId == null) {
                return -1; // 预发布版本小于正式版本
            }
            if (!this.preReleaseId.equals(other.preReleaseId)) {
                // 预发布标识符不同，但具体比较逻辑可能依赖于应用需求
                // 这里简单返回字符串比较结果
                return this.preReleaseId.compareTo(other.preReleaseId);
            }
            if (this.preReleaseNumber == null && other.preReleaseNumber != null) {
                return -1; // 无预发布版本号视为低于有预发布版本号的版本
            }
            if (this.preReleaseNumber != null && other.preReleaseNumber == null) {
                return 1; // 有预发布版本号视为高于无预发布版本号的版本
            }
            if (this.preReleaseNumber != null && other.preReleaseNumber != null) {
                return Integer.compare(this.preReleaseNumber, other.preReleaseNumber);
            }
            // 如果所有部分都相同，则它们是相等的
            return 0;
        } else {
            return -1;
        }
    }
}
