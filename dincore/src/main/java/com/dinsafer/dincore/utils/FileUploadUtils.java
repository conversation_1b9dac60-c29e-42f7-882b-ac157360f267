package com.dinsafer.dincore.utils;

import android.text.TextUtils;
import android.util.Log;
import android.webkit.MimeTypeMap;

import com.dinsafer.dssupport.utils.DDLog;

import java.io.File;

public class FileUploadUtils {
    private static final String TAG = "FileUploadUtils";
    
    /**
     * 获取文件扩展名
     * @param filePath 文件路径
     * @return 文件扩展名（包含点号，如".jpg"）
     */
    public static String getFileExtension(String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return "";
        }
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filePath.length() - 1) {
            String substring = filePath.substring(lastDotIndex);
            //这里没有保存后缀名
            if (substring.contains(".image_cache")) {
                substring = ".jpg";
            }
            return substring;
        }
        return "";
    }
    public static File getFile(String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return null;
        }
        File file = new File(filePath);
        return file;
    }
    /**
     * 根据文件路径自动获取Content-Type
     * @param filePath 文件路径
     * @return MIME类型
     */
    public static String getContentTypeFromFile(String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return "application/octet-stream";
        }
        Log.i(TAG, "filePath: "+filePath);
        String extension = getFileExtension(filePath);
        Log.i(TAG, "extension: "+extension);
        if (!TextUtils.isEmpty(extension)) {
            // 去掉点号
            String ext = extension.substring(1);
            String mimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(ext);
            Log.i(TAG, "getContentTypeFromFile: "+mimeType);
            if (!TextUtils.isEmpty(mimeType)) {
                return mimeType;
            }
        }
        
        return "application/octet-stream";
    }
    
    /**
     * 获取文件信息
     * @param filePath 文件路径
     * @return 文件信息对象
     */
    public static FileInfo getFileInfo(String filePath, int typeCode) {
        if (TextUtils.isEmpty(filePath)) {
            return null;
        }
        
        File file = new File(filePath);
        if (!file.exists()) {
            return null;
        }
        
        FileInfo info = new FileInfo();
        info.filePath = filePath;
        info.fileName = file.getName();
        info.fileSize = file.length();
        info.extension = getFileExtension(filePath);
        info.contentType = getContentTypeFromFile(filePath);
        info.typeCode = typeCode;
        info.file = file;
        Log.i(TAG, "getFileInfo: "+info.toString());
        return info;
    }
    
    /**
     * 文件信息类
     */
    public static class FileInfo {
        public String filePath;      // 文件路径
        public String fileName;      // 文件名
        public long fileSize;        // 文件大小（字节）
        public String extension;     // 扩展名
        public String contentType;   // MIME类型
        public int typeCode;         // 类型编号
        public File file;            // File对象
        
        @Override
        public String toString() {
            return "FileInfo{" +
                    "fileName='" + fileName + '\'' +
                    ", fileSize=" + fileSize +
                    ", contentType='" + contentType + '\'' +
                    ", typeCode=" + typeCode +
                    ", extension='" + extension + '\'' +
                    '}';
        }
    }
}
