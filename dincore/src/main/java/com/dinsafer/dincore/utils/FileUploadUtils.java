package com.dinsafer.dincore.utils;

import android.text.TextUtils;
import android.webkit.MimeTypeMap;

import com.dinsafer.dssupport.utils.DDLog;

import java.io.File;

/**
 * 文件上传工具类
 * 用于获取文件上传相关的数据和处理
 * 
 * <AUTHOR> Assistant
 * @since 2025/7/16
 */
public class FileUploadUtils {
    private static final String TAG = "FileUploadUtils";
    
    /**
     * 获取文件扩展名
     * @param filePath 文件路径
     * @return 文件扩展名（包含点号，如".jpg"）
     */
    public static String getFileExtension(String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return "";
        }
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filePath.length() - 1) {
            return filePath.substring(lastDotIndex);
        }
        return "";
    }
    
    /**
     * 根据Content-Type确定文件类型参数
     * @param contentType MIME类型
     * @return 文件类型编号
     */
    public static int getFileTypeByContentType(String contentType) {
        if (TextUtils.isEmpty(contentType)) {
            return 5; // 默认类型
        }
        
        if (contentType.startsWith("image/")) {
            return 5; // 图片类型
        } else if (contentType.startsWith("video/")) {
            return 6; // 视频类型
        } else if (contentType.startsWith("audio/")) {
            return 7; // 音频类型
        } else if (contentType.equals("application/pdf")) {
            return 8; // PDF文档
        } else {
            return 9; // 其他文件类型
        }
    }
    public static File getFile(String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return null;
        }
        File file = new File(filePath);
        return file;
    }
    /**
     * 根据文件路径自动获取Content-Type
     * @param filePath 文件路径
     * @return MIME类型
     */
    public static String getContentTypeFromFile(String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return "application/octet-stream";
        }
        
        String extension = getFileExtension(filePath);
        if (!TextUtils.isEmpty(extension)) {
            // 去掉点号
            String ext = extension.substring(1);
            String mimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(ext);
            if (!TextUtils.isEmpty(mimeType)) {
                return mimeType;
            }
        }
        
        return "application/octet-stream";
    }
    
    /**
     * 检查文件是否为图片
     * @param filePath 文件路径
     * @return 是否为图片
     */
    public static boolean isImageFile(String filePath) {
        String contentType = getContentTypeFromFile(filePath);
        return contentType.startsWith("image/");
    }
    
    /**
     * 获取文件信息
     * @param filePath 文件路径
     * @return 文件信息对象
     */
    public static FileInfo getFileInfo(String filePath, int typeCode) {
        if (TextUtils.isEmpty(filePath)) {
            return null;
        }
        
        File file = new File(filePath);
        if (!file.exists()) {
            return null;
        }
        
        FileInfo info = new FileInfo();
        info.filePath = filePath;
        info.fileName = file.getName();
        info.fileSize = file.length();
        info.extension = getFileExtension(filePath);
        info.contentType = getContentTypeFromFile(filePath);
        info.typeCode = typeCode;
        info.file = file;
        return info;
    }
    
    /**
     * 文件信息类
     */
    public static class FileInfo {
        public String filePath;      // 文件路径
        public String fileName;      // 文件名
        public long fileSize;        // 文件大小（字节）
        public String extension;     // 扩展名
        public String contentType;   // MIME类型
        public int typeCode;         // 类型编号
        public File file;            // File对象
        
        @Override
        public String toString() {
            return "FileInfo{" +
                    "fileName='" + fileName + '\'' +
                    ", fileSize=" + fileSize +
                    ", contentType='" + contentType + '\'' +
                    ", typeCode=" + typeCode +
                    ", extension='" + extension + '\'' +
                    '}';
        }
    }
}
