package com.dinsafer.dincore.utils;

import com.qiniu.android.common.Zone;
import com.qiniu.android.storage.Configuration;
import com.qiniu.android.storage.UploadManager;

/**
 * 七牛文件上传管理器
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/7 5:03 PM
 */
public class QiNiuUploadManager {
    private UploadManager mUploadManager;

    private QiNiuUploadManager() {
        initUploadManager();
    }

    private static class Holder {
        private static final QiNiuUploadManager INSTANCE = new QiNiuUploadManager();
    }

    public static QiNiuUploadManager getInstance() {
        return Holder.INSTANCE;
    }

    public UploadManager getUploadManager() {
        return mUploadManager;
    }

    private void initUploadManager() {
        Configuration config = new Configuration.Builder()
                .chunkSize(256 * 1024)  //分片上传时，每片的大小。 默认256K
                .putThreshhold(512 * 1024)  // 启用分片上传阀值。默认512K
                .connectTimeout(60) // 链接超时。默认10秒
                .responseTimeout(60) // 服务器响应超时。默认60秒
                .zone(Zone.zone0) // 设置区域，指定不同区域的上传域名、备用域名、备用IP。默认 Zone.zone0
                .build();
// 重用uploadManager。一般地，只需要创建一个uploadManager对象
        mUploadManager = new UploadManager(config);
    }

}
