package com.dinsafer.dincore.utils;

import androidx.annotation.Keep;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;

import org.json.JSONObject;

import java.util.Map;

@Keep
public class MapUtils {
    public static Object get(Map map, String key, Object defaultValue) {
        Object v;
        return (((v = map.get(key)) != null) || map.containsKey(key))
                ? v
                : defaultValue;
    }

    public static Map fromJson(JSONObject jsonObject) {
//        Map tempResult = new Gson().fromJson(
//                jsonObject.toString(), new TypeToken<HashMap>() {
//                }.getType()
//        );


        return (Map) JSON.parse(jsonObject.toString());

    }

    public static String toJson(Map src) {
        Gson gson = new Gson();
//        Type gsonType = new TypeToken<HashMap>() {
//        }.getType();
//        String gsonString = gson.toJson(src, gsonType);
        return gson.toJson(src);
    }
}
