package com.dinsafer.dincore.utils;

import android.app.Activity;
import android.app.Application;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattService;
import android.bluetooth.le.ScanRecord;
import android.content.Context;
import android.content.Intent;
import android.os.Handler;

import androidx.annotation.Keep;

import android.text.TextUtils;


import com.clj.fastble.BleManager;
import com.clj.fastble.callback.BleGattCallback;
import com.clj.fastble.callback.BleMtuChangedCallback;
import com.clj.fastble.callback.BleNotifyCallback;
import com.clj.fastble.callback.BleScanCallback;
import com.clj.fastble.callback.BleWriteCallback;
import com.clj.fastble.data.BleDevice;
import com.clj.fastble.exception.BleException;
import com.clj.fastble.scan.BleScanRuleConfig;
import com.dinsafer.dincore.DinCore;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.msctlib.convert.DefaultConvert;
import com.dinsafer.dssupport.msctlib.msct.IConvert;
import com.dinsafer.dssupport.msctlib.utils.HexUtil;
import com.dinsafer.dssupport.utils.DDLog;


import org.json.JSONObject;

import java.lang.reflect.Method;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


/**
 *
 */
@Keep
public class BleHelper {
    private static String TAG = "BleHelper";

    public static final String BLE_KEY_CMD = "cmd";

    private static IConvert currentConvert = new DefaultConvert();

    public static final int BLE_SCAN_TIMEOUT = Integer.MAX_VALUE;
    public static final int BLE_CONNECT_TIMEOUT = 50 * 1000;
    public static final int BLE_OPERATE_TIMEOUT = 50 * 1000;

    private String mConnectedUUID = "";
    private String mServiceUuids;
    private String mWriteUuid, mNotifyUuid;

    private List<BleHelper.IMessageCallback> messageCallbackList = new ArrayList<>();
    private List<ConnectCallback> connectCallbacks = new ArrayList<>();

    private static class Holder {
        static BleHelper instance = new BleHelper();
    }

    public static BleHelper getInstance() {
        return Holder.instance;
    }


    public static void init(Application application) {
        BleManager.getInstance().init(application);
        BleManager.getInstance()
                .enableLog(DinCore.getInstance().getDebugMode())
                .setReConnectCount(2, 5000)
                .setConnectOverTime(BLE_CONNECT_TIMEOUT)
                .setOperateTimeout(BLE_OPERATE_TIMEOUT);
    }

    public static boolean isBleOpen() {
        return BleManager.getInstance().isBlueEnable();
    }

    public static void openBle(Context context, int requestCode) {
        Intent enableIntent = new Intent(
                BluetoothAdapter.ACTION_REQUEST_ENABLE);
        enableIntent.putExtra(BluetoothAdapter.EXTRA_DISCOVERABLE_DURATION, 300);
        ((Activity) context).startActivityForResult(enableIntent, requestCode);
    }

    /**
     * 扫描主机
     */
    public void scanDevice(BleScanCallback bleScanCallback) {
        checkInitScanRule();
        BleManager.getInstance().scanCustom(bleScanCallback);
    }

    private void checkInitScanRule() {
        if (TextUtils.isEmpty(mServiceUuids) || TextUtils.isEmpty(mWriteUuid) || TextUtils.isEmpty(mNotifyUuid)) {
            throw new NullPointerException("Bluetooth's uuid is null, you must call method setScanRuleWithUUID first.");
        }
    }

    public void setScanRuleWithUUID(long bleScanTime, String serviceUuids, String writeUuid, String notifyUuid) {
        DDLog.i(TAG, "setScanRuleWithUUID, bleScanTime: " + bleScanTime);
        this.mServiceUuids = serviceUuids;
        this.mWriteUuid = writeUuid;
        this.mNotifyUuid = notifyUuid;

        if (bleScanTime <= 0) {
            bleScanTime = BLE_SCAN_TIMEOUT;
        }
        String[] uuidStrArray = mServiceUuids.split(",");
        UUID[] uuids = new UUID[uuidStrArray.length];
        for (int i = 0; i < uuidStrArray.length; i++) {
            uuids[i] = UUID.fromString(uuidStrArray[i]);
            DDLog.i(TAG, "uuids " + (i + 1) + "===" + uuids[i]);
        }
//        UUID[] uuids = new UUID[1];
//        uuids[0] = UUID.fromString(mServiceUuids);
        BleScanRuleConfig scanRuleConfig = new BleScanRuleConfig.Builder()
                .setServiceUuids(uuids)
                .setScanTimeOut(bleScanTime)
                .build();
        BleManager.getInstance().initScanRule(scanRuleConfig);
    }

    public void setScanRuleWithUUID(String serviceUuids, String writeUuid, String notifyUuid) {
        int bleScanTime = 0;
        DDLog.i(TAG, "setScanRuleWithUUID, bleScanTime: " + bleScanTime);
        this.mServiceUuids = serviceUuids;
        this.mWriteUuid = writeUuid;
        this.mNotifyUuid = notifyUuid;

        if (bleScanTime <= 0) {
            bleScanTime = BLE_SCAN_TIMEOUT;
        }
        String[] uuidStrArray = mServiceUuids.split(",");
        UUID[] uuids = new UUID[uuidStrArray.length];
        for (int i = 0; i < uuidStrArray.length; i++) {
            uuids[i] = UUID.fromString(uuidStrArray[i]);
            DDLog.i(TAG, "uuids " + (i + 1) + "===" + uuids[i]);
        }
//        UUID[] uuids = new UUID[1];
//        uuids[0] = UUID.fromString(mServiceUuids);
        BleScanRuleConfig scanRuleConfig = new BleScanRuleConfig.Builder()
                .setServiceUuids(uuids)
                .setScanTimeOut(bleScanTime)
                .build();
        BleManager.getInstance().initScanRule(scanRuleConfig);
    }

    public String getConnectedUUID() {
        return mConnectedUUID;
    }

    /**
     * 返回主机的状态：可添加、离线、已添加
     * 状态位 ：两位(是否为新机 1、是否在线 1)
     * 已添加优先于离线
     */

//    public static final int BLE_DEVICE_STATUS_NORMAL = 0;
//
//    public static final int BLE_DEVICE_STATUS_OFFLINE_OLD = 1;
//
//    public static final int BLE_DEVICE_STATUS_ADDED = 2;

//    public static int getBleDeviceStatus(byte[] scanRecordByte) {
//        /**
//         * 通过ScanRecord,获取到他的ServiceData，获取到前两位
//         * 第一个 1代表新主机  0代表旧主机
//         * 第二位 1代表在线 0代表离线
//         */
//        ScanRecord scanRecord = parseScanRecordFromBytes(scanRecordByte);
//        if (scanRecord.getServiceData().size() <= 0) {
//            return BLE_DEVICE_STATUS_OFFLINE_OLD;
//        }
//        byte[] data = scanRecord.getServiceData().entrySet().iterator().next().getValue();
//        char[] dataChar = String.format("%08d", Integer.valueOf(new BigInteger(1, data).toString(2))).toCharArray();
//        MsctLog.i(TAG, "service data is" + String.format("%08d", Integer.valueOf(new BigInteger(1, data).toString(2))));
//        int newStatus = Character.getNumericValue(dataChar[0]);
//        int onlineStatus = Character.getNumericValue(dataChar[1]);
//
//        /**
//         * 新主机
//         */
//        boolean isNew = newStatus == 1 ? true : false;
//        if (isNew) {
//            return BLE_DEVICE_STATUS_NORMAL;
//        }
//
//
//        boolean isOnline = onlineStatus == 1 ? true : false;
//        if (!isOnline) {
//            //旧主机 未添加 已离线'
//            return BLE_DEVICE_STATUS_OFFLINE_OLD;
//        }
//
//        //旧主机 未添加 在线
//        return BLE_DEVICE_STATUS_NORMAL;
//
//    }
    public static ScanRecord parseScanRecordFromBytes(byte[] bytes) {
        try {
            Method parseFromBytes = ScanRecord.class.getMethod("parseFromBytes", byte[].class);
            return (ScanRecord) parseFromBytes.invoke(null, (Object) bytes);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 断开所有已连接的设备
     */
    public void disconnectAllDevice() {
        if (getConnectedDevice() != null) {
            BleManager.getInstance().disconnect(getConnectedDevice());
        }
        BleManager.getInstance().disconnectAllDevice();
        deleteCache();
        MsctLog.i(TAG, "disconnectAllDevice");
    }

    public void release() {
        cancelScan();
        stopNotify();
        messageCallbackList.clear();
        connectCallbacks.clear();
    }

    /**
     * 取消扫描
     */
    public void cancelScan() {
        //没有start scan去cancel scan的话会出现异常
        try {
            BleManager.getInstance().cancelScanCustom();
        } catch (Exception ignore) {
            ignore.printStackTrace();
        }
    }

    /**
     * 删除Ble缓存
     */
    private static void deleteCache() {
        MsctLog.i(TAG, "deleteCache");
        if (BleManager.getInstance().getAllConnectedDevice() != null
                && BleManager.getInstance().getAllConnectedDevice().size() > 0) {
            MsctLog.i(TAG, "BleManager.newInstance().getAllConnectedDevice().size() > 0");
            refreshGattCache(BleManager.getInstance().getBluetoothGatt(getConnectedDevice()));
        }
    }

    public static boolean refreshGattCache(BluetoothGatt gatt) {
        boolean result = false;
        try {
            if (gatt != null) {
                Method refresh = BluetoothGatt.class.getMethod("refresh");
                if (refresh != null) {
                    refresh.setAccessible(true);
                    result = (boolean) refresh.invoke(gatt, new Object[0]);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }

    public static BleDevice getConnectedDevice() {
        if (BleManager.getInstance().getAllConnectedDevice().size() <= 0) {
            return null;
        }
        return BleManager.getInstance().getAllConnectedDevice().get(0);
    }

    /**
     * 连接设备
     */
    static boolean isFail = false;

    public void connected(BleDevice bleDevice, IConvert convert) {
        disconnectAllDevice();
        if (convert != null) {
            currentConvert = convert;
        }
        BleManager.getInstance().connect(bleDevice, new BleGattCallback() {
            @Override
            public void onStartConnect() {
                MsctLog.i(TAG, "start connect");
                // 开始连接
                isFail = false;
                for (ConnectCallback connectCallback : connectCallbacks) {
                    connectCallback.onStartConnect();
                }
            }

            @Override
            public void onConnectFail(BleDevice bleDevice, BleException exception) {
                MsctLog.d(TAG, "connect fail " + exception.toString());
                // 连接失败
                if (isFail) {
                    return;
                }
                isFail = true;

                for (ConnectCallback connectCallback : connectCallbacks) {
                    connectCallback.onConnectFail(exception.toString());
                }

            }

            @Override
            public void onConnectSuccess(BleDevice bleDevice, BluetoothGatt gatt, int status) {
                MsctLog.i(TAG, "connect success,start set mtu");
                for (BluetoothGattService service : gatt.getServices()) {
                    String uuid = String.valueOf(service.getUuid());
                    MsctLog.i(TAG, "uuid===" + uuid);
                    if (!TextUtils.isEmpty(uuid) &&
                            mServiceUuids.toLowerCase().contains(uuid.toLowerCase())) {
                        mConnectedUUID = uuid;
                        break;
                    }
                }
                // 连接成功，BleDevice即为所连接的BLE设备
                BleManager.getInstance().setMtu(bleDevice, 512, new BleMtuChangedCallback() {
                    @Override
                    public void onSetMTUFailure(BleException exception) {
                        // 设置MTU失败
                        MsctLog.d(TAG, "set mtu fail:" + exception.toString());
                        for (ConnectCallback connectCallback : connectCallbacks) {
                            connectCallback.onConnectFail(exception.toString());
                        }

                    }

                    @Override
                    public void onMtuChanged(int mtu) {
                        // 设置MTU成功，并获得当前设备传输支持的MTU值
                        MsctLog.i(TAG, "set mtu success，get current mtu:" + mtu);

                        for (ConnectCallback connectCallback : connectCallbacks) {
                            connectCallback.onConnectSuccess();
                        }

                    }
                });

                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        openNotify(new NotifyCallback() {
                            @Override
                            public void onNotifySuccess() {

                            }

                            @Override
                            public void onNotifyFailure() {

                            }

                            @Override
                            public void onCharacteristicChanged(byte[] notifyData) {
                                for (IMessageCallback iMessageCallback : messageCallbackList) {
                                    iMessageCallback.onMessage(notifyData);
                                }
                            }
                        });

                    }
                }, 500);

            }

            @Override
            public void onDisConnected(boolean isActiveDisConnected, BleDevice bleDevice, BluetoothGatt gatt, int status) {
                MsctLog.d(TAG, "ble disconnected,isActiveDisConnected " + isActiveDisConnected + " ,status is " + status);
                // 连接中断，isActiveDisConnected表示是否是主动调用了断开连接方法\
                /**
                 * 如果是主动断开，即连接完成的时候等。就不必弹出主机蓝牙断开窗口
                 * 如果是手机蓝牙没开的情况也是
                 */
                if (isActiveDisConnected) {
                    return;
                }
//                if (!isBleOpen()) {
//                    return;
//                }
//                EventBus.getDefault().post(new BleConnectStopEvent());
                for (ConnectCallback connectCallback : connectCallbacks) {
                    connectCallback.onDisConnected();
                }
                BleManager.getInstance().disconnect(bleDevice);
            }
        });
    }

    public void stopNotify() {
        BleManager.getInstance().stopNotify(
                getConnectedDevice(),
                mConnectedUUID,
                mNotifyUuid);
    }


    public void openNotify(final NotifyCallback notifyCallback) {
        if (!isHasDeviceConnect()) {
            return;
        }

        BleManager.getInstance().notify(
                getConnectedDevice(),
                mConnectedUUID,
                mNotifyUuid,
                new BleNotifyCallback() {
                    @Override
                    public void onNotifySuccess() {
                        // 打开通知操作成功
                        MsctLog.i(TAG, "open notify success");
                        if (notifyCallback != null) {
                            notifyCallback.onNotifySuccess();
                        }

                    }

                    @Override
                    public void onNotifyFailure(BleException exception) {
                        // 打开通知操作失败
                        MsctLog.d(TAG, "open notify error, " + exception);
                        if (notifyCallback != null) {
                            notifyCallback.onNotifyFailure();
                        }

                    }

                    @Override
                    public void onCharacteristicChanged(byte[] data) {
                        // 打开通知后，设备发过来的数据将在这里出现
//                        NON hex 之后是：4E4F4E
                        String tmp = HexUtil.byte2hex(data);
                        MsctLog.i(TAG, "receive data after hex:" + tmp);
                        if (tmp.equals("4E4F4E")) {
//                            不知道为什么一直收到non
                            MsctLog.e(TAG, "receive a NON，drop");
                            return;
                        }


                        byte[] handleData = currentConvert.decode(data);

                        if (handleData != null) {
//                            MsctLog.i(TAG, "receivedata:" + handleData);
                            if (notifyCallback != null) {
                                notifyCallback.onCharacteristicChanged(handleData);
                            }
                        }
                    }
                });
    }

    public static boolean isHasDeviceConnect() {
        return BleManager.getInstance().getAllConnectedDevice().size() > 0;
    }


    public void write(byte[] msg, final IBleSendCallBack callBack) {
        if (!isHasDeviceConnect()) {
            if (callBack != null) {
                callBack.onError(-1, "no device connect");
            }
            MsctLog.d(TAG, "write: not device connect,skip write!!!");
            return;
        }
        MsctLog.i(TAG, "write before data：" + HexUtil.byte2hex(msg));

        byte[] bytes = currentConvert.encode(msg);

        if (BleManager.getInstance().getAllConnectedDevice().isEmpty()) {
            // FIXME: 2019/2/24 断开处理
            MsctLog.d(TAG, "write: not device connect,skip write!");
            return;
        }

//        MsctLog.d(TAG, "data：" + jsonObject.toString());
        MsctLog.i(TAG, "write data：" + bytes.length);

        BleManager.getInstance().write(getConnectedDevice(), mConnectedUUID, mWriteUuid,
                bytes, false, new BleWriteCallback() {
                    @Override
                    public void onWriteSuccess(int current, int total, byte[] justWrite) {
                        if (callBack != null) {
                            callBack.onSuccess();
                        }
                        MsctLog.d(TAG, "send success");
                    }

                    @Override
                    public void onWriteFailure(final BleException exception) {
                        MsctLog.d(TAG, "send fail:" + exception.toString());
                        if (exception.getCode() == 102) {
                            connected(getConnectedDevice(), currentConvert);
                        }
                        if (callBack != null) {
                            callBack.onError(exception.getCode(), exception.getDescription());
                        }
                    }
                });
    }


    public void write(JSONObject jsonObject, final IBleSendCallBack callBack) {

        if (!isHasDeviceConnect()) {
            if (callBack != null) {
                callBack.onError(-1, "no device connect");
            }
            MsctLog.d(TAG, "write: not device connect,skip write!!!");
            return;
        }

        byte[] bytes = currentConvert.encode(jsonObject.toString().getBytes());

        if (BleManager.getInstance().getAllConnectedDevice().isEmpty()) {
            // FIXME: 2019/2/24 断开处理
            MsctLog.d(TAG, "write: not device connect,skip write!");
            return;
        }

//        MsctLog.d(TAG, "data：" + jsonObject.toString());
        MsctLog.i(TAG, "data：" + bytes.length);

        BleManager.getInstance().write(getConnectedDevice(), mConnectedUUID,
                mWriteUuid, bytes, false, new BleWriteCallback() {
                    @Override
                    public void onWriteSuccess(int current, int total, byte[] justWrite) {
                        if (callBack != null) {
                            callBack.onSuccess();
                        }
                        MsctLog.d(TAG, "send success");
                    }

                    @Override
                    public void onWriteFailure(final BleException exception) {
                        MsctLog.d(TAG, "send fail:" + exception.toString());
                        if (exception.getCode() == 102) {
                            connected(getConnectedDevice(), currentConvert);
                        }
                        if (callBack != null) {
                            callBack.onError(exception.getCode(), exception.getDescription());
                        }
                    }
                });
    }

    /**
     * 字符串转换成为16进制(无需Unicode编码)
     *
     * @param str
     * @return
     */
    public static String str2HexStr(String str) {
        char[] chars = "0123456789ABCDEF".toCharArray();
        StringBuilder sb = new StringBuilder("");
        byte[] bs = str.getBytes();
        int bit;
        for (int i = 0; i < bs.length; i++) {
            bit = (bs[i] & 0x0f0) >> 4;
            sb.append(chars[bit]);
            bit = bs[i] & 0x0f;
            sb.append(chars[bit]);
            // sb.append(' ');
        }
        return sb.toString().trim();
    }

    public void addConnectCallBack(ConnectCallback callback) {
        if (connectCallbacks.contains(callback)) {
            return;
        }

        connectCallbacks.add(callback);

    }

    public void removeConnectCallBack(IMessageCallback callback) {
        connectCallbacks.remove(callback);
    }

    public void addMessageCallBack(IMessageCallback callback) {
        if (messageCallbackList.contains(callback)) {
            return;
        }
        messageCallbackList.add(callback);
    }

    public void removeMessageCallBack(IMessageCallback callback) {
        messageCallbackList.remove(callback);
    }


    public static Boolean isNewDevice(BleDevice bleDevice) {

        /**
         * 通过ScanRecord,获取到他的ServiceData，获取到前两位
         * 第一个 1代表新主机  0代表旧主机
         */
        ScanRecord scanRecord = parseScanRecordFromBytes(bleDevice.getScanRecord());
        if (scanRecord.getServiceData().size() <= 0) {
            return false;
        }
        byte[] data = scanRecord.getServiceData().entrySet().iterator().next().getValue();
        char[] dataChar = String.format("%08d", Integer.valueOf(new BigInteger(1, data).toString(2))).toCharArray();
        MsctLog.d(TAG, "service data is" + dataChar.toString());
        int newStatus = Character.getNumericValue(dataChar[0]);

        /**
         * 新主机
         */
        boolean isNew = newStatus == 1 ? true : false;
        return isNew;
    }

    @Keep
    public interface ConnectCallback {
        void onStartConnect();

        void onConnectFail(String message);

        void onConnectSuccess();

        void onDisConnected();
    }


    public interface NotifyCallback {
        void onNotifySuccess();

        void onNotifyFailure();

        void onCharacteristicChanged(byte[] notifyData);
    }

    @Keep
    public interface IMessageCallback {
        void onMessage(byte[] notifyData);
    }


}
