package com.dinsafer.dincore;

import android.app.Application;

import androidx.annotation.Keep;

import com.alibaba.sdk.android.httpdns.HttpDns;
import com.alibaba.sdk.android.httpdns.HttpDnsService;
import com.dinsafer.dincore.db.cache.CacheHelper;
import com.dinsafer.dincore.http.Api;
import com.dinsafer.dincore.http.BmtStatsApi;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.dincore.user.api.IUser;
import com.dinsafer.dincore.utils.AppStateEvent;
import com.dinsafer.dincore.utils.AppStateTracker;
import com.dinsafer.dincore.utils.BleHelper;
import com.dinsafer.dssupport.msctlib.DSSDK;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.dssupport.utils.DDLog;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.Arrays;

@Keep
public class DinCore {
    Application application;
    String domain;
    String appID;
    String appSecret;
    boolean debugMode;
    String helioCamSecret;
    //    IP:PORT
    String udpAddress;

    private static HttpDnsService httpdns;

    private static volatile DinCore instance;
    private String e2eDomain;
    private int e2ePort;
    private String e2eHelpDomain;
    private int e2eHelpDomainPort;

    // 是否cawa兼容模式
    private boolean compatMode;

    // bmt 域名
    private String bmtDomain;

    public static DinCore getInstance() {
        return instance;
    }

    public static HttpDnsService getHttpdns() {
        return httpdns;
    }

    public static IUser getUserInstance() {
        return UserManager.getInstance();
    }


    public String getDomain() {
        return domain;
    }

    public String getAppID() {
        return appID;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public boolean getDebugMode() {
        return debugMode;
    }

    public String getHelioCamSecret() {
        return helioCamSecret;
    }

    public boolean isCompatMode() {
        return compatMode;
    }

    public String getUdpAddress() {
        return udpAddress;
    }

    public void setUdpAddress(String udpAddress) {
        this.udpAddress = udpAddress;
    }

    public void setHelioCamSecret(String helioCamSecret) {
        this.helioCamSecret = helioCamSecret;
    }

    public void setE2eDomain(String e2eDomain) {
        this.e2eDomain = e2eDomain;
        DSSDK.getInstance().setDomain(e2eDomain);
    }

    public void setE2ePort(int e2ePort) {
        this.e2ePort = e2ePort;
        DSSDK.getInstance().setPort(e2ePort);
    }

    public void setE2eHelpDomain(String e2eHelpDomain) {
        this.e2eHelpDomain = e2eHelpDomain;
        DSSDK.getInstance().setHelpDomain(e2eHelpDomain);
    }

    public void setE2eHelpDomainPort(int e2eHelpDomainPort) {
        this.e2eHelpDomainPort = e2eHelpDomainPort;
        DSSDK.getInstance().setHelpPort(e2eHelpDomainPort);
    }

    public int getE2eHelpDomainPort() {
        return e2eHelpDomainPort;
    }

    public String getE2eHelpDomain() {
        return e2eHelpDomain;
    }

    public String getBmtDomain() {
        return bmtDomain;
    }

    public void setBmtDomain(String bmtDomain) {
        this.bmtDomain = bmtDomain;
    }

    public Application getApplication() {
        return application;
    }

    public void destory() {
    }

    public static final class DinCoreBuilder {
        private Application application;
        private String domain;
        private String appID;
        private String appSecret;
        private boolean debugMode;
        private String tuyaKey;
        private String tuyaSecret;
        private String helioCamSecret;
        private String udpAddress;
        private String e2eDomain;
        private int e2ePort;
        private String e2eHelpDomain;
        private int e2eHelpDomainPort;
        // 是否cawa兼容模式
        private boolean compatMode = false;
        private String bmtDomain;

        private DinCoreBuilder() {
        }

        public static DinCoreBuilder create() {
            return new DinCoreBuilder();
        }

        public DinCoreBuilder withApplication(Application application) {
            this.application = application;
            return this;
        }

        public DinCoreBuilder withDomain(String domain) {
            this.domain = domain;
            return this;
        }

        public DinCoreBuilder withAppID(String appID) {
            this.appID = appID;
            return this;
        }

        public DinCoreBuilder withAppSecret(String appSecret) {
            this.appSecret = appSecret;
            return this;
        }

        public DinCoreBuilder withDebugMode(boolean debugMode) {
            this.debugMode = debugMode;
            return this;
        }


        public DinCoreBuilder withHelioCamSecret(String secret) {
            this.helioCamSecret = secret;
            return this;
        }

        public DinCoreBuilder withUDPAddress(String ipPort) {
            this.udpAddress = ipPort;
            return this;
        }

        public DinCoreBuilder withE2eDomain(String e2eDomain) {
            this.e2eDomain = e2eDomain;
            return this;
        }

        public DinCoreBuilder withE2ePort(int e2ePort) {
            this.e2ePort = e2ePort;
            return this;
        }


        public DinCoreBuilder withE2eHelpDomain(String e2eHelpDomain) {
            this.e2eHelpDomain = e2eHelpDomain;
            return this;
        }

        public DinCoreBuilder withE2eHelpPor(int ipPort) {
            this.e2eHelpDomainPort = ipPort;
            return this;
        }

        public DinCoreBuilder withCompatMode(boolean compatMode) {
            this.compatMode = compatMode;
            return this;
        }

        public DinCoreBuilder withBmtDomain(String bmtDomain) {
            this.bmtDomain = bmtDomain;
            return this;
        }


        private void initHttp(DinCore dinCore, Application application) {
            httpdns = HttpDns.getService(application, "160011", "d180fdf58e9c03ca8894f700441d8b44");
//        httpdns = HttpDns.getService(context, "accoutid", "sec");
            httpdns.setHTTPSRequestEnabled(true);
//            TODO IPC 付费域名
            httpdns.setPreResolveHosts(new ArrayList<>(Arrays.asList(dinCore.getDomain())));
            // 允许过期IP以实现懒加载策略
            httpdns.setExpiredIPEnabled(false);
            httpdns.setCachedIPEnabled(false);
            Api.init(dinCore);
            BmtStatsApi.init(dinCore);
        }


        public DinCore build() {
            DinCore dinCore = new DinCore();
            dinCore.appID = this.appID;
            dinCore.application = this.application;
            dinCore.debugMode = this.debugMode;
            dinCore.appSecret = this.appSecret;
            dinCore.domain = this.domain;
            dinCore.helioCamSecret = this.helioCamSecret;
            dinCore.udpAddress = this.udpAddress;
            dinCore.e2eDomain = this.e2eDomain;
            dinCore.e2ePort = this.e2ePort;
            dinCore.e2eHelpDomain = this.e2eHelpDomain;
            dinCore.e2eHelpDomainPort = this.e2eHelpDomainPort;
            dinCore.compatMode = this.compatMode;
            dinCore.bmtDomain = this.bmtDomain;
            instance = dinCore;
            DSSDK.builder().appId(this.appID)
                    .appSecret(this.appSecret)
                    .application(this.application)
//                    这个跟上面的domain是不一样的，这里的是udp服务器
                    .helpDomain(dinCore.e2eHelpDomain)
                    .helpPort(dinCore.e2eHelpDomainPort)
                    .domain(dinCore.e2eDomain)
                    .port(dinCore.e2ePort)
                    .build();
            DDLog.setDebug(debugMode);
            MsctLog.initLog(debugMode);
            initHttp(dinCore, application);
            BleHelper.init(this.application);
            CacheHelper.init(application);
            initAppStateTracker(application);
            return dinCore;
        }

        private void initAppStateTracker(Application application) {
            AppStateTracker.track(application, new AppStateTracker.AppStateChangeListener() {
                @Override
                public void appTurnIntoForeground() {
                    // 处理app到前台的逻辑
                    DDLog.i("apptracker", "app is fore");
                    EventBus.getDefault().post(new AppStateEvent(true));
                }

                @Override
                public void appTurnIntoBackGround() {
                    // app处理到到后台的逻辑
                    DDLog.i("apptracker", "app is background");
                    EventBus.getDefault().post(new AppStateEvent(false));
                }
            });
        }
    }
}
