package com.dinsafer.dincore.user.bean;

import androidx.annotation.Keep;

/**
 * Created by Rinfon on 16/8/23.
 */
@Keep
public class LogoutEvent {

    // 是否需要清除缓存中User的信息
    // 如果DinCore主动调用退出登录接口，成功会主动清除，所以无需再清除
    private final boolean needCleanCache;

    public LogoutEvent() {
        needCleanCache = true;
    }

    public LogoutEvent(boolean needCleanCache) {
        this.needCleanCache = needCleanCache;
    }

    public boolean isNeedCleanCache() {
        return needCleanCache;
    }
}
