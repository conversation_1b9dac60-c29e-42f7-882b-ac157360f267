package com.dinsafer.dincore.user;

import androidx.annotation.Keep;
import androidx.annotation.Nullable;
import android.text.TextUtils;

import com.dinsafer.dincore.common.CommonCmdEvent;
import com.dinsafer.dincore.db.DBKey;
import com.dinsafer.dincore.user.api.ILoginCallback;
import com.dinsafer.dincore.user.api.IUser;
import com.dinsafer.dincore.user.bean.DinUser;
import com.dinsafer.dincore.user.bean.DinUserLoginResponse;
import com.dinsafer.dssupport.msctlib.db.KV;
import com.google.gson.Gson;

import org.greenrobot.eventbus.EventBus;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/3/21 3:29 下午
 */
abstract class BaseUserManager implements IUser {
    protected static final String TAG = BaseUserManager.class.getSimpleName();

    public static final int PHONE = 1;

    public static final int EMAIL = 0;

    public static final int UID = 2;

    protected final UserRepository userRepository = new UserRepository();

    protected DinUser mUser;

    @Nullable
    @Override
    public DinUser getUser() {
        synchronized (this) {
            if (mUser == null) {
                String userString = KV.getString(DBKey.KEY_USER_INFO, "");
                if (!TextUtils.isEmpty(userString)) {
                    this.mUser = new Gson().fromJson(userString, DinUser.class);
                } else {
                    mUser = new DinUser();
                }
            }
            return mUser;
        }
    }

    public void setUser(DinUser mUser) {
        synchronized (this) {
            this.mUser = mUser;
        }
    }

    @Override
    public boolean isLogin() {
        return getUser() != null && !TextUtils.isEmpty(mUser.getUser_id());
    }

    @Override
    public boolean isThirdPartyUser() {
        return getUser() != null && !TextUtils.isEmpty(mUser.getThirdPartyUID());
    }

    @Keep
    public String getToken() {
        String token = "";
        if (!isLogin() || TextUtils.isEmpty(token = getUser().getToken())) {
            return "";
        }
        return token;
    }

    @Override
    public void loginWithEmailPassword(String email, String passwd, ILoginCallback callback) {
        login(EMAIL, email, passwd, callback);
    }

    @Override
    public void loginWithPhonePassword(String countryCode, String phone, String passwd, ILoginCallback callback) {
        login(PHONE, countryCode + " " + phone, passwd, callback);
    }

    @Override
    public void loginWithUUid(String uuid, String password, ILoginCallback callback) {
        login(UID, uuid, password, callback);
    }

    @Override
    public void cleanCache() {
        KV.remove(DBKey.KEY_USER_ACCOUNT);
        KV.remove(DBKey.KEY_USER_PASSWORD);
        KV.remove(DBKey.KEY_LOGIN_TYPE);
        KV.remove(DBKey.CURRENT_DEVICE_ID);
        KV.remove(DBKey.KEY_USER_INFO);
        setUser(null);
    }

    protected void cleanCacheAfterLogout() {
        cleanCache();
        EventBus.getDefault().post(new CommonCmdEvent(CommonCmdEvent.CMD.LOGOUT_SUCCESS));
    }

    protected void updateThirdPartyUserInfo(DinUserLoginResponse response, String thirdPartyUID) {
        if (response == null) {
            return;
        }
        DinUser dinUser = DinUser.from(response);
        dinUser.setThirdPartyUID(thirdPartyUID);
        setUser(dinUser);
        save(dinUser);
    }

    protected void updateUserInfo(DinUserLoginResponse response, String password) {
        if (response == null) {
            return;
        }
        DinUser dinUser = DinUser.from(response);
        dinUser.setPassword(password);
        setUser(dinUser);
        save(dinUser);
    }

    protected synchronized void save(DinUser newDinUser) {
        KV.putObj(DBKey.KEY_USER_INFO, newDinUser);
    }

    protected abstract void login(int type, String account, String password, final ILoginCallback callback);

    /**
     * 使用之前已登录的信息自动登录
     */
    public abstract void autoLogin(final ILoginCallback callback);
}
