package com.dinsafer.dincore.user.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;

/**
 * Created by Rinfon on 16/8/11.
 */
@Keep
public class DinUserLoginResponse extends BaseHttpEntry implements Serializable {


    /**
     * Cmd :
     * Result : {"user_id":"","uid":"","email":"","phone":"","avatar":"","token":"","reg_time":1620617963263591000,"gmtime":23511234342234}
     */

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean implements Serializable {
        /**
         * user_id :
         * uid :
         * email :
         * phone :
         * avatar :
         * token :
         * reg_time : 1620617963263591000
         * gmtime : 23511234342234
         */

        private String user_id;
        private String uid;
        private String email;
        private String phone;
        private String avatar;
        private String token;
        private long reg_time;
        private long gmtime;

        public String getUser_id() {
            return user_id;
        }

        public void setUser_id(String user_id) {
            this.user_id = user_id;
        }

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public long getReg_time() {
            return reg_time;
        }

        public void setReg_time(long reg_time) {
            this.reg_time = reg_time;
        }

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }
    }
}
