package com.dinsafer.dincore.user;


import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.user.api.ILoginCallback;
import com.dinsafer.dincore.user.api.ILoginStateChangedListener;
import com.dinsafer.dincore.user.api.ILogoutCallback;
import com.dinsafer.dincore.user.api.IRegisterCallback;
import com.dinsafer.dincore.user.api.IResultCallback;
import com.dinsafer.dincore.user.api.IResultCallback2;
import com.dinsafer.dincore.user.api.IUser;
import com.dinsafer.dincore.user.bean.DinUser;
import com.dinsafer.dincore.user.bean.LoginAgainEvent;
import com.dinsafer.dincore.user.bean.LogoutEvent;
import com.dinsafer.dincore.user.bean.RefreshVerifyCodeResponse;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * TODO
 * <p>
 * 登出
 * 注销用户
 * 注册用户
 * 修改密码
 * 修改头像
 * 修改名字
 * 忘记密码
 */
public class UserManager implements IUser {

    private ILoginStateChangedListener loginStateChangedListener;
    private final BaseUserManager proxy;

    private UserManager() {
        EventBus.getDefault().register(this);
        proxy = DinCore.getInstance().isCompatMode()
                ? new CompatUserManager()
                : new CommonUserManager();
    }

    private static class Holder {
        private final static UserManager INSTANCE = new UserManager();
    }

    @Keep
    public static synchronized UserManager getInstance() {
        return Holder.INSTANCE;
    }

    @Nullable
    @Override
    public DinUser getThirdPartyUser(String userId, String token, String thirdPartyUID) {
        return proxy.getThirdPartyUser(userId, token, thirdPartyUID);
    }

    @Nullable
    @Override
    public DinUser getUser() {
        return proxy.getUser();
    }

    @Override
    public boolean isThirdPartyUser() {
        return proxy.isThirdPartyUser();
    }

    @Override
    public boolean isLogin() {
        return proxy.isLogin();
    }

    /**
     * 使用之前已登录的信息自动登录
     */
    public void autoLogin(final ILoginCallback callback) {
        proxy.autoLogin(callback);
    }

    /**
     * 只要调用了退出登录的接口，不管是否调用接口成功，都需要让用户退出并清除缓存的数据
     */
    @Override
    public void logout(final ILogoutCallback callback) {
        proxy.logout(callback);
    }

    @Keep
    public String getToken() {
        return proxy.getToken();
    }

    @Override
    public void loginWithEmailPassword(String email, String passwd, ILoginCallback callback) {
        proxy.login(BaseUserManager.EMAIL, email, passwd, callback);
    }

    @Override
    public void loginWithPhonePassword(String countryCode, String phone, String passwd, ILoginCallback callback) {
        proxy.login(BaseUserManager.PHONE, countryCode + " " + phone, passwd, callback);
    }

    @Override
    public void getPhoneValidateCode(String account, String specialId, String verifyCode, String verifyId, IResultCallback callback) {
        proxy.getPhoneValidateCode(account, specialId, verifyCode, verifyId, callback);
    }

    @Override
    public void getEmailValidateCode(String account, IResultCallback callback) {
        proxy.getEmailValidateCode(account, callback);
    }

    @Override
    public void registerAccountWithPhone(String account, String code, IRegisterCallback callback) {
        proxy.registerAccountWithPhone(account, code, callback);
    }

    @Override
    public void registerAccountWithEmail(String account, String code, IRegisterCallback callback) {
        proxy.registerAccountWithEmail(account, code, callback);
    }

    @Override
    public void loginWithUUid(String uuid, String password, ILoginCallback callback) {
        proxy.login(BaseUserManager.UID, uuid, password, callback);
    }

    @Override
    public void modifyUidPassword(String username, String password, final IResultCallback callback) {
        proxy.modifyUidPassword(username, password, callback);
    }

    @Override
    public void changePassword(String oldPassword, String newPassword, IResultCallback callback) {
        proxy.changePassword(oldPassword, newPassword, callback);
    }

    @Override
    public void changeUid(String newUuid, IResultCallback callback) {
        proxy.changeUid(newUuid, callback);
    }

    @Override
    public void getForgetPWDbyEmail(String uuid, IResultCallback callback) {
        proxy.getForgetPWDbyEmail(uuid, callback);
    }
    @Override
    public void comfirmForgetPWDByEmailCode(String uuid, String verifyCode, String newPassword, IResultCallback callback) {
        proxy.comfirmForgetPWDByEmailCode(uuid, verifyCode, newPassword, callback);
    }

    @Override
    public void getForgetPWDbyPhone(String uuid, String specialId, String verifyCode, String verifyId, IResultCallback callback) {
        proxy.getForgetPWDbyPhone(uuid, specialId, verifyCode, verifyId, callback);
    }


    @Override
    public void comfirmForgetPWDByPhoneCode(String uuid, String verifyCode, String newPassword, IResultCallback callback) {
        proxy.comfirmForgetPWDByPhoneCode(uuid, verifyCode, newPassword, callback);
    }

    @Override
    public void bindPhone(String areCode, String phone, String specialId, String verifyCode, String verifyId, IResultCallback callback) {
        proxy.bindPhone(areCode, phone, specialId, verifyCode, verifyId, callback);
    }

    @Override
    public void unbindPhone(String phone, String specialId, String verifyCode, String verifyId, IResultCallback callback) {
        proxy.unbindPhone(phone, specialId, verifyCode, verifyId, callback);
    }

    @Override
    public void verifyBindPhone(String areCode, String phone, String verifyCode, IResultCallback callback) {
        proxy.verifyBindPhone(areCode, phone, verifyCode, callback);
    }

    @Override
    public void verifyUnbindPhone(String account, String verifyCode, IResultCallback callback) {
        proxy.verifyUnbindPhone(account, verifyCode, callback);
    }

    @Override
    public void bindEmail(String email, IResultCallback callback) {
        proxy.bindEmail(email, callback);
    }

    @Override
    public void verifyBindEmailCode(String email, String code, IResultCallback callback) {
        proxy.verifyBindEmailCode(email, code, callback);
    }

    @Override
    public void unbindEmail(String account, IResultCallback callback) {
        proxy.unbindEmail(account, callback);
    }

    @Override
    public void verifyUnbindEmailCode(String account, String verifyCode, IResultCallback callback) {
        proxy.verifyUnbindEmailCode(account, verifyCode, callback);
    }

    @Override
    public void uploadUserAvatar(final String imageFilePath, final String homeId, final IResultCallback2<String> callback) {
        proxy.uploadUserAvatar(imageFilePath, homeId, callback);
    }

    @Override
    public void cleanCache() {
        proxy.cleanCache();
    }

    /**
     * 删除用户
     * 注销用户
     */
    @Override
    @Keep
    public void deleteAccount(final IResultCallback callback) {
        proxy.deleteAccount(callback);
    }

    @Keep
    @Override
    public void setLoginStateChangedListener(ILoginStateChangedListener loginStateChangedListener) {
        this.loginStateChangedListener = loginStateChangedListener;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(LogoutEvent ev) {
        if (this.loginStateChangedListener != null) {
            this.loginStateChangedListener.onLogout();
        }
        if (ev.isNeedCleanCache()) {
            proxy.cleanCacheAfterLogout();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(LoginAgainEvent ev) {
        if (this.loginStateChangedListener != null) {
            this.loginStateChangedListener.onNeedAutoLogin();
        }
    }

    @Override
    public void verifyCodeOnly(@NonNull String account, @NonNull String code, IResultCallback2<Boolean> callback) {
        proxy.verifyCodeOnly(account, code, callback);
    }

    @Override
    public void changePasswordOnly(@NonNull String account, @NonNull String password, IResultCallback2<Boolean> callback) {
        proxy.changePasswordOnly(account, password, callback);
    }

    @Override
    public void checkPasswordOnly(@NonNull String password, IResultCallback2<Boolean> callback) {
        proxy.checkPasswordOnly(password,callback);
    }

    @Override
    public void refreshVerifyCode(@NonNull String specialId, IResultCallback2<RefreshVerifyCodeResponse.ResultBean> callback) {
        proxy.refreshVerifyCode(specialId, callback);

    }

}
