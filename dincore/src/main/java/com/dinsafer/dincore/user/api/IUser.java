package com.dinsafer.dincore.user.api;


import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dincore.user.bean.DinUser;
import com.dinsafer.dincore.user.bean.RefreshVerifyCodeResponse;

/**
 * 用户接口
 */
@Keep
public interface IUser {

    @Nullable
    DinUser getThirdPartyUser(final String userId, final String token, final String thirdPartyUID);

    @Nullable
    DinUser getUser();

    boolean isThirdPartyUser();

    boolean isLogin();

    public void cleanCache();

    //
    void logout(final ILogoutCallback callback);

    void loginWithEmailPassword(String email, String passwd, final ILoginCallback callback);

    void loginWithPhonePassword(String countryCode, String phone, String passwd, final ILoginCallback callback);

    //    void cancelAccount(IResultCallback callback);
//

    void getPhoneValidateCode(String account, String specialId, String verifyCode, String verifyId, final IResultCallback callback);

    //    /**
//     * 获取邮箱验证码
//     *
//     * @param email
//     * @param callback
//     */
    void getEmailValidateCode(String email, final IResultCallback callback);
//

    /**
     * @param phone
     * @param code
     * @param callback
     */
    void registerAccountWithPhone(final String phone,
                                  final String code,
                                  final IRegisterCallback callback);
//

    /**
     * @param email
     * @param code
     * @param callback
     */
    void registerAccountWithEmail(final String email,
                                  final String code,
                                  final IRegisterCallback callback);

    //
//    /**
//     * 通过设备uuid注册一个账号
//     *
//     * @param targetUUid
//     * @param callback
//     */
//    void registerAccountWithDevice(String targetUUid,
//                                   String targetKey,
//                                   final IRegisterCallback callback);
//
    void loginWithUUid(String uuid, String password, final ILoginCallback callback);

    void modifyUidPassword(String username, String password, final IResultCallback callback);

    void changePassword(String oldPassword, String newPassword, final IResultCallback callback);

    void changeUid(String newUuid, final IResultCallback callback);

    void getForgetPWDbyEmail(String uuid, final IResultCallback callback);

    void comfirmForgetPWDByEmailCode(String uuid, String verifyCode, String newPassword, final IResultCallback callback);

    void getForgetPWDbyPhone(String uuid, String specialId, String verifyCode, String verifyId, final IResultCallback callback);

    void comfirmForgetPWDByPhoneCode(String uuid, String verifyCode, String newPassword, final IResultCallback callback);

    void bindPhone(String areCode, String phone, String specialId, String verifyCode, String verifyId, final IResultCallback callback);

    void unbindPhone(String phone, String specialId, String verifyCode, String verifyId, final IResultCallback callback);

    void verifyBindPhone(String areCode, String phone, String verifyCode, final IResultCallback callback);

    void verifyUnbindPhone(String account, String verifyCode, final IResultCallback callback);

    void bindEmail(String email, final IResultCallback callback);

    void verifyBindEmailCode(String email, String code, final IResultCallback callback);

    void unbindEmail(String account, final IResultCallback callback);

    void verifyUnbindEmailCode(String account, String verifyCode, IResultCallback callback);

    void uploadUserAvatar(final String imageFilePath, final String homeId, final IResultCallback2<String> callback);

    void deleteAccount(final IResultCallback callback);

    void setLoginStateChangedListener(ILoginStateChangedListener loginStateChangedListener);

    void verifyCodeOnly(@NonNull final String account, @NonNull final String code, final IResultCallback2<Boolean> callback);

    void changePasswordOnly(@NonNull final String account, @NonNull final String password, final IResultCallback2<Boolean> callback);

    void checkPasswordOnly(@NonNull final String password, final IResultCallback2<Boolean> callback);

    void refreshVerifyCode(@NonNull final String specialId, final IResultCallback2<RefreshVerifyCodeResponse.ResultBean> callback);
}
