package com.dinsafer.dincore.user;

import static com.dinsafer.dincore.common.ErrorCode.DEFAULT;

import android.text.TextUtils;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dincore.common.Cmd;
import com.dinsafer.dincore.common.CommonCmdEvent;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.db.DBKey;
import com.dinsafer.dincore.http.CloudflareUploadTokenResponse;
import com.dinsafer.dincore.http.NetWorkException;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.user.api.ILoginCallback;
import com.dinsafer.dincore.user.api.ILoginStateChangedListener;
import com.dinsafer.dincore.user.api.ILogoutCallback;
import com.dinsafer.dincore.user.api.IRegisterCallback;
import com.dinsafer.dincore.user.api.IResultCallback;
import com.dinsafer.dincore.user.api.IResultCallback2;
import com.dinsafer.dincore.user.bean.DinUser;
import com.dinsafer.dincore.user.bean.DinUserLoginResponse;
import com.dinsafer.dincore.user.bean.RefreshVerifyCodeResponse;
import com.dinsafer.dincore.user.bean.RegisterResponse;
import com.dinsafer.dincore.utils.FileUploadUtils;
import com.dinsafer.dssupport.msctlib.db.KV;
import com.dinsafer.dssupport.utils.DDLog;
import com.qiniu.android.http.ResponseInfo;

import org.greenrobot.eventbus.EventBus;
import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;

import okhttp3.MediaType;
import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 完整独立的用户管理
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/3/21 3:13 下午
 */
class CommonUserManager extends BaseUserManager {

    private static final int SOURCE_CLOUDFLARE = 1;
    private static final int SOURCE_QINIU = 0;

    @Override
    protected void login(int type, String account, String password, final ILoginCallback callback) {
        userRepository.login(type, account, password, new Callback<DinUserLoginResponse>() {
            @Override
            public void onResponse(Call<DinUserLoginResponse> call, Response<DinUserLoginResponse> response) {
                DinUserLoginResponse user = response.body();
                // TODO 缓存数据加密
                KV.putInt(DBKey.KEY_LOGIN_TYPE, type);
                KV.putString(DBKey.KEY_USER_ACCOUNT, account);
                KV.putString(DBKey.KEY_USER_PASSWORD, password);
                updateUserInfo(user, password);
                EventBus.getDefault().post(new CommonCmdEvent(CommonCmdEvent.CMD.LOGIN_SUCCESS));
                callback.onSuccess(mUser);
            }

            @Override
            public void onFailure(Call<DinUserLoginResponse> call, Throwable t) {
                DDLog.e(TAG, "fail code:" + 0 + " message:" + t.getMessage());
                if (null != callback) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), t.getMessage());
                        return;
                    }

                    callback.onError(DEFAULT, "Unknown Error: " + t.getMessage());
                }

            }
        });
    }

    /**
     * 使用之前已登录的信息自动登录
     */
    public void autoLogin(final ILoginCallback callback) {
        DDLog.i(TAG, "autoLogin");
        int loginType = KV.getInt(DBKey.KEY_LOGIN_TYPE, -1);
        String account = KV.getString(DBKey.KEY_USER_ACCOUNT, null);
        String password = KV.getString(DBKey.KEY_USER_PASSWORD, null);
        // TODO 数据解密
        if (-1 == loginType || TextUtils.isEmpty(account) || TextUtils.isEmpty(password)) {
            if (null != callback) {
                callback.onError(DEFAULT, "无法获取之前登录的账号信息，自动登录失败");
            }
        } else {
            login(loginType, account, password, callback);
        }
    }

    @Nullable
    @Override
    public DinUser getThirdPartyUser(String userId, String token, String thirdPartyUID) {
        if (TextUtils.isEmpty(userId)
                || TextUtils.isEmpty(token)
                || TextUtils.isEmpty(thirdPartyUID)) {
            return null;
        }
        // 创建一个新的DinUser
        DinUserLoginResponse.ResultBean resultBean = new DinUserLoginResponse.ResultBean();
        resultBean.setToken(token);
        resultBean.setUser_id(userId);
        resultBean.setUid(userId);
        DinUserLoginResponse user = new DinUserLoginResponse();
        user.setResult(resultBean);
        updateThirdPartyUserInfo(user, thirdPartyUID);
        return getUser();
    }

    /**
     * 只要调用了退出登录的接口，不管是否调用接口成功，都需要让用户退出并清除缓存的数据
     */
    @Override
    public void logout(final ILogoutCallback callback) {
        String token = getToken();
        EventBus.getDefault().post(new CommonCmdEvent(Cmd.COMMON_CMD_DISCONNECT_HOME));
        if (TextUtils.isEmpty(token)) {
            callback.onSuccess();
            cleanCacheAfterLogout();
            return;
        }
        userRepository.logout(new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                callback.onSuccess();
                cleanCacheAfterLogout();
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                DDLog.e(TAG, "fail code:" + 0 + " message:" + t.getMessage());
                t.printStackTrace();
                callback.onSuccess();
                cleanCacheAfterLogout();
            }
        });
    }

    @Override
    public void getPhoneValidateCode(String account, String specialId, String verifyCode, String verifyId, IResultCallback callback) {
        userRepository.getPhoneValidateCode(account, specialId, verifyCode, verifyId, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (null != callback) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });

    }

    @Override
    public void getEmailValidateCode(String account, IResultCallback callback) {
        userRepository.getEmailValidateCode(account, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (null != callback) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    @Override
    public void registerAccountWithPhone(String account, String code, IRegisterCallback callback) {
        userRepository.validatePhoneCode(account, code, new Callback<RegisterResponse>() {
            @Override

            public void onResponse(Call<RegisterResponse> call, Response<RegisterResponse> response) {
                DinUser dinUser = DinUser.from(response.body());
                dinUser.setPhone(account);
                setUser(dinUser);
                save(dinUser);
                if (null != callback) {
                    callback.onSuccess(dinUser);
                }
            }

            @Override
            public void onFailure(Call<RegisterResponse> call, Throwable t) {
                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    @Override
    public void registerAccountWithEmail(String account, String code, IRegisterCallback callback) {
        userRepository.validateEmailCode(account, code, new Callback<RegisterResponse>() {
            @Override
            public void onResponse(Call<RegisterResponse> call, Response<RegisterResponse> response) {
                DinUser dinUser = DinUser.from(response.body());
                dinUser.setEmail(account);
                setUser(dinUser);
                save(dinUser);
                if (null != callback) {
                    callback.onSuccess(dinUser);
                }
            }

            @Override
            public void onFailure(Call<RegisterResponse> call, Throwable t) {
                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    @Override
    public void modifyUidPassword(String username, String password, final IResultCallback callback) {
        if (TextUtils.isEmpty(username)
                || TextUtils.isEmpty(password)) {
            DDLog.e(TAG, "modifyUuidPassword, username or password is null.");
            if (null != callback) {
                callback.onError(ErrorCode.PARAM_ERROR, "username or password is null.");
            }
            return;
        }

        userRepository.modifyUuidPassword(username, password, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on: modifyUuidPassword");
                mUser.setUid(username);
                mUser.setPassword(password);
                save(mUser);
                KV.putInt(DBKey.KEY_LOGIN_TYPE, UID);
                KV.putString(DBKey.KEY_USER_ACCOUNT, username);
                KV.putString(DBKey.KEY_USER_PASSWORD, password);
                if (null != callback) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                DDLog.e(TAG, "Error on: modifyUuidPassword");

                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    @Override
    public void changePassword(String oldPassword, String newPassword, IResultCallback callback) {
        if (TextUtils.isEmpty(oldPassword)
                || TextUtils.isEmpty(newPassword)) {
            DDLog.e(TAG, "changePassword, oldPassword or newPassword is null.");
            if (null != callback) {
                callback.onError(ErrorCode.PARAM_ERROR, "oldPassword or newPassword is null.");
            }
            return;
        }

        userRepository.changePassword(oldPassword, newPassword, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on: changePassword");
                mUser.setToken(response.body().getResult());
                mUser.setPassword(newPassword);
                save(mUser);

                if (null != callback) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                DDLog.e(TAG, "Error on: changePassword");

                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    @Override
    public void changeUid(String newUuid, IResultCallback callback) {
        if (TextUtils.isEmpty(newUuid)) {
            DDLog.e(TAG, "changeUuid, newUuid is null.");
            if (null != callback) {
                callback.onError(ErrorCode.PARAM_ERROR, "newUuid is null.");
            }
            return;
        }

        userRepository.changeUuid(newUuid, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on: changeUuid");
                mUser.setUid(newUuid);
                save(mUser);
                if (null != callback) {
                    callback.onSuccess();
                }
                return;
            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                DDLog.e(TAG, "Error on: changeUuid");

                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    @Override
    public void getForgetPWDbyEmail(String uuid, IResultCallback callback) {
        if (TextUtils.isEmpty(uuid)) {
            DDLog.e(TAG, "forgetPassword, uuid is null.");
            if (null != callback) {
                callback.onError(ErrorCode.PARAM_ERROR, "uuid is null.");
            }
            return;
        }

        userRepository.getForgetPWDbyEmail(uuid, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on: forgetPassword");
                if (null != callback) {
                    callback.onSuccess();
                }
                return;
            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                DDLog.e(TAG, "Error on: forgetPassword");

                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    @Override
    public void comfirmForgetPWDByEmailCode(String uuid, String verifyCode, String newPassword, IResultCallback callback) {
        if (TextUtils.isEmpty(uuid)
                || TextUtils.isEmpty(verifyCode)
                || TextUtils.isEmpty(newPassword)) {
            DDLog.e(TAG, "forgetPasswordSetNewPassword, param is null.");
            if (null != callback) {
                callback.onError(ErrorCode.PARAM_ERROR, "param is null.");
            }
            return;
        }

        userRepository.comfirmForgetPWDByEmailCode(uuid, verifyCode, newPassword, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on: forgetPasswordSetNewPassword");
                if (null != callback) {
                    callback.onSuccess();
                }
                return;

            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                DDLog.e(TAG, "Error on: forgetPasswordSetNewPassword");

                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    @Override
    public void getForgetPWDbyPhone(String uuid, String specialId, String verifyCode, String verifyId, IResultCallback callback) {
        if (TextUtils.isEmpty(uuid)) {
            DDLog.e(TAG, "forgetPassword, uuid is null.");
            if (null != callback) {
                callback.onError(ErrorCode.PARAM_ERROR, "uuid is null.");
            }
            return;
        }

        userRepository.getForgetPWDbyPhone(uuid, specialId, verifyCode, verifyId, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on: forgetPassword");
                if (null != callback) {
                    callback.onSuccess();
                }
                return;
            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                DDLog.e(TAG, "Error on: forgetPassword");

                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    @Override
    public void comfirmForgetPWDByPhoneCode(String uuid, String verifyCode, String newPassword, IResultCallback callback) {
        if (TextUtils.isEmpty(uuid)
                || TextUtils.isEmpty(verifyCode)
                || TextUtils.isEmpty(newPassword)) {
            DDLog.e(TAG, "forgetPasswordSetNewPassword, param is null.");
            if (null != callback) {
                callback.onError(ErrorCode.PARAM_ERROR, "param is null.");
            }
            return;
        }

        userRepository.comfirmForgetPWDByPhoneCode(uuid, verifyCode, newPassword, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on: forgetPasswordSetNewPassword");
                if (null != callback) {
                    callback.onSuccess();
                }
                return;

            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                DDLog.e(TAG, "Error on: forgetPasswordSetNewPassword");

                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    @Override
    public void bindPhone(String areCode, String phone, String specialId, String verifyCode, String verifyId, IResultCallback callback) {
        if (TextUtils.isEmpty(areCode)
                || TextUtils.isEmpty(phone)) {
            DDLog.e(TAG, "bindPhone, param is null.");
            if (null != callback) {
                callback.onError(ErrorCode.PARAM_ERROR, "param is null.");
            }
            return;
        }

        String param = areCode + " " + phone;
        userRepository.bindPhone(param, specialId, verifyCode, verifyId, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on: bindPhone");
                if (null != callback) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                DDLog.e(TAG, "Error on: bindPhone");

                if (null == callback) {
                    DDLog.e(TAG, "Error on: bindPhone callback null");
                    return;
                }

                if (t instanceof NetWorkException) {
                    DDLog.e(TAG, "Error on: bindPhone callback NetWorkException");
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    DDLog.e(TAG, "Error on: bindPhone callback ondefault");
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    @Override
    public void unbindPhone(String phone, String specialId, String verifyCode, String verifyId, IResultCallback callback) {
        userRepository.unbindPhone(phone, specialId, verifyCode, verifyId, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on: unbindPhone");
                if (null != callback) {
                    callback.onSuccess();
                }

            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                DDLog.e(TAG, "Error on: unbindPhone");

                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    @Override
    public void verifyBindPhone(String areCode, String phone, String verifyCode, IResultCallback callback) {
        if (TextUtils.isEmpty(verifyCode)) {
            DDLog.e(TAG, "verifyBindPhone, param is null.");
            if (null != callback) {
                callback.onError(ErrorCode.PARAM_ERROR, "param is null.");
            }
            return;
        }

        userRepository.verifyBindPhone(areCode + " " + phone, verifyCode, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on: verifyBindPhone");
                mUser.setPhone(areCode + " " + phone);
                save(mUser);

                if (null != callback) {
                    callback.onSuccess();
                }
                return;

            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                DDLog.e(TAG, "Error on: verifyBindPhone");

                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    @Override
    public void verifyUnbindPhone(String account, String verifyCode, IResultCallback callback) {
        if (TextUtils.isEmpty(verifyCode)) {
            DDLog.e(TAG, "verifyUnbindPhone, param is null.");
            if (null != callback) {
                callback.onError(ErrorCode.PARAM_ERROR, "param is null.");
            }
            return;
        }

        userRepository.verifyUnbindPhone(account, verifyCode, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on: verifyUnbindPhone");
                StringResponseEntry entry = response.body();

                if (null != entry) {
                    int resultStatus = entry.getStatus();
                    if (1 == resultStatus) {
                        mUser.setPhone("");
                        save(mUser);

                        if (null != callback) {
                            callback.onSuccess();
                        }
                        return;
                    }

                    if (null != callback) {
                        callback.onError(resultStatus, "");
                    }
                    return;
                }

                if (null != callback) {
                    callback.onError(DEFAULT, "");
                }
            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                DDLog.e(TAG, "Error on: verifyUnbindPhone");

                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    @Override
    public void bindEmail(String email, IResultCallback callback) {
        if (TextUtils.isEmpty(email)) {
            DDLog.e(TAG, "bindEmail, param is null.");
            if (null != callback) {
                callback.onError(ErrorCode.PARAM_ERROR, "param is null.");
            }
            return;
        }

        userRepository.bindEmail(email, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on: bindEmail");
                if (null != callback) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                DDLog.e(TAG, "Error on: bindEmail");
                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    @Override
    public void verifyBindEmailCode(String email, String code, IResultCallback callback) {
        if (TextUtils.isEmpty(email)) {
            DDLog.e(TAG, "bindEmail, param is null.");
            if (null != callback) {
                callback.onError(ErrorCode.PARAM_ERROR, "param is null.");
            }
            return;
        }

        userRepository.verifyBindEmailCall(email, code, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on: bindEmail");
                mUser.setEmail(email);
                save(mUser);
                if (null != callback) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                DDLog.e(TAG, "Error on: bindEmail");
                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    @Override
    public void unbindEmail(String account, IResultCallback callback) {
        userRepository.unbindEmail(account, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on: unbindEmail");
                if (null != callback) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                DDLog.e(TAG, "Error on: unbindEmail");

                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    @Override
    public void verifyUnbindEmailCode(String account, String verifyCode, IResultCallback callback) {
        if (TextUtils.isEmpty(verifyCode)) {
            DDLog.e(TAG, "verifyUnbindEmailCode, param is null.");
            if (null != callback) {
                callback.onError(ErrorCode.PARAM_ERROR, "param is null.");
            }
            return;
        }

        userRepository.verifyUnbindEmail(account, verifyCode, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on: verifyUnbindEmailCode");
                StringResponseEntry entry = response.body();

                if (null != entry) {
                    int resultStatus = entry.getStatus();
                    if (1 == resultStatus) {
                        mUser.setEmail("");
                        save(mUser);

                        if (null != callback) {
                            callback.onSuccess();
                        }
                        return;
                    }

                    if (null != callback) {
                        callback.onError(resultStatus, "");
                    }
                    return;
                }

                if (null != callback) {
                    callback.onError(DEFAULT, "");
                }
            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                DDLog.e(TAG, "Error on: verifyUnbindEmailCode");

                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    @Override
    public void uploadUserAvatar(final String imageFilePath, final String homeId, final IResultCallback2<String> callback) {
        if (TextUtils.isEmpty(imageFilePath)
                || !(new File(imageFilePath).exists())) {
            DDLog.e(TAG, "uploadUserAvatar, param is null.");
            if (null != callback) {
                callback.onError(ErrorCode.PARAM_ERROR, "param is null.");
            }
            return;
        }

        //七牛方式
//        userRepository.getUploadToken(new Callback<StringResponseEntry>() {
//            @Override
//            public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
//                StringResponseEntry responseEntry = response.body();
//                if (null != responseEntry) {
//                    final String uploadToken = responseEntry.getResult();
//                    if (!TextUtils.isEmpty(uploadToken)) {
//                        uploadImageByQiuNiu(uploadToken, imageFilePath, callback);
//                        return;
//                    }
//
//                    if (null != callback) {
//                        callback.onError(DEFAULT, "Empty upload token.");
//                    }
//                    return;
//                }
//
//                if (null != callback) {
//                    callback.onError(DEFAULT, "Empty data on get upload token.");
//                }
//            }
//
//            @Override
//            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
//                DDLog.e(TAG, "Error on: getUploadToken");
//
//                if (null == callback) {
//                    return;
//                }
//
//                if (t instanceof NetWorkException) {
//                    NetWorkException exception = (NetWorkException) t;
//                    callback.onError(exception.getStatus(), exception.getMsgDes());
//                } else {
//                    callback.onError(DEFAULT, "");
//                }
//            }
//        });

        //资源类型(type)，0:放在default目录下；1:设备日志；2:调频日志；3:电池日志；4：签名，5:头像；6:老化测试；
        userRepository.uploadUserAvatarByCloudflare(imageFilePath, homeId, new IResultCallback2<String>() {
            @Override
            public void onError(int code, String error) {

            }

            @Override
            public void onSuccess(String result) {
                if (callback != null) {
                    uploadImageKey(imageKey, SOURCE_CLOUDFLARE, callback);
                    callback.onSuccess(responseBody);
                }
            }
        });

    }
    /**
     * 执行实际的文件上传
     */
    private void performFileUpload(String uploadUrl, FileUploadUtils.FileInfo fileInfo, String imageKey,
                                   IResultCallback2 callback) {
        try {

            if (fileInfo == null) {
                if (callback != null) {
                    callback.onError(DEFAULT, "file is null");
                }
                return;
            }
            File file = new File(fileInfo.filePath);
            // 创建RequestBody
            RequestBody fileBody = RequestBody.create(MediaType.parse(fileInfo.contentType), file);

            userRepository.uploadFileByCloudflareR2(uploadUrl, fileBody, new Callback<ResponseBody>() {
                @Override
                public void onResponse(retrofit2.Call<ResponseBody> call, retrofit2.Response<ResponseBody> response) {
                    com.dinsafer.dssupport.utils.DDLog.i("Api", "Retrofit上传响应，状态码: " + response.code());
                    if (response.isSuccessful()) {
                        try {
                            String responseBody = response.body() != null ? response.body().string() : "success";
                            if (callback != null) {
                                uploadImageKey(imageKey, SOURCE_CLOUDFLARE, callback);
                                callback.onSuccess(responseBody);
                            }
                        } catch (java.io.IOException e) {
                            if (callback != null) {
                                callback.onError(DEFAULT, "读取响应失败: " + e.getMessage());
                            }
                        }
                    } else {
                        try {
                            String errorBody = response.errorBody() != null ? response.errorBody().string() : "";
                            com.dinsafer.dssupport.utils.DDLog.e("Api", "Retrofit文件上传失败，状态码: " + response.code() + ", 错误信息: " + errorBody);
                            if (callback != null) {
                                callback.onError(response.code(), "上传失败: " + errorBody);
                            }
                        } catch (java.io.IOException e) {
                            com.dinsafer.dssupport.utils.DDLog.e("Api", "读取错误响应失败: " + e.getMessage());
                            if (callback != null) {
                                callback.onError(response.code(), "上传失败，状态码: " + response.code());
                            }
                        }
                    }
                }

                @Override
                public void onFailure(retrofit2.Call<ResponseBody> call, Throwable t) {
                    com.dinsafer.dssupport.utils.DDLog.e("Api", "Retrofit文件上传失败: " + t.getMessage());
                    if (callback != null) {
                        if (t instanceof NetWorkException) {
                            NetWorkException netException = (NetWorkException) t;
                            callback.onError(DEFAULT, netException.getMsgDes());
                        } else {
                            callback.onError(DEFAULT, "Retrofit文件上传失败: " + t.getMessage());
                        }
                    }
                }
                    });

        } catch (Exception e) {
            e.printStackTrace();
            if (callback != null) {
                callback.onError(DEFAULT, "upload error: " + e.getMessage());
            }
        }
    }
    private void uploadImageByQiuNiu(String uploadToken, String imagePath, final IResultCallback2<String> callback) {
        userRepository.uploadImage(uploadToken, imagePath,
                (String key, ResponseInfo info, JSONObject response) -> {
                    DDLog.i(TAG, "Upload image success by qiu niu.");
                    //response包含hash、key等信息，具体字段取决于上传策略的设置。
                    if (info.isOK()) {
                        try {
                            final String imageKey = response.getString("key");
                            uploadImageKey(imageKey, SOURCE_QINIU, callback);
                        } catch (JSONException e) {
                            DDLog.e(TAG, "Can't get image's key");
                            e.printStackTrace();

                            if (null != callback) {
                                callback.onError(DEFAULT, "Can't get image's key");
                            }
                        }
                    } else {
                        DDLog.e(TAG, "Upload image by qiu niu error.");
                        if (null != callback) {
                            callback.onError(DEFAULT, "Upload image by qiu niu error.");
                        }
                    }
                });
    }

    private void uploadImageKey(String imageKey, int source, final IResultCallback2<String> callback) {
        userRepository.uploadImageKey(imageKey, source, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(@NotNull Call<StringResponseEntry> call, @NotNull Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on: uploadImageKey");
                mUser.setAvatar(imageKey);
                save(mUser);
                if (null != callback) {
                    callback.onSuccess(imageKey);
                }
            }

            @Override
            public void onFailure(@NotNull Call<StringResponseEntry> call, @NotNull Throwable t) {
                DDLog.e(TAG, "Error on: uploadImageKey");

                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    /**
     * 删除用户
     * 注销用户
     */
    @Override
    @Keep
    public void deleteAccount(final IResultCallback callback) {
        DDLog.i(TAG, "deleteAccount");
        userRepository.deleteAccount(new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                DDLog.i(TAG, "Success on deleteAccount");
                if (null != callback) {
                    if (null != response
                            && null != response.body()
                            && 1 == response.body().getStatus()) {
                        callback.onSuccess();
                    } else {
                        callback.onError(DEFAULT, "Unknown Error!!!");
                    }
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                DDLog.e(TAG, "Error on deleteAccount");
                if (null != callback) {
                    if (t instanceof NetWorkException
                            && (ErrorCode.DEVICE_NOT_BELONE_USER == ((NetWorkException) t).getStatus()
                            || ErrorCode.ERROR_SQLERROR == ((NetWorkException) t).getStatus())) {
                        callback.onError(((NetWorkException) t).getStatus(), ((NetWorkException) t).getMessage());
                    } else {
                        callback.onError(DEFAULT, "Unknown Error!!!");
                    }
                }
            }
        });
    }

    @Override
    public void setLoginStateChangedListener(ILoginStateChangedListener loginStateChangedListener) {
    }

    @Override
    public void verifyCodeOnly(@NonNull String account, @NonNull String code, IResultCallback2<Boolean> callback) {
        userRepository.verifyCodeOnly(account, code, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (null != callback) {
                    StringResponseEntry body = response.body();
                    if (null != body && 1 == body.getStatus()) {
                        boolean success = false;
                        try {
                            success = Boolean.parseBoolean(body.getResult());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        callback.onSuccess(success);
                    } else {
                        callback.onError(DEFAULT, "Empty response or status != 1");
                    }
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    @Override
    public void changePasswordOnly(@NonNull String account, @NonNull String password, IResultCallback2<Boolean> callback) {
        userRepository.changePasswordOnly(account, password, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                StringResponseEntry body = response.body();
                if (null != body && 1 == body.getStatus()) {
                    boolean success = true;
//                    try {
//                        success = Boolean.parseBoolean(body.getResult());
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                    }
                    if (success) {
                        mUser.setPassword(password);
                        KV.putString(DBKey.KEY_USER_PASSWORD, password);
                        save(mUser);
                    }
                    if (null != callback) {
                        callback.onSuccess(success);
                    }
                } else {
                    if (null != callback) {
                        callback.onError(DEFAULT, "Empty response or status != 1");
                    }
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    @Override
    public void checkPasswordOnly(@NonNull String password, IResultCallback2<Boolean> callback) {
        userRepository.checkPasswordOnly(password, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                if (null != callback) {
                    StringResponseEntry body = response.body();
                    if (null != body && 1 == body.getStatus()) {
                        boolean success = false;
                        try {
                            success = Boolean.parseBoolean(body.getResult());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        callback.onSuccess(success);
                    } else {
                        callback.onError(DEFAULT, "Empty response or status != 1");
                    }
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

    @Override
    public void refreshVerifyCode(@NonNull String specialId, IResultCallback2<RefreshVerifyCodeResponse.ResultBean> callback) {
        if (TextUtils.isEmpty(specialId)) {
            DDLog.e(TAG, "refreshVerifyCode, specialId is null.");
            if (null != callback) {
                callback.onError(ErrorCode.PARAM_ERROR, "specialId is null.");
            }
            return;
        }

        userRepository.refreshVerifyCode(specialId, new Callback<RefreshVerifyCodeResponse>() {
            @Override
            public void onResponse(Call<RefreshVerifyCodeResponse> call, Response<RefreshVerifyCodeResponse> response) {
                if (callback != null) {
                    RefreshVerifyCodeResponse body = response.body();
                    if (null != body && 1 == body.getStatus()) {
                        callback.onSuccess(body.getResult());
                    } else {
                        callback.onError(DEFAULT, "Empty response or status != 1");
                    }
                }
            }

            @Override
            public void onFailure(Call<RefreshVerifyCodeResponse> call, Throwable t) {
                if (null == callback) {
                    return;
                }

                if (t instanceof NetWorkException) {
                    NetWorkException exception = (NetWorkException) t;
                    callback.onError(exception.getStatus(), exception.getMsgDes());
                } else {
                    callback.onError(DEFAULT, "");
                }
            }
        });
    }

}
