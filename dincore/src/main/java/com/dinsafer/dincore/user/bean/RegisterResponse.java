package com.dinsafer.dincore.user.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

@Keep
public class RegisterResponse extends BaseHttpEntry {

    /**
     * Cmd :
     * Result : {"user_id":"","token":"","reg_time":23511234342234,"gmtime":23511234342234}
     */

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean Result) {
        this.Result = Result;
    }

    @Keep
    public static class ResultBean {
        /**
         * user_id :
         * token :
         * reg_time : 23511234342234
         * gmtime : 23511234342234
         */

        private String user_id;
        private String token;
        private long reg_time;
        private long gmtime;

        public String getUser_id() {
            return user_id;
        }

        public void setUser_id(String user_id) {
            this.user_id = user_id;
        }

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public long getReg_time() {
            return reg_time;
        }

        public void setReg_time(long reg_time) {
            this.reg_time = reg_time;
        }

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }
    }
}
