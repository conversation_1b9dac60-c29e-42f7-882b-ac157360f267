package com.dinsafer.dincore.user.bean;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.Keep;

/**
 * Description:
 * Date: 2019/6/6
 */
@Keep
public class DinUser implements Parcelable {

    private String user_id;
    private String uid;
    private String avatar;
    private String email;
    private String phone;
    private String token;
    private String password;
    private long reg_time;
    private String gmtime;
    // 后台user_id, 用于判断临时用户，默认是null
    private String thirdPartyUID;

    /**
     * deviceid : d1234567893
     * permission : 10
     * name :
     * time : 1467965207204069311
     */

    public static DinUser from(DinUserLoginResponse loginResponse) {
        DinUser dinUser = new DinUser();
        dinUser.setUser_id(loginResponse.getResult().getUser_id());
        dinUser.setUid(loginResponse.getResult().getUid());
        dinUser.setAvatar(loginResponse.getResult().getAvatar());
        dinUser.setEmail(loginResponse.getResult().getEmail());
        dinUser.setPhone(loginResponse.getResult().getPhone());
        dinUser.setToken(loginResponse.getResult().getToken());
        dinUser.setReg_time(loginResponse.getResult().getReg_time());
        return dinUser;
    }

    public static DinUser from(RegisterResponse registerResponse) {
        DinUser dinUser = new DinUser();
        dinUser.setUser_id(registerResponse.getResult().getUser_id());
        dinUser.setReg_time(registerResponse.getResult().getReg_time());
        dinUser.setToken(registerResponse.getResult().getToken());
        return dinUser;
    }


    public String getUser_id() {
        return user_id;
    }

    public void setUser_id(String user_id) {
        this.user_id = user_id;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public long getReg_time() {
        return reg_time;
    }

    public void setReg_time(long reg_time) {
        this.reg_time = reg_time;
    }

    public String getGmtime() {
        return gmtime;
    }

    public void setGmtime(String gmtime) {
        this.gmtime = gmtime;
    }

    public void setThirdPartyUID(String thirdPartyUID) {
        this.thirdPartyUID = thirdPartyUID;
    }

    public String getThirdPartyUID() {
        return thirdPartyUID;
    }

    public DinUser() {
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.user_id);
        dest.writeString(this.uid);
        dest.writeString(this.avatar);
        dest.writeString(this.email);
        dest.writeString(this.phone);
        dest.writeString(this.token);
        dest.writeString(this.password);
        dest.writeLong(this.reg_time);
        dest.writeString(this.gmtime);
    }

    public void readFromParcel(Parcel source) {
        this.user_id = source.readString();
        this.uid = source.readString();
        this.avatar = source.readString();
        this.email = source.readString();
        this.phone = source.readString();
        this.token = source.readString();
        this.password = source.readString();
        this.reg_time = source.readLong();
        this.gmtime = source.readString();
    }


    protected DinUser(Parcel in) {
        this.user_id = in.readString();
        this.uid = in.readString();
        this.avatar = in.readString();
        this.email = in.readString();
        this.phone = in.readString();
        this.token = in.readString();
        this.password = in.readString();
        this.reg_time = in.readLong();
        this.gmtime = in.readString();
    }

    public static final Creator<DinUser> CREATOR = new Creator<DinUser>() {
        @Override
        public DinUser createFromParcel(Parcel source) {
            return new DinUser(source);
        }

        @Override
        public DinUser[] newArray(int size) {
            return new DinUser[size];
        }
    };
}
