package com.dinsafer.dincore.user;

import static com.dinsafer.dincore.common.ErrorCode.DEFAULT;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import android.text.TextUtils;

import com.dinsafer.dincore.common.Cmd;
import com.dinsafer.dincore.common.CommonCmdEvent;
import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.db.DBKey;
import com.dinsafer.dincore.http.NetWorkException;
import com.dinsafer.dincore.user.api.ILoginCallback;
import com.dinsafer.dincore.user.api.ILoginStateChangedListener;
import com.dinsafer.dincore.user.api.ILogoutCallback;
import com.dinsafer.dincore.user.api.IRegisterCallback;
import com.dinsafer.dincore.user.api.IResultCallback;
import com.dinsafer.dincore.user.api.IResultCallback2;
import com.dinsafer.dincore.user.bean.DinUser;
import com.dinsafer.dincore.user.bean.DinUserLoginResponse;
import com.dinsafer.dincore.user.bean.RefreshVerifyCodeResponse;
import com.dinsafer.dssupport.msctlib.db.KV;
import com.dinsafer.dssupport.utils.DDLog;

import org.greenrobot.eventbus.EventBus;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 兼容模式（cawa）用户管理
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2022/3/21 3:13 下午
 */
class CompatUserManager extends BaseUserManager {
    private static final String ERROR_INFO = "Unsupported API";
    private static final String ERROR_INFO_EXT = ": not support on compat mode!";

    @Override
    protected void login(int type, String account, String password, ILoginCallback callback) {
        if (type != UID) {
            if (null != callback) {
                callback.onError(ErrorCode.ERROR_UNSUPPORTED_API, ERROR_INFO + ": Only uuid login is support on compat mode.");
            }
            return;
        }

        if (TextUtils.isEmpty(account)
                || TextUtils.isEmpty(password)) {
            if (null != callback) {
                callback.onError(ErrorCode.PARAM_ERROR, "uuid and password can't be null!");
            }
            return;
        }

        userRepository.loginCompat(account, password, new Callback<DinUserLoginResponse>() {
            @Override
            public void onResponse(Call<DinUserLoginResponse> call, Response<DinUserLoginResponse> response) {
                DinUserLoginResponse user = response.body();
                // TODO 缓存数据加密
                KV.putInt(DBKey.KEY_LOGIN_TYPE, type);
                KV.putString(DBKey.KEY_USER_ACCOUNT, account);
                KV.putString(DBKey.KEY_USER_PASSWORD, password);
                updateUserInfo(user, password);
                EventBus.getDefault().post(new CommonCmdEvent(CommonCmdEvent.CMD.LOGIN_SUCCESS));
                callback.onSuccess(mUser);
            }

            @Override
            public void onFailure(Call<DinUserLoginResponse> call, Throwable t) {
                DDLog.e(TAG, "fail code:" + 0 + " message:" + t.getMessage());
                if (null != callback) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), t.getMessage());
                        return;
                    }

                    callback.onError(DEFAULT, "Unknown Error: " + t.getMessage());
                }

            }
        });
    }

    @Override
    public void autoLogin(ILoginCallback callback) {
        if (null != callback) {
            callback.onError(ErrorCode.ERROR_UNSUPPORTED_API, ERROR_INFO + ERROR_INFO_EXT);
        }
    }

    @Nullable
    @Override
    public DinUser getThirdPartyUser(String userId, String token, String thirdPartyUID) {
        return null;
    }

    @Override
    public void logout(ILogoutCallback callback) {
        EventBus.getDefault().post(new CommonCmdEvent(Cmd.COMMON_CMD_DISCONNECT_HOME));

        if (null != callback) {
            callback.onSuccess();
        }

        cleanCacheAfterLogout();
    }

    @Override
    public void getPhoneValidateCode(String account, String specialId, String verifyCode, String verifyId, IResultCallback callback) {
        onDefaultError(callback);
    }

    @Override
    public void getEmailValidateCode(String email, IResultCallback callback) {
        onDefaultError(callback);
    }

    @Override
    public void registerAccountWithPhone(String phone, String code, IRegisterCallback callback) {
        onDefaultError(callback);
    }

    @Override
    public void registerAccountWithEmail(String email, String code, IRegisterCallback callback) {
        onDefaultError(callback);
    }

    @Override
    public void modifyUidPassword(String username, String password, IResultCallback callback) {
        onDefaultError(callback);
    }

    @Override
    public void changePassword(String oldPassword, String newPassword, IResultCallback callback) {
        onDefaultError(callback);
    }

    @Override
    public void changeUid(String newUuid, IResultCallback callback) {
        onDefaultError(callback);
    }

    @Override
    public void getForgetPWDbyEmail(String uuid, IResultCallback callback) {
        onDefaultError(callback);
    }

    @Override
    public void comfirmForgetPWDByEmailCode(String uuid, String verifyCode, String newPassword, IResultCallback callback) {
        onDefaultError(callback);
    }

    @Override
    public void getForgetPWDbyPhone(String uuid, String specialId, String verifyCode, String verifyId, IResultCallback callback) {
        onDefaultError(callback);
    }

    @Override
    public void comfirmForgetPWDByPhoneCode(String uuid, String verifyCode, String newPassword, IResultCallback callback) {
        onDefaultError(callback);
    }

    @Override
    public void bindPhone(String areCode, String phone, String specialId, String verifyCode, String verifyId, IResultCallback callback) {
        onDefaultError(callback);
    }

    @Override
    public void unbindPhone(String phone, String specialId, String verifyCode, String verifyId, IResultCallback callback) {
        onDefaultError(callback);
    }

    @Override
    public void verifyBindPhone(String areCode, String phone, String verifyCode, IResultCallback callback) {
        onDefaultError(callback);
    }

    @Override
    public void verifyUnbindPhone(String account, String verifyCode, IResultCallback callback) {
        onDefaultError(callback);
    }

    @Override
    public void bindEmail(String email, IResultCallback callback) {
        onDefaultError(callback);
    }

    @Override
    public void verifyBindEmailCode(String email, String code, IResultCallback callback) {
        onDefaultError(callback);
    }

    @Override
    public void unbindEmail(String account, IResultCallback callback) {
        onDefaultError(callback);
    }

    @Override
    public void verifyUnbindEmailCode(String account, String verifyCode, IResultCallback callback) {
        onDefaultError(callback);
    }

    @Override
    public void uploadUserAvatar(String imageFilePath, String homeId, IResultCallback2<String> callback) {
        onDefaultError(callback);
    }

    @Override
    public void deleteAccount(IResultCallback callback) {
        onDefaultError(callback);
    }

    @Override
    public void setLoginStateChangedListener(ILoginStateChangedListener loginStateChangedListener) {

    }

    @Override
    public void verifyCodeOnly(@NonNull String account, @NonNull String code, IResultCallback2<Boolean> callback) {
        onDefaultError(callback);
    }

    @Override
    public void changePasswordOnly(@NonNull String account, @NonNull String password, IResultCallback2<Boolean> callback) {
        onDefaultError(callback);
    }

    @Override
    public void checkPasswordOnly(@NonNull String password, IResultCallback2<Boolean> callback) {
        onDefaultError(callback);
    }

    @Override
    public void refreshVerifyCode(@NonNull String specialId, IResultCallback2<RefreshVerifyCodeResponse.ResultBean> callback) {
        onDefaultError(callback);
    }


    private void onDefaultError(IResultCallback callback) {
        if (null != callback) {
            callback.onError(ErrorCode.ERROR_UNSUPPORTED_API, ERROR_INFO + ERROR_INFO_EXT);
        }
    }

    private void onDefaultError(IResultCallback2<?> callback) {
        if (null != callback) {
            callback.onError(ErrorCode.ERROR_UNSUPPORTED_API, ERROR_INFO + ERROR_INFO_EXT);
        }
    }

    private void onDefaultError(IRegisterCallback callback) {
        if (null != callback) {
            callback.onError(ErrorCode.ERROR_UNSUPPORTED_API, ERROR_INFO + ERROR_INFO_EXT);
        }
    }
}
