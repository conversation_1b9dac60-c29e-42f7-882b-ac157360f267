package com.dinsafer.dincore.user;

import androidx.annotation.NonNull;

import com.dinsafer.dincore.http.Api;
import com.dinsafer.dincore.http.CloudflareUploadTokenResponse;
import com.dinsafer.dincore.http.FileUploadService;
import com.dinsafer.dincore.http.StringResponseEntry;
import com.dinsafer.dincore.user.api.IResultCallback2;
import com.dinsafer.dincore.user.bean.DinUserLoginResponse;
import com.dinsafer.dincore.user.bean.RefreshVerifyCodeResponse;
import com.dinsafer.dincore.user.bean.RegisterResponse;
import com.dinsafer.dincore.utils.FileUploadUtils;
import com.dinsafer.dincore.utils.QiNiuUploadManager;
import com.qiniu.android.storage.UpCompletionHandler;

import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.Callback;


/**
 * Description:
 * Date: 2019/5/9
 * gou
 */
public class UserRepository {

    public UserRepository() {

    }

    public void getPhoneValidateCode(String account, String specialId, String verifyCode, String verifyId, Callback<StringResponseEntry> callback) {
        Api.getApi().getPhoneValidateCode(account, specialId, verifyCode, verifyId)
                .enqueue(callback);
    }

    public void validatePhoneCode(String account, String code, Callback<RegisterResponse> callback) {
        Api.getApi().validatePhoneCode(account, code)
                .enqueue(callback);
    }


    public void login(int loginType, String account, String password, Callback<DinUserLoginResponse> myRequestCallback) {
        Api.getApi().login(loginType, account, password)
                .enqueue(myRequestCallback);
    }

    public void logout(Callback<StringResponseEntry> myRequestCallback) {
        Api.getApi().logout()
                .enqueue(myRequestCallback);
    }

    public void modifyUuidPassword(String username, String password, Callback<StringResponseEntry> callback) {
        Api.getApi().modifyUidPasswordCall(username, password).enqueue(callback);
    }

    public void changePassword(String oldPassword, String newPassword, Callback<StringResponseEntry> callback) {
        Api.getApi().changePasswordCall(oldPassword, newPassword).enqueue(callback);
    }

    public void changeUuid(String newUuid, Callback<StringResponseEntry> callback) {
        Api.getApi().getChangeUidCall(newUuid).enqueue(callback);
    }

    public void getForgetPWDbyEmail(String uuid, Callback<StringResponseEntry> callback) {
        Api.getApi().getForgetPWDbyEmail(uuid).enqueue(callback);
    }

    public void comfirmForgetPWDByEmailCode(String uuid, String verifyCode, String newPassword, Callback<StringResponseEntry> callback) {
        Api.getApi().comfirmForgetPWDByEmailCode(uuid, verifyCode, newPassword).enqueue(callback);
    }

    public void getForgetPWDbyPhone(String uuid, String specialId, String verifyCode, String verifyId, Callback<StringResponseEntry> callback) {
        Api.getApi().getForgetPWDbyPhone(uuid, specialId, verifyCode, verifyId).enqueue(callback);
    }

    public void comfirmForgetPWDByPhoneCode(String uuid, String verifyCode, String newPassword, Callback<StringResponseEntry> callback) {
        Api.getApi().comfirmForgetPWDByPhoneCode(uuid, verifyCode, newPassword).enqueue(callback);
    }

    public void bindPhone(String phone, String specialId, String verifyCode, String verifyId, Callback<StringResponseEntry> callback) {
        Api.getApi().bindPhoneCall(phone, specialId, verifyCode, verifyId).enqueue(callback);
    }

    public void unbindPhone(String phone, String specialId, String verifyCode, String verifyId, Callback<StringResponseEntry> callback) {
        Api.getApi().unbindPhoneCall(phone, specialId, verifyCode, verifyId).enqueue(callback);
    }

    public void verifyBindPhone(String account, String verifyCode, Callback<StringResponseEntry> callback) {
        Api.getApi().verifyBindPhoneCall(account, verifyCode).enqueue(callback);
    }

    public void verifyUnbindPhone(String account, String verifyCode, Callback<StringResponseEntry> callback) {
        Api.getApi().verifyUnBindPhoneCall(account, verifyCode).enqueue(callback);
    }

    public void bindEmail(String email, Callback<StringResponseEntry> callback) {
        Api.getApi().bindEmailCall(email).enqueue(callback);
    }

    public void verifyBindEmailCall(String account, String code, Callback<StringResponseEntry> callback) {
        Api.getApi().verifyBindEmailCall(account, code).enqueue(callback);
    }

    public void unbindEmail(String account, Callback<StringResponseEntry> callback) {
        Api.getApi().unbindEmailCall(account).enqueue(callback);
    }

    public void verifyUnbindEmail(String account, String verifyCode, Callback<StringResponseEntry> callback) {
        Api.getApi().verifyUnBindEmailCall(account, verifyCode).enqueue(callback);
    }

    public void getUploadToken(Callback<StringResponseEntry> callback) {
        Api.getApi().getUploadToken().enqueue(callback);
    }

    public void uploadImage(String uploadToken, String filePath, final UpCompletionHandler handler) {
        QiNiuUploadManager.getInstance().getUploadManager().put(filePath, null, uploadToken, handler, null);
    }

    public void uploadUserAvatarByCloudflare(String filePath, String homeId, IResultCallback2<String> callback) {
        //type = 5 表示头像
        FileUploadService.getInstance().uploadFile(filePath, homeId, 5, callback);
    }
    public void uploadImageKey(final String imageKey,final int source, final Callback<StringResponseEntry> callback) {
        Api.getApi().uploadImageKey(imageKey, source).enqueue(callback);
    }

    public void getEmailValidateCode(String account, Callback<StringResponseEntry> callback) {
        Api.getApi().getEmailValidateCode(account).enqueue(callback);
    }

    public void validateEmailCode(String account, String code, Callback<RegisterResponse> callback) {
        Api.getApi().validateEmailCode(account, code)
                .enqueue(callback);
    }

    public void deleteAccount(Callback<StringResponseEntry> callback) {
        Api.getApi().deleteAccount()
                .enqueue(callback);
    }

    public void verifyCodeOnly(@NonNull final String account, @NonNull final String code,
                               Callback<StringResponseEntry> callback) {
        Api.getApi().verifyCodeOnly(account, code)
                .enqueue(callback);
    }

    public void changePasswordOnly(@NonNull final String account, @NonNull final String password,
                                   Callback<StringResponseEntry> callback) {
        Api.getApi().changePasswordOnly(account, password)
                .enqueue(callback);
    }

    public void checkPasswordOnly(@NonNull final String password,
                                  Callback<StringResponseEntry> callback) {
        Api.getApi().checkPasswordOnly(password)
                .enqueue(callback);
    }

    public void refreshVerifyCode(@NonNull final String specialId, Callback<RefreshVerifyCodeResponse> callback) {
        Api.getApi().refreshVerifyCode(specialId).enqueue(callback);
    }

    // ********************* 兼容模式（cawa） START***********************//

    /**
     * 兼容模式（cawa）登录或注册
     */
    public void loginCompat(String userId, String pwd, Callback<DinUserLoginResponse> myRequestCallback) {
        Api.getApi().loginCompat(userId, pwd).enqueue(myRequestCallback);
    }
    // ********************* 兼容模式（cawa） END ***********************//
}
