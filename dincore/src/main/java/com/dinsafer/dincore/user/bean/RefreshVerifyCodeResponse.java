package com.dinsafer.dincore.user.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

/**
 * @describe：
 * @date：2024/11/26
 * @author: create by Sydnee
 */
@Keep
public class RefreshVerifyCodeResponse extends BaseHttpEntry {
    /**
     * "result": {
     *         "base64": "string",
     *                 "gmtime": 1658649822246802200,
     *                 "mime_type_image": "string",
     *                 "verify_id": "string"
     *     }
     */

    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        this.Result = result;
    }

    @Keep
    public static class ResultBean {
        private String base64;
        private long gmtime;
        private String mime_type_image;
        private String verify_id;

        public String getBase64() {
            return base64;
        }

        public void setBase64(String base64) {
            this.base64 = base64;
        }

        public long getGmtime() {
            return gmtime;
        }

        public void setGmtime(long gmtime) {
            this.gmtime = gmtime;
        }

        public String getMime_type_image() {
            return mime_type_image;
        }

        public void setMime_type_image(String mime_type_image) {
            this.mime_type_image = mime_type_image;
        }

        public String getVerify_id() {
            return verify_id;
        }

        public void setVerify_id(String verify_id) {
            this.verify_id = verify_id;
        }
    }

}
