package com.dinsafer.dincore.db.cache;

import android.app.Application;
import android.os.Looper;
import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.TextUtils;

import com.dinsafer.dincore.db.gen.CacheDao;
import com.dinsafer.dincore.db.gen.DaoMaster;
import com.dinsafer.dincore.db.gen.DaoSession;

import org.greenrobot.greendao.database.Database;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/11/27 11:49
 */
@Keep
public class CacheHelper {
    private static final String DB_NAME = "cache-db";

    private static DaoSession daoSession = null;

    public static <T extends ICacheInfo> boolean saveCache(String key, T data) {
        assertInit();
        assertMainThread();
        final byte[] bytes = toByteArray(data);
        if (!TextUtils.isEmpty(key) && bytes != null && bytes.length > 0) {
            final Cache cache = new Cache();
            cache.setKey(key);
            cache.setData(bytes);
            daoSession.getCacheDao().insertOrReplace(cache);
            return true;
        }
        return false;
    }

    private static void assertMainThread() {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            throw new IllegalStateException("必须在子线程操作缓存");
        }
    }

    @Nullable
    public static <T extends ICacheInfo> T getCache(String key) {
        assertInit();
        assertMainThread();
        if (!TextUtils.isEmpty(key)) {
            final Cache cache = daoSession.getCacheDao().load(key);
            if (null != cache) {
                Object o = toObject(cache.getData());
                if (null != o) {
                    try {
                        T r = (T) o;
                        return r;
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return null;
    }

    public static boolean containKey(String key) {
        assertInit();
        if (!TextUtils.isEmpty(key)) {
            final long count = daoSession.getCacheDao().queryBuilder()
                    .where(CacheDao.Properties.Key.eq(key))
                    .count();
            return count > 0;
        }
        return false;
    }

    @Nullable
    public static boolean deleteCache(String key) {
        assertInit();
        assertMainThread();
        if (!TextUtils.isEmpty(key)) {
            final Cache cache = new Cache();
            cache.setKey(key);
            if (containKey(key)) {
                daoSession.getCacheDao().delete(cache);
                return true;
            }
        }
        return false;
    }

    private static void assertInit() {
        if (null == daoSession) {
            throw new IllegalStateException("在使用之前必须先调用init方法进行初始化");
        }
    }

    public static void init(@NonNull Application app) {
        if (null == daoSession) {
            DaoMaster.DevOpenHelper helper = new DaoMaster.DevOpenHelper(app, DB_NAME);
            Database db = helper.getWritableDb();
            daoSession = new DaoMaster(db).newSession();
        }
    }

    @Nullable
    private static <T extends ICacheInfo> byte[] toByteArray(@NonNull T body) {
        ByteArrayOutputStream baos = null;
        ObjectOutputStream oos = null;
        try {
            baos = new ByteArrayOutputStream();
            oos = new ObjectOutputStream(baos);
            oos.writeObject(body);
            oos.flush();
            return baos.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != baos) {
                    baos.close();
                }
                if (null != oos) {
                    oos.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    private static Object toObject(byte[] data) {
        ByteArrayInputStream bais = null;
        ObjectInputStream ois = null;
        try {
            bais = new ByteArrayInputStream(data);
            ois = new ObjectInputStream(bais);
            return ois.readObject();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != bais)
                    bais.close();
                if (null != ois)
                    ois.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
