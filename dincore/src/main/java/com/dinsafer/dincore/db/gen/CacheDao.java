package com.dinsafer.dincore.db.gen;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.dinsafer.dincore.db.cache.Cache;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "common_cache".
*/
public class CacheDao extends AbstractDao<Cache, String> {

    public static final String TABLENAME = "common_cache";

    /**
     * Properties of entity Cache.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Key = new Property(0, String.class, "key", true, "KEY");
        public final static Property Data = new Property(1, byte[].class, "data", false, "data");
    }


    public CacheDao(DaoConfig config) {
        super(config);
    }
    
    public CacheDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"common_cache\" (" + //
                "\"KEY\" TEXT PRIMARY KEY NOT NULL UNIQUE ," + // 0: key
                "\"data\" BLOB);"); // 1: data
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"common_cache\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, Cache entity) {
        stmt.clearBindings();
 
        String key = entity.getKey();
        if (key != null) {
            stmt.bindString(1, key);
        }
 
        byte[] data = entity.getData();
        if (data != null) {
            stmt.bindBlob(2, data);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, Cache entity) {
        stmt.clearBindings();
 
        String key = entity.getKey();
        if (key != null) {
            stmt.bindString(1, key);
        }
 
        byte[] data = entity.getData();
        if (data != null) {
            stmt.bindBlob(2, data);
        }
    }

    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getString(offset + 0);
    }    

    @Override
    public Cache readEntity(Cursor cursor, int offset) {
        Cache entity = new Cache( //
            cursor.isNull(offset + 0) ? null : cursor.getString(offset + 0), // key
            cursor.isNull(offset + 1) ? null : cursor.getBlob(offset + 1) // data
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, Cache entity, int offset) {
        entity.setKey(cursor.isNull(offset + 0) ? null : cursor.getString(offset + 0));
        entity.setData(cursor.isNull(offset + 1) ? null : cursor.getBlob(offset + 1));
     }
    
    @Override
    protected final String updateKeyAfterInsert(Cache entity, long rowId) {
        return entity.getKey();
    }
    
    @Override
    public String getKey(Cache entity) {
        if(entity != null) {
            return entity.getKey();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(Cache entity) {
        return entity.getKey() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
