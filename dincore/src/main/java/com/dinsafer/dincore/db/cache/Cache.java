package com.dinsafer.dincore.db.cache;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Property;
import org.greenrobot.greendao.annotation.Unique;
import org.greenrobot.greendao.annotation.Generated;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/11/27 11:06
 */
@Entity(nameInDb = "common_cache")
public class Cache {
    @Id()
    @Unique
    public String key;
    @Property(nameInDb = "data")
    public byte[] data;

    @Generated(hash = 447348033)
    public Cache(String key, byte[] data) {
        this.key = key;
        this.data = data;
    }

    @Generated(hash = 1305017356)
    public Cache() {
    }

    public String getKey() {
        return this.key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public byte[] getData() {
        return this.data;
    }

    public void setData(byte[] data) {
        this.data = data;
    }

}
