package com.dinsafer.dincore.db.cache;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import android.text.TextUtils;

import com.dinsafer.dincore.utils.MD5;
import com.dinsafer.dssupport.utils.HexUtil;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * DeviceInfo缓存工具类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @version 1.0
 * @since 2022/11/26 17:01
 */
@Keep
public class DeviceCacheHelper {

    private final static ThreadPoolExecutor operatorPools = new ThreadPoolExecutor(
            0, 5, 60, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(50),
            new ThreadFactory() {
                private final AtomicInteger index = new AtomicInteger(0);

                @Override
                public Thread newThread(Runnable r) {
                    final Thread t = new Thread(r);
                    t.setName("CacheOperateThread-" + index.getAndIncrement());
                    return t;
                }
            }, new ThreadPoolExecutor.DiscardOldestPolicy());

    @Nullable
    public static <T extends ICacheInfo> T getCache(final String homeId, String userId,
                                                    final String identify, @NonNull Class<T> clazz) {
        final String key = getDeviceCacheKey(homeId, userId, identify);
        T cache = null;
        if (!TextUtils.isEmpty(key)) {
            cache = CacheHelper.getCache(key);
        }
        return cache;
    }

    public static <T extends ICacheInfo> boolean saveCacheAsync(final String homeId, String userId,
                                                                final String identify, T cacheInfo) {
        return saveCacheInner(homeId, userId, identify, cacheInfo, false);
    }

    public static boolean removeCacheASync(final String homeId, String userId, final String identify) {
        return removeCacheInner(homeId, userId, identify, false);
    }

    public static <T extends ICacheInfo> boolean saveCacheSync(final String homeId, String userId,
                                                               final String identify, T cacheInfo) {
        return saveCacheInner(homeId, userId, identify, cacheInfo, true);
    }

    public static boolean removeCacheSync(final String homeId, String userId, final String identify) {
        return removeCacheInner(homeId, userId, identify, true);
    }

    public static boolean containKey(final String homeId, String userId, final String identify) {
        final String key = getDeviceCacheKey(homeId, userId, identify);
        if (TextUtils.isEmpty(key)) {
            return false;
        }
        return CacheHelper.containKey(key);
    }

    private static <T extends ICacheInfo> boolean saveCacheInner(final String homeId, String userId,
                                                                 final String identify, T cacheInfo, boolean sync) {
        final String key = getDeviceCacheKey(homeId, userId, identify);
        if (TextUtils.isEmpty(key) || null == cacheInfo || !cacheInfo.isNeedSaveCache()) {
            return false;
        }

        if (sync) {
            return CacheHelper.saveCache(key, cacheInfo);
        } else {
            operatorPools.execute(() -> CacheHelper.saveCache(key, cacheInfo));
            return true;
        }
    }

    private static boolean removeCacheInner(final String homeId, String userId, final String identify, boolean sync) {
        final String key = getDeviceCacheKey(homeId, userId, identify);
        if (!TextUtils.isEmpty(key) && CacheHelper.containKey(key)) {
            if (sync) {
                return CacheHelper.deleteCache(key);
            } else {
                operatorPools.execute(() -> CacheHelper.deleteCache(key));
            }
            return true;
        }
        return false;
    }

    /**
     * 获取缓存的KEY
     *
     * @param homeId 当前家庭的id
     * @param userId 当前用户的id
     * @return 缓存KEY，如果homeId或useId为空，key返回null
     */
    @Nullable
    public static String getDeviceCacheKey(final String homeId, final String userId, final String identify) {
        if (TextUtils.isEmpty(homeId) || TextUtils.isEmpty(userId) || TextUtils.isEmpty(identify)) {
            return null;
        }

        final String key = homeId + userId + identify;
        byte[] bytes = MD5.get(key.getBytes(StandardCharsets.UTF_8));
        if (null == bytes || bytes.length == 0) {
            return null;
        }
        return HexUtil.bytesToHexString(bytes);
    }
}
