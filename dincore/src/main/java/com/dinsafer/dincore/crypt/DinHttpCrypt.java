package com.dinsafer.dincore.crypt;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dssupport.crypt.Encryption;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.dssupport.utils.HexUtil;

import org.iq80.snappy.Snappy;

public class DinHttpCrypt implements ICrypt {
    private static final String TAG = DinHttpCrypt.class.getSimpleName();

    @Override
    public byte[] decode(byte[] msg) {
        if (msg == null) {
            return new byte[0];
        }
        DDLog.d(TAG, "-------------------------解密前--------------------------");
        DDLog.d(TAG, "| " + new String(msg));
        DDLog.d(TAG, "-------------------------解密后--------------------------");
        try {
            byte[] tempD = HexUtil.hexStringToBytes(new String(msg));
            byte[] result = snappy(Encryption.decodeWithRC4(DinCore.getInstance().getAppSecret(), tempD));

            DDLog.d(TAG, "| " + new String(result));
            DDLog.d(TAG, "--------------------------------------------------------");
            return result;
        } catch (Exception e) {
            DDLog.d(TAG, "| 解密失败");
            DDLog.d(TAG, "--------------------------------------------------------");
            return msg;
        }
    }

    public static byte[] snappy(byte[] data) {
        if (data == null)
            return null;
        return Snappy.uncompress(data, 0, data.length);
    }

    @Override
    public byte[] encode(byte[] msg) {
        if (msg == null) {
            return new byte[0];
        }
        DDLog.d(TAG, "-------------------------加密前--------------------------");
        DDLog.d(TAG, "| " + new String(msg));
        DDLog.d(TAG, "-------------------------加密后Hex--------------------------");
        try {
            byte[] result = Encryption.encryptWithRC4(DinCore.getInstance().getAppSecret(), msg);
            DDLog.d(TAG, "| " + HexUtil.bytesToHexString(result));
            DDLog.d(TAG, "--------------------------------------------------------");
            return result;
        } catch (Exception e) {
            DDLog.d(TAG, "| 加密失败");
            DDLog.d(TAG, "--------------------------------------------------------");
            return msg;
        }
    }
}
