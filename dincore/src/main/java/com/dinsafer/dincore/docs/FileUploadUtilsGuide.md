# FileUploadUtils 使用指南

## 概述

`FileUploadUtils` 是一个专门用于文件上传相关数据获取和处理的工具类，提供了丰富的文件信息获取、验证和处理功能。

## 主要功能

### 1. 文件信息获取

#### 获取完整文件信息
```java
FileUploadUtils.FileInfo fileInfo = FileUploadUtils.getFileInfo("/sdcard/photo.jpg");
if (fileInfo != null) {
    System.out.println("文件名: " + fileInfo.fileName);
    System.out.println("文件大小: " + fileInfo.readableSize);
    System.out.println("MIME类型: " + fileInfo.contentType);
    System.out.println("类型编号: " + fileInfo.typeCode);
    System.out.println("是否为图片: " + fileInfo.isImage);
}
```

#### 单独获取特定信息
```java
// 获取文件扩展名
String extension = FileUploadUtils.getFileExtension("/sdcard/photo.jpg"); // ".jpg"

// 获取Content-Type
String contentType = FileUploadUtils.getContentTypeFromFile("/sdcard/photo.jpg"); // "image/jpeg"

// 获取文件类型编号
int typeCode = FileUploadUtils.getFileTypeByContentType("image/jpeg"); // 5

// 获取可读的文件大小
String readableSize = FileUploadUtils.getReadableFileSize(1048576); // "1.0 MB"
```

### 2. 文件类型判断

```java
String filePath = "/sdcard/video.mp4";

boolean isImage = FileUploadUtils.isImageFile(filePath);
boolean isVideo = FileUploadUtils.isVideoFile(filePath);
boolean isAudio = FileUploadUtils.isAudioFile(filePath);
boolean isPdf = FileUploadUtils.isPdfFile(filePath);
```

### 3. 文件验证

#### 基本验证
```java
// 检查文件是否存在且可读
boolean isValid = FileUploadUtils.validateFile("/sdcard/photo.jpg");
```

#### 带大小限制的验证
```java
// 检查文件大小是否在10MB以内
boolean isValid = FileUploadUtils.validateFile("/sdcard/photo.jpg", 10 * 1024 * 1024);
```

#### 使用推荐大小限制
```java
String filePath = "/sdcard/video.mp4";
long maxSize = FileUploadUtils.getRecommendedMaxSize(filePath);
boolean isValid = FileUploadUtils.validateFile(filePath, maxSize);
```

### 4. 文件大小常量

```java
// 预定义的大小常量
long maxImageSize = FileUploadUtils.FileSizeConstants.MAX_IMAGE_SIZE;    // 10MB
long maxVideoSize = FileUploadUtils.FileSizeConstants.MAX_VIDEO_SIZE;    // 100MB
long maxAudioSize = FileUploadUtils.FileSizeConstants.MAX_AUDIO_SIZE;    // 50MB
long maxDocSize = FileUploadUtils.FileSizeConstants.MAX_DOCUMENT_SIZE;   // 20MB

// 基本单位
long kb = FileUploadUtils.FileSizeConstants.KB;  // 1024
long mb = FileUploadUtils.FileSizeConstants.MB;  // 1024 * 1024
long gb = FileUploadUtils.FileSizeConstants.GB;  // 1024 * 1024 * 1024
```

## 文件类型映射

| Content-Type前缀 | 类型编号 | 说明 |
|-----------------|---------|------|
| `image/`        | 5       | 图片文件 |
| `video/`        | 6       | 视频文件 |
| `audio/`        | 7       | 音频文件 |
| `application/pdf` | 8     | PDF文档 |
| 其他            | 9       | 其他类型 |

## 与Api集成使用

### 方式1: 手动指定参数
```java
String filePath = "/sdcard/photo.jpg";
String contentType = FileUploadUtils.getContentTypeFromFile(filePath);
int type = FileUploadUtils.getFileTypeByContentType(contentType);

Api.getApi().uploadFileToCloudflareR2(filePath, contentType, homeId, type, callback);
```

### 方式2: 让Api自动处理
```java
// Api会自动调用FileUploadUtils获取Content-Type
Api.getApi().uploadFileToCloudflareR2(filePath, null, homeId, 0, callback);
```

### 方式3: 通过BaseHome（推荐）
```java
// BaseHome提供了自动检测Content-Type的重载方法
baseHome.uploadFileToCloudflareR2(filePath, callback);
```

## 最佳实践

### 1. 上传前验证
```java
public void uploadFileWithValidation(String filePath) {
    // 1. 基本验证
    if (!FileUploadUtils.validateFile(filePath)) {
        showError("文件无效或不存在");
        return;
    }
    
    // 2. 大小验证
    long maxSize = FileUploadUtils.getRecommendedMaxSize(filePath);
    if (!FileUploadUtils.validateFile(filePath, maxSize)) {
        showError("文件过大，请选择较小的文件");
        return;
    }
    
    // 3. 获取文件信息
    FileUploadUtils.FileInfo fileInfo = FileUploadUtils.getFileInfo(filePath);
    if (fileInfo == null) {
        showError("无法读取文件信息");
        return;
    }
    
    // 4. 业务逻辑验证
    if (fileInfo.isVideo && fileInfo.fileSize > 50 * FileUploadUtils.FileSizeConstants.MB) {
        showWarning("视频文件较大，上传可能需要较长时间");
    }
    
    // 5. 执行上传
    baseHome.uploadFileToCloudflareR2(filePath, callback);
}
```

### 2. 批量文件处理
```java
public void processBatchFiles(String[] filePaths) {
    List<String> validFiles = new ArrayList<>();
    long totalSize = 0;
    
    for (String filePath : filePaths) {
        FileUploadUtils.FileInfo fileInfo = FileUploadUtils.getFileInfo(filePath);
        if (fileInfo != null) {
            long maxSize = FileUploadUtils.getRecommendedMaxSize(filePath);
            if (fileInfo.fileSize <= maxSize) {
                validFiles.add(filePath);
                totalSize += fileInfo.fileSize;
            } else {
                Log.w(TAG, "跳过过大文件: " + fileInfo.fileName);
            }
        }
    }
    
    Log.i(TAG, "有效文件: " + validFiles.size() + "/" + filePaths.length);
    Log.i(TAG, "总大小: " + FileUploadUtils.getReadableFileSize(totalSize));
    
    // 批量上传有效文件
    for (String filePath : validFiles) {
        baseHome.uploadFileToCloudflareR2(filePath, callback);
    }
}
```

### 3. 文件类型特定处理
```java
public void handleFileByType(String filePath) {
    FileUploadUtils.FileInfo fileInfo = FileUploadUtils.getFileInfo(filePath);
    if (fileInfo == null) return;
    
    if (fileInfo.isImage) {
        // 图片特定处理
        if (fileInfo.fileSize > 5 * FileUploadUtils.FileSizeConstants.MB) {
            // 建议压缩
            showDialog("图片较大，是否压缩后上传？");
        }
    } else if (fileInfo.isVideo) {
        // 视频特定处理
        if (!fileInfo.contentType.equals("video/mp4")) {
            showWarning("建议使用MP4格式的视频文件");
        }
    } else if (fileInfo.isPdf) {
        // PDF特定处理
        Log.i(TAG, "准备上传PDF文档: " + fileInfo.fileName);
    }
    
    baseHome.uploadFileToCloudflareR2(filePath, callback);
}
```

## 错误处理

### 常见错误及处理
```java
public void handleUploadErrors(String filePath) {
    try {
        // 验证文件
        if (!FileUploadUtils.validateFile(filePath)) {
            throw new IllegalArgumentException("文件验证失败");
        }
        
        // 获取文件信息
        FileUploadUtils.FileInfo fileInfo = FileUploadUtils.getFileInfo(filePath);
        if (fileInfo == null) {
            throw new IllegalStateException("无法获取文件信息");
        }
        
        // 检查文件大小
        long maxSize = FileUploadUtils.getRecommendedMaxSize(filePath);
        if (fileInfo.fileSize > maxSize) {
            throw new IllegalArgumentException("文件过大: " + fileInfo.readableSize + 
                " (最大: " + FileUploadUtils.getReadableFileSize(maxSize) + ")");
        }
        
        // 执行上传
        baseHome.uploadFileToCloudflareR2(filePath, callback);
        
    } catch (IllegalArgumentException e) {
        Log.e(TAG, "参数错误: " + e.getMessage());
        showError(e.getMessage());
    } catch (IllegalStateException e) {
        Log.e(TAG, "状态错误: " + e.getMessage());
        showError("文件处理失败");
    } catch (Exception e) {
        Log.e(TAG, "未知错误: " + e.getMessage());
        showError("上传失败，请重试");
    }
}
```

## 性能优化建议

### 1. 缓存文件信息
```java
private Map<String, FileUploadUtils.FileInfo> fileInfoCache = new HashMap<>();

public FileUploadUtils.FileInfo getCachedFileInfo(String filePath) {
    if (!fileInfoCache.containsKey(filePath)) {
        FileUploadUtils.FileInfo info = FileUploadUtils.getFileInfo(filePath);
        if (info != null) {
            fileInfoCache.put(filePath, info);
        }
    }
    return fileInfoCache.get(filePath);
}
```

### 2. 异步处理大文件
```java
public void processLargeFileAsync(String filePath, Callback callback) {
    new Thread(() -> {
        FileUploadUtils.FileInfo fileInfo = FileUploadUtils.getFileInfo(filePath);
        
        // 在主线程回调
        new Handler(Looper.getMainLooper()).post(() -> {
            if (fileInfo != null) {
                callback.onSuccess(fileInfo);
            } else {
                callback.onError("文件处理失败");
            }
        });
    }).start();
}
```

## 总结

`FileUploadUtils` 提供了完整的文件上传相关工具方法，主要优势：

- ✅ **功能完整**：涵盖文件信息获取、验证、类型判断等所有需求
- ✅ **易于使用**：简单的静态方法调用，无需实例化
- ✅ **类型安全**：提供了完整的文件类型映射和判断
- ✅ **性能优化**：合理的缓存和异步处理建议
- ✅ **集成友好**：与Api和BaseHome完美集成
- ✅ **扩展性好**：可以轻松添加新的文件类型和验证规则

通过使用 `FileUploadUtils`，可以大大简化文件上传相关的开发工作，提高代码质量和用户体验。
