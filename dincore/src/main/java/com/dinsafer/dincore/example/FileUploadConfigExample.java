package com.dinsafer.dincore.example;

import com.dinsafer.dincore.http.FileUploadService;
import com.dinsafer.dincore.user.api.IResultCallback2;
import com.dinsafer.dssupport.utils.DDLog;

/**
 * FileUploadService配置参数使用示例
 * 展示如何使用不同的上传模式来优化性能
 * 
 * <AUTHOR> Assistant
 * @since 2025/7/16
 */
public class FileUploadConfigExample {
    private static final String TAG = "FileUploadConfigExample";
    
    /**
     * 演示不同上传模式的使用
     */
    public void demonstrateUploadModes() {
        DDLog.i(TAG, "=== 演示不同上传模式 ===");
        
        String filePath = "/sdcard/test_image.jpg";
        String homeId = "test_home_id";
        int type = 5;
        
        // 模式1: FILE_ONLY - 只上传文件，返回imageKey
        DDLog.i(TAG, "--- 模式1: FILE_ONLY ---");
        FileUploadService.getInstance()
                .setUploadMode(FileUploadService.UploadMode.FILE_ONLY)
                .uploadFile(filePath, homeId, type, new IResultCallback2<String>() {
                    @Override
                    public void onSuccess(String imageKey) {
                        DDLog.i(TAG, "FILE_ONLY模式成功，获得imageKey: " + imageKey);
                        // 调用者可以决定是否/何时上传key
                    }
                    
                    @Override
                    public void onError(int errorCode, String errorMsg) {
                        DDLog.e(TAG, "FILE_ONLY模式失败: " + errorMsg);
                    }
                });
        
        // 模式2: AUTO_UPLOAD_KEY - 自动上传文件和key
        DDLog.i(TAG, "--- 模式2: AUTO_UPLOAD_KEY ---");
        FileUploadService.getInstance()
                .setUploadMode(FileUploadService.UploadMode.AUTO_UPLOAD_KEY)
                .uploadFile(filePath, homeId, type, new IResultCallback2<String>() {
                    @Override
                    public void onSuccess(String result) {
                        DDLog.i(TAG, "AUTO_UPLOAD_KEY模式成功: " + result);
                        // 这里的result是最终的上传结果
                    }
                    
                    @Override
                    public void onError(int errorCode, String errorMsg) {
                        DDLog.e(TAG, "AUTO_UPLOAD_KEY模式失败: " + errorMsg);
                    }
                });
        
        // 模式3: SMART_MODE - 智能模式
        DDLog.i(TAG, "--- 模式3: SMART_MODE ---");
        FileUploadService.getInstance()
                .setUploadMode(FileUploadService.UploadMode.SMART_MODE)
                .uploadFile(filePath, homeId, type, new IResultCallback2<String>() {
                    @Override
                    public void onSuccess(String result) {
                        DDLog.i(TAG, "SMART_MODE模式成功: " + result);
                        // 根据文件大小和类型自动选择的结果
                    }
                    
                    @Override
                    public void onError(int errorCode, String errorMsg) {
                        DDLog.e(TAG, "SMART_MODE模式失败: " + errorMsg);
                    }
                });
    }
    
    /**
     * 基于性能考虑的配置建议
     */
    public void performanceOptimizedConfig() {
        DDLog.i(TAG, "=== 基于性能考虑的配置建议 ===");
        
        // 场景1: 批量上传小图片 - 使用AUTO_UPLOAD_KEY模式
        DDLog.i(TAG, "场景1: 批量上传小图片");
        FileUploadService.getInstance().setUploadMode(FileUploadService.UploadMode.AUTO_UPLOAD_KEY);
        
        String[] smallImages = {"/sdcard/thumb1.jpg", "/sdcard/thumb2.jpg", "/sdcard/thumb3.jpg"};
        for (String imagePath : smallImages) {
            uploadSmallImage(imagePath, "home_id", 5);
        }
        
        // 场景2: 上传大视频文件 - 使用FILE_ONLY模式
        DDLog.i(TAG, "场景2: 上传大视频文件");
        FileUploadService.getInstance().setUploadMode(FileUploadService.UploadMode.FILE_ONLY);
        
        String largeVideo = "/sdcard/large_video.mp4";
        uploadLargeVideo(largeVideo, "home_id", 6);
        
        // 场景3: 混合文件类型 - 使用SMART_MODE模式
        DDLog.i(TAG, "场景3: 混合文件类型");
        FileUploadService.getInstance().setUploadMode(FileUploadService.UploadMode.SMART_MODE);
        
        String[] mixedFiles = {
            "/sdcard/small_image.jpg",    // 小图片，会自动上传key
            "/sdcard/large_video.mp4",    // 大视频，只上传文件
            "/sdcard/document.pdf",       // 文档，会自动上传key
            "/sdcard/huge_file.zip"       // 大文件，只上传文件
        };
        
        for (String filePath : mixedFiles) {
            uploadMixedFile(filePath, "home_id");
        }
    }
    
    /**
     * 链式调用示例
     */
    public void chainCallExample() {
        DDLog.i(TAG, "=== 链式调用示例 ===");
        
        // 支持链式调用，代码更简洁
        FileUploadService.getInstance()
                .setUploadMode(FileUploadService.UploadMode.SMART_MODE)
                .uploadFile("/sdcard/test.jpg", "home_id", 5, new IResultCallback2<String>() {
                    @Override
                    public void onSuccess(String result) {
                        DDLog.i(TAG, "链式调用上传成功: " + result);
                    }
                    
                    @Override
                    public void onError(int errorCode, String errorMsg) {
                        DDLog.e(TAG, "链式调用上传失败: " + errorMsg);
                    }
                });
    }
    
    /**
     * 动态切换模式示例
     */
    public void dynamicModeSwitch() {
        DDLog.i(TAG, "=== 动态切换模式示例 ===");
        
        String filePath = "/sdcard/test_file.jpg";
        String homeId = "home_id";
        int type = 5;
        
        // 根据网络状态动态切换模式
        if (isNetworkFast()) {
            DDLog.i(TAG, "网络较快，使用AUTO_UPLOAD_KEY模式");
            FileUploadService.getInstance().setUploadMode(FileUploadService.UploadMode.AUTO_UPLOAD_KEY);
        } else if (isNetworkSlow()) {
            DDLog.i(TAG, "网络较慢，使用FILE_ONLY模式");
            FileUploadService.getInstance().setUploadMode(FileUploadService.UploadMode.FILE_ONLY);
        } else {
            DDLog.i(TAG, "网络一般，使用SMART_MODE模式");
            FileUploadService.getInstance().setUploadMode(FileUploadService.UploadMode.SMART_MODE);
        }
        
        FileUploadService.getInstance().uploadFile(filePath, homeId, type, new IResultCallback2<String>() {
            @Override
            public void onSuccess(String result) {
                DDLog.i(TAG, "动态模式上传成功: " + result);
            }
            
            @Override
            public void onError(int errorCode, String errorMsg) {
                DDLog.e(TAG, "动态模式上传失败: " + errorMsg);
            }
        });
    }
    
    /**
     * 应用启动时的配置建议
     */
    public void applicationStartupConfig() {
        DDLog.i(TAG, "=== 应用启动时的配置建议 ===");
        
        // 根据设备性能和网络状况设置默认模式
        if (isHighPerformanceDevice() && isWifiConnected()) {
            DDLog.i(TAG, "高性能设备 + WiFi环境，设置为AUTO_UPLOAD_KEY模式");
            FileUploadService.getInstance().setUploadMode(FileUploadService.UploadMode.AUTO_UPLOAD_KEY);
        } else if (isLowEndDevice() || isMobileNetwork()) {
            DDLog.i(TAG, "低端设备或移动网络，设置为FILE_ONLY模式");
            FileUploadService.getInstance().setUploadMode(FileUploadService.UploadMode.FILE_ONLY);
        } else {
            DDLog.i(TAG, "一般情况，设置为SMART_MODE模式");
            FileUploadService.getInstance().setUploadMode(FileUploadService.UploadMode.SMART_MODE);
        }
        
        DDLog.i(TAG, "当前配置模式: " + FileUploadService.getInstance().getUploadMode());
    }
    
    /**
     * 性能监控和优化建议
     */
    public void performanceMonitoring() {
        DDLog.i(TAG, "=== 性能监控和优化建议 ===");
        
        DDLog.i(TAG, "各模式性能特点:");
        DDLog.i(TAG, "FILE_ONLY模式:");
        DDLog.i(TAG, "  优点: 上传速度快，网络占用少，适合大文件");
        DDLog.i(TAG, "  缺点: 需要手动管理key上传，代码复杂度高");
        DDLog.i(TAG, "  适用: 大文件、批量上传、网络较慢的场景");
        
        DDLog.i(TAG, "AUTO_UPLOAD_KEY模式:");
        DDLog.i(TAG, "  优点: 使用简单，一次调用完成所有操作");
        DDLog.i(TAG, "  缺点: 网络占用较多，大文件上传时间长");
        DDLog.i(TAG, "  适用: 小文件、简单场景、网络较好的环境");
        
        DDLog.i(TAG, "SMART_MODE模式:");
        DDLog.i(TAG, "  优点: 自动优化，平衡性能和易用性");
        DDLog.i(TAG, "  缺点: 逻辑复杂，可能不适合特殊需求");
        DDLog.i(TAG, "  适用: 混合文件类型、不确定文件大小的场景");
        
        DDLog.i(TAG, "优化建议:");
        DDLog.i(TAG, "1. 根据应用场景选择合适的默认模式");
        DDLog.i(TAG, "2. 在关键上传场景动态切换模式");
        DDLog.i(TAG, "3. 监控上传成功率和耗时，调整策略");
        DDLog.i(TAG, "4. 考虑用户网络环境和设备性能");
    }
    
    // 辅助方法
    private void uploadSmallImage(String imagePath, String homeId, int type) {
        FileUploadService.getInstance().uploadFile(imagePath, homeId, type, createCallback("小图片"));
    }
    
    private void uploadLargeVideo(String videoPath, String homeId, int type) {
        FileUploadService.getInstance().uploadFile(videoPath, homeId, type, createCallback("大视频"));
    }
    
    private void uploadMixedFile(String filePath, String homeId) {
        int type = getFileTypeByPath(filePath);
        FileUploadService.getInstance().uploadFile(filePath, homeId, type, createCallback("混合文件"));
    }
    
    private IResultCallback2<String> createCallback(String fileType) {
        return new IResultCallback2<String>() {
            @Override
            public void onSuccess(String result) {
                DDLog.i(TAG, fileType + "上传成功: " + result);
            }
            
            @Override
            public void onError(int errorCode, String errorMsg) {
                DDLog.e(TAG, fileType + "上传失败: " + errorMsg);
            }
        };
    }
    
    // 模拟方法
    private boolean isNetworkFast() { return true; }
    private boolean isNetworkSlow() { return false; }
    private boolean isHighPerformanceDevice() { return true; }
    private boolean isLowEndDevice() { return false; }
    private boolean isWifiConnected() { return true; }
    private boolean isMobileNetwork() { return false; }
    private int getFileTypeByPath(String filePath) { return 5; }
}
