package com.dinsafer.dincore.example;

import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dincore.http.Api;
import com.dinsafer.dincore.http.FileUploadUtils;
import com.dinsafer.dssupport.utils.DDLog;

/**
 * FileUploadUtils使用示例
 * 
 * <AUTHOR> Assistant
 * @since 2025/7/16
 */
public class FileUploadUtilsExample {
    private static final String TAG = "FileUploadUtilsExample";
    
    /**
     * 演示FileUploadUtils的基本功能
     */
    public void demonstrateBasicFeatures() {
        DDLog.i(TAG, "=== FileUploadUtils基本功能演示 ===");
        
        String testFilePath = "/sdcard/test_image.jpg";
        
        // 1. 获取文件信息
        FileUploadUtils.FileInfo fileInfo = FileUploadUtils.getFileInfo(testFilePath);
        if (fileInfo != null) {
            DDLog.i(TAG, "文件信息: " + fileInfo.toString());
            DDLog.i(TAG, "文件名: " + fileInfo.fileName);
            DDLog.i(TAG, "文件大小: " + fileInfo.readableSize);
            DDLog.i(TAG, "MIME类型: " + fileInfo.contentType);
            DDLog.i(TAG, "类型编号: " + fileInfo.typeCode);
            DDLog.i(TAG, "扩展名: " + fileInfo.extension);
            DDLog.i(TAG, "是否为图片: " + fileInfo.isImage);
            DDLog.i(TAG, "是否为视频: " + fileInfo.isVideo);
        } else {
            DDLog.e(TAG, "无法获取文件信息");
        }
        
        // 2. 单独获取各种信息
        DDLog.i(TAG, "--- 单独获取信息 ---");
        DDLog.i(TAG, "扩展名: " + FileUploadUtils.getFileExtension(testFilePath));
        DDLog.i(TAG, "Content-Type: " + FileUploadUtils.getContentTypeFromFile(testFilePath));
        DDLog.i(TAG, "文件类型编号: " + FileUploadUtils.getFileTypeByContentType("image/jpeg"));
        DDLog.i(TAG, "是否为图片: " + FileUploadUtils.isImageFile(testFilePath));
        DDLog.i(TAG, "推荐最大大小: " + FileUploadUtils.getReadableFileSize(FileUploadUtils.getRecommendedMaxSize(testFilePath)));
        
        // 3. 文件验证
        DDLog.i(TAG, "--- 文件验证 ---");
        boolean isValid = FileUploadUtils.validateFile(testFilePath);
        DDLog.i(TAG, "文件是否有效: " + isValid);
        
        boolean isValidWithSize = FileUploadUtils.validateFile(testFilePath, FileUploadUtils.FileSizeConstants.MAX_IMAGE_SIZE);
        DDLog.i(TAG, "文件大小是否符合限制: " + isValidWithSize);
    }
    
    /**
     * 演示不同文件类型的处理
     */
    public void demonstrateDifferentFileTypes() {
        DDLog.i(TAG, "=== 不同文件类型处理演示 ===");
        
        String[] testFiles = {
            "/sdcard/photo.jpg",
            "/sdcard/video.mp4",
            "/sdcard/music.mp3",
            "/sdcard/document.pdf",
            "/sdcard/archive.zip"
        };
        
        for (String filePath : testFiles) {
            DDLog.i(TAG, "--- 分析文件: " + filePath + " ---");
            
            String contentType = FileUploadUtils.getContentTypeFromFile(filePath);
            int typeCode = FileUploadUtils.getFileTypeByContentType(contentType);
            long recommendedMaxSize = FileUploadUtils.getRecommendedMaxSize(filePath);
            
            DDLog.i(TAG, "Content-Type: " + contentType);
            DDLog.i(TAG, "类型编号: " + typeCode);
            DDLog.i(TAG, "推荐最大大小: " + FileUploadUtils.getReadableFileSize(recommendedMaxSize));
            
            // 类型判断
            if (FileUploadUtils.isImageFile(filePath)) {
                DDLog.i(TAG, "这是一个图片文件");
            } else if (FileUploadUtils.isVideoFile(filePath)) {
                DDLog.i(TAG, "这是一个视频文件");
            } else if (FileUploadUtils.isAudioFile(filePath)) {
                DDLog.i(TAG, "这是一个音频文件");
            } else if (FileUploadUtils.isPdfFile(filePath)) {
                DDLog.i(TAG, "这是一个PDF文件");
            } else {
                DDLog.i(TAG, "这是其他类型的文件");
            }
        }
    }
    
    /**
     * 演示文件大小处理
     */
    public void demonstrateFileSizeHandling() {
        DDLog.i(TAG, "=== 文件大小处理演示 ===");
        
        // 演示不同大小的可读格式
        long[] sizes = {
            512L,                                    // 512 B
            1024L,                                   // 1 KB
            1024L * 1024L,                          // 1 MB
            5L * 1024L * 1024L,                     // 5 MB
            100L * 1024L * 1024L,                   // 100 MB
            1024L * 1024L * 1024L                   // 1 GB
        };
        
        for (long size : sizes) {
            DDLog.i(TAG, size + " bytes = " + FileUploadUtils.getReadableFileSize(size));
        }
        
        // 演示大小常量
        DDLog.i(TAG, "--- 大小常量 ---");
        DDLog.i(TAG, "最大图片大小: " + FileUploadUtils.getReadableFileSize(FileUploadUtils.FileSizeConstants.MAX_IMAGE_SIZE));
        DDLog.i(TAG, "最大视频大小: " + FileUploadUtils.getReadableFileSize(FileUploadUtils.FileSizeConstants.MAX_VIDEO_SIZE));
        DDLog.i(TAG, "最大音频大小: " + FileUploadUtils.getReadableFileSize(FileUploadUtils.FileSizeConstants.MAX_AUDIO_SIZE));
        DDLog.i(TAG, "最大文档大小: " + FileUploadUtils.getReadableFileSize(FileUploadUtils.FileSizeConstants.MAX_DOCUMENT_SIZE));
    }
    
    /**
     * 演示与Api的集成使用
     */
    public void demonstrateApiIntegration() {
        DDLog.i(TAG, "=== 与Api集成使用演示 ===");
        
        String testFilePath = "/sdcard/test_image.jpg";
        String homeId = "test_home_id";
        
        // 方式1: 手动指定Content-Type和类型
        String contentType = FileUploadUtils.getContentTypeFromFile(testFilePath);
        int type = FileUploadUtils.getFileTypeByContentType(contentType);
        
        DDLog.i(TAG, "方式1: 手动指定参数");
        DDLog.i(TAG, "Content-Type: " + contentType);
        DDLog.i(TAG, "类型编号: " + type);
        
        Api.getApi().uploadFileToCloudflareR2(testFilePath, contentType, homeId, type, new IDefaultCallBack2<String>() {
            @Override
            public void onSuccess(String result) {
                DDLog.i(TAG, "方式1上传成功: " + result);
            }
            
            @Override
            public void onError(int errorCode, String errorMsg) {
                DDLog.e(TAG, "方式1上传失败: " + errorMsg);
            }
        });
        
        // 方式2: 让Api自动检测Content-Type
        DDLog.i(TAG, "方式2: 自动检测Content-Type");
        
        Api.getApi().uploadFileToCloudflareR2(testFilePath, null, homeId, 0, new IDefaultCallBack2<String>() {
            @Override
            public void onSuccess(String result) {
                DDLog.i(TAG, "方式2上传成功: " + result);
            }
            
            @Override
            public void onError(int errorCode, String errorMsg) {
                DDLog.e(TAG, "方式2上传失败: " + errorMsg);
            }
        });
    }
    
    /**
     * 演示文件验证的最佳实践
     */
    public void demonstrateValidationBestPractices() {
        DDLog.i(TAG, "=== 文件验证最佳实践 ===");
        
        String testFilePath = "/sdcard/large_video.mp4";
        
        // 1. 基本验证
        if (!FileUploadUtils.validateFile(testFilePath)) {
            DDLog.e(TAG, "文件基本验证失败");
            return;
        }
        
        // 2. 根据文件类型进行大小验证
        long maxSize = FileUploadUtils.getRecommendedMaxSize(testFilePath);
        if (!FileUploadUtils.validateFile(testFilePath, maxSize)) {
            DDLog.e(TAG, "文件大小验证失败");
            return;
        }
        
        // 3. 获取详细信息进行进一步验证
        FileUploadUtils.FileInfo fileInfo = FileUploadUtils.getFileInfo(testFilePath);
        if (fileInfo == null) {
            DDLog.e(TAG, "无法获取文件信息");
            return;
        }
        
        // 4. 根据业务需求进行特定验证
        if (fileInfo.isVideo && fileInfo.fileSize > 50 * FileUploadUtils.FileSizeConstants.MB) {
            DDLog.w(TAG, "视频文件较大，建议压缩后上传");
        }
        
        if (fileInfo.isImage && !fileInfo.contentType.equals("image/jpeg") && !fileInfo.contentType.equals("image/png")) {
            DDLog.w(TAG, "建议使用JPEG或PNG格式的图片");
        }
        
        DDLog.i(TAG, "文件验证通过，可以上传");
        DDLog.i(TAG, "文件信息: " + fileInfo.toString());
    }
    
    /**
     * 演示批量文件处理
     */
    public void demonstrateBatchProcessing() {
        DDLog.i(TAG, "=== 批量文件处理演示 ===");
        
        String[] filePaths = {
            "/sdcard/photo1.jpg",
            "/sdcard/photo2.png", 
            "/sdcard/video1.mp4",
            "/sdcard/document.pdf"
        };
        
        int validCount = 0;
        long totalSize = 0;
        
        for (String filePath : filePaths) {
            FileUploadUtils.FileInfo fileInfo = FileUploadUtils.getFileInfo(filePath);
            if (fileInfo != null) {
                validCount++;
                totalSize += fileInfo.fileSize;
                
                DDLog.i(TAG, "文件: " + fileInfo.fileName + 
                        " (" + fileInfo.readableSize + ", " + fileInfo.contentType + ")");
                
                // 检查是否符合上传要求
                long maxSize = FileUploadUtils.getRecommendedMaxSize(filePath);
                if (fileInfo.fileSize <= maxSize) {
                    DDLog.i(TAG, "  ✓ 可以上传");
                } else {
                    DDLog.w(TAG, "  ✗ 文件过大，建议压缩");
                }
            } else {
                DDLog.e(TAG, "无效文件: " + filePath);
            }
        }
        
        DDLog.i(TAG, "--- 批量处理结果 ---");
        DDLog.i(TAG, "总文件数: " + filePaths.length);
        DDLog.i(TAG, "有效文件数: " + validCount);
        DDLog.i(TAG, "总大小: " + FileUploadUtils.getReadableFileSize(totalSize));
    }
}
