package com.dinsafer.dincore.upload;

import android.text.TextUtils;

import com.dinsafer.dincore.common.ErrorCode;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dincore.http.Api;
import com.dinsafer.dincore.http.IFileUploadApi;
import com.dinsafer.dincore.http.NetWorkException;
import com.dinsafer.dssupport.utils.DDLog;

import java.io.File;
import java.io.IOException;

import okhttp3.MediaType;
import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;

/**
 * 文件上传管理器
 * 提供统一的文件上传功能
 * 
 * <AUTHOR> Assistant
 * @since 2025/7/16
 */
public class FileUploadManager {
    private static final String TAG = "FileUploadManager";
    private static final int DEFAULT_ERROR_CODE = -1;
    
    private static FileUploadManager instance;
    private IFileUploadApi fileUploadApi;
    
    private FileUploadManager() {
        // 创建专门用于文件上传的Retrofit实例
        Retrofit retrofit = Api.getApi().getRetrofit();
        fileUploadApi = retrofit.create(IFileUploadApi.class);
    }
    
    public static FileUploadManager getInstance() {
        if (instance == null) {
            synchronized (FileUploadManager.class) {
                if (instance == null) {
                    instance = new FileUploadManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 上传文件到指定URL
     * @param uploadUrl 上传地址
     * @param filePath 文件路径
     * @param contentType 文件MIME类型
     * @param callback 回调接口
     */
    public void uploadFile(String uploadUrl, String filePath, String contentType, IDefaultCallBack2<String> callback) {
        uploadFile(uploadUrl, filePath, contentType, null, callback);
    }
    
    /**
     * 上传文件到指定URL（带认证）
     * @param uploadUrl 上传地址
     * @param filePath 文件路径
     * @param contentType 文件MIME类型
     * @param token 认证token（可选）
     * @param callback 回调接口
     */
    public void uploadFile(String uploadUrl, String filePath, String contentType, String token, IDefaultCallBack2<String> callback) {
        // 参数验证
        if (TextUtils.isEmpty(uploadUrl)) {
            if (callback != null) {
                callback.onError(ErrorCode.PARAM_ERROR, "上传URL不能为空");
            }
            return;
        }
        
        if (TextUtils.isEmpty(filePath)) {
            if (callback != null) {
                callback.onError(ErrorCode.PARAM_ERROR, "文件路径不能为空");
            }
            return;
        }
        
        File file = new File(filePath);
        if (!file.exists()) {
            if (callback != null) {
                callback.onError(ErrorCode.PARAM_ERROR, "文件不存在: " + filePath);
            }
            return;
        }
        
        if (TextUtils.isEmpty(contentType)) {
            contentType = "application/octet-stream"; // 默认二进制类型
        }
        
        DDLog.i(TAG, "开始上传文件: " + filePath);
        DDLog.i(TAG, "上传URL: " + uploadUrl);
        DDLog.i(TAG, "Content-Type: " + contentType);
        DDLog.i(TAG, "文件大小: " + file.length() + " bytes");
        
        try {
            // 创建RequestBody
            RequestBody fileBody = RequestBody.create(MediaType.parse(contentType), file);
            DDLog.i(TAG, "RequestBody类型: " + fileBody.getClass().getName());
            
            // 准备认证头
            String authHeader = TextUtils.isEmpty(token) ? "" : ("Bearer " + token);
            
            // 执行上传
            fileUploadApi.uploadFile(uploadUrl, fileBody, authHeader)
                    .enqueue(new Callback<ResponseBody>() {
                @Override
                public void onResponse(Call<ResponseBody> call, Response<ResponseBody> response) {
                    handleUploadResponse(response, callback);
                }

                @Override
                public void onFailure(Call<ResponseBody> call, Throwable t) {
                    handleUploadFailure(t, callback);
                }
            });
            
        } catch (Exception e) {
            DDLog.e(TAG, "文件上传异常: " + e.getMessage());
            e.printStackTrace();
            if (callback != null) {
                callback.onError(DEFAULT_ERROR_CODE, "文件上传异常: " + e.getMessage());
            }
        }
    }
    
    /**
     * 处理上传响应
     */
    private void handleUploadResponse(Response<ResponseBody> response, IDefaultCallBack2<String> callback) {
        DDLog.i(TAG, "收到上传响应，状态码: " + response.code());
        
        if (response.isSuccessful()) {
            try {
                String responseBody = response.body() != null ? response.body().string() : "";
                DDLog.i(TAG, "文件上传成功: " + responseBody);
                if (callback != null) {
                    callback.onSuccess(responseBody);
                }
            } catch (IOException e) {
                DDLog.e(TAG, "读取响应失败: " + e.getMessage());
                if (callback != null) {
                    callback.onError(DEFAULT_ERROR_CODE, "读取响应失败: " + e.getMessage());
                }
            }
        } else {
            try {
                String errorBody = response.errorBody() != null ? response.errorBody().string() : "";
                DDLog.e(TAG, "文件上传失败，状态码: " + response.code() + ", 错误信息: " + errorBody);
                if (callback != null) {
                    callback.onError(response.code(), "上传失败: " + errorBody);
                }
            } catch (IOException e) {
                DDLog.e(TAG, "读取错误响应失败: " + e.getMessage());
                if (callback != null) {
                    callback.onError(response.code(), "上传失败，状态码: " + response.code());
                }
            }
        }
    }
    
    /**
     * 处理上传失败
     */
    private void handleUploadFailure(Throwable t, IDefaultCallBack2<String> callback) {
        DDLog.e(TAG, "文件上传失败: " + t.getMessage());
        t.printStackTrace();
        
        if (callback != null) {
            if (t instanceof NetWorkException) {
                NetWorkException netException = (NetWorkException) t;
                callback.onError(netException.getStatus(), netException.getMsgDes());
            } else {
                callback.onError(DEFAULT_ERROR_CODE, "文件上传失败: " + t.getMessage());
            }
        }
    }
    
    /**
     * 获取文件扩展名
     */
    public static String getFileExtension(String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return "";
        }
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filePath.length() - 1) {
            return filePath.substring(lastDotIndex);
        }
        return "";
    }
    
    /**
     * 根据Content-Type确定文件类型参数
     */
    public static int getFileTypeByContentType(String contentType) {
        if (TextUtils.isEmpty(contentType)) {
            return 5; // 默认类型
        }
        
        if (contentType.startsWith("image/")) {
            return 5; // 图片类型
        } else if (contentType.startsWith("video/")) {
            return 6; // 视频类型
        } else if (contentType.startsWith("audio/")) {
            return 7; // 音频类型
        } else if (contentType.equals("application/pdf")) {
            return 8; // PDF文档
        } else {
            return 9; // 其他文件类型
        }
    }
    
    /**
     * 验证文件
     */
    public static boolean validateFile(String filePath) {
        return validateFile(filePath, Long.MAX_VALUE);
    }
    
    /**
     * 验证文件（带大小限制）
     */
    public static boolean validateFile(String filePath, long maxSizeInBytes) {
        if (TextUtils.isEmpty(filePath)) {
            DDLog.e(TAG, "文件路径为空");
            return false;
        }
        
        File file = new File(filePath);
        if (!file.exists()) {
            DDLog.e(TAG, "文件不存在: " + filePath);
            return false;
        }
        
        if (!file.canRead()) {
            DDLog.e(TAG, "文件无法读取: " + filePath);
            return false;
        }
        
        long fileSize = file.length();
        if (fileSize > maxSizeInBytes) {
            DDLog.e(TAG, "文件过大: " + fileSize + " bytes (最大: " + maxSizeInBytes + " bytes)");
            return false;
        }
        
        DDLog.i(TAG, "文件验证通过: " + filePath + " (" + fileSize + " bytes)");
        return true;
    }
}
