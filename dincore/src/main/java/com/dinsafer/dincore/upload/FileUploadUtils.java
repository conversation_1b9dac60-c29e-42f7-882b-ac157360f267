package com.dinsafer.dincore.upload;

import android.text.TextUtils;
import android.webkit.MimeTypeMap;

import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dssupport.utils.DDLog;

import java.io.File;

/**
 * 文件上传工具类
 * 提供便利的文件上传方法
 * 
 * <AUTHOR> Assistant
 * @since 2025/7/16
 */
public class FileUploadUtils {
    private static final String TAG = "FileUploadUtils";
    
    /**
     * 自动检测Content-Type并上传文件
     */
    public static void uploadFileWithAutoContentType(String uploadUrl, String filePath, IDefaultCallBack2<String> callback) {
        String contentType = getContentTypeFromFile(filePath);
        FileUploadManager.getInstance().uploadFile(uploadUrl, filePath, contentType, callback);
    }
    
    /**
     * 自动检测Content-Type并上传文件（带认证）
     */
    public static void uploadFileWithAutoContentType(String uploadUrl, String filePath, String token, IDefaultCallBack2<String> callback) {
        String contentType = getContentTypeFromFile(filePath);
        FileUploadManager.getInstance().uploadFile(uploadUrl, filePath, contentType, token, callback);
    }
    
    /**
     * 批量上传文件
     */
    public static void uploadFilesSequentially(String uploadUrl, String[] filePaths, IDefaultCallBack2<BatchUploadResult> callback) {
        uploadFilesSequentially(uploadUrl, filePaths, null, callback);
    }
    
    /**
     * 批量上传文件（带认证）
     */
    public static void uploadFilesSequentially(String uploadUrl, String[] filePaths, String token, IDefaultCallBack2<BatchUploadResult> callback) {
        if (filePaths == null || filePaths.length == 0) {
            if (callback != null) {
                callback.onError(-1, "文件列表为空");
            }
            return;
        }
        
        BatchUploadResult result = new BatchUploadResult(filePaths.length);
        uploadNextFile(uploadUrl, filePaths, token, 0, result, callback);
    }
    
    /**
     * 递归上传下一个文件
     */
    private static void uploadNextFile(String uploadUrl, String[] filePaths, String token, int index, 
                                      BatchUploadResult result, IDefaultCallBack2<BatchUploadResult> callback) {
        if (index >= filePaths.length) {
            // 所有文件上传完成
            DDLog.i(TAG, "批量上传完成，成功: " + result.getSuccessCount() + ", 失败: " + result.getFailureCount());
            if (callback != null) {
                callback.onSuccess(result);
            }
            return;
        }
        
        String filePath = filePaths[index];
        String contentType = getContentTypeFromFile(filePath);
        
        DDLog.i(TAG, "上传文件 " + (index + 1) + "/" + filePaths.length + ": " + filePath);
        
        FileUploadManager.getInstance().uploadFile(uploadUrl, filePath, contentType, token, new IDefaultCallBack2<String>() {
            @Override
            public void onSuccess(String response) {
                result.addSuccess(filePath, response);
                // 继续上传下一个文件
                uploadNextFile(uploadUrl, filePaths, token, index + 1, result, callback);
            }
            
            @Override
            public void onError(int errorCode, String errorMsg) {
                result.addFailure(filePath, errorCode, errorMsg);
                // 即使失败也继续上传下一个文件
                uploadNextFile(uploadUrl, filePaths, token, index + 1, result, callback);
            }
        });
    }
    
    /**
     * 带文件验证的上传
     */
    public static void uploadFileWithValidation(String uploadUrl, String filePath, String contentType, 
                                               long maxSizeInBytes, IDefaultCallBack2<String> callback) {
        uploadFileWithValidation(uploadUrl, filePath, contentType, maxSizeInBytes, null, callback);
    }
    
    /**
     * 带文件验证的上传（带认证）
     */
    public static void uploadFileWithValidation(String uploadUrl, String filePath, String contentType, 
                                               long maxSizeInBytes, String token, IDefaultCallBack2<String> callback) {
        // 验证文件
        if (!FileUploadManager.validateFile(filePath, maxSizeInBytes)) {
            if (callback != null) {
                callback.onError(-1, "文件验证失败");
            }
            return;
        }
        
        // 执行上传
        FileUploadManager.getInstance().uploadFile(uploadUrl, filePath, contentType, token, callback);
    }
    
    /**
     * 根据文件路径获取Content-Type
     */
    public static String getContentTypeFromFile(String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return "application/octet-stream";
        }
        
        String extension = FileUploadManager.getFileExtension(filePath);
        if (!TextUtils.isEmpty(extension)) {
            // 去掉点号
            String ext = extension.substring(1);
            String mimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(ext);
            if (!TextUtils.isEmpty(mimeType)) {
                return mimeType;
            }
        }
        
        return "application/octet-stream";
    }
    
    /**
     * 获取可读的文件大小格式
     */
    public static String getReadableFileSize(long size) {
        if (size <= 0) return "0 B";
        
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        
        return String.format("%.1f %s", size / Math.pow(1024, digitGroups), units[digitGroups]);
    }
    
    /**
     * 检查文件是否为图片
     */
    public static boolean isImageFile(String filePath) {
        String contentType = getContentTypeFromFile(filePath);
        return contentType.startsWith("image/");
    }
    
    /**
     * 检查文件是否为视频
     */
    public static boolean isVideoFile(String filePath) {
        String contentType = getContentTypeFromFile(filePath);
        return contentType.startsWith("video/");
    }
    
    /**
     * 检查文件是否为音频
     */
    public static boolean isAudioFile(String filePath) {
        String contentType = getContentTypeFromFile(filePath);
        return contentType.startsWith("audio/");
    }
    
    /**
     * 批量上传结果类
     */
    public static class BatchUploadResult {
        private int totalCount;
        private int successCount = 0;
        private int failureCount = 0;
        private StringBuilder successFiles = new StringBuilder();
        private StringBuilder failureFiles = new StringBuilder();
        
        public BatchUploadResult(int totalCount) {
            this.totalCount = totalCount;
        }
        
        public void addSuccess(String filePath, String response) {
            successCount++;
            successFiles.append(filePath).append(": ").append(response).append("\n");
        }
        
        public void addFailure(String filePath, int errorCode, String errorMsg) {
            failureCount++;
            failureFiles.append(filePath).append(": ").append(errorCode).append(" - ").append(errorMsg).append("\n");
        }
        
        public int getTotalCount() { return totalCount; }
        public int getSuccessCount() { return successCount; }
        public int getFailureCount() { return failureCount; }
        public String getSuccessFiles() { return successFiles.toString(); }
        public String getFailureFiles() { return failureFiles.toString(); }
        
        public boolean isAllSuccess() { return failureCount == 0; }
        public boolean hasFailures() { return failureCount > 0; }
        
        @Override
        public String toString() {
            return "BatchUploadResult{" +
                    "total=" + totalCount +
                    ", success=" + successCount +
                    ", failure=" + failureCount +
                    '}';
        }
    }
}
