package com.dinsafer.dincore.websocket;

import android.util.Log;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.crypt.DinWBCrypt;
import com.dinsafer.dincore.crypt.ICrypt;
import com.dinsafer.dincore.user.UserManager;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;
import okio.ByteString;

// 家庭的home socket连接
public class HomeWebSocketManager extends WebSocketListener {

    private static final String TAG = HomeWebSocketManager.class.getSimpleName();

    private static volatile HomeWebSocketManager instance;

    private OkHttpClient client;
    private WebSocket mWebSocket;
    private int mRetryCount;
    private ICrypt mCrypt = new DinWBCrypt();

    List<IWebSocketCallBack> msgCallBackList = new ArrayList<>();

    private HomeWebSocketManager() {
        client = new OkHttpClient.Builder()
                .readTimeout(0, TimeUnit.MILLISECONDS)
                .build();
    }

    public static void init() {
        if (instance == null) {
            synchronized (HomeWebSocketManager.class) {
                if (instance == null) {
                    instance = new HomeWebSocketManager();
                }
            }
        }
    }

    public ICrypt getCrypt() {
        return mCrypt;
    }

    public void setCrypt(ICrypt crypt) {
        this.mCrypt = crypt;
    }

    public static HomeWebSocketManager getInstance() {
        return instance;
    }

    @Override
    public void onOpen(WebSocket webSocket, Response response) {
//        TODO 修改hometoken
//        String message = UserManager.getInstance().getToken()
//                + "&" +
//                DeviceManager.getInstance().getDeviceToken()
//                + "_" + System.currentTimeMillis() * 1000;
//        webSocket.send(Hexutils.bytetohexstring(mCrypt.encode(message.getBytes())));
        mWebSocket = webSocket;
    }

    @Override
    public void onMessage(WebSocket webSocket, String messageStr) {

        if ("1".equals(messageStr)) {
            mRetryCount = 0;
//            websocket auth success
            callBackConnectSuccess();
        } else if ("-1".equals(messageStr)) {
//            isDisConnectByServer = true;
            Log.i(TAG, " -1 disconect by server，to logout");
            callBackOnDisconnect(-1, " disconect by server，to logout");
//            EventBus.getDefault().post(new LogoutEvent());
        } else if ("-2".equals(messageStr)) {
            callBackOnDisconnect(-2, "device offlint");
        } else {
            callBackOnMessage(messageStr);
        }
    }


    @Override
    public void onMessage(WebSocket webSocket, ByteString bytes) {
//        output("Receiving bytes : " + bytes.hex());
    }

    @Override
    public void onClosing(WebSocket webSocket, int code, String reason) {
        webSocket.close(1000, null);
        callBackOnDisconnect(code, "Closing : " + code + " / " + reason);
    }

    @Override
    public void onFailure(WebSocket webSocket, Throwable t, Response response) {
        callBackOnDisconnect(-1, "Error : " + t.getMessage());
    }

    public void start() {
        Request request = new Request.Builder()
                .addHeader("Host", DinCore.getInstance().getDomain())
                .url("wss://" + DinCore.getInstance().getDomain()
                        + "/device/ws/v2/" + DinCore.getInstance().getAppID()).build();

        WebSocket ws = client.newWebSocket(request, this);
//        ws.request();
        client.dispatcher().executorService().shutdown();
    }

    public void stop() {
        if (mWebSocket != null) {
            mWebSocket.close(1000, null);
        }
    }

    private void callBackConnectSuccess() {
        for (IWebSocketCallBack callback :
                msgCallBackList) {
            callback.onConnect();
        }
    }

    private void callBackOnDisconnect(int code, String msg) {
        for (IWebSocketCallBack callback :
                msgCallBackList) {
            callback.onDisConnect(code, msg);
        }
    }


    private void callBackOnMessage(String msg) {
        for (IWebSocketCallBack callback :
                msgCallBackList) {
            callback.onMessage(msg);
        }
    }


    public void addCallBack(IWebSocketCallBack callBack) {
        if (msgCallBackList.contains(callBack)) {
            return;
        }

        msgCallBackList.add(callBack);
    }

    public void removeCallBack(IWebSocketCallBack callBack) {
        if (!msgCallBackList.contains(callBack)) {
            return;
        }

        msgCallBackList.remove(callBack);
    }


}
