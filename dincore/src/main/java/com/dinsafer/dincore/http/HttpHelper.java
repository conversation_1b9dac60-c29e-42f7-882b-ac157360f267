package com.dinsafer.dincore.http;

import androidx.annotation.Keep;

import com.dinsafer.dincore.common.Cmd;
import com.dinsafer.dincore.utils.DDJSONUtil;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;

/**
 * Created by Rinfon on 17/3/17.
 */
@Keep
public class HttpHelper {

    private static HashMap<String, String> whiteList;

    static {
        whiteList = new HashMap<>();
        whiteList.put(Cmd.SET_CAREMODE_DATA, Cmd.SET_CAREMODE_DATA);
    }


    /**
     * //        以前的resutle只会有一个string的返回结果,现在多了一个gmstime,跟一个key对应着string的结果,导致改动的地反很多
     * //        所以判断如果只有gmstime跟string结果的话,直接构造一个string回调就可以了,暂时不处理gmstime
     * 如果cmd在白名单里面，那么就不做转换
     *
     * @param cmd
     * @param jsonObject
     * @return
     */
    public static String checkIsOnlyGMSTimeAndData(String cmd, JSONObject jsonObject) {
//        以前的resutle只会有一个string的返回结果,现在多了一个gmstime,跟一个key对应着string的结果,导致改动的地反很多
//        所以判断如果只有gmstime跟string结果的话,直接构造一个string回调就可以了,暂时不处理gmstime
        if (whiteList.containsKey(cmd)) {
            return jsonObject.toString();
        }
        if (jsonObject.length() == 2 && DDJSONUtil.has(jsonObject, MyInterceptor.GMTIME)) {
            jsonObject.remove(MyInterceptor.GMTIME);
            try {
//                把除了gmtime的字段,以string返回
                return jsonObject.get(jsonObject.keys().next()).toString();
            } catch (JSONException e) {
                e.printStackTrace();
                return "";
            }
        } else if (jsonObject.length() == 1 && DDJSONUtil.has(jsonObject, MyInterceptor.GMTIME)) {
            return jsonObject.toString();
        }
        return "";
    }

    public static String checkIsOnlyGMSTimeAndData(JSONObject jsonObject) {
//        以前的resutle只会有一个string的返回结果,现在多了一个gmstime,跟一个key对应着string的结果,导致改动的地反很多
//        所以判断如果只有gmstime跟string结果的话,直接构造一个string回调就可以了,暂时不处理gmstime
        if (jsonObject.length() == 2 && DDJSONUtil.has(jsonObject, MyInterceptor.GMTIME)) {
            jsonObject.remove(MyInterceptor.GMTIME);
            try {
//                把除了gmtime的字段,以string返回
                return jsonObject.get(jsonObject.keys().next()).toString();
            } catch (JSONException e) {
                e.printStackTrace();
                return null;
            }
        } else if (jsonObject.length() == 1 && DDJSONUtil.has(jsonObject, MyInterceptor.GMTIME)) {
            return jsonObject.toString();
        }
        return jsonObject.toString();
    }


    public static boolean checkIsOnlyGMSTime(JSONObject jsonObject) {
        if (jsonObject.length() == 1 && DDJSONUtil.has(jsonObject, MyInterceptor.GMTIME)) {
            return true;
        }
        return false;
    }

    public static boolean checkIsJsonObject(String string) {
        try {
            new JSONObject(string);
            return true;
        } catch (JSONException e) {
            return false;
        }
    }


    public static boolean checkIsJsonArray(String string) {
        try {
            new JSONArray(string);
            return true;
        } catch (JSONException e) {
            return false;
        }
    }

}
