package com.dinsafer.dincore.http;

import androidx.annotation.Keep;

/**
 * Created by Rinfon on 16/8/12.
 */
@Keep
public class NetWorkException extends RuntimeException {
    private int status;  //异常对应的返回码
    private String msgDes;  //异常对应的描述信息

    public NetWorkException() {
        super();
    }

    public NetWorkException(String message) {
        super(message);
        msgDes = message;
    }

    public NetWorkException(int retCd, String msgDes) {
        super();
        this.status = retCd;
        this.msgDes = msgDes;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsgDes() {
        return msgDes;
    }
}
