package com.dinsafer.dincore.http;

import androidx.annotation.Keep;

/**
 * Created by Rinfon on 16/8/12.
 */
@Keep
public class BaseHttpEntry {
    /**
     * Status : 1
     * Action :
     * ErrorMessage :
     * MessageId :
     * Result : [{"deviceid":"d12345678901","id":"577a363c3b2e5f1625000003","isadmin":true,"module":"dd112","name":"","time":1467627068980169110},{"deviceid":"d12345678903","id":"577a37763b2e5f1625000009","isadmin":true,"module":"dd11d","name":"","time":1467627382743108512},{"deviceid":"d1234567893","id":"577f5f171cc77a0cb23f6f90","isadmin":true,"module":"vrinux001","name":"","time":1467965207204069311},{"deviceid":"deviceid_lxy_1","id":"578f19679948c64607000003","isadmin":true,"module":"dd11d","name":"","time":1468995943651300984},{"deviceid":"lxy_test_deviceid_lxy_1","id":"57a40ece9948c60ca4000003","isadmin":true,"module":"lxy_test_dev_type2","name":"lxy_test_dev_name","time":1470369486017384544}]
     */

    private int Status;
    private String Action;
    private String ErrorMessage;
    private String MessageId;

    public int getStatus() {
        return Status;
    }

    public void setStatus(int Status) {
        this.Status = Status;
    }

    public String getAction() {
        return Action;
    }

    public void setAction(String Action) {
        this.Action = Action;
    }

    public String getErrorMessage() {
        return ErrorMessage;
    }

    public void setErrorMessage(String ErrorMessage) {
        this.ErrorMessage = ErrorMessage;
    }

    public String getMessageId() {
        return MessageId;
    }

    public void setMessageId(String MessageId) {
        this.MessageId = MessageId;
    }
}
