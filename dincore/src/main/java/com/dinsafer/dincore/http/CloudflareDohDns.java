package com.dinsafer.dincore.http;

import androidx.annotation.NonNull;

import com.dinsafer.dssupport.utils.DDLog;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Dns;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * @describe：
 * @date：2025/6/4
 * @author: create by Sydnee
 */
public class CloudflareDohDns implements Dns {
    private static final String TAG = CloudflareDohDns.class.getSimpleName();
    private static final String CF_DOH_URL = "https://cloudflare-dns.com/dns-query?name=%s&type=A";
    private static final String EMALDO_DOH_URL = "https://doh.emaldo.com/dns-query?name=%s&type=A";
    private static final String GOOGLE_DOH_URL = "https://dns.google/dns-query?name=%s&type=A";
    private final long CACHE_DURATION = 10 * 60; // 默认缓存时间10分钟
    private final int MAX_CACHE_SIZE = 50; // 最大缓存条目数
    private final int TIMEOUT = 1; // 超时时间
    private String mDomain;
    private ConcurrentHashMap<String, CacheEntry> cache = new ConcurrentHashMap<String, CacheEntry>();

    private final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(TIMEOUT, TimeUnit.SECONDS)
            .build();

    private List<InetAddress> address = new ArrayList<>();
    private String type;

    public CloudflareDohDns(@NonNull String domain) {
        this.mDomain = domain;
    }

    @Override
    public List<InetAddress> lookup(String hostname) throws UnknownHostException {

        // 1. 检查缓存是否存在且未过期
        CacheEntry entry = cache.get(mDomain);
        if (entry != null && !entry.isExpired()) {
            return entry.getAddresses();
        }

        final Object lock = new Object();

        synchronized (lock) {

            // 2. 缓存未命中或过期，执行实际 DOH 查询
            requestDoh(new RequestCallback() {
                @Override
                public void onSuccess(Response response) {
                    try {
                        if (!response.isSuccessful()) {
                            DDLog.e(TAG, "DoH query failed: " + response.code());
                            // 2. 失败走DNS
                            fallbackToSystemDns();
                            synchronized (lock) {
                                lock.notifyAll();
                            }
                            return;
                        }

                        String json = response.body().string();
                        CacheEntry newEntry = parseDnsResponse(json);
                        address = newEntry.getAddresses();
                        updateCache(newEntry);
                    } catch (Exception e) {
                        DDLog.e(TAG, "DoH query failed: " + e.getMessage());
                        fallbackToSystemDns();
                    } finally {
                        synchronized (lock) {
                            lock.notifyAll();
                        }
                    }

                }

                @Override
                public void onError(Throwable error) {
                    DDLog.e(TAG, "DoH onError: " + error.getMessage());
                    fallbackToSystemDns();
                    synchronized (lock) {
                        lock.notifyAll();
                    }
                }

                private void fallbackToSystemDns() {
                    try {
                        address = Dns.SYSTEM.lookup(hostname);
                        type = CacheEntry.TYPE_DNS;
                        updateCache(new CacheEntry(type, address, System.currentTimeMillis() + TimeUnit.SECONDS.toMillis(CACHE_DURATION)));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });

            try {
                lock.wait();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }

            return address;
        }

    }

    private void updateCache(CacheEntry newEntry) {
        // 3. 更新缓存
        if (cache.size() >= MAX_CACHE_SIZE) {
            cache.clear();
        }
        cache.put(mDomain, newEntry);
    }

    private void requestDoh(RequestCallback callback) {
        if (callback == null) {
            return;
        }

        String cfUrl = String.format(CF_DOH_URL, mDomain);
        String emaldoUrl = String.format(EMALDO_DOH_URL, mDomain);

        Request cfRequest = createDohRequest(cfUrl);
        Request emaldoRequest = createDohRequest(emaldoUrl);

        // 使用AtomicReference来确保线程安全
        final AtomicReference<Call> cfCall = new AtomicReference<>();
        final AtomicReference<Call> emaldoCall = new AtomicReference<>();

        Callback responseCallback = new Callback() {
            @Override
            public void onResponse(Call call, Response response) throws IOException {

                if (call.isCanceled()) {
                    return;
                }
                // 取消另一个请求
                cancelOtherCall(call == cfCall.get() ? emaldoCall : cfCall);

                String responseType = call == cfCall.get() ? CacheEntry.TYPE_CF : CacheEntry.TYPE_EMALDO;
                type = responseType;
                callback.onSuccess(response);
            }

            List<Call> callList = new ArrayList<>();

            @Override
            public void onFailure(Call call, IOException e) {
                DDLog.e(TAG, "onFailure:  " + call + " ： " + call.isCanceled());

                callList.add(call);
                if (callList.size() < 2) {
                    if (!call.isCanceled()) {
                        call.cancel();
                    }
                    return;
                }

                // 检查另一个请求是否也已经失败
                Call otherCall = call == cfCall.get() ? emaldoCall.get() : cfCall.get();
                if (otherCall == null || otherCall.isCanceled()) {
                    try {
                        callback.onError(e);
                        callList.clear();
                    } catch (UnknownHostException ex) {
                        throw new RuntimeException(ex);
                    }
                }
            }
        };

        // 发起请求
        cfCall.set(client.newCall(cfRequest));
        emaldoCall.set(client.newCall(emaldoRequest));

        cfCall.get().enqueue(responseCallback);
        emaldoCall.get().enqueue(responseCallback);
    }

    private Request createDohRequest(String url) {
        return new Request.Builder()
                .url(url)
                .addHeader("Accept", "application/dns-json")
                .build();
    }

    private void cancelOtherCall(AtomicReference<Call> otherCallRef) {
        Call otherCall = otherCallRef.getAndSet(null);
        if (otherCall != null) {
            otherCall.cancel();
        }
    }

    private CacheEntry parseDnsResponse(String json) throws UnknownHostException {
        try {
            List<InetAddress> addresses = new ArrayList<>();
            int expireTime = 0;
            JSONObject response = new JSONObject(json);
            JSONArray answers = response.optJSONArray("Answer");

            if (answers != null) {
                for (int i = 0; i < answers.length(); i++) {
                    JSONObject record = answers.getJSONObject(i);
                    if (record.getString("type").equals("1")) { // Type A record
                        String ip = record.getString("data");
                        addresses.add(InetAddress.getByName(ip));
                        expireTime = record.getInt("TTL");
                    }
                }
            }

            if (addresses.isEmpty()) {
                throw new UnknownHostException("No IP found in DoH response");
            }
            CacheEntry entry = new CacheEntry(type, addresses, System.currentTimeMillis() + TimeUnit.SECONDS.toMillis(expireTime));
            return entry;
        } catch (Exception e) {
            throw new UnknownHostException("Failed to parse DoH response: " + e.getMessage());
        }
    }

    public interface RequestCallback {
        void onSuccess(Response response) throws IOException;

        void onError(Throwable error) throws UnknownHostException;
    }
}
