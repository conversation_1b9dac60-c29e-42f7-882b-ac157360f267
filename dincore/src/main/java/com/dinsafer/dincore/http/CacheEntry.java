package com.dinsafer.dincore.http;

/**
 * @describe：
 * @date：2025/6/4
 * @author: create by Sydnee
 */

import java.net.InetAddress;
import java.util.List;

public class CacheEntry {
    public static final String TYPE_CF = "CF";
    public static final String TYPE_EMALDO = "EMALDO";
    public static final String TYPE_DNS = "DNS";
    private String type;
    private final List<InetAddress> addresses;
    private final long expireTime;

    public CacheEntry(String type, List<InetAddress> addresses, long expireTime) {
        this.type = type;
        this.addresses = addresses;
        this.expireTime = expireTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<InetAddress> getAddresses() {
        return addresses;
    }

    public long getExpireTime() {
        return expireTime;
    }

    public boolean isExpired() {
        return System.currentTimeMillis() >= expireTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CacheEntry that = (CacheEntry) o;
        return expireTime == that.expireTime &&
                addresses.equals(that.addresses);
    }

    @Override
    public int hashCode() {
        return 31 * addresses.hashCode() + Long.hashCode(expireTime);
    }

    @Override
    public String toString() {
        return "CacheEntry{" +
                "type='" + type + '\'' +
                ", addresses=" + addresses +
                ", expireTime=" + expireTime +
                '}';
    }
}