package com.dinsafer.dincore.http;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.crypt.DinHttpCrypt;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.dincore.user.bean.DinUserLoginResponse;
import com.dinsafer.dincore.user.bean.RefreshVerifyCodeResponse;
import com.dinsafer.dincore.user.bean.RegisterResponse;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava.RxJavaCallAdapterFactory;

@Keep
public class Api {

    private static Api instance;
    private final int TIMEOUT = 60000;
    //手动创建一个OkHttpClient并设置超时时间
    private OkHttpClient.Builder httpClientBuilder;
    private Retrofit retrofit;
    private IApi services;


    private Urls urls;

    private Api(DinCore dinCore) {

        httpClientBuilder = new OkHttpClient.Builder()
                .addInterceptor(new MyInterceptor(dinCore.getDomain(), new DinHttpCrypt()))
                .connectTimeout(TIMEOUT, TimeUnit.SECONDS)
                .hostnameVerifier((hostname, session) -> true)
                .dns(new CloudflareDohDns(dinCore.getDomain()))
                .eventListener(new MyEventListener(dinCore.getAppID()))
                .writeTimeout(20 * 1000, TimeUnit.MILLISECONDS)
                .readTimeout(20 * 1000, TimeUnit.MILLISECONDS);
        retrofit = new Retrofit.Builder()
                .client(httpClientBuilder.build())
                .addConverterFactory(DecodeConverterFactory.create())
                .addCallAdapterFactory(RxJavaCallAdapterFactory.create())
                .baseUrl("https:" + dinCore.getDomain())
                .build();
        services = retrofit.create(IApi.class);

        urls = Urls.builder()
                .appid(dinCore.getAppID())
                .build();

    }

    public static void init(DinCore dinCore) {
        if (instance == null) {
            synchronized (Api.class) {
                if (instance == null) {
                    instance = new Api(dinCore);
                }
            }
        }
    }

    public static Api getApi() {
        return instance;
    }

    public String getUrl(String url) {
        return urls.getUrl(url);
    }

    public Retrofit getRetrofit() {
        return retrofit;
    }

    //    loginType : 0:email,1:phone 2:uid
    public Call<DinUserLoginResponse> login(int loginType, String account, String password) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            if (loginType == 0) {
                jsonObject.put("email", account);
            } else if (loginType == 1) {
                jsonObject.put("phone", account);
            } else {
                jsonObject.put("uid", account);
            }
            jsonObject.put("password", password);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);

        return services.Login(getUrl(Urls.URL_LOGIN), map);
    }

    public Call<StringResponseEntry> logout() {
        Map<String, Object> map = new HashMap<>();
        map.put("token", UserManager.getInstance().getToken());

        return services.logout(getUrl(Urls.URL_LOGOUT), map);
    }

    public Call<StringResponseEntry> modifyUidPasswordCall(String username, String password) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", username);
            jsonObject.put("password", password);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.modifyUidPassword(getUrl(Urls.URL_MODIFY_UID_PASSWORD), map);
    }

    public Call<StringResponseEntry> bindPhoneCall(String phone, String specialId, String verifyCode, String verifyId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", phone);
            jsonObject.put("special_id", specialId);
            jsonObject.put("verify_code", verifyCode);
            jsonObject.put("verify_id", verifyId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.bindPhone(getUrl(Urls.URL_BIND_PHONE_V3), map);
    }

    public Call<StringResponseEntry> unbindPhoneCall(String phone, String specialId, String verifyCode, String verifyId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", phone);
            jsonObject.put("special_id", specialId);
            jsonObject.put("verify_code", verifyCode);
            jsonObject.put("verify_id", verifyId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.unbindPhone(getUrl(Urls.URL_UNBIND_PHONE_V3), map);
    }

    public Call<StringResponseEntry> verifyBindPhoneCall(String account, String code) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
            jsonObject.put("code", code);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.verifyBindPhone(getUrl(Urls.URL_VERIFY_BIND_PHONE), map);
    }

    public Call<StringResponseEntry> verifyUnBindPhoneCall(String account, String code) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
            jsonObject.put("code", code);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.verifyUnBindPhone(getUrl(Urls.URL_VERIFY_UNBIND_PHONE), map);
    }

    public Call<StringResponseEntry> bindEmailCall(String account) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.bindEmail(getUrl(Urls.URL_BIND_EMAIL), map);
    }

    public Call<StringResponseEntry> verifyBindEmailCall(String account, String code) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
            jsonObject.put("code", code);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.verifyBindEmail(getUrl(Urls.URL_VERIFY_BIND_EMAIL), map);
    }

    public Call<StringResponseEntry> unbindEmailCall(String account) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.unbindEmail(getUrl(Urls.URL_UNBIND_EMAIL), map);
    }

    public Call<StringResponseEntry> verifyUnBindEmailCall(String account, String code) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
            jsonObject.put("code", code);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.verifyUnBindPhone(getUrl(Urls.URL_VERIFY_UNBIND_EMAIL), map);
    }


    public Call<StringResponseEntry> changePasswordCall(String oldpassword, String password) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("oldpassword", oldpassword);
            jsonObject.put("password", password);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.changePassword(getUrl(Urls.URL_CHANGE_PASSWORD), map);
    }

    public Call<StringResponseEntry> getChangeUidCall(String uid) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", uid);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.modifyUid(getUrl(Urls.URL_MODIFY_UID), map);
    }

    //    通過email找回密碼，發送驗證碼
    public Call<StringResponseEntry> getForgetPWDbyEmail(String uid) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", uid);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);

        return services.getForgetPWDbyEmail(getUrl(Urls.URL_FORGET_PASSWORD_BY_EMAIL), map);
    }

    public Call<StringResponseEntry> comfirmForgetPWDByEmailCode(String uid, String key, String password) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", uid);
            jsonObject.put("code", key);
            jsonObject.put("new_password", password);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);

        return services.comfirmForgetPWDByEmailCode(getUrl(Urls.URL_CONFIRM_FORGET_PASSWORD_BY_EMAIL_CODE), map);
    }

    public Call<StringResponseEntry> getForgetPWDbyPhone(String uid, String specialId, String verifyCode, String verifyId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", uid);
            jsonObject.put("special_id", specialId);
            jsonObject.put("verify_code", verifyCode);
            jsonObject.put("verify_id", verifyId);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);

        return services.getForgetPWDbyPhone(getUrl(Urls.URL_FORGET_PASSWORD_BY_PHONE_V3), map);
    }

    public Call<StringResponseEntry> comfirmForgetPWDByPhoneCode(String uid, String key, String password) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", uid);
            jsonObject.put("code", key);
            jsonObject.put("new_password", password);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);

        return services.comfirmForgetPWDByPhoneCode(getUrl(Urls.URL_CONFIRM_FORGET_PASSWORD_BY_PHONE_CODE), map);
    }

    public Call<StringResponseEntry> getUploadToken() {
        Map<String, Object> map = new HashMap<>();
        map.put("token", UserManager.getInstance().getToken());

        return services.getUploadToken(getUrl(Urls.URL_GET_UPLOAD_TOKEN), map);
    }

    public Call<StringResponseEntry> uploadImageKey(String imageKey, ) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("photo", imageKey);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getUploadImage(getUrl(Urls.URL_UPLOAD_AVATAR_KEY), map);
    }

    public Call<ResponseBody> getNewQRCodeScan(String shortId, String currentDeviceID) {

        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("types_id", shortId);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        Call<ResponseBody> getCall =
                services.getNewQRCodeScan(getUrl(Urls.URL_SCAN_PLUGIN_QR + shortId + "/"), map);
        return getCall;
    }


    public Call<ResponseBody> getNewQRCodetpIpcScan(String shortId, String currentDeviceID, String home_id) {

        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("types_id", shortId);
            jsonObject.put("home_id", home_id);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        Call<ResponseBody> getCall =
                services.getNewQRCodetpIpcScan(getUrl(Urls.URL_SCAN_PLUGIN_QR_TP_IPC + shortId + "/"), map);
        return getCall;
    }

    public Call<StringResponseEntry> getPhoneValidateCode(String account, String specialId, String verifyCode, String verifyId) {

        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
            jsonObject.put("special_id", specialId);
            jsonObject.put("verify_code", verifyCode);
            jsonObject.put("verify_id", verifyId);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);

        Call<StringResponseEntry> getCall =
                services.getPhoneValidateCode(getUrl(Urls.URL_GET_PHONE_REGISTER_CODE_V3), map);
        return getCall;
    }

    public Call<RegisterResponse> validatePhoneCode(String account, String code) {

        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
            jsonObject.put("code", code);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);

        Call<RegisterResponse> getCall =
                services.validatePhoneCode(getUrl(Urls.URL_VARIFY_REGISTER_CODE), map);
        return getCall;
    }

    public Call<StringResponseEntry> getEmailValidateCode(String account) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);

        Call<StringResponseEntry> getCall =
                services.getEmailValidateCode(getUrl(Urls.URL_GET_EMAIL_REGISTER_CODE), map);
        return getCall;
    }

    public Call<RegisterResponse> validateEmailCode(String account, String code) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
            jsonObject.put("code", code);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);

        Call<RegisterResponse> getCall =
                services.validateEmailCode(getUrl(Urls.URL_VARIFY_EMAIL_REGISTER_CODE), map);
        return getCall;
    }

    public Call<StringResponseEntry> deleteAccount() {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.deleteAccount(getUrl(Urls.URL_DELETE_ACCOUNT), map);
    }

    /**
     * 仅校验验证码接口
     *
     * @param account 手机号或邮箱
     * @param code    验证码
     */
    public Call<StringResponseEntry> verifyCodeOnly(@NonNull final String account, @NonNull final String code) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
            jsonObject.put("code", code);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.verifyCodeOnly(getUrl(Urls.URL_ONLY_VERIFY_CODE), map);
    }

    /**
     * 修改密码接口
     *
     * @param account  用户uid
     * @param password 新密码
     */
    public Call<StringResponseEntry> changePasswordOnly(@NonNull final String account, @NonNull final String password) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
            jsonObject.put("password", password);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.changePasswordOnly(getUrl(Urls.URL_CHANGE_PASSWORD_ONLY), map);
    }

    /**
     * 校验原密码接口
     *
     * @param password 旧密码
     */
    public Call<StringResponseEntry> checkPasswordOnly(@NonNull final String password) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("password", password);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.checkPasswordOnly(getUrl(Urls.URL_CHECK_PASSWORD), map);
    }

    /**
     * 获取图片验证码
     * @return
     */
    public Call<RefreshVerifyCodeResponse> refreshVerifyCode(String specialId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("special_id", specialId);
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.refreshVerifyCode(getUrl(Urls.URL_REFRESH_VERIFY_CODE), map);
    }

    // ********************* 兼容模式（cawa）登录或注册

    public Call<DinUserLoginResponse> loginCompat(String userId, String pwd) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("user_id", userId);
            jsonObject.put("password", pwd);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);

        return services.LoginCW(getUrl(Urls.URL_LOGIN_COMPAT), map);
    }
    // ********************* 使用cloudflare R2上传文件

    public Call<CloudflareUploadTokenResponse> getCloudflareUploadToken(String filePath, String homeId, int type) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("content_length", content_length);
            jsonObject.put("content_type", content_type);
            jsonObject.put("file_extension", file_extension);
            jsonObject.put("home_id", homeId);
            jsonObject.put("type", type);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getCloudflareUploadToken(getUrl(Urls.URL_CLOUDFLARE_UPLOAD_TOKEN), map);
    }
    /**
     * 上传文件到Cloudflare R2
     * @param uploadUrl 上传地址
     * @param fileBody 文件请求体
     * @return 上传响应
     */
    public Call<ResponseBody> uploadFileToCloudflareR2(String uploadUrl, RequestBody fileBody) {
        return services.uploadFileToCloudflareR2(uploadUrl, fileBody);
    }

    /**
     * 完整的Cloudflare R2文件上传流程
     * 先获取token，再上传文件
     * @param filePath 文件路径
     * @param contentType 文件MIME类型
     * @param homeId 家庭ID
     * @param type 文件类型
     * @param callback 回调接口
     */
    public void uploadFileToCloudflareR2(String filePath, String contentType, String homeId, int type,
                                        com.dinsafer.dincore.common.IDefaultCallBack2<String> callback) {
        if (android.text.TextUtils.isEmpty(filePath) || !(new java.io.File(filePath).exists())) {
            if (callback != null) {
                callback.onError(com.dinsafer.dincore.common.ErrorCode.PARAM_ERROR, "文件路径无效或文件不存在");
            }
            return;
        }

        java.io.File file = new java.io.File(filePath);
        long contentLength = file.length();
        String fileExtension = getFileExtension(filePath);

        // 先获取上传token
        getCloudflareUploadTokenAndUpload(contentLength, contentType, fileExtension, homeId, type, filePath, callback);
    }

    /**
     * 获取上传token并执行文件上传
     */
    private void getCloudflareUploadTokenAndUpload(long contentLength, String contentType, String fileExtension,
                                                   String homeId, int type, String filePath,
                                                   com.dinsafer.dincore.common.IDefaultCallBack2<String> callback) {
        getCloudflareUploadToken(contentLength, contentType, fileExtension, homeId, type)
                .enqueue(new retrofit2.Callback<CloudflareUploadTokenResponse>() {
            @Override
            public void onResponse(retrofit2.Call<CloudflareUploadTokenResponse> call, retrofit2.Response<CloudflareUploadTokenResponse> response) {
                CloudflareUploadTokenResponse responseEntry = response.body();
                if (responseEntry != null && responseEntry.getResult() != null) {
                    String token = responseEntry.getResult().getToken();
                    String uploadUrl = responseEntry.getResult().getAccess(); // 上传地址

                    if (!android.text.TextUtils.isEmpty(token) && !android.text.TextUtils.isEmpty(uploadUrl)) {
                        // 执行实际的文件上传（注意：上传过程不需要token，token只用于获取上传地址）
                        performFileUpload(uploadUrl, filePath, contentType, callback);
                    } else {
                        if (callback != null) {
                            callback.onError(-1, "获取上传token或地址失败");
                        }
                    }
                } else {
                    if (callback != null) {
                        callback.onError(-1, "获取上传token响应为空");
                    }
                }
            }

            @Override
            public void onFailure(retrofit2.Call<CloudflareUploadTokenResponse> call, Throwable t) {
                com.dinsafer.dssupport.utils.DDLog.e("Api", "获取Cloudflare上传token失败: " + t.getMessage());
                if (callback != null) {
                    if (t instanceof NetWorkException) {
                        NetWorkException netException = (NetWorkException) t;
                        callback.onError(netException.getStatus(), netException.getMsgDes());
                    } else {
                        callback.onError(-1, "获取上传token失败: " + t.getMessage());
                    }
                }
            }
        });
    }

    /**
     * 执行实际的文件上传
     */
    private void performFileUpload(String uploadUrl, String filePath, String contentType,
                                   com.dinsafer.dincore.common.IDefaultCallBack2<String> callback) {
        try {
            java.io.File file = new java.io.File(filePath);
            if (!file.exists()) {
                if (callback != null) {
                    callback.onError(com.dinsafer.dincore.common.ErrorCode.PARAM_ERROR, "文件不存在");
                }
                return;
            }

            com.dinsafer.dssupport.utils.DDLog.i("Api", "使用Retrofit进行文件上传（无token认证）...");

            // 创建RequestBody
            okhttp3.RequestBody fileBody = okhttp3.RequestBody.create(okhttp3.MediaType.parse(contentType), file);
            com.dinsafer.dssupport.utils.DDLog.i("Api", "RequestBody类型: " + fileBody.getClass().getName());

            // 使用Retrofit进行文件上传（注意：上传时不需要token）
            uploadFileToCloudflareR2(uploadUrl, fileBody)
                    .enqueue(new retrofit2.Callback<ResponseBody>() {
                @Override
                public void onResponse(retrofit2.Call<ResponseBody> call, retrofit2.Response<ResponseBody> response) {
                    com.dinsafer.dssupport.utils.DDLog.i("Api", "Retrofit上传响应，状态码: " + response.code());
                    if (response.isSuccessful()) {
                        try {
                            String responseBody = response.body() != null ? response.body().string() : "";
                            com.dinsafer.dssupport.utils.DDLog.i("Api", "Retrofit文件上传成功: " + responseBody);
                            if (callback != null) {
                                callback.onSuccess(responseBody);
                            }
                        } catch (java.io.IOException e) {
                            com.dinsafer.dssupport.utils.DDLog.e("Api", "读取响应失败: " + e.getMessage());
                            if (callback != null) {
                                callback.onError(-1, "读取响应失败: " + e.getMessage());
                            }
                        }
                    } else {
                        try {
                            String errorBody = response.errorBody() != null ? response.errorBody().string() : "";
                            com.dinsafer.dssupport.utils.DDLog.e("Api", "Retrofit文件上传失败，状态码: " + response.code() + ", 错误信息: " + errorBody);
                            if (callback != null) {
                                callback.onError(response.code(), "上传失败: " + errorBody);
                            }
                        } catch (java.io.IOException e) {
                            com.dinsafer.dssupport.utils.DDLog.e("Api", "读取错误响应失败: " + e.getMessage());
                            if (callback != null) {
                                callback.onError(response.code(), "上传失败，状态码: " + response.code());
                            }
                        }
                    }
                }

                @Override
                public void onFailure(retrofit2.Call<ResponseBody> call, Throwable t) {
                    com.dinsafer.dssupport.utils.DDLog.e("Api", "Retrofit文件上传失败: " + t.getMessage());
                    if (callback != null) {
                        if (t instanceof NetWorkException) {
                            NetWorkException netException = (NetWorkException) t;
                            callback.onError(netException.getStatus(), netException.getMsgDes());
                        } else {
                            callback.onError(-1, "Retrofit文件上传失败: " + t.getMessage());
                        }
                    }
                }
            });

        } catch (Exception e) {
            com.dinsafer.dssupport.utils.DDLog.e("Api", "Retrofit文件上传异常: " + e.getMessage());
            e.printStackTrace();
            if (callback != null) {
                callback.onError(-1, "Retrofit文件上传异常: " + e.getMessage());
            }
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filePath) {
        if (android.text.TextUtils.isEmpty(filePath)) {
            return "";
        }
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filePath.length() - 1) {
            return filePath.substring(lastDotIndex);
        }
        return "";
    }

    /**
     * 根据Content-Type确定文件类型参数
     */
    public static int getFileTypeByContentType(String contentType) {
        if (android.text.TextUtils.isEmpty(contentType)) {
            return 5; // 默认类型
        }

        if (contentType.startsWith("image/")) {
            return 5; // 图片类型
        } else if (contentType.startsWith("video/")) {
            return 6; // 视频类型
        } else if (contentType.startsWith("audio/")) {
            return 7; // 音频类型
        } else if (contentType.equals("application/pdf")) {
            return 8; // PDF文档
        } else {
            return 9; // 其他文件类型
        }
    }
}
