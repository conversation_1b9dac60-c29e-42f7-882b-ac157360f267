package com.dinsafer.dincore.http;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.crypt.DinHttpCrypt;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.dincore.user.bean.DinUserLoginResponse;
import com.dinsafer.dincore.user.bean.RefreshVerifyCodeResponse;
import com.dinsafer.dincore.user.bean.RegisterResponse;
import com.dinsafer.dincore.utils.FileUploadUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava.RxJavaCallAdapterFactory;

@Keep
public class Api {

    private static Api instance;
    private final int TIMEOUT = 60000;
    //手动创建一个OkHttpClient并设置超时时间
    private OkHttpClient.Builder httpClientBuilder;
    private Retrofit retrofit;
    private IApi services;


    private Urls urls;

    private Api(DinCore dinCore) {

        httpClientBuilder = new OkHttpClient.Builder()
                .addInterceptor(new MyInterceptor(dinCore.getDomain(), new DinHttpCrypt()))
                .connectTimeout(TIMEOUT, TimeUnit.SECONDS)
                .hostnameVerifier((hostname, session) -> true)
                .dns(new CloudflareDohDns(dinCore.getDomain()))
                .eventListener(new MyEventListener(dinCore.getAppID()))
                .writeTimeout(20 * 1000, TimeUnit.MILLISECONDS)
                .readTimeout(20 * 1000, TimeUnit.MILLISECONDS);
        retrofit = new Retrofit.Builder()
                .client(httpClientBuilder.build())
                .addConverterFactory(DecodeConverterFactory.create())
                .addCallAdapterFactory(RxJavaCallAdapterFactory.create())
                .baseUrl("https:" + dinCore.getDomain())
                .build();
        services = retrofit.create(IApi.class);

        urls = Urls.builder()
                .appid(dinCore.getAppID())
                .build();

    }

    public static void init(DinCore dinCore) {
        if (instance == null) {
            synchronized (Api.class) {
                if (instance == null) {
                    instance = new Api(dinCore);
                }
            }
        }
    }

    public static Api getApi() {
        return instance;
    }

    public String getUrl(String url) {
        return urls.getUrl(url);
    }

    public Retrofit getRetrofit() {
        return retrofit;
    }

    //    loginType : 0:email,1:phone 2:uid
    public Call<DinUserLoginResponse> login(int loginType, String account, String password) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            if (loginType == 0) {
                jsonObject.put("email", account);
            } else if (loginType == 1) {
                jsonObject.put("phone", account);
            } else {
                jsonObject.put("uid", account);
            }
            jsonObject.put("password", password);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);

        return services.Login(getUrl(Urls.URL_LOGIN), map);
    }

    public Call<StringResponseEntry> logout() {
        Map<String, Object> map = new HashMap<>();
        map.put("token", UserManager.getInstance().getToken());

        return services.logout(getUrl(Urls.URL_LOGOUT), map);
    }

    public Call<StringResponseEntry> modifyUidPasswordCall(String username, String password) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", username);
            jsonObject.put("password", password);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.modifyUidPassword(getUrl(Urls.URL_MODIFY_UID_PASSWORD), map);
    }

    public Call<StringResponseEntry> bindPhoneCall(String phone, String specialId, String verifyCode, String verifyId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", phone);
            jsonObject.put("special_id", specialId);
            jsonObject.put("verify_code", verifyCode);
            jsonObject.put("verify_id", verifyId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.bindPhone(getUrl(Urls.URL_BIND_PHONE_V3), map);
    }

    public Call<StringResponseEntry> unbindPhoneCall(String phone, String specialId, String verifyCode, String verifyId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", phone);
            jsonObject.put("special_id", specialId);
            jsonObject.put("verify_code", verifyCode);
            jsonObject.put("verify_id", verifyId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.unbindPhone(getUrl(Urls.URL_UNBIND_PHONE_V3), map);
    }

    public Call<StringResponseEntry> verifyBindPhoneCall(String account, String code) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
            jsonObject.put("code", code);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.verifyBindPhone(getUrl(Urls.URL_VERIFY_BIND_PHONE), map);
    }

    public Call<StringResponseEntry> verifyUnBindPhoneCall(String account, String code) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
            jsonObject.put("code", code);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.verifyUnBindPhone(getUrl(Urls.URL_VERIFY_UNBIND_PHONE), map);
    }

    public Call<StringResponseEntry> bindEmailCall(String account) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.bindEmail(getUrl(Urls.URL_BIND_EMAIL), map);
    }

    public Call<StringResponseEntry> verifyBindEmailCall(String account, String code) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
            jsonObject.put("code", code);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.verifyBindEmail(getUrl(Urls.URL_VERIFY_BIND_EMAIL), map);
    }

    public Call<StringResponseEntry> unbindEmailCall(String account) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.unbindEmail(getUrl(Urls.URL_UNBIND_EMAIL), map);
    }

    public Call<StringResponseEntry> verifyUnBindEmailCall(String account, String code) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
            jsonObject.put("code", code);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.verifyUnBindPhone(getUrl(Urls.URL_VERIFY_UNBIND_EMAIL), map);
    }


    public Call<StringResponseEntry> changePasswordCall(String oldpassword, String password) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("oldpassword", oldpassword);
            jsonObject.put("password", password);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.changePassword(getUrl(Urls.URL_CHANGE_PASSWORD), map);
    }

    public Call<StringResponseEntry> getChangeUidCall(String uid) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", uid);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.modifyUid(getUrl(Urls.URL_MODIFY_UID), map);
    }

    //    通過email找回密碼，發送驗證碼
    public Call<StringResponseEntry> getForgetPWDbyEmail(String uid) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", uid);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);

        return services.getForgetPWDbyEmail(getUrl(Urls.URL_FORGET_PASSWORD_BY_EMAIL), map);
    }

    public Call<StringResponseEntry> comfirmForgetPWDByEmailCode(String uid, String key, String password) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", uid);
            jsonObject.put("code", key);
            jsonObject.put("new_password", password);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);

        return services.comfirmForgetPWDByEmailCode(getUrl(Urls.URL_CONFIRM_FORGET_PASSWORD_BY_EMAIL_CODE), map);
    }

    public Call<StringResponseEntry> getForgetPWDbyPhone(String uid, String specialId, String verifyCode, String verifyId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", uid);
            jsonObject.put("special_id", specialId);
            jsonObject.put("verify_code", verifyCode);
            jsonObject.put("verify_id", verifyId);

        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);

        return services.getForgetPWDbyPhone(getUrl(Urls.URL_FORGET_PASSWORD_BY_PHONE_V3), map);
    }

    public Call<StringResponseEntry> comfirmForgetPWDByPhoneCode(String uid, String key, String password) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", uid);
            jsonObject.put("code", key);
            jsonObject.put("new_password", password);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);

        return services.comfirmForgetPWDByPhoneCode(getUrl(Urls.URL_CONFIRM_FORGET_PASSWORD_BY_PHONE_CODE), map);
    }

    public Call<StringResponseEntry> getUploadToken() {
        Map<String, Object> map = new HashMap<>();
        map.put("token", UserManager.getInstance().getToken());

        return services.getUploadToken(getUrl(Urls.URL_GET_UPLOAD_TOKEN), map);
    }

    public Call<StringResponseEntry> uploadImageKey(String imageKey, int source) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("photo", imageKey);
            jsonObject.put("source", source);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.getUploadImage(getUrl(Urls.URL_UPLOAD_AVATAR_KEY), map);
    }

    public Call<ResponseBody> getNewQRCodeScan(String shortId, String currentDeviceID) {

        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("types_id", shortId);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        Call<ResponseBody> getCall =
                services.getNewQRCodeScan(getUrl(Urls.URL_SCAN_PLUGIN_QR + shortId + "/"), map);
        return getCall;
    }


    public Call<ResponseBody> getNewQRCodetpIpcScan(String shortId, String currentDeviceID, String home_id) {

        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("types_id", shortId);
            jsonObject.put("home_id", home_id);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        Call<ResponseBody> getCall =
                services.getNewQRCodetpIpcScan(getUrl(Urls.URL_SCAN_PLUGIN_QR_TP_IPC + shortId + "/"), map);
        return getCall;
    }

    public Call<StringResponseEntry> getPhoneValidateCode(String account, String specialId, String verifyCode, String verifyId) {

        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
            jsonObject.put("special_id", specialId);
            jsonObject.put("verify_code", verifyCode);
            jsonObject.put("verify_id", verifyId);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);

        Call<StringResponseEntry> getCall =
                services.getPhoneValidateCode(getUrl(Urls.URL_GET_PHONE_REGISTER_CODE_V3), map);
        return getCall;
    }

    public Call<RegisterResponse> validatePhoneCode(String account, String code) {

        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
            jsonObject.put("code", code);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);

        Call<RegisterResponse> getCall =
                services.validatePhoneCode(getUrl(Urls.URL_VARIFY_REGISTER_CODE), map);
        return getCall;
    }

    public Call<StringResponseEntry> getEmailValidateCode(String account) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);

        Call<StringResponseEntry> getCall =
                services.getEmailValidateCode(getUrl(Urls.URL_GET_EMAIL_REGISTER_CODE), map);
        return getCall;
    }

    public Call<RegisterResponse> validateEmailCode(String account, String code) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
            jsonObject.put("code", code);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);

        Call<RegisterResponse> getCall =
                services.validateEmailCode(getUrl(Urls.URL_VARIFY_EMAIL_REGISTER_CODE), map);
        return getCall;
    }

    public Call<StringResponseEntry> deleteAccount() {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());

        return services.deleteAccount(getUrl(Urls.URL_DELETE_ACCOUNT), map);
    }

    /**
     * 仅校验验证码接口
     *
     * @param account 手机号或邮箱
     * @param code    验证码
     */
    public Call<StringResponseEntry> verifyCodeOnly(@NonNull final String account, @NonNull final String code) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
            jsonObject.put("code", code);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.verifyCodeOnly(getUrl(Urls.URL_ONLY_VERIFY_CODE), map);
    }

    /**
     * 修改密码接口
     *
     * @param account  用户uid
     * @param password 新密码
     */
    public Call<StringResponseEntry> changePasswordOnly(@NonNull final String account, @NonNull final String password) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("account", account);
            jsonObject.put("password", password);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.changePasswordOnly(getUrl(Urls.URL_CHANGE_PASSWORD_ONLY), map);
    }

    /**
     * 校验原密码接口
     *
     * @param password 旧密码
     */
    public Call<StringResponseEntry> checkPasswordOnly(@NonNull final String password) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("password", password);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.checkPasswordOnly(getUrl(Urls.URL_CHECK_PASSWORD), map);
    }

    /**
     * 获取图片验证码
     * @return
     */
    public Call<RefreshVerifyCodeResponse> refreshVerifyCode(String specialId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("special_id", specialId);
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.refreshVerifyCode(getUrl(Urls.URL_REFRESH_VERIFY_CODE), map);
    }

    // ********************* 兼容模式（cawa）登录或注册

    public Call<DinUserLoginResponse> loginCompat(String userId, String pwd) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("user_id", userId);
            jsonObject.put("password", pwd);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);

        return services.LoginCW(getUrl(Urls.URL_LOGIN_COMPAT), map);
    }
    // ********************* 使用cloudflare R2上传文件

    public Call<CloudflareUploadTokenResponse> getCloudflareUploadToken(String filePath, String homeId, int type) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        FileUploadUtils.FileInfo fileInfo = FileUploadUtils.getFileInfo(filePath, type);
        if (fileInfo == null) {
            return null;
        }
        try {
            jsonObject.put("content_length", fileInfo.fileSize);
            jsonObject.put("content_type", fileInfo.contentType);
            jsonObject.put("file_extension", fileInfo.extension);
            jsonObject.put("home_id", homeId);
            jsonObject.put("type", fileInfo.typeCode);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.getCloudflareUploadToken(getUrl(Urls.URL_CLOUDFLARE_UPLOAD_TOKEN), map);
    }

    public Call<ResponseBody> uploadFileToCloudflareR2(String uploadUrl, RequestBody fileBody) {
        return services.uploadFileToCloudflareR2(uploadUrl, fileBody);
    }
}
