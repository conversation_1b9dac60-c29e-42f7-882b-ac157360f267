package com.dinsafer.dincore.http;

import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.PUT;
import retrofit2.http.Url;

/**
 * 文件上传API接口
 * 
 * <AUTHOR> Assistant
 * @since 2025/7/16
 */
public interface IFileUploadApi {
    
    /**
     * 上传文件到指定URL
     * @param uploadUrl 上传地址
     * @param fileBody 文件请求体
     * @param authorization 认证头（可选）
     * @return 上传响应
     */
    @PUT
    Call<ResponseBody> uploadFile(@Url String uploadUrl, 
                                 @Body RequestBody fileBody,
                                 @Header("Authorization") String authorization);
}
