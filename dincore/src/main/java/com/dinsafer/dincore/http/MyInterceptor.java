package com.dinsafer.dincore.http;


import android.util.Log;

import com.dinsafer.dincore.common.DDTag;
import com.dinsafer.dincore.crypt.ICrypt;
import com.dinsafer.dincore.db.DBKey;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.dincore.user.bean.LoginAgainEvent;
import com.dinsafer.dincore.user.bean.LogoutEvent;
import com.dinsafer.dssupport.msctlib.db.KV;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.dssupport.utils.HexUtil;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.nio.charset.Charset;

import okhttp3.FormBody;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.internal.http.HttpHeaders;

/**
 * Created by Rinfon on 16/8/22.
 */
public class MyInterceptor implements Interceptor {


    public static String GMTIME = "gmtime";

    public static String GM = "1";

    private static String[] whiteList = new String[]{"/device/threeinone",
            "/device/homepage", "/device/get-device-info"};
    private static String[] ignoreList = new String[]{"/home/<USER>"};

    private String host;

    private ICrypt iCrypt;

    public MyInterceptor(String host, ICrypt crypt) {
        this.host = host;
        this.iCrypt = crypt;
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        String value = "";
        if (request.body() != null && request.body() instanceof FormBody) {
            FormBody.Builder body = new FormBody.Builder();
            for (int i = 0; i < ((FormBody) request.body()).size(); i++) {
                if (((FormBody) request.body()).name(i).equals("json")) {
                    value = ((FormBody) request.body()).value(i);
                    try {
                        JSONObject jsonObject = new JSONObject(value);
                        String rc4String = getRC4StringWithGMTime(jsonObject);
                        body.addEncoded("json", rc4String);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }

                } else if (((FormBody) request.body()).name(i).equals("token")) {
                    String tokenValue = (((FormBody) request.body()).value(i));
                    if (!UserManager.getInstance().isThirdPartyUser()) {
                        tokenValue = tokenValue + "_" + (System.currentTimeMillis() * 1000000);
                    }
                    DDLog.i("MyInterceptor", "token =========>>> " + tokenValue);
                    tokenValue = HexUtil.bytesToHexString(iCrypt.encode(tokenValue.getBytes()));
                    body.addEncoded("token", tokenValue);
                } else {
                    body.addEncoded(((FormBody) request.body()).name(i), ((FormBody) request.body()).value(i));
                }
            }

            body.addEncoded("gm", GM);

            Request.Builder builder = request.newBuilder();
            if (KV.containKey(DBKey.APIKEY)) {
                String tempUrl = request.url().toString().replaceFirst(request.url().host(),
                        KV.getString(DBKey.APIKEY, "https://" + host));
                builder.url(tempUrl);

                DDLog.i("widget helper", "++++ Exists :" + tempUrl + " ++++");
            } else {
                builder.addHeader("Host", host);
                builder.addHeader("X-Online-Host", host);
//                builder.header("host",request.url().host());
            }

            request = builder.post(body.build()).build();
        }
        // try the request
        Response originalResponse = chain.proceed(request);
        DDLog.i(DDTag.NETWORK, "request-->url:" + request.url() + " /body:" + value);
        DDLog.i(DDTag.NETWORK, "code:" + originalResponse.code()
                + " message:" + originalResponse.message()
                + " url:" + request.url()
                + "params:" + request.body());
        originalResponse = checkStatus(value, originalResponse, request.url().toString());
        if (originalResponse.isSuccessful()) {
            return originalResponse;

        } else {
            DDLog.e(DDTag.NETWORK, "code:" + originalResponse.code()
                    + " message:" + originalResponse.message()
                    + " url:" + request.url()
                    + "params:" + request.body());
            throw new IOException("message:" + originalResponse.message() + " code:" + originalResponse.code());
        }
    }

    private Response checkStatus(String request, Response response, String url) {
        Response.Builder builder = response.newBuilder();
        Response clone = builder.build();
        ResponseBody responseBody = clone.body();
        JSONObject respnoseJson = null;
        String body = "";
        try {
            if (HttpHeaders.hasBody(clone)) {
                if (responseBody == null) return response;
                MediaType contentType = responseBody.contentType();
                if (isPlaintext(contentType)) {
                    body = response.body().string();
//                    pluginqrcode / scan / 这个接口不会返回json，它是直接返回加密后的字符串，所以这里会报错
                    respnoseJson = new JSONObject(body);
                    DDLog.i(DDTag.NETWORK, "response:" + respnoseJson.toString());
                    if (respnoseJson.getInt("Status") == -12) {
                        logTokenErrorInfo(request, body, url);
                        if (isInIgnoreList(url)) {
                            // 不做处理
                        } else if (isInWhiteList(url)) {
                            DDLog.i(DDTag.NETWORK, "-12!!!:need to autologin");
                            EventBus.getDefault().post(new LoginAgainEvent());
                        } else {
                            DDLog.i(DDTag.NETWORK, "-12!!!:but didn't need to autologin");
                            DDLog.e(DDTag.NETWORK, "-12!!! 进入离线弹窗");
                            EventBus.getDefault().post(new LogoutEvent());
                        }
                    }
//                    重新生成一个给下游使用
                    responseBody = ResponseBody.create(contentType, body);
                    return response.newBuilder().body(responseBody).build();
                } else {
                }
            }
        } catch (IOException e) {
            responseBody = ResponseBody.create(responseBody.contentType(), body);
            return response.newBuilder().body(responseBody).build();
        } catch (Exception e) {
            responseBody = ResponseBody.create(responseBody.contentType(), body);
            return response.newBuilder().body(responseBody).build();
        }

        return response;
    }

    /**
     * 检查-12是否需要处理。
     *
     * @param url
     * @return
     */
    private boolean isInWhiteList(String url) {
        for (String white :
                whiteList) {
            if (url.contains(white)) {
                return true;
            }
        }
        return false;
    }

    private boolean isInIgnoreList(String url) {
        for (String white :
                ignoreList) {
            if (url.contains(white)) {
                return true;
            }
        }
        return false;
    }

    private static boolean isPlaintext(MediaType mediaType) {
        if (mediaType == null) return false;
        if (mediaType.type() != null && mediaType.type().equals("text")) {
            return true;
        }
        String subtype = mediaType.subtype();
        if (subtype != null) {
            subtype = subtype.toLowerCase();
            if (subtype.contains("x-www-form-urlencoded") || subtype.contains("json") || subtype.contains("xml") || subtype.contains("html")) //
                return true;
        }
        return false;
    }

    private static final Charset UTF8 = Charset.forName("UTF-8");

    private static Charset getCharset(MediaType contentType) {
        Charset charset = contentType != null ? contentType.charset(UTF8) : UTF8;
        if (charset == null) charset = UTF8;
        return charset;
    }

    public String getRC4StringWithGMTime(JSONObject jsonObject) {
        jsonObject = getGMTime(jsonObject);
        return HexUtil.bytesToHexString(iCrypt.encode(jsonObject.toString().getBytes()));
    }

    public JSONObject getGMTime(JSONObject jsonObject) {
        try {
            jsonObject.put(GMTIME, System.currentTimeMillis() * 1000000);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObject;
    }

    private void logTokenErrorInfo(String request, String response, String url) {
        DDLog.e(DDTag.NETWORK, "-12!!!,url is:" + url);
        DDLog.e(DDTag.NETWORK, "-12!!!,request is:" + request);
        DDLog.e(DDTag.NETWORK, "-12!!!,response is:" + response);
    }
}
