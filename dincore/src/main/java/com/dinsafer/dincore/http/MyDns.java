package com.dinsafer.dincore.http;


import com.dinsafer.dincore.DinCore;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;

import okhttp3.Dns;

/**
 * Created by rinofn on 17-12-10.
 */

public class MyDns implements Dns {

    @Override
    public List<InetAddress> lookup(String hostname) throws UnknownHostException {
        String[] strIps = DinCore.getHttpdns().getIpsByHostAsync(hostname);
        List<InetAddress> ipList;
        if (strIps != null && strIps.length > 0) {
            ipList = new ArrayList<>();
            for (String ip : strIps) {
                ipList.add(InetAddress.getByName(ip));
            }
        } else {
            ipList = Dns.SYSTEM.lookup(hostname);
        }
        return ipList;
    }

}
