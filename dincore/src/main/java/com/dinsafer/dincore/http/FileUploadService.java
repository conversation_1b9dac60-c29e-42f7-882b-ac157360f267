package com.dinsafer.dincore.http;

import androidx.annotation.NonNull;

import com.dinsafer.dincore.user.api.IResultCallback2;
import com.dinsafer.dincore.utils.FileUploadUtils;
import com.dinsafer.dssupport.utils.DDLog;

import okhttp3.MediaType;
import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 通用文件上传服务
 * 提供完整的文件上传流程：获取上传token -> 上传文件 -> 上传文件key
 */
public class FileUploadService {
    private static final String TAG = "FileUploadService";
    private static final int DEFAULT = -1;
    public static final int SOURCE_CLOUDFLARE = 1;
    public static final int SOURCE_QINIU = 0;

    private static FileUploadService instance;

    // 上传模式配置
    private UploadMode uploadMode = UploadMode.AUTO_UPLOAD_KEY; // 默认自动上传key

    /**
     * 上传模式枚举
     */
    public enum UploadMode {
        /**
         * 只上传文件，返回imageKey，不自动上传key
         * 适合需要精确控制的场景
         */
        FILE_ONLY,

        /**
         * 自动上传文件和key（完整流程）
         * 适合简单场景，性能较好
         */
        AUTO_UPLOAD_KEY,

        /**
         * 智能模式：根据文件大小和类型自动选择
         * 小文件自动上传key，大文件只上传文件
         */
        SMART_MODE
    }

    private FileUploadService() {
        // 私有构造函数，防止外部实例化
    }

    /**
     * 获取单例实例
     * @return FileUploadService实例
     */
    public static FileUploadService getInstance() {
        if (instance == null) {
            synchronized (FileUploadService.class) {
                if (instance == null) {
                    instance = new FileUploadService();
                }
            }
        }
        return instance;
    }

    /**
     * 设置上传模式
     * @param mode 上传模式
     * @return FileUploadService实例，支持链式调用
     */
    public FileUploadService setUploadMode(UploadMode mode) {
        this.uploadMode = mode;
        DDLog.i(TAG, "设置上传模式: " + mode);
        return this;
    }

    /**
     * 获取当前上传模式
     * @return 当前上传模式
     */
    public UploadMode getUploadMode() {
        return uploadMode;
    }

    /**
     * 上传文件到Cloudflare R2（根据配置模式自动选择行为）
     * @param filePath 文件路径
     * @param homeId 家庭ID
     * @param type 文件类型
     * @param callback 回调接口
     */
    public void uploadFile(@NonNull String filePath, @NonNull String homeId, int type,
                           @NonNull IResultCallback2<String> callback) {

        DDLog.i(TAG, "开始上传文件，当前模式: " + uploadMode);

        switch (uploadMode) {
            case FILE_ONLY:
                // 只上传文件，返回imageKey
                performUploadProcess(filePath, homeId, type, false, callback);
                break;

            case AUTO_UPLOAD_KEY:
                // 自动上传文件和key
                performUploadProcess(filePath, homeId, type, true, callback);
                break;

            case SMART_MODE:
                // 智能模式：根据文件信息决定
                uploadFileWithSmartMode(filePath, homeId, type, callback);
                break;

            default:
                // 默认使用AUTO_UPLOAD_KEY模式
                performUploadProcess(filePath, homeId, type, true, callback);
                break;
        }
    }

    /**
     * 智能模式上传
     */
    private void uploadFileWithSmartMode(@NonNull String filePath, @NonNull String homeId, int type,
                                        @NonNull IResultCallback2<String> callback) {
        FileUploadUtils.FileInfo fileInfo = FileUploadUtils.getFileInfo(filePath, type);
        if (fileInfo == null) {
            callback.onError(DEFAULT, "文件信息获取失败");
            return;
        }

        // 智能判断：大文件或特定类型只上传文件，小文件自动上传key
        boolean shouldUploadKey = shouldUploadKeyInSmartMode(fileInfo);
        DDLog.i(TAG, "智能模式判断结果: " + (shouldUploadKey ? "上传文件和key" : "只上传文件"));

        performUploadProcess(filePath, homeId, type, shouldUploadKey, callback);
    }

    /**
     * 执行上传流程的核心方法
     */
    private void performUploadProcess(@NonNull String filePath, @NonNull String homeId, int type,
                                     boolean shouldUploadKey, @NonNull IResultCallback2<String> callback) {
        FileUploadUtils.FileInfo fileInfo = FileUploadUtils.getFileInfo(filePath, type);
        if (fileInfo == null) {
            callback.onError(DEFAULT, "文件信息获取失败");
            return;
        }

        // 获取上传token
        Api.getApi().getCloudflareUploadToken(filePath, homeId, type)
                .enqueue(new Callback<CloudflareUploadTokenResponse>() {
                    @Override
                    public void onResponse(@NonNull Call<CloudflareUploadTokenResponse> call,
                                           @NonNull Response<CloudflareUploadTokenResponse> response) {
                        CloudflareUploadTokenResponse responseEntry = response.body();
                        if (responseEntry != null && responseEntry.getResult() != null) {
                            String uploadUrl = responseEntry.getResult().getToken();
                            String imageKey = responseEntry.getResult().getKey();

                            if (uploadUrl != null && !uploadUrl.isEmpty()) {
                                if (shouldUploadKey) {
                                    // 上传文件后自动上传key
                                    performFileUploadWithAutoKey(uploadUrl, fileInfo, imageKey, callback);
                                } else {
                                    // 只上传文件，返回imageKey
                                    performFileUpload(uploadUrl, fileInfo, imageKey, callback);
                                }
                                return;
                            }
                        }

                        callback.onError(DEFAULT, "获取上传token失败");
                    }

                    @Override
                    public void onFailure(@NonNull Call<CloudflareUploadTokenResponse> call,
                                          @NonNull Throwable t) {
                        DDLog.e(TAG, "获取上传token失败: " + t.getMessage());
                        callback.onError(DEFAULT, "网络错误");
                    }
                });
    }

    /**
     * 智能模式判断是否需要上传key
     */
    private boolean shouldUploadKeyInSmartMode(FileUploadUtils.FileInfo fileInfo) {
        // 基于性能考虑的智能判断规则

        // 1. 大文件（超过10MB）只上传文件，不自动上传key
        if (fileInfo.fileSize > 10 * FileUploadUtils.FileSizeConstants.MB) {
            DDLog.i(TAG, "大文件(" + FileUploadUtils.getReadableFileSize(fileInfo.fileSize) + ")，只上传文件");
            return false;
        }

        // 2. 视频文件超过5MB，只上传文件
        if (FileUploadUtils.isVideoFile(fileInfo.filePath) &&
            fileInfo.fileSize > 5 * FileUploadUtils.FileSizeConstants.MB) {
            DDLog.i(TAG, "大视频文件，只上传文件");
            return false;
        }

        // 3. 临时文件或缓存文件，只上传文件
        if (fileInfo.filePath.contains("/cache/") || fileInfo.filePath.contains("/temp/")) {
            DDLog.i(TAG, "临时文件，只上传文件");
            return false;
        }

        // 4. 其他情况自动上传key
        DDLog.i(TAG, "小文件或常规文件，自动上传key");
        return true;
    }

    /**
     * 执行文件上传并自动上传key
     */
    private void performFileUploadWithAutoKey(String uploadUrl, FileUploadUtils.FileInfo fileInfo,
                                             String imageKey, IResultCallback2<String> callback) {
        try {
            RequestBody fileBody = RequestBody.create(
                    MediaType.parse(fileInfo.contentType),
                    fileInfo.file
            );

            Api.getApi().uploadFileToCloudflareR2(uploadUrl, fileBody)
                    .enqueue(new Callback<ResponseBody>() {
                        @Override
                        public void onResponse(@NonNull Call<ResponseBody> call,
                                               @NonNull Response<ResponseBody> response) {
                            DDLog.i(TAG, "文件上传响应，状态码: " + response.code());

                            if (response.isSuccessful()) {
                                // 文件上传成功，自动上传key
                                DDLog.i(TAG, "文件上传成功，自动上传key: " + imageKey);
                                uploadFileKey(imageKey, SOURCE_CLOUDFLARE, callback);
                            } else {
                                callback.onError(response.code(), "文件上传失败");
                            }
                        }

                        @Override
                        public void onFailure(@NonNull Call<ResponseBody> call, @NonNull Throwable t) {
                            DDLog.e(TAG, "文件上传失败: " + t.getMessage());
                            callback.onError(DEFAULT, "文件上传失败");
                        }
                    });

        } catch (Exception e) {
            DDLog.e(TAG, "创建请求体失败: " + e.getMessage());
            callback.onError(DEFAULT, "文件处理失败");
        }
    }

    /**
     * 执行实际的文件上传
     */
    private void performFileUpload(String uploadUrl, FileUploadUtils.FileInfo fileInfo,
                                   String key, IResultCallback2<String> callback) {
        try {
            RequestBody fileBody = RequestBody.create(
                    MediaType.parse(fileInfo.contentType),
                    fileInfo.file
            );

            Api.getApi().uploadFileToCloudflareR2(uploadUrl, fileBody)
                    .enqueue(new Callback<ResponseBody>() {
                        @Override
                        public void onResponse(@NonNull Call<ResponseBody> call,
                                               @NonNull Response<ResponseBody> response) {
                            DDLog.i(TAG, "文件上传响应，状态码: " + response.code());

                            if (response.isSuccessful()) {
                                // 文件上传成功，将imageKey回调给调用者
                                DDLog.i(TAG, "文件上传成功，返回imageKey: " + key);
                                callback.onSuccess(key);
                            } else {
                                callback.onError(response.code(), "文件上传失败");
                            }
                        }

                        @Override
                        public void onFailure(@NonNull Call<ResponseBody> call, @NonNull Throwable t) {
                            DDLog.e(TAG, "文件上传失败: " + t.getMessage());
                            callback.onError(DEFAULT, "文件上传失败");
                        }
                    });

        } catch (Exception e) {
            DDLog.e(TAG, "创建请求体失败: " + e.getMessage());
            callback.onError(DEFAULT, "文件处理失败");
        }
    }
}