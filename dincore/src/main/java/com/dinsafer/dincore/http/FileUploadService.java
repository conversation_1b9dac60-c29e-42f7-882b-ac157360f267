package com.dinsafer.dincore.http;

import androidx.annotation.NonNull;

import com.dinsafer.dincore.user.api.IResultCallback2;
import com.dinsafer.dincore.utils.FileUploadUtils;
import com.dinsafer.dssupport.utils.DDLog;

import okhttp3.MediaType;
import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 通用文件上传服务
 * 提供完整的文件上传流程：获取上传token -> 上传文件 -> 上传文件key
 */
public class FileUploadService {
    private static final String TAG = "FileUploadService";
    private static final int DEFAULT = -1;
    private static final int SOURCE_CLOUDFLARE = 1;

    private static FileUploadService instance;

    private FileUploadService() {
        // 私有构造函数，防止外部实例化
    }

    /**
     * 获取单例实例
     * @return FileUploadService实例
     */
    public static FileUploadService getInstance() {
        if (instance == null) {
            synchronized (FileUploadService.class) {
                if (instance == null) {
                    instance = new FileUploadService();
                }
            }
        }
        return instance;
    }

    /**
     * 上传文件到Cloudflare R2
     * @param filePath 文件路径
     * @param homeId 家庭ID
     * @param type 文件类型
     * @param callback 回调接口
     */
    public void uploadFile(@NonNull String filePath, @NonNull String homeId, int type,
                           @NonNull IResultCallback2<String> callback) {

        FileUploadUtils.FileInfo fileInfo = FileUploadUtils.getFileInfo(filePath, type);
        if (fileInfo == null) {
            callback.onError(DEFAULT, "文件信息获取失败");
            return;
        }

        // 获取上传token
        Api.getApi().getCloudflareUploadToken(filePath, homeId, type)
                .enqueue(new Callback<CloudflareUploadTokenResponse>() {
                    @Override
                    public void onResponse(@NonNull Call<CloudflareUploadTokenResponse> call,
                                           @NonNull Response<CloudflareUploadTokenResponse> response) {
                        CloudflareUploadTokenResponse responseEntry = response.body();
                        if (responseEntry != null && responseEntry.getResult() != null) {
                            String uploadUrl = responseEntry.getResult().getToken();
                            String imageKey = responseEntry.getResult().getKey();

                            if (uploadUrl != null && !uploadUrl.isEmpty()) {
                                performFileUpload(uploadUrl, fileInfo, imageKey, callback);
                                return;
                            }
                        }

                        callback.onError(DEFAULT, "获取上传token失败");
                    }

                    @Override
                    public void onFailure(@NonNull Call<CloudflareUploadTokenResponse> call,
                                          @NonNull Throwable t) {
                        DDLog.e(TAG, "获取上传token失败: " + t.getMessage());
                        callback.onError(DEFAULT, "网络错误");
                    }
                });
    }

    /**
     * 执行实际的文件上传
     */
    private void performFileUpload(String uploadUrl, FileUploadUtils.FileInfo fileInfo,
                                   String imageKey, IResultCallback2<String> callback) {
        try {
            RequestBody fileBody = RequestBody.create(
                    MediaType.parse(fileInfo.contentType),
                    fileInfo.file
            );

            Api.getApi().uploadFileToCloudflareR2(uploadUrl, fileBody)
                    .enqueue(new Callback<ResponseBody>() {
                        @Override
                        public void onResponse(@NonNull Call<ResponseBody> call,
                                               @NonNull Response<ResponseBody> response) {
                            DDLog.i(TAG, "文件上传响应，状态码: " + response.code());

                            if (response.isSuccessful()) {
                                // 上传文件key到服务器
                                uploadFileKey(imageKey, SOURCE_CLOUDFLARE, callback);
                            } else {
                                callback.onError(response.code(), "文件上传失败");
                            }
                        }

                        @Override
                        public void onFailure(@NonNull Call<ResponseBody> call, @NonNull Throwable t) {
                            DDLog.e(TAG, "文件上传失败: " + t.getMessage());
                            callback.onError(DEFAULT, "文件上传失败");
                        }
                    });

        } catch (Exception e) {
            DDLog.e(TAG, "创建请求体失败: " + e.getMessage());
            callback.onError(DEFAULT, "文件处理失败");
        }
    }

    /**
     * 上传文件key到服务器
     */
    private void uploadFileKey(String fileKey, int source, IResultCallback2<String> callback) {
        Api.getApi().uploadImageKey(fileKey, source)
                .enqueue(new retrofit2.Callback<StringResponseEntry>() {
                    @Override
                    public void onResponse(@NonNull Call<StringResponseEntry> call,
                                           @NonNull Response<StringResponseEntry> response) {
                        DDLog.i(TAG, "文件key上传成功");
                        callback.onSuccess(fileKey);
                    }

                    @Override
                    public void onFailure(@NonNull Call<StringResponseEntry> call,
                                          @NonNull Throwable t) {
                        DDLog.e(TAG, "文件key上传失败: " + t.getMessage());
                        callback.onError(DEFAULT, "文件key上传失败");
                    }
                });
    }
}