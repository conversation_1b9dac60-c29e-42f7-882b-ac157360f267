package com.dinsafer.dincore.http;

import androidx.annotation.NonNull;

import com.dinsafer.dincore.user.api.IResultCallback2;
import com.dinsafer.dincore.utils.FileUploadUtils;
import com.dinsafer.dssupport.utils.DDLog;

import okhttp3.MediaType;
import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 通用文件上传服务
 * 提供完整的文件上传流程：获取上传token -> 上传文件 -> 上传文件key
 */
public class FileUploadService {
    private static final String TAG = "FileUploadService";
    private static final int DEFAULT = -1;

    private static FileUploadService instance;

    // 上传模式配置
    private UploadMode uploadMode = UploadMode.SOURCE_CLOUDFLARE; // 默认使用Cloudflare

    private String host = "";

    /**
     * 上传模式枚举
     */
    public enum UploadMode {
        //七牛
        SOURCE_QINIU,
        //CloudflareR2
        SOURCE_CLOUDFLARE,
        
    }

    private FileUploadService() {
        // 私有构造函数，防止外部实例化
    }

    public static FileUploadService getInstance() {
        if (instance == null) {
            synchronized (FileUploadService.class) {
                if (instance == null) {
                    instance = new FileUploadService();
                }
            }
        }
        return instance;
    }

    public String getHost() {
        return host;
    }

    public FileUploadService setUploadMode(UploadMode mode) {
        this.uploadMode = mode;
        return this;
    }

    public UploadMode getUploadMode() {
        return uploadMode;
    }

    public void uploadFile(@NonNull String filePath, @NonNull String homeId, int type,
                           @NonNull IResultCallback2<String> callback) {

        DDLog.i(TAG, "开始上传文件，当前方式: " + uploadMode);

        FileUploadUtils.FileInfo fileInfo = FileUploadUtils.getFileInfo(filePath, type);
        if (fileInfo == null) {
            callback.onError(DEFAULT, "path params error");
            return;
        }

        // 获取上传token
        Api.getApi().getCloudflareUploadToken(filePath, homeId, type)
                .enqueue(new Callback<CloudflareUploadTokenResponse>() {
                    @Override
                    public void onResponse(@NonNull Call<CloudflareUploadTokenResponse> call,
                                           @NonNull Response<CloudflareUploadTokenResponse> response) {
                        CloudflareUploadTokenResponse responseEntry = response.body();
                        if (responseEntry != null && responseEntry.getResult() != null) {
                            String uploadUrl = responseEntry.getResult().getToken();
                            String imageKey = responseEntry.getResult().getKey();
                            String access = responseEntry.getResult().getAccess();
                            String host = responseEntry.getResult().getHost();
                            if (host != null && !host.isEmpty()) {
                                FileUploadService.this.host = host;
                            }
                            if (uploadUrl != null && !uploadUrl.isEmpty()) {
                                performFileUpload(uploadUrl, fileInfo, access, callback);
                                return;
                            }
                        }

                        callback.onError(DEFAULT, "get token fail");
                    }

                    @Override
                    public void onFailure(@NonNull Call<CloudflareUploadTokenResponse> call,
                                          @NonNull Throwable t) {
                        DDLog.e(TAG, "获取上传token失败: " + t.getMessage());
                        callback.onError(DEFAULT, "network error");
                    }
                });
    }



    /**
     * 执行实际的文件上传
     */
    private void performFileUpload(String uploadUrl, FileUploadUtils.FileInfo fileInfo,
                                   String key, IResultCallback2<String> callback) {
        try {
            RequestBody fileBody = RequestBody.create(
                    MediaType.parse(fileInfo.contentType),
                    fileInfo.file
            );

            Api.getApi().uploadFileToCloudflareR2(uploadUrl, fileBody)
                    .enqueue(new Callback<ResponseBody>() {
                        @Override
                        public void onResponse(@NonNull Call<ResponseBody> call,
                                               @NonNull Response<ResponseBody> response) {
                            DDLog.i(TAG, "文件上传响应，状态码: " + response.code());

                            if (response.isSuccessful()) {
                                // 文件上传成功，将imageKey回调给调用者
                                DDLog.i(TAG, "文件上传成功，返回imageKey: " + key);
                                callback.onSuccess(key);
                            } else {
                                callback.onError(response.code(), "file upload fail");
                            }
                        }

                        @Override
                        public void onFailure(@NonNull Call<ResponseBody> call, @NonNull Throwable t) {
                            DDLog.e(TAG, "文件上传失败: " + t.getMessage());
                            callback.onError(DEFAULT, "file upload fail");
                        }
                    });

        } catch (Exception e) {
            DDLog.e(TAG, "创建请求体失败: " + e.getMessage());
            callback.onError(DEFAULT, "file upload fail:"+e.getMessage());
        }
    }
}