package com.dinsafer.dincore.http;

import com.dinsafer.dincore.user.bean.DinUserLoginResponse;
import com.dinsafer.dincore.user.bean.RefreshVerifyCodeResponse;
import com.dinsafer.dincore.user.bean.RegisterResponse;

import java.util.Map;

import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Url;

public interface IApi {
    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> getPhoneValidateCode(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<RegisterResponse> validatePhoneCode(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<DinUserLoginResponse> Login(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> logout(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> changePassword(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> modifyUid(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> modifyUidPassword(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> bindPhone(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> unbindPhone(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> verifyBindPhone(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> verifyUnBindPhone(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> bindEmail(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> verifyBindEmail(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> unbindEmail(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> getForgetPWDbyEmail(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> comfirmForgetPWDByEmailCode(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> getForgetPWDbyPhone(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> comfirmForgetPWDByPhoneCode(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> getUploadToken(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> getUploadImage(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<ResponseBody> getDeviceCmdCall(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<ResponseBody> getNewQRCodeScan(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<ResponseBody> getNewQRCodetpIpcScan(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> getEmailValidateCode(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<RegisterResponse> validateEmailCode(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> deleteAccount(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> verifyCodeOnly(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> changePasswordOnly(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> checkPasswordOnly(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<RefreshVerifyCodeResponse> refreshVerifyCode(@Url String url, @FieldMap Map<String, Object> map);

    // ********************* CAWA 兼容接口

    @POST
    @FormUrlEncoded
    Call<DinUserLoginResponse> LoginCW(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<CloudflareUploadTokenResponse> getCloudflareUploadToken(@Url String url, @FieldMap Map<String, Object> map);

    /**
     * 上传文件到Cloudflare R2
     */
    @PUT
    Call<ResponseBody> uploadFileToCloudflareR2(@Url String uploadUrl, @Body RequestBody fileBody);
}
