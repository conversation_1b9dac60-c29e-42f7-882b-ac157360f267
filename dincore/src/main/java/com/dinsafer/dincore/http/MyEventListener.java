package com.dinsafer.dincore.http;

import androidx.annotation.NonNull;

import com.dinsafer.dssupport.utils.DDLog;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import okhttp3.Call;
import okhttp3.EventListener;

public class MyEventListener extends EventListener {
    private static final String TAG = MyEventListener.class.getSimpleName();
    private long startTime;
    private List<String> urls;
    private String appId;

    public MyEventListener(String appId) {
        this.appId = appId;
        urls = new ArrayList<>();
        urls.add("https://emaldo-test.dinsafer.com:50443/user/login");
        urls.add("https://emaldo-test.dinsafer.com:50443/home/<USER>");
        urls.add("https://emaldo-test.dinsafer.com:50443/home/<USER>");
        urls.add("https://emaldo-test.dinsafer.com:50443/bmt/search-bmt");
        urls.add("https://emaldo-test.dinsafer.com:50443/bmt/e2e-user-login");
    }

    @Override
    public void callStart(Call call) {
        startTime = System.currentTimeMillis();
        String url = call.request().url().toString().replace("/"+appId, "");
        DDLog.i(TAG, "callurl==="+url);
        if (urls.contains(url)) {
            DDLog.i(TAG, url + "==>Request started at: " + startTime);
        }
    }

    @Override
    public void callEnd(Call call) {
        long endTime = System.currentTimeMillis();
        long requestTime = endTime - startTime;
        String url = call.request().url().toString().replace("/"+appId, "");
        DDLog.i(TAG, "callurl==="+url);
        if (urls.contains(url)) {
            DDLog.i(TAG, url + "==>Request ended at: " + endTime);
            DDLog.i(TAG, url + "==>Request time: " + requestTime + "ms");
        }
    }

    @Override
    public void callFailed(Call call, IOException ioe) {
        // 处理请求失败的情况
    }
}

