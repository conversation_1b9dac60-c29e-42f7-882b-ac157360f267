package com.dinsafer.dincore.http;


/**
 * Description:
 * Date: 2019/5/9
 */
public class Urls {

    private String appid;

    public static final String URL_LOGIN = "/user/login/";
    public static final String URL_LOGOUT = "/user/logout/";
    public static final String URL_CHANGE_PASSWORD = "/auth/changepwd/v2/";
    public static final String URL_MODIFY_UID = "/user/change-uid/";
    public static final String URL_MODIFY_UID_PASSWORD = "/user/change-uid-password/";
    public static final String URL_BIND_PHONE_V3 = "/user/change-phone-binding-send-code-v3/";
    public static final String URL_UNBIND_PHONE_V3 = "/user/unbind-phone-send-code-v3/";
    public static final String URL_VERIFY_BIND_PHONE = "/user/change-phone-binding-verify-code/";
    public static final String URL_VERIFY_UNBIND_PHONE = "/user/unbind-phone-verify-code/";
    public static final String URL_BIND_EMAIL = "/user/change-email-binding-send-code/";
    public static final String URL_VERIFY_BIND_EMAIL = "/user/change-email-binding-verify-code/";
    public static final String URL_UNBIND_EMAIL = "/user/unbind-email-send-code/";
    public static final String URL_VERIFY_UNBIND_EMAIL = "/user/unbind-email-verify-code/";
    public static final String URL_FORGET_PASSWORD_BY_EMAIL = "/user/forget-password-email-send-code/";
    public static final String URL_CONFIRM_FORGET_PASSWORD_BY_EMAIL_CODE = "/user/forget-password-email-verify-code/";
    public static final String URL_FORGET_PASSWORD_BY_PHONE_V3 = "/user/forget-password-phone-send-code-v3/";
    public static final String URL_CONFIRM_FORGET_PASSWORD_BY_PHONE_CODE = "/user/forget-password-phone-verify-code/";
    public static final String URL_GET_UPLOAD_TOKEN = "/uploader/getuptoken/";
    public static final String URL_UPLOAD_AVATAR_KEY = "/auth/modifyuserinfos/v3/";

    public static final String URL_PANEL_CMD = "/device/sendcmd/";
    public static final String URL_GET_PHONE_REGISTER_CODE_V3 = "/user/sign-up-phone-send-code-v3/";
    public static final String URL_GET_EMAIL_REGISTER_CODE = "/user/sign-up-email-send-code/";
    public static final String URL_VARIFY_REGISTER_CODE = "/user/sign-up-phone-verify-code/";
    public static final String URL_VARIFY_EMAIL_REGISTER_CODE = "/user/sign-up-email-verify-code/";
    public static final String URL_DELETE_ACCOUNT = "/user/logoff/";
    public static final String URL_ONLY_VERIFY_CODE = "/user/only-verify-code/";
    public static final String URL_CHANGE_PASSWORD_ONLY = "/user/change-password/";
    public static final String URL_CHECK_PASSWORD = "/user/check-password/";

    public static final String URL_SCAN_PLUGIN_QR = "/qrcode/scan/";

    public static final String URL_SCAN_PLUGIN_QR_TP_IPC = "/qrcode/scan/ipc/";

    public static final String URL_REFRESH_VERIFY_CODE = "/user/refresh-verify-code/";


    // ************ 兼容模式（cawa）登录或注册
    // 登录或注册
    public static final String URL_LOGIN_COMPAT = "/user/ext/login/";
    public static final String URL_CLOUDFLARE_UPLOAD_TOKEN = ;

    public String getUrl(String inteface) {
        return inteface + appid;
    }

    public Urls(String appid) {
        this.appid = appid;
    }

    public static UrlsBuilder builder() {
        return new UrlsBuilder();
    }

    public static class UrlsBuilder {
        private String appid;

        UrlsBuilder() {
        }

        public UrlsBuilder appid(String appid) {
            this.appid = appid;
            return this;
        }

        public Urls build() {
            return new Urls(this.appid);
        }

        @Override
        public String toString() {
            return "UrlsBuilder{" +
                    "appid='" + appid + '\'' +
                    '}';
        }
    }

}
