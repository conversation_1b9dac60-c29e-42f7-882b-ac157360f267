package com.dinsafer.dincore.http;

import android.text.TextUtils;
import android.webkit.MimeTypeMap;

import com.dinsafer.dssupport.utils.DDLog;

import java.io.File;

/**
 * 文件上传工具类
 * 用于获取文件上传相关的数据和处理
 * 
 * <AUTHOR> Assistant
 * @since 2025/7/16
 */
public class FileUploadUtils {
    private static final String TAG = "FileUploadUtils";
    
    /**
     * 获取文件扩展名
     * @param filePath 文件路径
     * @return 文件扩展名（包含点号，如".jpg"）
     */
    public static String getFileExtension(String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return "";
        }
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filePath.length() - 1) {
            return filePath.substring(lastDotIndex);
        }
        return "";
    }
    
    /**
     * 根据Content-Type确定文件类型参数
     * @param contentType MIME类型
     * @return 文件类型编号
     */
    public static int getFileTypeByContentType(String contentType) {
        if (TextUtils.isEmpty(contentType)) {
            return 5; // 默认类型
        }
        
        if (contentType.startsWith("image/")) {
            return 5; // 图片类型
        } else if (contentType.startsWith("video/")) {
            return 6; // 视频类型
        } else if (contentType.startsWith("audio/")) {
            return 7; // 音频类型
        } else if (contentType.equals("application/pdf")) {
            return 8; // PDF文档
        } else {
            return 9; // 其他文件类型
        }
    }
    
    /**
     * 根据文件路径自动获取Content-Type
     * @param filePath 文件路径
     * @return MIME类型
     */
    public static String getContentTypeFromFile(String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return "application/octet-stream";
        }
        
        String extension = getFileExtension(filePath);
        if (!TextUtils.isEmpty(extension)) {
            // 去掉点号
            String ext = extension.substring(1);
            String mimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(ext);
            if (!TextUtils.isEmpty(mimeType)) {
                return mimeType;
            }
        }
        
        return "application/octet-stream";
    }
    
    /**
     * 验证文件是否存在且可读
     * @param filePath 文件路径
     * @return 是否有效
     */
    public static boolean validateFile(String filePath) {
        return validateFile(filePath, Long.MAX_VALUE);
    }
    
    /**
     * 验证文件是否存在且可读，并检查大小限制
     * @param filePath 文件路径
     * @param maxSizeInBytes 最大文件大小（字节）
     * @return 是否有效
     */
    public static boolean validateFile(String filePath, long maxSizeInBytes) {
        if (TextUtils.isEmpty(filePath)) {
            DDLog.e(TAG, "文件路径为空");
            return false;
        }
        
        File file = new File(filePath);
        if (!file.exists()) {
            DDLog.e(TAG, "文件不存在: " + filePath);
            return false;
        }
        
        if (!file.canRead()) {
            DDLog.e(TAG, "文件无法读取: " + filePath);
            return false;
        }
        
        long fileSize = file.length();
        if (fileSize > maxSizeInBytes) {
            DDLog.e(TAG, "文件过大: " + getReadableFileSize(fileSize) + 
                    " (最大: " + getReadableFileSize(maxSizeInBytes) + ")");
            return false;
        }
        
        DDLog.i(TAG, "文件验证通过: " + filePath + " (" + getReadableFileSize(fileSize) + ")");
        return true;
    }
    
    /**
     * 获取可读的文件大小格式
     * @param size 文件大小（字节）
     * @return 可读格式的大小字符串
     */
    public static String getReadableFileSize(long size) {
        if (size <= 0) return "0 B";
        
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        
        return String.format("%.1f %s", size / Math.pow(1024, digitGroups), units[digitGroups]);
    }
    
    /**
     * 检查文件是否为图片
     * @param filePath 文件路径
     * @return 是否为图片
     */
    public static boolean isImageFile(String filePath) {
        String contentType = getContentTypeFromFile(filePath);
        return contentType.startsWith("image/");
    }
    
    /**
     * 检查文件是否为视频
     * @param filePath 文件路径
     * @return 是否为视频
     */
    public static boolean isVideoFile(String filePath) {
        String contentType = getContentTypeFromFile(filePath);
        return contentType.startsWith("video/");
    }
    
    /**
     * 检查文件是否为音频
     * @param filePath 文件路径
     * @return 是否为音频
     */
    public static boolean isAudioFile(String filePath) {
        String contentType = getContentTypeFromFile(filePath);
        return contentType.startsWith("audio/");
    }
    
    /**
     * 检查文件是否为PDF
     * @param filePath 文件路径
     * @return 是否为PDF
     */
    public static boolean isPdfFile(String filePath) {
        String contentType = getContentTypeFromFile(filePath);
        return "application/pdf".equals(contentType);
    }
    
    /**
     * 获取文件信息
     * @param filePath 文件路径
     * @return 文件信息对象
     */
    public static FileInfo getFileInfo(String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return null;
        }
        
        File file = new File(filePath);
        if (!file.exists()) {
            return null;
        }
        
        FileInfo info = new FileInfo();
        info.filePath = filePath;
        info.fileName = file.getName();
        info.fileSize = file.length();
        info.extension = getFileExtension(filePath);
        info.contentType = getContentTypeFromFile(filePath);
        info.typeCode = getFileTypeByContentType(info.contentType);
        info.readableSize = getReadableFileSize(info.fileSize);
        info.isImage = isImageFile(filePath);
        info.isVideo = isVideoFile(filePath);
        info.isAudio = isAudioFile(filePath);
        info.isPdf = isPdfFile(filePath);
        
        return info;
    }
    
    /**
     * 文件信息类
     */
    public static class FileInfo {
        public String filePath;      // 文件路径
        public String fileName;      // 文件名
        public long fileSize;        // 文件大小（字节）
        public String extension;     // 扩展名
        public String contentType;   // MIME类型
        public int typeCode;         // 类型编号
        public String readableSize;  // 可读大小
        public boolean isImage;      // 是否为图片
        public boolean isVideo;      // 是否为视频
        public boolean isAudio;      // 是否为音频
        public boolean isPdf;        // 是否为PDF
        
        @Override
        public String toString() {
            return "FileInfo{" +
                    "fileName='" + fileName + '\'' +
                    ", fileSize=" + readableSize +
                    ", contentType='" + contentType + '\'' +
                    ", typeCode=" + typeCode +
                    ", extension='" + extension + '\'' +
                    '}';
        }
    }
    
    /**
     * 常用文件大小常量
     */
    public static class FileSizeConstants {
        public static final long KB = 1024L;
        public static final long MB = KB * 1024L;
        public static final long GB = MB * 1024L;
        
        // 常用限制
        public static final long MAX_IMAGE_SIZE = 10 * MB;    // 10MB
        public static final long MAX_VIDEO_SIZE = 100 * MB;   // 100MB
        public static final long MAX_AUDIO_SIZE = 50 * MB;    // 50MB
        public static final long MAX_DOCUMENT_SIZE = 20 * MB; // 20MB
    }
    
    /**
     * 根据文件类型获取推荐的大小限制
     * @param filePath 文件路径
     * @return 推荐的最大大小（字节）
     */
    public static long getRecommendedMaxSize(String filePath) {
        if (isImageFile(filePath)) {
            return FileSizeConstants.MAX_IMAGE_SIZE;
        } else if (isVideoFile(filePath)) {
            return FileSizeConstants.MAX_VIDEO_SIZE;
        } else if (isAudioFile(filePath)) {
            return FileSizeConstants.MAX_AUDIO_SIZE;
        } else if (isPdfFile(filePath)) {
            return FileSizeConstants.MAX_DOCUMENT_SIZE;
        } else {
            return FileSizeConstants.MAX_DOCUMENT_SIZE; // 默认文档大小限制
        }
    }
}
