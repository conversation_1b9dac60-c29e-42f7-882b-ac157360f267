package com.dinsafer.dincore.http;

import androidx.annotation.Keep;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.crypt.DinHttpCrypt;

import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava.RxJavaCallAdapterFactory;

@Keep
public class BmtStatsApi {

    private static BmtStatsApi instance;
    private final int TIMEOUT = 60000;
    //手动创建一个OkHttpClient并设置超时时间
    private OkHttpClient.Builder httpClientBuilder;
    private Retrofit retrofit;
    private Urls urls;

    // bmt 域名
    private String bmtDomain;

    private BmtStatsApi(DinCore dinCore) {
        httpClientBuilder = new OkHttpClient.Builder()
                .addInterceptor(new MyInterceptor(dinCore.getBmtDomain(), new DinHttpCrypt()))
                .connectTimeout(TIMEOUT, TimeUnit.SECONDS)
                .hostnameVerifier((hostname, session) -> true)
                .dns(new CloudflareDohDns(dinCore.getBmtDomain()))
                .writeTimeout(20 * 1000, TimeUnit.MILLISECONDS)
                .readTimeout(20 * 1000, TimeUnit.MILLISECONDS);
        retrofit = new Retrofit.Builder()
                .client(httpClientBuilder.build())
                .addConverterFactory(DecodeConverterFactory.create())
                .addCallAdapterFactory(RxJavaCallAdapterFactory.create())
                .baseUrl("https:" + dinCore.getBmtDomain())
                .build();
        urls = Urls.builder()
                .appid(dinCore.getAppID())
                .build();

        bmtDomain = dinCore.getBmtDomain();
    }

    public static void init(DinCore dinCore) {
        if (instance == null) {
            synchronized (Api.class) {
                if (instance == null) {
                    instance = new BmtStatsApi(dinCore);
                }
            }
        }
    }

    public static BmtStatsApi getApi() {
        return instance;
    }

    public String getUrl(String url) {
        return urls.getUrl(url);
    }

    public Retrofit getRetrofit() {
        return retrofit;
    }

    public String getBmtDomain() {
        return bmtDomain;
    }

    public void setBmtDomain(String bmtDomain) {
        this.bmtDomain = bmtDomain;
    }
}
