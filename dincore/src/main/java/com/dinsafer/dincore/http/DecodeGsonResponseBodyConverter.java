package com.dinsafer.dincore.http;

import android.text.TextUtils;

import com.dinsafer.dincore.common.DDTag;
import com.dinsafer.dincore.crypt.DinHttpCrypt;
import com.dinsafer.dincore.crypt.ICrypt;
import com.dinsafer.dssupport.utils.DDLog;
import com.google.gson.TypeAdapter;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.Charset;

import okhttp3.MediaType;
import okhttp3.ResponseBody;
import retrofit2.Converter;

/**
 * Created by Rinfon on 16/8/12.
 */
public class DecodeGsonResponseBodyConverter<T> implements Converter<ResponseBody, T> {
    private static final Charset UTF_8 = Charset.forName("UTF-8");
    private final TypeAdapter<T> adapter;

    private ICrypt iCrypt = new DinHttpCrypt();

    DecodeGsonResponseBodyConverter(TypeAdapter<T> adapter) {
        this.adapter = adapter;
    }

    @Override
    public T convert(ResponseBody value) throws IOException {
        try {

            JSONObject respnoseJson = null;
            try {
                respnoseJson = new JSONObject(value.string());

                if (respnoseJson.getInt("Status") == 1) {

                    String oldResult = respnoseJson.getString("Result");
                    if (checkResult(oldResult)) {

                        String resultStr = new String(
                                iCrypt.decode(respnoseJson.getString("Result").getBytes()));
                        DDLog.i(DDTag.NETWORK, "convert1: " + resultStr);
                        try {
                            JSONObject jsonObject = new JSONObject(resultStr);
                            String isStringValue = HttpHelper.checkIsOnlyGMSTimeAndData(jsonObject);
                            DDLog.i(DDTag.NETWORK, "convert2: " + isStringValue);
                            logJson(DDTag.NETWORK, isStringValue, "");
                            if (null == isStringValue) {
                                respnoseJson.put("Result", jsonObject);
                            } else {
//                                只有一个GMTIME字段,去除不要
                                if (HttpHelper.checkIsOnlyGMSTime(jsonObject)) {
                                    respnoseJson.put("Result", "");
                                } else if (HttpHelper.checkIsJsonObject(isStringValue)) {
                                    respnoseJson.put("Result", new JSONObject(isStringValue));
                                } else if (HttpHelper.checkIsJsonArray(isStringValue)) {
                                    respnoseJson.put("Result", new JSONArray(isStringValue));
                                } else {
                                    respnoseJson.put("Result", isStringValue);
                                }
                            }
                        } catch (Exception ex) {
                            try {
                                JSONArray jsonArray = new JSONArray(resultStr);
                                respnoseJson.put("Result", jsonArray);
                            } catch (Exception exs) {
                                if (resultStr.toString().length() > 1 && resultStr.startsWith("\"")) {
//                                    这个是string类型，但服务器是把双引号加上一起加密，到时这里需要去除双引号
                                    respnoseJson.put("Result", resultStr.toString().substring(1, resultStr.toString().length() - 1));
                                } else {
//                                    不处理解密后的结果，直接返回，只要和变量类型对应上，则可以正常转换；
                                    respnoseJson.put("Result", resultStr.toString());
                                }
                            }
                        }
                    } else {
//                        不需要解密，直接返回
                    }
                } else if (respnoseJson.getInt("Status") == -12) {
                    DDLog.e(DDTag.NETWORK, respnoseJson.getInt("Status") + ":" + respnoseJson.getString("ErrorMessage"));
//                    转移到interceptor去处理，
//                    EventBus.getDefault().post(new LogoutEvent());
//                    在添加主机的时候,有可能出现-12
                    throw new NetWorkException(respnoseJson.getInt("Status"), respnoseJson.getString("ErrorMessage"));
                } else {
                    DDLog.e(DDTag.NETWORK, respnoseJson.getInt("Status") + ":" + respnoseJson.getString("ErrorMessage"));
                    throw new NetWorkException(respnoseJson.getInt("Status"), respnoseJson.getString("ErrorMessage"));
                }
            } catch (JSONException e) {
                e.printStackTrace();
                DDLog.e(DDTag.NETWORK, "exceptions:" + e.getMessage());
            }
            if (respnoseJson == null) {
                DDLog.e(DDTag.NETWORK, "Result decode onFail");
                throw new IOException("Result decode onFail");
            } else {
                MediaType contentType = value.contentType();
                Charset charset = contentType != null ? contentType.charset(UTF_8) : UTF_8;
                InputStream inputStream = new ByteArrayInputStream(respnoseJson.toString().getBytes());
                Reader reader = new InputStreamReader(inputStream, charset);
                DDLog.i(DDTag.NETWORK, respnoseJson.toString());
                return adapter.fromJson(reader);
            }
        } finally {
            value.close();
        }
    }


    private boolean checkResult(String string) {
        if (!TextUtils.isEmpty(string) && !"null".equals(string.toLowerCase())) {
            return true;
        }
        return false;
    }

    private final String LINE_SEPARATOR = System.getProperty("line.separator");

    public void logJson(String tag, String msg, String headString) {
        String message;
        try {
            if (msg.startsWith("{")) {
                JSONObject jsonObject = new JSONObject(msg);
                message = jsonObject.toString(4);//最重要的方法，就一行，返回格式化的json字符串，其中的数字4是缩进字符数
            } else if (msg.startsWith("[")) {
                JSONArray jsonArray = new JSONArray(msg);
                message = jsonArray.toString(4);
            } else {
                message = msg;
            }
        } catch (JSONException e) {
            message = msg;
        }
        message = headString + LINE_SEPARATOR + message;
        DDLog.i(tag, "************开始打印json************");
        String[] lines = message.split(LINE_SEPARATOR);
        for (String line : lines) {
            DDLog.i(tag, "* " + line);
        }
        DDLog.i(tag, "************结束打印json************");
    }
}
