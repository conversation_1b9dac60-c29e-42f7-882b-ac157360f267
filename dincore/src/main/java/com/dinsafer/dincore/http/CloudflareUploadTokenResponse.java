package com.dinsafer.dincore.http;

import androidx.annotation.Keep;

import java.io.Serializable;

/**
 * Cloudflare上传token响应
 * 
 * <AUTHOR> Assistant
 * @since 2025/7/16
 */
@Keep
public class CloudflareUploadTokenResponse extends BaseHttpEntry implements Serializable {
    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        Result = result;
    }

    @Keep
    public static class ResultBean implements Serializable {
        private String token;
        private String access;
        private String host;
        private int expires_in;
        private String key;

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public int getExpires_in() {
            return expires_in;
        }

        public void setExpires_in(int expires_in) {
            this.expires_in = expires_in;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public String getAccess() {
            return access;
        }

        public void setAccess(String access) {
            this.access = access;
        }
    }
}
