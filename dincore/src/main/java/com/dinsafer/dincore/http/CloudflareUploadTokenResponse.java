package com.dinsafer.dincore.http;

import androidx.annotation.Keep;

import java.io.Serializable;

/**
 * Cloudflare上传token响应
 * 
 * <AUTHOR> Assistant
 * @since 2025/7/16
 */
@Keep
public class CloudflareUploadTokenResponse extends BaseHttpEntry implements Serializable {
    private ResultBean Result;

    public ResultBean getResult() {
        return Result;
    }

    public void setResult(ResultBean result) {
        Result = result;
    }

    @Keep
    public static class ResultBean implements Serializable {
        private String token;
        private String access;

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public String getAccess() {
            return access;
        }

        public void setAccess(String access) {
            this.access = access;
        }
    }
}
