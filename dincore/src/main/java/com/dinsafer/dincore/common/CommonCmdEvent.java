package com.dinsafer.dincore.common;

import androidx.annotation.Keep;

@Keep
public class CommonCmdEvent {

    String cmd;

    String extra;

    public CommonCmdEvent(String cmd) {
        this.cmd = cmd;
    }

    public String getCmd() {
        return cmd;
    }

    public void setCmd(String cmd) {
        this.cmd = cmd;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    @Keep
    public static final class CMD {
        /**
         * 删除离线主机
         */
        public static final String DELETE_OFFLINE_PANEL = "DELETE_OFFLINE_PANEL";

        /**
         * 添加主机
         */
        public static final String DEVICE_ADD = "DeviceAdded";

        /**
         * 成功获取家庭信息
         */
        public static final String GET_HOME_INFO = "GET_HOME_INFO";

        /**
         * 成功退出登录
         */
        public static final String LOGOUT_SUCCESS = "LOGOUT_SUCCESS";
        /**
         * 成功登录
         */
        public static final String LOGIN_SUCCESS = "LOGIN_SUCCESS";

        /**
         * 用户已在其他APP删除ipc
         */
        public static final String DSCAM_ADD = "new-ipc";
        public static final String DSCAM_DELETE = "del-ipc";
        public static final String DSCAM_RENAME = "rename-ipc";

        /**
         * 用户已在其他APP删除门铃
         */
        public static final String DSDOORBELL_ADD = "new-door-bell";
        public static final String DSDOORBELL_DELETE = "del-door-bell";
        public static final String DSDOORBELL_RENAME = "rename-door-bell";

        /**
         * 需要通信的远程服务器地址变更-切换服务器节点后
         */
        public static final String REMOTE_ADDRESS_UPDATED = "REMOTE_ADDRESS_UPDATED";

        /**
         * BMT相关指令
         */
        public static final String BMT_ADD = "new-bmt";
        public static final String BMT_DELETE = "del-bmt";
        public static final String BMT_RENAME = "rename-bmt";

        /**
         * 断开家庭的e2e连接之前
         */
        public static final String ON_BEFORE_HOME_DISCONNECT = "ON_BEFORE_HOME_DISCONNECT";

        /**
         * 切换家庭之前
         */
        public static final String ON_BEFORE_HOME_SWITCH = "ON_BEFORE_HOME_DISCONNECT";

        /**
         *  地区修改通知
         */
        public static final String UPDATE_REGION = "update-region";
    }
}
