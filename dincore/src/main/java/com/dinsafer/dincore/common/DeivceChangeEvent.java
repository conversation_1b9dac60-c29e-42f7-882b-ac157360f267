package com.dinsafer.dincore.common;

import androidx.annotation.Keep;

@Keep
public class DeivceChangeEvent {
    boolean isRemove;

    boolean isAdd;

    Device device;

    public DeivceChangeEvent(Device device) {
        this.device = device;
    }

    public void setRemove(boolean remove) {
        isRemove = remove;
    }

    public void setAdd(boolean add) {
        isAdd = add;
    }

    public boolean isRemove() {
        return isRemove;
    }

    public boolean isAdd() {
        return isAdd;
    }

    public Device getDevice() {
        return device;
    }
}
