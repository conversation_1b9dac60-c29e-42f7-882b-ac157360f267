package com.dinsafer.dincore.common;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class DeviceManager implements IDeviceManager {

    private static final byte[] deviceLock = new byte[0];

    List<Device> mDeviceList = new ArrayList<>();

    List<IDeviceListChangeListener> callback = new ArrayList<>();

    public DeviceManager() {
        EventBus.getDefault().register(this);
    }

    @Override
    public List<Device> getDevices() {
        synchronized (deviceLock) {
            return mDeviceList;
        }
    }

    @Override
    public Device getDevice(String id) {
//        先从自己的缓存里面找，没找到再重各个模块继续找
//         for (Device device : mDeviceList) {
//             if (device.getId().equals(id))
//                 return device;
//         }
        for (IService iService : PluginServiceManager.getInstance().getServiceList().values()) {
            if (iService.getDevice(id) != null) {
                return iService.getDevice(id);
            }
        }

        return null;
    }

    @Override
    public Device getDevice(String id, String sub) {
        // bmt 可能出现相同id，sub不同的情况
        for (IService iService : PluginServiceManager.getInstance().getServiceList().values()) {
            if (iService.getDevice(id, sub) != null) {
                return iService.getDevice(id, sub);
            }
        }
        return null;
    }

    @Override
    public void config(Map<String, Object> arg) {
        PluginServiceManager.getInstance().config(arg);
    }

    @Override
    public List<Device> fetchDevice() {
        List<Device> tempDevice = new ArrayList<>();
        for (IService iService : PluginServiceManager.getInstance().getServiceList().values()) {
            tempDevice.addAll(iService.fetchDevices());
        }
        synchronized (deviceLock) {
            mDeviceList.clear();
            mDeviceList.addAll(tempDevice);
        }
        return mDeviceList;
    }

    @Override
    public List<Device> getDeviceByType(String sub) {
        List<Device> deviceList;
        for (IService iService : PluginServiceManager.getInstance().getServiceList().values()) {
            deviceList = iService.getDeviceByType(sub);
            if (deviceList != null) {
                return deviceList;
            }
        }

        return null;
    }

    @Override
    public List<Device> getDeviceByType(String sub, boolean cacheFirst) {
        List<Device> deviceList;
        for (IService iService : PluginServiceManager.getInstance().getServiceList().values()) {
            deviceList = iService.getDeviceByType(sub, cacheFirst);
            if (deviceList != null) {
                return deviceList;
            }
        }

        return null;
    }

    @Nullable
    @Override
    public List<Device> getCacheDeviceByType(String sub) {
        List<Device> deviceList;
        for (IService iService : PluginServiceManager.getInstance().getServiceList().values()) {
            deviceList = iService.getCacheDeviceByType(sub);
            if (deviceList != null) {
                return deviceList;
            }
        }
        return null;
    }

    @Nullable
    @Override
    public List<Device> getLocalAndNewDeviceByType(String sub) {
        List<Device> deviceList;
        for (IService iService : PluginServiceManager.getInstance().getServiceList().values()) {
            deviceList = iService.getLocalAndNewDeviceByType(sub);
            if (deviceList != null) {
                return deviceList;
            }
        }
        return null;
    }

    @Nullable
    @Override
    public List<Device> getAllDeviceByType(String sub) {
        List<Device> deviceList;
        for (IService iService : PluginServiceManager.getInstance().getServiceList().values()) {
            deviceList = iService.getAllDeviceByType(sub);
            if (deviceList != null) {
                return deviceList;
            }
        }
        return null;
    }

    @Override
    public void removeDeviceCacheById(String id) {
        for (IService iService : PluginServiceManager.getInstance().getServiceList().values()) {
            iService.removeDeviceCacheById(id);
        }
    }

    @Override
    public void removeDeviceCacheByIdAndSub(String id, String sub) {
        for (IService iService : PluginServiceManager.getInstance().getServiceList().values()) {
            iService.removeDeviceCacheByIdAndSub(id, sub);
        }
    }

    @Override
    public void removeDeviceCacheByType(String sub) {

    }

    @Override
    public boolean releaseDeviceByType(String sub) {
        boolean release = false;
        for (IService iService : PluginServiceManager.getInstance().getServiceList().values()) {
            release = iService.releaseDeviceByType(sub);
            if (release) {
                break;
            }
        }
        return release;
    }

    @Override
    public void registerDeviceListChangeListener(IDeviceListChangeListener listChangeListener) {
        if (callback.contains(listChangeListener)) {
            return;
        }

        callback.add(listChangeListener);
    }

    @Override
    public void unRegisterDeviceListChangeListener(IDeviceListChangeListener listChangeListener) {
        callback.remove(listChangeListener);
    }

    @Subscribe
    public void onEvent(DeivceChangeEvent event) {
        if (event.isAdd) {
            synchronized (deviceLock) {
                mDeviceList.add(event.getDevice());
            }
            for (int i = callback.size() - 1; i >= 0; i--) {
                callback.get(i).onDeviceAdd(event.getDevice());
            }
        } else if (event.isRemove) {
            for (int i = callback.size() - 1; i >= 0; i--) {
                callback.get(i).onDeviceRemove(event.getDevice());
            }
            synchronized (deviceLock) {
                mDeviceList.remove(event.getDevice());
            }
        }
    }

    @Override
    public BasePluginBinder createPluginBinder(Context context, String type) {
        if (TextUtils.isEmpty(type)) {
            return null;
        }
        BasePluginBinder binder;
        for (IService iService : PluginServiceManager.getInstance().getServiceList().values()) {
            binder = iService.createPluginBinder(context, type);
            if (binder != null) {
                return binder;
            }
        }
        return null;
    }

    @Override
    public void release() {
        EventBus.getDefault().unregister(this);
        PluginServiceManager.getInstance().release();
        callback.clear();
    }

    @Override
    public Device acquireTemporaryDevices(@NonNull String sub, String id, String model) {
        IService service = PluginServiceManager.getInstance().getServiceList().get(sub);
        if (null != service) {
            return service.acquireTemporaryDevices(id, model);
        }
        return null;
    }
}

