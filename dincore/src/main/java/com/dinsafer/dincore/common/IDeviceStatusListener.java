package com.dinsafer.dincore.common;

import androidx.annotation.Keep;

@Keep
public interface IDeviceStatusListener {
    int FLAG_ALL = 0; // 不区别Flag的更新
    int FLAG_CACHE = 1; // 数据从缓存更新为网络返回的数据
    int FLAG_DELETED = 2; // 是否已经被删除
    int FLAG_LOADED = 3; // loaded状态改变
    int FLAG_LOADING = 4; // loading状态改变

    void online(String id, String subCategory);

    void offline(String id, String subCategory, String msg);

    default void onInfoUpdate(String id, String subCategory, int type) {
    }
}
