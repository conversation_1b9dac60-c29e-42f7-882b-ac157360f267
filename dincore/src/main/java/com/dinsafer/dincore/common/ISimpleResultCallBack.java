package com.dinsafer.dincore.common;

import com.dinsafer.dssupport.msctlib.msct.IMsg;
import com.dinsafer.dssupport.msctlib.queue.IRequestCallBack;

public abstract class ISimpleResultCallBack implements IRequestCallBack {

    @Override
    public void onAckEvent(IMsg iMsg) {

    }

    @Override
    public void onStart() {

    }

    @Override
    public void onFinish() {

    }

    @Override
    public void onReStart() {

    }

    @Override
    public void onFail() {
        onError(ErrorCode.DEFAULT, "fail");
    }

    @Override
    public void onTimeOut() {
        onError(ErrorCode.DEFAULT, "timeout");
    }

    public abstract void onError(int code, String error);
}
