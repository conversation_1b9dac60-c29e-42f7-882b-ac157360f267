package com.dinsafer.dincore.common;

import androidx.annotation.Keep;

@Keep
public class ErrorCode {
    public static final int DEFAULT = -1;

    public static final int ACTIVTOR_ALREAD_HAS_PLUGIN = -101;

    public static final int ACTIVTOR_ILLEGAID = -102;

    public static final int ACTIVTOR_BIND_DEVICE_PLUGIN_NULL = -103;

    public static final int ACTIVTOR_BIND_DEVICE_ACTIVTOR_NULL = -104;

    public static final int ACTIVTOR_BIND_DEVICE_FAIL = -105;

    public static final int ACTIVTOR_BIND_IPC_REACHED_LIMIT = -416;


    public static final int USER_NO_LOGIN = -200;

    /**
     * 请求参数错误
     */
    public static final int PARAM_ERROR = -4001;

    public static final int SAME_USER_NAME = -22;

    public static final int ERROR_EMAIL_EXIST = -30;

    /**
     * 忘记密码-设置新密码-验证码错误
     */
    public static final int ERROR_VERIFY_CODE = -52;

    public static final int ERROR_SHARE_CODE_EXPIRED = -26;
    public static final int ERROR_SHARE_CODE_BEEN_USED = -27;
    public static final int ERROR_SHARE_CODE_BEEN_BIND = -28;

    public static final int DEVICE_OFFLINE = -24;
    public static final int USER_LOGOUT = -12;
    public static final int DEVICE_NOT_BELONE_USER = -25;
    public static final int ERROR_SQLERROR = -97;


    //    MSCT Start
    public static final int MEMBER_EXSPIRED = 21110;
//    MSCT End


    //    DSCAM Start
//    这个是用于发送connect指令回调的错误码
    public static final int ERROR_CONNECT = 8001;

    //    这个是ipc状态更改的错误码
    public static final int ERROR_CONNECT_STATUS = 8002;
//    DSCAM End

    // 账号或密码错误
    public static final int ERROR_USERNAME_OR_PWD = -18;

    // mail不合法
    public static final int ERROR_MAIL_ILLEGAL = -10;

    // phone不合法
    public static final int ERROR_PHONE_ILLEGAL = -9;

    // 密码不合法
    public static final int ERROR_PWD_ILLEGAL = -51;

    // 密码包含用户id
    public static final int ERROR_PWD_CONTAINS_UID = -53;

    /**
     * 未支持功能
     */
    public static final int ERROR_UNSUPPORTED_API = -404;
}
