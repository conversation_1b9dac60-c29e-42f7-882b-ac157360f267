package com.dinsafer.dincore.common;

import java.util.HashMap;
import java.util.Map;

import androidx.annotation.Keep;

@Keep
public abstract class Cmd {
    protected static final int SUCCESS = 1;
    protected static final int FAIL = 0;
    protected static final int TIMEOUT = -2;
    protected static final int OFFLINE_FAIL = -999;
    /**
     * 设置care mode 内容
     */
    public static final String SET_CAREMODE_DATA = "SET_CAREMODE_DATA";

    /**
     * 设置care mode配件
     */
    public static final String SET_CAREMODE_PLUGIN = "SET_CAREMODE_PLUGIN";

    public static final String ADD_NEWASKPLUGIN = "ADD_NEWASKPLUGIN";

    public static final String ADD_PLUGIN = "ADD_PLUGIN";
    public static final String SET_NAME = "set_name";
    public static final String DELETE_DEVICE = "delete_device";
    public static final String UPDATE_TUYA_DEVICE_NAME = "UPDATE_TUYA_DEVICE_NAME";

    public static final String COMMON_CMD_DISCONNECT_HOME = "disconnect_home";


    //    	"cmd": "tuya_set_name",		// [必填, String]
//                "owner": true,    			// [选填, Bool] - 是否是自己操作的结果，如果不是，就是第三方通知结果
//                "status": 1,      			// [必填, int] - 1:成功, 0:失败
//                "errorMessage": "",			// [选填, String] - 错误描述
//                "result": {},
    public static Map getDefaultResultMap(boolean isSuccess, String cmd) {
        Map result = new HashMap();
        result.put("status", isSuccess ? SUCCESS : FAIL);
        result.put("errorMessage", "");
        result.put("result", new HashMap<>());
        result.put("cmd", cmd);
        return result;
    }

    public static Map getTimeoutResultMap(String cmd) {
        Map result = new HashMap();
        result.put("status", TIMEOUT);
        result.put("errorMessage", "timeout");
        result.put("result", new HashMap<>());
        result.put("cmd", cmd);
        return result;
    }

    public static Map getOfflineFailResultMap(String cmd) {
        Map result = new HashMap();
        result.put("status", OFFLINE_FAIL);
        result.put("errorMessage", "Offline");
        result.put("result", new HashMap<>());
        result.put("cmd", cmd);
        return result;
    }

    public static boolean putResultValue(Map map, Object key, Object value) {
        if (map == null) {
            return false;
        }
        if (!map.containsKey("result") || map.get("result") == null) {
            map.put("result", new HashMap<>());
        }
        ((Map) map.get("result")).put(key, value);
        return true;
    }


}
