package com.dinsafer.dincore.common;


import androidx.annotation.Keep;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

//设备控制的基类
@Keep
public abstract class Device {

    private static final int FLAG_CACHE = 0x1; // 是否是缓存 1->表示是缓存
    private static final int FLAG_DELETED = 0X1 << 1; // 是否已经删除 1->表示已经删除
    private static final int FLAG_LOADED = 0x1 << 2;// 是否已经load配件的额外数据 1->表示已经加载
    private static final int FLAG_LOADING_INFO = 0x1 << 3;// 是否正在load配件的额外数据 1->表示正在加载

    private String id;

    private int category;

    private String subCategory;
    private Map info = new ConcurrentHashMap();
    private String fatherId = "";
    private List<IDeviceCallBack> iDeviceCallBack = new ArrayList<>();
    private List<IDeviceStatusListener> iDeviceStatusListener = new ArrayList<>();
    public static final String STATE = "state";
    private final byte[] deviceCallBackLock = new byte[0];
    private final byte[] deviceStatusCallBackLock = new byte[0];

    // 标记信息
    protected int flags = 0x0;
    private final byte[] flagLock = new byte[0];

    public Device() {
    }

    public Device(String id, int category, String subCategory, Map<String, Object> info) {
        this.id = id;
        this.category = category;
        this.subCategory = subCategory;
        this.info = info;
    }

    public Device(String id, int category, String subCategory, Map<String, Object> info, String fatherId) {
        this.id = id;
        this.category = category;
        this.subCategory = subCategory;
        this.info = info;
        this.fatherId = fatherId;
    }

    public abstract void submit(Map arg);

    protected void remove() {
        DeivceChangeEvent event = new DeivceChangeEvent(this);
        event.setRemove(true);
        EventBus.getDefault().post(event);
    }

    public String getId() {
        return id;
    }

    protected void setId(String id) {
        this.id = id;
    }

    public int getCategory() {
        return category;
    }

    protected void setCategory(int category) {
        this.category = category;
    }

    public String getSubCategory() {
        return subCategory;
    }

    protected void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }

    public Map getInfo() {
        return info;
    }

    protected void setInfo(Map info) {
        this.info = info;
    }

    public String getFatherId() {
        return fatherId;
    }

    protected void dispatchResult(String cmd, Map result) {
        synchronized (deviceCallBackLock) {
            for (IDeviceCallBack deviceCallBack : iDeviceCallBack) {
                deviceCallBack.onCmdCallBack(id, subCategory, cmd, result);
            }
        }
    }

    protected void dispatchOnline() {
        synchronized (deviceStatusCallBackLock) {
            for (IDeviceStatusListener deviceStatusListener : iDeviceStatusListener) {
                deviceStatusListener.online(id, subCategory);
            }
        }
    }

    protected void dispatchOffline(String errorMsg) {
        synchronized (deviceStatusCallBackLock) {
            for (IDeviceStatusListener deviceStatusListener : iDeviceStatusListener) {
                deviceStatusListener.offline(id, subCategory, errorMsg);
            }
        }
    }


    public void registerDeviceCallBack(IDeviceCallBack iDeviceCallBack) {
        synchronized (deviceCallBackLock) {
            if (!this.iDeviceCallBack.contains(iDeviceCallBack)) {
                this.iDeviceCallBack.add(iDeviceCallBack);
            }
        }
    }

    public void registerDeviceStatusListener(IDeviceStatusListener iDeviceStatusListener) {
        synchronized (deviceStatusCallBackLock) {
            if (!this.iDeviceStatusListener.contains(iDeviceStatusListener)) {
                this.iDeviceStatusListener.add(iDeviceStatusListener);
            }
        }
    }

    public void unregisterDeviceCallBack(IDeviceCallBack iDeviceCallBack) {
        synchronized (deviceCallBackLock) {
            this.iDeviceCallBack.remove(iDeviceCallBack);
        }
    }

    public void unregisterDeviceStatusListener(IDeviceStatusListener iDeviceStatusListener) {
        synchronized (deviceStatusCallBackLock) {
            this.iDeviceStatusListener.remove(iDeviceStatusListener);
        }
    }

    public void destory() {
        synchronized (deviceCallBackLock) {
            this.iDeviceCallBack.clear();
        }
        synchronized (deviceStatusCallBackLock) {
            this.iDeviceStatusListener.clear();
        }
    }

    protected void setFlagCache(final boolean isCache) {
        final boolean changed;
        synchronized (flagLock) {
            boolean current = (flags & FLAG_CACHE) != 0;
            changed = current != isCache;
            if (isCache) {
                flags |= FLAG_CACHE;
            } else {
                flags &= (~FLAG_CACHE);
            }
        }
        if (changed) {
            synchronized (deviceStatusCallBackLock) {
                for (IDeviceStatusListener deviceStatusListener : iDeviceStatusListener) {
                    deviceStatusListener.onInfoUpdate(id, subCategory, IDeviceStatusListener.FLAG_CACHE);
                }
            }
        }
    }

    public boolean getFlagCache() {
        synchronized (flagLock) {
            return (flags & FLAG_CACHE) != 0;
        }
    }

    protected void setFlagDeleted(final boolean isDeleted) {
        final boolean changed;
        synchronized (flagLock) {
            boolean current = (flags & FLAG_DELETED) != 0;
            changed = current != isDeleted;
            if (isDeleted) {
                flags |= FLAG_DELETED;
            } else {
                flags &= (~FLAG_DELETED);
            }
        }

        if (changed) {
            synchronized (deviceStatusCallBackLock) {
                for (IDeviceStatusListener deviceStatusListener : iDeviceStatusListener) {
                    deviceStatusListener.onInfoUpdate(id, subCategory, IDeviceStatusListener.FLAG_DELETED);
                }
            }
        }
    }

    public boolean getFlagDeleted() {
        synchronized (flagLock) {
            return (flags & FLAG_DELETED) != 0;
        }
    }

    protected void setFlagLoaded(final boolean isLoaded) {
        final boolean changed;
        synchronized (flagLock) {
            boolean current = (flags & FLAG_LOADED) != 0;
            changed = current != isLoaded;
            if (isLoaded) {
                flags |= FLAG_LOADED;
                flags &= (~FLAG_LOADING_INFO);
            } else {
                flags &= (~FLAG_LOADED);
            }
        }

        if (changed) {
            synchronized (deviceStatusCallBackLock) {
                for (IDeviceStatusListener deviceStatusListener : iDeviceStatusListener) {
                    deviceStatusListener.onInfoUpdate(id, subCategory, IDeviceStatusListener.FLAG_LOADED);
                }
            }
        }
    }

    protected void setFlagLoading(final boolean isLoading) {
        final boolean changed;
        synchronized (flagLock) {
            boolean current = (flags & FLAG_LOADING_INFO) != 0;
            changed = current != isLoading;
            if (isLoading) {
                flags |= FLAG_LOADING_INFO;
                flags &= (~FLAG_LOADING_INFO);
            } else {
                flags &= (~FLAG_LOADING_INFO);
            }
        }

        if (changed) {
            synchronized (deviceStatusCallBackLock) {
                for (IDeviceStatusListener deviceStatusListener : iDeviceStatusListener) {
                    deviceStatusListener.onInfoUpdate(id, subCategory, IDeviceStatusListener.FLAG_LOADING);
                }
            }
        }
    }

    public boolean getFlagLoaded() {
        synchronized (flagLock) {
            return (flags & FLAG_LOADED) != 0;
        }
    }

    public boolean getFlagLoading() {
        synchronized (flagLock) {
            return (flags & FLAG_LOADING_INFO) != 0;
        }
    }

    /**
     * 初始化flag为缓存、非移除、非loaded
     */
    protected void initFlagOnReadCache() {
        synchronized (flagLock) {
            flags |= FLAG_CACHE;
            flags &= (~FLAG_DELETED);
            flags &= (~FLAG_LOADING_INFO);
            flags &= (~FLAG_LOADED);
        }
        synchronized (deviceStatusCallBackLock) {
            for (IDeviceStatusListener deviceStatusListener : iDeviceStatusListener) {
                deviceStatusListener.onInfoUpdate(id, subCategory, IDeviceStatusListener.FLAG_ALL);
            }
        }
    }

    /**
     * 初始化flag为非缓存、非移除、loaded
     */
    protected void initFlagOnNetwork() {
        synchronized (flagLock) {
            flags &= (~FLAG_CACHE);
            flags &= (~FLAG_DELETED);
            flags &= (~FLAG_LOADING_INFO);
            flags |= FLAG_LOADED;
        }

        synchronized (deviceStatusCallBackLock) {
            for (IDeviceStatusListener deviceStatusListener : iDeviceStatusListener) {
                deviceStatusListener.onInfoUpdate(id, subCategory, IDeviceStatusListener.FLAG_ALL);
            }
        }
    }

    @Override
    public String toString() {
        return "Device{" +
                "id='" + id + '\'' +
                ", category=" + category +
                ", subCategory='" + subCategory + '\'' +
                ", fatherId='" + fatherId + '\'' +
                '}';
    }
}
