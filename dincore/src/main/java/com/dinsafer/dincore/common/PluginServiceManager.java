package com.dinsafer.dincore.common;

import java.util.HashMap;
import java.util.Map;

public class PluginServiceManager {

    private static final byte[] sInstanceLock = new byte[0];

    private static PluginServiceManager sInstance;

    private PluginServiceManager() {

    }

    private Map<String, IService> mServiceList = new HashMap<>();

    public static PluginServiceManager getInstance() {
        if (sInstance == null) {
            synchronized (sInstanceLock) {
                if (sInstance == null) {
                    sInstance = new PluginServiceManager();
                }
            }
        }
        return sInstance;
    }

    public void register(IService service) {
        if (mServiceList.containsKey(service)) {
            return;
        }
        mServiceList.put(service.getClass().getSimpleName(), service);
        service.load();
    }

    public void config(Map<String, Object> arg) {
        for (IService value : mServiceList.values()) {
            value.config(arg);
        }
    }

    public void unRegister(IService service) {
        mServiceList.remove(service);
        service.unLoad();
    }

    public void release() {
        for (IService value : mServiceList.values()) {
            value.unLoad();
        }
        mServiceList.clear();
    }

    public Map<String, IService> getServiceList() {
        return mServiceList;
    }
}
