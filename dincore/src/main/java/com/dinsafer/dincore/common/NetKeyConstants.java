package com.dinsafer.dincore.common;

import androidx.annotation.Keep;

/**
 * 服务器返回的数据字段名
 * <p>
 * 服务器返回有_的，在命名时统一使用两个_代替
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2020/12/29 8:58 PM
 */
@Keep
public class NetKeyConstants {
    public static final String NET_KEY_ID = "id";
    public static final String NET_KEY_PLUGIN_ID = "pluginid";
    public static final String NET_KEY_PLUGIN__ID = "plugin_id";
    public static final String NET_KEY_DECODE__ID = "decode_id";
    public static final String NET_KEY_DECODE_ID = "decodeid";
    public static final String NET_KEY_NAME = "name";
    public static final String NET_KEY_PLUGIN_NAME = "pluginname";
    public static final String NET_KEY_SEND_ID = "sendid";
    public static final String NET_KEY_D_TYPE = "dtype";
    public static final String NET_KEY_S_TYPE = "stype";
    public static final String NET_KEY_CATEGORY = "category";
    public static final String NET_KEY_SUB__CATEGORY = "sub_category";
    public static final String NET_KEY_ENABLE = "enable";
    public static final String NET_KEY_PLUGIN__ITEM__SMART__PLUG__ENABLE = "plugin_item_smart_plug_enable";
    public static final String NET_KEY_KEEP_LIVE = "keeplive";
    public static final String NET_KEY_BLOCK = "block";
    public static final String NET_KEY_POWER = "power";
    public static final String NET_KEY_TIME = "time";
    public static final String NET_KEY_PLUGIN__INFO = "plugin_info";
    public static final String NET_KEY_SIGNAL_REPEATER__PLUG = "signal_repeater_plug";
    public static final String NET_KEY_SMART__PLUG = "smart_plug";
    public static final String NET_KEY_DOOR__WINDOW = "door_window";
    public static final String NET_KEY_VIBRATION = "vibration";
    public static final String NET_KEY_RESULT = "Result";
    public static final String NET_KEY_NEW_ASK_DATAS = "newaskdatas";
    public static final String NET_KEY_BATTERY__LEVEL = "battery_level";
    public static final String NET_KEY_RSSI = "rssi";
    public static final String NET_KEY_TAMPER = "tamper";
    public static final String NET_KEY_CHARGING = "charging";
    public static final String NET_KEY_DATAS = "datas";
    public static final String NET_KEY_CARE__MODE = "care_mode";
    public static final String NET_KEY_ALARM__DELAY__TIME = "alarm_delay_time";
    public static final String NET_KEY_PIR = "pir";
    public static final String NET_KEY_NO__ACTION__TIME = "no_action_time";
    public static final String NET_KEY_SCENARY = "scenary";
    public static final String NET_KEY_ACTION = "action";
    public static final String NET_KEY_CMD = "cmd";
    public static final String NET_KEY_VOLUME = "volume";
    public static final String NET_KEY_MUSIC = "music";
    public static final String NET_KEY_ACTION__CONF = "action_conf";
}
