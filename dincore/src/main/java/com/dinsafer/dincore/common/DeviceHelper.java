package com.dinsafer.dincore.common;

import androidx.annotation.Keep;
import android.text.TextUtils;

import com.dinsafer.dssupport.utils.DDLog;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Device数据获取工具类
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/25 5:31 PM
 */
@Keep
public class DeviceHelper {
    private static final String TAG = DeviceHelper.class.getSimpleName();

    @Keep
    public static int getInt(Device device, String key, int defaultValue) {
        if (TextUtils.isEmpty(key)
                || null == device
                || null == device.getInfo()
                || !device.getInfo().containsKey(key)) {
            return defaultValue;
        }

        try {
            return (int) device.getInfo().get(key);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on get value by key: " + key);
            e.printStackTrace();
        }
        return defaultValue;
    }

    @Keep
    public static boolean getBoolean(Device device, String key, boolean defaultValue) {
        if (TextUtils.isEmpty(key)
                || null == device
                || null == device.getInfo()
                || !device.getInfo().containsKey(key)) {
            return defaultValue;
        }

        try {
            return (Boolean) device.getInfo().get(key);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on get value by key: " + key);
            e.printStackTrace();
        }
        return defaultValue;
    }

    @Keep
    public static long getLong(Device device, String key, long defaultValue) {
        if (TextUtils.isEmpty(key)
                || null == device
                || null == device.getInfo()
                || !device.getInfo().containsKey(key)) {
            return defaultValue;
        }

        try {
            return (Long) device.getInfo().get(key);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on get value by key: " + key);
            e.printStackTrace();
        }
        return defaultValue;
    }

    @Keep
    public static float getFloat(Device device, String key, float defaultValue) {
        if (TextUtils.isEmpty(key)
                || null == device
                || null == device.getInfo()
                || !device.getInfo().containsKey(key)) {
            return defaultValue;
        }

        try {
            return (Float) device.getInfo().get(key);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on get value by key: " + key);
            e.printStackTrace();
        }
        return defaultValue;
    }


    @Keep
    public static double getDouble(Device device, String key, double defaultValue) {
        if (TextUtils.isEmpty(key)
                || null == device
                || null == device.getInfo()
                || !device.getInfo().containsKey(key)) {
            return defaultValue;
        }

        try {
            return (Double) device.getInfo().get(key);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on get value by key: " + key);
            e.printStackTrace();
        }
        return defaultValue;
    }

    @Keep
    public static String getString(Device device, String key, String defaultValue) {
        if (TextUtils.isEmpty(key)
                || null == device
                || null == device.getInfo()
                || !device.getInfo().containsKey(key)) {
            return defaultValue;
        }

        try {
            return (String) device.getInfo().get(key);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on get value by key: " + key);
            e.printStackTrace();
        }
        return defaultValue;
    }

    @Keep
    public static <T> List<T> getList(Device device, String key) {
        if (TextUtils.isEmpty(key)
                || null == device
                || null == device.getInfo()
                || !device.getInfo().containsKey(key)) {
            return new ArrayList<>();
        }

        try {
            return (List<T>) device.getInfo().get(key);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on get value by key: " + key);
            e.printStackTrace();
        }
        return new ArrayList<>();
    }

    @Keep
    public static <K, V> Map<K, V> getMap(Device device, String key) {
        if (TextUtils.isEmpty(key)
                || null == device
                || null == device.getInfo()
                || !device.getInfo().containsKey(key)) {
            return new HashMap<>();
        }

        try {
            return (Map<K, V>) device.getInfo().get(key);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on get value by key: " + key);
            e.printStackTrace();
        }
        return new HashMap<K, V>();
    }

    @Keep
    public static JSONObject getJsonObject(Device device, String key) {
        if (TextUtils.isEmpty(key)
                || null == device
                || null == device.getInfo()
                || !device.getInfo().containsKey(key)) {
            return null;
        }

        try {
            return (JSONObject) device.getInfo().get(key);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on get value by key: " + key);
            e.printStackTrace();
        }
        return null;
    }

    @Keep
    public static int getInt(Map map, String key, int defaultValue) {
        if (TextUtils.isEmpty(key)
                || null == map
                || !map.containsKey(key)) {
            return defaultValue;
        }

        try {
            return (int) map.get(key);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on get value by key: " + key);
            e.printStackTrace();
        }
        return defaultValue;
    }

    @Keep
    public static boolean getBoolean(Map map, String key, boolean defaultValue) {
        if (TextUtils.isEmpty(key)
                || null == map
                || !map.containsKey(key)) {
            return defaultValue;
        }

        try {
            return (Boolean) map.get(key);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on get value by key: " + key);
            e.printStackTrace();
        }
        return defaultValue;
    }

    @Keep
    public static long getLong(Map map, String key, long defaultValue) {
        if (TextUtils.isEmpty(key)
                || null == map
                || !map.containsKey(key)) {
            return defaultValue;
        }

        try {
            return (Long) map.get(key);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on get value by key: " + key);
            e.printStackTrace();
        }
        return defaultValue;
    }

    @Keep
    public static float getFloat(Map map, String key, float defaultValue) {
        if (TextUtils.isEmpty(key)
                || null == map
                || !map.containsKey(key)) {
            return defaultValue;
        }

        try {
            return (Float) map.get(key);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on get value by key: " + key);
            e.printStackTrace();
        }
        return defaultValue;
    }


    @Keep
    public static double getDouble(Map map, String key, double defaultValue) {
        if (TextUtils.isEmpty(key)
                || null == map
                || !map.containsKey(key)) {
            return defaultValue;
        }

        try {
            return (Double) map.get(key);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on get value by key: " + key);
            e.printStackTrace();
        }
        return defaultValue;
    }

    @Keep
    public static String getString(Map map, String key, String defaultValue) {
        if (TextUtils.isEmpty(key)
                || null == map
                || !map.containsKey(key)) {
            return defaultValue;
        }

        try {
            return (String) map.get(key);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on get value by key: " + key);
            e.printStackTrace();
        }
        return defaultValue;
    }

    @Keep
    public static <T> List<T> getList(Map map, String key) {
        if (TextUtils.isEmpty(key)
                || null == map
                || !map.containsKey(key)) {
            return new ArrayList<>();
        }

        try {
            return (List<T>) map.get(key);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on get value by key: " + key);
            e.printStackTrace();
        }
        return new ArrayList<>();
    }

    @Keep
    public static <K, V> Map<K, V> getMap(Map map, String key) {
        if (TextUtils.isEmpty(key)
                || null == map
                || !map.containsKey(key)) {
            return new HashMap<>();
        }

        try {
            return (Map<K, V>) map.get(key);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on get value by key: " + key);
            e.printStackTrace();
        }
        return new HashMap<K, V>();
    }


    @Keep
    public static JSONObject getJsonObject(Map map, String key) {
        if (TextUtils.isEmpty(key)
                || null == map
                || !map.containsKey(key)) {
            return null;
        }

        try {
            return (JSONObject) map.get(key);
        } catch (Exception e) {
            DDLog.e(TAG, "Error on get value by key: " + key);
            e.printStackTrace();
        }
        return null;
    }
}
