package com.dinsafer.dincore.common;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;
import com.dinsafer.dincore.user.api.IResultCallback2;

import java.util.List;
import java.util.Map;

public interface IDeviceManager {
    //    获取内存缓存
    List<Device> getDevices();

    Device getDevice(String id);
    Device getDevice(String id, String sub);

    void config(Map<String, Object> arg);

    //    获取网络
    List<Device> fetchDevice();

    List<Device> getDeviceByType(String sub);

    /**
     * @param cacheFirst 是否先读取缓存-true先读取缓存; false等同于{{@link #getDeviceByType(String)}}
     */
    List<Device> getDeviceByType(String sub, boolean cacheFirst);

    /**
     * 获取缓存的Device
     *
     * @param sub
     * @return 仅返回缓存Device
     */
    @Nullable
    List<Device> getCacheDeviceByType(String sub);

    /**
     * 获取全部device
     *
     * @return 返回全部Device，但请求网络数据的时候是请求缓存时间戳后的device，
     * 如果没有缓存，相当于{{@link #getAllDeviceByType(String)}}
     */
    @Nullable
    List<Device> getLocalAndNewDeviceByType(String sub);

    /**
     * 获取全部Device
     *
     * @return 返回全部Device，但请求网络数据的时候是请求全部Device
     */
    @Nullable
    List<Device> getAllDeviceByType(String sub);

    void removeDeviceCacheById(String id);

    void removeDeviceCacheByIdAndSub(String id, String  sub);

    void removeDeviceCacheByType(String sub);

    boolean releaseDeviceByType(String sub);

    void registerDeviceListChangeListener(IDeviceListChangeListener listChangeListener);

    void unRegisterDeviceListChangeListener(IDeviceListChangeListener listChangeListener);

    BasePluginBinder createPluginBinder(Context context, String type);

    void release();

    /**
     * 获取单个临时设备
     * @param sub 模块名
     * @param id
     * @param model
     * @return
     */
    Device acquireTemporaryDevices(@NonNull String sub, String id, String model);
}
