package com.dinsafer.dincore.common;

import android.content.Context;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dinsafer.dincore.activtor.api.base.impl.BasePluginBinder;

import java.util.List;
import java.util.Map;

@Keep
public interface IService {

    void load();

    void unLoad();

    void config(Map<String, Object> configArg);

    //    不能在主线程调用
    //      必须是同步方法
    List<Device> fetchDevices();

    Device getDevice(String id);

    Device getDevice(String id, String sub);

    //    不能在主线程调用
    //      必须是同步方法
    List<Device> getDeviceByType(String sub);

    /**
     * 不能在主线程调用，必须是同步方法
     *
     * @param cacheFirst 是否先读取缓存-true先读取缓存；false等同于{{@link #getDeviceByType(String)}}
     */
    List<Device> getDeviceByType(String sub, boolean cacheFirst);

    /**
     * 获取缓存的Device
     *
     * @param sub
     * @return 仅返回缓存Device
     */
    @Nullable
    List<Device> getCacheDeviceByType(String sub);

    /**
     * 获取全部device
     *
     * @return 返回全部Device，但请求网络数据的时候是请求缓存时间戳后的device，
     * 如果没有缓存，相当于{{@link #getAllDeviceByType(String)}}
     */
    @Nullable
    List<Device> getLocalAndNewDeviceByType(String sub);

    /**
     * 获取全部Device
     *
     * @return 返回全部Device，但请求网络数据的时候是请求全部Device
     */
    @Nullable
    List<Device> getAllDeviceByType(String sub);

    /**
     * 是否是该服务支持的配件类型
     */
    boolean isSupportedDeviceType(String sub);

    boolean removeDeviceCacheById(String id);

    boolean removeDeviceCacheByIdAndSub(String id, String  sub);

    boolean removeDeviceCacheByType(String sub);

    // 清楚device列表缓存
    boolean releaseDeviceByType(String sub);

    BasePluginBinder createPluginBinder(Context context, @NonNull String type);

    Device acquireTemporaryDevices(String id, String model);
}
