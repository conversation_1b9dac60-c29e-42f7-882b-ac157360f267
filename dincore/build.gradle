plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'org.greenrobot.greendao'
}

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    buildToolsVersion rootProject.ext.android.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode rootProject.ext.android.versionCode
        versionName rootProject.ext.android.versionName

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        consumerProguardFiles "consumer-rules.pro"
        ndk {
            abiFilters "armeabi-v7a", "arm64-v8a"
        }
    }

    packagingOptions {
        pickFirst 'lib/*/libc++_shared.so' // 多个aar存在此so，需要选择第一个
    }

    buildTypes {
        release {
            debuggable rootProject.ext.debuggable
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility rootProject.ext.Java_Version
        targetCompatibility rootProject.ext.Java_Version
    }

    greendao {
        schemaVersion 1 //数据库版本号
        daoPackage 'com.dinsafer.dincore.db.gen'
        // 设置DaoMaster、DaoSession、Dao 包名
        targetGenDir 'src/main/java'//设置DaoMaster、DaoSession、Dao目录,请注意，这里路径用/不要用.
        generateTests false //设置为true以自动生成单元测试。
        targetGenDirTests 'src/main/java' //应存储生成的单元测试的基本目录。默认为 src / androidTest / java。
    }
}

dependencies {

    api 'org.iq80.snappy:snappy:0.4'

    implementation 'com.squareup.okhttp3:okhttp-urlconnection:4.9.0'
    api 'com.squareup.retrofit2:retrofit:2.9.0'
    api("com.squareup.okhttp3:okhttp:4.9.1")
//    api files('libs/libDinsafe.jar')
    //gson
//    api "com.google.code.gson:gson:2.8.5"
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.retrofit2:adapter-rxjava:2.9.0'
    api 'org.greenrobot:eventbus:3.0.0'
    implementation 'com.alibaba:fastjson:1.1.67.android'
    api 'com.aliyun.ams:alicloud-android-httpdns:2.1.1'
//    api files('libs/alicloud-android-sdk-httpdns-1.1.3.jar')
//    api files('libs/alicloud-android-utils-1.1.1.jar')

    implementation 'com.qiniu:qiniu-android-sdk:7.2.3'
    implementation 'androidx.annotation:annotation:1.0.0'
    api "com.dinsafer.support:dssupport:1.0.3${rootProject.ext.dependlibVersionPostfixt}"
    api 'com.dinsafer.support:fastblesupport:1.0.0'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'org.greenrobot:greendao:3.3.0' // add library
}

ext.modulePublishConfig = [
        artifactId: 'dincore',
]

// 使用maven插件上传
apply from: rootProject.file('gradle-scripts/upload.gradle')

// 使用maven-publish插件上传
//apply from: rootProject.file('gradle-scripts/publish.gradle')

