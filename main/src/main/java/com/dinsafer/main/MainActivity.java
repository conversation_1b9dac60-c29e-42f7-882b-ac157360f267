package com.dinsafer.main;

import android.content.Intent;
import androidx.appcompat.app.AppCompatActivity;
import android.os.Bundle;
import android.view.View;

import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dincore.user.api.ILoginCallback;
import com.dinsafer.dincore.user.bean.DinUser;
import com.dinsafer.dssupport.msctlib.MsctLog;
import com.dinsafer.module_dscam.bean.LanDevice;
import com.dinsafer.module_dscam.utils.LanDiscovery;
import com.dinsafer.module_home.DinSDK;

import org.iq80.snappy.Main;

import java.util.List;

public class MainActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        DinSDK.getUserInstance().loginWithUUid("k22", "111111", new ILoginCallback() {
            @Override
            public void onSuccess(DinUser user) {

            }

            @Override
            public void onError(int code, String error) {

            }
        });
        findViewById(R.id.tuya).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivity(new Intent(MainActivity.this, TuyaAcitivty.class));
            }
        });

        findViewById(R.id.user).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivity(new Intent(MainActivity.this, UserTestActivity.class));
            }
        });

        findViewById(R.id.home).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivity(new Intent(MainActivity.this, HomeTestActivity.class));
            }
        });

        MsctLog.initLog(true);
        LanDiscovery discovery = new LanDiscovery();
        discovery.setCallBack(new IDefaultCallBack2<List<LanDevice>>() {
            @Override
            public void onSuccess(List<LanDevice> T) {
                MsctLog.i("devicee", "device:" + T.size());

            }

            @Override
            public void onError(int code, String error) {

            }
        });
        findViewById(R.id.dscam).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                discovery.startDiscoveryDevice(5000);
//                startActivity(new Intent(MainActivity.this, DscamActivity.class));
            }
        });
    }
}