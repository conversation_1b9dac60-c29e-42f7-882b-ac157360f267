package com.dinsafer.main;

import androidx.databinding.DataBindingUtil;
import androidx.appcompat.app.AppCompatActivity;
import android.os.Bundle;
import android.view.View;

import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDefaultCallBack2;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.main.databinding.ActivityHomeTestBinding;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.module_home.api.IHomeListCallBack;
import com.dinsafer.module_home.bean.Home;

import java.util.List;

public class HomeTestActivity extends AppCompatActivity {
    private ActivityHomeTestBinding mBinding;
    private String TAG = this.getClass().getSimpleName();
    List<Home> homes;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_home_test);

        mBinding.create.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DinSDK.getHomeInstance().createHome("dinsafer","default", new IDefaultCallBack2<Home>() {
                    @Override
                    public void onSuccess(Home T) {
                        DDLog.i(TAG, "createhome success:" + T.getHomeID());
                    }

                    @Override
                    public void onError(int code, String error) {
                        DDLog.i(TAG, "createhome onError:" + error);
                    }
                });
            }
        });

        mBinding.rename.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DinSDK.getHomeInstance().reNameHome("60b5e1b5b2590661f4a6fdf0",
                        "dinsafer2", new IDefaultCallBack() {
                            @Override
                            public void onSuccess() {
                                DDLog.i(TAG, "reNameHome success");
                            }

                            @Override
                            public void onError(int code, String error) {
                                DDLog.i(TAG, "reNameHome onError:" + error);
                            }
                        });
            }
        });
        mBinding.gethome.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DinSDK.getHomeInstance().queryHomeList(new IHomeListCallBack() {
                    @Override
                    public void onSuccess(List<Home> homeBeans) {
                        DDLog.i(TAG, "gethomeList success:" + homeBeans.size());
                        homes = homeBeans;
                    }

                    @Override
                    public void onError(int errorCode, String error) {
                        DDLog.i(TAG, "gethomeList onError:" + error);
                    }
                });
            }
        });

        mBinding.switchhome.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                DinSDK.getHomeInstance().switchHome(homes.get(0));
            }
        });
    }
}