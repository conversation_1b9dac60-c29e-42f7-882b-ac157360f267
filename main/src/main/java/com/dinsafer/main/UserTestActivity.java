package com.dinsafer.main;

import androidx.databinding.DataBindingUtil;
import android.os.Bundle;
import androidx.appcompat.app.AppCompatActivity;
import android.util.Patterns;
import android.view.View;

import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.dincore.user.api.ILoginCallback;
import com.dinsafer.dincore.user.api.IRegisterCallback;
import com.dinsafer.dincore.user.api.IResultCallback;
import com.dinsafer.dincore.user.bean.DinUser;
import com.dinsafer.dssupport.msctlib.DSSDK;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.main.databinding.ActivityUserTestBinding;
import com.dinsafer.module_home.DinSDK;

public class UserTestActivity extends AppCompatActivity {
    private static final String TAG = UserTestActivity.class.getSimpleName();
    private ActivityUserTestBinding mBinding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_user_test);
        mBinding.getCode.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Patterns.EMAIL_ADDRESS.matcher(mBinding.account.getText().toString()).matches()) {
                    UserManager.getInstance().getEmailValidateCode(mBinding.account.getText().toString(),
                            new IResultCallback() {
                                @Override
                                public void onError(int code, String error) {

                                }

                                @Override
                                public void onSuccess() {

                                }
                            });
                } else if (Patterns.PHONE.matcher(mBinding.account.getText().toString()).matches()) {
                    UserManager.getInstance().getPhoneValidateCode(mBinding.account.getText().toString(),
                            new IResultCallback() {
                                @Override
                                public void onError(int code, String error) {

                                }

                                @Override
                                public void onSuccess() {

                                }
                            });
                }
            }
        });

        mBinding.register.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Patterns.EMAIL_ADDRESS.matcher(mBinding.account.getText().toString()).matches()) {
                    UserManager.getInstance().registerAccountWithEmail(mBinding.account.getText().toString(),
                            mBinding.code.getText().toString(), new IRegisterCallback() {
                                @Override
                                public void onSuccess(DinUser user) {
                                    DDLog.i(TAG, user.toString());
                                }

                                @Override
                                public void onError(int code, String error) {

                                }
                            });
                } else if (Patterns.PHONE.matcher(mBinding.account.getText().toString()).matches()) {
                    UserManager.getInstance().registerAccountWithPhone(mBinding.account.getText().toString(),
                            mBinding.code.getText().toString(), new IRegisterCallback() {
                                @Override
                                public void onSuccess(DinUser user) {
                                    DDLog.i(TAG, user.toString());
                                }

                                @Override
                                public void onError(int code, String error) {

                                }
                            });
                }
            }
        });
        mBinding.login.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DinSDK.getUserInstance().loginWithUUid("k22", "111111", new ILoginCallback() {
                    @Override
                    public void onSuccess(DinUser user) {
                        DDLog.i(TAG, user.toString());
                    }

                    @Override
                    public void onError(int code, String error) {
                        DDLog.i(TAG, "login error:" + error);
                    }
                });
            }
        });
    }
}