package com.dinsafer.main;

import android.content.Intent;
import androidx.databinding.DataBindingUtil;
import android.os.Bundle;
import androidx.appcompat.app.AppCompatActivity;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Toast;

import com.dinsafer.dincore.activtor.api.base.IPluginActivator;
import com.dinsafer.dincore.activtor.api.base.IPluginBindCallBack;
import com.dinsafer.dincore.activtor.api.base.IPluginScanCallback;
import com.dinsafer.dincore.activtor.bean.Plugin;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.common.IDeviceListChangeListener;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.main.databinding.ActivityTuyaAcitivtyBinding;
import com.dinsafer.module_home.DinSDK;

import java.util.ArrayList;
import java.util.List;

public class TuyaAcitivty extends AppCompatActivity implements IDeviceListChangeListener, IPluginScanCallback, IPluginBindCallBack {
    private ActivityTuyaAcitivtyBinding mBinding;

    private static final String TAG = TuyaAcitivty.class.getSimpleName();
    private List<String> items = new ArrayList<>();
    ArrayAdapter<String> itemsAdapter;
    IPluginActivator activator;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_tuya_acitivty);

        itemsAdapter = new ArrayAdapter<String>(this, android.R.layout.simple_list_item_1, items);
        mBinding.tuyaLogin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
        mBinding.getdevice.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            List<Device> devices = DinSDK.getHomeInstance().fetchDevices();
                            TuyaAcitivty.this.runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    items.clear();
                                    for (Device device : devices) {
                                        items.add((String) device.getInfo().get("name"));
                                        itemsAdapter.notifyDataSetChanged();
                                    }

                                }
                            });
                            DDLog.i(TAG, "get device success:" + devices.size());
                        } catch (Exception e) {
                            DDLog.i(TAG, "get deivce fail:" + e.getMessage());
                        }
                    }
                }).start();
            }
        });

        mBinding.deviceList.setAdapter(itemsAdapter);

        mBinding.deviceList.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {

                Intent intent = new Intent(TuyaAcitivty.this, TuyaDeviceSetting.class);
                intent.putExtra("id", DinSDK.getHomeInstance().getDevices().get(position).getId());
                startActivity(intent);
            }
        });

        activator = DinSDK.getPluginActivtor();
        activator.addScanCallBack(this);
        activator.addBindCallBack(this);

        activator.setup(this);


        mBinding.adddevice.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                activator.scan("!THGa3xT");
//                DinSDK.getPluginActivtor().bindDevice();
            }
        });

        DinSDK.getHomeInstance().registerDeviceListChangeListener(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        activator.destroyActivator();
        DinSDK.getHomeInstance().unRegisterDeviceListChangeListener(this);
    }

    @Override
    public void onDeviceAdd(Device device) {
        DDLog.i(TAG, "onDeviceAdd:" + device.getId());
    }

    @Override
    public void onDeviceRemove(String id) {
        DDLog.i(TAG, "onDeviceRemove id:" + id);
    }

    @Override
    public void onScanResult(int code, Plugin plugin) {
        DDLog.i(TAG, "code:" + code);
        DDLog.i(TAG, "plugin:" + plugin.toString());
        if (code == 1) {

            TuyaAcitivty.this.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    Toast.makeText(TuyaAcitivty.this, "扫描到设备："
                            + plugin.getPluginTypeName(), Toast.LENGTH_LONG).show();
                }
            });
            plugin.setPluginName("插座");
            activator.setWifiSSID("Dinsafer");
            activator.setWifiPassword("Din1010101010");
            activator.bindDevice(plugin);
        }

    }

    @Override
    public void onBindResult(int code, String msg) {
        if (code == 1) {
            TuyaAcitivty.this.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    Toast.makeText(TuyaAcitivty.this, "绑定成功" + msg, Toast.LENGTH_LONG).show();
                }
            });
        }

    }
}