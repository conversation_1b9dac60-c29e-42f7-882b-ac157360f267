package com.dinsafer.main;

import androidx.databinding.DataBindingUtil;
import android.os.Handler;
import androidx.appcompat.app.AppCompatActivity;
import android.os.Bundle;
import android.view.View;

import com.clj.fastble.callback.BleScanCallback;
import com.clj.fastble.data.BleDevice;
import com.dinsafer.dincore.activtor.api.base.IPluginBindCallBack;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dincore.utils.BleHelper;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.main.databinding.ActivityDscamBinding;
import com.dinsafer.module_dscam.DsCamBinder;
import com.dinsafer.module_home.DinSDK;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DscamActivity extends AppCompatActivity {
    private ActivityDscamBinding mBinding;
    private List<Device> ipcDevice;

    private static String TAG = DscamActivity.class.getSimpleName();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_dscam);
        mBinding.fetch.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        ipcDevice = DinSDK.getHomeInstance().getDeviceByType("dscam");
                        DDLog.i("DscamActivity", "获取device成功:" + ipcDevice);
                        if (ipcDevice != null && ipcDevice.size() > 0) {
                            ipcDevice.get(0).registerDeviceCallBack(new IDeviceCallBack() {
                                @Override
                                public void onCmdCallBack(String id, String cmd, Map info) {
                                    DDLog.i(TAG, "id:" + id + " cmd:" + cmd);
                                }
                            });
                        }
                    }
                }).start();
            }
        });

        mBinding.connect.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Map par = new HashMap();
                par.put("cmd", "connect");
                ipcDevice.get(0).submit(par);
            }
        });

        mBinding.send.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Map par = new HashMap();
                par.put("cmd", "test");
                ipcDevice.get(0).submit(par);
            }
        });

        mBinding.addDscam.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DsCamBinder dsCamBinder = new DsCamBinder(DscamActivity.this);
                dsCamBinder.setBindHomeID("60cab836fb51fbdd8b5f3129");
                dsCamBinder.setSsid("dinsafer");
                dsCamBinder.setSsidPassword("Din1010101010");
                dsCamBinder.addBindCallBack(new IPluginBindCallBack() {
                    @Override
                    public void onBindResult(int code, String msg) {
                        DDLog.i(TAG, "onBindResult:" + code + " mesg:" + msg);
                    }
                });
                dsCamBinder.setWifiListCallBack(new DsCamBinder.IWifiListCallBack() {
                    @Override
                    public void onWifiListCallBack(List<String> wifis) {
                        DDLog.i(TAG, "get wifi list:" + wifis.size());
                    }
                });

                final boolean[] isconnect = new boolean[1];
                dsCamBinder.discoveryDevice(10 * 1000, new BleScanCallback() {
                    @Override
                    public void onScanFinished(List<BleDevice> scanResultList) {
                        DDLog.i(TAG, "onScanFinished:" + scanResultList.size());
                        for (BleDevice bleDevice : scanResultList) {
                            if ("ff0000001350".equals(bleDevice.getName()) && isconnect[0] == false) {
                                isconnect[0] = true;
                                dsCamBinder.connectDevice(bleDevice, new BleHelper.ConnectCallback() {
                                    @Override
                                    public void onStartConnect() {
                                        DDLog.i(TAG, "onStartConnect");
                                    }

                                    @Override
                                    public void onConnectFail(String message) {
                                        DDLog.i(TAG, "onConnectFail");
                                    }

                                    @Override
                                    public void onConnectSuccess() {
                                        DDLog.i(TAG, "onConnectSuccess");
                                        new Handler().postDelayed(new Runnable() {
                                            @Override
                                            public void run() {
                                                dsCamBinder.bindDevice(null);
                                            }
                                        }, 2000);

                                    }

                                    @Override
                                    public void onDisConnected() {

                                    }
                                });
                                break;
                            }
                        }

                    }

                    @Override
                    public void onScanStarted(boolean success) {
                        DDLog.i(TAG, "onScanStarted:" + success);
                    }

                    @Override
                    public void onScanning(BleDevice bleDevice) {

                    }
                });
            }
        });
    }
}