<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".TuyaAcitivty>">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/tuya_device_background"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <Button
            android:id="@+id/red"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="36dp"
            android:text="red"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/yellow" />

        <Button
            android:id="@+id/yellow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="32dp"
            android:layout_marginEnd="36dp"
            android:text="yellow"
            app:layout_constraintEnd_toStartOf="@+id/blue"
            app:layout_constraintStart_toEndOf="@+id/red"
            app:layout_constraintTop_toBottomOf="@+id/isOn" />

        <Button
            android:id="@+id/blue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="36dp"
            android:text="blue"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/yellow" />

        <Button
            android:id="@+id/setname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="SETNAME"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/delete" />

        <Button
            android:id="@+id/delete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Delete"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.498"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/mode" />

        <Button
            android:id="@+id/get_en"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="60dp"
            android:text="获取电量"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/brightness" />

        <SeekBar
            android:id="@+id/brightness"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:max="100"
            android:min="0"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.502"
            app:layout_constraintStart_toEndOf="@+id/brightness_text"
            app:layout_constraintTop_toBottomOf="@+id/setname" />

        <Switch
            android:id="@+id/mode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="38dp"
            android:text="WhiteMode"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/yellow" />

        <Switch
            android:id="@+id/isOn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="43dp"
            android:text="isOn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/type" />

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="64dp"
            android:text="Name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.498"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/online"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:text="TextView"
            app:layout_constraintEnd_toStartOf="@+id/name"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:text="TextView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.498"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/name" />

        <TextView
            android:id="@+id/brightness_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:text="TextView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/setname" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>