<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".UserActivity">
<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <EditText
            android:id="@+id/account"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="-1"
            android:inputType="textPersonName"
            android:hint="account"
            android:text="<EMAIL>"
            />
        <EditText
            android:id="@+id/code"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="-1"
            android:inputType="textPersonName"
            android:hint="code" />
        <Button
            android:id="@+id/get_code"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="get code" />

        <Button
            android:id="@+id/register"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="register" />

        <Button
            android:id="@+id/login"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="-1"
            android:text="login" />


    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
</layout>