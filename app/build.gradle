plugins {
    id 'com.android.application'
}

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    buildToolsVersion rootProject.ext.android.buildToolsVersion

    defaultConfig {
        applicationId "com.dinsafer.novaproplus"
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode rootProject.ext.android.versionCode
        versionName rootProject.ext.android.versionName

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
    }


//    TODO 增加到使用说明，主app集成的时候必须要增加的，涂鸦相关
    packagingOptions {
        pickFirst 'lib/*/libc++_shared.so' // 多个aar存在此so，需要选择第一个
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    dataBinding {
        enabled = true
    }

    packagingOptions {
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/INDEX.LIST'
        exclude 'META-INF/services/javax.annotation.processing.Processor'

        exclude '3rd-party/APACHE-LICENSE-2.0.txt'
        exclude '3rd-party/cc0-legalcode.html'
        exclude '3rd-party/BSD-3-Clause-LICENSE.txt'
        exclude '3rd-party/*.txt'
        exclude '3rd-party/*.html'

        pickFirst 'about.html'
        pickFirst 'edl-v10.html'
        pickFirst 'epl-v10.html'
        pickFirst 'notice.html'
        pickFirst 'META-INF/LICENSE'
        pickFirst 'META-INF/io.netty.versions.properties'
        pickFirst 'LICENSE-2.0.txt'
        exclude '**/*.jks'
    }
}

dependencies {

    implementation 'android.support.appcompat:appcompat:1.2.0'
    implementation 'com.google.android.material:material:1.3.0'
    implementation 'android.support.constraintlayout:constraintlayout:2.0.4'
    implementation 'android.support.navigation:navigation-fragment:2.2.2'
    implementation 'android.support.navigation:navigation-ui:2.2.2'
    implementation 'android.support.lifecycle:lifecycle-livedata-ktx:2.3.1'
    implementation 'android.support.lifecycle:lifecycle-viewmodel-ktx:2.3.1'
    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'android.support.test.ext:junit:1.1.2'
    androidTestImplementation 'android.support.test.espresso:espresso-core:3.3.0'
    implementation 'com.google.zxing:core:3.2.1'
    implementation project(':module_home')
}