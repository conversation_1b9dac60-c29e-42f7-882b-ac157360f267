package com.dinsafer.dincore.ui.notifications;



import com.dinsafer.module_home.DinSDK;
import com.dinsafer.module_home.activator.PluginActivatorManager;

import android.support.lifecycle.LiveData;
import android.support.lifecycle.MutableLiveData;
import android.support.lifecycle.ViewModel;

public class NotificationsViewModel extends ViewModel {

    private MutableLiveData<String> mText;

    PluginActivatorManager pluginActivtorManager;

    public NotificationsViewModel() {
        mText = new MutableLiveData<>();
        mText.setValue("This is notifications fragment");
        pluginActivtorManager = DinSDK.getPluginActivtor();
    }

    public void addScanPlugin(String pluginID){
//        pluginActivtorManager.setActivtor(PanelPluginActivtor.create(pluginID,
//                DeviceManager.getInstance().getCurrendDeviceID(),
//                DeviceManager.getInstance().getDeviceToken()));
//        pluginActivtorManager.setContext(getApplication().getApplicationContext());
//        pluginActivtorManager.addCallBack(this);

    }

    public LiveData<String> getText() {
        return mText;
    }
}