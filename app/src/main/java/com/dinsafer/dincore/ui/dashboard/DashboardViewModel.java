package com.dinsafer.dincore.ui.dashboard;

import android.support.lifecycle.LiveData;
import android.support.lifecycle.MutableLiveData;
import android.support.lifecycle.ViewModel;

public class DashboardViewModel extends ViewModel {

    private MutableLiveData<String> mText;

    public DashboardViewModel() {
        mText = new MutableLiveData<>();
        mText.setValue("This is dashboard fragment");
    }

    public LiveData<String> getText() {
        return mText;
    }
}