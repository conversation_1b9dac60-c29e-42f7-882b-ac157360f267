package com.dinsafer.dincore.panel;

import android.content.Context;

import androidx.annotation.NonNull;

import com.clj.fastble.data.BleDevice;

import java.util.ArrayList;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/11 11:30 AM
 */
public class PanelIAdapter extends BaseSimpleItemAdapter<BleDevice> {
    public PanelIAdapter(Context context, ArrayList<BleDevice> dataList) {
        super(context, dataList);
    }

    @Override
    public void onBindViewHolder(@NonNull SimpleItemHolder holder, int position) {
        holder.mTvContent.setText(dataList.get(position).getName());
        holder.mRoot.setOnClickListener(v -> {
            if (null != mListener) {
                mListener.onItemClick(position, holder.mRoot);
            }
        });
    }
}
