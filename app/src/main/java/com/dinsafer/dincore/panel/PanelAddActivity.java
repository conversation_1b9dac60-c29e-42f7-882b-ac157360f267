package com.dinsafer.dincore.panel;

import android.Manifest;
import android.bluetooth.BluetoothGatt;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.widget.Toast;

import androidx.annotation.Nullable;
import android.support.appcompat.app.AppCompatActivity;
import android.support.core.content.ContextCompat;
import android.support.databinding.DataBindingUtil;
import android.support.recyclerview.widget.LinearLayoutManager;

import com.clj.fastble.data.BleDevice;
import com.clj.fastble.exception.BleException;
import com.dinsafer.dincore.R;
import com.dinsafer.dincore.databinding.ActivityPanelAddBinding;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.add.callback.IPanelConnectListener;
import com.dinsafer.panel.add.callback.IPanelScanListener;
import com.dinsafer.dssupport.utils.DDLog;

import java.util.ArrayList;
import java.util.List;

/**
 * 主机相关功能测试
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/10 11:06 AM
 */
public class PanelAddActivity extends AppCompatActivity implements IPanelScanListener, IPanelConnectListener {
    private final static String TAG = PanelAddActivity.class.getSimpleName();
    private ActivityPanelAddBinding mBinding;
    private PanelIAdapter mAdapter;
    private ArrayList<BleDevice> mDataList;

    public static final String UUID_SERVICE = "cc8fb70a-255a-454a-981f-88de30240e80";
    public static final String UUID_CHRA_WRITE = "08a4a09f-f1f8-4696-a608-e06241f0a103";
    public static final String UUID_CHRA_NOTIFY = "c298e090-71a7-421e-a2c5-0f99c5489c99";
    private String[] permission = new String[]{Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION};

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_panel_add);

        PanelManager.getInstance().getPanelAdderManager().initScanRule(-1, new String[]{UUID_SERVICE},
                UUID_CHRA_WRITE, UUID_CHRA_NOTIFY);

        mBinding.btnScan.setOnClickListener(v -> startScan());

        mDataList = new ArrayList<>();
        mAdapter = new PanelIAdapter(this, mDataList);
        mBinding.rcvDevice.setLayoutManager(new LinearLayoutManager(this));
        mBinding.rcvDevice.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((position, view) -> {
            startConnect(position);
        });
    }

    private void startScan() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M
                && ContextCompat.checkSelfPermission(this,
                Manifest.permission.ACCESS_COARSE_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {
            requestLocationPermisiions();
            return;
        }

        if (!PanelManager.getInstance().getPanelAdderManager().isOpenedBluetooth()) {
            Toast.makeText(this, "Bluetooth closed.", Toast.LENGTH_SHORT).show();
            return;
        }

        PanelManager.getInstance().getPanelAdderManager().startScanPanel(this);
    }

    public void requestLocationPermisiions() {
        requestPermissions(permission, 1000);
    }

    private void startConnect(int position) {
        if (position < 0 || position >= mDataList.size()) {
            DDLog.e(TAG, "Error, index out of range.");
            return;
        }

        PanelManager.getInstance().getPanelAdderManager().connect(mDataList.get(position), this);
    }

    @Override
    protected void onDestroy() {
        PanelManager.getInstance().getPanelAdderManager().stopScanPanel();
        PanelManager.getInstance().getPanelAdderManager().destroyAdder();
        super.onDestroy();
    }

    @Override
    public void onScanFinished(List<BleDevice> scanResultList) {
        DDLog.i(TAG, "onScanFinished");
    }

    @Override
    public void onScanStarted(boolean success) {
        DDLog.i(TAG, "onScanStarted, SUCCESS: " + success);
        if (success) {
            runOnUiThread(() -> {
                mDataList.clear();
                mAdapter.notifyDataSetChanged();
            });
        }
    }

    @Override
    public void onScanning(BleDevice bleDevice) {
        DDLog.i(TAG, "onScanning");
        runOnUiThread(() -> {
            mDataList.add(bleDevice);
            mAdapter.notifyItemInserted(mDataList.size() - 1);
        });
    }

    @Override
    public void onStartConnect() {
        DDLog.i(TAG, "onStartConnect");
    }

    @Override
    public void onConnectFail(BleDevice bleDevice, BleException exception) {
        DDLog.i(TAG, "onConnectFail");
    }

    @Override
    public void onConnectSuccess(BleDevice bleDevice, BluetoothGatt gatt, int status) {
        DDLog.i(TAG, "onConnectSuccess");
        PanelManager.getInstance().getPanelAdderManager().stopScanPanel();
        startActivity(new Intent(this, PanelDetailActivity.class));
    }

    @Override
    public void onDisConnected(BleDevice bleDevice, boolean isActiveDisConnected, BluetoothGatt gatt, int status) {
        DDLog.i(TAG, "onDisConnected");
    }

    @Override
    public void onNotifySuccess() {
        DDLog.i(TAG, "onNotifySuccess");
    }

    @Override
    public void onNotifyFailure(BleDevice bleDevice, BleException exception) {
        DDLog.i(TAG, "onNotifyFailure");
        PanelManager.getInstance().getPanelAdderManager().disconnectAllBle();
    }
}
