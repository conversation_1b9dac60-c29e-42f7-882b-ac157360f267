package com.dinsafer.dincore.panel;

import android.os.Bundle;

import androidx.annotation.Nullable;
import android.support.appcompat.app.AppCompatActivity;
import android.support.databinding.DataBindingUtil;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.R;
import com.dinsafer.dincore.databinding.ActivityPanelDetailBinding;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.add.callback.IPanelCmdCallback;
import com.dinsafer.panel.add.bean.PanelCmdResult;

/**
 * 主机交互测试页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/11 3:57 PM
 */
public class PanelDetailActivity extends AppCompatActivity implements IPanelCmdCallback {
    private final static String TAG = PanelDetailActivity.class.getSimpleName();
    private ActivityPanelDetailBinding mBinding;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_panel_detail);

        if (null != DinCore.getUserInstance().getUser()) {
            PanelManager.getInstance().getPanelAdderOperator()
                    .initUserId(DinCore.getUserInstance().getUser().getUser_id());
        }

        mBinding.btnVerifyPanelPassword.setOnClickListener(v -> {
            PanelManager.getInstance().getPanelAdderOperator().verifyDevicePassword("1111");
        });
        mBinding.btnSetPanelName.setOnClickListener(v -> {
            PanelManager.getInstance().getPanelAdderOperator().setDeviceName("Helio Smart  001");
        });
        mBinding.btnSetPanelPassword.setOnClickListener(v -> {
            PanelManager.getInstance().getPanelAdderOperator().setDevicePassword("1111");
        });
        mBinding.btnGetWifi.setOnClickListener(v -> {
            PanelManager.getInstance().getPanelAdderOperator().getWifiList();
        });

        PanelManager.getInstance().getPanelAdderManager().addPanelCmdResultListener(this);
    }

    @Override
    protected void onDestroy() {
        PanelManager.getInstance().getPanelAdderManager().removePanelCmdResultListener(this);
        PanelManager.getInstance().getPanelAdderManager().disconnectAllBle();
        super.onDestroy();
    }

    @Override
    public void onPanelResult(PanelCmdResult result) {
        DDLog.i(TAG, "onPanelResult, result: " + result);
    }

}
