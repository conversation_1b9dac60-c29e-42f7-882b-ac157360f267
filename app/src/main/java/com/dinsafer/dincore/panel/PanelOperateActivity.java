package com.dinsafer.dincore.panel;

import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.Nullable;
import android.support.appcompat.app.AlertDialog;
import android.support.appcompat.app.AppCompatActivity;
import android.support.databinding.DataBindingUtil;

import com.dinsafer.dincore.DinCore;
import com.dinsafer.dincore.R;
import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dincore.databinding.ActivityPanelOperateBinding;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.panel.PanelManager;
import com.dinsafer.panel.bean.device.PanelDevice;
import com.dinsafer.panel.common.PanelParamsHelper;
import com.dinsafer.panel.operate.callback.PanelCallback;

import java.util.List;
import java.util.Map;

/**
 * 主机交互测试页
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/14 11:24 AM
 */
public class PanelOperateActivity extends AppCompatActivity implements PanelCallback.GetPanelInfo, IDeviceCallBack {
    private final static String TAG = PanelOperateActivity.class.getSimpleName();
    private ActivityPanelOperateBinding mBinding;

    private String deviceId;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_panel_operate);

        PanelManager.getInstance().getPanelCallbackHelper().addGetPanelInfoCallback(this);

        mBinding.btnGetDeviceInfo.setOnClickListener(v -> {
            if (checkHadLogin()) {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            List<Device> devices = DinSDK.getHomeInstance().fetchDevices();
                            DDLog.i(TAG, "device: " + devices.toString());
                            if (devices.size() > 0) {
                                Device device;
                                for (int i = 0; i < devices.size(); i++) {
                                    device = devices.get(i);
                                    if (device instanceof PanelDevice) {
                                        device.registerDeviceCallBack(PanelOperateActivity.this);
                                        deviceId = device.getId();
                                        break;
                                    }
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }).start();
            }
        });
        mBinding.btnArm.setOnClickListener(v -> {
            if (checkHadLogin()) {
                Device panelDevice = DinSDK.getHomeInstance().getDevice(deviceId);
                if (null != panelDevice) {
                    panelDevice.submit(PanelParamsHelper.operationArm(PanelParamsHelper.OPERATE_ARM_ARM, true));
                }
            }
        });
        mBinding.btnDisarm.setOnClickListener(v -> {
            if (checkHadLogin()) {
                Device panelDevice = DinSDK.getHomeInstance().getDevice(deviceId);
                if (null != panelDevice) {
                    panelDevice.submit(PanelParamsHelper.operationArm(PanelParamsHelper.OPERATE_ARM_DISARM, true));
                }
            }
        });


        mBinding.btnSetExitdelayy.setOnClickListener(v -> {
            if (checkHadLogin()) {
                Device panelDevice = DinSDK.getHomeInstance().getDevice(deviceId);
                if (null != panelDevice) {
                    panelDevice.submit(PanelParamsHelper.setExitDelay(10, false));
                }
            }
        });

        mBinding.btnSetHomeArmInfo.setOnClickListener(v -> {
            if (checkHadLogin()) {
                Device panelDevice = DinSDK.getHomeInstance().getDevice(deviceId);
                if (null != panelDevice) {
                    panelDevice.submit(PanelParamsHelper.setHomeArmInfo(null, null, null));
                }
            }
        });
    }

    @Override
    public void onGetPanelInfo(boolean isSuccess, boolean isDeviceOffline, boolean isNewDevice) {
        DDLog.i(TAG, "onGetPanelInfo, isSuccess: " + isSuccess + ", isNewDevice: " + isNewDevice);
        if (isSuccess) {
            PanelManager.getInstance().getPanelOperator().connectWs();
        }
    }

    @Override
    public void onStartGettingPanelInfo(boolean isNewDevice, String deviceId) {
        DDLog.i(TAG, "onStartGettingPanelInfo, deviceId: " + deviceId);
    }

    /**
     * 检查是否已经登录
     * 调用接口前，必须先登录
     *
     * @return true: 已登录
     */
    private boolean checkHadLogin() {
        boolean login = null != DinCore.getUserInstance().getUser()
                && !TextUtils.isEmpty(DinCore.getUserInstance().getUser().getUser_id());

        if (!login) {
            showNeedLoginDialog();
        }
        return login;
    }

    private void showNeedLoginDialog() {
        new AlertDialog.Builder(this)
                .setTitle("Error")
                .setMessage("You must login before do this action.")
                .setPositiveButton("I Know", null)
                .create().show();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onCmdCallBack(String id, String cmd, Map info) {
        DDLog.i(TAG, "onCmdCallBack, ID: " + id + ", info: " + info);
    }
}
