package com.dinsafer.dincore.panel;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import android.support.recyclerview.widget.RecyclerView;

import com.dinsafer.dincore.R;

import java.util.ArrayList;

/**
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/11 11:22 AM
 */
public abstract class BaseSimpleItemAdapter<T> extends RecyclerView.Adapter<BaseSimpleItemAdapter.SimpleItemHolder> {
    protected Context context;
    protected ArrayList<T> dataList;
    protected OnItemClickListener mListener;

    public BaseSimpleItemAdapter(Context context, ArrayList<T> dataList) {
        this.context = context;
        this.dataList = dataList;
        if (this.dataList == null) {
            this.dataList = new ArrayList<>();
        }
    }

    @NonNull
    @Override
    public SimpleItemHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_simple_text, null, false);
        return new SimpleItemHolder(view);
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public void setOnItemClickListener(OnItemClickListener listener) {
        this.mListener = listener;
    }

    static class SimpleItemHolder extends RecyclerView.ViewHolder {
        TextView mTvContent;
        FrameLayout mRoot;

        public SimpleItemHolder(@NonNull View itemView) {
            super(itemView);
            mTvContent = itemView.findViewById(R.id.tv_content);
            mRoot = itemView.findViewById(R.id.fl_root);
        }
    }

    public interface OnItemClickListener {
        void onItemClick(int position, View view);
    }
}
