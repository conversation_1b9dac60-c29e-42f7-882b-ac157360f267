package com.dinsafer.dincore;

import android.content.Intent;
import android.os.Bundle;

import android.support.appcompat.app.AppCompatActivity;
import android.support.databinding.DataBindingUtil;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.databinding.ActivityMainBinding;
import com.dinsafer.dincore.panel.PanelAddActivity;
import com.dinsafer.dincore.panel.PanelOperateActivity;

public class MainActivity extends AppCompatActivity {

    private final String TAG = getClass().getSimpleName();
    private ActivityMainBinding mBinding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_main);

        mBinding.user.setOnClickListener(v -> startActivity(new Intent(this, UserActivity.class)));
        mBinding.activator.setOnClickListener(v -> startActivity(new Intent(this, ActivatorActivity.class)));

        mBinding.panel.setOnClickListener(v -> startActivity(new Intent(this, PanelAddActivity.class)));
        mBinding.btnPanelOperate.setOnClickListener(v->startActivity(new Intent(this, PanelOperateActivity.class)));
        mBinding.tuya.setOnClickListener(v -> startActivity(new Intent(this, TuyaAcitivty.class)));
    }
}