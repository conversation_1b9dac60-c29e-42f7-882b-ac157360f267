package com.dinsafer.dincore;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;
import android.support.appcompat.app.AppCompatActivity;
import android.support.databinding.DataBindingUtil;

import com.dinsafer.dincore.databinding.ActivityUserBinding;
import com.dinsafer.dincore.user.api.ILoginCallback;
import com.dinsafer.dincore.user.api.IResultCallback;
import com.dinsafer.dincore.user.api.IResultCallback2;
import com.dinsafer.dincore.user.bean.DinUser;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_home.DinSDK;


/**
 * 用户相关功能测试
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/10 11:06 AM
 */
public class UserActivity extends AppCompatActivity {
    private final String TAG = UserActivity.class.getSimpleName();

    private ActivityUserBinding mBinding;

    private final static String mUserUuid = "zbcheliosmart2";
    private final static String mNewUserUuid = "zbc";
    private final static String mOldPassword = "1234567890";
    private final static String mNewPassword = "111111";
    private final String mVerifyCode = "12323";
    private final static String mAreaCode = "86";
    private final static String mPhone = "13570618924";
    private final static String mEmail = "<EMAIL>";
    private final static String mImageFilePath = "";

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_user);


        mBinding.uid.setText(mUserUuid);
        mBinding.password.setText(mOldPassword);

        mBinding.login.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DinSDK.getUserInstance().loginWithUUid(mBinding.uid.getText().toString(), mBinding.password.getText().toString(),
                        new ILoginCallback() {
                            @Override
                            public void onSuccess(DinUser user) {
                                DDLog.i(TAG, "login success");
                            }

                            @Override
                            public void onError(int code, String error) {
                                DDLog.i(TAG, "login fail:" + code + " message:" + error);
                            }
                        });
            }
        });

        mBinding.register.setOnClickListener(v -> {

        });

        mBinding.modifyUuidPassword.setOnClickListener(v -> {
            DinSDK.getUserInstance().modifyUuidPassword(mNewUserUuid, mNewPassword, new IResultCallback() {
                @Override
                public void onError(int code, String error) {
                    DDLog.i(TAG, "mModifyUuidPassword failed, code: " + code + ", error: " + error);
                }

                @Override
                public void onSuccess() {
                    DDLog.i(TAG, "mModifyUuidPassword success.");
                }
            });
        });

        mBinding.changePassword.setOnClickListener(v -> {
            DinSDK.getUserInstance().changePassword(mOldPassword, mNewPassword, new IResultCallback() {
                @Override
                public void onError(int code, String error) {
                    DDLog.i(TAG, "mChangePassword failed, code: " + code + ", error: " + error);
                }

                @Override
                public void onSuccess() {
                    DDLog.i(TAG, "mChangePassword success.");
                }
            });
        });

        mBinding.changeUuid.setOnClickListener(v -> {
            DinSDK.getUserInstance().changeUuid(mNewUserUuid, new IResultCallback() {
                @Override
                public void onError(int code, String error) {
                    DDLog.i(TAG, "mChangeUuid failed, code: " + code + ", error: " + error);
                }

                @Override
                public void onSuccess() {
                    DDLog.i(TAG, "mChangeUuid success.");
                }
            });
        });


        mBinding.forgetPassword.setOnClickListener(v -> {
            DinSDK.getUserInstance().forgetPassword(mUserUuid, new IResultCallback2<String>() {
                @Override
                public void onError(int code, String error) {
                    DDLog.i(TAG, "mForgetPassword failed, code: " + code + ", error: " + error);
                }

                @Override
                public void onSuccess(String result) {
                    DDLog.i(TAG, "mForgetPassword success.");
                }
            });
        });

        mBinding.forgetPasswordNew.setOnClickListener(v -> {
            DinSDK.getUserInstance().forgetPasswordSetNewPassword(mUserUuid, mVerifyCode, mNewPassword, new IResultCallback() {
                @Override
                public void onError(int code, String error) {
                    DDLog.i(TAG, "mForgetPasswordNew failed, code: " + code + ", error: " + error);
                }

                @Override
                public void onSuccess() {
                    DDLog.i(TAG, "mForgetPasswordNew success.");
                }
            });
        });

        mBinding.bindPhone.setOnClickListener(v -> {
            DinSDK.getUserInstance().bindPhone(mAreaCode, mPhone, new IResultCallback2<String>() {
                @Override
                public void onError(int code, String error) {
                    DDLog.i(TAG, "mBindPhone failed, code: " + code + ", error: " + error);
                }

                @Override
                public void onSuccess(String result) {
                    DDLog.i(TAG, "mBindPhone success.");
                }
            });
        });

        mBinding.unbindPhone.setOnClickListener(v -> {
            DinSDK.getUserInstance().unbindPhone(new IResultCallback2<String>() {
                @Override
                public void onError(int code, String error) {
                    DDLog.i(TAG, "mBindPhone failed, code: " + code + ", error: " + error);
                }

                @Override
                public void onSuccess(String result) {
                    DDLog.i(TAG, "mBindPhone success.");
                }
            });
        });

        mBinding.verifyBindPhone.setOnClickListener(v -> {
            String key = "aaa";
            DinSDK.getUserInstance().verifyBindPhone(mAreaCode, mPhone, key + mVerifyCode, new IResultCallback() {
                @Override
                public void onError(int code, String error) {
                    DDLog.i(TAG, "mVerifyBindPhone failed, code: " + code + ", error: " + error);
                }

                @Override
                public void onSuccess() {
                    DDLog.i(TAG, "mVerifyBindPhone success.");
                }
            });
        });

        mBinding.verifyUnbindPhone.setOnClickListener(v -> {
            String key = "aaa";
            DinSDK.getUserInstance().verifyUnbindPhone(key + mVerifyCode, new IResultCallback() {
                @Override
                public void onError(int code, String error) {
                    DDLog.i(TAG, "mVerifyUnbindPhone failed, code: " + code + ", error: " + error);
                }

                @Override
                public void onSuccess() {
                    DDLog.i(TAG, "mVerifyUnbindPhone success.");
                }
            });
        });

        mBinding.bindEmail.setOnClickListener(v -> {
            DinSDK.getUserInstance().bindEmail(mEmail, new IResultCallback() {
                @Override
                public void onError(int code, String error) {
                    DDLog.i(TAG, "mBindEmail failed, code: " + code + ", error: " + error);
                }

                @Override
                public void onSuccess() {
                    DDLog.i(TAG, "mBindEmail success.");
                }
            });
        });

        mBinding.unbindEmail.setOnClickListener(v -> {
            DinSDK.getUserInstance().unbindEmail(new IResultCallback() {
                @Override
                public void onError(int code, String error) {
                    DDLog.i(TAG, "mUnbindEmail failed, code: " + code + ", error: " + error);
                }

                @Override
                public void onSuccess() {
                    DDLog.i(TAG, "mUnbindEmail success.");
                }
            });
        });

        mBinding.setAvatar.setOnClickListener(v -> {
            DinSDK.getUserInstance().uploadUserAvatar(mImageFilePath, new IResultCallback2<String>() {
                @Override
                public void onError(int code, String error) {
                    DDLog.i(TAG, "mSetAvatar failed, code: " + code + ", error: " + error);
                }

                @Override
                public void onSuccess(String result) {
                    DDLog.i(TAG, "mSetAvatar success.");
                }
            });
        });

    }
}
