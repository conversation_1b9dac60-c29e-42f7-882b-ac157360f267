package com.dinsafer.dincore;

import android.os.Bundle;
import android.text.TextUtils;
import android.widget.Toast;

import androidx.annotation.Nullable;
import android.support.appcompat.app.AppCompatActivity;
import android.support.databinding.DataBindingUtil;

import com.dinsafer.dincore.activtor.api.base.IPluginBindCallBack;
import com.dinsafer.dincore.activtor.api.base.IPluginScanCallback;
import com.dinsafer.dincore.activtor.bean.Plugin;
import com.dinsafer.dincore.databinding.ActivityActivatorBinding;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_home.DinSDK;
import com.dinsafer.module_home.activator.PluginActivatorManager;
import com.dinsafer.panel.PanelManager;

/**
 * 配件添加相关功能测试
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">Contact me.</a>
 * @since 2021/5/10 11:06 AM
 */
public class ActivatorActivity extends AppCompatActivity implements IPluginScanCallback, IPluginBindCallBack {
    private static final String TAG = ActivatorActivity.class.getSimpleName();

    private static final String DEFAULT_QR_CODE = "!THGa3xT";

    private ActivityActivatorBinding mBinding;
    PluginActivatorManager mPluginActivtor;
    private Plugin mPlugin;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_activator);
        mPluginActivtor = DinSDK.getPluginActivtor();
        mPluginActivtor.setup(this, PanelManager.getInstance().getCurrentPanelId(),
                PanelManager.getInstance().getCurrentPanelToken());
        mPluginActivtor.addScanCallBack(this);
        mPluginActivtor.addBindCallBack(this);

        mBinding.btnScan.setOnClickListener(v -> startScan());
        mBinding.btnAdd.setOnClickListener(v -> startBind());

        mBinding.etQrCode.setText(DEFAULT_QR_CODE);
    }

    private void startScan() {
        DDLog.i(TAG, "startScan");
        mPluginActivtor.scan(mBinding.etQrCode.getText().toString());
    }

    private void startBind() {
        DDLog.i(TAG, "startBind");
        if (null == mPlugin || TextUtils.isEmpty(mPlugin.getPluginID())) {
            Toast.makeText(this, "Error on bind plugin", Toast.LENGTH_SHORT).show();
            return;
        }

        mPluginActivtor.bindDevice(mPlugin);
    }

    @Override
    protected void onDestroy() {
        mPluginActivtor.removeBindCallBack(this);
        mPluginActivtor.removeScanCallBack(this);
        super.onDestroy();
    }

    @Override
    public void onScanResult(int code, Plugin plugin) {
        DDLog.i(TAG, "onScanResult, code: " + code + "，plugin: " + plugin);
        mPlugin = plugin;
        Toast.makeText(this, "Find plugin", Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onBindResult(int code, String msg) {
        DDLog.i(TAG, "onScanResult, code: " + code + "，msg: " + msg);

    }
}
