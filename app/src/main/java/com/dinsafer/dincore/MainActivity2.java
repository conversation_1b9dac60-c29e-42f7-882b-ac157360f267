package com.dinsafer.dincore;

import android.os.Bundle;

import com.google.android.material.bottomnavigation.BottomNavigationView;

import android.support.appcompat.app.AppCompatActivity;
import android.support.navigation.NavController;
import android.support.navigation.Navigation;
import android.support.navigation.ui.AppBarConfiguration;
import android.support.navigation.ui.NavigationUI;

public class MainActivity2 extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main2);
        BottomNavigationView navView = findViewById(R.id.nav_view);
        // Passing each menu ID as a set of Ids because each
        // menu should be considered as top level destinations.
        AppBarConfiguration appBarConfiguration = new AppBarConfiguration.Builder(
                R.id.navigation_home, R.id.navigation_dashboard, R.id.navigation_notifications)
                .build();
        NavController navController = Navigation.findNavController(this, R.id.nav_host_fragment);
        NavigationUI.setupActionBarWithNavController(this, navController, appBarConfiguration);
        NavigationUI.setupWithNavController(navView, navController);
    }

}