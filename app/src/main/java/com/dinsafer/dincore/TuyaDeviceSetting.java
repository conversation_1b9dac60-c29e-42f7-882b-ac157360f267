package com.dinsafer.dincore;

import android.graphics.Color;
import android.os.Bundle;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.SeekBar;

import com.dinsafer.dincore.common.Device;
import com.dinsafer.dincore.common.IDeviceCallBack;
import com.dinsafer.dincore.databinding.ActivityTuyaDeviceSettingBinding;
import com.dinsafer.dssupport.plugin.PluginTypeHelper;
import com.dinsafer.dssupport.utils.DDLog;
import com.dinsafer.module_home.DinHome;

import java.util.HashMap;
import java.util.Map;

import android.support.appcompat.app.AppCompatActivity;
import android.support.databinding.DataBindingUtil;

public class TuyaDeviceSetting extends AppCompatActivity implements IDeviceCallBack {
    private ActivityTuyaDeviceSettingBinding mBinding;
    Device device;

    private static final String TAG = TuyaDeviceSetting.class.getSimpleName();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_tuya_device_setting);
        device = DinHome.getInstance().getDevice(getIntent().getExtras().getString("id"));
        if (device == null) {
            finish();
            return;
        }

        device.registerDeviceCallBack(this);
        mBinding.setname.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Map par = new HashMap();
                par.put("cmd", "set_name");
                par.put("name", "dinsafer");
                device.submit(par);
            }
        });
        updataUI();
        upDataName();
        mBinding.isOn.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                Map par = new HashMap();
                par.put("cmd", "tuya_set_on");
                par.put("isOn", isChecked);
                device.submit(par);
            }
        });

        mBinding.mode.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                Map par = new HashMap();
                par.put("cmd", "tuya_set_blub_mode");
                par.put("mode", isChecked ? "white" : "colour");
                device.submit(par);
            }
        });

        mBinding.red.setOnClickListener(v -> {
            Map par = new HashMap();
            par.put("cmd", "tuya_set_blub_color");
            par.put("color", -8519667);
            par.put("brightness", mBinding.brightness.getProgress() / 100f);
            device.submit(par);
        });

        mBinding.blue.setOnClickListener(v -> {
            Map par = new HashMap();
            par.put("cmd", "tuya_set_blub_color");
            par.put("color", -16768257);
            par.put("brightness", mBinding.brightness.getProgress() / 100f);
            device.submit(par);
        });
        mBinding.yellow.setOnClickListener(v -> {
            Map par = new HashMap();
            par.put("cmd", "tuya_set_blub_color");
            par.put("color", -5120);
            par.put("brightness", mBinding.brightness.getProgress() / 100f);
            device.submit(par);
        });

        mBinding.brightness.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                Map par = new HashMap();
                par.put("cmd", "tuya_set_blub_color");
                String mode = (String) device.getInfo().get("mode");
                if (mode.equals("colour")) {
                    par.put("color", (int) device.getInfo().get("color"));
                }
                par.put("brightness", seekBar.getProgress() / 100f);
                device.submit(par);
            }
        });

        mBinding.delete.setOnClickListener(v -> {
            Map par = new HashMap();
            par.put("cmd", "delete_device");
            device.submit(par);
        });

        mBinding.getEn.setOnClickListener(v -> {
            Map par = new HashMap();
            par.put("cmd", "tuya_get_energy");
            par.put("type", "day");
            par.put("start_time", System.currentTimeMillis());
            par.put("count", 1);
            device.submit(par);
        });

    }

    private void updataUI() {
        TuyaDeviceSetting.this.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mBinding.name.setText((String) device.getInfo().get("name"));
                String name = PluginTypeHelper.getInstance()
                        .getNameByBigIDAndSType(device.getCategory(), device.getSubCategory());
                mBinding.type.setText(name);
                mBinding.isOn.setChecked((Boolean) device.getInfo().get("isOn"));
                mBinding.online.setText(((int) device.getInfo().get("state") == 1) ?
                        "online" : "offline");

                if (device.getSubCategory().equals("01")) {
                    mBinding.mode.setVisibility(View.GONE);
                    mBinding.red.setVisibility(View.GONE);
                    mBinding.yellow.setVisibility(View.GONE);
                    mBinding.blue.setVisibility(View.GONE);
                    mBinding.brightness.setVisibility(View.GONE);
                    mBinding.brightnessText.setVisibility(View.GONE);
                    return;
                }
                String mode = (String) device.getInfo().get("mode");
                if (mode.equals("white")) {
                    mBinding.mode.setChecked(true);
                    mBinding.tuyaDeviceBackground.setBackgroundColor(Color.BLACK);
                } else {
                    mBinding.mode.setChecked(false);
                    mBinding.tuyaDeviceBackground.setBackgroundColor((int) device.getInfo().get("color"));
                }

                mBinding.brightness.setProgress((int) ((Double) device.getInfo().get("brightness") * 100));
                mBinding.brightnessText.setText((int) ((Double) device.getInfo().get("brightness") * 100) + "");
            }
        });
    }

    private void upDataName() {
        TuyaDeviceSetting.this.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mBinding.name.setText((String) device.getInfo().get("name"));
            }
        });
    }


    @Override
    public void onCmdCallBack(String id, String cmd, Map info) {
        DDLog.i(TAG, "cmd:" + cmd);
        if (device.getId().equals(id)) {
            if (cmd.equals("tuya_set_name")) {
                upDataName();
            } else {
                updataUI();
            }
        }
    }

    @Override
    protected void onDestroy() {
        if (device != null) {
            device.unregisterDeviceCallBack(this);
        }
        super.onDestroy();
    }
}