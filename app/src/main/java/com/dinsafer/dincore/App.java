package com.dinsafer.dincore;

import android.app.Application;
import android.content.Context;

import com.dinsafer.module_home.DinSDK;
import com.dinsafer.panel.PanelManager;


/**
 * Created by Rinfon on 16/7/21.
 */
public class App extends Application {
    private String TAG = getClass().getSimpleName();

    private static Context context;

    private static Application mInstance;

    @Override
    public void onCreate() {
        super.onCreate();
        mInstance = this;
        context = getApplicationContext();
        DinSDK.DinSDKBuilder.create()
                .withApplication(this)
                .withAppID("c08415b02e9ad8badcc7bbb1d504a531")
                .withAppSecret("c1fa7176c39b97272390ff080f83d020")
                .withDebugMode(true)
                .withDomain("api-nx.plutomen.com")
                .withTuyaAppKey("p5dd5qyhautxufrgscut")
                .withTuyaAppSecret("ksqv8qtv4jxn4d43yc9hf77paagyspkv")
                .build();

        PanelManager.getInstance().initPanelManager(this, "");
    }


    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
    }

    public static Context getAppContext() {
        return context;
    }

    public static Application getInstance() {
        return mInstance;
    }


}
