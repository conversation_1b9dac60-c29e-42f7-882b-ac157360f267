<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.dinsafer.dincore">

    <application
        android:name=".App"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.DinCore"
        android:usesCleartextTraffic="true"
        tools:replace="allowBackup,supportsRtl">

        <activity
            android:name=".MainActivity2"
            android:label="@string/title_activity_main2" />
        <activity android:name=".UserActivity" />
        <activity android:name=".ActivatorActivity" />
        <activity android:name=".panel.PanelAddActivity" />
        <activity android:name=".panel.PanelDetailActivity" />
        <activity android:name=".panel.PanelOperateActivity" />
        <activity android:name=".MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

</manifest>