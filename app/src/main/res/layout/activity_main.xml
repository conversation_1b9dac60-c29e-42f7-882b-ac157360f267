<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".MainActivity>">

    <android.support.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <Button
                android:id="@+id/user"
                style="@style/MyButton"
                android:text="UserTest" />

            <Button
                android:id="@+id/activator"
                style="@style/MyButton"
                android:text="ActivatorTest" />

            <Button
                android:id="@+id/panel"
                style="@style/MyButton"
                android:text="PanelAddTest" />

            <Button
                android:id="@+id/btn_panel_operate"
                style="@style/MyButton"
                android:text="PanelOperateTest" />
                android:text="PanelTest" />

            <Button
                android:id="@+id/tuya"
                style="@style/MyButton"
                android:text="TuyaTest" />
        </LinearLayout>
    </android.support.core.widget.NestedScrollView>
</layout>