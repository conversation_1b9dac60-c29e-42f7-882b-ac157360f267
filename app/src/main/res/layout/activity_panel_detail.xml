<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".MainActivity>">

    <android.support.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <Button
                android:id="@+id/btn_verify_panel_password"
                style="@style/MyButton"
                android:text="VerifyPanelPassword" />

            <Button
                android:id="@+id/btn_set_panel_name"
                style="@style/MyButton"
                android:text="SetPanelName" />

            <Button
                android:id="@+id/btn_set_panel_password"
                style="@style/MyButton"
                android:text="SetPanelPassword" />
            <Button
                android:id="@+id/btn_get_wifi"
                style="@style/MyButton"
                android:text="GetWifi" />

        </LinearLayout>
    </android.support.core.widget.NestedScrollView>
</layout>