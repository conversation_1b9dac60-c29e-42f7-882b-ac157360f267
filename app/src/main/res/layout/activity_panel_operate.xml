<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <android.support.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <Button
                android:id="@+id/btn_get_device_info"
                style="@style/MyButton"
                android:text="GetDeviceInfo" />

            <Button
                android:id="@+id/btn_arm"
                style="@style/MyButton"
                android:text="arm" />

            <Button
                android:id="@+id/btn_disarm"
                style="@style/MyButton"
                android:text="disarm" />

            <Button
                android:id="@+id/btn_set_exitdelayy"
                style="@style/MyButton"
                android:text="SET_EXITDELAY" />

            <Button
                android:id="@+id/btn_setHomeArmInfo"
                style="@style/MyButton"
                android:text="setHomeArmInfo" />
        </LinearLayout>
    </android.support.core.widget.NestedScrollView>
</layout>