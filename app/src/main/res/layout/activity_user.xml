<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".UserActivity">

    <android.support.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <EditText
                android:id="@+id/uid"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="76dp"
                android:ems="10"
                android:inputType="textPersonName"
                android:text="Name" />

            <EditText
                android:id="@+id/password"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:ems="10"
                android:inputType="textPassword" />

            <Button
                android:id="@+id/login"
                style="@style/MyButton"
                android:text="Login" />

            <Button
                android:id="@+id/register"
                style="@style/MyButton"
                android:text="Register" />

            <Button
                android:id="@+id/modify_uuid_password"
                style="@style/MyButton"
                android:text="ModifyUuidPassword" />

            <Button
                android:id="@+id/change_password"
                style="@style/MyButton"
                android:text="ChangePassword" />

            <Button
                android:id="@+id/change_uuid"
                style="@style/MyButton"
                android:text="ChangeUuid" />

            <Button
                android:id="@+id/forget_password"
                style="@style/MyButton"
                android:text="ForgetPassword" />

            <Button
                android:id="@+id/forget_password_new"
                style="@style/MyButton"
                android:text="ForgetPwdSetNew" />

            <Button
                android:id="@+id/bind_phone"
                style="@style/MyButton"
                android:text="BindPhone" />

            <Button
                android:id="@+id/unbind_phone"
                style="@style/MyButton"
                android:text="UnbindPhone" />


            <Button
                android:id="@+id/verify_bind_phone"
                style="@style/MyButton"
                android:text="VerifyBindPhone" />

            <Button
                android:id="@+id/verify_unbind_phone"
                style="@style/MyButton"
                android:text="VirifyUnbindPhone" />


            <Button
                android:id="@+id/bind_email"
                style="@style/MyButton"
                android:text="BindEmail" />

            <Button
                android:id="@+id/unbind_email"
                style="@style/MyButton"
                android:text="UnbindEmail" />

            <Button
                android:id="@+id/set_avatar"
                style="@style/MyButton"
                android:text="SetAvatar" />

        </LinearLayout>
    </android.support.core.widget.NestedScrollView>
</layout>