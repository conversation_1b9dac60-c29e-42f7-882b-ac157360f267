1. 在项目的build.gradle中引入maven相关配置和打包相关配置

    ```groovy
    apply from: rootProject.file('gradle-scripts/config.gradle')
    ```

2. 在需要上传到私有仓库模块的build.gradle中增加打包上传配置(**通常需要覆盖artifactId**)

    ```groovy
    
    ext.modulePublishConfig = [
            artifactId: 'dssupport',
    ]
    // 使用maven插件上传
    apply from: rootProject.file('gradle-scripts/upload.gradle')
    
    // 使用maven-publish插件上传
    //apply from: rootProject.file('gradle-scripts/publish.gradle')
    
    ```

3. 执行上传任务
    - 如果使用maven插件上传，直接执行模块的uploadArchives任务
    - 如果使用maven-publish插件上传，需要先执行模块的assemble任务，完成后再执行publish任务