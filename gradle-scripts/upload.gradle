// 使用maven插件将库打包到本地仓库/远程仓库或仓库快照
apply plugin: 'maven'

if (!rootProject.hasProperty('mavenConfig')) {
    println("请在根的build.gradle中引入配置文件：--》" + "apply from: rootProject.file('gradle-scripts/config.gradle')")

}

def mavenConfig = rootProject.ext.mavenConfig
def moduleConfig = (rootProject.ext.modulePublishConfig ?: [:]) + (project.ext.modulePublishConfig ?: [:])


// 计算最终的版本号（如果是快照版本，需要加上 `-SNAPSHOT`）
def finalVersion = moduleConfig.version ?: rootProject.ext.android.versionName
if (!mavenConfig.release) {
    finalVersion += "-SNAPSHOT"
}

// 选择仓库 URL
def targetRepoUrl = mavenConfig.release ? mavenConfig.remoteReleaseRepoUrl : mavenConfig.remoteSnapshotRepoUrl

// 打印配置信息
println "====================== Maven Upload Configuration ======================"
println "📦 Group ID      : ${moduleConfig.groupId ?: 'com.dinsafer.dinsdk'}"
println "📦 Artifact ID   : ${moduleConfig.artifactId ?: project.name}"
println "📦 Version       : ${finalVersion}"
println "📦 Packaging     : ${moduleConfig.packaging ?: 'aar'}"
println "🏠 Local Repo    : ${mavenConfig.localRepoUrl}"
println "🌍 Remote Repo   : ${targetRepoUrl}"
println "👤 User          : ${mavenConfig.user}"
println "🔐 Password      : ${mavenConfig.password ? '******' : 'Not Set'}"
println "🌐 Use Remote Maven: ${mavenConfig.useRemoteMaven ? 'Yes' : 'No'}"
println "🚀 Release Mode  : ${mavenConfig.release ? 'Release' : 'Snapshot'}"
println "======================================================================="

uploadArchives {
    repositories {
        mavenDeployer {
            pom.groupId = moduleConfig.groupId ?: 'com.dinsafer.dinsdk'
            pom.artifactId = moduleConfig.artifactId ?: project.name
            pom.version = finalVersion
            pom.packaging = moduleConfig.packaging ?: 'aar'

            // 默认发布到本地仓库
            repository(url: mavenConfig.localRepoUrl)

            // 如果启用了远程仓库，则上传到正式或快照仓库
            if (mavenConfig.useRemoteMaven) {
                repository(url: targetRepoUrl) {
                    authentication(userName: mavenConfig.user, password: mavenConfig.password)
                }
            }
        }
    }
}
