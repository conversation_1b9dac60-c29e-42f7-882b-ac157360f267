ext {
    libVersion = "3.6.0" // 发布的库的版本
    dependReleaseLib = true // 依赖自己的库时，是否依赖正式的版本，true: 正式版本，false：快照版本
    dependlibVersionPostfixt = dependReleaseLib ? "" : "-SNAPSHOT" // 依赖快照时，会依赖指定版本的快照
    remoteRepoUrl = "http://wan2.sca.im:58720/repository" // 私有仓库地址

    isAar = true
    debuggable = false
    minifyEnabled = true
    // 相关Android版本
    android = [
            compileSdkVersion: 30,
            buildToolsVersion: "30.0.1",
            minSdkVersion    : 21,
            targetSdkVersion : 30,
            versionCode      : 1,
            versionName      : "${libVersion}",
    ]

    Java_Version = JavaVersion.VERSION_1_8

    mavenConfig = [
            localRepoUrl         : uri("${rootProject.projectDir}/dinsdk-repository"), // 本地仓库路径
            remoteSnapshotRepoUrl: "${remoteRepoUrl}/maven-snapshots/", // 远程快照版本仓库地址-一个版本号可以发布多次
            remoteReleaseRepoUrl : "${remoteRepoUrl}/maven-releases/", // 远程正式版本仓库地址-一个版本号只能发布一次
            remoteDownloadRepoUrl: "${remoteRepoUrl}/maven-public/",
            user                 : "DinAndroid",   // 远程仓库用户名
            password             : "dinsafer",    // 远程仓库密码
            useRemoteMaven       : false, // 是否发布到远端私有仓库
            release              : true // true：正式版本，false: snapshot版本
    ]
    // 每个module会覆盖artifactId
    modulePublishConfig = [
            groupId   : 'com.dinsafer.dinsdk',
            artifactId: 'dinsdk',
            version   : "${libVersion}",
            packaging : 'aar',
    ]
}