package com.dinsafer.module_dinfix.bean;

import androidx.annotation.Keep;

import com.dinsafer.dincore.http.BaseHttpEntry;

import java.io.Serializable;

/**
 * @describe：
 * @date：2024/4/26
 * @author: create by <PERSON>ydnee
 */
@Keep
public class TransferBmtResponse extends BaseHttpEntry {
    private String Cmd;
    private String Result;

    public String getCmd() {
        return Cmd;
    }

    public void setCmd(String cmd) {
        Cmd = cmd;
    }

    public String getResult() {
        return Result;
    }

    public void setResult(String result) {
        Result = result;
    }

    //    public ResultBean getResult() {
//        return Result;
//    }
//
//    public void setResult(ResultBean result) {
//        Result = result;
//    }
//
//    @Keep
//    public static class ResultBean implements Serializable {
//        private String group_id;
//        private Long gmtime;
//
//        public String getGroup_id() {
//            return group_id;
//        }
//
//        public void setGroup_id(String group_id) {
//            this.group_id = group_id;
//        }
//
//        public Long getGmtime() {
//            return gmtime;
//        }
//
//        public void setGmtime(Long gmtime) {
//            this.gmtime = gmtime;
//        }
//
//
//    }
}
