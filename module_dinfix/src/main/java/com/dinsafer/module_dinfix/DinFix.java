package com.dinsafer.module_dinfix;
import com.dinsafer.dincore.http.StringResponseEntry;

import static com.dinsafer.dincore.common.ErrorCode.DEFAULT;

import androidx.annotation.Keep;

import com.dinsafer.dincore.common.IDefaultCallBack;
import com.dinsafer.dincore.http.NetWorkException;
import com.dinsafer.module_dinfix.bean.GenHomeQRCodeResponse;
import com.dinsafer.module_dinfix.bean.TransferBmtResponse;
import com.dinsafer.module_dinfix.http.DinFixRepo;

import retrofit2.Callback;
import retrofit2.Call;
import retrofit2.Response;

@Keep
public class DinFix {

    private static DinFix mInstance;
    private final DinFixRepo dinFixRepo;

    private DinFix() {
        dinFixRepo = new DinFixRepo();
    }

    @Keep
    public static DinFix getInstance() {
        if (mInstance == null) {
            synchronized (DinFix.class) {
                if (mInstance == null) {
                    mInstance = new DinFix();
                }
            }
        }
        return mInstance;
    }

    @Keep
    public void genHomeQRCode(String homeID, IDinFixResponseCallback<String> callback) {
        dinFixRepo.genHomeQRCode(homeID, new Callback<GenHomeQRCodeResponse>() {
            @Override
            public void onResponse(Call<GenHomeQRCodeResponse> call, Response<GenHomeQRCodeResponse> response) {
                GenHomeQRCodeResponse genHomeQRCodeResponse = response.body();
                if (genHomeQRCodeResponse != null) {
                    GenHomeQRCodeResponse.ResultBean resultBean = genHomeQRCodeResponse.getResult();
                    if (resultBean != null) {
                        callback.onSuccess(resultBean.getQr_code());
                    } else {
                        callback.onError(-1, "response is null");
                    }
                } else {
                    callback.onError(-1, "response is null");
                }
            }

            @Override
            public void onFailure(Call<GenHomeQRCodeResponse> call, Throwable t) {
                if (null != callback) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), t.getMessage());
                        return;
                    }

                    callback.onError(DEFAULT, "Unknown Error: " + t.getMessage());
                }
            }
        });
    }

    @Keep
    public void operatorTransferBmt(String bmtId, String model, String qrCode, String ticketId, IDefaultCallBack callback) {
        dinFixRepo.operatorTransferBmt(bmtId, model, qrCode, ticketId, new Callback<TransferBmtResponse>() {
            @Override
            public void onResponse(Call<TransferBmtResponse> call, Response<TransferBmtResponse> response) {
                TransferBmtResponse transferBmtResponse = response.body();

                if (transferBmtResponse != null) {
                    if (transferBmtResponse.getResult() != null) {
                        if (null != callback) {
                            callback.onSuccess();
                        }
                    } else {
                        if (null != callback) {
                            callback.onError(transferBmtResponse.getStatus(), transferBmtResponse.getErrorMessage());
                        }
                    }
                } else {
                    if (null != callback) {
                        callback.onError(transferBmtResponse.getStatus(), transferBmtResponse.getErrorMessage());
                    }
                }
            }

            @Override
            public void onFailure(Call<TransferBmtResponse> call, Throwable t) {
                if (null != callback) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), t.getMessage());
                        return;
                    }

                    callback.onError(DEFAULT, "Unknown Error: " + t.getMessage());
                }
            }
        });
    }

    @Keep
    public void deleteBmt(String homeID, String pid, String provider, IDefaultCallBack callback) {
        dinFixRepo.deleteBmt(homeID, pid, provider, new Callback<StringResponseEntry>() {
            @Override
            public void onResponse(Call<StringResponseEntry> call, Response<StringResponseEntry> response) {
                StringResponseEntry stringResponseEntry = response.body();

                if (stringResponseEntry != null && stringResponseEntry.getStatus() == 1) {
                    if (null != callback) {
                        callback.onSuccess();
                    }
                } else {
                    if (null != callback) {
                        callback.onError(stringResponseEntry.getStatus(), stringResponseEntry.getErrorMessage());
                    }
                }
            }

            @Override
            public void onFailure(Call<StringResponseEntry> call, Throwable t) {
                if (null != callback) {
                    if (t instanceof NetWorkException) {
                        callback.onError(((NetWorkException) t).getStatus(), t.getMessage());
                        return;
                    }

                    callback.onError(DEFAULT, "Unknown Error: " + t.getMessage());
                }
            }
        });
    }
}