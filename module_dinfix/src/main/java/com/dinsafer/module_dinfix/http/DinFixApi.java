package com.dinsafer.module_dinfix.http;


import com.dinsafer.dincore.http.Api;
import com.dinsafer.dincore.user.UserManager;
import com.dinsafer.module_dinfix.bean.GenHomeQRCodeResponse;
import com.dinsafer.module_dinfix.bean.TransferBmtResponse;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;
import com.dinsafer.dincore.http.StringResponseEntry;

public class DinFixApi {
    public static final String GM = "gm";

    private final IDinFixApi services;

    private DinFixApi() {
        services = Api.getApi().getRetrofit().create(IDinFixApi.class);
    }

    private static class Holder {
        private static final DinFixApi INSTANT = new DinFixApi();
    }

    public static DinFixApi getInstance() {
        return Holder.INSTANT;
    }

    public Call<GenHomeQRCodeResponse> genHomeQRCode(String homeID) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.genHomeQRCode(Api.getApi()
                .getUrl(DinFixUrls.URL_BMT_GEN_HOME_QRCODE), map);
    }

    public Call<TransferBmtResponse> operatorTransferBmt(String bmtId, String model, String qrCode, String ticketId) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("bmt_id", bmtId);
            jsonObject.put("model", model);
            jsonObject.put("qr_code", qrCode);
            jsonObject.put("ticket_id", ticketId);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put(GM, System.currentTimeMillis() * 1000);
        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.operatorTransferBmt(Api.getApi().getUrl(DinFixUrls.URL_Transfer_Bmt), map);
    }

    public Call<StringResponseEntry> deleteBmt(String homeID, String pid, String provider) {
        Map<String, Object> map = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("home_id", homeID);
            jsonObject.put("model", provider);
            jsonObject.put("id", pid);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        map.put("json", jsonObject);
        map.put("token", UserManager.getInstance().getToken());
        return services.stringCall(Api.getApi()
                .getUrl(DinFixUrls.URL_BMT_DELETE), map);
    }
}
