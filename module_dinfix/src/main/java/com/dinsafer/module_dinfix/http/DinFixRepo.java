package com.dinsafer.module_dinfix.http;

import androidx.annotation.Nullable;

import com.dinsafer.module_dinfix.bean.GenHomeQRCodeResponse;
import com.dinsafer.module_dinfix.bean.TransferBmtResponse;
import com.dinsafer.dincore.http.StringResponseEntry;

import retrofit2.Callback;

public class DinFixRepo {

    public DinFixRepo() {
    }

    @Nullable
    public void genHomeQRCode(String homeID, Callback<GenHomeQRCodeResponse> callback) {
        DinFixApi.getInstance().genHomeQRCode(homeID).enqueue(callback);
    }

    public void operatorTransferBmt(String bmtId, String model, String qrCode, String ticketId, Callback<TransferBmtResponse> callback) {
        DinFixApi.getInstance().operatorTransferBmt(bmtId, model, qrCode, ticketId).enqueue(callback);
    }

    public void deleteBmt(String homeID, String pid, String provider, Callback<StringResponseEntry>  callback) {
        DinFixApi.getInstance().deleteBmt(homeID, pid, provider).enqueue(callback);
    }
}
