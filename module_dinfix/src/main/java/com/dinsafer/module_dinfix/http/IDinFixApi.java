package com.dinsafer.module_dinfix.http;

import com.dinsafer.module_dinfix.bean.GenHomeQRCodeResponse;
import com.dinsafer.module_dinfix.bean.TransferBmtResponse;

import java.util.Map;

import retrofit2.Call;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;
import retrofit2.http.Url;
import com.dinsafer.dincore.http.StringResponseEntry;

public interface IDinFixApi {
    @POST
    @FormUrlEncoded
    Call<GenHomeQRCodeResponse> genHomeQRCode(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<TransferBmtResponse> operatorTransferBmt(@Url String url, @FieldMap Map<String, Object> map);

    @POST
    @FormUrlEncoded
    Call<StringResponseEntry> stringCall(@Url String url, @FieldMap Map<String, Object> map);
}
